---
description:
globs:
alwaysApply: false
---
# 测试与部署规范

本文档描述瑞旗管理系统的测试和部署规范，确保系统的质量和稳定性。

## 测试规范

### 单元测试

#### 后端单元测试

- 使用 JUnit 5 + Mockito 框架进行单元测试
- 测试类命名：被测试类名 + Test，如 `UserServiceTest`
- 测试方法命名：test + 被测试方法名 + 测试场景，如 `testFindUserByIdWhenUserExists`
- 每个测试方法应该只测试一个功能点
- 使用 `@Mock` 注解模拟依赖对象
- 使用 `@InjectMocks` 注解注入被测试对象
- 使用断言验证测试结果

```java
@ExtendWith(MockitoExtension.class)
public class UserServiceTest {
    
    @Mock
    private UserMapper userMapper;
    
    @InjectMocks
    private UserServiceImpl userService;
    
    @Test
    public void testFindUserByIdWhenUserExists() {
        // 准备测试数据
        Long userId = 1L;
        SysUser user = new SysUser();
        user.setUserId(userId);
        user.setUserName("admin");
        
        // 模拟依赖行为
        when(userMapper.selectUserById(userId)).thenReturn(user);
        
        // 执行测试
        SysUser result = userService.selectUserById(userId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(userId, result.getUserId());
        assertEquals("admin", result.getUserName());
        
        // 验证依赖方法被调用
        verify(userMapper).selectUserById(userId);
    }
}
```

#### 前端单元测试

- 使用 Jest + Vue Test Utils 进行单元测试
- 测试文件命名：被测试组件名 + .spec.js，如 `UserList.spec.js`
- 测试组件的渲染、事件处理和生命周期方法
- 使用快照测试验证组件渲染结果
- 使用模拟数据测试组件行为

```javascript
import { shallowMount } from '@vue/test-utils';
import UserList from '@/components/UserList.vue';
import { createStore } from 'vuex';

describe('UserList.vue', () => {
  let wrapper;
  let store;
  
  beforeEach(() => {
    // 模拟 store
    store = createStore({
      modules: {
        user: {
          namespaced: true,
          state: {
            users: [
              { id: 1, name: 'User 1' },
              { id: 2, name: 'User 2' }
            ]
          },
          actions: {
            fetchUsers: jest.fn()
          }
        }
      }
    });
    
    // 挂载组件
    wrapper = shallowMount(UserList, {
      global: {
        plugins: [store]
      }
    });
  });
  
  test('renders user list correctly', () => {
    // 验证组件渲染
    expect(wrapper.findAll('li')).toHaveLength(2);
    expect(wrapper.find('li:first-child').text()).toContain('User 1');
    expect(wrapper.find('li:last-child').text()).toContain('User 2');
  });
  
  test('calls fetchUsers on mounted', () => {
    // 验证生命周期方法
    expect(store.dispatch).toHaveBeenCalledWith('user/fetchUsers');
  });
});
```

### 集成测试

- 使用 Spring Boot Test 进行后端集成测试
- 使用 H2 内存数据库进行数据库集成测试
- 使用 MockMvc 测试 Controller 层接口
- 测试类命名：被测试类名 + IT，如 `UserControllerIT`

```java
@SpringBootTest
@AutoConfigureMockMvc
public class UserControllerIT {
    
    @Autowired
    private MockMvc mockMvc;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Test
    public void testGetUserList() throws Exception {
        mockMvc.perform(get("/system/user/list")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray());
    }
    
    @Test
    public void testAddUser() throws Exception {
        SysUser user = new SysUser();
        user.setUserName("testuser");
        user.setNickName("Test User");
        user.setPassword("password");
        
        mockMvc.perform(post("/system/user")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(user)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }
}
```

### 端到端测试

- 使用 Cypress 或 Selenium 进行端到端测试
- 测试关键业务流程和用户交互
- 测试文件组织按功能模块划分
- 使用 Page Object 模式组织测试代码

```javascript
// Cypress 测试示例
describe('User Management', () => {
  beforeEach(() => {
    // 登录系统
    cy.login('admin', 'admin123');
    // 导航到用户管理页面
    cy.visit('/system/user');
  });
  
  it('should display user list', () => {
    cy.get('.user-table').should('be.visible');
    cy.get('.user-table tbody tr').should('have.length.greaterThan', 0);
  });
  
  it('should add new user', () => {
    const username = `test_${Date.now()}`;
    
    // 点击添加按钮
    cy.get('.btn-add').click();
    
    // 填写表单
    cy.get('#username').type(username);
    cy.get('#nickname').type('Test User');
    cy.get('#password').type('Password123');
    cy.get('#confirmPassword').type('Password123');
    
    // 提交表单
    cy.get('.btn-submit').click();
    
    // 验证结果
    cy.get('.el-message--success').should('be.visible');
    cy.get('.user-table').contains(username).should('be.visible');
  });
});
```

## 部署规范

### 环境配置

#### 开发环境

- JDK 1.8+
- Maven 3.6+
- MySQL 5.7+
- Redis 5.0+
- Node.js 12+
- npm 6+

#### 测试环境

- CentOS 7 或 Ubuntu 18.04+
- JDK 1.8+
- MySQL 5.7+
- Redis 5.0+
- Nginx 1.18+

#### 生产环境

- CentOS 7 或 Ubuntu 18.04+
- JDK 1.8+
- MySQL 5.7+ (主从复制)
- Redis 5.0+ (集群模式)
- Nginx 1.18+ (负载均衡)

### 配置文件管理

- 使用 Spring Profiles 管理不同环境的配置
- 开发环境：`application-dev.yml`
- 测试环境：`application-test.yml`
- 生产环境：`application-prod.yml`
- 敏感配置（如数据库密码）使用环境变量或配置中心

```yaml
# 开发环境配置示例
spring:
  datasource:
    url: **********************************************************************************************************************************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
```

### 打包部署

#### 后端打包

- 使用 Maven 进行打包：`mvn clean package -Dmaven.test.skip=true`
- 生成的 JAR 文件位于 `rich-admin/target/` 目录下
- 使用 `java -jar` 命令启动应用：`java -jar rich-admin.jar --spring.profiles.active=prod`

#### 前端打包

- 使用 npm 进行打包：`npm run build:prod`
- 生成的静态文件位于 `rich-ui/dist/` 目录下
- 将静态文件部署到 Nginx 服务器

#### 移动端打包

- 使用 HBuilderX 进行打包
- 微信小程序：导出 unpackage/dist/build/mp-weixin 目录，上传到微信开发者工具
- H5：导出 unpackage/dist/build/h5 目录，部署到 Nginx 服务器
- App：使用云打包生成 Android/iOS 安装包

### 部署架构

```
                                ┌─────────────┐
                                │   Nginx     │
                                │ 负载均衡/反向代理 │
                                └───────┬─────┘
                                        │
                 ┌────────────────────┬─┴───────────────────┐
                 │                    │                     │
         ┌───────▼──────┐     ┌───────▼──────┐      ┌───────▼──────┐
         │  应用服务器 1   │     │  应用服务器 2   │      │  应用服务器 n   │
         │ Spring Boot   │     │ Spring Boot   │      │ Spring Boot   │
         └───────┬──────┘     └───────┬──────┘      └───────┬──────┘
                 │                    │                     │
                 └────────────────────┼─────────────────────┘
                                      │
                 ┌──────────────────┬─┴─────────────────┐
                 │                  │                   │
         ┌───────▼──────┐   ┌───────▼──────┐    ┌───────▼──────┐
         │  MySQL 主库   │   │  MySQL 从库   │    │  Redis 集群   │
         └──────────────┘   └──────────────┘    └──────────────┘
```

### 部署步骤

1. **准备环境**
   - 安装 JDK, MySQL, Redis, Nginx
   - 创建数据库并导入初始数据

2. **部署后端服务**
   - 上传 JAR 文件到服务器
   - 创建启动脚本 `startup.sh`：

```bash
#!/bin/bash
nohup java -Xms512m -Xmx1024m -jar rich-admin.jar --spring.profiles.active=prod > rich.log 2>&1 &
echo $! > rich.pid
```

3. **部署前端资源**
   - 上传打包后的静态资源到 Nginx 服务器
   - 配置 Nginx：

```nginx
server {
    listen 80;
    server_name example.com;
    
    location / {
        root /var/www/rich-ui;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://backend-server:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

4. **部署移动端**
   - 部署 H5 版本到 Nginx 服务器
   - 提交小程序代码审核并发布
   - 上传 App 安装包到应用商店

### 监控与维护

- 使用 Spring Boot Actuator 监控应用健康状态
- 使用 ELK 栈收集和分析日志
- 使用 Prometheus + Grafana 监控系统性能
- 配置告警机制，及时发现和处理异常

```yaml
# Spring Boot Actuator 配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
```

### 备份策略

- 数据库每日定时备份，保留最近 30 天的备份
- 代码仓库定期备份
- 配置文件定期备份
- 备份文件异地存储

```bash
# MySQL 备份脚本示例
#!/bin/bash
BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d)
MYSQL_USER="root"
MYSQL_PASSWORD="password"
DATABASE="rich"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u$MYSQL_USER -p$MYSQL_PASSWORD $DATABASE > $BACKUP_DIR/$DATABASE-$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/$DATABASE-$DATE.sql

# 删除 30 天前的备份
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete
```
