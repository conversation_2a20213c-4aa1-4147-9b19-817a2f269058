package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.BasQuotationStrategy;
import org.apache.ibatis.annotations.Mapper;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-25
 */
@Mapper
public interface BasQuotationStrategyMapper {
    /**
     * 查询【请填写功能名称】
     *
     * @param strategyCode 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    BasQuotationStrategy selectBasQuotationStrategyByStrategyCode(String strategyCode);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param basQuotationStrategy 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    List<BasQuotationStrategy> selectBasQuotationStrategyList(BasQuotationStrategy basQuotationStrategy);

    /**
     * 新增【请填写功能名称】
     *
     * @param basQuotationStrategy 【请填写功能名称】
     * @return 结果
     */
    int insertBasQuotationStrategy(BasQuotationStrategy basQuotationStrategy);

    /**
     * 修改【请填写功能名称】
     *
     * @param basQuotationStrategy 【请填写功能名称】
     * @return 结果
     */
    int updateBasQuotationStrategy(BasQuotationStrategy basQuotationStrategy);

    /**
     * 删除【请填写功能名称】
     *
     * @param strategyCode 【请填写功能名称】主键
     * @return 结果
     */
    int deleteBasQuotationStrategyByStrategyCode(String strategyCode);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param strategyCodes 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasQuotationStrategyByStrategyCodes(String[] strategyCodes);
}
