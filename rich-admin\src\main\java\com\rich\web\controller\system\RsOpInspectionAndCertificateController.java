package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsOpInspectionAndCertificate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsOpInspectionAndCertificateService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 检验与证书服务Controller
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@RestController
@RequestMapping("/system/opinspectionandcertificate")
public class RsOpInspectionAndCertificateController extends BaseController {
    @Autowired
    private RsOpInspectionAndCertificateService rsOpInspectionAndCertificateService;

    /**
     * 查询检验与证书服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opinspectionandcertificate:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsOpInspectionAndCertificate rsOpInspectionAndCertificate) {
        startPage();
        List<RsOpInspectionAndCertificate> list = rsOpInspectionAndCertificateService.selectRsOpInspectionAndCertificateList(rsOpInspectionAndCertificate);
        return getDataTable(list);
    }

    /**
     * 导出检验与证书服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opinspectionandcertificate:export')")
    @Log(title = "检验与证书服务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsOpInspectionAndCertificate rsOpInspectionAndCertificate) {
        List<RsOpInspectionAndCertificate> list = rsOpInspectionAndCertificateService.selectRsOpInspectionAndCertificateList(rsOpInspectionAndCertificate);
        ExcelUtil<RsOpInspectionAndCertificate> util = new ExcelUtil<RsOpInspectionAndCertificate>(RsOpInspectionAndCertificate.class);
        util.exportExcel(response, list, "检验与证书服务数据");
    }

    /**
     * 获取检验与证书服务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:opinspectionandcertificate:query')")
    @GetMapping(value = "/{inspectionAndCertificateId}")
    public AjaxResult getInfo(@PathVariable("inspectionAndCertificateId") Long inspectionAndCertificateId) {
        return AjaxResult.success(rsOpInspectionAndCertificateService.selectRsOpInspectionAndCertificateByInspectionAndCertificateId(inspectionAndCertificateId));
    }

    /**
     * 新增检验与证书服务
     */
    @PreAuthorize("@ss.hasPermi('system:opinspectionandcertificate:add')")
    @Log(title = "检验与证书服务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsOpInspectionAndCertificate rsOpInspectionAndCertificate) {
        return toAjax(rsOpInspectionAndCertificateService.insertRsOpInspectionAndCertificate(rsOpInspectionAndCertificate));
    }

    /**
     * 修改检验与证书服务
     */
    @PreAuthorize("@ss.hasPermi('system:opinspectionandcertificate:edit')")
    @Log(title = "检验与证书服务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsOpInspectionAndCertificate rsOpInspectionAndCertificate) {
        return toAjax(rsOpInspectionAndCertificateService.updateRsOpInspectionAndCertificate(rsOpInspectionAndCertificate));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:opinspectionandcertificate:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsOpInspectionAndCertificate rsOpInspectionAndCertificate) {
        rsOpInspectionAndCertificate.setUpdateBy(getUserId());
        return toAjax(rsOpInspectionAndCertificateService.changeStatus(rsOpInspectionAndCertificate));
    }

    /**
     * 删除检验与证书服务
     */
    @PreAuthorize("@ss.hasPermi('system:opinspectionandcertificate:remove')")
    @Log(title = "检验与证书服务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{inspectionAndCertificateIds}")
    public AjaxResult remove(@PathVariable Long[] inspectionAndCertificateIds) {
        return toAjax(rsOpInspectionAndCertificateService.deleteRsOpInspectionAndCertificateByInspectionAndCertificateIds(inspectionAndCertificateIds));
    }
}
