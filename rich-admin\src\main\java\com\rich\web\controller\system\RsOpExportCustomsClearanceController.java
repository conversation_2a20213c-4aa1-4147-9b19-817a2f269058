package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsOpExportCustomsClearance;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsOpExportCustomsClearanceService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 出口报关服务Controller
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@RestController
@RequestMapping("/system/opexportcustomsclearance")
public class RsOpExportCustomsClearanceController extends BaseController {
    @Autowired
    private RsOpExportCustomsClearanceService rsOpExportCustomsClearanceService;

    /**
     * 查询出口报关服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opexportcustomsclearance:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsOpExportCustomsClearance rsOpExportCustomsClearance) {
        startPage();
        List<RsOpExportCustomsClearance> list = rsOpExportCustomsClearanceService.selectRsOpExportCustomsClearanceList(rsOpExportCustomsClearance);
        return getDataTable(list);
    }

    /**
     * 导出出口报关服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opexportcustomsclearance:export')")
    @Log(title = "出口报关服务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsOpExportCustomsClearance rsOpExportCustomsClearance) {
        List<RsOpExportCustomsClearance> list = rsOpExportCustomsClearanceService.selectRsOpExportCustomsClearanceList(rsOpExportCustomsClearance);
        ExcelUtil<RsOpExportCustomsClearance> util = new ExcelUtil<RsOpExportCustomsClearance>(RsOpExportCustomsClearance.class);
        util.exportExcel(response, list, "出口报关服务数据");
    }

    /**
     * 获取出口报关服务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:opexportcustomsclearance:query')")
    @GetMapping(value = "/{exportCustomsClearanceId}")
    public AjaxResult getInfo(@PathVariable("exportCustomsClearanceId") Long exportCustomsClearanceId) {
        return AjaxResult.success(rsOpExportCustomsClearanceService.selectRsOpExportCustomsClearanceByExportCustomsClearanceId(exportCustomsClearanceId));
    }

    /**
     * 新增出口报关服务
     */
    @PreAuthorize("@ss.hasPermi('system:opexportcustomsclearance:add')")
    @Log(title = "出口报关服务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsOpExportCustomsClearance rsOpExportCustomsClearance) {
        return toAjax(rsOpExportCustomsClearanceService.insertRsOpExportCustomsClearance(rsOpExportCustomsClearance));
    }

    /**
     * 修改出口报关服务
     */
    @PreAuthorize("@ss.hasPermi('system:opexportcustomsclearance:edit')")
    @Log(title = "出口报关服务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsOpExportCustomsClearance rsOpExportCustomsClearance) {
        return toAjax(rsOpExportCustomsClearanceService.updateRsOpExportCustomsClearance(rsOpExportCustomsClearance));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:opexportcustomsclearance:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsOpExportCustomsClearance rsOpExportCustomsClearance) {
        rsOpExportCustomsClearance.setUpdateBy(getUserId());
        return toAjax(rsOpExportCustomsClearanceService.changeStatus(rsOpExportCustomsClearance));
    }

    /**
     * 删除出口报关服务
     */
    @PreAuthorize("@ss.hasPermi('system:opexportcustomsclearance:remove')")
    @Log(title = "出口报关服务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{exportCustomsClearanceIds}")
    public AjaxResult remove(@PathVariable Long[] exportCustomsClearanceIds) {
        return toAjax(rsOpExportCustomsClearanceService.deleteRsOpExportCustomsClearanceByExportCustomsClearanceIds(exportCustomsClearanceIds));
    }
}
