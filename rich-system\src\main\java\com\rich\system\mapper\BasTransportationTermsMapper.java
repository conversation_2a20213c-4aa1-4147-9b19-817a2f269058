package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasTransportationTerms;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 运输条款Mapper接口
 *
 * <AUTHOR>
 * @date 2023-05-05
 */
@Mapper
public interface BasTransportationTermsMapper {
    /**
     * 查询运输条款
     *
     * @param transportationTermsId 运输条款主键
     * @return 运输条款
     */
    BasTransportationTerms selectBasTransportationTermsByTransportationTermsId(Long transportationTermsId);

    /**
     * 查询运输条款列表
     *
     * @param basTransportationTerms 运输条款
     * @return 运输条款集合
     */
    List<BasTransportationTerms> selectBasTransportationTermsList(BasTransportationTerms basTransportationTerms);

    /**
     * 新增运输条款
     *
     * @param basTransportationTerms 运输条款
     * @return 结果
     */
    int insertBasTransportationTerms(BasTransportationTerms basTransportationTerms);

    /**
     * 修改运输条款
     *
     * @param basTransportationTerms 运输条款
     * @return 结果
     */
    int updateBasTransportationTerms(BasTransportationTerms basTransportationTerms);

    /**
     * 删除运输条款
     *
     * @param transportationTermsId 运输条款主键
     * @return 结果
     */
    int deleteBasTransportationTermsByTransportationTermsId(Long transportationTermsId);

    /**
     * 批量删除运输条款
     *
     * @param transportationTermsIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasTransportationTermsByTransportationTermsIds(Long[] transportationTermsIds);
}
