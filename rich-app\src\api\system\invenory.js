import request from "@/utils/request";

// 获取库存列表
export function getInventoryList(query) {
    return request({
        url: "/mp/inventory/list",
        method: "get",
        params: query,
    });
}

// 根据快递单号查询库存
export function getInventoryByExpress(expressNo) {
    return request({
        url: "/mp/inventory/express",
        method: "get",
        params: {expressNo},
    });
}

// 新增库存
export function addInventory(data) {
    return request({
        url: "/mp/inventory",
        method: "post",
        data: data,
    });
}

// 更新库存
export function updateInventory(data) {
    return request({
        url: "/mp/inventory",
        method: "put",
        data: data,
    });
}

// 获取库存详情
export function getInventoryDetail(id) {
    return request({
        url: `/mp/inventory/${id}`,
        method: "get",
    });
}

// 仓管确认入仓
export function confirmEnter(data) {
    return request({
        url: "/mp/inventory/enter",
        method: "put",
        data: data,
    });
}

// 获取打包箱列表
export function getPackageList(query) {
    return request({
        url: "/system/inventory/package",
        method: "get",
        params: query,
    });
}

// 删除入库单
export function deleteInventory(data) {
    return request({
        url: `/mp/inventory/delete`,
        method: "post",
        data: data,
    });
}

// 获取状态数量
export function getStatusNumber(query) {
    return request({
        url: "/mp/inventory/status",
        method: "get",
        params: query,
    });
}

// 客户认领快递
export function claimExpress(inventoryId, data) {
    return request({
        url: `/system/inventory/claim/${inventoryId}`,
        method: "put",
        data: data,
    });
}

// 获取客户快递状态统计
export function getInventoryStatusCount(params) {
    return request({
        url: "/system/inventory/status/count",
        method: "get",
        params: params,
    });
}

// 检查快递信息完整性
export function checkInventoryComplete(inventoryId) {
    return request({
        url: `/system/inventory/check/complete/${inventoryId}`,
        method: "get",
    });
}