package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.BasDocFlowDirection;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.system.mapper.BasDocFlowDirectionMapper;
import com.rich.system.service.BasDocFlowDirectionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 文件流向Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@Service
public class BasDocFlowDirectionServiceImpl implements BasDocFlowDirectionService {
    @Autowired
    private BasDocFlowDirectionMapper basDocFlowDirectionMapper;

    /**
     * 查询文件流向
     *
     * @param docFlowDirectionId 文件流向主键
     * @return 文件流向
     */
    @Override
    public BasDocFlowDirection selectBasDocFlowDirectionByDocFlowDirectionId(Long docFlowDirectionId) {
        return basDocFlowDirectionMapper.selectBasDocFlowDirectionByDocFlowDirectionId(docFlowDirectionId);
    }

    /**
     * 查询文件流向列表
     *
     * @param basDocFlowDirection 文件流向
     * @return 文件流向
     */
    @Override
    public List<BasDocFlowDirection> selectBasDocFlowDirectionList(BasDocFlowDirection basDocFlowDirection) {
        return basDocFlowDirectionMapper.selectBasDocFlowDirectionList(basDocFlowDirection);
    }

    /**
     * 新增文件流向
     *
     * @param basDocFlowDirection 文件流向
     * @return 结果
     */
    @Override
    public int insertBasDocFlowDirection(BasDocFlowDirection basDocFlowDirection) {
        basDocFlowDirection.setCreateTime(DateUtils.getNowDate());
        basDocFlowDirection.setCreateBy(SecurityUtils.getUserId());
        return basDocFlowDirectionMapper.insertBasDocFlowDirection(basDocFlowDirection);
    }

    /**
     * 修改文件流向
     *
     * @param basDocFlowDirection 文件流向
     * @return 结果
     */
    @Override
    public int updateBasDocFlowDirection(BasDocFlowDirection basDocFlowDirection) {
        basDocFlowDirection.setUpdateTime(DateUtils.getNowDate());
        basDocFlowDirection.setUpdateBy(SecurityUtils.getUserId());
        return basDocFlowDirectionMapper.updateBasDocFlowDirection(basDocFlowDirection);
    }

    /**
     * 修改文件流向状态
     *
     * @param basDocFlowDirection 文件流向
     * @return 文件流向
     */
    @Override
    public int changeStatus(BasDocFlowDirection basDocFlowDirection) {
        return basDocFlowDirectionMapper.updateBasDocFlowDirection(basDocFlowDirection);
    }

    /**
     * 批量删除文件流向
     *
     * @param docFlowDirectionIds 需要删除的文件流向主键
     * @return 结果
     */
    @Override
    public int deleteBasDocFlowDirectionByDocFlowDirectionIds(Long[] docFlowDirectionIds) {
        return basDocFlowDirectionMapper.deleteBasDocFlowDirectionByDocFlowDirectionIds(docFlowDirectionIds);
    }

    /**
     * 删除文件流向信息
     *
     * @param docFlowDirectionId 文件流向主键
     * @return 结果
     */
    @Override
    public int deleteBasDocFlowDirectionByDocFlowDirectionId(Long docFlowDirectionId) {
        return basDocFlowDirectionMapper.deleteBasDocFlowDirectionByDocFlowDirectionId(docFlowDirectionId);
    }
}
