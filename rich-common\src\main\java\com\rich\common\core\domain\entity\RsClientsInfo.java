package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 用来记录客户常用的信息，避免重复劳动、错漏对象 rs_clients_info
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
public class RsClientsInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 客户信息ID ,自动序列
     */
    private Long clientsInfoId;

    /**
     * 所属客户 ,记录客户的ext_company_id
     */
    @Excel(name = "所属客户 ,记录客户的ext_company_id")
    private Long clientId;

    /**
     * 速查标记 ,业务/操作自编，用于速记，如墨西哥音响
     */
    @Excel(name = "速查标记 ,业务/操作自编，用于速记，如墨西哥音响")
    private String searchMark;

    /**
     * 服务类型 ,自动记录，备用
     */
    @Excel(name = "服务类型 ,自动记录，备用")
    private Long serviceTypeId;

    /**
     * 装运区域 ,自动记录，备用
     */
    @Excel(name = "装运区域 ,自动记录，备用")
    private Long precarriageRegionId;

    /**
     * 启运港 ,自动记录，备用
     */
    @Excel(name = "启运港 ,自动记录，备用")
    private Long polId;

    /**
     * 目的港 ,自动记录，备用
     */
    @Excel(name = "目的港 ,自动记录，备用")
    private Long destinationPortId;

    /**
     * 派送区域 ,自动记录，备用
     */
    @Excel(name = "派送区域 ,自动记录，备用")
    private Long dispatchRegionId;

    /**
     * 发货人简称 ,用简称填明细
     */
    @Excel(name = "发货人简称 ,用简称填明细")
    private String shipperShortName;

    /**
     * 发货人明细 ,
     */
    @Excel(name = "发货人明细 ,")
    private String bookingShipper;

    /**
     * 收货人简称 ,用简称填明细
     */
    @Excel(name = "收货人简称 ,用简称填明细")
    private String consigneeShortName;

    /**
     * 收货人明细 ,
     */
    @Excel(name = "收货人明细 ,")
    private String bookingConsignee;

    /**
     * 通知人简称 ,用简称填明细
     */
    @Excel(name = "通知人简称 ,用简称填明细")
    private String notifyPartyShortName;

    /**
     * 通知人明细 ,
     */
    @Excel(name = "通知人明细 ,")
    private String bookingNotifyParty;

    /**
     * 装运详址 ,用区域填明细
     */
    @Excel(name = "装运详址 ,用区域填明细")
    private String precarriageAddress;

    /**
     * 装运联系人 ,
     */
    @Excel(name = "装运联系人 ,")
    private String precarriageContact;

    /**
     * 装运电话 ,
     */
    @Excel(name = "装运电话 ,")
    private String precarriageTel;

    /**
     * 装运备注 ,
     */
    @Excel(name = "装运备注 ,")
    private String precarriageRemark;

    /**
     * 派送详址 ,用区域填明细
     */
    @Excel(name = "派送详址 ,用区域填明细")
    private String dispatchAddress;

    /**
     * 派送联系人 ,
     */
    @Excel(name = "派送联系人 ,")
    private String dispatchContact;

    /**
     * 派送电话 ,
     */
    @Excel(name = "派送电话 ,")
    private String dispatchTel;

    /**
     * 派送备注 ,
     */
    @Excel(name = "派送备注 ,")
    private String dispatchRemark;
    private String pol;
    private String destinationPort;
    private String companyName;

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getDestinationPort() {
        return destinationPort;
    }

    public void setDestinationPort(String destinationPort) {
        this.destinationPort = destinationPort;
    }

    public String getPol() {
        return pol;
    }

    public void setPol(String pol) {
        this.pol = pol;
    }

    public Long getClientsInfoId() {
        return clientsInfoId;
    }

    public void setClientsInfoId(Long clientsInfoId) {
        this.clientsInfoId = clientsInfoId;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public String getSearchMark() {
        return searchMark;
    }

    public void setSearchMark(String searchMark) {
        this.searchMark = searchMark;
    }

    public Long getServiceTypeId() {
        return serviceTypeId;
    }

    public void setServiceTypeId(Long serviceTypeId) {
        this.serviceTypeId = serviceTypeId;
    }

    public Long getPrecarriageRegionId() {
        return precarriageRegionId;
    }

    public void setPrecarriageRegionId(Long precarriageRegionId) {
        this.precarriageRegionId = precarriageRegionId;
    }

    public Long getPolId() {
        return polId;
    }

    public void setPolId(Long polId) {
        this.polId = polId;
    }

    public Long getDestinationPortId() {
        return destinationPortId;
    }

    public void setDestinationPortId(Long destinationPortId) {
        this.destinationPortId = destinationPortId;
    }

    public Long getDispatchRegionId() {
        return dispatchRegionId;
    }

    public void setDispatchRegionId(Long dispatchRegionId) {
        this.dispatchRegionId = dispatchRegionId;
    }

    public String getShipperShortName() {
        return shipperShortName;
    }

    public void setShipperShortName(String shipperShortName) {
        this.shipperShortName = shipperShortName;
    }

    public String getBookingShipper() {
        return bookingShipper;
    }

    public void setBookingShipper(String bookingShipper) {
        this.bookingShipper = bookingShipper;
    }

    public String getConsigneeShortName() {
        return consigneeShortName;
    }

    public void setConsigneeShortName(String consigneeShortName) {
        this.consigneeShortName = consigneeShortName;
    }

    public String getBookingConsignee() {
        return bookingConsignee;
    }

    public void setBookingConsignee(String bookingConsignee) {
        this.bookingConsignee = bookingConsignee;
    }

    public String getNotifyPartyShortName() {
        return notifyPartyShortName;
    }

    public void setNotifyPartyShortName(String notifyPartyShortName) {
        this.notifyPartyShortName = notifyPartyShortName;
    }

    public String getBookingNotifyParty() {
        return bookingNotifyParty;
    }

    public void setBookingNotifyParty(String bookingNotifyParty) {
        this.bookingNotifyParty = bookingNotifyParty;
    }

    public String getPrecarriageAddress() {
        return precarriageAddress;
    }

    public void setPrecarriageAddress(String precarriageAddress) {
        this.precarriageAddress = precarriageAddress;
    }

    public String getPrecarriageContact() {
        return precarriageContact;
    }

    public void setPrecarriageContact(String precarriageContact) {
        this.precarriageContact = precarriageContact;
    }

    public String getPrecarriageTel() {
        return precarriageTel;
    }

    public void setPrecarriageTel(String precarriageTel) {
        this.precarriageTel = precarriageTel;
    }

    public String getPrecarriageRemark() {
        return precarriageRemark;
    }

    public void setPrecarriageRemark(String precarriageRemark) {
        this.precarriageRemark = precarriageRemark;
    }

    public String getDispatchAddress() {
        return dispatchAddress;
    }

    public void setDispatchAddress(String dispatchAddress) {
        this.dispatchAddress = dispatchAddress;
    }

    public String getDispatchContact() {
        return dispatchContact;
    }

    public void setDispatchContact(String dispatchContact) {
        this.dispatchContact = dispatchContact;
    }

    public String getDispatchTel() {
        return dispatchTel;
    }

    public void setDispatchTel(String dispatchTel) {
        this.dispatchTel = dispatchTel;
    }

    public String getDispatchRemark() {
        return dispatchRemark;
    }

    public void setDispatchRemark(String dispatchRemark) {
        this.dispatchRemark = dispatchRemark;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("clientsInfoId", getClientsInfoId())
                .append("clientId", getClientId())
                .append("searchMark", getSearchMark())
                .append("serviceTypeId", getServiceTypeId())
                .append("precarriageRegionId", getPrecarriageRegionId())
                .append("polId", getPolId())
                .append("destinationPortId", getDestinationPortId())
                .append("dispatchRegionId", getDispatchRegionId())
                .append("shipperShortName", getShipperShortName())
                .append("bookingShipper", getBookingShipper())
                .append("consigneeShortName", getConsigneeShortName())
                .append("bookingConsignee", getBookingConsignee())
                .append("notifyPartyShortName", getNotifyPartyShortName())
                .append("bookingNotifyParty", getBookingNotifyParty())
                .append("precarriageAddress", getPrecarriageAddress())
                .append("precarriageContact", getPrecarriageContact())
                .append("precarriageTel", getPrecarriageTel())
                .append("precarriageRemark", getPrecarriageRemark())
                .append("dispatchAddress", getDispatchAddress())
                .append("dispatchContact", getDispatchContact())
                .append("dispatchTel", getDispatchTel())
                .append("dispatchRemark", getDispatchRemark())
                .toString();
    }
}
