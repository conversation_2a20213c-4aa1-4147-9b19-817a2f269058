package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.MpWarehouseConsignee;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.MpWarehouseConsigneeService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 收货人信息Controller
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@RestController
@RequestMapping("/mp/warehouseconsignee")
public class MpWarehouseConsigneeController extends BaseController {
    @Autowired
    private MpWarehouseConsigneeService mpWarehouseConsigneeService;

    /**
     * 查询收货人信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MpWarehouseConsignee mpWarehouseConsignee) {
        // 检查是否有分页参数，如果有则启用分页，否则不分页
        if (mpWarehouseConsignee.getParams() != null &&
                (mpWarehouseConsignee.getParams().get("pageNum") != null ||
                        mpWarehouseConsignee.getParams().get("pageSize") != null)) {
            startPage();
        } else {
            clearPage();
        }
        List<MpWarehouseConsignee> list = mpWarehouseConsigneeService.selectMpWarehouseConsigneeList(mpWarehouseConsignee);
        return getDataTable(list);
    }

    /**
     * 导出收货人信息列表
     */
    @Log(title = "收货人信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MpWarehouseConsignee mpWarehouseConsignee) {
        List<MpWarehouseConsignee> list = mpWarehouseConsigneeService.selectMpWarehouseConsigneeList(mpWarehouseConsignee);
        ExcelUtil<MpWarehouseConsignee> util = new ExcelUtil<MpWarehouseConsignee>(MpWarehouseConsignee.class);
        util.exportExcel(response, list, "收货人信息数据");
    }

    /**
     * 获取收货人信息详细信息
     */
    @GetMapping(value = "/{consigneeId}")
    public AjaxResult getInfo(@PathVariable("consigneeId") Long consigneeId) {
        return AjaxResult.success(mpWarehouseConsigneeService.selectMpWarehouseConsigneeByConsigneeId(consigneeId));
    }
    @GetMapping(value = "/client/{userId}")
    public AjaxResult getConsigneeBuUser(@PathVariable("userId") Long userId) {
        return AjaxResult.success(mpWarehouseConsigneeService.selectMpWarehouseConsigneeByuserId(userId));
    }

    @GetMapping(value = "/bind/{consigneeId}/{userId}")
    public AjaxResult bindConsignee(@PathVariable("consigneeId") Long consigneeId,@PathVariable("userId") Long userId) {
        return AjaxResult.success(mpWarehouseConsigneeService.bindConsignee(consigneeId,userId));
    }

    /**
     * 新增收货人信息
     */
    @Log(title = "收货人信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MpWarehouseConsignee mpWarehouseConsignee) {
        return toAjax(mpWarehouseConsigneeService.insertMpWarehouseConsignee(mpWarehouseConsignee));
    }

    /**
     * 修改收货人信息
     */
    @Log(title = "收货人信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MpWarehouseConsignee mpWarehouseConsignee) {
        return toAjax(mpWarehouseConsigneeService.updateMpWarehouseConsignee(mpWarehouseConsignee));
    }

    /**
     * 状态状态
     */
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody MpWarehouseConsignee mpWarehouseConsignee) {
        mpWarehouseConsignee.setUpdateBy(getUserId());
        return toAjax(mpWarehouseConsigneeService.changeStatus(mpWarehouseConsignee));
    }

    /**
     * 删除收货人信息
     */
    @Log(title = "收货人信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{consigneeIds}")
    public AjaxResult remove(@PathVariable Long[] consigneeIds) {
        return toAjax(mpWarehouseConsigneeService.deleteMpWarehouseConsigneeByConsigneeIds(consigneeIds));
    }

    /**
     * 检查收货人代码是否已存在
     */
    @GetMapping("/checkConsigneeCode/{consigneeCode}")
    public AjaxResult checkConsigneeCode(@PathVariable("consigneeCode") String consigneeCode) {
        MpWarehouseConsignee consignee = mpWarehouseConsigneeService.selectMpWarehouseConsigneeByConsigneeCode(consigneeCode);
        if (consignee != null) {
            // 使用exist标记表示收货人代码已存在
            consignee.setExist(true);
        }
        return AjaxResult.success(consignee);
    }
}
