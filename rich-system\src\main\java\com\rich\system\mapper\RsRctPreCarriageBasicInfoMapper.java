package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsRctPreCarriageBasicInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 操作单前程运输基础信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-15
 */
@Mapper
public interface RsRctPreCarriageBasicInfoMapper {
    /**
     * 查询操作单前程运输基础信息
     *
     * @param preCarriageInfoId 操作单前程运输基础信息主键
     * @return 操作单前程运输基础信息
     */
    RsRctPreCarriageBasicInfo selectRsRctPreCarriageBasicInfoByRctId(Long rctId);

    /**
     * 查询操作单前程运输基础信息列表
     *
     * @param rsRctPreCarriageBasicInfo 操作单前程运输基础信息
     * @return 操作单前程运输基础信息集合
     */
    List<RsRctPreCarriageBasicInfo> selectRsRctPreCarriageBasicInfoList(RsRctPreCarriageBasicInfo rsRctPreCarriageBasicInfo);

    /**
     * 新增操作单前程运输基础信息
     *
     * @param rsRctPreCarriageBasicInfo 操作单前程运输基础信息
     * @return 结果
     */
    int insertRsRctPreCarriageBasicInfo(RsRctPreCarriageBasicInfo rsRctPreCarriageBasicInfo);

    /**
     * 修改操作单前程运输基础信息
     *
     * @param rsRctPreCarriageBasicInfo 操作单前程运输基础信息
     * @return 结果
     */
    int updateRsRctPreCarriageBasicInfo(RsRctPreCarriageBasicInfo rsRctPreCarriageBasicInfo);

    /**
     * 删除操作单前程运输基础信息
     *
     * @return 结果
     */
    int deleteRsRctPreCarriageBasicInfoById(Long rctId);

    /**
     * 批量删除操作单前程运输基础信息
     *
     * @return 结果
     */
    int deleteRsRctPreCarriageBasicInfoByIds(Long[] rctIds);
}
