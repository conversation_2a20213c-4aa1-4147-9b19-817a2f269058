package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.BasServiceDetails;
import org.apache.ibatis.annotations.Mapper;

/**
 * 服务明细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@Mapper
public interface BasServiceDetailsMapper {
    /**
     * 查询服务明细
     *
     * @param serviceDetailsCode 服务明细主键
     * @return 服务明细
     */
    BasServiceDetails selectBasServiceDetailsByServiceDetailsCode(String serviceDetailsCode);

    /**
     * 查询服务明细列表
     *
     * @param basServiceDetails 服务明细
     * @return 服务明细集合
     */
    List<BasServiceDetails> selectBasServiceDetailsList(BasServiceDetails basServiceDetails);

    /**
     * 新增服务明细
     *
     * @param basServiceDetails 服务明细
     * @return 结果
     */
    int insertBasServiceDetails(BasServiceDetails basServiceDetails);

    /**
     * 修改服务明细
     *
     * @param basServiceDetails 服务明细
     * @return 结果
     */
    int updateBasServiceDetails(BasServiceDetails basServiceDetails);

    /**
     * 删除服务明细
     *
     * @param serviceDetailsCode 服务明细主键
     * @return 结果
     */
    int deleteBasServiceDetailsByServiceDetailsCode(String serviceDetailsCode);

    /**
     * 批量删除服务明细
     *
     * @param serviceDetailsCodes 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasServiceDetailsByServiceDetailsCodes(String[] serviceDetailsCodes);
}
