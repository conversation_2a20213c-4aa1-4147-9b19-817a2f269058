package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.BasProcess;
import com.rich.common.core.domain.entity.BasProcessType;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.StringUtils;
import com.rich.system.mapper.BasProcessTypeMapper;
import com.rich.system.service.BasProcessTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 进度分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-05
 */
@Service
public class BasProcessTypeServiceImpl implements BasProcessTypeService {
    @Autowired
    private BasProcessTypeMapper basProcessTypeMapper;

    /**
     * 查询进度分类
     *
     * @param processTypeId 进度分类主键
     * @return 进度分类
     */
    @Override
    public BasProcessType selectBasProcessTypeByProcessTypeId(Long processTypeId) {
        return basProcessTypeMapper.selectBasProcessTypeByProcessTypeId(processTypeId);
    }

    /**
     * 查询进度分类列表
     *
     * @param basProcessType 进度分类
     * @return 进度分类
     */
    @Override
    public List<BasProcessType> selectBasProcessTypeList(BasProcessType basProcessType) {
        return basProcessTypeMapper.selectBasProcessTypeList(basProcessType);
    }

    /**
     * 新增进度分类
     *
     * @param basProcessType 进度分类
     * @return 结果
     */
    @Transactional
    @Override
    public int insertBasProcessType(BasProcessType basProcessType) {
        basProcessType.setCreateTime(DateUtils.getNowDate());
        basProcessType.setCreateBy(SecurityUtils.getUserId());
        return basProcessTypeMapper.insertBasProcessType(basProcessType);
    }

    /**
     * 修改进度分类
     *
     * @param basProcessType 进度分类
     * @return 结果
     */
    @Transactional
    @Override
    public int updateBasProcessType(BasProcessType basProcessType) {
        basProcessType.setUpdateTime(DateUtils.getNowDate());
        basProcessType.setUpdateBy(SecurityUtils.getUserId());
        return basProcessTypeMapper.updateBasProcessType(basProcessType);
    }

    /**
     * 修改进度分类状态
     *
     * @param basProcessType 进度分类
     * @return 进度分类
     */
    @Override
    public int changeStatus(BasProcessType basProcessType) {
        return basProcessTypeMapper.updateBasProcessType(basProcessType);
    }

    /**
     * 批量删除进度分类
     *
     * @param processTypeIds 需要删除的进度分类主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteBasProcessTypeByProcessTypeIds(Long[] processTypeIds) {
        return basProcessTypeMapper.deleteBasProcessTypeByProcessTypeIds(processTypeIds);
    }

    /**
     * 删除进度分类信息
     *
     * @param processTypeId 进度分类主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteBasProcessTypeByProcessTypeId(Long processTypeId) {
        return basProcessTypeMapper.deleteBasProcessTypeByProcessTypeId(processTypeId);
    }
}
