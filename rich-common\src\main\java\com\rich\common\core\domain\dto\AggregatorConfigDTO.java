package com.rich.common.core.domain.dto;

import com.rich.common.core.domain.entity.RsAggregatorConfigs;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/18 18:19
 * @Version 1.0
 */

/**
 * 数据汇总配置DTO
 */
public class AggregatorConfigDTO {
    /**
     * 配置名称
     */
    private String name;

    /**
     * 配置详情
     */
    private ConfigDetail config;

    private String type;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public ConfigDetail getConfig() {
        return config;
    }

    public void setConfig(ConfigDetail config) {
        this.config = config;
    }

    /**
     * 配置详情类
     */
    public static class ConfigDetail {
        /**
         * 配置类型（search-搜索配置，aggregator-汇总配置）
         */
        private String type;

        /**
         * 主分组字段（用于汇总配置）
         */
        private String primaryField;

        /**
         * 匹配选项（用于汇总配置）
         */
        private MatchOptions matchOptions;

        /**
         * 日期字段（用于汇总配置）
         */
        private String dateField;

        /**
         * 日期选项（用于汇总配置）
         */
        private DateOptions dateOptions;

        /**
         * 是否显示明细（用于汇总配置）
         */
        private Boolean showDetails;

        /**
         * 字段配置列表（用于汇总配置）
         */
        private List<FieldConfig> fields;

        /**
         * 搜索条件列表（用于搜索配置）
         */
        private List<SearchCondition> searchConditions;

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getPrimaryField() {
            return primaryField;
        }

        public void setPrimaryField(String primaryField) {
            this.primaryField = primaryField;
        }

        public MatchOptions getMatchOptions() {
            return matchOptions;
        }

        public void setMatchOptions(MatchOptions matchOptions) {
            this.matchOptions = matchOptions;
        }

        public String getDateField() {
            return dateField;
        }

        public void setDateField(String dateField) {
            this.dateField = dateField;
        }

        public DateOptions getDateOptions() {
            return dateOptions;
        }

        public void setDateOptions(DateOptions dateOptions) {
            this.dateOptions = dateOptions;
        }

        public Boolean getShowDetails() {
            return showDetails;
        }

        public void setShowDetails(Boolean showDetails) {
            this.showDetails = showDetails;
        }

        public List<FieldConfig> getFields() {
            return fields;
        }

        public void setFields(List<FieldConfig> fields) {
            this.fields = fields;
        }

        public List<SearchCondition> getSearchConditions() {
            return searchConditions;
        }

        public void setSearchConditions(List<SearchCondition> searchConditions) {
            this.searchConditions = searchConditions;
        }
    }

    /**
     * 匹配选项类
     */
    public static class MatchOptions {
        /**
         * 是否精确匹配
         */
        private Boolean exact;

        /**
         * 是否区分大小写
         */
        private Boolean caseSensitive;

        public Boolean getExact() {
            return exact;
        }

        public void setExact(Boolean exact) {
            this.exact = exact;
        }

        public Boolean getCaseSensitive() {
            return caseSensitive;
        }

        public void setCaseSensitive(Boolean caseSensitive) {
            this.caseSensitive = caseSensitive;
        }
    }

    /**
     * 日期选项类
     */
    public static class DateOptions {
        /**
         * 是否转换为数字
         */
        private Boolean convertToNumber;

        /**
         * 是否按年/月/日格式化
         */
        private Boolean formatByYMD;

        private String formatType;

        public String getFormatType() {
            return formatType;
        }

        public void setFormatType(String formatType) {
            this.formatType = formatType;
        }

        public Boolean getConvertToNumber() {
            return convertToNumber;
        }

        public void setConvertToNumber(Boolean convertToNumber) {
            this.convertToNumber = convertToNumber;
        }

        public Boolean getFormatByYMD() {
            return formatByYMD;
        }

        public void setFormatByYMD(Boolean formatByYMD) {
            this.formatByYMD = formatByYMD;
        }
    }

    /**
     * 字段配置类
     */
    public static class FieldConfig {
        /**
         * 字段标识
         */
        private String fieldKey;

        /**
         * 汇总方式
         */
        private String aggregation;

        /**
         * 显示格式
         */
        private String format;

        /**
         * 排序方式
         */
        private String sort;

        public String getFieldKey() {
            return fieldKey;
        }

        public void setFieldKey(String fieldKey) {
            this.fieldKey = fieldKey;
        }

        public String getAggregation() {
            return aggregation;
        }

        public void setAggregation(String aggregation) {
            this.aggregation = aggregation;
        }

        public String getFormat() {
            return format;
        }

        public void setFormat(String format) {
            this.format = format;
        }

        public String getSort() {
            return sort;
        }

        public void setSort(String sort) {
            this.sort = sort;
        }
    }

    /**
     * 搜索条件类
     */
    public static class SearchCondition {
        /**
         * 字段标识
         */
        private String field;

        /**
         * 字段值
         */
        private Object value;

        /**
         * 字段类型
         */
        private String type;

        public String getField() {
            return field;
        }

        public void setField(String field) {
            this.field = field;
        }

        public Object getValue() {
            return value;
        }

        public void setValue(Object value) {
            this.value = value;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }
    }
}