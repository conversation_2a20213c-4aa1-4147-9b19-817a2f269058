<template>
  <view class="rich-search-select">
    <view :class="{ 'is-focus': isFocus, 'is-disabled': disabled }" class="select-input-wrapper">
      <input
          v-model="searchText"
          :disabled="disabled"
          :placeholder="placeholder"
          class="select-input"
          type="text"
          :readonly="readonlyInput"
          @blur="onBlur"
          @focus="handleFocus"
          @input="onInput"
          @click="toggleDropdown"
      />
      <view class="select-icon" @click="toggleDropdown">
        <text :class="{'select-arrow-up': isFocus}" class="select-arrow">▼</text>
      </view>
    </view>

    <view v-if="isFocus && filteredOptions.length > 0 && !disabled"
          :class="{'select-dropdown-top': showOnTop}"
          class="select-dropdown">
      <scroll-view class="select-dropdown-scroll" scroll-y="true">
        <view
            v-for="(item, index) in filteredOptions"
            :key="index"
            :class="{ 'is-active': selectedValue === item.value }"
            class="select-option"
            @click="selectOption(item)"
        >
          {{ item.text }}
        </view>
      </scroll-view>
    </view>

    <view v-if="isFocus && filteredOptions.length === 0 && !disabled"
          :class="{'select-dropdown-top': showOnTop}"
          class="select-dropdown select-dropdown-empty">
      <view class="select-empty">暂无数据</view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'rich-search-select',
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    localdata: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    keepInputValue: {
      type: Boolean,
      default: false,
      description: '是否保留用户输入的内容作为值，即使该内容不在选项列表中'
    },
    readonlyInput: {
      type: Boolean,
      default: true,
      description: '是否将输入框设为只读，避免弹出键盘'
    },
    showOnTop: {
      type: Boolean,
      default: false,
      description: '是否在输入框上方显示下拉列表，避免被键盘遮挡'
    }
  },
  data() {
    return {
      isFocus: false,
      searchText: '',
      selectedValue: '',
      selectedText: ''
    }
  },
  computed: {
    filteredOptions() {
      if (!this.searchText) {
        return this.localdata;
      }

      return this.localdata.filter(item => {
        return item.text.toString().toLowerCase().includes(this.searchText.toLowerCase()) ||
            item.value.toString().toLowerCase().includes(this.searchText.toLowerCase());
      });
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.selectedValue = newVal;
        this.updateSelectedText();
      },
      immediate: true
    }
  },
  methods: {
    toggleDropdown() {
      if (this.disabled) return;

      this.isFocus = !this.isFocus;
      if (this.isFocus) {
        // 如果不是只读模式，聚焦到输入框
        if (!this.readonlyInput) {
          this.$nextTick(() => {
            const inputEl = this.$el.querySelector('.select-input');
            if (inputEl) {
              inputEl.focus();
            }
          });
        }
      }
    },
    handleFocus(e) {
      if (this.disabled) return;
      this.isFocus = true;
      this.$emit('focus', e);
    },
    onFocus() {
      if (this.disabled) return;
      this.isFocus = true;
      this.$emit('focus');
    },
    onBlur(e) {
      if (this.disabled) return;
      // 延迟关闭，以便点击选项时能够触发选择
      setTimeout(() => {
        this.isFocus = false;
        // 如果有输入内容但没有匹配的选项，根据keepInputValue决定是否保留用户输入
        if (this.keepInputValue && this.searchText && !this.localdata.some(item => item.value === this.selectedValue)) {
          this.selectedValue = this.searchText;
          this.selectedText = this.searchText;
          this.$emit('input', this.searchText);
          this.$emit('change', this.searchText);
        } else {
          this.updateSelectedText();
        }
      }, 200);
    },
    onInput() {
      if (this.disabled) return;
      // 当输入内容为空时，清除选中的选项
      if (!this.searchText) {
        this.selectedValue = '';
        this.selectedText = '';
        this.$emit('input', '');
        this.$emit('change', '');
      }
      // 输入时触发，可以在这里添加自定义逻辑
      this.$emit('input', this.selectedValue);
    },
    selectOption(item) {
      if (this.disabled) return;

      this.selectedValue = item.value;
      this.selectedText = item.text;
      this.searchText = item.text;
      this.isFocus = false;

      this.$emit('input', this.selectedValue);
      this.$emit('change', this.selectedValue);
    },
    updateSelectedText() {
      // 根据value找到对应的text
      const selectedItem = this.localdata.find(item => item.value === this.selectedValue);
      if (selectedItem) {
        this.selectedText = selectedItem.text;
        this.searchText = selectedItem.text;
      } else if (this.keepInputValue && this.selectedValue) {
        // 如果没有在选项中找到，但有值且允许保留用户输入，则保留该值作为显示文本
        this.searchText = this.selectedValue;
        this.selectedText = this.selectedValue;
      } else {
        this.searchText = '';
        this.selectedText = '';
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.rich-search-select {
  position: relative;
  width: 100%;

  .select-input-wrapper {
    display: flex;
    position: relative;
    height: 35px;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
    transition: all 0.3s;

    &.is-focus {
      border-color: #2979ff;
    }

    &.is-disabled {
      background-color: #f5f5f5;
      cursor: not-allowed;

      .select-input, .select-icon {
        background-color: #f5f5f5;
        color: #999;
        cursor: not-allowed;
      }
    }
  }

  .select-input {
    flex: 1;
    height: 100%;
    padding: 0 10px;
    font-size: 14px;
    color: #333;
    background-color: rgb(255, 242, 204);

    &:disabled {
      background-color: #f5f5f5;
      color: #999;
      cursor: not-allowed;
    }
  }

  .select-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 35px;
    height: 100%;
    background-color: rgb(255, 242, 204);
    cursor: pointer;
  }

  .select-arrow {
    font-size: 12px;
    color: #666;
    transition: transform 0.3s;

    &-up {
      transform: rotate(180deg);
    }
  }

  .select-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    max-height: 200px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 4px 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 999;
    margin-top: -1px;

    &-top {
      top: auto;
      bottom: 100%;
      border-top: 1px solid #ddd;
      border-bottom: none;
      border-radius: 4px 4px 0 0;
      margin-top: 0;
      margin-bottom: -1px;
    }

    &-scroll {
      max-height: 200px;
    }

    &-empty {
      padding: 10px 0;
      text-align: center;
      color: #999;
    }
  }

  .select-option {
    padding: 8px 10px;
    font-size: 14px;
    cursor: pointer;

    &:hover {
      background-color: #f5f7fa;
    }

    &.is-active {
      background-color: #e6f1fc;
      color: #2979ff;
    }
  }

  .select-empty {
    padding: 15px 0;
    text-align: center;
    color: #999;
    font-size: 14px;
  }
}
</style> 