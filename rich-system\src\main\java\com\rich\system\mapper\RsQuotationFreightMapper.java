package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsQuotationFreight;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 报价详细Mapper接口
 *
 * <AUTHOR>
 * @date 2023-05-12
 */
@Mapper
public interface RsQuotationFreightMapper {
    /**
     * 查询报价详细
     *
     * @param quotationId 报价详细主键
     * @return 报价详细
     */
    List<RsQuotationFreight> selectRsQuotationFreightByQuotationId(Long quotationId);

    /**
     * 查询报价详细列表
     *
     * @param rsQuotationFreight 报价详细
     * @return 报价详细集合
     */
    List<RsQuotationFreight> selectRsQuotationFreightList(RsQuotationFreight rsQuotationFreight);

    /**
     * 新增报价详细
     *
     * @param rsQuotationFreight 报价详细
     * @return 结果
     */
    int insertRsQuotationFreight(RsQuotationFreight rsQuotationFreight);

    /**
     * 修改报价详细
     *
     * @param rsQuotationFreight 报价详细
     * @return 结果
     */
    int updateRsQuotationFreight(RsQuotationFreight rsQuotationFreight);

    /**
     * 删除报价详细
     *
     * @param quotationId 报价详细主键
     * @return 结果
     */
    int deleteRsQuotationFreightByQuotationId(Long quotationId);

    /**
     * 批量删除报价详细
     *
     * @param quotationIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsQuotationFreightByQuotationIds(Long[] quotationIds);

    int BatchRsQuotationFreight(List<RsQuotationFreight> rsQuotationFreightList);

    int checkExist(Long f);
}
