package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasDistLine;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasDistLineService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 航线Controller
 *
 * <AUTHOR>
 * @date 2022-08-22
 */
@RestController
@RequestMapping("/system/line")
public class BasDistLineController extends BaseController {

    @Autowired
    private  BasDistLineService basDistLineService;

    @Autowired
    private  RedisCache redisCache;

    /**
     * 查询航线列表
     */
    @PreAuthorize("@ss.hasPermi('system:line:list')")
    @GetMapping("/list")
    public AjaxResult list(BasDistLine basDistLine) {
        return AjaxResult.success(basDistLineService.selectBasDistLineList(basDistLine));
    }

    @GetMapping("/selectList")
    public AjaxResult selectList(BasDistLine basDistLine) {
        List<BasDistLine> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        if (list == null) {
            basDistLine.setStatus("0");
            list = basDistLineService.selectBasDistLineList(basDistLine);
            redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "line");
            redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "line", list);
        }
        return AjaxResult.success(AjaxResult.DATA_TAG, list);
    }

    /**
     * 导出航线列表
     */
    @PreAuthorize("@ss.hasPermi('system:line:export')")
    @Log(title = "航线", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasDistLine basDistLine) {
        List<BasDistLine> list = basDistLineService.selectBasDistLineList(basDistLine);
        ExcelUtil<BasDistLine> util = new ExcelUtil<BasDistLine>(BasDistLine.class);
        util.exportExcel(response, list, "航线数据");
    }

    /**
     * 获取航线详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:line:edit')")
    @GetMapping(value = "/{lineId}")
    public AjaxResult getInfo(@PathVariable("lineId") Long lineId) {
        return AjaxResult.success(basDistLineService.selectBasDistLineByLineId(lineId));
    }

    /**
     * 新增航线
     */
    @PreAuthorize("@ss.hasPermi('system:line:add')")
    @Log(title = "航线", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasDistLine basDistLine) {
        int out = basDistLineService.insertBasDistLine(basDistLine);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "line");
        return toAjax(out);
    }

    /**
     * 修改航线
     */
    @PreAuthorize("@ss.hasPermi('system:line:edit')")
    @Log(title = "航线", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasDistLine basDistLine) {
        int out = basDistLineService.updateBasDistLine(basDistLine);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "line");
        return toAjax(out);
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('system:line:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasDistLine basDistLine) {
        basDistLine.setUpdateBy(getUserId());
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "line");
        return toAjax(basDistLineService.changeStatus(basDistLine));
    }

    /**
     * 删除航线
     */
    @PreAuthorize("@ss.hasPermi('system:line:remove')")
    @Log(title = "航线", businessType = BusinessType.DELETE)
    @DeleteMapping("/{lineIds}")
    public AjaxResult remove(@PathVariable Long[] lineIds) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "line");
        return toAjax(basDistLineService.deleteBasDistLineByLineIds(lineIds));
    }
}
