package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpRoroShip;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsOpRoroShipMapper;
import com.rich.system.service.RsOpRoroShipService;

/**
 * 滚装船服务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Service
public class RsOpRoroShipServiceImpl implements RsOpRoroShipService {
    @Autowired
    private RsOpRoroShipMapper rsOpRoroShipMapper;

    /**
     * 查询滚装船服务
     *
     * @param roroShipId 滚装船服务主键
     * @return 滚装船服务
     */
    @Override
    public RsOpRoroShip selectRsOpRoroShipByRoroShipId(Long roroShipId) {
        return rsOpRoroShipMapper.selectRsOpRoroShipByRoroShipId(roroShipId);
    }

    /**
     * 查询滚装船服务列表
     *
     * @param rsOpRoroShip 滚装船服务
     * @return 滚装船服务
     */
    @Override
    public List<RsOpRoroShip> selectRsOpRoroShipList(RsOpRoroShip rsOpRoroShip) {
        return rsOpRoroShipMapper.selectRsOpRoroShipList(rsOpRoroShip);
    }

    /**
     * 新增滚装船服务
     *
     * @param rsOpRoroShip 滚装船服务
     * @return 结果
     */
    @Override
    public int insertRsOpRoroShip(RsOpRoroShip rsOpRoroShip) {
        return rsOpRoroShipMapper.insertRsOpRoroShip(rsOpRoroShip);
    }

    /**
     * 修改滚装船服务
     *
     * @param rsOpRoroShip 滚装船服务
     * @return 结果
     */
    @Override
    public int updateRsOpRoroShip(RsOpRoroShip rsOpRoroShip) {
        return rsOpRoroShipMapper.updateRsOpRoroShip(rsOpRoroShip);
    }

    /**
     * 修改滚装船服务状态
     *
     * @param rsOpRoroShip 滚装船服务
     * @return 滚装船服务
     */
    @Override
    public int changeStatus(RsOpRoroShip rsOpRoroShip) {
        return rsOpRoroShipMapper.updateRsOpRoroShip(rsOpRoroShip);
    }

    /**
     * 批量删除滚装船服务
     *
     * @param roroShipIds 需要删除的滚装船服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpRoroShipByRoroShipIds(Long[] roroShipIds) {
        return rsOpRoroShipMapper.deleteRsOpRoroShipByRoroShipIds(roroShipIds);
    }

    /**
     * 删除滚装船服务信息
     *
     * @param roroShipId 滚装船服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpRoroShipByRoroShipId(Long roroShipId) {
        return rsOpRoroShipMapper.deleteRsOpRoroShipByRoroShipId(roroShipId);
    }
}
