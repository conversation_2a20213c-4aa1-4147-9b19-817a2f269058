package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.MpWarehouseConsignee;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.MpWarehouseConsigneeMapper;
import com.rich.system.service.MpWarehouseConsigneeService;
import com.rich.common.exception.ServiceException;

/**
 * 收货人信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Service
public class MpWarehouseConsigneeServiceImpl implements MpWarehouseConsigneeService {
    @Autowired
    private MpWarehouseConsigneeMapper mpWarehouseConsigneeMapper;

    /**
     * 查询收货人信息
     *
     * @param consigneeId 收货人信息主键
     * @return 收货人信息
     */
    @Override
    public MpWarehouseConsignee selectMpWarehouseConsigneeByConsigneeId(Long consigneeId) {
        return mpWarehouseConsigneeMapper.selectMpWarehouseConsigneeByConsigneeId(consigneeId);
    }

    /**
     * 查询收货人信息列表
     *
     * @param mpWarehouseConsignee 收货人信息
     * @return 收货人信息
     */
    @Override
    public List<MpWarehouseConsignee> selectMpWarehouseConsigneeList(MpWarehouseConsignee mpWarehouseConsignee) {
        return mpWarehouseConsigneeMapper.selectMpWarehouseConsigneeList(mpWarehouseConsignee);
    }

    /**
     * 新增收货人信息
     *
     * @param mpWarehouseConsignee 收货人信息
     * @return 结果
     */
    @Override
    public int insertMpWarehouseConsignee(MpWarehouseConsignee mpWarehouseConsignee) {
        MpWarehouseConsignee mpWarehouseConsignee1 = mpWarehouseConsigneeMapper.selectMpWarehouseConsigneeByConsigneeCodeAndClientCode(mpWarehouseConsignee.getConsigneeCode(), mpWarehouseConsignee.getClientCode());
        if (mpWarehouseConsignee1 != null) {
            throw new RuntimeException("收货人代码重复");
        }
        return mpWarehouseConsigneeMapper.insertMpWarehouseConsignee(mpWarehouseConsignee);
    }

    /**
     * 修改收货人信息
     *
     * @param mpWarehouseConsignee 收货人信息
     * @return 结果
     */
    @Override
    public int updateMpWarehouseConsignee(MpWarehouseConsignee mpWarehouseConsignee) {
        return mpWarehouseConsigneeMapper.updateMpWarehouseConsignee(mpWarehouseConsignee);
    }

    /**
     * 修改收货人信息状态
     *
     * @param mpWarehouseConsignee 收货人信息
     * @return 收货人信息
     */
    @Override
    public int changeStatus(MpWarehouseConsignee mpWarehouseConsignee) {
        return mpWarehouseConsigneeMapper.updateMpWarehouseConsignee(mpWarehouseConsignee);
    }

    @Override
    public MpWarehouseConsignee selectMpWarehouseConsigneeByuserId(Long userId) {
        return mpWarehouseConsigneeMapper.selectMpWarehouseConsigneeByuserId(userId);
    }

    @Override
    public int bindConsignee(Long consigneeId, Long userId) {
        // 该收货人还未被绑定才可以进行绑定
        MpWarehouseConsignee mpWarehouseConsignee1 = mpWarehouseConsigneeMapper.selectMpWarehouseConsigneeByConsigneeId(consigneeId);
        if (mpWarehouseConsignee1 != null && mpWarehouseConsignee1.getUserId() == null) {
            MpWarehouseConsignee mpWarehouseConsignee = new MpWarehouseConsignee();
            mpWarehouseConsignee.setConsigneeId(consigneeId);
            mpWarehouseConsignee.setUserId(userId);
            return mpWarehouseConsigneeMapper.updateMpWarehouseConsignee(mpWarehouseConsignee);
        } else {
            throw new ServiceException("该收货人信息已被绑定，无法重复绑定");
        }
    }

    /**
     * 根据收货人代码查询收货人信息
     *
     * @param consigneeCode 收货人代码
     * @return 收货人信息
     */
    @Override
    public MpWarehouseConsignee selectMpWarehouseConsigneeByConsigneeCode(String consigneeCode) {
        return mpWarehouseConsigneeMapper.selectMpWarehouseConsigneeByConsigneeCode(consigneeCode);
    }

    /**
     * 批量删除收货人信息
     *
     * @param consigneeIds 需要删除的收货人信息主键
     * @return 结果
     */
    @Override
    public int deleteMpWarehouseConsigneeByConsigneeIds(Long[] consigneeIds) {
        return mpWarehouseConsigneeMapper.deleteMpWarehouseConsigneeByConsigneeIds(consigneeIds);
    }

    /**
     * 删除收货人信息信息
     *
     * @param consigneeId 收货人信息主键
     * @return 结果
     */
    @Override
    public int deleteMpWarehouseConsigneeByConsigneeId(Long consigneeId) {
        return mpWarehouseConsigneeMapper.deleteMpWarehouseConsigneeByConsigneeId(consigneeId);
    }
}
