package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.BasDocReleaseWay;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.system.mapper.BasDocReleaseWayMapper;
import com.rich.system.service.BasDocReleaseWayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 交单方式Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@Service
public class BasDocReleaseWayServiceImpl implements BasDocReleaseWayService {
    @Autowired
    private BasDocReleaseWayMapper basDocReleaseWayMapper;

    /**
     * 查询交单方式
     *
     * @param releaseWayId 交单方式主键
     * @return 交单方式
     */
    @Override
    public BasDocReleaseWay selectBasDocReleaseWayByReleaseWayId(Long releaseWayId) {
        return basDocReleaseWayMapper.selectBasDocReleaseWayByReleaseWayId(releaseWayId);
    }

    /**
     * 查询交单方式列表
     *
     * @param basDocReleaseWay 交单方式
     * @return 交单方式
     */
    @Override
    public List<BasDocReleaseWay> selectBasDocReleaseWayList(BasDocReleaseWay basDocReleaseWay) {
        return basDocReleaseWayMapper.selectBasDocReleaseWayList(basDocReleaseWay);
    }

    /**
     * 新增交单方式
     *
     * @param basDocReleaseWay 交单方式
     * @return 结果
     */
    @Override
    public int insertBasDocReleaseWay(BasDocReleaseWay basDocReleaseWay) {
        basDocReleaseWay.setCreateTime(DateUtils.getNowDate());
        basDocReleaseWay.setCreateBy(SecurityUtils.getUserId());
        return basDocReleaseWayMapper.insertBasDocReleaseWay(basDocReleaseWay);
    }

    /**
     * 修改交单方式
     *
     * @param basDocReleaseWay 交单方式
     * @return 结果
     */
    @Override
    public int updateBasDocReleaseWay(BasDocReleaseWay basDocReleaseWay) {
        basDocReleaseWay.setUpdateTime(DateUtils.getNowDate());
        basDocReleaseWay.setUpdateBy(SecurityUtils.getUserId());
        return basDocReleaseWayMapper.updateBasDocReleaseWay(basDocReleaseWay);
    }

    /**
     * 修改交单方式状态
     *
     * @param basDocReleaseWay 交单方式
     * @return 交单方式
     */
    @Override
    public int changeStatus(BasDocReleaseWay basDocReleaseWay) {
        return basDocReleaseWayMapper.updateBasDocReleaseWay(basDocReleaseWay);
    }

    /**
     * 批量删除交单方式
     *
     * @param releaseWayIds 需要删除的交单方式主键
     * @return 结果
     */
    @Override
    public int deleteBasDocReleaseWayByReleaseWayIds(Long[] releaseWayIds) {
        return basDocReleaseWayMapper.deleteBasDocReleaseWayByReleaseWayIds(releaseWayIds);
    }

    /**
     * 删除交单方式信息
     *
     * @param releaseWayId 交单方式主键
     * @return 结果
     */
    @Override
    public int deleteBasDocReleaseWayByReleaseWayId(Long releaseWayId) {
        return basDocReleaseWayMapper.deleteBasDocReleaseWayByReleaseWayId(releaseWayId);
    }
}
