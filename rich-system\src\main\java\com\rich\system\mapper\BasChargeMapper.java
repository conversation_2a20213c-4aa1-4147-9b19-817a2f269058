package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasCharge;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 费用Mapper接口
 *
 * <AUTHOR>
 * @date 2022-08-30
 */
@Mapper
public interface BasChargeMapper {
    /**
     * 查询费用
     *
     * @param chargeId 费用主键
     * @return 费用
     */
    BasCharge selectBasChargeByChargeId(Long chargeId);

    /**
     * 查询费用列表
     *
     * @param basCharge 费用
     * @return 费用集合
     */
    List<BasCharge> selectBasChargeList(BasCharge basCharge);

    /**
     * 新增费用
     *
     * @param basCharge 费用
     * @return 结果
     */
    int insertBasCharge(BasCharge basCharge);

    /**
     * 修改费用
     *
     * @param basCharge 费用
     * @return 结果
     */
    int updateBasCharge(BasCharge basCharge);

    /**
     * 删除费用
     *
     * @param chargeId 费用主键
     * @return 结果
     */
    int deleteBasChargeByChargeId(Long chargeId);

    /**
     * 批量删除费用
     *
     * @param chargeIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasChargeByChargeIds(Long[] chargeIds);
}
