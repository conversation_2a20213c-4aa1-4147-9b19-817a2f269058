package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsBookingReceivablePayable;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 订舱单应收应付Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-15
 */
@Mapper
public interface RsBookingReceivablePayableMapper {
    /**
     * 查询订舱单应收应付
     *
     * @return 订舱单应收应付
     */
    List<RsBookingReceivablePayable> selectRsBookingReceivablePayable(Long bookingId, Long typeId, Long basicInfoId);

    /**
     * 查询订舱单应收应付列表
     *
     * @param rsBookingReceivablePayable 订舱单应收应付
     * @return 订舱单应收应付集合
     */
    List<RsBookingReceivablePayable> selectRsBookingReceivablePayableList(RsBookingReceivablePayable rsBookingReceivablePayable);

    /**
     * 新增订舱单应收应付
     *
     * @param rsBookingReceivablePayable 订舱单应收应付
     * @return 结果
     */
    int insertRsBookingReceivablePayable(RsBookingReceivablePayable rsBookingReceivablePayable);

    /**
     * 修改订舱单应收应付
     *
     * @param rsBookingReceivablePayable 订舱单应收应付
     * @return 结果
     */
    int updateRsBookingReceivablePayable(RsBookingReceivablePayable rsBookingReceivablePayable);

    /**
     * 删除订舱单应收应付
     *
     * @return 结果
     */
    int deleteRsBookingReceivablePayable(Long bookingId, Long typeId, Long basicInfoId);

    /**
     * 批量删除订舱单应收应付
     *
     * @return 结果
     */
    int deleteRsBookingReceivablePayableByIds(Long[] bookingIds);

    int deleteRsBookingReceivablePayableById(Long bookingId);
}
