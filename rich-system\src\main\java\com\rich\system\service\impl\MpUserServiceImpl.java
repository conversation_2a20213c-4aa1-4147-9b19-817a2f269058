package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.MpUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.MpUserMapper;
import com.rich.system.service.MpUserService;

/**
 * 用户信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Service
public class MpUserServiceImpl implements MpUserService {
    @Autowired
    private MpUserMapper mpUserMapper;

    /**
     * 查询用户信息
     *
     * @param userId 用户信息主键
     * @return 用户信息
     */
    @Override
    public MpUser selectMpUserByUserId(Long userId) {
        return mpUserMapper.selectMpUserByUserId(userId);
    }

    /**
     * 查询用户信息列表
     *
     * @param mpUser 用户信息
     * @return 用户信息
     */
    @Override
    public List<MpUser> selectMpUserList(MpUser mpUser) {
        return mpUserMapper.selectMpUserList(mpUser);
    }

    /**
     * 新增用户信息
     *
     * @param mpUser 用户信息
     * @return 结果
     */
    @Override
    public int insertMpUser(MpUser mpUser) {
        return mpUserMapper.insertMpUser(mpUser);
    }

    /**
     * 修改用户信息
     *
     * @param mpUser 用户信息
     * @return 结果
     */
    @Override
    public int updateMpUser(MpUser mpUser) {
        return mpUserMapper.updateMpUser(mpUser);
    }

    /**
     * 修改用户信息状态
     *
     * @param mpUser 用户信息
     * @return 用户信息
     */
    @Override
    public int changeStatus(MpUser mpUser) {
        return mpUserMapper.updateMpUser(mpUser);
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户信息主键
     * @return 结果
     */
    @Override
    public int deleteMpUserByUserIds(Long[] userIds) {
        return mpUserMapper.deleteMpUserByUserIds(userIds);
    }

    /**
     * 删除用户信息信息
     *
     * @param userId 用户信息主键
     * @return 结果
     */
    @Override
    public int deleteMpUserByUserId(Long userId) {
        return mpUserMapper.deleteMpUserByUserId(userId);
    }
}
