
a818ce6d5c4d728243a3600890a429a70491c3fa	{"key":"make-fetch-happen:request-cache:https://registry.npmmirror.com/@anthropic-ai%2fclaude-code","integrity":"sha512-HD2YlX3qluNV7r1AEmLGtdlLADuI+iiPR4o+hlurWDaoEKe1MpiNmMb0kY1EstS1Ib7vsf2wOdklQSp8a+Pnwg==","time":1750750149363,"size":337223,"metadata":{"time":1750750149363,"url":"https://registry.npmmirror.com/@anthropic-ai%2fclaude-code","reqHeaders":{"accept":"application/json"},"resHeaders":{"cache-control":"public, max-age=300","date":"Tue, 24 Jun 2025 07:27:47 GMT","etag":"W/\"a8650da435c9dce10149c9406c944725dc0431f0\"","vary":"Origin, Accept, Accept-Encoding","content-encoding":"gzip","content-type":"application/json; charset=utf-8"},"options":{"compress":true}}}
b0da0a5a936a27507c0b7bc9022c609f29e3b467	{"key":"make-fetch-happen:request-cache:https://registry.npmmirror.com/@anthropic-ai%2fclaude-code","integrity":"sha512-HD2YlX3qluNV7r1AEmLGtdlLADuI+iiPR4o+hlurWDaoEKe1MpiNmMb0kY1EstS1Ib7vsf2wOdklQSp8a+Pnwg==","time":1750750159693,"size":337223,"metadata":{"time":1750750159693,"url":"https://registry.npmmirror.com/@anthropic-ai%2fclaude-code","reqHeaders":{"accept":"application/json"},"resHeaders":{"cache-control":"public, max-age=300","date":"Tue, 24 Jun 2025 07:27:47 GMT","etag":"W/\"a8650da435c9dce10149c9406c944725dc0431f0\"","vary":"Origin, Accept, Accept-Encoding","content-encoding":"gzip","content-type":"application/json; charset=utf-8"},"options":{"compress":true}}}