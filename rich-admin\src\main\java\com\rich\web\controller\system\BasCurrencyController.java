package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasCurrency;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasCurrencyService;
import com.rich.system.service.BasDistLocationService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 币种Controller
 *
 * <AUTHOR>
 * @date 2022-09-06
 */
@RestController
@RequestMapping("/system/currency")

public class BasCurrencyController extends BaseController {

    @Autowired
    private  BasCurrencyService basCurrencyService;

    @Autowired
    private  BasDistLocationService basDistLocationService;

    @Autowired
    private  RedisCache redisCache;

    /**
     * 查询币种列表
     */
    @PreAuthorize("@ss.hasPermi('system:currency:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasCurrency basCurrency) {
        startPage();
        List<BasCurrency> list = basCurrencyService.selectBasCurrencyList(basCurrency);
        return getDataTable(list);
    }

    @GetMapping("/selectList")
    public AjaxResult selectList(BasCurrency basCurrency) {
        List<BasCurrency> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "currency");
        if (list == null) {
            basCurrency.setStatus("0");
            list = basCurrencyService.selectBasCurrencyList(basCurrency);
            redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "currency");
            redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "currency", list);
        }
        return AjaxResult.success(AjaxResult.DATA_TAG, list);
    }

    /**
     * 导出币种列表
     */
    @PreAuthorize("@ss.hasPermi('system:currency:export')")
    @Log(title = "币种", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasCurrency basCurrency) {
        List<BasCurrency> list = basCurrencyService.selectBasCurrencyList(basCurrency);
        ExcelUtil<BasCurrency> util = new ExcelUtil<BasCurrency>(BasCurrency.class);
        util.exportExcel(response, list, "币种数据");
    }

    /**
     * 获取币种详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:currency:edit')")
    @GetMapping(value = "/{currencyId}")
    public AjaxResult getInfo(@PathVariable("currencyId") Long currencyId) {
        AjaxResult ajaxResult = AjaxResult.success();
        Set<Long> set = new HashSet<>();
        BasCurrency basCurrency = basCurrencyService.selectBasCurrencyByCurrencyId(currencyId);
        ajaxResult.put(AjaxResult.DATA_TAG, basCurrency);
        if(basCurrency.getLocationId()!=null){
            set.add(basCurrency.getLocationId());
        }
        ajaxResult.put("locationOptions", set.size() > 0 ? basDistLocationService.selectBasDistLocationByIds(set): null);
        return ajaxResult;
    }

    /**
     * 新增币种
     */
    @PreAuthorize("@ss.hasPermi('system:currency:add')")
    @Log(title = "币种", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasCurrency basCurrency) {
        int out = basCurrencyService.insertBasCurrency(basCurrency);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "currency");
        return toAjax(out);
    }

    /**
     * 修改币种
     */
    @PreAuthorize("@ss.hasPermi('system:currency:edit')")
    @Log(title = "币种", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasCurrency basCurrency) {
        int out = basCurrencyService.updateBasCurrency(basCurrency);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "currency");
        return toAjax(out);
    }

    /**
     * 修改币种
     */
    @PreAuthorize("@ss.hasPermi('system:currency:edit')")
    @Log(title = "币种", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasCurrency basCurrency) {
        int out = basCurrencyService.changeStatus(basCurrency);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "currency");
        return toAjax(out);
    }

    /**
     * 删除币种
     */
    @PreAuthorize("@ss.hasPermi('system:currency:remove')")
    @Log(title = "币种", businessType = BusinessType.DELETE)
    @DeleteMapping("/{currencyIds}")
    public AjaxResult remove(@PathVariable Long[] currencyIds) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "currency");
        return toAjax(basCurrencyService.deleteBasCurrencyByCurrencyIds(currencyIds));
    }
}
