package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasDocFlowDirection;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 文件流向Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@Mapper
public interface BasDocFlowDirectionMapper {
    /**
     * 查询文件流向
     *
     * @param docFlowDirectionId 文件流向主键
     * @return 文件流向
     */
    BasDocFlowDirection selectBasDocFlowDirectionByDocFlowDirectionId(Long docFlowDirectionId);

    /**
     * 查询文件流向列表
     *
     * @param basDocFlowDirection 文件流向
     * @return 文件流向集合
     */
    List<BasDocFlowDirection> selectBasDocFlowDirectionList(BasDocFlowDirection basDocFlowDirection);

    /**
     * 新增文件流向
     *
     * @param basDocFlowDirection 文件流向
     * @return 结果
     */
    int insertBasDocFlowDirection(BasDocFlowDirection basDocFlowDirection);

    /**
     * 修改文件流向
     *
     * @param basDocFlowDirection 文件流向
     * @return 结果
     */
    int updateBasDocFlowDirection(BasDocFlowDirection basDocFlowDirection);

    /**
     * 删除文件流向
     *
     * @param docFlowDirectionId 文件流向主键
     * @return 结果
     */
    int deleteBasDocFlowDirectionByDocFlowDirectionId(Long docFlowDirectionId);

    /**
     * 批量删除文件流向
     *
     * @param docFlowDirectionIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasDocFlowDirectionByDocFlowDirectionIds(Long[] docFlowDirectionIds);
}
