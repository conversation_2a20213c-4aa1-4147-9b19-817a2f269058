package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsBookingReceivablePayable;
import com.rich.common.core.domain.entity.RsRctReceivablePayable;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 操作单应收应付Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-15
 */
@Mapper
public interface RsRctReceivablePayableMapper {
    /**
     * 查询操作单应收应付
     *
     * @return 操作单应收应付
     */
    List<RsRctReceivablePayable> selectRsRctReceivablePayable(Long bookingId, Long logisticsTypeId, Long basicInfoId);

    /**
     * 查询操作单应收应付列表
     *
     * @param rsRctReceivablePayable 操作单应收应付
     * @return 操作单应收应付集合
     */
    List<RsRctReceivablePayable> selectRsRctReceivablePayableList(RsRctReceivablePayable rsRctReceivablePayable);

    /**
     * 新增操作单应收应付
     *
     * @param rsRctReceivablePayable 操作单应收应付
     * @return 结果
     */
    int insertRsRctReceivablePayable(RsRctReceivablePayable rsRctReceivablePayable);

    /**
     * 修改操作单应收应付
     *
     * @param rsRctReceivablePayable 操作单应收应付
     * @return 结果
     */
    int updateRsRctReceivablePayable(RsRctReceivablePayable rsRctReceivablePayable);

    /**
     * 删除操作单应收应付
     *
     * @return 结果
     */
    int deleteRsRctReceivablePayable(Long rctId, Long logisticsTypeId, Long basicInfoId);

    /**
     * 批量删除操作单应收应付
     *
     * @param receivablePayableIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsRctReceivablePayableByReceivablePayableIds(Long[] receivablePayableIds);

}
