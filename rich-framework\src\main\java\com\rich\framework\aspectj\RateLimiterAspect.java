package com.rich.framework.aspectj;


import com.rich.common.annotation.RateLimiter;
import com.rich.common.enums.LimitType;
import com.rich.common.exception.ServiceException;
import com.rich.common.utils.StringUtils;
import com.rich.common.utils.ip.IpUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;

/**
 * 限流处理
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class RateLimiterAspect {
    private static final Logger log = LoggerFactory.getLogger(RateLimiterAspect.class);

    private RedisTemplate<Object, Object> redisTemplate;

    private RedisScript<Long> limitScript;

    // 默认MP接口限流配置
    private static final int MP_LIMIT_TIME = 60; // 限流时间，单位秒
    private static final int MP_LIMIT_COUNT = 1000; // 限流次数
    private static final String MP_LIMIT_KEY = "mp-rate-limit:";

    @Autowired
    public void setRedisTemplate1(RedisTemplate<Object, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Autowired
    public void setLimitScript(RedisScript<Long> limitScript) {
        this.limitScript = limitScript;
    }

    @Before("@annotation(rateLimiter)")
    public void doBefore(JoinPoint point, RateLimiter rateLimiter) throws Throwable {
        int time = rateLimiter.time();
        int count = rateLimiter.count();

        String combineKey = getCombineKey(rateLimiter, point);
        List<Object> keys = Collections.singletonList(combineKey);
        try {
            Long number = redisTemplate.execute(limitScript, keys, count, time);
            if (StringUtils.isNull(number) || number.intValue() > count) {
                throw new ServiceException("访问过于频繁，请稍候再试");
            }
            log.info("限制请求'{}',当前请求'{}',缓存key'{}'", count, number.intValue(), combineKey);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new RuntimeException("服务器限流异常，请稍候再试");
        }
    }

    /**
     * 拦截所有/mp路径下的请求进行限流
     */
    /*@Around("execution(* com.rich.web.controller.system.Mp*Controller.*(..))")
    public Object doAround(ProceedingJoinPoint point) throws Throwable {
        // 获取请求路径
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String uri = request.getRequestURI();

        // 判断是否为/mp路径下的请求
        if (uri.startsWith("/mp")) {
            // 创建限流key，基于IP限流
            String ip = IpUtils.getIpAddr();
            String combineKey = MP_LIMIT_KEY + ip + ":" + uri;
            List<Object> keys = Collections.singletonList(combineKey);

            try {
                // 执行限流脚本
                Long number = redisTemplate.execute(limitScript, keys, MP_LIMIT_COUNT, MP_LIMIT_TIME);
                if (StringUtils.isNull(number) || number.intValue() > MP_LIMIT_COUNT) {
                    throw new ServiceException("访问过于频繁，请稍候再试");
                }
                log.info("MP接口限流：限制请求'{}',当前请求'{}',缓存key'{}'", MP_LIMIT_COUNT, number.intValue(), combineKey);

                // 继续执行原方法
                return point.proceed();
            } catch (ServiceException e) {
                throw e;
            } catch (Exception e) {
                throw new RuntimeException("服务器限流异常，请稍候再试");
            }
        }

        // 非/mp路径请求，直接放行
        return point.proceed();
    }*/

    public String getCombineKey(RateLimiter rateLimiter, JoinPoint point) {
        StringBuffer stringBuffer = new StringBuffer(rateLimiter.key());
        if (rateLimiter.limitType() == LimitType.IP) {
            stringBuffer.append(IpUtils.getIpAddr()).append("-");
        }
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        Class<?> targetClass = method.getDeclaringClass();
        stringBuffer.append(targetClass.getName()).append("-").append(method.getName());
        return stringBuffer.toString();
    }
}
