package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsDocDetail;
import org.apache.ibatis.annotations.Mapper;

/**
 * 操作文件Mapper接口
 *
 * <AUTHOR>
 * @date 2023-09-21
 */
@Mapper
public interface RsDocDetailMapper {
    /**
     * 查询操作文件
     *
     * @param docDetailId 操作文件主键
     * @return 操作文件
     */
    RsDocDetail selectRsDocDetailByDocDetailId(Long docDetailId);

    /**
     * 查询操作文件列表
     *
     * @param rsDocDetail 操作文件
     * @return 操作文件集合
     */
    List<RsDocDetail> selectRsDocDetailList(RsDocDetail rsDocDetail);

    /**
     * 新增操作文件
     *
     * @param rsDocDetail 操作文件
     * @return 结果
     */
    int insertRsDocDetail(RsDocDetail rsDocDetail);

    /**
     * 修改操作文件
     *
     * @param rsDocDetail 操作文件
     * @return 结果
     */
    int updateRsDocDetail(RsDocDetail rsDocDetail);

    /**
     * 删除操作文件
     *
     * @param docDetailId 操作文件主键
     * @return 结果
     */
    int deleteRsDocDetailByDocDetailId(Long docDetailId);

    /**
     * 批量删除操作文件
     *
     * @param docDetailIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsDocDetailByDocDetailIds(Long[] docDetailIds);
}
