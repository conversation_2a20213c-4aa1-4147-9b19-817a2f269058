package com.rich.system.mapper;

import com.rich.system.domain.MidCompanyRole;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 公司角色Mapper接口
 *
 * <AUTHOR>
 * @date 2022-09-07
 */
@Mapper
public interface MidCompanyRoleMapper {
    /**
     * 查询公司角色
     *
     * @param companyId 公司角色主键
     * @return 公司角色
     */
    List<Long> selectMidCompanyRoleByCompanyId(Long companyId);

    /**
     * 查询公司角色列表
     *
     * @param midCompanyRole 公司角色
     * @return 公司角色集合
     */
    List<MidCompanyRole> selectMidCompanyRoleList(MidCompanyRole midCompanyRole);

    /**
     * 新增公司角色
     *
     * @param midCompanyRole 公司角色
     * @return 结果
     */
    int insertMidCompanyRole(MidCompanyRole midCompanyRole);

    /**
     * 修改公司角色
     *
     * @param midCompanyRole 公司角色
     * @return 结果
     */
    int updateMidCompanyRole(MidCompanyRole midCompanyRole);

    /**
     * 删除公司角色
     *
     * @param companyId 公司角色主键
     * @return 结果
     */
    int deleteMidCompanyRoleByCompanyId(Long companyId);

    /**
     * 批量删除公司角色
     *
     * @param companyIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteMidCompanyRoleByCompanyIds(Long[] companyIds);

    int batchCompanyRole(List<MidCompanyRole> list);
}
