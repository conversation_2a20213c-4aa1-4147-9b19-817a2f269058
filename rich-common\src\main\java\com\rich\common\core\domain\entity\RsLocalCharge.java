package com.rich.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 物流附加费策略对象 rs_local_chaege
 *
 * <AUTHOR>
 * @date 2022-12-14
 */
public class RsLocalCharge extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * localChargeId
     */
    @Excel(name = "序号")
    private Long localChargeId;

    @Excel(name = "询价单号", width = 22)
    private String inquiryNo;

    /**
     * 服务类型ID
     */
    private Long serviceTypeId;
    @Excel(name = "服务类型")
    private String serviceType;

    private Long logisticsTypeId;
    @Excel(name = "物流类型")
    private String logisticsType;

    @Excel(name = "承运人")
    private String carrier;

    /**
     * 费用ID
     */
    private Long chargeId;
    @Excel(name = "费用类型")
    private String charge;
    private String chargeEn;

    /**
     * 币种ID
     */
    private Long currencyId;

    @Excel(name = "币种")
    private String currencyCode;

    private String currency;

    @Excel(name = "单位")
    private String unitCode;

    @Excel(name = "启运港")
    private String locationDeparture;

    private Long[] locationDepartureIds;

    @Excel(name = "目的航线")
    private String lineDestination;

    private Long[] lineDestinationIds;

    @Excel(name = "目的港")
    private String locationDestination;

    /**
     * 单位ID
     */
    private Long unitId;

    private String unit;

    /**
     * 20
     */
    @Excel(name = "20GP")
    private BigDecimal priceB;

    /**
     * 40
     */
    @Excel(name = "40GP")
    private BigDecimal priceC;

    /**
     * 20H
     */
    @Excel(name = "40HQ")
    private BigDecimal priceD;


    /**
     * 具体费用
     */
    @Excel(name = "具体费用")
    private BigDecimal priceA;

    /**
     * 有效期至
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "有效期至", width = 30, dateFormat = "yyyy-MM-dd")
    private Date validTo;

    /**
     * 有效期从
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "有效期从", width = 30, dateFormat = "yyyy-MM-dd")
    private Date validFrom;

    /**
     * 是否有效
     */
    @Excel(name = "是否有效")
    private String isValid;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;

    private String cargoType;

    private Long[] cargoTypeIds;

    //    @Excel(name = "启运航线")
    private String lineDeparture;

    private Long[] lineDepartureIds;

    private Long[] locationDestinationIds;


    private Long[] carrierIds;

    private List<Long> localIds;

    private Long supplierId;

    @Excel(name = "供应商")
    private String company;

    private Integer chargeOrderNum;

    private Integer chargeTypeOrderNum;


    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getChargeEn() {
        return chargeEn;
    }

    public void setChargeEn(String chargeEn) {
        this.chargeEn = chargeEn;
    }

    public Integer getChargeTypeOrderNum() {
        return chargeTypeOrderNum;
    }

    public void setChargeTypeOrderNum(Integer chargeTypeOrderNum) {
        this.chargeTypeOrderNum = chargeTypeOrderNum;
    }

    public Date getValidTo() {
        return validTo;
    }

    public void setValidTo(Date validTo) {
        this.validTo = validTo;
    }

    public Date getValidFrom() {
        return validFrom;
    }

    public void setValidFrom(Date validFrom) {
        this.validFrom = validFrom;
    }

    public String getInquiryNo() {
        return inquiryNo;
    }

    public void setInquiryNo(String inquiryNo) {
        this.inquiryNo = inquiryNo;
    }

    public Long getLogisticsTypeId() {
        return logisticsTypeId;
    }

    public void setLogisticsTypeId(Long logisticsTypeId) {
        this.logisticsTypeId = logisticsTypeId;
    }

    public String getLogisticsType() {
        return logisticsType;
    }

    public void setLogisticsType(String logisticsType) {
        this.logisticsType = logisticsType;
    }

    public Integer getChargeOrderNum() {
        return chargeOrderNum;
    }

    public void setChargeOrderNum(Integer chargeOrderNum) {
        this.chargeOrderNum = chargeOrderNum;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public List<Long> getLocalIds() {
        return localIds;
    }

    public void setLocalIds(List<Long> localIds) {
        this.localIds = localIds;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public String getCharge() {
        return charge;
    }

    public void setCharge(String charge) {
        this.charge = charge;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getCargoType() {
        return cargoType;
    }

    public void setCargoType(String cargoType) {
        this.cargoType = cargoType;
    }

    public Long[] getCargoTypeIds() {
        return cargoTypeIds;
    }

    public void setCargoTypeIds(Long[] cargoTypeIds) {
        this.cargoTypeIds = cargoTypeIds;
    }

    public String getLineDeparture() {
        return lineDeparture;
    }

    public void setLineDeparture(String lineDeparture) {
        this.lineDeparture = lineDeparture;
    }

    public Long[] getLineDepartureIds() {
        return lineDepartureIds;
    }

    public void setLineDepartureIds(Long[] lineDepartureIds) {
        this.lineDepartureIds = lineDepartureIds;
    }

    public String getLocationDeparture() {
        return locationDeparture;
    }

    public void setLocationDeparture(String locationDeparture) {
        this.locationDeparture = locationDeparture;
    }

    public Long[] getLocationDepartureIds() {
        return locationDepartureIds;
    }

    public void setLocationDepartureIds(Long[] locationDepartureIds) {
        this.locationDepartureIds = locationDepartureIds;
    }

    public String getLineDestination() {
        return lineDestination;
    }

    public void setLineDestination(String lineDestination) {
        this.lineDestination = lineDestination;
    }

    public Long[] getLineDestinationIds() {
        return lineDestinationIds;
    }

    public void setLineDestinationIds(Long[] lineDestinationIds) {
        this.lineDestinationIds = lineDestinationIds;
    }

    public String getLocationDestination() {
        return locationDestination;
    }

    public void setLocationDestination(String locationDestination) {
        this.locationDestination = locationDestination;
    }

    public Long[] getLocationDestinationIds() {
        return locationDestinationIds;
    }

    public void setLocationDestinationIds(Long[] locationDestinationIds) {
        this.locationDestinationIds = locationDestinationIds;
    }

    public String getCarrier() {
        return carrier;
    }

    public void setCarrier(String carrier) {
        this.carrier = carrier;
    }

    public Long[] getCarrierIds() {
        return carrierIds;
    }

    public void setCarrierIds(Long[] carrierIds) {
        this.carrierIds = carrierIds;
    }

    public Long getLocalChargeId() {
        return localChargeId;
    }

    public void setLocalChargeId(Long localChargeId) {
        this.localChargeId = localChargeId;
    }

    public void setServiceTypeId(Long serviceTypeId) {
        this.serviceTypeId = serviceTypeId;
    }

    public Long getServiceTypeId() {
        return serviceTypeId;
    }

    public void setChargeId(Long chargeId) {
        this.chargeId = chargeId;
    }

    public Long getChargeId() {
        return chargeId;
    }

    public void setCurrencyId(Long currencyId) {
        this.currencyId = currencyId;
    }

    public Long getCurrencyId() {
        return currencyId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public Long getUnitId() {
        return unitId;
    }

    public BigDecimal getPriceB() {
        return priceB;
    }

    public void setPriceB(BigDecimal priceB) {
        this.priceB = priceB;
    }

    public BigDecimal getPriceC() {
        return priceC;
    }

    public void setPriceC(BigDecimal priceC) {
        this.priceC = priceC;
    }

    public BigDecimal getPriceD() {
        return priceD;
    }

    public void setPriceD(BigDecimal priceD) {
        this.priceD = priceD;
    }

    public BigDecimal getPriceA() {
        return priceA;
    }

    public void setPriceA(BigDecimal priceA) {
        this.priceA = priceA;
    }

    public void setIsValid(String isValid) {
        this.isValid = isValid;
    }

    public String getIsValid() {
        return isValid;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

}
