package com.rich.common.core.domain.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

/**
 * 文件出单方式对象 bas_doc_issue_type
 * 
 * <AUTHOR>
 * @date 2023-06-16
 */
public class BasDocIssueType extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 出单方式 */
    private Long issueTypeId;

    /** 简称 */
    @Excel(name = "简称")
    private String issueTypeShortName;

    /** 中文名 */
    @Excel(name = "中文名")
    private String issueTypeLocalName;

    /** 英文名 */
    @Excel(name = "英文名")
    private String issueTypeEnName;

    /** 排序 */
    @Excel(name = "排序")
    private Integer orderNum;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 删除人 */
    private String deleteBy;

    /** 删除时间 */
    private Date deleteTime;

    /** 数据状态（-1:删除或不可用，0：正常） */
    private String deleteStatus;

    public void setIssueTypeId(Long issueTypeId) 
    {
        this.issueTypeId = issueTypeId;
    }

    public Long getIssueTypeId() 
    {
        return issueTypeId;
    }
    public void setIssueTypeShortName(String issueTypeShortName) 
    {
        this.issueTypeShortName = issueTypeShortName;
    }

    public String getIssueTypeShortName() 
    {
        return issueTypeShortName;
    }
    public void setIssueTypeLocalName(String issueTypeLocalName) 
    {
        this.issueTypeLocalName = issueTypeLocalName;
    }

    public String getIssueTypeLocalName() 
    {
        return issueTypeLocalName;
    }
    public void setIssueTypeEnName(String issueTypeEnName) 
    {
        this.issueTypeEnName = issueTypeEnName;
    }

    public String getIssueTypeEnName() 
    {
        return issueTypeEnName;
    }
    public void setOrderNum(Integer orderNum) 
    {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() 
    {
        return orderNum;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
}
