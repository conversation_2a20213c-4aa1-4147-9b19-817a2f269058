package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/12/24 11:15
 * @Version 1.0
 */
public class RsReimburseDTO {
    @Excel(name = "录入日期")
    private Date applyDate;
    @Excel(name = "报单日期")
    private Date happenDate;
    @Excel(name = "所属部门")
    private String deptName;
    @Excel(name = "报销人")
    private String staffName;
    @Excel(name = "费用类目")
    private String chargeTypeName;
    @Excel(name = "费用说明")
    private String reimburseContent;
    @Excel(name = "报销金额")
    private BigDecimal reimbursePrice;
    @Excel(name = "备注")
    private String remark;

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public Date getHappenDate() {
        return happenDate;
    }

    public void setHappenDate(Date happenDate) {
        this.happenDate = happenDate;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public String getChargeTypeName() {
        return chargeTypeName;
    }

    public void setChargeTypeName(String chargeTypeName) {
        this.chargeTypeName = chargeTypeName;
    }

    public String getReimburseContent() {
        return reimburseContent;
    }

    public void setReimburseContent(String reimburseContent) {
        this.reimburseContent = reimburseContent;
    }

    public BigDecimal getReimbursePrice() {
        return reimbursePrice;
    }

    public void setReimbursePrice(BigDecimal reimbursePrice) {
        this.reimbursePrice = reimbursePrice;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
