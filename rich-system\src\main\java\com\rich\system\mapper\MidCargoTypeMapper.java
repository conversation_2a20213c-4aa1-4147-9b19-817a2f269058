package com.rich.system.mapper;

import com.rich.system.domain.MidCargoType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 货物特征Mapper接口
 *
 * <AUTHOR>
 * @date 2022-09-07
 */
@Mapper
public interface MidCargoTypeMapper {
    /**
     * 查询货物特征
     *
     * @param belongId 货物特征主键
     * @param belongTo 从属
     * @return 货物特征
     */
    List<Long> selectMidCargoTypeById(Long belongId, String belongTo);

    /**
     * 查询货物特征列表
     *
     * @param MidCargoType 货物特征
     * @return 货物特征集合
     */
    List<MidCargoType> selectMidCargoTypeList(MidCargoType MidCargoType);

    /**
     * 新增货物特征
     *
     * @param MidCargoType 货物特征
     * @return 结果
     */
    int insertMidCargoType(MidCargoType MidCargoType);

    /**
     * 删除货物特征
     *
     * @param belongId 货物特征主键
     * @param belongTo 从属
     * @return 结果
     */
    int deleteMidCargoTypeById(Long belongId, String belongTo);

    /**
     * 批量删除货物特征
     *
     * @param belongIds 需要删除的数据主键集合
     * @param belongTo  从属
     * @return 结果
     */
    int deleteMidCargoTypeByIds(Long[] belongIds, String belongTo);

    int batchCargoType(List<MidCargoType> list);
}
