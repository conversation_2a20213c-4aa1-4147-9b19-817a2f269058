package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

/**
 * 邮件规则对象 bas_mail_rules
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
public class BasMailRules extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 邮件规则
     */
    private Long mailRulesId;

    /**
     * 简称
     */
    @Excel(name = "简称")
    private String mailRulesShortName;

    /**
     * 中文名
     */
    @Excel(name = "中文名")
    private String mailRulesLocalName;

    /**
     * 英文名
     */
    @Excel(name = "英文名")
    private String mailRulesEnName;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Integer orderNum;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;

    private String code;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setMailRulesId(Long mailRulesId) {
        this.mailRulesId = mailRulesId;
    }

    public Long getMailRulesId() {
        return mailRulesId;
    }

    public void setMailRulesShortName(String mailRulesShortName) {
        this.mailRulesShortName = mailRulesShortName;
    }

    public String getMailRulesShortName() {
        return mailRulesShortName;
    }

    public void setMailRulesLocalName(String mailRulesLocalName) {
        this.mailRulesLocalName = mailRulesLocalName;
    }

    public String getMailRulesLocalName() {
        return mailRulesLocalName;
    }

    public void setMailRulesEnName(String mailRulesEnName) {
        this.mailRulesEnName = mailRulesEnName;
    }

    public String getMailRulesEnName() {
        return mailRulesEnName;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
}
