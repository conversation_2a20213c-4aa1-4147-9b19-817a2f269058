package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpImportCustomsClearance;
import com.rich.common.core.domain.entity.RsOpImportDispatchTruck;
import org.apache.ibatis.annotations.Mapper;

/**
 * 进口派送服务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Mapper
public interface RsOpImportDispatchTruckMapper {
    /**
     * 查询进口派送服务
     *
     * @param importDispatchTruckId 进口派送服务主键
     * @return 进口派送服务
     */
    RsOpImportDispatchTruck selectRsOpImportDispatchTruckByImportDispatchTruckId(Long importDispatchTruckId);

    /**
     * 查询进口派送服务列表
     *
     * @param rsOpImportDispatchTruck 进口派送服务
     * @return 进口派送服务集合
     */
    List<RsOpImportDispatchTruck> selectRsOpImportDispatchTruckList(RsOpImportDispatchTruck rsOpImportDispatchTruck);

    /**
     * 新增进口派送服务
     *
     * @param rsOpImportDispatchTruck 进口派送服务
     * @return 结果
     */
    int insertRsOpImportDispatchTruck(RsOpImportDispatchTruck rsOpImportDispatchTruck);

    /**
     * 修改进口派送服务
     *
     * @param rsOpImportDispatchTruck 进口派送服务
     * @return 结果
     */
    int updateRsOpImportDispatchTruck(RsOpImportDispatchTruck rsOpImportDispatchTruck);

    /**
     * 删除进口派送服务
     *
     * @param importDispatchTruckId 进口派送服务主键
     * @return 结果
     */
    int deleteRsOpImportDispatchTruckByImportDispatchTruckId(Long importDispatchTruckId);

    /**
     * 批量删除进口派送服务
     *
     * @param importDispatchTruckIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsOpImportDispatchTruckByImportDispatchTruckIds(Long[] importDispatchTruckIds);

    RsOpImportDispatchTruck selectRsOpImportDispatchTruckByRctId(Long rctId);
}
