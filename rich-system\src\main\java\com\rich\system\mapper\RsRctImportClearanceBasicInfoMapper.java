package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsRctImportClearanceBasicInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 操作单进口清关基础信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-15
 */
@Mapper
public interface RsRctImportClearanceBasicInfoMapper {
    /**
     * 查询操作单进口清关基础信息
     *
     * @param importClearanceId 操作单进口清关基础信息主键
     * @return 操作单进口清关基础信息
     */
    RsRctImportClearanceBasicInfo selectRsRctImportClearanceBasicInfoByRctId(Long rctId);

    /**
     * 查询操作单进口清关基础信息列表
     *
     * @param rsRctImportClearanceBasicInfo 操作单进口清关基础信息
     * @return 操作单进口清关基础信息集合
     */
    List<RsRctImportClearanceBasicInfo> selectRsRctImportClearanceBasicInfoList(RsRctImportClearanceBasicInfo rsRctImportClearanceBasicInfo);

    /**
     * 新增操作单进口清关基础信息
     *
     * @param rsRctImportClearanceBasicInfo 操作单进口清关基础信息
     * @return 结果
     */
    int insertRsRctImportClearanceBasicInfo(RsRctImportClearanceBasicInfo rsRctImportClearanceBasicInfo);

    /**
     * 修改操作单进口清关基础信息
     *
     * @param rsRctImportClearanceBasicInfo 操作单进口清关基础信息
     * @return 结果
     */
    int updateRsRctImportClearanceBasicInfo(RsRctImportClearanceBasicInfo rsRctImportClearanceBasicInfo);

    /**
     * 删除操作单进口清关基础信息
     *
     * @return 结果
     */
    int deleteRsRctImportClearanceBasicInfoById(Long rctId);

    /**
     * 批量删除操作单进口清关基础信息
     *
     * @return 结果
     */
    int deleteRsRctImportClearanceBasicInfoByIds(Long[] rctIds);
}
