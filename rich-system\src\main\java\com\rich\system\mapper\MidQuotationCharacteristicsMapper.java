package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsCharacteristics;
import com.rich.system.domain.MidQuotationCharacteristics;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-05-26
 */
@Mapper
public interface MidQuotationCharacteristicsMapper {
    /**
     * 查询【请填写功能名称】
     *
     * @param quotationId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    List<Long> selectMidQuotationCharacteristicsByQuotationId(Long quotationId);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param midQuotationCharacteristics 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    List<MidQuotationCharacteristics> selectMidQuotationCharacteristicsList(MidQuotationCharacteristics midQuotationCharacteristics);

    /**
     * 新增【请填写功能名称】
     *
     * @param midQuotationCharacteristics 【请填写功能名称】
     * @return 结果
     */
    int insertMidQuotationCharacteristics(MidQuotationCharacteristics midQuotationCharacteristics);

    /**
     * 修改【请填写功能名称】
     *
     * @param midQuotationCharacteristics 【请填写功能名称】
     * @return 结果
     */
    int updateMidQuotationCharacteristics(MidQuotationCharacteristics midQuotationCharacteristics);

    /**
     * 删除【请填写功能名称】
     *
     * @param quotationId 【请填写功能名称】主键
     * @return 结果
     */
    int deleteMidQuotationCharacteristicsByQuotationId(Long quotationId);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param quotationIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteMidQuotationCharacteristicsByQuotationIds(Long[] quotationIds);

    int BatchQuotationCharacteristics(List<MidQuotationCharacteristics> characteristicsList);
}
