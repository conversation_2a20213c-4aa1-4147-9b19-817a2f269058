package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasContractType;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasContractTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 合约类别Controller
 *
 * <AUTHOR>
 * @date 2023-01-13
 */
@RestController
@RequestMapping("/system/contracttype")
public class BasContractTypeController extends BaseController {

    @Autowired
    private  BasContractTypeService basContractTypeService;

    @Autowired
    private  RedisCache redisCache;

    /**
     * 查询合约类别列表
     */
    @PreAuthorize("@ss.hasPermi('system:contracttype:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasContractType basContractType) {
        startPage();
        List<BasContractType> list = basContractTypeService.selectBasContractTypeList(basContractType);
        return getDataTable(list);
    }

    @GetMapping("/selectList")
    public AjaxResult selectList(BasContractType basContractType) {
        List<BasContractType> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "contractType");
        if (list == null) {
            basContractType.setStatus("0");
            list = basContractTypeService.selectBasContractTypeList(basContractType);
            redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "contractType");
            redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "contractType", list);
        }
        return AjaxResult.success(AjaxResult.DATA_TAG, list);
    }

    /**
     * 导出合约类别列表
     */
    @PreAuthorize("@ss.hasPermi('system:contracttype:export')")
    @Log(title = "合约类别", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasContractType basContractType) {
        List<BasContractType> list = basContractTypeService.selectBasContractTypeList(basContractType);
        ExcelUtil<BasContractType> util = new ExcelUtil<BasContractType>(BasContractType.class);
        util.exportExcel(response, list, "合约类别数据");
    }

    /**
     * 获取合约类别详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:contracttype:edit')")
    @GetMapping(value = "/{contractTypeId}")
    public AjaxResult getInfo(@PathVariable("contractTypeId") Long contractTypeId) {
        return AjaxResult.success(basContractTypeService.selectBasContractTypeByContractTypeId(contractTypeId));
    }

    /**
     * 新增合约类别
     */
    @PreAuthorize("@ss.hasPermi('system:contracttype:add')")
    @Log(title = "合约类别", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasContractType basContractType) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "contractType");
        return toAjax(basContractTypeService.insertBasContractType(basContractType));
    }

    /**
     * 修改合约类别
     */
    @PreAuthorize("@ss.hasPermi('system:contracttype:edit')")
    @Log(title = "合约类别", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasContractType basContractType) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "contractType");
        return toAjax(basContractTypeService.updateBasContractType(basContractType));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:contracttype:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasContractType basContractType) {
        basContractType.setUpdateBy(getUserId());
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "contractType");
        return toAjax(basContractTypeService.changeStatus(basContractType));
    }

    /**
     * 删除合约类别
     */
    @PreAuthorize("@ss.hasPermi('system:contracttype:remove')")
    @Log(title = "合约类别", businessType = BusinessType.DELETE)
    @DeleteMapping("/{contractTypeIds}")
    public AjaxResult remove(@PathVariable Long[] contractTypeIds) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "contractType");
        return toAjax(basContractTypeService.deleteBasContractTypeByContractTypeIds(contractTypeIds));
    }
}
