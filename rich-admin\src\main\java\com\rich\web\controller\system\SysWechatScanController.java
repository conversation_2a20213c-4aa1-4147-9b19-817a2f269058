package com.rich.web.controller.system;

import com.rich.common.constant.CacheConstants;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.RsStaff;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.ServletUtils;
import com.rich.common.utils.StringUtils;
import com.rich.common.utils.uuid.IdUtils;
import com.rich.system.service.RsStaffService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 微信扫码登录
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/wechat")
public class SysWechatScanController {
    private static final Logger log = LoggerFactory.getLogger(SysWechatScanController.class);

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private RsStaffService rsStaffService;

    @Value("${wechat.app-id}")
    private String appId;

    @Value("${wechat.app-secret}")
    private String appSecret;

    @Value("${wechat.redirect-url}")
    private String redirectUrl;

    /**
     * 检查用户是否需要进行微信扫码验证
     *
     * @param username 用户名
     * @return 结果
     */
    @GetMapping("/check/{username}")
    public AjaxResult checkNeedScan(@PathVariable String username) {
        if (StringUtils.isEmpty(username)) {
            return AjaxResult.error("用户名不能为空");
        }

        // 获取用户信息
        RsStaff user = rsStaffService.selectUserByUserName(username);
        if (user == null) {
            return AjaxResult.error("用户不存在");
        }

        // 检查当日是否首次登录
        String today = DateUtils.getDate();
        String key = CacheConstants.USER_FIRST_LOGIN_KEY + username + ":" + today;
        Boolean isFirstLogin = !redisCache.hasKey(key);

        // 检查用户是否已绑定微信
        boolean isWechatBound = StringUtils.isNotEmpty(user.getOpenid());

        Map<String, Object> result = new HashMap<>();
        result.put("needScan", isFirstLogin);
        result.put("isWechatBound", isWechatBound);

        return AjaxResult.success(result);
    }

    /**
     * 获取微信扫码登录二维码
     *
     * @return 结果
     */
    @PostMapping("/getQrCode")
    public AjaxResult getQrCode(@RequestBody Map<String, String> params) {
        String username = params.get("username");
        if (StringUtils.isEmpty(username)) {
            return AjaxResult.error("用户名不能为空");
        }

        try {
            // 获取用户信息
            RsStaff user = rsStaffService.selectUserByUserName(username);
            if (user == null) {
                return AjaxResult.error("用户不存在");
            }

            // 生成唯一的扫码标识
            String scanId = IdUtils.fastUUID();

            // 存储到Redis中，设置5分钟过期
            String key = CacheConstants.WECHAT_SCAN_LOGIN_KEY + scanId;
            Map<String, Object> scanInfo = new HashMap<>();
            scanInfo.put("username", username);
            scanInfo.put("staffId", user.getStaffId());
            scanInfo.put("isWechatBound", StringUtils.isNotEmpty(user.getOpenid()));
            scanInfo.put("status", "WAITING"); // 状态：WAITING-等待扫码，SCANNED-已扫码，CONFIRMED-已确认
            redisCache.setCacheObject(key, scanInfo, 5, TimeUnit.MINUTES);

            // 使用微信开放平台生成二维码
            // 构建授权URL，用户扫码后会跳转到redirectUrl，并附带code和state参数
            /*String authUrl = String.format(
                "https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&scope=snsapi_login&state=%s#wechat_redirect",
                appId, redirectUrl, scanId
            );*/
            String authUrl = String.format(
                    "https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&scope=snsapi_login&state=%s#wechat_redirect",
                    appId, ServletUtils.urlEncode(redirectUrl), scanId
            );

            log.info("生成微信扫码登录二维码，扫码ID: {}, 授权URL: {}", scanId, authUrl);

            Map<String, Object> result = new HashMap<>();
            result.put("scanId", scanId);
            result.put("qrCodeUrl", authUrl);

            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("生成微信扫码二维码失败", e);
            return AjaxResult.error("生成微信扫码二维码失败");
        }
    }

    /**
     * 检查扫码状态
     *
     * @param scanId 扫码ID
     * @return 结果
     */
    @GetMapping("/checkStatus/{scanId}")
    public AjaxResult checkStatus(@PathVariable String scanId) {
        if (StringUtils.isEmpty(scanId)) {
            return AjaxResult.error("扫码ID不能为空");
        }

        String key = CacheConstants.WECHAT_SCAN_LOGIN_KEY + scanId;
        Map<String, Object> scanInfo = redisCache.getCacheObject(key);

        if (scanInfo == null) {
            return AjaxResult.error("二维码已过期");
        }

        return AjaxResult.success(new HashMap<String, Object>() {{
            put("status", scanInfo.get("status"));
        }});
    }

    /**
     * 微信扫码回调接口
     *
     * @param code  授权码
     * @param state 状态(扫码ID)
     * @return 结果
     */
    @GetMapping("/callback")
    public AjaxResult callback(@RequestParam String code, @RequestParam String state) {
        log.info("接收到微信扫码回调，code: {}, state: {}", code, state);

        String scanId = state;
        String key = CacheConstants.WECHAT_SCAN_LOGIN_KEY + scanId;
        Map<String, Object> scanInfo = redisCache.getCacheObject(key);

        if (scanInfo == null) {
            log.error("扫码信息已过期，scanId: {}", scanId);
            return AjaxResult.error("二维码已过期，请重新扫码");
        }

        try {
            // 使用授权码获取访问令牌
            String tokenUrl = String.format(
                    "https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code",
                    appId, appSecret, code
            );

            // 使用更健壮的方式处理微信API的响应
            try {
                ResponseEntity<Map> tokenResponse = restTemplate.exchange(
                        tokenUrl, HttpMethod.GET, null, Map.class);
                
                Map<String, Object> tokenInfo = tokenResponse.getBody();
                if (tokenInfo != null && tokenInfo.containsKey("access_token")) {
                    String accessToken = (String) tokenInfo.get("access_token");
                    String openId = (String) tokenInfo.get("openid");
                    String unionId = (String) tokenInfo.getOrDefault("unionid", "");

                    // 获取用户信息
                    String userInfoUrl = String.format(
                            "https://api.weixin.qq.com/sns/userinfo?access_token=%s&openid=%s&lang=zh_CN",
                            accessToken, openId
                    );

                    ResponseEntity<Map> userInfoResponse = restTemplate.exchange(
                            userInfoUrl, HttpMethod.GET, null, Map.class);
                    
                    Map<String, Object> userInfo = userInfoResponse.getBody();
                    if (userInfo != null && userInfo.containsKey("nickname")) {
                        String wxNickName = (String) userInfo.get("nickname");

                        // 获取本地用户信息
                        String username = (String) scanInfo.get("username");
                        Long staffId = Long.valueOf(scanInfo.get("staffId").toString());
                        boolean isWechatBound = (boolean) scanInfo.get("isWechatBound");

                        // 检查是否已有其他用户绑定了该微信
                        RsStaff existUser = rsStaffService.selectUserByOpenid(openId);
                        if (existUser != null && !existUser.getStaffId().equals(staffId)) {
                            log.warn("该微信已绑定其他账号，微信昵称: {}，已绑定用户: {}", wxNickName, existUser.getStaffUsername());
                            return AjaxResult.error("该微信已绑定其他账号，请联系管理员解绑后再试");
                        }

                        // 如果未绑定微信，则进行绑定
                        if (!isWechatBound) {
                            int result = rsStaffService.bindWechatUser(staffId, openId, unionId, wxNickName);
                            if (result <= 0) {
                                log.error("绑定微信用户失败，用户ID: {}, 微信昵称: {}", staffId, wxNickName);
                                return AjaxResult.error("绑定失败，请联系管理员");
                            }
                            log.info("绑定微信用户成功，用户: {}, 微信昵称: {}", username, wxNickName);
                        } else if (existUser == null) {
                            // 如果已绑定但在数据库中找不到记录，可能是数据不一致，重新绑定
                            rsStaffService.bindWechatUser(staffId, openId, unionId, wxNickName);
                        }

                        // 更新扫码状态
                        scanInfo.put("status", "CONFIRMED");
                        scanInfo.put("openId", openId);
                        scanInfo.put("nickname", wxNickName);
                        redisCache.setCacheObject(key, scanInfo, 5, TimeUnit.MINUTES);

                        // 设置用户当天已登录标记
                        String today = DateUtils.getDate();
                        String firstLoginKey = CacheConstants.USER_FIRST_LOGIN_KEY + username + ":" + today;
                        redisCache.setCacheObject(firstLoginKey, true, 24, TimeUnit.HOURS);

                        log.info("微信扫码验证成功，用户: {}, 微信昵称: {}", username, wxNickName);

                        return AjaxResult.success("验证成功");
                    }
                } else if (tokenInfo != null && tokenInfo.containsKey("errcode")) {
                    // 处理微信API返回的错误
                    String errcode = tokenInfo.get("errcode").toString();
                    String errmsg = tokenInfo.getOrDefault("errmsg", "未知错误").toString();
                    log.error("微信API返回错误，errcode: {}, errmsg: {}", errcode, errmsg);
                    return AjaxResult.error("验证失败: " + errmsg);
                }
            } catch (Exception e) {
                log.error("调用微信API失败", e);
                // 不向上抛出异常，继续执行
            }

            log.error("获取微信用户信息失败");
            return AjaxResult.error("验证失败，请重新扫码");
        } catch (Exception e) {
            log.error("微信扫码验证处理失败", e);
            return AjaxResult.error("验证失败，请重新扫码");
        }
    }
} 