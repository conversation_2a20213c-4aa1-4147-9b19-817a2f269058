package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.RsMessage;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.RsMessageService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 消息通知Controller
 *
 * <AUTHOR>
 * @date 2023-02-15
 */
@RestController
@RequestMapping("/system/message")
public class RsMessageController extends BaseController {
    @Autowired
    private  RsMessageService rsMessageService;

    /**
     * 查询消息通知列表
     */
    @GetMapping("/list")
    public TableDataInfo list(RsMessage rsMessage) {
        startPage();
        List<RsMessage> list = rsMessageService.selectRsMessageList(rsMessage);
        return getDataTable(list);
    }

    @GetMapping("/newMessage")
    public AjaxResult newMessage(RsMessage rsMessage) {
        return AjaxResult.success(rsMessageService.countNewMessage(rsMessage));
    }

    /**
     * 导出消息通知列表
     */
    @PreAuthorize("@ss.hasPermi('system:message:export')")
    @Log(title = "消息通知", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsMessage rsMessage) {
        List<RsMessage> list = rsMessageService.selectRsMessageList(rsMessage);
        ExcelUtil<RsMessage> util = new ExcelUtil<RsMessage>(RsMessage.class);
        util.exportExcel(response, list, "消息通知数据");
    }

    /**
     * 获取消息通知详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:message:edit')")
    @GetMapping(value = "/{messageId}")
    public AjaxResult getInfo(@PathVariable("messageId") Long messageId) {
        return AjaxResult.success(rsMessageService.selectRsMessageByMessageId(messageId));
    }

    /**
     * 新增消息通知
     */
    @PreAuthorize("@ss.hasPermi('system:message:add')")
    @Log(title = "消息通知", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsMessage rsMessage) {
        return toAjax(rsMessageService.insertRsMessage(rsMessage));
    }

    /**
     * 修改消息通知
     */
    @PreAuthorize("@ss.hasPermi('system:message:edit')")
    @Log(title = "消息通知", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsMessage rsMessage) {
        return toAjax(rsMessageService.updateRsMessage(rsMessage));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:message:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsMessage rsMessage) {
        rsMessage.setUpdateBy(getUserId());
        return toAjax(rsMessageService.changeStatus(rsMessage));
    }

    /**
     * 删除消息通知
     */
    @PreAuthorize("@ss.hasPermi('system:message:remove')")
    @Log(title = "消息通知", businessType = BusinessType.DELETE)
    @DeleteMapping("/{messageIds}")
    public AjaxResult remove(@PathVariable Long[] messageIds) {
        return toAjax(rsMessageService.deleteRsMessageByMessageIds(messageIds));
    }
}
