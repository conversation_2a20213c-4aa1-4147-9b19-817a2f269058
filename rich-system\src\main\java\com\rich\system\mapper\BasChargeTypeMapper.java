package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasChargeType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 费用类型Mapper接口
 *
 * <AUTHOR>
 * @date 2022-08-30
 */
@Mapper
public interface BasChargeTypeMapper {
    /**
     * 查询费用类型
     *
     * @param chargeTypeId 费用类型主键
     * @return 费用类型
     */
    BasChargeType selectBasChargeTypeByChargeTypeId(Long chargeTypeId);

    /**
     * 查询费用类型列表
     *
     * @param basChargeType 费用类型
     * @return 费用类型集合
     */
    List<BasChargeType> selectBasChargeTypeList(BasChargeType basChargeType);

    List<BasChargeType> selectBasChargeList();

    /**
     * 新增费用类型
     *
     * @param basChargeType 费用类型
     * @return 结果
     */
    int insertBasChargeType(BasChargeType basChargeType);

    /**
     * 修改费用类型
     *
     * @param basChargeType 费用类型
     * @return 结果
     */
    int updateBasChargeType(BasChargeType basChargeType);

    /**
     * 删除费用类型
     *
     * @param chargeTypeId 费用类型主键
     * @return 结果
     */
    int deleteBasChargeTypeByChargeTypeId(Long chargeTypeId);

    /**
     * 批量删除费用类型
     *
     * @param chargeTypeIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasChargeTypeByChargeTypeIds(Long[] chargeTypeIds);

}
