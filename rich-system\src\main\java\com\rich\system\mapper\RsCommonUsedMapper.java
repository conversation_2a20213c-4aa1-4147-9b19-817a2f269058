package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsCommonUsed;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 常用信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-07-31
 */
@Mapper
public interface RsCommonUsedMapper 
{
    /**
     * 查询常用信息
     * 
     * @param commonUsedId 常用信息主键
     * @return 常用信息
     */
        RsCommonUsed selectRsCommonUsedByCommonUsedId(Long commonUsedId);

    /**
     * 查询常用信息列表
     * 
     * @param rsCommonUsed 常用信息
     * @return 常用信息集合
     */
    List<RsCommonUsed> selectRsCommonUsedList(RsCommonUsed rsCommonUsed);

    /**
     * 新增常用信息
     * 
     * @param rsCommonUsed 常用信息
     * @return 结果
     */
    int insertRsCommonUsed(RsCommonUsed rsCommonUsed);

    /**
     * 修改常用信息
     * 
     * @param rsCommonUsed 常用信息
     * @return 结果
     */
    int updateRsCommonUsed(RsCommonUsed rsCommonUsed);

    /**
     * 删除常用信息
     * 
     * @param commonUsedId 常用信息主键
     * @return 结果
     */
    int deleteRsCommonUsedByCommonUsedId(Long commonUsedId);

    /**
     * 批量删除常用信息
     * 
     * @param commonUsedIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsCommonUsedByCommonUsedIds(Long[] commonUsedIds);
}
