package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.MidChargeBankWriteoff;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.MidChargeBankWriteoffService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 记录银行销账的明细中间Controller
 *
 * <AUTHOR>
 * @date 2024-05-06
 */
@RestController
@RequestMapping("/system/chargebankwriteoff")
public class MidChargeBankWriteoffController extends BaseController {
    @Autowired
    private MidChargeBankWriteoffService midChargeBankWriteoffService;

    /**
     * 查询记录银行销账的明细中间列表
     */
    @PreAuthorize("@ss.hasPermi('system:chargebankwriteoff:list')")
    @GetMapping("/list")
    public TableDataInfo list(MidChargeBankWriteoff midChargeBankWriteoff) {
        startPage();
        List<MidChargeBankWriteoff> list = midChargeBankWriteoffService.selectMidChargeBankWriteoffList(midChargeBankWriteoff);
        return getDataTable(list);
    }

    /**
     * 导出记录银行销账的明细中间列表
     */
    @PreAuthorize("@ss.hasPermi('system:chargebankwriteoff:export')")
    @Log(title = "记录银行销账的明细中间", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MidChargeBankWriteoff midChargeBankWriteoff) {
        List<MidChargeBankWriteoff> list = midChargeBankWriteoffService.selectMidChargeBankWriteoffList(midChargeBankWriteoff);
        ExcelUtil<MidChargeBankWriteoff> util = new ExcelUtil<MidChargeBankWriteoff>(MidChargeBankWriteoff.class);
        util.exportExcel(response, list, "记录银行销账的明细中间数据");
    }

    /**
     * 获取记录银行销账的明细中间详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:chargebankwriteoff:query')")
    @GetMapping(value = "/{midChargeBankId}")
    public AjaxResult getInfo(@PathVariable("midChargeBankId") Long midChargeBankId) {
        return AjaxResult.success(midChargeBankWriteoffService.selectMidChargeBankWriteoffByMidChargeBankId(midChargeBankId));
    }

    /**
     * 新增记录银行销账的明细中间
     */
    @PreAuthorize("@ss.hasPermi('system:chargebankwriteoff:add')")
    @Log(title = "记录银行销账的明细中间", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MidChargeBankWriteoff midChargeBankWriteoff) {
        return toAjax(midChargeBankWriteoffService.insertMidChargeBankWriteoff(midChargeBankWriteoff));
    }

    /**
     * 修改记录银行销账的明细中间
     */
    @PreAuthorize("@ss.hasPermi('system:chargebankwriteoff:edit')")
    @Log(title = "记录银行销账的明细中间", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MidChargeBankWriteoff midChargeBankWriteoff) {
        return toAjax(midChargeBankWriteoffService.updateMidChargeBankWriteoff(midChargeBankWriteoff));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:chargebankwriteoff:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody MidChargeBankWriteoff midChargeBankWriteoff) {
        midChargeBankWriteoff.setUpdateBy(getUserId());
        return toAjax(midChargeBankWriteoffService.changeStatus(midChargeBankWriteoff));
    }

    /**
     * 删除记录银行销账的明细中间
     */
    @PreAuthorize("@ss.hasPermi('system:chargebankwriteoff:remove')")
    @Log(title = "记录银行销账的明细中间", businessType = BusinessType.DELETE)
    @DeleteMapping("/{midChargeBankIds}")
    public AjaxResult remove(@PathVariable Long[] midChargeBankIds) {
        return toAjax(midChargeBankWriteoffService.deleteMidChargeBankWriteoffByMidChargeBankIds(midChargeBankIds));
    }
}
