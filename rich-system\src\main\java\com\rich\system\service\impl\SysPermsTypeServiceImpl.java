package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.SysPermsType;
import com.rich.system.mapper.SysPermsTypeMapper;
import com.rich.system.service.SysPermsTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 权限类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-21
 */
@Service
public class SysPermsTypeServiceImpl implements SysPermsTypeService {
    @Autowired
    private  SysPermsTypeMapper sysPermsTypeMapper;

    /**
     * 查询权限类型
     *
     * @param permsId 权限类型主键
     * @return 权限类型
     */
    @Override
    public SysPermsType selectSysPermsTypeByPermsId(Long permsId) {
        return sysPermsTypeMapper.selectSysPermsTypeByPermsId(permsId);
    }

    /**
     * 查询权限类型列表
     *
     * @param sysPermsType 权限类型
     * @return 权限类型
     */
    @Override
    public List<SysPermsType> selectSysPermsTypeList(SysPermsType sysPermsType) {
        return sysPermsTypeMapper.selectSysPermsTypeList(sysPermsType);
    }

    /**
     * 新增权限类型
     *
     * @param sysPermsType 权限类型
     * @return 结果
     */
    @Override
    public int insertSysPermsType(SysPermsType sysPermsType) {
        return sysPermsTypeMapper.insertSysPermsType(sysPermsType);
    }

    /**
     * 修改权限类型
     *
     * @param sysPermsType 权限类型
     * @return 结果
     */
    @Override
    public int updateSysPermsType(SysPermsType sysPermsType) {
        return sysPermsTypeMapper.updateSysPermsType(sysPermsType);
    }

    /**
     * 修改权限类型状态
     *
     * @param sysPermsType 权限类型
     * @return 权限类型
     */
    @Override
    public int changeStatus(SysPermsType sysPermsType) {
        return sysPermsTypeMapper.updateSysPermsType(sysPermsType);
    }

    /**
     * 批量删除权限类型
     *
     * @param permsIds 需要删除的权限类型主键
     * @return 结果
     */
    @Override
    public int deleteSysPermsTypeByPermsIds(Long[] permsIds) {
        return sysPermsTypeMapper.deleteSysPermsTypeByPermsIds(permsIds);
    }

    /**
     * 删除权限类型信息
     *
     * @param permsId 权限类型主键
     * @return 结果
     */
    @Override
    public int deleteSysPermsTypeByPermsId(Long permsId) {
        return sysPermsTypeMapper.deleteSysPermsTypeByPermsId(permsId);
    }
}
