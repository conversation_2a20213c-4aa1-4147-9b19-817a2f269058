package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpBulkTruck;
import com.rich.common.core.domain.entity.RsOpCtnrTruck;
import com.rich.common.core.domain.entity.RsOpTruck;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 出口拖车服务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Mapper
public interface RsOpTruckMapper {
    /**
     * 查询出口拖车服务
     *
     * @param exportTruckId 出口拖车服务主键
     * @return 出口拖车服务
     */
    RsOpTruck selectRsOpExportTruckByExportTruckId(Long exportTruckId);

    /**
     * 查询出口拖车服务列表
     *
     * @param rsOpExportTruck 出口拖车服务
     * @return 出口拖车服务集合
     */
    List<RsOpTruck> selectRsOpExportTruckList(RsOpTruck rsOpExportTruck);

    /**
     * 新增出口拖车服务
     *
     * @param rsOpExportTruck 出口拖车服务
     * @return 结果
     */
    int insertRsOpTruck(RsOpTruck rsOpExportTruck);

    int insertRsOpTruck(RsOpCtnrTruck rsOpCtnrTruck);

    int insertRsOpTruck(RsOpBulkTruck rsOpBulkTruck);

    /**
     * 修改出口拖车服务
     *
     * @param rsOpExportTruck 出口拖车服务
     * @return 结果
     */
    int updateRsOpExportTruck(RsOpTruck rsOpExportTruck);

    int updateRsOpExportTruck(RsOpCtnrTruck rsOpCtnrTruck);

    int updateRsOpExportTruck(RsOpBulkTruck rsOpBulkTruck);

    /**
     * 删除出口拖车服务
     *
     * @param exportTruckId 出口拖车服务主键
     * @return 结果
     */
    int deleteRsOpExportTruckByExportTruckId(Long exportTruckId);

    /**
     * 批量删除出口拖车服务
     *
     * @param exportTruckIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsOpExportTruckByExportTruckIds(Long[] exportTruckIds);


    List<RsOpCtnrTruck> selectRsOpCtnrTruckByRctId(@Param("rctId") Long rctId, @Param("sqdServiceTypeId") Long sqdServiceTypeId);

    List<RsOpBulkTruck> selectRsOpBulkTruckByRctId(@Param("rctId") Long rctId, @Param("sqdServiceTypeId") Long sqdServiceTypeId);

    int batchInsertRsOpCtnrTruck(List<RsOpCtnrTruck> rsOpCtnrTruckList);

    int batchInsertRsOpBulkTruck(List<RsOpBulkTruck> rsOpBulkTruckList);

    void deleteRsOpTruckByServiceId(Long serviceId);

    void deleteRsOpBulkTruckByRctIdAndServiceTypeId(@Param("rctId") Long rctId, @Param("sqdServiceTypeId") Long sqdServiceTypeId);

    void insertRsOpCtnrTruck(RsOpCtnrTruck rsOpCtnrTruck);

    void deleteRsOpCtnrTruckByRctIdAndServiceTypeId(@Param("rctId") Long rctId, @Param("sqdServiceTypeId") Long sqdServiceTypeId);

    void insertRsOpBulkTruck(RsOpBulkTruck rsOpBulkTruck);

    void upsertRsOpBulkTruck(RsOpBulkTruck rsOpBulkTruck);

    void upsertRsOpCtnrTruck(RsOpCtnrTruck rsOpCtnrTruck);

    void deleteRsOpTruckByTruckId(Long truckId);
}
