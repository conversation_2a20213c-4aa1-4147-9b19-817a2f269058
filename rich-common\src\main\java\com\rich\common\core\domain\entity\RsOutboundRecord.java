package com.rich.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 出仓记录对象 rs_outbound_record
 *
 * <AUTHOR>
 * @date 2024-11-11
 */
public class RsOutboundRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long outboundRecordId;

    /**
     * 出仓单号
     */
    @Excel(name = "出仓单号")
    private String outboundNo;

    /**
     * 客户代码
     */
    @Excel(name = "客户代码")
    private String clientCode;

    /**
     * 客户名称
     */
    @Excel(name = "客户名称")
    private String clientName;

    /**
     * 操作员
     */
    @Excel(name = "操作员")
    private Long operatorId;

    /**
     * 柜型
     */
    @Excel(name = "柜型")
    private String containerType;

    /**
     * 柜号
     */
    @Excel(name = "柜号")
    private String containerNo;

    /**
     * 封号
     */
    @Excel(name = "封号")
    private String sealNo;

    /**
     * 出仓日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出仓日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date outboundDate;

    /**
     * 仓库报价
     */
    @Excel(name = "仓库报价", scale = 2)
    private BigDecimal warehouseQuote;

    /**
     * 工人装柜费
     */
    @Excel(name = "工人装柜费", scale = 2)
    private BigDecimal workerLoadingFee;

    /**
     * 仓管代收
     */
    @Excel(name = "仓管代收", scale = 2)
    private BigDecimal warehouseCollection;

    @Excel(name = "补收入仓费", scale = 2)
    private BigDecimal additionalStorageFee;

    /**
     * 已收入仓费
     */
    @Excel(name = "已收入仓费", scale = 2)
    private BigDecimal receivedStorageFee;

    /**
     * 未收卸货费
     */
    @Excel(name = "未收卸货费", scale = 2)
    private BigDecimal unpaidUnloadingFee;

    @Excel(name = "实付卸货费", scale = 2)
    private BigDecimal receivedUnloadingFee;

    /**
     * 未收打包费 unpaidPackingFee
     */
//    @Excel(name = "未收打包费", scale = 2)
    private BigDecimal unpaidPackagingFee;

    @Excel(name = "未收打包费", scale = 2)
    private BigDecimal unpaidPackingFee;

    @Excel(name = "实付打包费", scale = 2)
    private BigDecimal receivedPackingFee;

    /**
     * 物流代垫费
     */
    @Excel(name = "物流代垫费", scale = 2)
    private BigDecimal logisticsAdvanceFee;

    /**
     * 超期仓租
     */
//    @Excel(name = "超期仓租", scale = 2)
    private BigDecimal overdueRent;
    /**
     * 代收备注
     */
//    @Excel(name = "代收备注")
    private String collectionNotes;

    /**
     * 总箱数
     */
//    @Excel(name = "总箱数")
    private Long totalBoxes;

    /**
     * 总毛重
     */
//    @Excel(name = "总毛重", scale = 2)
    private BigDecimal totalGrossWeight;

    /**
     * 总体积
     */
//    @Excel(name = "总体积", scale = 2)
    private BigDecimal totalVolume;

    /**
     * 总行数
     */
//    @Excel(name = "总行数")
    private Long totalRows;


    /**
     * 租金平衡费
     */
//    @Excel(name = "租金平衡费", scale = 2)
    private BigDecimal rentalBalanceFee;


    private BigDecimal unpaidInboundFee;

    /**
     * 免堆天数
     */
//    @Excel(name = "免堆天数")
    private Long freeStackDays;

    /**
     * 超期单价
     */
//    @Excel(name = "超期单价", scale = 2)
    private BigDecimal overdueUnitPrice;
    private String outboundNote;
    private Date createdAt;
    private String outboundHandler;
    private String outboundMethod;
    private String plannedOutboundDate;
    private String preOutboundFlag;
    private List<RsInventory> rsInventoryList;
    private String outboundType;
    private String operator;
    private String operationRequirement;

        @Excel(name = "超期仓租", scale = 2)
    private BigDecimal overdueRentalFee;
    //    @Excel(name = "困难作业费", scale = 2)
    private BigDecimal difficultyWorkFee;
    //    @Excel(name = "仓库代付其他费用", scale = 2)
    private BigDecimal warehouseAdvanceOtherFee;
    /**
     * 已收供应商
     */
//    @Excel(name = "已收供应商总额", scale = 2)
    private BigDecimal receivedFromSupplier;

    /**
     * 未收客户
     */
//    @Excel(name = "未收客户", scale = 2)
    private BigDecimal unreceivedFromCustomer;

    /**
     * 已收客户
     */
//    @Excel(name = "已收客户", scale = 2)
    private BigDecimal receivedFromCustomer;

    /**
     * 应收客户余额
     */
//    @Excel(name = "应收客户余额", scale = 2)
    private BigDecimal customerReceivableBalance;

    /**
     * 应付工人
     */
//    @Excel(name = "应付工人", scale = 2)
    private BigDecimal payableToWorker;

    /**
     * 本票销售额
     */
//    @Excel(name = "本票销售额", scale = 2)
    private BigDecimal promissoryNoteSales;

    /**
     * 本票成本
     */
//    @Excel(name = "本票成本", scale = 2)
    private BigDecimal promissoryNoteCost;

    /**
     * 本票毛润
     */
//    @Excel(name = "本票毛润", scale = 2)
    private BigDecimal promissoryNoteGrossProfit;
    //    @Excel(name = "已收供应商", scale = 2)
    private BigDecimal receivedSupplier;
    private int isRentSettlement;

    private Date[] outboundDateRange;


    private BigDecimal additionalUnloadingFee;
    private Long rctId;
    @Excel(name = "客户单号")
    private String customerOrderNo;
    private String cargoType;
    private String plateNumber;
    private String driverPhone;
    private String warehouseConfirmed;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date confirmationTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderDate;

    public Date getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(Date orderDate) {
        this.orderDate = orderDate;
    }

    public Date getConfirmationTime() {
        return confirmationTime;
    }

    public void setConfirmationTime(Date confirmationTime) {
        this.confirmationTime = confirmationTime;
    }

    public String getWarehouseConfirmed() {
        return warehouseConfirmed;
    }

    public void setWarehouseConfirmed(String warehouseConfirmed) {
        this.warehouseConfirmed = warehouseConfirmed;
    }

    public String getDriverPhone() {
        return driverPhone;
    }

    public void setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone;
    }

    public String getPlateNumber() {
        return plateNumber;
    }

    public void setPlateNumber(String plateNumber) {
        this.plateNumber = plateNumber;
    }

    public String getCargoType() {
        return cargoType;
    }

    public void setCargoType(String cargoType) {
        this.cargoType = cargoType;
    }

    public String getCustomerOrderNo() {
        return customerOrderNo;
    }

    public void setCustomerOrderNo(String customerOrderNo) {
        this.customerOrderNo = customerOrderNo;
    }

    public Long getRctId() {
        return rctId;
    }

    public void setRctId(Long rctId) {
        this.rctId = rctId;
    }

    public BigDecimal getAdditionalUnloadingFee() {
        return additionalUnloadingFee;
    }

    public void setAdditionalUnloadingFee(BigDecimal additionalUnloadingFee) {
        this.additionalUnloadingFee = additionalUnloadingFee;
    }

    public BigDecimal getReceivedPackingFee() {
        return receivedPackingFee;
    }

    public void setReceivedPackingFee(BigDecimal receivedPackingFee) {
        this.receivedPackingFee = receivedPackingFee;
    }

    public BigDecimal getReceivedUnloadingFee() {
        return receivedUnloadingFee;
    }

    public void setReceivedUnloadingFee(BigDecimal receivedUnloadingFee) {
        this.receivedUnloadingFee = receivedUnloadingFee;
    }

    public BigDecimal getAdditionalStorageFee() {
        return additionalStorageFee;
    }

    public void setAdditionalStorageFee(BigDecimal additionalStorageFee) {
        this.additionalStorageFee = additionalStorageFee;
    }


    public Date[] getOutboundDateRange() {
        return outboundDateRange;
    }

    public void setOutboundDateRange(Date[] outboundDateRange) {
        this.outboundDateRange = outboundDateRange;
    }

    public int getIsRentSettlement() {
        return isRentSettlement;
    }

    public void setIsRentSettlement(int isRentSettlement) {
        this.isRentSettlement = isRentSettlement;
    }

    public BigDecimal getReceivedSupplier() {
        return receivedSupplier;
    }

    public void setReceivedSupplier(BigDecimal receivedSupplier) {
        this.receivedSupplier = receivedSupplier;
    }

    public BigDecimal getReceivedFromSupplier() {
        return receivedFromSupplier;
    }

    public void setReceivedFromSupplier(BigDecimal receivedFromSupplier) {
        this.receivedFromSupplier = receivedFromSupplier;
    }

    public BigDecimal getUnreceivedFromCustomer() {
        return unreceivedFromCustomer;
    }

    public void setUnreceivedFromCustomer(BigDecimal unreceivedFromCustomer) {
        this.unreceivedFromCustomer = unreceivedFromCustomer;
    }

    public BigDecimal getReceivedFromCustomer() {
        return receivedFromCustomer;
    }

    public void setReceivedFromCustomer(BigDecimal receivedFromCustomer) {
        this.receivedFromCustomer = receivedFromCustomer;
    }

    public BigDecimal getCustomerReceivableBalance() {
        return customerReceivableBalance;
    }

    public void setCustomerReceivableBalance(BigDecimal customerReceivableBalance) {
        this.customerReceivableBalance = customerReceivableBalance;
    }

    public BigDecimal getPayableToWorker() {
        return payableToWorker;
    }

    public void setPayableToWorker(BigDecimal payableToWorker) {
        this.payableToWorker = payableToWorker;
    }

    public BigDecimal getPromissoryNoteSales() {
        return promissoryNoteSales;
    }

    public void setPromissoryNoteSales(BigDecimal promissoryNoteSales) {
        this.promissoryNoteSales = promissoryNoteSales;
    }

    public BigDecimal getPromissoryNoteCost() {
        return promissoryNoteCost;
    }

    public void setPromissoryNoteCost(BigDecimal promissoryNoteCost) {
        this.promissoryNoteCost = promissoryNoteCost;
    }

    public BigDecimal getPromissoryNoteGrossProfit() {
        return promissoryNoteGrossProfit;
    }

    public void setPromissoryNoteGrossProfit(BigDecimal promissoryNoteGrossProfit) {
        this.promissoryNoteGrossProfit = promissoryNoteGrossProfit;
    }

    public BigDecimal getWarehouseAdvanceOtherFee() {
        return warehouseAdvanceOtherFee;
    }

    public void setWarehouseAdvanceOtherFee(BigDecimal warehouseAdvanceOtherFee) {
        this.warehouseAdvanceOtherFee = warehouseAdvanceOtherFee;
    }

    public BigDecimal getDifficultyWorkFee() {
        return difficultyWorkFee;
    }

    public void setDifficultyWorkFee(BigDecimal difficultyWorkFee) {
        this.difficultyWorkFee = difficultyWorkFee;
    }

    public BigDecimal getOverdueRentalFee() {
        return overdueRentalFee;
    }

    public void setOverdueRentalFee(BigDecimal overdueRentalFee) {
        this.overdueRentalFee = overdueRentalFee;
    }

    public BigDecimal getUnpaidPackingFee() {
        return unpaidPackingFee;
    }

    public void setUnpaidPackingFee(BigDecimal unpaidPackingFee) {
        this.unpaidPackingFee = unpaidPackingFee;
    }


    public BigDecimal getUnpaidInboundFee() {
        return unpaidInboundFee;
    }

    public void setUnpaidInboundFee(BigDecimal unpaidInboundFee) {
        this.unpaidInboundFee = unpaidInboundFee;
    }

    public String getOperationRequirement() {
        return operationRequirement;
    }

    public void setOperationRequirement(String operationRequirement) {
        this.operationRequirement = operationRequirement;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOutboundType() {
        return outboundType;
    }

    public void setOutboundType(String outboundType) {
        this.outboundType = outboundType;
    }

    public List<RsInventory> getRsInventoryList() {
        return rsInventoryList;
    }

    public void setRsInventoryList(List<RsInventory> rsInventoryList) {
        this.rsInventoryList = rsInventoryList;
    }

    public String getPreOutboundFlag() {
        return preOutboundFlag;
    }

    public void setPreOutboundFlag(String preOutboundFlag) {
        this.preOutboundFlag = preOutboundFlag;
    }

    public String getPlannedOutboundDate() {
        return plannedOutboundDate;
    }

    public void setPlannedOutboundDate(String plannedOutboundDate) {
        this.plannedOutboundDate = plannedOutboundDate;
    }

    public String getOutboundMethod() {
        return outboundMethod;
    }

    public void setOutboundMethod(String outboundMethod) {
        this.outboundMethod = outboundMethod;
    }

    public String getOutboundHandler() {
        return outboundHandler;
    }

    public void setOutboundHandler(String outboundHandler) {
        this.outboundHandler = outboundHandler;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getOutboundNote() {
        return outboundNote;
    }

    public void setOutboundNote(String outboundNote) {
        this.outboundNote = outboundNote;
    }

    public Long getOutboundRecordId() {
        return outboundRecordId;
    }

    public void setOutboundRecordId(Long outboundRecordId) {
        this.outboundRecordId = outboundRecordId;
    }

    public String getOutboundNo() {
        return outboundNo;
    }

    public void setOutboundNo(String outboundNo) {
        this.outboundNo = outboundNo;
    }

    public String getClientCode() {
        return clientCode;
    }

    public void setClientCode(String clientCode) {
        this.clientCode = clientCode;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public String getContainerType() {
        return containerType;
    }

    public void setContainerType(String containerType) {
        this.containerType = containerType;
    }

    public String getContainerNo() {
        return containerNo;
    }

    public void setContainerNo(String containerNo) {
        this.containerNo = containerNo;
    }

    public String getSealNo() {
        return sealNo;
    }

    public void setSealNo(String sealNo) {
        this.sealNo = sealNo;
    }

    public Date getOutboundDate() {
        return outboundDate;
    }

    public void setOutboundDate(Date outboundDate) {
        this.outboundDate = outboundDate;
    }

    public BigDecimal getWarehouseQuote() {
        return warehouseQuote;
    }

    public void setWarehouseQuote(BigDecimal warehouseQuote) {
        this.warehouseQuote = warehouseQuote;
    }

    public BigDecimal getWorkerLoadingFee() {
        return workerLoadingFee;
    }

    public void setWorkerLoadingFee(BigDecimal workerLoadingFee) {
        this.workerLoadingFee = workerLoadingFee;
    }

    public BigDecimal getWarehouseCollection() {
        return warehouseCollection;
    }

    public void setWarehouseCollection(BigDecimal warehouseCollection) {
        this.warehouseCollection = warehouseCollection;
    }

    public String getCollectionNotes() {
        return collectionNotes;
    }

    public void setCollectionNotes(String collectionNotes) {
        this.collectionNotes = collectionNotes;
    }

    public Long getTotalBoxes() {
        return totalBoxes;
    }

    public void setTotalBoxes(Long totalBoxes) {
        this.totalBoxes = totalBoxes;
    }

    public BigDecimal getTotalGrossWeight() {
        return totalGrossWeight;
    }

    public void setTotalGrossWeight(BigDecimal totalGrossWeight) {
        this.totalGrossWeight = totalGrossWeight;
    }

    public BigDecimal getTotalVolume() {
        return totalVolume;
    }

    public void setTotalVolume(BigDecimal totalVolume) {
        this.totalVolume = totalVolume;
    }

    public Long getTotalRows() {
        return totalRows;
    }

    public void setTotalRows(Long totalRows) {
        this.totalRows = totalRows;
    }

    public BigDecimal getReceivedStorageFee() {
        return receivedStorageFee;
    }

    public void setReceivedStorageFee(BigDecimal receivedStorageFee) {
        this.receivedStorageFee = receivedStorageFee;
    }

    public BigDecimal getUnpaidUnloadingFee() {
        return unpaidUnloadingFee;
    }

    public void setUnpaidUnloadingFee(BigDecimal unpaidUnloadingFee) {
        this.unpaidUnloadingFee = unpaidUnloadingFee;
    }

    public BigDecimal getUnpaidPackagingFee() {
        return unpaidPackagingFee;
    }

    public void setUnpaidPackagingFee(BigDecimal unpaidPackagingFee) {
        this.unpaidPackagingFee = unpaidPackagingFee;
    }

    public BigDecimal getLogisticsAdvanceFee() {
        return logisticsAdvanceFee;
    }

    public void setLogisticsAdvanceFee(BigDecimal logisticsAdvanceFee) {
        this.logisticsAdvanceFee = logisticsAdvanceFee;
    }

    public BigDecimal getRentalBalanceFee() {
        return rentalBalanceFee;
    }

    public void setRentalBalanceFee(BigDecimal rentalBalanceFee) {
        this.rentalBalanceFee = rentalBalanceFee;
    }

    public BigDecimal getOverdueRent() {
        return overdueRent;
    }

    public void setOverdueRent(BigDecimal overdueRent) {
        this.overdueRent = overdueRent;
    }

    public Long getFreeStackDays() {
        return freeStackDays;
    }

    public void setFreeStackDays(Long freeStackDays) {
        this.freeStackDays = freeStackDays;
    }

    public BigDecimal getOverdueUnitPrice() {
        return overdueUnitPrice;
    }

    public void setOverdueUnitPrice(BigDecimal overdueUnitPrice) {
        this.overdueUnitPrice = overdueUnitPrice;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("outboundRecordId", getOutboundRecordId())
                .append("outboundNo", getOutboundNo())
                .append("clientCode", getClientCode())
                .append("clientName", getClientName())
                .append("operatorId", getOperatorId())
                .append("containerType", getContainerType())
                .append("containerNo", getContainerNo())
                .append("sealNo", getSealNo())
                .append("outboundDate", getOutboundDate())
                .append("warehouseQuote", getWarehouseQuote())
                .append("workerLoadingFee", getWorkerLoadingFee())
                .append("warehouseCollection", getWarehouseCollection())
                .append("collectionNotes", getCollectionNotes())
                .append("totalBoxes", getTotalBoxes())
                .append("totalGrossWeight", getTotalGrossWeight())
                .append("totalVolume", getTotalVolume())
                .append("totalRows", getTotalRows())
                .append("receivedStorageFee", getReceivedStorageFee())
                .append("unpaidUnloadingFee", getUnpaidUnloadingFee())
                .append("unpaidPackagingFee", getUnpaidPackagingFee())
                .append("logisticsAdvanceFee", getLogisticsAdvanceFee())
                .append("rentalBalanceFee", getRentalBalanceFee())
                .append("overdueRent", getOverdueRent())
                .append("freeStackDays", getFreeStackDays())
                .append("overdueUnitPrice", getOverdueUnitPrice())
                .toString();
    }
}
