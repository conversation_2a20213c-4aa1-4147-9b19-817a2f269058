package com.rich.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 报价列表对象 rs_quotation
 *
 * <AUTHOR>
 * @date 2023-05-04
 */
public class RsQuotation extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 报价记录
     */
    private Long quotationId;

    private Integer typeId;

    private Long pageNum;

    private Long pageSize;

    /**
     * 报价单号
     */
    @Excel(name = "报价单号")
    private String richNo;

    /**
     * 客户
     */
    @Excel(name = "客户")
    private Long companyId;

    private String company;

    @Excel(name = "联系人")
    private Long extStaffId;

    private String extStaffName;

    private String extStaffPhoneNum;

    private String extStaffEmailEnterprise;

    @Excel(name = "业务员")
    private Long staffId;
    private String staffName;

    /**
     * 服务类型
     */
    @Excel(name = "服务类型")
    private Long logisticsTypeId;

    private String logisticsType;

    @Excel(name = "运输条款")
    private Long transportationTermsId;

    private String transportationTerms;

    private Long[] serviceTypeIds;

    private Long serviceTypeId;

    private String serviceType;

    /**
     * 进出口
     */
    @Excel(name = "进出口")
    private String imExPort;

    private Long[] cargoTypeIds;

    private String cargoType;
    private String cargoTypeEn;

    /**
     * 装运区域
     */
    @Excel(name = "装运区域")
    private Long[] loadingIds;

    private String loading;

    /**
     * 启运港
     */
    @Excel(name = "启运港")
    private Long[] departureIds;

    private Long departureId;

    private String departure;
    private String departureEn;

    /**
     * 目的港
     */
    @Excel(name = "目的港")
    private Long destinationId;

    private String destination;

    /**
     * 货物名称
     */
    @Excel(name = "货物名称")
    private String cargoName;

    /**
     * 货物限重
     */
    @Excel(name = "货物限重")
    private BigDecimal grossWeight;

    /**
     * 货好时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "货好时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date goodsTime;

    /**
     * 货物单位
     */
    @Excel(name = "货物单位")
    private Long cargoUnitId;

    private String cargoUnit;

    /**
     * 货值
     */
    @Excel(name = "货值")
    private BigDecimal cargoPrice;

    /**
     * 货值币种
     */
    @Excel(name = "货值币种")
    private Long cargoCurrencyId;

    private String cargoCurrency;

    private Long[] carrierIds;

    private String carrier;

    /**
     * 成本总计
     */
    @Excel(name = "成本总计")
    private BigDecimal inquiryRate;

    /**
     * 报价总计
     */
    @Excel(name = "报价总计")
    private BigDecimal quotationRate;

    /**
     * 利润总计
     */
    @Excel(name = "利润总计")
    private BigDecimal profitPrice;

    private String orderMark;

    /**
     * 货量信息
     */
    private List<MidRevenueTons> midRevenueTonsList;

    private List<RsQuotationFreight> rsQuotationFreightList;

    private List<Long> characteristicsIds;

    private String revenueTons;

    private Long unitId;
    private String CompanyName;
    private String quotationSketch;
    private String serviceShortType;
    private Long loadingId;

    private String unitCode;

    private String[] carriers;
    private String transportationTermsCode;

    private Long userId;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getTransportationTermsCode() {
        return transportationTermsCode;
    }

    public void setTransportationTermsCode(String transportationTermsCode) {
        this.transportationTermsCode = transportationTermsCode;
    }

    public String[] getCarriers() {
        return carriers;
    }

    public void setCarriers(String[] carriers) {
        this.carriers = carriers;
    }

    public BigDecimal getInquiryRate() {
        return inquiryRate;
    }

    public void setInquiryRate(BigDecimal inquiryRate) {
        this.inquiryRate = inquiryRate;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public Long getLoadingId() {
        return loadingId;
    }

    public void setLoadingId(Long loadingId) {
        this.loadingId = loadingId;
    }

    public String getServiceShortType() {
        return serviceShortType;
    }

    public void setServiceShortType(String serviceShortType) {
        this.serviceShortType = serviceShortType;
    }

    public String getQuotationSketch() {
        return quotationSketch;
    }

    public void setQuotationSketch(String quotationSketch) {
        this.quotationSketch = quotationSketch;
    }

    public String getCompanyName() {
        return CompanyName;
    }

    public void setCompanyName(String companyName) {
        CompanyName = companyName;
    }

    public Long getServiceTypeId() {
        return serviceTypeId;
    }

    public void setServiceTypeId(Long serviceTypeId) {
        this.serviceTypeId = serviceTypeId;
    }

    public String getCargoCurrency() {
        return cargoCurrency;
    }

    public void setCargoCurrency(String cargoCurrency) {
        this.cargoCurrency = cargoCurrency;
    }

    public Long[] getCarrierIds() {
        return carrierIds;
    }

    public void setCarrierIds(Long[] carrierIds) {
        this.carrierIds = carrierIds;
    }

    public String getExtStaffPhoneNum() {
        return extStaffPhoneNum;
    }

    public void setExtStaffPhoneNum(String extStaffPhoneNum) {
        this.extStaffPhoneNum = extStaffPhoneNum;
    }

    public String getExtStaffEmailEnterprise() {
        return extStaffEmailEnterprise;
    }

    public void setExtStaffEmailEnterprise(String extStaffEmailEnterprise) {
        this.extStaffEmailEnterprise = extStaffEmailEnterprise;
    }

    public List<Long> getCharacteristicsIds() {
        return characteristicsIds;
    }

    public void setCharacteristicsIds(List<Long> characteristicsIds) {
        this.characteristicsIds = characteristicsIds;
    }

    public List<RsQuotationFreight> getRsQuotationFreightList() {
        return rsQuotationFreightList;
    }

    public void setRsQuotationFreightList(List<RsQuotationFreight> rsQuotationFreightList) {
        this.rsQuotationFreightList = rsQuotationFreightList;
    }

    public String getCargoTypeEn() {
        return cargoTypeEn;
    }

    public void setCargoTypeEn(String cargoTypeEn) {
        this.cargoTypeEn = cargoTypeEn;
    }

    public String getDepartureEn() {
        return departureEn;
    }

    public void setDepartureEn(String departureEn) {
        this.departureEn = departureEn;
    }

    public String getRevenueTons() {
        return revenueTons;
    }

    public void setRevenueTons(String revenueTons) {
        this.revenueTons = revenueTons;
    }

    public Integer getTypeId() {
        return typeId;
    }

    public void setTypeId(Integer typeId) {
        this.typeId = typeId;
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public Long getPageNum() {
        return pageNum;
    }

    public void setPageNum(Long pageNum) {
        this.pageNum = pageNum;
    }

    public Long getPageSize() {
        return pageSize;
    }

    public void setPageSize(Long pageSize) {
        this.pageSize = pageSize;
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }

    public String getExtStaffName() {
        return extStaffName;
    }

    public void setExtStaffName(String extStaffName) {
        this.extStaffName = extStaffName;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public Long getExtStaffId() {
        return extStaffId;
    }

    public void setExtStaffId(Long extStaffId) {
        this.extStaffId = extStaffId;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public String getLogisticsType() {
        return logisticsType;
    }

    public void setLogisticsType(String logisticsType) {
        this.logisticsType = logisticsType;
    }

    public Long getTransportationTermsId() {
        return transportationTermsId;
    }

    public void setTransportationTermsId(Long transportationTermsId) {
        this.transportationTermsId = transportationTermsId;
    }

    public String getTransportationTerms() {
        return transportationTerms;
    }

    public void setTransportationTerms(String transportationTerms) {
        this.transportationTerms = transportationTerms;
    }

    public Long[] getServiceTypeIds() {
        return serviceTypeIds;
    }

    public void setServiceTypeIds(Long[] serviceTypeIds) {
        this.serviceTypeIds = serviceTypeIds;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public Long[] getCargoTypeIds() {
        return cargoTypeIds;
    }

    public void setCargoTypeIds(Long[] cargoTypeIds) {
        this.cargoTypeIds = cargoTypeIds;
    }

    public String getCargoType() {
        return cargoType;
    }

    public void setCargoType(String cargoType) {
        this.cargoType = cargoType;
    }

    public Long[] getLoadingIds() {
        return loadingIds;
    }

    public void setLoadingIds(Long[] loadingIds) {
        this.loadingIds = loadingIds;
    }

    public String getLoading() {
        return loading;
    }

    public void setLoading(String loading) {
        this.loading = loading;
    }

    public Long[] getDepartureIds() {
        return departureIds;
    }

    public void setDepartureIds(Long[] departureIds) {
        this.departureIds = departureIds;
    }

    public Long getDepartureId() {
        return departureId;
    }

    public void setDepartureId(Long departureId) {
        this.departureId = departureId;
    }

    public String getDeparture() {
        return departure;
    }

    public void setDeparture(String departure) {
        this.departure = departure;
    }

    public String getCargoUnit() {
        return cargoUnit;
    }

    public void setCargoUnit(String cargoUnit) {
        this.cargoUnit = cargoUnit;
    }
    public String getCarrier() {
        return carrier;
    }

    public void setCarrier(String carrier) {
        this.carrier = carrier;
    }

    public String getOrderMark() {
        return orderMark;
    }

    public void setOrderMark(String orderMark) {
        this.orderMark = orderMark;
    }

    public void setQuotationId(Long quotationId) {
        this.quotationId = quotationId;
    }

    public Long getQuotationId() {
        return quotationId;
    }

    public void setRichNo(String richNo) {
        this.richNo = richNo;
    }

    public String getRichNo() {
        return richNo;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setLogisticsTypeId(Long logisticsTypeId) {
        this.logisticsTypeId = logisticsTypeId;
    }

    public Long getLogisticsTypeId() {
        return logisticsTypeId;
    }

    public void setImExPort(String imExPort) {
        this.imExPort = imExPort;
    }

    public String getImExPort() {
        return imExPort;
    }

    public void setDestinationId(Long destinationId) {
        this.destinationId = destinationId;
    }

    public Long getDestinationId() {
        return destinationId;
    }

    public void setCargoName(String cargoName) {
        this.cargoName = cargoName;
    }

    public String getCargoName() {
        return cargoName;
    }

    public void setGrossWeight(BigDecimal grossWeight) {
        this.grossWeight = grossWeight;
    }

    public BigDecimal getGrossWeight() {
        return grossWeight;
    }

    public void setGoodsTime(Date goodsTime) {
        this.goodsTime = goodsTime;
    }

    public Date getGoodsTime() {
        return goodsTime;
    }

    public void setCargoUnitId(Long cargoUnitId) {
        this.cargoUnitId = cargoUnitId;
    }

    public Long getCargoUnitId() {
        return cargoUnitId;
    }

    public void setCargoPrice(BigDecimal cargoPrice) {
        this.cargoPrice = cargoPrice;
    }

    public BigDecimal getCargoPrice() {
        return cargoPrice;
    }

    public void setCargoCurrencyId(Long cargoCurrencyId) {
        this.cargoCurrencyId = cargoCurrencyId;
    }

    public Long getCargoCurrencyId() {
        return cargoCurrencyId;
    }

    public BigDecimal getCostRate() {
        return inquiryRate;
    }

    public void setCostRate(BigDecimal inquiryRate) {
        this.inquiryRate = inquiryRate;
    }

    public BigDecimal getQuotationRate() {
        return quotationRate;
    }

    public void setQuotationRate(BigDecimal quotationRate) {
        this.quotationRate = quotationRate;
    }

    public void setProfitPrice(BigDecimal profitPrice) {
        this.profitPrice = profitPrice;
    }

    public BigDecimal getProfitPrice() {
        return profitPrice;
    }

    public List<MidRevenueTons> getMidRevenueTonsList() {
        return midRevenueTonsList;
    }

    public void setMidRevenueTonsList(List<MidRevenueTons> midRevenueTonsList) {
        this.midRevenueTonsList = midRevenueTonsList;
    }

}
