package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasCharge;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasChargeService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 费用Controller
 *
 * <AUTHOR>
 * @date 2022-08-30
 */
@RestController
@RequestMapping("/system/charge")

public class BasChargeController extends BaseController {

    @Autowired
    private BasChargeService basChargeService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询费用列表
     */
    @PreAuthorize("@ss.hasPermi('system:charge:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasCharge basCharge) {
        startPage();
        List<BasCharge> list = basChargeService.selectBasChargeList(basCharge);
        return getDataTable(list);
    }

    @GetMapping("/selectList")
    public AjaxResult selectList(BasCharge basCharge) {
        List<BasCharge> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "charge");
        if (list == null) {
            basCharge.setStatus("0");
            list = basChargeService.selectBasChargeList(basCharge);
            redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "charge");
            redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "charge", list);
        }
        return AjaxResult.success(AjaxResult.DATA_TAG, list);
    }

    /**
     * 导出费用列表
     */
    @PreAuthorize("@ss.hasPermi('system:charge:export')")
    @Log(title = "费用", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasCharge basCharge) {
        List<BasCharge> list = basChargeService.selectBasChargeList(basCharge);
        ExcelUtil<BasCharge> util = new ExcelUtil<BasCharge>(BasCharge.class);
        util.exportExcel(response, list, "费用数据");
    }

    /**
     * 获取费用详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:charge:edit')")
    @GetMapping(value = "/{chargeId}")
    public AjaxResult getInfo(@PathVariable("chargeId") Long chargeId) {
        return AjaxResult.success(basChargeService.selectBasChargeByChargeId(chargeId));
    }

    /**
     * 新增费用
     */
    @PreAuthorize("@ss.hasPermi('system:charge:add')")
    @Log(title = "费用", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasCharge basCharge) {
        int out = basChargeService.insertBasCharge(basCharge);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "charge");
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "chargeList");
        return toAjax(out);
    }

    /**
     * 修改费用
     */
    @PreAuthorize("@ss.hasPermi('system:charge:edit')")
    @Log(title = "费用", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasCharge basCharge) {
        int out = basChargeService.updateBasCharge(basCharge);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "charge");
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "chargeList");
        return toAjax(out);
    }

    /**
     * 修改费用
     */
    @PreAuthorize("@ss.hasPermi('system:charge:edit')")
    @Log(title = "费用", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasCharge basCharge) {
        int out = basChargeService.changeStatus(basCharge);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "charge");
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "chargeList");
        return toAjax(out);
    }


    /**
     * 删除费用
     */
    @PreAuthorize("@ss.hasPermi('system:charge:remove')")
    @Log(title = "费用", businessType = BusinessType.DELETE)
    @DeleteMapping("/{chargeIds}")
    public AjaxResult remove(@PathVariable Long[] chargeIds) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "charge");
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "chargeList");
        return toAjax(basChargeService.deleteBasChargeByChargeIds(chargeIds));
    }
}
