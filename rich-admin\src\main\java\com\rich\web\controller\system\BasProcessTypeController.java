package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasProcessType;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasProcessTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 进度分类Controller
 *
 * <AUTHOR>
 * @date 2023-06-05
 */
@RestController
@RequestMapping("/system/processtype")
public class BasProcessTypeController extends BaseController {
    @Autowired
    private BasProcessTypeService basProcessTypeService;

    /**
     * 查询进度分类列表
     */
    @GetMapping("/list")
    public TableDataInfo list(BasProcessType basProcessType) {
        startPage();
        List<BasProcessType> list = basProcessTypeService.selectBasProcessTypeList(basProcessType);
        return getDataTable(list);
    }

    /**
     * 导出进度分类列表
     */
    @PreAuthorize("@ss.hasPermi('system:processtype:export')")
    @Log(title = "进度分类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasProcessType basProcessType) {
        List<BasProcessType> list = basProcessTypeService.selectBasProcessTypeList(basProcessType);
        ExcelUtil<BasProcessType> util = new ExcelUtil<>(BasProcessType.class);
        util.exportExcel(response, list, "进度分类数据");
    }

    /**
     * 获取进度分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:processtype:query')")
    @GetMapping(value = "/{processTypeId}")
    public AjaxResult getInfo(@PathVariable("processTypeId") Long processTypeId) {
        return AjaxResult.success(basProcessTypeService.selectBasProcessTypeByProcessTypeId(processTypeId));
    }

    /**
     * 新增进度分类
     */
    @PreAuthorize("@ss.hasPermi('system:processtype:add')")
    @Log(title = "进度分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasProcessType basProcessType) {
        return toAjax(basProcessTypeService.insertBasProcessType(basProcessType));
    }

    /**
     * 修改进度分类
     */
    @PreAuthorize("@ss.hasPermi('system:processtype:edit')")
    @Log(title = "进度分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasProcessType basProcessType) {
        return toAjax(basProcessTypeService.updateBasProcessType(basProcessType));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:processtype:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasProcessType basProcessType) {
        basProcessType.setUpdateBy(getUserId());
        return toAjax(basProcessTypeService.changeStatus(basProcessType));
    }

    /**
     * 删除进度分类
     */
    @PreAuthorize("@ss.hasPermi('system:processtype:remove')")
    @Log(title = "进度分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{processTypeIds}")
    public AjaxResult remove(@PathVariable Long[] processTypeIds) {
        return toAjax(basProcessTypeService.deleteBasProcessTypeByProcessTypeIds(processTypeIds));
    }
}
