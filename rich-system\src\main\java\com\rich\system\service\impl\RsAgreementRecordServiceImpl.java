package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.RsAgreementRecord;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.system.mapper.RsAgreementRecordMapper;
import com.rich.system.service.RsAgreementRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 协议记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-10-28
 */
@Service

public class RsAgreementRecordServiceImpl implements RsAgreementRecordService {

    @Autowired
    private  RsAgreementRecordMapper rsAgreementRecordMapper;

    /**
     * 查询协议记录
     *
     * @param agreementId 协议记录主键
     * @return 协议记录
     */
    @Override
    public RsAgreementRecord selectRsAgreementRecordByAgreementId(Long agreementId) {
        return rsAgreementRecordMapper.selectRsAgreementRecordByAgreementId(agreementId);
    }

    /**
     * 查询协议记录列表
     *
     * @param rsAgreementRecord 协议记录
     * @return 协议记录
     */
    @Override
    public List<RsAgreementRecord> selectRsAgreementRecordList(RsAgreementRecord rsAgreementRecord) {
        return rsAgreementRecordMapper.selectRsAgreementRecordList(rsAgreementRecord);
    }

    /**
     * 新增协议记录
     *
     * @param rsAgreementRecord 协议记录
     * @return 结果
     */
    @Override
    public int insertRsAgreementRecord(RsAgreementRecord rsAgreementRecord) {
        rsAgreementRecord.setCreateTime(DateUtils.getNowDate());
        rsAgreementRecord.setCreateBy(SecurityUtils.getUserId());
        return rsAgreementRecordMapper.insertRsAgreementRecord(rsAgreementRecord);
    }

    /**
     * 修改协议记录
     *
     * @param rsAgreementRecord 协议记录
     * @return 结果
     */
    @Override
    public int updateRsAgreementRecord(RsAgreementRecord rsAgreementRecord) {
        rsAgreementRecord.setUpdateTime(DateUtils.getNowDate());
        rsAgreementRecord.setUpdateBy(SecurityUtils.getUserId());
        return rsAgreementRecordMapper.updateRsAgreementRecord(rsAgreementRecord);
    }

    /**
     * 批量删除协议记录
     *
     * @param agreementIds 需要删除的协议记录主键
     * @return 结果
     */
    @Override
    public int deleteRsAgreementRecordByAgreementIds(Long[] agreementIds) {
        return rsAgreementRecordMapper.deleteRsAgreementRecordByAgreementIds(agreementIds);
    }

    /**
     * 删除协议记录信息
     *
     * @param agreementId 协议记录主键
     * @return 结果
     */
    @Override
    public int deleteRsAgreementRecordByAgreementId(Long agreementId) {
        return rsAgreementRecordMapper.deleteRsAgreementRecordByAgreementId(agreementId);
    }
}
