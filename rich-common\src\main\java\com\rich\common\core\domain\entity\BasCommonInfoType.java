package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

/**
 * 通用信息类型对象 bas_common_info_type
 *
 * <AUTHOR>
 * @date 2022-12-07
 */
public class BasCommonInfoType extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 通用信息类别
     */
    private Long infoTypeId;

    private Long parentId;

    private String ancestors;

    /**
     * 简称
     */
    @Excel(name = "简称")
    private String infoTypeShortName;

    /**
     * 中文名
     */
    @Excel(name = "中文名")
    private String infoTypeLocalName;

    /**
     * 英文名
     */
    @Excel(name = "英文名")
    private String infoTypeEnName;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Integer orderNum;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;

    private String infoTypeQuery;

    private BasCommonInfo basCommonInfo;

    public BasCommonInfo getBasCommonInfo() {
        return basCommonInfo;
    }

    public void setBasCommonInfo(BasCommonInfo basCommonInfo) {
        this.basCommonInfo = basCommonInfo;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getAncestors() {
        return ancestors;
    }

    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }

    public String getInfoTypeQuery() {
        return infoTypeQuery;
    }

    public void setInfoTypeQuery(String infoTypeQuery) {
        this.infoTypeQuery = infoTypeQuery;
    }

    public void setInfoTypeId(Long infoTypeId) {
        this.infoTypeId = infoTypeId;
    }

    public Long getInfoTypeId() {
        return infoTypeId;
    }

    public void setInfoTypeShortName(String infoTypeShortName) {
        this.infoTypeShortName = infoTypeShortName;
    }

    public String getInfoTypeShortName() {
        return infoTypeShortName;
    }

    public void setInfoTypeLocalName(String infoTypeLocalName) {
        this.infoTypeLocalName = infoTypeLocalName;
    }

    public String getInfoTypeLocalName() {
        return infoTypeLocalName;
    }

    public void setInfoTypeEnName(String infoTypeEnName) {
        this.infoTypeEnName = infoTypeEnName;
    }

    public String getInfoTypeEnName() {
        return infoTypeEnName;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

}
