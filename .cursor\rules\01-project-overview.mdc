---
description: 
globs: 
alwaysApply: false
---
# 瑞旗管理系统 (Rich) 项目概述

这是一个基于 Spring Boot 的多模块企业级管理系统，采用前后端分离架构，包含三端应用：

- 后端：基于 Spring Boot 2.5.x + MyBatis 的 Java 多模块应用
- 管理前端：基于 Vue2 + Element-UI 的管理系统界面
- 移动端：基于 uni-app + uni-ui 的多端应用（小程序/App/H5）

## 核心模块说明

### 后端模块 (Maven 项目)
- [rich-admin](mdc:rich-admin/pom.xml): 系统入口模块，包含启动类与基础配置
- [rich-framework](mdc:rich-framework/pom.xml): 框架核心模块，提供框架基础设施
- [rich-system](mdc:rich-system/pom.xml): 系统功能模块，包含用户、角色、权限等核心业务
- [rich-common](mdc:rich-common/pom.xml): 通用工具模块，提供工具类与共享组件
- [rich-quartz](mdc:rich-quartz/pom.xml): 定时任务模块，基于 Quartz 实现任务调度
- [rich-generator](mdc:rich-generator/pom.xml): 代码生成模块，提供代码生成功能

### 前端模块
- [rich-ui](mdc:rich-ui/package.json): 基于 Vue2 + Element-UI 的管理平台
- [rich-app](mdc:rich-app/package.json): 基于 uni-app 的移动端应用

## 技术栈概述

### 后端技术栈
- 核心框架：Spring Boot 2.5.x
- 安全框架：自定义 JWT 认证
- 持久层框架：MyBatis + PageHelper
- 数据库连接池：Druid
- 缓存框架：Spring Cache + Redis
- 日志管理：SLF4J + Logback
- API文档：Swagger3
- 其他：Fastjson2, Commons-io, POI 等工具库

### 前端技术栈
- Vue2 + Vuex + Vue Router
- Element-UI 组件库
- Axios 请求库
- ECharts 图表库

### 移动端技术栈
- uni-app 框架
- uni-ui 组件库
- Vuex 状态管理

