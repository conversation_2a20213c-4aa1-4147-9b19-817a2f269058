package com.rich.common.core.domain.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 客户在仓货物明细对象 rs_cargo_details
 *
 * <AUTHOR>
 * @date 2024-11-11
 */
public class RsCargoDetails extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @Excel(name = "货物编号", cellType = Excel.ColumnType.NUMERIC)
    private Long cargoDetailsId;

    /**
     * 入仓流水号
     */
    @Excel(name = "入仓流水号")
    private String inboundSerialNo;

    /**
     * 入仓流水号拆分
     */
    @Excel(name = "入仓流水号拆分")
    private String inboundSerialSplit;

    /**
     * 客户代码
     */
    @Excel(name = "客户代码")
    private String clientCode;

    /**
     * 唛头
     */
    @Excel(name = "唛头")
    private String shippingMark;

    /**
     * 具体货名
     */
    @Excel(name = "具体货名")
    private String itemName;
    private String itemEnName;

    /**
     * 箱数
     */
    @Excel(name = "箱数")
    private Long boxCount;

    /**
     * 包装类型（纸箱/木箱/托盘/吨袋等）
     */
    @Excel(name = "包装类型", readConverterExp = "纸=箱/木箱/托盘/吨袋等")
    private String packageType;

    /**
     * 单件毛重
     */
    @Excel(name = "单件毛重", scale = 2)
    private BigDecimal unitGrossWeight;

    /**
     * 单件长
     */
    @Excel(name = "单件长", scale = 2)
    private BigDecimal unitLength;

    /**
     * 单件宽
     */
    @Excel(name = "单件宽", scale = 2)
    private BigDecimal unitWidth;

    /**
     * 单件高
     */
    @Excel(name = "单件高", scale = 2)
    private BigDecimal unitHeight;

    /**
     * 单件体积
     */
    @Excel(name = "单件体积", scale = 2)
    private BigDecimal unitVolume;

    /**
     * 破损标志
     */
    private String damageStatus;

    /**
     * 检索条码
     */
    @Excel(name = "检索条码")
    private String barcode;
    private Long inventoryId;
    private Date createdAt;
    private String preOutboundFlag;
    private String inventoryStatus;
    private Long outboundRecordId;
    private BigDecimal singlePieceWeight;
    private BigDecimal singlePieceVolume;
    private int boxItemCount;
    private int subtotalItemCount;
    private Date expressDate;
    private BigDecimal additionalFee;
    private String expressNo;

    public String getItemEnName() {
        return itemEnName;
    }

    public void setItemEnName(String itemEnName) {
        this.itemEnName = itemEnName;
    }

    public String getExpressNo() {
        return expressNo;
    }

    public void setExpressNo(String expressNo) {
        this.expressNo = expressNo;
    }

    public BigDecimal getAdditionalFee() {
        return additionalFee;
    }

    public void setAdditionalFee(BigDecimal additionalFee) {
        this.additionalFee = additionalFee;
    }

    public Date getExpressDate() {
        return expressDate;
    }

    public void setExpressDate(Date expressDate) {
        this.expressDate = expressDate;
    }

    public int getSubtotalItemCount() {
        return subtotalItemCount;
    }

    public void setSubtotalItemCount(int subtotalItemCount) {
        this.subtotalItemCount = subtotalItemCount;
    }

    public int getBoxItemCount() {
        return boxItemCount;
    }

    public void setBoxItemCount(int boxItemCount) {
        this.boxItemCount = boxItemCount;
    }

    public BigDecimal getSinglePieceVolume() {
        return singlePieceVolume;
    }

    public void setSinglePieceVolume(BigDecimal singlePieceVolume) {
        this.singlePieceVolume = singlePieceVolume;
    }

    public BigDecimal getSinglePieceWeight() {
        return singlePieceWeight;
    }

    public void setSinglePieceWeight(BigDecimal singlePieceWeight) {
        this.singlePieceWeight = singlePieceWeight;
    }

    public Long getOutboundRecordId() {
        return outboundRecordId;
    }

    public void setOutboundRecordId(Long outboundRecordId) {
        this.outboundRecordId = outboundRecordId;
    }

    public String getInventoryStatus() {
        return inventoryStatus;
    }

    public void setInventoryStatus(String inventoryStatus) {
        this.inventoryStatus = inventoryStatus;
    }

    public String getPreOutboundFlag() {
        return preOutboundFlag;
    }

    public void setPreOutboundFlag(String preOutboundFlag) {
        this.preOutboundFlag = preOutboundFlag;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Long getInventoryId() {
        return inventoryId;
    }

    public void setInventoryId(Long inventoryId) {
        this.inventoryId = inventoryId;
    }

    public Long getCargoDetailsId() {
        return cargoDetailsId;
    }

    public void setCargoDetailsId(Long cargoDetailsId) {
        this.cargoDetailsId = cargoDetailsId;
    }

    public String getInboundSerialNo() {
        return inboundSerialNo;
    }

    public void setInboundSerialNo(String inboundSerialNo) {
        this.inboundSerialNo = inboundSerialNo;
    }

    public String getInboundSerialSplit() {
        return inboundSerialSplit;
    }

    public void setInboundSerialSplit(String inboundSerialSplit) {
        this.inboundSerialSplit = inboundSerialSplit;
    }

    public String getClientCode() {
        return clientCode;
    }

    public void setClientCode(String clientCode) {
        this.clientCode = clientCode;
    }

    public String getShippingMark() {
        return shippingMark;
    }

    public void setShippingMark(String shippingMark) {
        this.shippingMark = shippingMark;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public Long getBoxCount() {
        return boxCount;
    }

    public void setBoxCount(Long boxCount) {
        this.boxCount = boxCount;
    }

    public String getPackageType() {
        return packageType;
    }

    public void setPackageType(String packageType) {
        this.packageType = packageType;
    }

    public BigDecimal getUnitGrossWeight() {
        return unitGrossWeight;
    }

    public void setUnitGrossWeight(BigDecimal unitGrossWeight) {
        this.unitGrossWeight = unitGrossWeight;
    }

    public BigDecimal getUnitLength() {
        return unitLength;
    }

    public void setUnitLength(BigDecimal unitLength) {
        this.unitLength = unitLength;
    }

    public BigDecimal getUnitWidth() {
        return unitWidth;
    }

    public void setUnitWidth(BigDecimal unitWidth) {
        this.unitWidth = unitWidth;
    }

    public BigDecimal getUnitHeight() {
        return unitHeight;
    }

    public void setUnitHeight(BigDecimal unitHeight) {
        this.unitHeight = unitHeight;
    }

    public BigDecimal getUnitVolume() {
        return unitVolume;
    }

    public void setUnitVolume(BigDecimal unitVolume) {
        this.unitVolume = unitVolume;
    }

    public String getDamageStatus() {
        return damageStatus;
    }

    public void setDamageStatus(String damageStatus) {
        this.damageStatus = damageStatus;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("cargoDetailsId", getCargoDetailsId())
                .append("inboundSerialNo", getInboundSerialNo())
                .append("inboundSerialSplit", getInboundSerialSplit())
                .append("clientCode", getClientCode())
                .append("shippingMark", getShippingMark())
                .append("itemName", getItemName())
                .append("boxCount", getBoxCount())
                .append("packageType", getPackageType())
                .append("unitGrossWeight", getUnitGrossWeight())
                .append("unitLength", getUnitLength())
                .append("unitWidth", getUnitWidth())
                .append("unitHeight", getUnitHeight())
                .append("unitVolume", getUnitVolume())
                .append("damageStatus", getDamageStatus())
                .append("barcode", getBarcode())
                .toString();
    }
}
