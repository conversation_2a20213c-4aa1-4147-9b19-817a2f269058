package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpPortService;
import org.apache.ibatis.annotations.Mapper;

/**
 * 码头服务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Mapper
public interface RsOpPortServiceMapper {
    /**
     * 查询码头服务
     *
     * @param portServiceId 码头服务主键
     * @return 码头服务
     */
    RsOpPortService selectRsOpPortServiceByPortServiceId(Long portServiceId);

    /**
     * 查询码头服务列表
     *
     * @param rsOpPortService 码头服务
     * @return 码头服务集合
     */
    List<RsOpPortService> selectRsOpPortServiceList(RsOpPortService rsOpPortService);

    /**
     * 新增码头服务
     *
     * @param rsOpPortService 码头服务
     * @return 结果
     */
    int insertRsOpPortService(RsOpPortService rsOpPortService);

    /**
     * 修改码头服务
     *
     * @param rsOpPortService 码头服务
     * @return 结果
     */
    int updateRsOpPortService(RsOpPortService rsOpPortService);

    /**
     * 删除码头服务
     *
     * @param portServiceId 码头服务主键
     * @return 结果
     */
    int deleteRsOpPortServiceByPortServiceId(Long portServiceId);

    /**
     * 批量删除码头服务
     *
     * @param portServiceIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsOpPortServiceByPortServiceIds(Long[] portServiceIds);

    RsOpPortService selectRsOpPortServiceByRctId(Long rctId);
}
