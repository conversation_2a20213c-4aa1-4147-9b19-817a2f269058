# 项目相关配置
rich:
  # 名称
  name: Rich
  # 版本
  version: 1.0.0
  # 版权年份
  copyrightYear: 2023
  # 实例演示开关
  demoEnabled: false
  # 文件路径 示例（ Windows配置D:/rich/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /data/profile
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math
  # 积木报表访问地址
  reporturl: http://127.0.0.1:8088
  # 水单上传路径
  bankSlipImage: /bankSlipImage


# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8088
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 200
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100
    connection-timeout: 100000
    keep-alive-timeout: 100000
# 日志配置
logging:
  level:
    com.rich: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: @profileActive@
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  #  # redis 配置
  #  redis:
  #    # 地址
  #    #    host: *************
  #    host: **********
  #    # 端口，默认为6379
  #    port: 6666
  #    # 数据库索引
  #    database: 0
  #    # 密码
  #    password: rich2009&&
  #    # 连接超时时间
  #    timeout: 60s
  #    jedis:
  #      pool:
  #        # 连接池中的最小空闲连接
  #        min-idle: 0
  #        # 连接池中的最大空闲连接
  #        max-idle: 8
  #        # 连接池的最大数据库连接数
  #        max-active: 8
  #        # #连接池最大阻塞等待时间（使用负值表示没有限制）
  ##        max-wait: -1ms
  mvc:
    async:
      request-timeout: 20s
#  rabbitmq:
#    host: localhost
#    port: 5672
#  elasticsearch:
#    rest:
#      uris: localhost:9200

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: tahaoshuaitahaoshuaiatahaoshuai
  # 令牌有效期（默认30分钟）
  expireTime: 360

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.rich.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  support-methods-arguments: true
  params: count=countSql
  reasonable: true
  helper-dialect: mysql
  page-size-zero: true

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

minidao:
  base-package: org.jeecg.modules.jmreport.desreport.dao*
  db-type: mysql

wechat:
  app-id: wx72ed58827900e965  # 使用您的真实AppID替换
  app-secret: 916157cd525efaed025e08dcfe339608  # 使用您的真实AppSecret替换
  redirect-url: https://sys.richgz.com/prod-api/wechat/callback  # 请替换为您的回调地址，例如：https://yourdomain.com/wechat/callback

mp:
  app-id: wxa5804c454f230752
  app-secret: fc06d727878b2350ce27e8b7da770279