package com.rich.common.enums;

/**
 * 用户状态
 *
 * <AUTHOR>
 */
public enum UserStatus
{
    OK("0", "在职"), DISABLE("1", "未入职"), DELETED("2", "离职");

    private final String code;
    private final String info;

    UserStatus(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
