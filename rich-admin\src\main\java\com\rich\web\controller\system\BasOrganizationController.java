package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasOrganization;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasOrganizationService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 所属组织Controller
 *
 * <AUTHOR>
 * @date 2022-09-30
 */
@RestController
@RequestMapping("/system/organization")
public class BasOrganizationController extends BaseController {

    @Autowired
    private BasOrganizationService basOrganizationService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询所属组织列表
     */
    @PreAuthorize("@ss.hasPermi('system:organization:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasOrganization basOrganization) {
        startPage();
        List<BasOrganization> list = basOrganizationService.selectBasOrganizationList(basOrganization);
        return getDataTable(list);
    }

    @GetMapping("/selectList")
    public AjaxResult selectList(BasOrganization basOrganization) {
        List<BasOrganization> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "organization");
        if (list == null) {
            basOrganization.setStatus("0");
            list = basOrganizationService.selectBasOrganizationList(basOrganization);
            redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "organization");
            redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "organization", list);
        }
        return AjaxResult.success(AjaxResult.DATA_TAG, list);
    }

    /**
     * 导出所属组织列表
     */
    @PreAuthorize("@ss.hasPermi('system:organization:export')")
    @Log(title = "所属组织", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasOrganization basOrganization) {
        List<BasOrganization> list = basOrganizationService.selectBasOrganizationList(basOrganization);
        ExcelUtil<BasOrganization> util = new ExcelUtil<BasOrganization>(BasOrganization.class);
        util.exportExcel(response, list, "所属组织数据");
    }

    /**
     * 获取所属组织详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:organization:edit')")
    @GetMapping(value = "/{organizationId}")
    public AjaxResult getInfo(@PathVariable("organizationId") Long organizationId) {
        return AjaxResult.success(basOrganizationService.selectBasOrganizationByOrganizationId(organizationId));
    }

    /**
     * 新增所属组织
     */
    @PreAuthorize("@ss.hasPermi('system:organization:add')")
    @Log(title = "所属组织", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasOrganization basOrganization) {
        int out = basOrganizationService.insertBasOrganization(basOrganization);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "organization");
        return toAjax(out);
    }

    /**
     * 修改所属组织
     */
    @PreAuthorize("@ss.hasPermi('system:organization:edit')")
    @Log(title = "所属组织", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasOrganization basOrganization) {
        int out = basOrganizationService.updateBasOrganization(basOrganization);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "organization");
        return toAjax(out);
    }

    /**
     * 修改所属组织
     */
    @PreAuthorize("@ss.hasPermi('system:organization:edit')")
    @Log(title = "所属组织", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasOrganization basOrganization) {
        int out = basOrganizationService.changeStatus(basOrganization);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "organization");
        return toAjax(out);
    }

    /**
     * 删除所属组织
     */
    @PreAuthorize("@ss.hasPermi('system:organization:remove')")
    @Log(title = "所属组织", businessType = BusinessType.DELETE)
    @DeleteMapping("/{organizationIds}")
    public AjaxResult remove(@PathVariable Long[] organizationIds) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "organization");
        return toAjax(basOrganizationService.deleteBasOrganizationByOrganizationIds(organizationIds));
    }
}
