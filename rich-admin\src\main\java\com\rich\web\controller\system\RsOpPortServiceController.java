package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsOpPortService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsOpPortServiceService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 码头服务Controller
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@RestController
@RequestMapping("/system/opportservice")
public class RsOpPortServiceController extends BaseController {
    @Autowired
    private RsOpPortServiceService rsOpPortServiceService;

    /**
     * 查询码头服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opportservice:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsOpPortService rsOpPortService) {
        startPage();
        List<RsOpPortService> list = rsOpPortServiceService.selectRsOpPortServiceList(rsOpPortService);
        return getDataTable(list);
    }

    /**
     * 导出码头服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opportservice:export')")
    @Log(title = "码头服务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsOpPortService rsOpPortService) {
        List<RsOpPortService> list = rsOpPortServiceService.selectRsOpPortServiceList(rsOpPortService);
        ExcelUtil<RsOpPortService> util = new ExcelUtil<RsOpPortService>(RsOpPortService.class);
        util.exportExcel(response, list, "码头服务数据");
    }

    /**
     * 获取码头服务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:opportservice:query')")
    @GetMapping(value = "/{portServiceId}")
    public AjaxResult getInfo(@PathVariable("portServiceId") Long portServiceId) {
        return AjaxResult.success(rsOpPortServiceService.selectRsOpPortServiceByPortServiceId(portServiceId));
    }

    /**
     * 新增码头服务
     */
    @PreAuthorize("@ss.hasPermi('system:opportservice:add')")
    @Log(title = "码头服务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsOpPortService rsOpPortService) {
        return toAjax(rsOpPortServiceService.insertRsOpPortService(rsOpPortService));
    }

    /**
     * 修改码头服务
     */
    @PreAuthorize("@ss.hasPermi('system:opportservice:edit')")
    @Log(title = "码头服务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsOpPortService rsOpPortService) {
        return toAjax(rsOpPortServiceService.updateRsOpPortService(rsOpPortService));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:opportservice:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsOpPortService rsOpPortService) {
        rsOpPortService.setUpdateBy(getUserId());
        return toAjax(rsOpPortServiceService.changeStatus(rsOpPortService));
    }

    /**
     * 删除码头服务
     */
    @PreAuthorize("@ss.hasPermi('system:opportservice:remove')")
    @Log(title = "码头服务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{portServiceIds}")
    public AjaxResult remove(@PathVariable Long[] portServiceIds) {
        return toAjax(rsOpPortServiceService.deleteRsOpPortServiceByPortServiceIds(portServiceIds));
    }
}
