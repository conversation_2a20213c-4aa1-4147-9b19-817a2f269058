export default {
  "panels": [{
    "index": 0,
    "name": 1,
    "height": 297,
    "width": 210,
    "paperHeader": -1.5,
    "paperFooter": 837,
    "printElements": [{
      "options": {
        "left": 28.5,
        "top": 10.5,
        "height": 45,
        "width": 118.5,
        "right": 153,
        "bottom": 57,
        "vCenter": 93.75,
        "hCenter": 34.5,
        "src": "/static/img/logo2.png",
        "fit": ""
      }, "printElementType": { "title": "图片", "type": "image" }
    }, {
      "options": {
        "left": 468,
        "top": 15,
        "height": 9.75,
        "width": 120,
        "title": " BILL OF LADING",
        "right": 588,
        "bottom": 24.75,
        "vCenter": 528,
        "hCenter": 19.875,
        "coordinateSync": false,
        "widthHeightSync": false,
        "fontSize": 12,
        "qrCodeLevel": 0
      }, "printElementType": { "title": "文本", "type": "text" }
    }, {
      "options": {
        "left": 150,
        "top": 30,
        "height": 9.75,
        "width": 211.5,
        "title": "广州瑞旗国际货运代理有限公司",
        "right": 279.75,
        "bottom": 36,
        "vCenter": 203.25,
        "hCenter": 31.125,
        "coordinateSync": false,
        "widthHeightSync": false,
        "fontSize": 11,
        "fontWeight": "bold",
        "qrCodeLevel": 0
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 148.5,
        "top": 43.5,
        "height": 9.75,
        "width": 283.5,
        "title": "GUANGZHOU RICH INT＇L SHIPPINGCO., LTD",
        "right": 552,
        "bottom": 81.75,
        "vCenter": 454.5,
        "hCenter": 76.875,
        "coordinateSync": false,
        "widthHeightSync": false,
        "fontWeight": "bold",
        "qrCodeLevel": 0,
        "fontSize": 10
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 468,
        "top": 54,
        "height": 87,
        "width": 9,
        "coordinateSync": false,
        "widthHeightSync": false
      }, "printElementType": { "title": "竖线", "type": "vline" }
    }, {
      "options": {
        "left": 340.5,
        "top": 54,
        "height": 362,
        "width": 9,
        "coordinateSync": false,
        "widthHeightSync": false
      }, "printElementType": { "title": "竖线", "type": "vline" }
    }, {
      "options": {
        "left": 28.5,
        "top": 55.5,
        "height": 9.75,
        "width": 120,
        "title": "Shipper",
        "right": 145.5,
        "bottom": 68.25,
        "vCenter": 85.5,
        "hCenter": 63.375,
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 345,
        "top": 57,
        "height": 9.75,
        "width": 109.5,
        "title": "bookingNo.",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 492,
        "bottom": 69,
        "vCenter": 432,
        "hCenter": 64.125
      }, "printElementType": { "title": "文本", "type": "text" }
    }, {
      "options": {
        "left": 471,
        "top": 57,
        "height": 9.75,
        "width": 109.5,
        "title": " MB/LNo.",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 594,
        "bottom": 66.75,
        "vCenter": 539.25,
        "hCenter": 61.875
      }, "printElementType": { "title": "文本", "type": "text" }
    }, {
      "options": {
        "left": 28.5,
        "top": 66,
        "height": 70,
        "width": 311,
        "field": "bookingShipper",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0,
        "right": 367,
        "bottom": 150.5,
        "vCenter": 198.5,
        "hCenter": 110.5,
        "style": {
          "0": "w",
          "1": "h",
          "2": "i",
          "3": "t",
          "4": "e",
          "5": "-",
          "6": "s",
          "7": "p",
          "8": "a",
          "9": "c",
          "10": "e",
          "11": ":",
          "12": " ",
          "13": "n",
          "14": "o",
          "15": "r",
          "16": "m",
          "17": "a",
          "18": "l",
          "19": ";",
          "20": " ",
          "21": "w",
          "22": "o",
          "23": "r",
          "24": "d",
          "25": "-",
          "26": "b",
          "27": "r",
          "28": "e",
          "29": "a",
          "30": "k",
          "31": ":",
          "32": " ",
          "33": "k",
          "34": "e",
          "35": "e",
          "36": "p",
          "37": "-",
          "38": "a",
          "39": "l",
          "40": "l",
          "41": ";",
          "42": " ",
          "43": "w",
          "44": "o",
          "45": "r",
          "46": "d",
          "47": "-",
          "48": "w",
          "49": "r",
          "50": "a",
          "51": "p",
          "52": ":",
          "53": " ",
          "54": "b",
          "55": "r",
          "56": "e",
          "57": "a",
          "58": "k",
          "59": "-",
          "60": "w",
          "61": "o",
          "62": "r",
          "63": "d",
          "64": ";"
        },
        "title": "这是更新后的元素"
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 471,
        "top": 72,
        "height": 9.75,
        "width": 110,
        "title": "这是更新后的元素",
        "field": "mblNo",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0,
        "right": 596.75,
        "bottom": 81.75,
        "vCenter": 541.75,
        "hCenter": 76.875
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 345,
        "top": 69,
        "height": 9.75,
        "width": 119,
        "title": "这是更新后的元素",
        "field": "bookingNo",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0,
        "right": 471.75,
        "bottom": 83.25,
        "vCenter": 421.5,
        "hCenter": 78.375
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 343.5,
        "top": 105,
        "height": 9,
        "width": 231,
        "coordinateSync": false,
        "widthHeightSync": false
      }, "printElementType": { "title": "横线", "type": "hline" }
    }, {
      "options": {
        "left": 471,
        "top": 108,
        "height": 9.75,
        "width": 109.5,
        "title": " HB/L No.",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 594,
        "bottom": 95.25,
        "vCenter": 539.25,
        "hCenter": 90.375
      }, "printElementType": { "title": "文本", "type": "text" }
    }, {
      "options": {
        "left": 345,
        "top": 108,
        "height": 9.75,
        "width": 109.5,
        "title": "Export References",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 480,
        "bottom": 96.75,
        "vCenter": 425.25,
        "hCenter": 91.875
      }, "printElementType": { "title": "文本", "type": "text" }
    }, {
      "options": {
        "left": 345,
        "top": 120,
        "height": 20,
        "width": 121,
        "title": "这是更新后的元素",
        "field": "exportReferences",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0,
        "right": 463.75,
        "bottom": 139.25,
        "vCenter": 403.25,
        "hCenter": 129.25
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 469.5,
        "top": 120,
        "height": 20,
        "width": 107,
        "title": "这是更新后的元素",
        "field": "rctNo",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0,
        "right": 578.75,
        "bottom": 140.25,
        "vCenter": 525.25,
        "hCenter": 129.75
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 21,
        "top": 141,
        "height": 9,
        "width": 555,
        "coordinateSync": false,
        "widthHeightSync": false
      }, "printElementType": { "title": "横线", "type": "hline" }
    }, {
      "options": {
        "left": 345,
        "top": 143.25,
        "height": 9.75,
        "width": 156,
        "title": "Forwarding Agent References",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 480.24609375,
        "bottom": 95.49609375,
        "vCenter": 425.49609375,
        "hCenter": 90.62109375
      }, "printElementType": { "title": "文本", "type": "text" }
    }, {
      "options": {
        "left": 28.5,
        "top": 144,
        "height": 9.75,
        "width": 120,
        "title": "Consignee",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 28.5,
        "top": 154.5,
        "height": 105,
        "width": 310,
        "field": "bookingConsignee",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0,
        "right": 338.5,
        "bottom": 233,
        "vCenter": 183.5,
        "hCenter": 193,
        "title": "这是更新后的元素"
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 345,
        "top": 156,
        "height": 68,
        "width": 192,
        "title": "这是更新后的元素",
        "field": "forwardingAgent",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0,
        "right": 538.5,
        "bottom": 223.25,
        "vCenter": 442.5,
        "hCenter": 189.25
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 19.5,
        "top": 262.5,
        "height": 9,
        "width": 556.5,
        "coordinateSync": false,
        "widthHeightSync": false,
        "borderWidth": "0.75"
      }, "printElementType": { "title": "横线", "type": "hline" }
    }, {
      "options": {
        "left": 28.5,
        "top": 264,
        "height": 9.75,
        "width": 184.5,
        "title": "Notify(Complete name and address)",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 213,
        "bottom": 273.75,
        "vCenter": 120.75,
        "hCenter": 268.875
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 348,
        "top": 265.5,
        "height": 9.75,
        "width": 109.5,
        "title": "B/L Remark No.1",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 483.75,
        "bottom": 269.25,
        "vCenter": 429,
        "hCenter": 264.375
      }, "printElementType": { "title": "文本", "type": "text" }
    }, {
      "options": {
        "left": 346.5,
        "top": 277.5,
        "height": 126,
        "width": 193.5,
        "title": "这是更新后的元素",
        "field": "blRemark",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0,
        "right": 567.75,
        "bottom": 385.5,
        "vCenter": 471,
        "hCenter": 338.25
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 30,
        "top": 274.5,
        "height": 60,
        "width": 310,
        "title": "文本",
        "field": "bookingNotifyParty",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0,
        "right": 340,
        "bottom": 294,
        "vCenter": 185,
        "hCenter": 268.5
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 25.5,
        "top": 336,
        "height": 9,
        "width": 313.5,
        "coordinateSync": false,
        "widthHeightSync": false
      }, "printElementType": { "title": "横线", "type": "hline" }
    }, {
      "options": {
        "left": 187.5,
        "top": 337.5,
        "height": 9.75,
        "width": 120,
        "title": " Place of receipt",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 307.5,
        "bottom": 314.25,
        "vCenter": 247.5,
        "hCenter": 309.375
      }, "printElementType": { "title": "文本", "type": "text" }
    }, {
      "options": {
        "left": 28.5,
        "top": 337.5,
        "height": 9.75,
        "width": 120,
        "title": "Pre-carriage by",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 142.74609375,
        "bottom": 308.49609375,
        "vCenter": 82.74609375,
        "hCenter": 303.62109375
      }, "printElementType": { "title": "文本", "type": "text" }
    }, {
      "options": {
        "left": 28.5,
        "top": 351,
        "height": 9.75,
        "width": 120,
        "title": "文本",
        "field": "preCarriage",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0,
        "right": 148.5,
        "bottom": 326.25,
        "vCenter": 88.5,
        "hCenter": 321.375
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 27,
        "top": 361.5,
        "height": 9,
        "width": 313.5,
        "coordinateSync": false,
        "widthHeightSync": false,
        "right": 340.74609375,
        "bottom": 336.99609375,
        "vCenter": 183.99609375,
        "hCenter": 332.49609375
      }, "printElementType": { "title": "横线", "type": "hline" }
    }, {
      "options": {
        "left": 189,
        "top": 366,
        "height": 9.75,
        "width": 120,
        "title": " Port of loading",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0
      }, "printElementType": { "title": "文本", "type": "text" }
    }, {
      "options": {
        "left": 28.5,
        "top": 366,
        "height": 9.75,
        "width": 120,
        "title": "Ocean Vessel",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 158.25,
        "bottom": 341.25,
        "vCenter": 98.25,
        "hCenter": 336.375
      }, "printElementType": { "title": "文本", "type": "text" }
    }, {
      "options": {
        "left": 22.5,
        "top": 379.5,
        "height": 9.75,
        "width": 163.5,
        "title": "文本",
        "field": "firstVessel",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0,
        "right": 149.25,
        "bottom": 354,
        "vCenter": 89.25,
        "hCenter": 349.125
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 189,
        "top": 379.5,
        "height": 9.75,
        "width": 120,
        "title": "这是更新后的元素",
        "field": "polName",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 28.5,
        "top": 390,
        "height": 9.75,
        "width": 120,
        "title": "Port of Discharge",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0
      }, "printElementType": { "title": "文本", "type": "text" }
    }, {
      "options": {
        "left": 189,
        "top": 391.5,
        "height": 9.75,
        "width": 120,
        "title": "DestinationPort",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0
      }, "printElementType": { "title": "文本", "type": "text" }
    }, {
      "options": {
        "left": 28.5,
        "top": 400.5,
        "height": 9.75,
        "width": 151.5,
        "title": "这是更新后的元素",
        "field": "podName",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 190.5,
        "top": 405,
        "height": 9.75,
        "width": 120,
        "title": "文本",
        "right": 298.5,
        "bottom": 436.5,
        "vCenter": 238.5,
        "hCenter": 431.625,
        "field": "destinationPort",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 21,
        "top": 417,
        "height": 9,
        "width": 552,
        "coordinateSync": false,
        "widthHeightSync": false,
        "right": 558.25,
        "bottom": 417.75,
        "vCenter": 289.25,
        "hCenter": 413.25,
        "borderWidth": "1.5"
      }, "printElementType": { "title": "横线", "type": "hline" }
    }, {
      "options": {
        "left": 213,
        "top": 418.5,
        "height": 9.75,
        "width": 235.5,
        "title": "PARTICULARS FURNISHED BY SHIPPER",
        "right": 412.5,
        "bottom": 428.25,
        "vCenter": 352.5,
        "hCenter": 423.375,
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "fontSize": 10,
        "fontWeight": "bold"
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 19.5,
        "top": 430.5,
        "height": 9,
        "width": 555,
        "right": 118.5,
        "bottom": 463.5,
        "vCenter": 73.5,
        "hCenter": 459,
        "coordinateSync": false,
        "widthHeightSync": false
      }, "printElementType": { "title": "横线", "type": "hline" }
    }, {
      "options": {
        "left": 388.5,
        "top": 430.5,
        "height": 180,
        "width": 9,
        "coordinateSync": false,
        "widthHeightSync": false,
        "right": 397.5,
        "bottom": 567.5,
        "vCenter": 393,
        "hCenter": 499
      }, "printElementType": { "title": "竖线", "type": "vline" }
    }, {
      "options": {
        "left": 480,
        "top": 430.5,
        "height": 180,
        "width": 9,
        "coordinateSync": false,
        "widthHeightSync": false,
        "right": 490.5,
        "bottom": 567.5,
        "vCenter": 486,
        "hCenter": 499
      }, "printElementType": { "title": "竖线", "type": "vline" }
    }, {
      "options": {
        "left": 142.5,
        "top": 430.5,
        "height": 180,
        "width": 9,
        "coordinateSync": false,
        "widthHeightSync": false,
        "right": 151.5,
        "bottom": 597.5,
        "vCenter": 147,
        "hCenter": 514
      }, "printElementType": { "title": "竖线", "type": "vline" }
    }, {
      "options": {
        "left": 228,
        "top": 430.5,
        "height": 180,
        "width": 9,
        "coordinateSync": false,
        "widthHeightSync": false,
        "right": 237,
        "bottom": 567.5,
        "vCenter": 232.5,
        "hCenter": 499
      }, "printElementType": { "title": "竖线", "type": "vline" }
    }, {
      "options": {
        "left": 33,
        "top": 435,
        "height": 9,
        "width": 102,
        "title": " Marks and numbers   ",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 129.5,
        "bottom": 503.25,
        "vCenter": 86.5,
        "hCenter": 498.375
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 147,
        "top": 436.5,
        "height": 9.75,
        "width": 60,
        "title": "Quantity ＆",
        "coordinateSync": false,
        "widthHeightSync": false,
        "textAlign": "center",
        "qrCodeLevel": 0,
        "right": 209.25,
        "bottom": 413.25,
        "vCenter": 179.25,
        "hCenter": 408.375
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 235.5,
        "top": 438,
        "height": 9.75,
        "width": 120,
        "title": "(Description Of Goods)",
        "right": 351,
        "bottom": 415.5,
        "vCenter": 291,
        "hCenter": 410.625,
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 397.5,
        "top": 439.5,
        "height": 9.75,
        "width": 70,
        "title": "(Gross Weight)",
        "coordinateSync": false,
        "widthHeightSync": false,
        "textAlign": "center",
        "qrCodeLevel": 0
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 483,
        "top": 439.5,
        "height": 9.75,
        "width": 80,
        "title": "(Measuremen)",
        "coordinateSync": false,
        "widthHeightSync": false,
        "textAlign": "center",
        "qrCodeLevel": 0,
        "right": 612,
        "bottom": 505.5,
        "vCenter": 552,
        "hCenter": 500.625
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 27.75,
        "top": 447,
        "height": 9,
        "width": 107,
        "title": "Container No./Seal No.",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 127.75,
        "bottom": 425.25,
        "vCenter": 80.75,
        "hCenter": 420.75
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 147,
        "top": 448.5,
        "height": 9.75,
        "width": 77,
        "title": "kind of packages",
        "coordinateSync": false,
        "widthHeightSync": false,
        "textAlign": "center",
        "qrCodeLevel": 0,
        "right": 220.5,
        "bottom": 424.5,
        "vCenter": 190.5,
        "hCenter": 419.625
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 229.5,
        "top": 450,
        "height": 9.75,
        "width": 160,
        "title": "(See Clause 12 if Dangerous Goods)",
        "right": 366.75,
        "bottom": 426,
        "vCenter": 297.75,
        "hCenter": 421.125,
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 27,
        "top": 462,
        "height": 9,
        "width": 538,
        "coordinateSync": false,
        "widthHeightSync": false
      }, "printElementType": { "title": "横线", "type": "hline" }
    }, {
      "options": {
        "left": 232.5,
        "top": 468,
        "height": 12,
        "width": 148.5,
        "title": "文本",
        "right": 377.25,
        "bottom": 459.75,
        "vCenter": 313.5,
        "hCenter": 454.875,
        "field": "goodsDescription",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 30,
        "top": 469.5,
        "height": 9.75,
        "width": 97.5,
        "title": "文本",
        "field": "shippingMark",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0,
        "textAlign": "center"
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 397.5,
        "top": 468,
        "height": 9.75,
        "width": 75,
        "title": "文本",
        "right": 513,
        "bottom": 534,
        "vCenter": 453,
        "hCenter": 529.125,
        "field": "grossWeight",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0,
        "textAlign": "center"
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 147,
        "top": 469.5,
        "height": 9.75,
        "width": 76.5,
        "title": "文本",
        "field": "packageQuantity",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0,
        "right": 200.25,
        "bottom": 456,
        "vCenter": 174,
        "hCenter": 451.125,
        "textAlign": "center"
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 484.5,
        "top": 466.5,
        "height": 9.75,
        "width": 76.5,
        "title": "文本",
        "field": "goodsVolume",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0,
        "textAlign": "center"
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 30,
        "top": 586.5,
        "height": 9.75,
        "width": 489,
        "title": "文本",
        "field": "goodsSummary",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0,
        "right": 519,
        "bottom": 551.25,
        "vCenter": 274.5,
        "hCenter": 546.375
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 226.5,
        "top": 610.5,
        "height": 41,
        "width": 9,
        "right": 235.5,
        "bottom": 659.25,
        "vCenter": 231,
        "hCenter": 614.25,
        "coordinateSync": false,
        "widthHeightSync": false
      }, "printElementType": { "title": "竖线", "type": "vline" }
    }, {
      "options": {
        "left": 366,
        "top": 610.5,
        "height": 41,
        "width": 9,
        "right": 378,
        "bottom": 664.5,
        "vCenter": 373.5,
        "hCenter": 637.5,
        "coordinateSync": false,
        "widthHeightSync": false
      }, "printElementType": { "title": "竖线", "type": "vline" }
    }, {
      "options": {
        "left": 298.5,
        "top": 610.5,
        "height": 41,
        "width": 9,
        "right": 308.25,
        "bottom": 657,
        "vCenter": 303.75,
        "hCenter": 612,
        "coordinateSync": false,
        "widthHeightSync": false
      }, "printElementType": { "title": "竖线", "type": "vline" }
    }, {
      "options": {
        "left": 156,
        "top": 610.5,
        "height": 41,
        "width": 9,
        "coordinateSync": false,
        "widthHeightSync": false
      }, "printElementType": { "title": "竖线", "type": "vline" }
    }, {
      "options": {
        "left": 453,
        "top": 610.5,
        "height": 41,
        "width": 9,
        "right": 462,
        "bottom": 655.5,
        "vCenter": 457.5,
        "hCenter": 610.5,
        "coordinateSync": false,
        "widthHeightSync": false
      }, "printElementType": { "title": "竖线", "type": "vline" }
    }, {
      "options": {
        "left": 22.5,
        "top": 610.5,
        "height": 9,
        "width": 551,
        "coordinateSync": false,
        "widthHeightSync": false
      }, "printElementType": { "title": "横线", "type": "hline" }
    }, {
      "options": {
        "left": 303,
        "top": 612,
        "height": 9.75,
        "width": 64,
        "title": "Currency",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 118.5,
        "bottom": 585,
        "vCenter": 74.25,
        "hCenter": 580.125
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 456,
        "top": 612,
        "height": 9.75,
        "width": 103.5,
        "title": "Collect",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 562.74609375,
        "bottom": 585.99609375,
        "vCenter": 510.99609375,
        "hCenter": 581.12109375
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 370.5,
        "top": 612,
        "height": 9.75,
        "width": 77,
        "title": " Prepaid",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 447.5,
        "bottom": 578.25,
        "vCenter": 409,
        "hCenter": 573.375
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 229.5,
        "top": 613.5,
        "height": 9.75,
        "width": 64.5,
        "title": "Unit",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 118.5,
        "bottom": 585,
        "vCenter": 74.25,
        "hCenter": 580.125
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 159,
        "top": 613.5,
        "height": 9.75,
        "width": 63,
        "title": "Rate",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 118.5,
        "bottom": 585,
        "vCenter": 74.25,
        "hCenter": 580.125
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 30,
        "top": 615,
        "height": 9.75,
        "width": 103.5,
        "title": "Freight ＆ Charges",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 133.5,
        "bottom": 579.75,
        "vCenter": 81.75,
        "hCenter": 574.875
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 30,
        "top": 627,
        "height": 20,
        "width": 124.5,
        "title": "文本",
        "field": "payWay",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0,
        "right": 115.5,
        "bottom": 597.75,
        "vCenter": 71.25,
        "hCenter": 592.875
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 276,
        "top": 651,
        "height": 131,
        "width": 9,
        "coordinateSync": false,
        "widthHeightSync": false,
        "right": 277.74609375,
        "bottom": 784.49609375,
        "vCenter": 273.24609375,
        "hCenter": 702.49609375
      }, "printElementType": { "title": "竖线", "type": "vline" }
    }, {
      "options": {
        "left": 171,
        "top": 651,
        "height": 130,
        "width": 9,
        "coordinateSync": false,
        "widthHeightSync": false,
        "right": 180.75,
        "bottom": 788,
        "vCenter": 176.25,
        "hCenter": 706
      }, "printElementType": { "title": "竖线", "type": "vline" }
    }, {
      "options": {
        "left": 279,
        "top": 666,
        "height": 9.75,
        "width": 310.5,
        "title": "RECEIVED inexternal apparent good order and condition except as",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 447.5,
        "bottom": 578.25,
        "vCenter": 409,
        "hCenter": 573.375
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 28.5,
        "top": 655.5,
        "height": 9.75,
        "width": 143,
        "title": " Carrier's Recipt.Total number of",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 171.5,
        "bottom": 635.25,
        "vCenter": 100,
        "hCenter": 630.375
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 174,
        "top": 657,
        "height": 9.75,
        "width": 96,
        "title": "Place of Issue of B/L",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 253.5,
        "bottom": 636.75,
        "vCenter": 201.75,
        "hCenter": 631.875
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 279,
        "top": 678,
        "height": 9.75,
        "width": 310.5,
        "title": "therwise noted.The total number of packages or units stuffed in the",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 581.25,
        "bottom": 644.25,
        "vCenter": 426,
        "hCenter": 639.375
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 28.5,
        "top": 666,
        "height": 9.75,
        "width": 142,
        "title": "Container or packages received",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 152.75,
        "bottom": 643.5,
        "vCenter": 91.75,
        "hCenter": 638.625
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 178.5,
        "top": 672,
        "height": 27,
        "width": 88.5,
        "title": "文本",
        "field": "issuePlace",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0,
        "right": 262.5,
        "bottom": 714,
        "vCenter": 218.25,
        "hCenter": 700.5
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 28,
        "top": 676,
        "height": 9.75,
        "width": 142,
        "title": "by Carrier.",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 152.75,
        "bottom": 643.5,
        "vCenter": 91.75,
        "hCenter": 638.625
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 279,
        "top": 690,
        "height": 9.75,
        "width": 319.5,
        "title": "and the weights shown in this Bill of Lading are furnish by the Merchants,",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 580.5,
        "bottom": 657.75,
        "vCenter": 425.25,
        "hCenter": 652.875
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 279,
        "top": 702,
        "height": 9.75,
        "width": 310.5,
        "title": " and which the carrier has no reasonable means of checking and is not ",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 580.5,
        "bottom": 669.75,
        "vCenter": 425.25,
        "hCenter": 664.875
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 28.5,
        "top": 687,
        "height": 27,
        "width": 136.5,
        "title": "文本",
        "field": "carrier",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0,
        "right": 117,
        "bottom": 672,
        "vCenter": 72.75,
        "hCenter": 667.125
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 279,
        "top": 714,
        "height": 9.75,
        "width": 321,
        "title": "apart of contract.The carrier has issued the number Bills of Lading ",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 580.5,
        "bottom": 680.25,
        "vCenter": 425.25,
        "hCenter": 675.375
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 279,
        "top": 726,
        "height": 9.75,
        "width": 310.5,
        "title": "stated below,all of against the delivery of shipment and whereupon any ",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 577.5,
        "bottom": 692.25,
        "vCenter": 422.25,
        "hCenter": 687.375
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 24,
        "top": 715.5,
        "height": 9,
        "width": 252,
        "coordinateSync": false,
        "widthHeightSync": false
      }, "printElementType": { "title": "横线", "type": "hline" }
    }, {
      "options": {
        "left": 172.5,
        "top": 718.5,
        "height": 9.75,
        "width": 96,
        "title": "Date of Issue of B/L",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 269.25,
        "bottom": 706.5,
        "vCenter": 221.25,
        "hCenter": 701.625
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 279,
        "top": 738,
        "height": 9.75,
        "width": 310.5,
        "title": "other Original Bills of Lading shall be void.The Merchants agree to be ",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 580.5,
        "bottom": 707.25,
        "vCenter": 425.25,
        "hCenter": 702.375
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 25.5,
        "top": 718.5,
        "height": 9.75,
        "width": 143,
        "title": "Number of original B(s)/L",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 170,
        "bottom": 707.25,
        "vCenter": 98.5,
        "hCenter": 702.375
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 279,
        "top": 750.75,
        "height": 9.75,
        "width": 310.5,
        "title": "bound by the terms and as if each had personally signed this ",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 580.5,
        "bottom": 680.25,
        "vCenter": 425.25,
        "hCenter": 675.375
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 177,
        "top": 733.5,
        "height": 9.75,
        "width": 88.5,
        "title": "文本",
        "field": "issueDate",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0,
        "right": 262.5,
        "bottom": 724.5,
        "vCenter": 218.25,
        "hCenter": 719.625
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 27,
        "top": 733.5,
        "height": 9.75,
        "width": 136.5,
        "title": "文本",
        "field": "blNumbers",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0,
        "right": 163.5,
        "bottom": 728.25,
        "vCenter": 95.25,
        "hCenter": 723.375
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 279,
        "top": 763.5,
        "height": 9.75,
        "width": 310.5,
        "title": "Bill of Lading",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 580.74609375,
        "bottom": 731.49609375,
        "vCenter": 425.49609375,
        "hCenter": 726.62109375
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 171,
        "top": 747,
        "height": 9.75,
        "width": 102,
        "title": "Shipped on Board Date ",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 274.5,
        "bottom": 745.5,
        "vCenter": 223.5,
        "hCenter": 740.625
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 25.5,
        "top": 748.5,
        "height": 9.75,
        "width": 143,
        "title": "Declared Value",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 170,
        "bottom": 759,
        "vCenter": 98.5,
        "hCenter": 754.125
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 172.5,
        "top": 757.5,
        "height": 9.75,
        "width": 102,
        "title": "(Local time)",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 276.75,
        "bottom": 759,
        "vCenter": 225.75,
        "hCenter": 754.125
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 177,
        "top": 768,
        "height": 9.75,
        "width": 88.5,
        "title": "文本",
        "field": "onBoardDate",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0,
        "right": 263.25,
        "bottom": 770.25,
        "vCenter": 219,
        "hCenter": 765.375
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 28.5,
        "top": 768,
        "height": 9.75,
        "width": 136.5,
        "title": "文本",
        "field": "declaredValue",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0,
        "right": 117,
        "bottom": 770.25,
        "vCenter": 72.75,
        "hCenter": 765.375
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 25.5,
        "top": 780,
        "height": 25.5,
        "width": 48,
        "title": "TOTAL:",
        "right": 111,
        "bottom": 803.25,
        "vCenter": 66.75,
        "hCenter": 790.5,
        "coordinateSync": false,
        "widthHeightSync": false,
        "fontSize": 9,
        "fontWeight": "bold",
        "textAlign": "center",
        "textContentVerticalAlign": "middle",
        "qrCodeLevel": 0
      }, "printElementType": { "title": "文本", "type": "text" }
    }, {
      "options": {
        "left": 22.5,
        "top": 780,
        "height": 9,
        "width": 253,
        "coordinateSync": false,
        "widthHeightSync": false,
        "right": 266.5,
        "bottom": 786,
        "vCenter": 143,
        "hCenter": 781.5
      }, "printElementType": { "title": "横线", "type": "hline" }
    }, {
      "options": {
        "left": 357,
        "top": 780,
        "height": 9.75,
        "width": 208.5,
        "title": "GUANGZHOU RICH SHIPPING INT'LCO.,LTD.",
        "coordinateSync": false,
        "widthHeightSync": false,
        "fontWeight": "bold",
        "qrCodeLevel": 0,
        "right": 566.25,
        "bottom": 785.25,
        "vCenter": 462,
        "hCenter": 780.375
      }, "printElementType": { "title": "文本", "type": "text" }
    }, {
      "options": {
        "left": 354,
        "top": 793.5,
        "height": 9,
        "width": 212,
        "coordinateSync": false,
        "widthHeightSync": false
      }, "printElementType": { "title": "横线", "type": "hline" }
    }, {
      "options": {
        "left": 396.75,
        "top": 794.25,
        "height": 9.75,
        "width": 143,
        "title": "As Agent(s) for the carrier",
        "coordinateSync": false,
        "widthHeightSync": false,
        "qrCodeLevel": 0,
        "right": 170,
        "bottom": 707.25,
        "vCenter": 98.5,
        "hCenter": 702.375
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 21,
        "top": 805.5,
        "height": 9,
        "width": 555,
        "coordinateSync": false,
        "widthHeightSync": false,
        "borderWidth": "1.5"
      }, "printElementType": { "title": "横线", "type": "hline" }
    }, {
      "options": {
        "left": 25.5,
        "top": 390,
        "height": 9,
        "width": 313.5,
        "coordinateSync": false,
        "widthHeightSync": false,
        "right": 342.75,
        "bottom": 364.5,
        "vCenter": 186,
        "hCenter": 360
      }, "printElementType": { "title": "横线", "type": "hline" }
    }, {
      "options": {
        "left": 184.5,
        "top": 336,
        "height": 81,
        "width": 9,
        "coordinateSync": false,
        "widthHeightSync": false
      }, "printElementType": { "title": "竖线", "type": "vline" }
    }, {
      "options": {
        "left": 28.5,
        "top": 492,
        "height": 9.75,
        "width": 99,
        "title": "文本",
        "field": "logisticsTerms",
        "coordinateSync": false,
        "widthHeightSync": false,
        "hideTitle": true,
        "qrCodeLevel": 0,
        "right": 116.25,
        "bottom": 471,
        "vCenter": 72,
        "hCenter": 466.125,
        "textAlign": "center"
      }, "printElementType": { "title": "这是更新后的元素", "type": "text" }
    }, {
      "options": {
        "left": 135,
        "top": 781.5,
        "height": 25.5,
        "width": 120,
        "title": "CONTAINER(S)ONLY",
        "right": 321.75,
        "bottom": 805.5,
        "vCenter": 261.75,
        "hCenter": 792.75,
        "coordinateSync": false,
        "widthHeightSync": false,
        "fontSize": 9,
        "fontWeight": "bold",
        "textContentVerticalAlign": "middle",
        "qrCodeLevel": 0
      }, "printElementType": { "title": "文本", "type": "text" }
    }, {
      "options": {
        "left": 73.5,
        "top": 781.5,
        "height": 25.5,
        "width": 60,
        "title": "ONE(FCL)",
        "right": 123.24609375,
        "bottom": 807.24609375,
        "vCenter": 99.24609375,
        "hCenter": 794.49609375,
        "coordinateSync": false,
        "widthHeightSync": false,
        "fontSize": 9,
        "fontWeight": "bold",
        "textAlign": "center",
        "textContentVerticalAlign": "middle",
        "qrCodeLevel": 0,
        "field": "containerQuantity",
        "hideTitle": true
      }, "printElementType": { "title": "文本", "type": "text" }
    }, {
      "options": {
        "left": 24,
        "top": 745.5,
        "height": 9,
        "width": 252,
        "coordinateSync": false,
        "widthHeightSync": false,
        "right": 275.25,
        "bottom": 747.75,
        "vCenter": 149.25,
        "hCenter": 743.25
      }, "printElementType": { "title": "横线", "type": "hline" }
    }, {
      "options": {
        "left": 21,
        "top": 651,
        "height": 9,
        "width": 551,
        "coordinateSync": false,
        "widthHeightSync": false,
        "right": 758.75,
        "bottom": 664.5,
        "vCenter": 483.25,
        "hCenter": 660
      }, "printElementType": { "title": "横线", "type": "hline" }
    }],
    "paperNumberLeft": 565.5,
    "paperNumberTop": 819,
    "watermarkOptions": {}
  }]
}
