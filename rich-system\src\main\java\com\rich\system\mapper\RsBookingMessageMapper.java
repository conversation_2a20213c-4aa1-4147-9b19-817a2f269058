package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsBookingMessage;
import org.apache.ibatis.annotations.Mapper;

/**
 * 记录提单信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@Mapper
public interface RsBookingMessageMapper {
    /**
     * 查询记录提单信息
     *
     * @param rsBookingMessageId 记录提单信息主键
     * @return 记录提单信息
     */
    RsBookingMessage selectRsBookingMessageByRsBookingMessageId(Long rsBookingMessageId);

    /**
     * 查询记录提单信息列表
     *
     * @param rsBookingMessage 记录提单信息
     * @return 记录提单信息集合
     */
    List<RsBookingMessage> selectRsBookingMessageList(RsBookingMessage rsBookingMessage);

    /**
     * 新增记录提单信息
     *
     * @param rsBookingMessage 记录提单信息
     * @return 结果
     */
    int insertRsBookingMessage(RsBookingMessage rsBookingMessage);

    /**
     * 修改记录提单信息
     *
     * @param rsBookingMessage 记录提单信息
     * @return 结果
     */
    int updateRsBookingMessage(RsBookingMessage rsBookingMessage);

    /**
     * 删除记录提单信息
     *
     * @param rsBookingMessageId 记录提单信息主键
     * @return 结果
     */
    int deleteRsBookingMessageByRsBookingMessageId(Long rsBookingMessageId);

    /**
     * 批量删除记录提单信息
     *
     * @param rsBookingMessageIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsBookingMessageByRsBookingMessageIds(Long[] rsBookingMessageIds);

    void deleteRsBookingMessageByRctId(Long rctId);
}
