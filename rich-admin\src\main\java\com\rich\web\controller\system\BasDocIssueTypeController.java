package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasDocIssueType;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasDocIssueTypeService;
import com.rich.system.service.impl.RedisCacheImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 文件出单方式Controller
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@RestController
@RequestMapping("/system/docissuetype")
public class BasDocIssueTypeController extends BaseController {
    @Autowired
    private BasDocIssueTypeService basDocIssueTypeService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisCacheImpl RedisCache;

    /**
     * 查询文件出单方式列表
     */
    @PreAuthorize("@ss.hasPermi('system:docissuetype:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasDocIssueType basDocIssueType) {
        startPage();
        List<BasDocIssueType> list = basDocIssueTypeService.selectBasDocIssueTypeList(basDocIssueType);
        return getDataTable(list);
    }

    @GetMapping("/selectList")
    public AjaxResult selectList() {
        List<BasDocIssueType> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "docIssueType");
        if (list == null) {
            RedisCache.docIssueType();
            list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "docIssueType");
        }
        return AjaxResult.success(list);
    }

    /**
     * 导出文件出单方式列表
     */
    @PreAuthorize("@ss.hasPermi('system:docissuetype:export')")
    @Log(title = "文件出单方式", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasDocIssueType basDocIssueType) {
        List<BasDocIssueType> list = basDocIssueTypeService.selectBasDocIssueTypeList(basDocIssueType);
        ExcelUtil<BasDocIssueType> util = new ExcelUtil<BasDocIssueType>(BasDocIssueType.class);
        util.exportExcel(response, list, "文件出单方式数据");
    }

    /**
     * 获取文件出单方式详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:docissuetype:query')")
    @GetMapping(value = "/{issueTypeId}")
    public AjaxResult getInfo(@PathVariable("issueTypeId") Long issueTypeId) {
        return AjaxResult.success(basDocIssueTypeService.selectBasDocIssueTypeByIssueTypeId(issueTypeId));
    }

    /**
     * 新增文件出单方式
     */
    @PreAuthorize("@ss.hasPermi('system:docissuetype:add')")
    @Log(title = "文件出单方式", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasDocIssueType basDocIssueType) {
        return toAjax(basDocIssueTypeService.insertBasDocIssueType(basDocIssueType));
    }

    /**
     * 修改文件出单方式
     */
    @PreAuthorize("@ss.hasPermi('system:docissuetype:edit')")
    @Log(title = "文件出单方式", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasDocIssueType basDocIssueType) {
        return toAjax(basDocIssueTypeService.updateBasDocIssueType(basDocIssueType));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:docissuetype:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasDocIssueType basDocIssueType) {
        basDocIssueType.setUpdateBy(getUserId());
        return toAjax(basDocIssueTypeService.changeStatus(basDocIssueType));
    }

    /**
     * 删除文件出单方式
     */
    @PreAuthorize("@ss.hasPermi('system:docissuetype:remove')")
    @Log(title = "文件出单方式", businessType = BusinessType.DELETE)
    @DeleteMapping("/{issueTypeIds}")
    public AjaxResult remove(@PathVariable Long[] issueTypeIds) {
        return toAjax(basDocIssueTypeService.deleteBasDocIssueTypeByIssueTypeIds(issueTypeIds));
    }
}
