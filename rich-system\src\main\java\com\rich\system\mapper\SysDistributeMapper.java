package com.rich.system.mapper;

import com.rich.common.core.domain.entity.SysDistribute;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 权限分配Mapper接口
 *
 * <AUTHOR>
 * @date 2023-02-13
 */
@Mapper
public interface SysDistributeMapper {
    /**
     * 查询权限分配
     *
     * @param distributeId 权限分配主键
     * @return 权限分配
     */
    SysDistribute selectSysDistributeByDistributeId(Long distributeId);

    /**
     * 查询权限分配列表
     *
     * @param sysDistribute 权限分配
     * @return 权限分配集合
     */
    List<SysDistribute> selectSysDistributeList(SysDistribute sysDistribute);

    /**
     * 新增权限分配
     *
     * @param sysDistribute 权限分配
     * @return 结果
     */
    int insertSysDistribute(SysDistribute sysDistribute);

    /**
     * 修改权限分配
     *
     * @param sysDistribute 权限分配
     * @return 结果
     */
    int updateSysDistribute(SysDistribute sysDistribute);

    /**
     * 删除权限分配
     *
     * @param distributeId 权限分配主键
     * @return 结果
     */
    int deleteSysDistributeByDistributeId(Long distributeId);

    /**
     * 批量删除权限分配
     *
     * @param distributeIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteSysDistributeByDistributeIds(Long[] distributeIds);
}
