package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.ArrayList;
import java.util.List;

/**
 * 【请填写功能名称】对象 bas_dist_location
 *
 * <AUTHOR>
 * @date 2022-08-15
 */
public class BasDistLocation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Excel(name = "地区ID")
    private Long locationId;

    /**
     * 地区父ID
     */
    @Excel(name = "地区父ID")
    private Long parentId;

    /**
     * 祖籍列表
     */
    @Excel(name = "祖籍列表")
    private String ancestors;

    /**
     * 简称
     */
    @Excel(name = "中文简称")
    private String locationShortName;
    @Excel(name = "英文简称")
    private String locationEnShortName;

    /**
     * 地区名
     */
    @Excel(name = "地区名")
    private String locationLocalName;

    /**
     * 英文名
     */
    @Excel(name = "英文名")
    private String locationEnName;

    /**
     * 地区代码
     */
    @Excel(name = "地区代码")
    private String locationCode;

    /**
     * 国家代码
     */
    @Excel(name = "国家代码")
    private String nationCode;

    /**
     * 电话区号
     */
    @Excel(name = "电话区号")
    private String locationPhoneCode;

    /**
     * 地区级别
     */
    @Excel(name = "地区级别")
    private Long locationLevel;

    /**
     * 地区知名度
     */
    @Excel(name = "地区知名度")
    private Long locationPopularity;

    /**
     * 地区展示优先级
     */
    @Excel(name = "地区展示优先级")
    private Long locationShowpriority;

    /**
     * 父部门名称
     */
    private String parentName;

    @Excel(name = "是否港口")
    private String isPort;
    private String isIataPort;
    private String isRailPort;

    private String status;

    @Excel(name = "港口类型")
    private Long portTypeId;

    @Excel(name = "海运代码")
    private String portCode;
    @Excel(name = "空运代码")
    private String portIataCode;

    @Excel(name = "铁路代码")
    private String portRailCode;

    @Excel(name = "航线ID")
    private Long lineId;

    private String lineAncestors;

    @Excel(name = "航线名称")
    private String lineName;

    private BasDistLine line;

    private BasPortType portType;

    private String hasChildren;

    private Integer orderNum;

    private String locationQuery;

    private Long[] locationSelectList;

    public String getIsIataPort() {
        return isIataPort;
    }

    public void setIsIataPort(String isIataPort) {
        this.isIataPort = isIataPort;
    }

    public String getIsRailPort() {
        return isRailPort;
    }

    public void setIsRailPort(String isRailPort) {
        this.isRailPort = isRailPort;
    }

    public String getLocationEnShortName() {
        return locationEnShortName;
    }

    public void setLocationEnShortName(String locationEnShortName) {
        this.locationEnShortName = locationEnShortName;
    }

    public Long[] getLocationSelectList() {
        return locationSelectList;
    }

    public void setLocationSelectList(Long[] locationSelectList) {
        this.locationSelectList = locationSelectList;
    }

    public String getNationCode() {
        return nationCode;
    }

    public void setNationCode(String nationCode) {
        this.nationCode = nationCode;
    }

    public String getLineAncestors() {
        return lineAncestors;
    }

    public void setLineAncestors(String lineAncestors) {
        this.lineAncestors = lineAncestors;
    }

    public String getLocationQuery() {
        return locationQuery;
    }

    public void setLocationQuery(String locationQuery) {
        this.locationQuery = locationQuery;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getHasChildren() {
        return hasChildren;
    }

    public void setHasChildren(String hasChildren) {
        this.hasChildren = hasChildren;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    private List<BasDistLocation> children = new ArrayList<>();

    public BasDistLine getLine() {
        return line;
    }

    public void setLine(BasDistLine line) {
        this.line = line;
    }

    public BasPortType getPortType() {
        return portType;
    }

    public void setPortType(BasPortType portType) {
        this.portType = portType;
    }

    public Long getPortTypeId() {
        return portTypeId;
    }

    public void setPortTypeId(Long portTypeId) {
        this.portTypeId = portTypeId;
    }

    public String getPortCode() {
        return portCode;
    }

    public void setPortCode(String portCode) {
        this.portCode = portCode;
    }

    public String getPortIataCode() {
        return portIataCode;
    }

    public void setPortIataCode(String portIataCode) {
        this.portIataCode = portIataCode;
    }

    public String getPortRailCode() {
        return portRailCode;
    }

    public void setPortRailCode(String portRailCode) {
        this.portRailCode = portRailCode;
    }

    public Long getLineId() {
        return lineId;
    }

    public void setLineId(Long lineId) {
        this.lineId = lineId;
    }

    public String getIsPort() {
        return isPort;
    }

    public void setIsPort(String isPort) {
        this.isPort = isPort;
    }


    public BasDistLocation() {
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public List<BasDistLocation> getChildren() {
        return children;
    }

    public void setChildren(List<BasDistLocation> children) {
        this.children = children;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }

    public String getAncestors() {
        return ancestors;
    }

    public void setLocationShortName(String locationShortName) {
        this.locationShortName = locationShortName;
    }

    public String getLocationShortName() {
        return locationShortName;
    }

    public void setLocationLocalName(String locationLocalName) {
        this.locationLocalName = locationLocalName;
    }

    public String getLocationLocalName() {
        return locationLocalName;
    }

    public void setLocationEnName(String locationEnName) {
        this.locationEnName = locationEnName;
    }

    public String getLocationEnName() {
        return locationEnName;
    }

    public void setLocationCode(String locationCode) {
        this.locationCode = locationCode;
    }

    public String getLocationCode() {
        return locationCode;
    }

    public void setLocationPhoneCode(String locationPhoneCode) {
        this.locationPhoneCode = locationPhoneCode;
    }

    public String getLocationPhoneCode() {
        return locationPhoneCode;
    }

    public void setLocationLevel(Long locationLevel) {
        this.locationLevel = locationLevel;
    }

    public Long getLocationLevel() {
        return locationLevel;
    }

    public void setLocationPopularity(Long locationPopularity) {
        this.locationPopularity = locationPopularity;
    }

    public Long getLocationPopularity() {
        return locationPopularity;
    }

    public void setLocationShowpriority(Long locationShowpriority) {
        this.locationShowpriority = locationShowpriority;
    }

    public Long getLocationShowpriority() {
        return locationShowpriority;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("locationId", getLocationId())
                .append("parentId", getParentId())
                .append("ancestors", getAncestors())
                .append("locationShortName", getLocationShortName())
                .append("locationLocalName", getLocationLocalName())
                .append("locationEnName", getLocationEnName())
                .append("locationCode", getLocationCode())
                .append("locationPhoneCode", getLocationPhoneCode())
                .append("locationLevel", getLocationLevel())
                .append("locationPopularity", getLocationPopularity())
                .append("locationShowpriority", getLocationShowpriority())
                .toString();
    }
}
