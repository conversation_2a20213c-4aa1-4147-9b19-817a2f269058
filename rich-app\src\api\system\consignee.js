import request from "@/utils/request";

// 获取收货人列表
export function getConsigneeList(query) {
    return request({
        url: "/mp/warehouseconsignee/list",
        method: "get",
        params: query,
    });
}

// 新增收货人
export function addConsignee(data) {
    return request({
        url: "/mp/warehouseconsignee",
        method: "post",
        data: data,
    });
}

// 查询收货人详情
export function getConsignee(id) {
    return request({
        url: "/mp/warehouseconsignee/" + id,
        method: "get",
    });
}

// 收货人代码查重
export function checkConsigneeCode(consigneeCode) {
    return request({
        url: "/mp/warehouseconsignee/checkConsigneeCode/" + consigneeCode,
        method: "get",
    });
}

// 修改收货人
export function updateConsignee(data) {
    return request({
        url: "/mp/warehouseconsignee",
        method: "put",
        data: data,
    });
}

// 删除收货人
export function delConsignee(id) {
    return request({
        url: "/mp/warehouseconsignee/" + id,
        method: "delete",
    });
}

// 获取当前用户是否是客户或者已绑定收货人
export function getConsigneeBuUser(id) {
    return request({
        url: "/mp/warehouseconsignee/client/" + id,
        method: "get",
    });
}

// 扫码绑定收货人
export function scanBindConsignee(consigneeId, userId) {
    return request({
        url: "/mp/warehouseconsignee/bind/" + consigneeId + "/" + userId,
        method: "get",
    });
}
