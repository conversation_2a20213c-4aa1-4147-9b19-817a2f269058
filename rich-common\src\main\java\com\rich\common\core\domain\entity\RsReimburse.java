package com.rich.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 报销记录对象 rs_reimburse
 *
 * <AUTHOR>
 * @date 2023-03-30
 */
public class RsReimburse extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 报销ID
     */
    private Long reimburseId;

    /**
     * 报销人
     */
    @Excel(name = "报销人")
    private Long staffId;
    @Excel(name = "报销人", sort = 4)
    private String staffName;

    /**
     * 报销概要
     */
    @Excel(name = "报销概要")
    private String reimburseTitle;

    /**
     * 报销详情
     */
    @Excel(name = "报销详情", sort = 7)
    private String reimburseContent;

    /**
     * 报销金额
     */
    @Excel(name = "报销金额", sort = 8, scale = 2)
    private BigDecimal reimbursePrice;

    /**
     * 报销附件
     */
    @Excel(name = "报销附件")
    private String reimburseAppendix;

    /**
     * 参与人员
     */
    @Excel(name = "参与人员")
    private String reimburseParticipation;

    /**
     * 报销日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "报销日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", sort = 2)
    private Date happenDate;

    /**
     * 归属日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date belongDate;

    /**
     * 申请日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "申请日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", sort = 1)
    private Date applyDate;

    /**
     * 费用类型
     */
    @Excel(name = "费用类型")
    private Long chargeTypeId;
    @Excel(name = "费用细则", sort = 6)
    private String chargeTypeName;
    @Excel(name = "费用类目", sort = 5)
    private String chargeTypeSubName;

    /**
     * 部门审批
     */
    @Excel(name = "部门审批")
    private String deptConfirmed;

    /**
     * 确认人ID
     */
    @Excel(name = "确认人ID")
    private Long deptConfirmedId;

    private String deptConfirmedName;

    /**
     * 部门审批时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "部门审批时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date deptConfirmedDate;

    /**
     * 部门审批
     */
    @Excel(name = "人事审批")
    private String hrConfirmed;

    /**
     * 确认人ID
     */
    @Excel(name = "确认人ID")
    private Long hrConfirmedId;

    private String hrConfirmedName;
    private boolean reimbursed;

    public boolean isReimbursed() {
        return reimbursed;
    }

    public void setReimbursed(boolean reimbursed) {
        this.reimbursed = reimbursed;
    }

    /**
     * 部门审批时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "人事审批时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date hrConfirmedDate;

    /**
     * 财务确认
     */
    @Excel(name = "财务确认")
    private String financeConfirmed;

    /**
     * 确认人ID
     */
    @Excel(name = "确认人ID")
    private Long financeConfirmedId;
    private String financeConfirmedName;

    /**
     * 财务确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "财务确认时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date financeConfirmedDate;

    /**
     * 总经办审批
     */
    @Excel(name = "总经办审批")
    private String ceoConfirmed;

    /**
     * 确认人ID
     */
    @Excel(name = "确认人ID")
    private Long ceoConfirmedId;

    private String ceoConfirmedName;

    /**
     * 总经办审批时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "总经办审批时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date ceoConfirmedDate;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;

    private MidReimburseConfirm deptReimburseConfirm;
    private MidReimburseConfirm hrReimburseConfirm;
    private MidReimburseConfirm ceoReimburseConfirm;
    private MidReimburseConfirm financeReimburseConfirm;
    @Excel(name = "实际报销金额", sort = 8, scale = 2)
    private BigDecimal actualReimbursePrice;

    private Long[] permissionLevel;
    private Long deptId;
    private String relationRct;
    private Long bankRecordId;
    @Excel(name = "所属部门", sort = 3)
    private String deptName;
    private String sqdRaletiveRctList;
    @Excel(name = "归属日期", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String belongMonth;
    private String companyBelongsTo;

    private int type;


    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getCompanyBelongsTo() {
        return companyBelongsTo;
    }

    public void setCompanyBelongsTo(String companyBelongsTo) {
        this.companyBelongsTo = companyBelongsTo;
    }

    public String getBelongMonth() {
        return belongMonth;
    }

    public void setBelongMonth(String belongMonth) {
        this.belongMonth = belongMonth;
    }

    public String getChargeTypeSubName() {
        return chargeTypeSubName;
    }

    public void setChargeTypeSubName(String chargeTypeSubName) {
        this.chargeTypeSubName = chargeTypeSubName;
    }

    public String getSqdRaletiveRctList() {
        return sqdRaletiveRctList;
    }

    public void setSqdRaletiveRctList(String sqdRaletiveRctList) {
        this.sqdRaletiveRctList = sqdRaletiveRctList;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public Long getBankRecordId() {
        return bankRecordId;
    }

    public void setBankRecordId(Long bankRecordId) {
        this.bankRecordId = bankRecordId;
    }

    public String getRelationRct() {
        return relationRct;
    }

    public void setRelationRct(String relationRct) {
        this.relationRct = relationRct;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    @Override
    public Long[] getPermissionLevel() {
        return permissionLevel;
    }

    @Override
    public void setPermissionLevel(Long[] permissionLevel) {
        this.permissionLevel = permissionLevel;
    }

    public BigDecimal getActualReimbursePrice() {
        return actualReimbursePrice;
    }

    public void setActualReimbursePrice(BigDecimal actualReimbursePrice) {
        this.actualReimbursePrice = actualReimbursePrice;
    }

    public MidReimburseConfirm getDeptReimburseConfirm() {
        return deptReimburseConfirm;
    }

    public void setDeptReimburseConfirm(MidReimburseConfirm deptReimburseConfirm) {
        this.deptReimburseConfirm = deptReimburseConfirm;
    }

    public MidReimburseConfirm getHrReimburseConfirm() {
        return hrReimburseConfirm;
    }

    public void setHrReimburseConfirm(MidReimburseConfirm hrReimburseConfirm) {
        this.hrReimburseConfirm = hrReimburseConfirm;
    }

    public MidReimburseConfirm getCeoReimburseConfirm() {
        return ceoReimburseConfirm;
    }

    public void setCeoReimburseConfirm(MidReimburseConfirm ceoReimburseConfirm) {
        this.ceoReimburseConfirm = ceoReimburseConfirm;
    }

    public MidReimburseConfirm getFinanceReimburseConfirm() {
        return financeReimburseConfirm;
    }

    public void setFinanceReimburseConfirm(MidReimburseConfirm financeReimburseConfirm) {
        this.financeReimburseConfirm = financeReimburseConfirm;
    }

    public String getChargeTypeName() {
        return chargeTypeName;
    }

    public void setChargeTypeName(String chargeTypeName) {
        this.chargeTypeName = chargeTypeName;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public String getDeptConfirmedName() {
        return deptConfirmedName;
    }

    public void setDeptConfirmedName(String deptConfirmedName) {
        this.deptConfirmedName = deptConfirmedName;
    }

    public String getHrConfirmedName() {
        return hrConfirmedName;
    }

    public void setHrConfirmedName(String hrConfirmedName) {
        this.hrConfirmedName = hrConfirmedName;
    }

    public String getFinanceConfirmedName() {
        return financeConfirmedName;
    }

    public void setFinanceConfirmedName(String financeConfirmedName) {
        this.financeConfirmedName = financeConfirmedName;
    }

    public String getCeoConfirmedName() {
        return ceoConfirmedName;
    }

    public void setCeoConfirmedName(String ceoConfirmedName) {
        this.ceoConfirmedName = ceoConfirmedName;
    }

    public String getReimburseParticipation() {
        return reimburseParticipation;
    }

    public void setReimburseParticipation(String reimburseParticipation) {
        this.reimburseParticipation = reimburseParticipation;
    }

    public Date getHappenDate() {
        return happenDate;
    }

    public void setHappenDate(Date happenDate) {
        this.happenDate = happenDate;
    }

    public String getReimburseTitle() {
        return reimburseTitle;
    }

    public void setReimburseTitle(String reimburseTitle) {
        this.reimburseTitle = reimburseTitle;
    }

    public String getReimburseContent() {
        return reimburseContent;
    }

    public void setReimburseContent(String reimburseContent) {
        this.reimburseContent = reimburseContent;
    }

    public String getReimburseAppendix() {
        return reimburseAppendix;
    }

    public void setReimburseAppendix(String reimburseAppendix) {
        this.reimburseAppendix = reimburseAppendix;
    }

    public Date getBelongDate() {
        return belongDate;
    }

    public void setBelongDate(Date belongDate) {
        this.belongDate = belongDate;
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public String getCeoConfirmed() {
        return ceoConfirmed;
    }

    public void setCeoConfirmed(String ceoConfirmed) {
        this.ceoConfirmed = ceoConfirmed;
    }

    public Long getCeoConfirmedId() {
        return ceoConfirmedId;
    }

    public void setCeoConfirmedId(Long ceoConfirmedId) {
        this.ceoConfirmedId = ceoConfirmedId;
    }

    public Date getCeoConfirmedDate() {
        return ceoConfirmedDate;
    }

    public void setCeoConfirmedDate(Date ceoConfirmedDate) {
        this.ceoConfirmedDate = ceoConfirmedDate;
    }

    public void setReimburseId(Long reimburseId) {
        this.reimburseId = reimburseId;
    }

    public Long getReimburseId() {
        return reimburseId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setReimbursePrice(BigDecimal reimbursePrice) {
        this.reimbursePrice = reimbursePrice;
    }

    public BigDecimal getReimbursePrice() {
        return reimbursePrice;
    }

    public void setChargeTypeId(Long chargeTypeId) {
        this.chargeTypeId = chargeTypeId;
    }

    public Long getChargeTypeId() {
        return chargeTypeId;
    }

    public void setDeptConfirmed(String deptConfirmed) {
        this.deptConfirmed = deptConfirmed;
    }

    public String getDeptConfirmed() {
        return deptConfirmed;
    }

    public void setDeptConfirmedId(Long deptConfirmedId) {
        this.deptConfirmedId = deptConfirmedId;
    }

    public Long getDeptConfirmedId() {
        return deptConfirmedId;
    }

    public void setDeptConfirmedDate(Date deptConfirmedDate) {
        this.deptConfirmedDate = deptConfirmedDate;
    }

    public Date getDeptConfirmedDate() {
        return deptConfirmedDate;
    }

    public void setHrConfirmed(String hrConfirmed) {
        this.hrConfirmed = hrConfirmed;
    }

    public String getHrConfirmed() {
        return hrConfirmed;
    }

    public void setHrConfirmedId(Long hrConfirmedId) {
        this.hrConfirmedId = hrConfirmedId;
    }

    public Long getHrConfirmedId() {
        return hrConfirmedId;
    }

    public void setHrConfirmedDate(Date hrConfirmedDate) {
        this.hrConfirmedDate = hrConfirmedDate;
    }

    public Date getHrConfirmedDate() {
        return hrConfirmedDate;
    }

    public void setFinanceConfirmed(String financeConfirmed) {
        this.financeConfirmed = financeConfirmed;
    }

    public String getFinanceConfirmed() {
        return financeConfirmed;
    }

    public void setFinanceConfirmedId(Long financeConfirmedId) {
        this.financeConfirmedId = financeConfirmedId;
    }

    public Long getFinanceConfirmedId() {
        return financeConfirmedId;
    }

    public void setFinanceConfirmedDate(Date financeConfirmedDate) {
        this.financeConfirmedDate = financeConfirmedDate;
    }

    public Date getFinanceConfirmedDate() {
        return financeConfirmedDate;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

}
