package com.rich.common.utils;

import com.rich.common.constant.CacheConstants;
import com.rich.common.core.domain.entity.BasChargeType;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.utils.poi.ExcelHandlerAdapter;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 费用类型Excel处理器
 * 用于导出Excel时将费用类型ID转换为费用类型名称
 *
 * <AUTHOR>
 */
@Component
public class ChargeTypeExcelHandler implements ExcelHandlerAdapter {

    private RedisCache redisCache;

    @Override
    public Object format(Object value, String[] args) {
        if (redisCache == null) {
            redisCache = SpringContextHolder.getBean(RedisCache.class); // 在方法调用时获取
        }

        if (value == null) {
            return "";
        }

        // 从Redis中获取费用类型列表
        List<BasChargeType> chargeTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "chargeType");
        if (chargeTypes == null) {
            return "";
        }

        // 根据费用类型ID查找对应的费用类型名称
        Long chargeTypeId = Long.parseLong(value.toString());
        for (BasChargeType chargeType : chargeTypes) {
            if (chargeType.getChargeTypeId().equals(chargeTypeId)) {
                return chargeType.getChargeTypeLocalName();
            }
        }

        return value;
    }
} 