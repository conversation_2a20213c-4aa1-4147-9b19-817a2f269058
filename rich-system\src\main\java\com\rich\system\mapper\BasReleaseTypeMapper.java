package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasReleaseType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

;

/**
 * 放货方式Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@Mapper
public interface BasReleaseTypeMapper {
    /**
     * 查询放货方式
     *
     * @param releaseTypeId 放货方式主键
     * @return 放货方式
     */
    BasReleaseType selectBasReleaseTypeByReleaseTypeId(Long releaseTypeId);

    /**
     * 查询放货方式列表
     *
     * @param basReleaseType 放货方式
     * @return 放货方式集合
     */
    List<BasReleaseType> selectBasReleaseTypeList(BasReleaseType basReleaseType);

    /**
     * 新增放货方式
     *
     * @param basReleaseType 放货方式
     * @return 结果
     */
    int insertBasReleaseType(BasReleaseType basReleaseType);

    /**
     * 修改放货方式
     *
     * @param basReleaseType 放货方式
     * @return 结果
     */
    int updateBasReleaseType(BasReleaseType basReleaseType);

    /**
     * 删除放货方式
     *
     * @param releaseTypeId 放货方式主键
     * @return 结果
     */
    int deleteBasReleaseTypeByReleaseTypeId(Long releaseTypeId);

    /**
     * 批量删除放货方式
     *
     * @param releaseTypeIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasReleaseTypeByReleaseTypeIds(Long[] releaseTypeIds);
}
