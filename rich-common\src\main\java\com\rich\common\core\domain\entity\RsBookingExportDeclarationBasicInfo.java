package com.rich.common.core.domain.entity;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

/**
 * 订舱单出口报关基础信息对象 rs_booking_export_declaration_basic_info
 * 
 * <AUTHOR>
 * @date 2023-08-15
 */
public class RsBookingExportDeclarationBasicInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**
     * 出口报关信息
     */
    private Long exportDeclarationId;

    /**
     * 订舱申请单
     */
    @Excel(name = "订舱申请单")
    private Long bookingId;

    private Long typeId;

    /**
     * 物流属性
     */
    @Excel(name = "物流属性")
    private Long logisticsTypeId;

    /**
     * 派送区域
     */
    @Excel(name = "派送区域")
    private Long dispatchRegionId;

    /**
     * 派送地址
     */
    @Excel(name = "派送地址")
    private String dispatchAddress;

    /** 派送时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "派送时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dispatchTime;

    /** 派送联系人 */
    @Excel(name = "派送联系人")
    private String dispatchContact;

    /** 派送电话 */
    @Excel(name = "派送电话")
    private String dispatchTel;

    /** 派送备注 */
    @Excel(name = "派送备注")
    private String dispatchRemark;

    /** 派送司机姓名 */
    @Excel(name = "派送司机姓名")
    private String dispatchDriverName;

    /** 派送司机电话 */
    @Excel(name = "派送司机电话")
    private String dispatchDriverTel;

    /** 派送司机车牌 */
    @Excel(name = "派送司机车牌")
    private String dispatchTruckNo;

    /** 派送司机备注 */
    @Excel(name = "派送司机备注")
    private String dispatchTruckRemark;

    /** 操作审批 */
    @Excel(name = "操作审批")
    private String opConfirmed;

    /** 确认人ID */
    @Excel(name = "确认人ID")
    private Long opConfirmedId;

    /** 操作审批时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "操作审批时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date opConfirmedDate;

    /** 财务审批 */
    @Excel(name = "财务审批")
    private String financeConfirmed;

    /** 确认人ID */
    @Excel(name = "确认人ID")
    private Long financeConfirmedId;

    /** 财务审批时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "财务审批时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date financeConfirmedDate;

    /** 业务审批 */
    @Excel(name = "业务审批")
    private String salesConfirmed;

    /** 确认人ID */
    @Excel(name = "确认人ID")
    private Long salesConfirmedId;

    /** 业务审批时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "业务审批时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date salesConfirmedDate;

    /** 供应商审批 */
    @Excel(name = "供应商审批")
    private String supplierConfirmed;

    /** 确认人ID */
    @Excel(name = "确认人ID")
    private Long supplierConfirmedId;

    /**
     * 供应商审批时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "供应商审批时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date supplierConfirmedDate;

    /**
     * 发票查询编号
     */
    @Excel(name = "发票查询编号")
    private String invoiceQueryNo;

    private List<RsBookingReceivablePayable> rsBookingReceivablePayableList;

    public Long getTypeId() {
        return typeId;
    }

    public void setTypeId(Long typeId) {
        this.typeId = typeId;
    }

    public List<RsBookingReceivablePayable> getRsBookingReceivablePayableList() {
        return rsBookingReceivablePayableList;
    }

    public void setRsBookingReceivablePayableList(List<RsBookingReceivablePayable> rsBookingReceivablePayableList) {
        this.rsBookingReceivablePayableList = rsBookingReceivablePayableList;
    }

    public void setExportDeclarationId(Long exportDeclarationId)
    {
        this.exportDeclarationId = exportDeclarationId;
    }

    public Long getExportDeclarationId() 
    {
        return exportDeclarationId;
    }
    public void setBookingId(Long bookingId) 
    {
        this.bookingId = bookingId;
    }

    public Long getBookingId() 
    {
        return bookingId;
    }
    public void setLogisticsTypeId(Long logisticsTypeId) 
    {
        this.logisticsTypeId = logisticsTypeId;
    }

    public Long getLogisticsTypeId() 
    {
        return logisticsTypeId;
    }
    public void setDispatchRegionId(Long dispatchRegionId) 
    {
        this.dispatchRegionId = dispatchRegionId;
    }

    public Long getDispatchRegionId() 
    {
        return dispatchRegionId;
    }
    public void setDispatchAddress(String dispatchAddress) 
    {
        this.dispatchAddress = dispatchAddress;
    }

    public String getDispatchAddress() 
    {
        return dispatchAddress;
    }
    public void setDispatchTime(Date dispatchTime) 
    {
        this.dispatchTime = dispatchTime;
    }

    public Date getDispatchTime() 
    {
        return dispatchTime;
    }
    public void setDispatchContact(String dispatchContact) 
    {
        this.dispatchContact = dispatchContact;
    }

    public String getDispatchContact() 
    {
        return dispatchContact;
    }
    public void setDispatchTel(String dispatchTel) 
    {
        this.dispatchTel = dispatchTel;
    }

    public String getDispatchTel() 
    {
        return dispatchTel;
    }
    public void setDispatchRemark(String dispatchRemark) 
    {
        this.dispatchRemark = dispatchRemark;
    }

    public String getDispatchRemark() 
    {
        return dispatchRemark;
    }
    public void setDispatchDriverName(String dispatchDriverName) 
    {
        this.dispatchDriverName = dispatchDriverName;
    }

    public String getDispatchDriverName() 
    {
        return dispatchDriverName;
    }
    public void setDispatchDriverTel(String dispatchDriverTel) 
    {
        this.dispatchDriverTel = dispatchDriverTel;
    }

    public String getDispatchDriverTel() 
    {
        return dispatchDriverTel;
    }
    public void setDispatchTruckNo(String dispatchTruckNo) 
    {
        this.dispatchTruckNo = dispatchTruckNo;
    }

    public String getDispatchTruckNo() 
    {
        return dispatchTruckNo;
    }
    public void setDispatchTruckRemark(String dispatchTruckRemark) 
    {
        this.dispatchTruckRemark = dispatchTruckRemark;
    }

    public String getDispatchTruckRemark() 
    {
        return dispatchTruckRemark;
    }
    public void setOpConfirmed(String opConfirmed) 
    {
        this.opConfirmed = opConfirmed;
    }

    public String getOpConfirmed() 
    {
        return opConfirmed;
    }
    public void setOpConfirmedId(Long opConfirmedId) 
    {
        this.opConfirmedId = opConfirmedId;
    }

    public Long getOpConfirmedId() 
    {
        return opConfirmedId;
    }
    public void setOpConfirmedDate(Date opConfirmedDate) 
    {
        this.opConfirmedDate = opConfirmedDate;
    }

    public Date getOpConfirmedDate() 
    {
        return opConfirmedDate;
    }
    public void setFinanceConfirmed(String financeConfirmed) 
    {
        this.financeConfirmed = financeConfirmed;
    }

    public String getFinanceConfirmed() 
    {
        return financeConfirmed;
    }
    public void setFinanceConfirmedId(Long financeConfirmedId) 
    {
        this.financeConfirmedId = financeConfirmedId;
    }

    public Long getFinanceConfirmedId() 
    {
        return financeConfirmedId;
    }
    public void setFinanceConfirmedDate(Date financeConfirmedDate) 
    {
        this.financeConfirmedDate = financeConfirmedDate;
    }

    public Date getFinanceConfirmedDate() 
    {
        return financeConfirmedDate;
    }
    public void setSalesConfirmed(String salesConfirmed) 
    {
        this.salesConfirmed = salesConfirmed;
    }

    public String getSalesConfirmed() 
    {
        return salesConfirmed;
    }
    public void setSalesConfirmedId(Long salesConfirmedId) 
    {
        this.salesConfirmedId = salesConfirmedId;
    }

    public Long getSalesConfirmedId() 
    {
        return salesConfirmedId;
    }
    public void setSalesConfirmedDate(Date salesConfirmedDate) 
    {
        this.salesConfirmedDate = salesConfirmedDate;
    }

    public Date getSalesConfirmedDate() 
    {
        return salesConfirmedDate;
    }
    public void setSupplierConfirmed(String supplierConfirmed) 
    {
        this.supplierConfirmed = supplierConfirmed;
    }

    public String getSupplierConfirmed() 
    {
        return supplierConfirmed;
    }
    public void setSupplierConfirmedId(Long supplierConfirmedId) 
    {
        this.supplierConfirmedId = supplierConfirmedId;
    }

    public Long getSupplierConfirmedId() 
    {
        return supplierConfirmedId;
    }
    public void setSupplierConfirmedDate(Date supplierConfirmedDate) 
    {
        this.supplierConfirmedDate = supplierConfirmedDate;
    }

    public Date getSupplierConfirmedDate() 
    {
        return supplierConfirmedDate;
    }
    public void setInvoiceQueryNo(String invoiceQueryNo) 
    {
        this.invoiceQueryNo = invoiceQueryNo;
    }

    public String getInvoiceQueryNo() 
    {
        return invoiceQueryNo;
    }

}
