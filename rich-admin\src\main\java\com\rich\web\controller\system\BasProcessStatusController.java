package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasProcessStatus;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasProcessStatusService;
import com.rich.system.service.impl.RedisCacheImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 进度状态Controller
 *
 * <AUTHOR>
 * @date 2023-06-05
 */
@RestController
@RequestMapping("/system/processstatus")
public class BasProcessStatusController extends BaseController {
    @Autowired
    private BasProcessStatusService basProcessStatusService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisCacheImpl RedisCache;

    /**
     * 查询进度状态列表
     */
    @PreAuthorize("@ss.hasPermi('system:processstatus:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasProcessStatus basProcessStatus) {
        startPage();
        List<BasProcessStatus> list = basProcessStatusService.selectBasProcessStatusList(basProcessStatus);
        return getDataTable(list);
    }

    @GetMapping("/selectList")
    public AjaxResult selectList() {
        List<BasProcessStatus> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "processStatus");
        if (list == null) {
            RedisCache.processStatus();
            list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "processStatus");
        }
        return AjaxResult.success(list);
    }

    /**
     * 导出进度状态列表
     */
    @PreAuthorize("@ss.hasPermi('system:processstatus:export')")
    @Log(title = "进度状态", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasProcessStatus basProcessStatus) {
        List<BasProcessStatus> list = basProcessStatusService.selectBasProcessStatusList(basProcessStatus);
        ExcelUtil<BasProcessStatus> util = new ExcelUtil<BasProcessStatus>(BasProcessStatus.class);
        util.exportExcel(response, list, "进度状态数据");
    }

    /**
     * 获取进度状态详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:processstatus:query')")
    @GetMapping(value = "/{processStatusId}")
    public AjaxResult getInfo(@PathVariable("processStatusId") Long processStatusId) {
        return AjaxResult.success(basProcessStatusService.selectBasProcessStatusByProcessStatusId(processStatusId));
    }

    /**
     * 新增进度状态
     */
    @PreAuthorize("@ss.hasPermi('system:processstatus:add')")
    @Log(title = "进度状态", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasProcessStatus basProcessStatus) {
        return toAjax(basProcessStatusService.insertBasProcessStatus(basProcessStatus));
    }

    /**
     * 修改进度状态
     */
    @PreAuthorize("@ss.hasPermi('system:processstatus:edit')")
    @Log(title = "进度状态", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasProcessStatus basProcessStatus) {
        return toAjax(basProcessStatusService.updateBasProcessStatus(basProcessStatus));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:processstatus:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasProcessStatus basProcessStatus) {
        basProcessStatus.setUpdateBy(getUserId());
        return toAjax(basProcessStatusService.changeStatus(basProcessStatus));
    }

    /**
     * 删除进度状态
     */
    @PreAuthorize("@ss.hasPermi('system:processstatus:remove')")
    @Log(title = "进度状态", businessType = BusinessType.DELETE)
    @DeleteMapping("/{processStatusIds}")
    public AjaxResult remove(@PathVariable Long[] processStatusIds) {
        return toAjax(basProcessStatusService.deleteBasProcessStatusByProcessStatusIds(processStatusIds));
    }
}
