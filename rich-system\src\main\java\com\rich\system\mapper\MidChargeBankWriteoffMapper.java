package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.MidChargeBankWriteoff;
import org.apache.ibatis.annotations.Mapper;

/**
 * 记录银行销账的明细中间Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-06
 */
@Mapper
public interface MidChargeBankWriteoffMapper {
    /**
     * 查询记录银行销账的明细中间
     * 
     * @param midChargeBankId 记录银行销账的明细中间主键
     * @return 记录银行销账的明细中间
     */
    MidChargeBankWriteoff selectMidChargeBankWriteoffByMidChargeBankId(Long midChargeBankId);

    /**
     * 查询记录银行销账的明细中间列表
     * 
     * @param midChargeBankWriteoff 记录银行销账的明细中间
     * @return 记录银行销账的明细中间集合
     */
    List<MidChargeBankWriteoff> selectMidChargeBankWriteoffList(MidChargeBankWriteoff midChargeBankWriteoff);

    /**
     * 新增记录银行销账的明细中间
     * 
     * @param midChargeBankWriteoff 记录银行销账的明细中间
     * @return 结果
     */
    int insertMidChargeBankWriteoff(MidChargeBankWriteoff midChargeBankWriteoff);

    /**
     * 修改记录银行销账的明细中间
     * 
     * @param midChargeBankWriteoff 记录银行销账的明细中间
     * @return 结果
     */
    int updateMidChargeBankWriteoff(MidChargeBankWriteoff midChargeBankWriteoff);

    /**
     * 删除记录银行销账的明细中间
     * 
     * @param midChargeBankId 记录银行销账的明细中间主键
     * @return 结果
     */
    int deleteMidChargeBankWriteoffByMidChargeBankId(Long midChargeBankId);

    /**
     * 批量删除记录银行销账的明细中间
     * 
     * @param midChargeBankIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteMidChargeBankWriteoffByMidChargeBankIds(Long[] midChargeBankIds);

    void batchMidChargeBankWriteoff(List<MidChargeBankWriteoff> midChargeBankWriteoffList);
}
