package com.rich.system.mapper;


import com.rich.common.core.domain.entity.BasPosition;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Entity
 */
@Mapper
public interface BasPositionMapper {
    /**
     * 查询岗位数据集合
     *
     * @param post 岗位信息
     * @return 岗位数据集合
     */
    List<BasPosition> selectPostList(BasPosition post);

    /**
     * 查询所有岗位
     *
     * @return 岗位列表
     */
    List<BasPosition> selectPostAll();

    /**
     * 通过岗位ID查询岗位信息
     *
     * @param positionId 岗位ID
     * @return 角色对象信息
     */
    BasPosition selectPostById(Long positionId);

    /**
     * 根据用户ID获取岗位选择框列表
     *
     * @param staffId 用户ID
     * @return 选中岗位ID列表
     */
    List<Long> selectPostListByUserId(Long staffId);

    /**
     * 查询用户所属岗位组
     *
     * @param staffUsername 用户名
     * @return 结果
     */
    List<BasPosition> selectPostsByUserName(String staffUsername);

    /**
     * 删除岗位信息
     *
     * @param positionId 岗位ID
     * @return 结果
     */
    int deletePostById(Long positionId);

    /**
     * 批量删除岗位信息
     *
     * @param postIds 需要删除的岗位ID
     * @return 结果
     */
    int deletePostByIds(Long[] postIds);

    /**
     * 修改岗位信息
     *
     * @param post 岗位信息
     * @return 结果
     */
    int updatePost(BasPosition post);

    /**
     * 新增岗位信息
     *
     * @param post 岗位信息
     * @return 结果
     */
    int insertPost(BasPosition post);

    /**
     * 校验岗位名称
     *
     * @param positionLocalName 岗位名称
     * @return 结果
     */
    BasPosition checkPostNameUnique(String positionLocalName);

    /**
     * 校验岗位编码
     *
     * @param positionEnName 岗位编码
     * @return 结果
     */
    BasPosition checkPostCodeUnique(String positionEnName);


    List<BasPosition> selectPostUnderUser(Long userId);

}
