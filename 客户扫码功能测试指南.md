# 客户扫码功能测试指南

## 功能概述

当用户角色为'client'时，扫码快递单号会根据不同情况显示相应的处理逻辑：

1. **不存在**：此快递不存在，是否添加？
2. **存在但属于未知归属**：此快递已到仓库但无人认领，是否认领？
3. **存在但不属于自己**：此快递属于其他人，请您重新核对单号。
4. **存在且属于自己但信息未完善**：直接打开详情页，可编辑。
5. **存在且属于自己且已入仓**：直接打开详情页，仅查看。
6. **存在且属于自己但已出仓**：货物已出仓，请联系瑞旗公司获取详情。

## 后端接口实现

### 1. 根据快递单号查询库存信息
```http
GET /system/inventory/express/{expressNo}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "inventoryId": 123,
    "forwarderNo": "SF123456789",
    "clientCode": "CLIENT001",
    "clientName": "张三",
    "cargoName": "电子产品",
    "totalBoxes": 2,
    "totalGrossWeight": 5.5,
    "inventoryStatus": "0"
  }
}
```

### 2. 客户认领快递
```http
PUT /system/inventory/claim/{inventoryId}
Content-Type: application/json

{
  "clientCode": "CLIENT001"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "认领成功"
}
```

### 3. 获取客户快递状态统计
```http
GET /system/inventory/status/count?clientCode=CLIENT001
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "preEntryCount": 5,
    "inTransitCount": 3,
    "uncompletedCount": 2,
    "unconfirmedCount": 1,
    "confirmedCount": 10,
    "unknownCount": 8
  }
}
```

### 4. 检查快递信息完整性
```http
GET /system/inventory/check/complete/{inventoryId}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": true
}
```

## 前端实现

### 扫码处理逻辑

```javascript
// 客户扫码处理逻辑
handleClientExpressScan(no) {
  const that = this;
  // 根据快递单号查询库存信息
  getInventoryByExpress(no).then(res => {
    if (!res.data) {
      // 情况1：不存在
      that.showExpressNotExistDialog(no);
    } else {
      const inventory = res.data;
      const currentUserId = that.$store.state.user.id;
      
      if (!inventory.clientCode || inventory.clientCode === 'unknown') {
        // 情况2：存在但属于未知归属
        that.showUnclaimedExpressDialog(no, inventory);
      } else if (inventory.clientCode !== currentUserId) {
        // 情况3：存在但不属于自己
        that.showNotYourExpressDialog(no);
      } else {
        // 属于自己的快递
        if (inventory.inventoryStatus === 'outbound') {
          // 情况5：已出仓
          that.showExpressOutboundDialog(no);
        } else if (inventory.inventoryStatus === 'inbound' && !that.isInfoComplete(inventory)) {
          // 情况4：信息未完善，可编辑
          uni.navigateTo({
            url: '/packageA/inventory/edit?inventoryId=' + inventory.inventoryId + '&mode=edit'
          });
        } else {
          // 情况4：已入仓且信息完善，仅查看
          uni.navigateTo({
            url: '/packageA/inventory/detail?inventoryId=' + inventory.inventoryId + '&mode=view'
          });
        }
      }
    }
  }).catch(err => {
    console.error('查询快递信息失败:', err);
    uni.showToast({
      title: '查询失败，请重试',
      icon: 'none'
    });
  });
}
```

## 测试用例

### 测试用例1：快递不存在
**前置条件：** 扫描一个不存在的快递单号
**预期结果：** 显示"此快递不存在，是否添加？"对话框
**操作步骤：**
1. 客户角色登录
2. 扫描不存在的快递单号（如：TEST123456）
3. 验证弹出添加对话框
4. 点击"添加"跳转到添加页面

### 测试用例2：快递无人认领
**前置条件：** 数据库中存在clientCode为null或'unknown'的快递记录
**预期结果：** 显示"此快递已到仓库但无人认领，是否认领？"对话框
**操作步骤：**
1. 客户角色登录
2. 扫描无人认领的快递单号
3. 验证弹出认领对话框
4. 点击"认领"调用认领接口
5. 验证认领成功后跳转到编辑页面

### 测试用例3：快递属于其他人
**前置条件：** 数据库中存在clientCode为其他用户的快递记录
**预期结果：** 显示"此快递属于其他人，请您重新核对单号。"提示
**操作步骤：**
1. 客户角色登录
2. 扫描属于其他人的快递单号
3. 验证显示错误提示

### 测试用例4：自己的快递信息未完善
**前置条件：** 数据库中存在属于当前用户但信息不完整的快递记录
**预期结果：** 直接跳转到编辑页面
**操作步骤：**
1. 客户角色登录
2. 扫描自己的信息不完整的快递单号
3. 验证直接跳转到编辑页面（mode=edit）

### 测试用例5：自己的快递已入仓
**前置条件：** 数据库中存在属于当前用户且信息完整的快递记录
**预期结果：** 直接跳转到详情页面（只读）
**操作步骤：**
1. 客户角色登录
2. 扫描自己的已入仓快递单号
3. 验证直接跳转到详情页面（mode=view）

### 测试用例6：自己的快递已出仓
**前置条件：** 数据库中存在属于当前用户且已出仓的快递记录
**预期结果：** 显示"货物已出仓，请联系瑞旗公司获取详情。"提示
**操作步骤：**
1. 客户角色登录
2. 扫描自己的已出仓快递单号
3. 验证显示出仓提示
4. 点击"确定"可选择联系客服

## 数据库测试数据准备

### 创建测试数据SQL
```sql
-- 1. 无人认领的快递
INSERT INTO rs_inventory (forwarder_no, client_code, inventory_status, cargo_name, total_boxes, total_gross_weight) 
VALUES ('TEST001', 'unknown', '0', '测试货物1', 1, 2.5);

-- 2. 属于其他人的快递
INSERT INTO rs_inventory (forwarder_no, client_code, client_name, inventory_status, cargo_name, total_boxes, total_gross_weight) 
VALUES ('TEST002', 'OTHER_CLIENT', '其他客户', '0', '测试货物2', 2, 3.0);

-- 3. 属于当前用户但信息不完整的快递
INSERT INTO rs_inventory (forwarder_no, client_code, inventory_status, cargo_name) 
VALUES ('TEST003', 'CURRENT_CLIENT', '0', '测试货物3');

-- 4. 属于当前用户且信息完整的快递
INSERT INTO rs_inventory (forwarder_no, client_code, client_name, inventory_status, cargo_name, total_boxes, total_gross_weight) 
VALUES ('TEST004', 'CURRENT_CLIENT', '当前客户', '0', '测试货物4', 3, 4.5);

-- 5. 属于当前用户但已出仓的快递
INSERT INTO rs_inventory (forwarder_no, client_code, client_name, inventory_status, cargo_name, total_boxes, total_gross_weight) 
VALUES ('TEST005', 'CURRENT_CLIENT', '当前客户', '1', '测试货物5', 1, 1.5);
```

## 注意事项

1. **权限验证**：确保只有client角色的用户才能使用此功能
2. **数据一致性**：认领操作需要事务保证
3. **用户体验**：各种提示信息要清晰明确
4. **错误处理**：网络异常、服务器错误等情况的处理
5. **性能考虑**：扫码查询要快速响应

## 部署检查清单

- [ ] 后端接口部署完成
- [ ] 前端代码更新完成
- [ ] 数据库表结构检查
- [ ] 权限配置验证
- [ ] 测试数据准备
- [ ] 功能测试通过
- [ ] 性能测试通过
- [ ] 用户验收测试通过
