package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.MpWarehouseClient;
import org.apache.ibatis.annotations.Mapper;

/**
 * 仓库客户信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Mapper
public interface MpWarehouseClientMapper {
    /**
     * 查询仓库客户信息
     *
     * @param warehouseClientId 仓库客户信息主键
     * @return 仓库客户信息
     */
    MpWarehouseClient selectMpWarehouseClientByWarehouseClientId(Long warehouseClientId);

    /**
     * 查询仓库客户信息列表
     *
     * @param mpWarehouseClient 仓库客户信息
     * @return 仓库客户信息集合
     */
    List<MpWarehouseClient> selectMpWarehouseClientList(MpWarehouseClient mpWarehouseClient);

    /**
     * 新增仓库客户信息
     *
     * @param mpWarehouseClient 仓库客户信息
     * @return 结果
     */
    int insertMpWarehouseClient(MpWarehouseClient mpWarehouseClient);

    /**
     * 修改仓库客户信息
     *
     * @param mpWarehouseClient 仓库客户信息
     * @return 结果
     */
    int updateMpWarehouseClient(MpWarehouseClient mpWarehouseClient);

    /**
     * 删除仓库客户信息
     *
     * @param warehouseClientId 仓库客户信息主键
     * @return 结果
     */
    int deleteMpWarehouseClientByWarehouseClientId(Long warehouseClientId);

    /**
     * 批量删除仓库客户信息
     *
     * @param warehouseClientIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteMpWarehouseClientByWarehouseClientIds(Long[] warehouseClientIds);
}
