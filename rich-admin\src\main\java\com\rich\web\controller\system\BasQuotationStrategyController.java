package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.BasQuotationStrategy;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.BasQuotationStrategyService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 *
 * <AUTHOR>
 * @date 2024-04-25
 */
@RestController
@RequestMapping("/system/quotationstrategy")
public class BasQuotationStrategyController extends BaseController {
    @Autowired
    private BasQuotationStrategyService basQuotationStrategyService;

    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:quotationstrategy:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasQuotationStrategy basQuotationStrategy) {
        startPage();
        List<BasQuotationStrategy> list = basQuotationStrategyService.selectBasQuotationStrategyList(basQuotationStrategy);
        return getDataTable(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:quotationstrategy:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasQuotationStrategy basQuotationStrategy) {
        List<BasQuotationStrategy> list = basQuotationStrategyService.selectBasQuotationStrategyList(basQuotationStrategy);
        ExcelUtil<BasQuotationStrategy> util = new ExcelUtil<BasQuotationStrategy>(BasQuotationStrategy.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:quotationstrategy:query')")
    @GetMapping(value = "/{strategyCode}")
    public AjaxResult getInfo(@PathVariable("strategyCode") String strategyCode) {
        return AjaxResult.success(basQuotationStrategyService.selectBasQuotationStrategyByStrategyCode(strategyCode));
    }

    /**
     * 新增【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:quotationstrategy:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasQuotationStrategy basQuotationStrategy) {
        return toAjax(basQuotationStrategyService.insertBasQuotationStrategy(basQuotationStrategy));
    }

    /**
     * 修改【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:quotationstrategy:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasQuotationStrategy basQuotationStrategy) {
        return toAjax(basQuotationStrategyService.updateBasQuotationStrategy(basQuotationStrategy));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:quotationstrategy:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasQuotationStrategy basQuotationStrategy) {
        basQuotationStrategy.setUpdateBy(getUserId());
        return toAjax(basQuotationStrategyService.changeStatus(basQuotationStrategy));
    }

    /**
     * 删除【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:quotationstrategy:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{strategyCodes}")
    public AjaxResult remove(@PathVariable String[] strategyCodes) {
        return toAjax(basQuotationStrategyService.deleteBasQuotationStrategyByStrategyCodes(strategyCodes));
    }
}
