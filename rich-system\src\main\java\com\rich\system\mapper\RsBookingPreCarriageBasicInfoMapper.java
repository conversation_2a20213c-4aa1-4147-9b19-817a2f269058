package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsBookingPreCarriageBasicInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 订舱单前程运输基础信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-15
 */
@Mapper
public interface RsBookingPreCarriageBasicInfoMapper {
    /**
     * 查询订舱单前程运输基础信息
     *
     * @return 订舱单前程运输基础信息
     */
    RsBookingPreCarriageBasicInfo selectRsBookingPreCarriageBasicInfoByBookingId(Long bookingId);

    /**
     * 查询订舱单前程运输基础信息列表
     *
     * @param rsBookingPreCarriageBasicInfo 订舱单前程运输基础信息
     * @return 订舱单前程运输基础信息集合
     */
    List<RsBookingPreCarriageBasicInfo> selectRsBookingPreCarriageBasicInfoList(RsBookingPreCarriageBasicInfo rsBookingPreCarriageBasicInfo);

    /**
     * 新增订舱单前程运输基础信息
     *
     * @param rsBookingPreCarriageBasicInfo 订舱单前程运输基础信息
     * @return 结果
     */
    int insertRsBookingPreCarriageBasicInfo(RsBookingPreCarriageBasicInfo rsBookingPreCarriageBasicInfo);

    /**
     * 修改订舱单前程运输基础信息
     *
     * @param rsBookingPreCarriageBasicInfo 订舱单前程运输基础信息
     * @return 结果
     */
    int updateRsBookingPreCarriageBasicInfo(RsBookingPreCarriageBasicInfo rsBookingPreCarriageBasicInfo);

    /**
     * 删除订舱单前程运输基础信息
     *
     * @return 结果
     */
    int deleteRsBookingPreCarriageBasicInfoById(Long bookingId);

    /**
     * 批量删除订舱单前程运输基础信息
     *
     * @return 结果
     */
    int deleteRsBookingPreCarriageBasicInfoByIds(Long[] bookingIds);
}
