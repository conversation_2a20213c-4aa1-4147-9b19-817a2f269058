package com.rich.system.mapper;

import com.rich.system.domain.MidLocationDestination;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 目的港区域Mapper接口
 *
 * <AUTHOR>
 * @date 2022-09-16
 */
@Mapper
public interface MidLocationDestinationMapper {
    /**
     * 查询目的港区域
     *
     * @param belongId 目的港区域主键
     * @param belongTo 属于
     * @return 目的港区域
     */
    List<Long> selectMidLocationDestinationById(Long belongId, String belongTo);

    /**
     * 查询目的港区域列表
     *
     * @return 目的港区域集合
     */
    List<MidLocationDestination> selectMidLocationDestinationByLocationIds(@Param("locationIds") Long[] locationIds, String belongTo);

    /**
     * 查询目的港区域列表
     *
     * @return 目的港区域集合
     */
    List<MidLocationDestination> selectMidLocationDestinationList(MidLocationDestination MidLocationDestination);

    /**
     * 新增目的港区域
     *
     * @param MidLocationDestination 目的港区域
     * @return 结果
     */
    int insertMidLocationDestination(MidLocationDestination MidLocationDestination);


    /**
     * 删除目的港区域
     *
     * @param belongId 目的港区域主键
     * @param belongTo 属于
     * @return 结果
     */
    int deleteMidLocationDestinationById(Long belongId, String belongTo);

    /**
     * 批量删除目的港区域
     *
     * @param belongIds 需要删除的数据主键集合
     * @param belongTo  属于
     * @return 结果
     */
    int deleteMidLocationDestinationByIds(Long[] belongIds, String belongTo);


    /**
     * 批量新增${subTable.functionName}
     *
     * @param belongList ${subTable.functionName}列表
     * @return 结果
     */
    int batchLD(List<MidLocationDestination> belongList);

}
