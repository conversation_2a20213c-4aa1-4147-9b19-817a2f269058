package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.MidOutboundSettlement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.MidOutboundSettlementMapper;
import com.rich.system.service.MidOutboundSettlementService;

/**
 * 仓租结算中间Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-11
 */
@Service
public class MidOutboundSettlementServiceImpl implements MidOutboundSettlementService {
    @Autowired
    private MidOutboundSettlementMapper midOutboundSettlementMapper;

    /**
     * 查询仓租结算中间
     *
     * @param outboundRecordId 仓租结算中间主键
     * @return 仓租结算中间
     */
    @Override
    public MidOutboundSettlement selectMidOutboundSettlementByOutboundRecordId(Long outboundRecordId) {
        return midOutboundSettlementMapper.selectMidOutboundSettlementByOutboundRecordId(outboundRecordId);
    }

    /**
     * 查询仓租结算中间列表
     *
     * @param midOutboundSettlement 仓租结算中间
     * @return 仓租结算中间
     */
    @Override
    public List<MidOutboundSettlement> selectMidOutboundSettlementList(MidOutboundSettlement midOutboundSettlement) {
        return midOutboundSettlementMapper.selectMidOutboundSettlementList(midOutboundSettlement);
    }

    /**
     * 新增仓租结算中间
     *
     * @param midOutboundSettlement 仓租结算中间
     * @return 结果
     */
    @Override
    public int insertMidOutboundSettlement(MidOutboundSettlement midOutboundSettlement) {
        return midOutboundSettlementMapper.insertMidOutboundSettlement(midOutboundSettlement);
    }

    /**
     * 修改仓租结算中间
     *
     * @param midOutboundSettlement 仓租结算中间
     * @return 结果
     */
    @Override
    public int updateMidOutboundSettlement(MidOutboundSettlement midOutboundSettlement) {
        return midOutboundSettlementMapper.updateMidOutboundSettlement(midOutboundSettlement);
    }

    /**
     * 修改仓租结算中间状态
     *
     * @param midOutboundSettlement 仓租结算中间
     * @return 仓租结算中间
     */
    @Override
    public int changeStatus(MidOutboundSettlement midOutboundSettlement) {
        return midOutboundSettlementMapper.updateMidOutboundSettlement(midOutboundSettlement);
    }

    /**
     * 批量删除仓租结算中间
     *
     * @param outboundRecordIds 需要删除的仓租结算中间主键
     * @return 结果
     */
    @Override
    public int deleteMidOutboundSettlementByOutboundRecordIds(Long[] outboundRecordIds) {
        return midOutboundSettlementMapper.deleteMidOutboundSettlementByOutboundRecordIds(outboundRecordIds);
    }

    /**
     * 删除仓租结算中间信息
     *
     * @param outboundRecordId 仓租结算中间主键
     * @return 结果
     */
    @Override
    public int deleteMidOutboundSettlementByOutboundRecordId(Long outboundRecordId) {
        return midOutboundSettlementMapper.deleteMidOutboundSettlementByOutboundRecordId(outboundRecordId);
    }
}
