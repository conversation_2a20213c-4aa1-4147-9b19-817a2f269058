package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

/**
 * 文件名称对象 bas_doc
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
public class BasDoc extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 文件类型
     */
    private Long docId;

    /**
     * 简称
     */
    @Excel(name = "简称")
    private String docShortName;

    /**
     * 中文名
     */
    @Excel(name = "中文名")
    private String docLocalName;

    /**
     * 英文名
     */
    @Excel(name = "英文名")
    private String docEnName;

    /**
     * 所属进度
     */
    @Excel(name = "所属进度")
    private Long processTypeId;

    private String processType;

    /**
     * 系统生成
     */
    @Excel(name = "系统生成")
    private String isGenerated;

    /**
     * 系统隔离
     */
    @Excel(name = "系统隔离")
    private String isIsolation;

    /**
     * 横向优先级
     */
    @Excel(name = "横向优先级")
    private Integer priority;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Integer orderNum;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;

    private Long[] cargoTypeIds;

    private String cargoType;

    private Long[] serviceTypeIds;

    private String serviceType;

    private Long[] locationDepartureIds;

    private String locationDeparture;

    private Long[] locationDestinationIds;

    private String locationDestination;

    private Long[] lineDepartureIds;

    private String lineDeparture;

    private Long[] lineDestinationIds;

    private String lineDestination;

    private Long[] carrierIds;

    private String carrier;

    private Long[] docFlowDirectionIds;

    private String docFlowDirection;

    private Long[] docIssueTypeIds;

    private String docIssueType;

    public String getCargoType() {
        return cargoType;
    }

    public void setCargoType(String cargoType) {
        this.cargoType = cargoType;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public String getLocationDeparture() {
        return locationDeparture;
    }

    public void setLocationDeparture(String locationDeparture) {
        this.locationDeparture = locationDeparture;
    }

    public String getLocationDestination() {
        return locationDestination;
    }

    public void setLocationDestination(String locationDestination) {
        this.locationDestination = locationDestination;
    }

    public String getLineDeparture() {
        return lineDeparture;
    }

    public void setLineDeparture(String lineDeparture) {
        this.lineDeparture = lineDeparture;
    }

    public String getLineDestination() {
        return lineDestination;
    }

    public void setLineDestination(String lineDestination) {
        this.lineDestination = lineDestination;
    }

    public String getCarrier() {
        return carrier;
    }

    public void setCarrier(String carrier) {
        this.carrier = carrier;
    }

    public String getDocFlowDirection() {
        return docFlowDirection;
    }

    public void setDocFlowDirection(String docFlowDirection) {
        this.docFlowDirection = docFlowDirection;
    }

    public String getDocIssueType() {
        return docIssueType;
    }

    public void setDocIssueType(String docIssueType) {
        this.docIssueType = docIssueType;
    }

    public Long[] getCargoTypeIds() {
        return cargoTypeIds;
    }

    public void setCargoTypeIds(Long[] cargoTypeIds) {
        this.cargoTypeIds = cargoTypeIds;
    }

    public Long[] getServiceTypeIds() {
        return serviceTypeIds;
    }

    public void setServiceTypeIds(Long[] serviceTypeIds) {
        this.serviceTypeIds = serviceTypeIds;
    }

    public Long[] getLocationDepartureIds() {
        return locationDepartureIds;
    }

    public void setLocationDepartureIds(Long[] locationDepartureIds) {
        this.locationDepartureIds = locationDepartureIds;
    }

    public Long[] getLocationDestinationIds() {
        return locationDestinationIds;
    }

    public void setLocationDestinationIds(Long[] locationDestinationIds) {
        this.locationDestinationIds = locationDestinationIds;
    }

    public Long[] getLineDepartureIds() {
        return lineDepartureIds;
    }

    public void setLineDepartureIds(Long[] lineDepartureIds) {
        this.lineDepartureIds = lineDepartureIds;
    }

    public Long[] getLineDestinationIds() {
        return lineDestinationIds;
    }

    public void setLineDestinationIds(Long[] lineDestinationIds) {
        this.lineDestinationIds = lineDestinationIds;
    }

    public Long[] getCarrierIds() {
        return carrierIds;
    }

    public void setCarrierIds(Long[] carrierIds) {
        this.carrierIds = carrierIds;
    }

    public Long[] getDocFlowDirectionIds() {
        return docFlowDirectionIds;
    }

    public void setDocFlowDirectionIds(Long[] docFlowDirectionIds) {
        this.docFlowDirectionIds = docFlowDirectionIds;
    }

    public Long[] getDocIssueTypeIds() {
        return docIssueTypeIds;
    }

    public void setDocIssueTypeIds(Long[] docIssueTypeIds) {
        this.docIssueTypeIds = docIssueTypeIds;
    }

    public String getProcessType() {
        return processType;
    }

    public void setProcessType(String processType) {
        this.processType = processType;
    }

    public void setDocId(Long docId) {
        this.docId = docId;
    }

    public Long getDocId() {
        return docId;
    }

    public void setDocShortName(String docShortName) {
        this.docShortName = docShortName;
    }

    public String getDocShortName() {
        return docShortName;
    }

    public void setDocLocalName(String docLocalName) {
        this.docLocalName = docLocalName;
    }

    public String getDocLocalName() {
        return docLocalName;
    }

    public void setDocEnName(String docEnName) {
        this.docEnName = docEnName;
    }

    public String getDocEnName() {
        return docEnName;
    }

    public void setProcessTypeId(Long processTypeId) {
        this.processTypeId = processTypeId;
    }

    public Long getProcessTypeId() {
        return processTypeId;
    }

    public void setIsGenerated(String isGenerated) {
        this.isGenerated = isGenerated;
    }

    public String getIsGenerated() {
        return isGenerated;
    }

    public void setIsIsolation(String isIsolation) {
        this.isIsolation = isIsolation;
    }

    public String getIsIsolation() {
        return isIsolation;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
}
