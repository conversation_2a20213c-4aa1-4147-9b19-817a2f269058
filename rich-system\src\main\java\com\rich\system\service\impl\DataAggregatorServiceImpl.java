package com.rich.system.service.impl;

import com.rich.system.service.DataAggregatorService;
import com.rich.system.service.RsRctService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 动态数据汇总服务实现
 *
 * <AUTHOR>
 * @date 2024-05-22
 */

/**
 * 数据聚合服务实现类
 * 处理各种数据源的动态分类汇总
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
@Service
public class DataAggregatorServiceImpl implements DataAggregatorService {

    private static final Logger logger = LoggerFactory.getLogger(DataAggregatorServiceImpl.class);

    /**
     * 执行数据聚合
     *
     * @param <T>      数据类型
     * @param dataList 数据列表
     * @param config   汇总配置
     * @return 汇总结果
     */
    @Override
    @SuppressWarnings("unchecked")
    public <T> List<Map<String, Object>> aggregateData(List<T> dataList, Map<String, Object> config) {
        // 解析配置
        String primaryField = (String) config.get("primaryField");
        String dateField = (String) config.get("dateField");
        Map<String, Object> dateOptions = (Map<String, Object>) config.get("dateOptions");
        List<Map<String, Object>> fields = (List<Map<String, Object>>) config.get("fields");
        List<Map<String, Object>> filters = (List<Map<String, Object>>) config.get("filters");
        Boolean showDetails = (Boolean) config.get("showDetails");

        // 应用筛选
        List<T> filteredData = applyFilters(dataList, filters);

        // 根据配置分组
        Map<Object, List<T>> groupedData;

        // 修改分组逻辑
        if (dateField != null && !dateField.isEmpty()) {
            if (primaryField != null && !primaryField.isEmpty()) {
                // 同时有日期字段和主字段时采用组合分组
                groupedData = groupByDateAndPrimaryField(filteredData, primaryField, dateField, dateOptions);
            } else {
                // 只有日期字段时单独按日期分组
                groupedData = groupByDateOnly(filteredData, dateField, dateOptions);
            }
        } else {
            // 没有日期字段时，按主字段分组
            groupedData = groupByField(filteredData, primaryField);
        }

        // 计算聚合结果
        return calculateAggregations(groupedData, fields, showDetails);
    }

    @Override
    public Map<String, Object> extractAggregatorConfig(Object obj) {
        Map<String, Object> aggregatorConfig = new HashMap<>();

        try {
            // 通过反射获取params
            Method getParamsMethod = obj.getClass().getMethod("getParams");
            Object paramsObj = getParamsMethod.invoke(obj);

            if (paramsObj == null) {
                return aggregatorConfig;
            }

            logger.info("接收到的params对象: {}, 类型: {}", paramsObj, paramsObj.getClass().getName());

            if (paramsObj instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> params = (Map<String, Object>) paramsObj;

                // 检查是否有config键
                if (params.containsKey("config")) {
                    Object configObj = params.get("config");
                    logger.info("config键的值类型: {}", configObj != null ? configObj.getClass().getName() : "null");

                    if (configObj instanceof String) {
                        // 如果config是JSON字符串，解析它
                        String configJson = (String) configObj;
                        try {
                            aggregatorConfig = com.alibaba.fastjson2.JSON.parseObject(configJson);
                            logger.info("解析后的配置参数: {}", aggregatorConfig);
                        } catch (Exception e) {
                            logger.error("config JSON解析失败", e);
                            throw new RuntimeException("配置参数解析失败: " + e.getMessage());
                        }
                    } else if (configObj instanceof Map) {
                        // 如果config已经是Map类型，直接使用
                        @SuppressWarnings("unchecked")
                        Map<String, Object> configMap = (Map<String, Object>) configObj;
                        aggregatorConfig = configMap;
                    }
                } else {
                    // 没有config键，尝试直接使用params中的其他键值对
                    // 检查params中是否直接包含汇总需要的参数
                    if (params.containsKey("primaryField")) {
                        aggregatorConfig = params;
                    }
                }
            } else if (paramsObj instanceof String) {
                // 如果整个params是JSON字符串
                String paramsJson = (String) paramsObj;
                try {
                    aggregatorConfig = com.alibaba.fastjson2.JSON.parseObject(paramsJson);
                    logger.info("解析后的配置参数: {}", aggregatorConfig);
                } catch (Exception e) {
                    logger.error("JSON解析失败", e);
                    throw new RuntimeException("参数解析失败: " + e.getMessage());
                }
            }
        } catch (Exception e) {
            if (!(e instanceof RuntimeException)) {
                logger.error("提取聚合配置异常", e);
                throw new RuntimeException("提取聚合配置异常: " + e.getMessage());
            } else {
                throw (RuntimeException) e;
            }
        }

        return aggregatorConfig;
    }

    /**
     * 应用筛选条件
     *
     * @param <T>      数据类型
     * @param dataList 数据列表
     * @param filters  筛选条件
     * @return 筛选后的数据
     */
    @SuppressWarnings("unchecked")
    private <T> List<T> applyFilters(List<T> dataList, List<Map<String, Object>> filters) {
        if (filters == null || filters.isEmpty()) {
            return dataList;
        }

        return dataList.stream()
                .filter(item -> {
                    // 检查每个筛选条件
                    for (Map<String, Object> filter : filters) {
                        String field = (String) filter.get("field");
                        String operator = (String) filter.get("operator");
                        Object value = filter.get("value");

                        if (!matchesFilter(item, field, operator, value)) {
                            return false;
                        }
                    }
                    return true;
                })
                .collect(Collectors.toList());
    }

    /**
     * 检查一个对象是否匹配筛选条件
     *
     * @param <T>      数据类型
     * @param item     数据项
     * @param field    字段名
     * @param operator 操作符
     * @param value    比较值
     * @return 是否匹配
     */
    private <T> boolean matchesFilter(T item, String field, String operator, Object value) {
        try {
            // 获取字段值（通过反射调用getter方法）
            String getterName = "get" + field.substring(0, 1).toUpperCase() + field.substring(1);
            java.lang.reflect.Method getter = item.getClass().getMethod(getterName);
            Object fieldValue = getter.invoke(item);

            // 空值处理
            if (fieldValue == null) {
                return "eq".equals(operator) && (value == null || "".equals(value));
            }

            // 根据操作符进行比较
            switch (operator) {
                case "eq": // 等于
                    return fieldValue.toString().equals(value.toString());
                case "ne": // 不等于
                    return !fieldValue.toString().equals(value.toString());
                case "gt": // 大于
                    if (fieldValue instanceof Number && value instanceof Number) {
                        return ((Number) fieldValue).doubleValue() > ((Number) value).doubleValue();
                    } else if (fieldValue instanceof Comparable) {
                        @SuppressWarnings("unchecked")
                        Comparable<Object> comparableValue = (Comparable<Object>) fieldValue;
                        return comparableValue.compareTo(value) > 0;
                    }
                    return false;
                case "ge": // 大于等于
                    if (fieldValue instanceof Number && value instanceof Number) {
                        return ((Number) fieldValue).doubleValue() >= ((Number) value).doubleValue();
                    } else if (fieldValue instanceof Comparable) {
                        @SuppressWarnings("unchecked")
                        Comparable<Object> comparableValue = (Comparable<Object>) fieldValue;
                        return comparableValue.compareTo(value) >= 0;
                    }
                    return false;
                case "lt": // 小于
                    if (fieldValue instanceof Number && value instanceof Number) {
                        return ((Number) fieldValue).doubleValue() < ((Number) value).doubleValue();
                    } else if (fieldValue instanceof Comparable) {
                        @SuppressWarnings("unchecked")
                        Comparable<Object> comparableValue = (Comparable<Object>) fieldValue;
                        return comparableValue.compareTo(value) < 0;
                    }
                    return false;
                case "le": // 小于等于
                    if (fieldValue instanceof Number && value instanceof Number) {
                        return ((Number) fieldValue).doubleValue() <= ((Number) value).doubleValue();
                    } else if (fieldValue instanceof Comparable) {
                        @SuppressWarnings("unchecked")
                        Comparable<Object> comparableValue = (Comparable<Object>) fieldValue;
                        return comparableValue.compareTo(value) <= 0;
                    }
                    return false;
                case "contains": // 包含
                    return fieldValue.toString().contains(value.toString());
                case "startsWith": // 开始于
                    return fieldValue.toString().startsWith(value.toString());
                case "endsWith": // 结束于
                    return fieldValue.toString().endsWith(value.toString());
                default:
                    return false;
            }
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 按指定字段分组数据
     *
     * @param <T>       数据类型
     * @param dataList  数据列表
     * @param fieldName 分组字段
     * @return 分组结果
     */
    private <T> Map<Object, List<T>> groupByField(List<T> dataList, String fieldName) {
        if (fieldName == null || fieldName.isEmpty()) {
            // 如果未指定分组字段，则所有数据归为一组
            Map<Object, List<T>> result = new HashMap<>();
            result.put("all", dataList);
            return result;
        }

        return dataList.stream()
                .collect(Collectors.groupingBy(item -> {
                    try {
                        // 通过反射获取字段值
                        String getterName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                        java.lang.reflect.Method getter = item.getClass().getMethod(getterName);
                        Object fieldValue = getter.invoke(item);
                        return fieldValue != null ? fieldValue : "未定义";
                    } catch (Exception e) {
                        return "未定义";
                    }
                }));
    }

    /**
     * 按日期字段和主字段分组
     *
     * @param <T>          数据类型
     * @param dataList     数据列表
     * @param primaryField 主分组字段
     * @param dateField    日期分组字段
     * @param dateOptions  日期选项
     * @return 分组结果
     */
    private <T> Map<Object, List<T>> groupByDateAndPrimaryField(
            List<T> dataList,
            String primaryField,
            String dateField,
            Map<String, Object> dateOptions) {

        // 确定日期格式类型
        String formatType = dateOptions != null ? (String) dateOptions.get("formatType") : "day";
        Boolean convertToNumber = dateOptions != null && dateOptions.get("convertToNumber") != null ?
                (Boolean) dateOptions.get("convertToNumber") : false;

        // 确定日期格式
        String dateFormat;
        switch (formatType) {
            case "year":
                dateFormat = "yyyy";
                break;
            case "month":
                dateFormat = "yyyy-MM";
                break;
            default:
                dateFormat = "yyyy-MM-dd";
        }

        // 格式化日期的函数
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat);

        return dataList.stream()
                .collect(Collectors.groupingBy(item -> {
                    try {
                        Object primaryValue;
                        if (primaryField != null && !primaryField.isEmpty()) {
                            // 获取主字段值
                            String primaryGetterName = "get" + primaryField.substring(0, 1).toUpperCase()
                                    + primaryField.substring(1);
                            java.lang.reflect.Method primaryGetter = item.getClass().getMethod(primaryGetterName);
                            primaryValue = primaryGetter.invoke(item);
                        } else {
                            primaryValue = "all";
                        }

                        // 获取日期字段值
                        String dateGetterName = "get" + dateField.substring(0, 1).toUpperCase() + dateField.substring(1);
                        java.lang.reflect.Method dateGetter = item.getClass().getMethod(dateGetterName);
                        Object dateValue = dateGetter.invoke(item);

                        // 格式化日期
                        String formattedDate = "";
                        if (dateValue instanceof Date) {
                            LocalDateTime localDateTime = ((Date) dateValue).toInstant()
                                    .atZone(ZoneId.systemDefault())
                                    .toLocalDateTime();
                            formattedDate = formatter.format(localDateTime);
                        } else if (dateValue instanceof LocalDateTime) {
                            formattedDate = formatter.format((LocalDateTime) dateValue);
                        } else if (dateValue instanceof LocalDate) {
                            formattedDate = formatter.format((LocalDate) dateValue);
                        } else if (dateValue instanceof String && dateValue.toString().matches("\\d{4}-\\d{2}-\\d{2}.*")) {
                            // 处理字符串格式的日期
                            try {
                                LocalDateTime dateTime = LocalDateTime.parse(dateValue.toString().substring(0, 19),
                                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                                formattedDate = formatter.format(dateTime);
                            } catch (Exception e) {
                                try {
                                    // 尝试解析为LocalDate格式
                                    LocalDate date = LocalDate.parse(dateValue.toString().substring(0, 10),
                                            DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                                    formattedDate = formatter.format(date);
                                } catch (Exception ex) {
                                    // 解析失败时直接使用字符串
                                    formattedDate = dateValue.toString();
                                }
                            }
                        } else if (dateValue != null) {
                            formattedDate = dateValue.toString();
                        }

                        // 如果需要转换为数字格式
                        if (convertToNumber && !formattedDate.isEmpty()) {
                            formattedDate = formattedDate.replaceAll("-", "");
                        }

                        // 创建复合键
                        Map<String, Object> compositeKey = new HashMap<>();
                        compositeKey.put("primary", primaryValue != null ? primaryValue : "未定义");
                        compositeKey.put("date", formattedDate);

                        return compositeKey;
                    } catch (Exception e) {
                        Map<String, Object> compositeKey = new HashMap<>();
                        compositeKey.put("primary", "未定义");
                        compositeKey.put("date", "未定义");
                        return compositeKey;
                    }
                }));
    }

    /**
     * 仅按日期字段分组
     *
     * @param <T>         数据类型
     * @param dataList    数据列表
     * @param dateField   日期字段
     * @param dateOptions 日期选项
     * @return 分组结果
     */
    private <T> Map<Object, List<T>> groupByDateOnly(
            List<T> dataList,
            String dateField,
            Map<String, Object> dateOptions) {

        // 确定日期格式类型
        String formatType = dateOptions != null ? (String) dateOptions.get("formatType") : "day";
        Boolean convertToNumber = dateOptions != null && dateOptions.get("convertToNumber") != null ?
                (Boolean) dateOptions.get("convertToNumber") : false;

        // 确定日期格式
        String dateFormat;
        switch (formatType) {
            case "year":
                dateFormat = "yyyy";
                break;
            case "month":
                dateFormat = "yyyy-MM";
                break;
            default:
                dateFormat = "yyyy-MM-dd";
        }

        // 格式化日期的函数
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat);

        Map<Object, List<T>> result = new LinkedHashMap<>();

        for (T item : dataList) {
            try {
                // 获取日期字段值
                String dateGetterName = "get" + dateField.substring(0, 1).toUpperCase() + dateField.substring(1);
                java.lang.reflect.Method dateGetter = item.getClass().getMethod(dateGetterName);
                Object dateValue = dateGetter.invoke(item);

                // 格式化日期
                String formattedDate = "";
                if (dateValue instanceof Date) {
                    LocalDateTime localDateTime = ((Date) dateValue).toInstant()
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                    formattedDate = formatter.format(localDateTime);
                } else if (dateValue instanceof LocalDateTime) {
                    formattedDate = formatter.format((LocalDateTime) dateValue);
                } else if (dateValue instanceof LocalDate) {
                    formattedDate = formatter.format((LocalDate) dateValue);
                } else if (dateValue instanceof String && dateValue.toString().matches("\\d{4}-\\d{2}-\\d{2}.*")) {
                    // 处理字符串格式的日期
                    try {
                        LocalDateTime dateTime = LocalDateTime.parse(dateValue.toString().substring(0, 19),
                                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        formattedDate = formatter.format(dateTime);
                    } catch (Exception e) {
                        try {
                            // 尝试解析为LocalDate格式
                            LocalDate date = LocalDate.parse(dateValue.toString().substring(0, 10),
                                    DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                            formattedDate = formatter.format(date);
                        } catch (Exception ex) {
                            // 解析失败时直接使用字符串
                            formattedDate = dateValue.toString();
                        }
                    }
                } else if (dateValue != null) {
                    formattedDate = dateValue.toString();
                }

                // 如果需要转换为数字格式
                if (convertToNumber && !formattedDate.isEmpty()) {
                    formattedDate = formattedDate.replaceAll("-", "");
                }

                // 直接使用格式化的日期作为key
                result.computeIfAbsent(formattedDate, k -> new ArrayList<>()).add(item);
            } catch (Exception e) {
                // 异常情况
                result.computeIfAbsent("未定义", k -> new ArrayList<>()).add(item);
            }
        }

        return result;
    }

    /**
     * 计算聚合值
     *
     * @param <T>         数据类型
     * @param groupedData 分组后的数据
     * @param fields      字段配置
     * @param showDetails 是否显示明细
     * @return 聚合结果
     */
    private <T> List<Map<String, Object>> calculateAggregations(
            Map<Object, List<T>> groupedData,
            List<Map<String, Object>> fields,
            Boolean showDetails) {

        List<Map<String, Object>> result = new ArrayList<>();

        // 处理每个分组
        for (Map.Entry<Object, List<T>> entry : groupedData.entrySet()) {
            Object groupKey = entry.getKey();
            List<T> groupItems = entry.getValue();

            // 使用LinkedHashMap保持字段插入顺序
            Map<String, Object> groupResult = new LinkedHashMap<>();
            groupResult.put("groupKey", groupKey);

            // 按字段配置顺序处理每个字段的聚合
            for (Map<String, Object> field : fields) {
                String fieldKey = (String) field.get("fieldKey");
                String aggregation = (String) field.get("aggregation");

                // 获取字段值列表
                List<Object> fieldValues = getFieldValues(groupItems, fieldKey);

                // 根据聚合方式计算结果，保持字段顺序
                if ("none".equals(aggregation) || aggregation == null) {
                    // 不聚合，使用第一个值
                    groupResult.put(fieldKey, fieldValues.isEmpty() ? null : fieldValues.get(0));
                } else {
                    // 执行聚合计算
                    Object aggregateValue = calculateAggregateValue(fieldValues, aggregation);
                    groupResult.put(fieldKey + "_" + aggregation, aggregateValue);
                }
            }

            // 如果需要显示明细，也包含原始数据
            if (showDetails != null && showDetails) {
                groupResult.put("details", groupItems);
            }

            result.add(groupResult);
        }

        // 根据字段配置进行排序
        sortResults(result, fields);

        return result;
    }

    /**
     * 获取指定字段的值列表
     *
     * @param <T>       数据类型
     * @param items     数据项列表
     * @param fieldName 字段名
     * @return 值列表
     */
    private <T> List<Object> getFieldValues(List<T> items, String fieldName) {
        return items.stream()
                .map(item -> {
                    try {
                        // 通过反射获取字段值
                        String getterName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                        java.lang.reflect.Method getter = item.getClass().getMethod(getterName);
                        return getter.invoke(item);
                    } catch (Exception e) {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 计算聚合值
     *
     * @param values      值列表
     * @param aggregation 聚合方式
     * @return 聚合结果
     */
    private Object calculateAggregateValue(List<Object> values, String aggregation) {
        if (values.isEmpty()) {
            return null;
        }

        // 对于计数，直接返回值列表的大小
        if ("count".equals(aggregation)) {
            return values.size();
        }

        // 过滤出数值类型的值
        List<Number> numberValues = values.stream()
                .filter(v -> v instanceof Number)
                .map(v -> (Number) v)
                .collect(Collectors.toList());

        if (numberValues.isEmpty()) {
            // 如果不是数值，则根据聚合方式处理
            switch (aggregation) {
                case "distinct": // 去重计数
                    return values.stream().distinct().count();
                default:
                    return null;
            }
        }

        // 数值类型的聚合计算
        switch (aggregation) {
            case "sum": // 求和
                double sum = numberValues.stream()
                        .mapToDouble(Number::doubleValue)
                        .sum();
                return new BigDecimal(sum).setScale(2, RoundingMode.HALF_UP);

            case "avg": // 平均值
                double avg = numberValues.stream()
                        .mapToDouble(Number::doubleValue)
                        .average()
                        .orElse(0);
                return new BigDecimal(avg).setScale(2, RoundingMode.HALF_UP);

            case "max": // 最大值
                double max = numberValues.stream()
                        .mapToDouble(Number::doubleValue)
                        .max()
                        .orElse(0);
                return new BigDecimal(max).setScale(2, RoundingMode.HALF_UP);

            case "min": // 最小值
                double min = numberValues.stream()
                        .mapToDouble(Number::doubleValue)
                        .min()
                        .orElse(0);
                return new BigDecimal(min).setScale(2, RoundingMode.HALF_UP);

            case "variance": // 方差
                double mean = numberValues.stream()
                        .mapToDouble(Number::doubleValue)
                        .average()
                        .orElse(0);

                double variance = numberValues.stream()
                        .mapToDouble(v -> Math.pow(v.doubleValue() - mean, 2))
                        .average()
                        .orElse(0);

                return new BigDecimal(variance).setScale(2, RoundingMode.HALF_UP);

            default:
                return null;
        }
    }

    /**
     * 根据字段配置对结果进行排序
     *
     * @param results 结果列表
     * @param fields  字段配置
     */
    private void sortResults(List<Map<String, Object>> results, List<Map<String, Object>> fields) {
        // 查找第一个带有排序的字段
        if (fields == null || fields.isEmpty()) {
            // 没有字段配置时，默认按分组键排序
            results.sort((m1, m2) -> compareGroupKeys(m1.get("groupKey"), m2.get("groupKey")));
            return;
        }

        Optional<Map<String, Object>> sortField = fields.stream()
                .filter(field -> {
                    String sort = (String) field.get("sort");
                    return sort != null && !"none".equals(sort);
                })
                .findFirst();

        if (sortField.isPresent()) {
            String fieldKey = (String) sortField.get().get("fieldKey");
            String aggregation = (String) sortField.get().get("aggregation");
            String sort = (String) sortField.get().get("sort");

            // 确定排序键
            String sortKey = "none".equals(aggregation) || aggregation == null
                    ? fieldKey
                    : fieldKey + "_" + aggregation;

            // 按指定字段排序
            Comparator<Map<String, Object>> comparator = (m1, m2) -> {
                Object value1 = m1.get(sortKey);
                Object value2 = m2.get(sortKey);

                // 处理null值情况
                if (value1 == null && value2 == null) return 0;
                if (value1 == null) return "asc".equals(sort) ? -1 : 1;
                if (value2 == null) return "asc".equals(sort) ? 1 : -1;

                // 根据类型比较
                if (value1 instanceof Number && value2 instanceof Number) {
                    return "asc".equals(sort)
                            ? Double.compare(((Number) value1).doubleValue(), ((Number) value2).doubleValue())
                            : Double.compare(((Number) value2).doubleValue(), ((Number) value1).doubleValue());
                } else {
                    String str1 = value1.toString();
                    String str2 = value2.toString();
                    return "asc".equals(sort) ? str1.compareTo(str2) : str2.compareTo(str1);
                }
            };

            // 执行排序
            results.sort(comparator);
        } else {
            // 没有排序配置时，默认按分组键排序（升序）
            results.sort((m1, m2) -> compareGroupKeys(m1.get("groupKey"), m2.get("groupKey")));
        }
    }

    /**
     * 比较两个分组键
     *
     * @param key1 第一个分组键
     * @param key2 第二个分组键
     * @return 比较结果
     */
    @SuppressWarnings("unchecked")
    private int compareGroupKeys(Object key1, Object key2) {
        // 处理null值情况
        if (key1 == null && key2 == null) return 0;
        if (key1 == null) return -1;
        if (key2 == null) return 1;

        // 处理复合键（日期+主键）
        if (key1 instanceof Map && key2 instanceof Map) {
            Map<String, Object> map1 = (Map<String, Object>) key1;
            Map<String, Object> map2 = (Map<String, Object>) key2;

            // 先比较日期字段
            if (map1.containsKey("date") && map2.containsKey("date")) {
                Object date1 = map1.get("date");
                Object date2 = map2.get("date");

                if (date1 == null && date2 == null) return 0;
                if (date1 == null) return -1;
                if (date2 == null) return 1;

                int dateComparison = String.valueOf(date1).compareTo(String.valueOf(date2));
                if (dateComparison != 0) {
                    return dateComparison;
                }
            }

            // 日期相同时比较主字段
            if (map1.containsKey("primary") && map2.containsKey("primary")) {
                Object primary1 = map1.get("primary");
                Object primary2 = map2.get("primary");

                if (primary1 == null && primary2 == null) return 0;
                if (primary1 == null) return -1;
                if (primary2 == null) return 1;

                if (primary1 instanceof Number && primary2 instanceof Number) {
                    return Double.compare(((Number) primary1).doubleValue(), ((Number) primary2).doubleValue());
                } else {
                    return String.valueOf(primary1).compareTo(String.valueOf(primary2));
                }
            }

            // 如果没有比较出结果，则比较Map的大小
            return Integer.compare(map1.size(), map2.size());
        }

        // 简单键比较
        if (key1 instanceof Number && key2 instanceof Number) {
            return Double.compare(((Number) key1).doubleValue(), ((Number) key2).doubleValue());
        }

        return String.valueOf(key1).compareTo(String.valueOf(key2));
    }
}
