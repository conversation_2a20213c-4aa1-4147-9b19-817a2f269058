package com.rich.common.core.domain.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 合约类别对象 bas_contract_type
 * 
 * <AUTHOR>
 * @date 2023-01-13
 */
public class BasContractType extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 合同类别 */
    private Long contractTypeId;

    /** 简称 */
    @Excel(name = "简称")
    private String contractShortName;

    /** 中文名 */
    @Excel(name = "中文名")
    private String contractLocalName;

    /** 英文名 */
    @Excel(name = "英文名")
    private String contractEnName;

    /** 排序 */
    @Excel(name = "排序")
    private Integer orderNum;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 删除人 */
    private String deleteBy;

    /** 删除时间 */
    private Date deleteTime;

    /** 数据状态（-1:删除或不可用，0：正常） */
    private String deleteStatus;

    public void setContractTypeId(Long contractTypeId) 
    {
        this.contractTypeId = contractTypeId;
    }

    public Long getContractTypeId() 
    {
        return contractTypeId;
    }
    public void setContractShortName(String contractShortName) 
    {
        this.contractShortName = contractShortName;
    }

    public String getContractShortName() 
    {
        return contractShortName;
    }
    public void setContractLocalName(String contractLocalName) 
    {
        this.contractLocalName = contractLocalName;
    }

    public String getContractLocalName() 
    {
        return contractLocalName;
    }
    public void setContractEnName(String contractEnName) 
    {
        this.contractEnName = contractEnName;
    }

    public String getContractEnName() 
    {
        return contractEnName;
    }
    public void setOrderNum(Integer orderNum) 
    {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() 
    {
        return orderNum;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("contractTypeId", getContractTypeId())
            .append("contractShortName", getContractShortName())
            .append("contractLocalName", getContractLocalName())
            .append("contractEnName", getContractEnName())
            .append("orderNum", getOrderNum())
            .append("status", getStatus())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("deleteBy", getDeleteBy())
            .append("deleteTime", getDeleteTime())
            .append("deleteStatus", getDeleteStatus())
            .toString();
    }
}
