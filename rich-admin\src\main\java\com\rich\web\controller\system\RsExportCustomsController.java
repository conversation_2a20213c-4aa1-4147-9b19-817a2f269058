package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsExportCustoms;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsExportCustomsService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 出口报关Controller
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@RestController
@RequestMapping("/system/exportcustoms")
public class RsExportCustomsController extends BaseController {
    @Autowired
    private RsExportCustomsService rsExportCustomsService;

    /**
     * 查询出口报关列表
     */
    @PreAuthorize("@ss.hasPermi('system:exportcustoms:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsExportCustoms rsExportCustoms) {
        startPage();
        List<RsExportCustoms> list = rsExportCustomsService.selectRsExportCustomsList(rsExportCustoms);
        return getDataTable(list);
    }

    /**
     * 导出出口报关列表
     */
    @PreAuthorize("@ss.hasPermi('system:exportcustoms:export')")
    @Log(title = "出口报关", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsExportCustoms rsExportCustoms) {
        List<RsExportCustoms> list = rsExportCustomsService.selectRsExportCustomsList(rsExportCustoms);
        ExcelUtil<RsExportCustoms> util = new ExcelUtil<RsExportCustoms>(RsExportCustoms.class);
        util.exportExcel(response, list, "出口报关数据");
    }

    /**
     * 获取出口报关详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:exportcustoms:query')")
    @GetMapping(value = "/{exportCustomsId}")
    public AjaxResult getInfo(@PathVariable("exportCustomsId") Long exportCustomsId) {
        return AjaxResult.success(rsExportCustomsService.selectRsExportCustomsByExportCustomsId(exportCustomsId));
    }

    /**
     * 新增出口报关
     */
    @PreAuthorize("@ss.hasPermi('system:exportcustoms:add')")
    @Log(title = "出口报关", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsExportCustoms rsExportCustoms) {
        return toAjax(rsExportCustomsService.insertRsExportCustoms(rsExportCustoms));
    }

    /**
     * 修改出口报关
     */
    @PreAuthorize("@ss.hasPermi('system:exportcustoms:edit')")
    @Log(title = "出口报关", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsExportCustoms rsExportCustoms) {
        return toAjax(rsExportCustomsService.updateRsExportCustoms(rsExportCustoms));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:exportcustoms:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsExportCustoms rsExportCustoms) {
        rsExportCustoms.setUpdateBy(getUserId());
        return toAjax(rsExportCustomsService.changeStatus(rsExportCustoms));
    }

    /**
     * 删除出口报关
     */
    @PreAuthorize("@ss.hasPermi('system:exportcustoms:remove')")
    @Log(title = "出口报关", businessType = BusinessType.DELETE)
    @DeleteMapping("/{exportCustomsIds}")
    public AjaxResult remove(@PathVariable Long[] exportCustomsIds) {
        return toAjax(rsExportCustomsService.deleteRsExportCustomsByExportCustomsIds(exportCustomsIds));
    }
}
