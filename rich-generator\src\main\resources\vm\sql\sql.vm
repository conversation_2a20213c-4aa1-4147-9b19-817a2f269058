-- 菜单 SQL
insert into sys_menu (menu_name, parent_id , ancestors , order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms,perms_type, icon, create_by, create_time, update_by, update_time, remark)
values('${functionName}', '${parentMenuId}',concat('${ancestors}',',','${parentMenuId}'), '1', '${businessName}', '${moduleName}/${businessName}/index', 1, 0, 'C', '0', '0', '${permissionPrefix}:list','1', '#', '1', sysdate(), '1', null, '${functionName}菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, ancestors , order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms,perms_type, icon, create_by, create_time, update_by, update_time, remark)
values('${functionName}查询', @parentId,concat('${ancestors},','${parentMenuId},',@parentId), '1',  '#', '', 1, 0, 'F', '0', '0', '${permissionPrefix}:query','2', '#', '1', sysdate(),'1', null, '');

insert into sys_menu (menu_name, parent_id, ancestors , order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms,perms_type, icon, create_by, create_time, update_by, update_time, remark)
values('${functionName}新增', @parentId,concat('${ancestors},','${parentMenuId},',@parentId), '2',  '#', '', 1, 0, 'F', '0', '0', '${permissionPrefix}:add','3','#', '1', sysdate(),'1', null, '');

insert into sys_menu (menu_name, parent_id, ancestors , order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms,perms_type, icon, create_by, create_time, update_by, update_time, remark)
values('${functionName}修改', @parentId,concat('${ancestors},','${parentMenuId},',@parentId), '3',  '#', '', 1, 0, 'F', '0', '0', '${permissionPrefix}:edit','3','#', '1', sysdate(),'1', null, '');

insert into sys_menu (menu_name, parent_id, ancestors , order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms,perms_type, icon, create_by, create_time, update_by, update_time, remark)
values('${functionName}删除', @parentId,concat('${ancestors},','${parentMenuId},',@parentId), '4',  '#', '', 1, 0, 'F', '0', '0', '${permissionPrefix}:remove','3','#', '1', sysdate(),'1', null, '');

insert into sys_menu (menu_name, parent_id, ancestors , order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms,perms_type, icon, create_by, create_time, update_by, update_time, remark)
values('${functionName}部门内部锁定', @parentId,concat('${ancestors},','${parentMenuId},',@parentId), '5',  '#', '', 1, 0, 'F', '0', '0', '${permissionPrefix}:deptlock','4','#', '1', sysdate(),'1', null, '');

insert into sys_menu (menu_name, parent_id, ancestors , order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms,perms_type, icon, create_by, create_time, update_by, update_time, remark)
values('${functionName}部门内部解锁', @parentId,concat('${ancestors},','${parentMenuId},',@parentId), '6',  '#', '', 1, 0, 'F', '0', '0', '${permissionPrefix}:deptunlock','5','#', '1', sysdate(), '1', null, '');

insert into sys_menu (menu_name, parent_id, ancestors , order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms,perms_type, icon, create_by, create_time, update_by, update_time, remark)
values('${functionName}财务锁定', @parentId,concat('${ancestors},','${parentMenuId},',@parentId), '7',  '#', '', 1, 0, 'F', '0', '0', '${permissionPrefix}:financelock','6','#', '1', sysdate(),'1', null, '');

insert into sys_menu (menu_name, parent_id, ancestors , order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms,perms_type, icon, create_by, create_time, update_by, update_time, remark)
values('${functionName}财务解锁', @parentId,concat('${ancestors},','${parentMenuId},',@parentId), '8',  '#', '', 1, 0, 'F', '0', '0', '${permissionPrefix}:financeunlock','7','#', '1', sysdate(),'1', null, '');

insert into sys_menu (menu_name, parent_id, ancestors , order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms,perms_type, icon, create_by, create_time, update_by, update_time, remark)
values('${functionName}导入', @parentId,concat('${ancestors},','${parentMenuId},',@parentId), '9',  '#', '', 1, 0, 'F', '0', '0', '${permissionPrefix}:import','8','#', '1', sysdate(), '1', null, '');

insert into sys_menu (menu_name, parent_id, ancestors , order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms,perms_type, icon, create_by, create_time, update_by, update_time, remark)
values('${functionName}导出', @parentId,concat('${ancestors},','${parentMenuId},',@parentId), '10',  '#', '', 1, 0, 'F', '0', '0', '${permissionPrefix}:export','9','#', '1', sysdate(),'1', null, '');