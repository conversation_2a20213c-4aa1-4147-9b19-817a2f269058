package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsOpSeaLcl;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsOpSeaLclService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 拼柜海运服务Controller
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@RestController
@RequestMapping("/system/opsealcl")
public class RsOpSeaLclController extends BaseController {
    @Autowired
    private RsOpSeaLclService rsOpSeaLclService;

    /**
     * 查询拼柜海运服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opsealcl:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsOpSeaLcl rsOpSeaLcl) {
        startPage();
        List<RsOpSeaLcl> list = rsOpSeaLclService.selectRsOpSeaLclList(rsOpSeaLcl);
        return getDataTable(list);
    }

    /**
     * 导出拼柜海运服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opsealcl:export')")
    @Log(title = "拼柜海运服务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsOpSeaLcl rsOpSeaLcl) {
        List<RsOpSeaLcl> list = rsOpSeaLclService.selectRsOpSeaLclList(rsOpSeaLcl);
        ExcelUtil<RsOpSeaLcl> util = new ExcelUtil<RsOpSeaLcl>(RsOpSeaLcl.class);
        util.exportExcel(response, list, "拼柜海运服务数据");
    }

    /**
     * 获取拼柜海运服务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:opsealcl:query')")
    @GetMapping(value = "/{seaLclId}")
    public AjaxResult getInfo(@PathVariable("seaLclId") Long seaLclId) {
        return AjaxResult.success(rsOpSeaLclService.selectRsOpSeaLclBySeaLclId(seaLclId));
    }

    /**
     * 新增拼柜海运服务
     */
    @PreAuthorize("@ss.hasPermi('system:opsealcl:add')")
    @Log(title = "拼柜海运服务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsOpSeaLcl rsOpSeaLcl) {
        return toAjax(rsOpSeaLclService.insertRsOpSeaLcl(rsOpSeaLcl));
    }

    /**
     * 修改拼柜海运服务
     */
    @PreAuthorize("@ss.hasPermi('system:opsealcl:edit')")
    @Log(title = "拼柜海运服务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsOpSeaLcl rsOpSeaLcl) {
        return toAjax(rsOpSeaLclService.updateRsOpSeaLcl(rsOpSeaLcl));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:opsealcl:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsOpSeaLcl rsOpSeaLcl) {
        rsOpSeaLcl.setUpdateBy(getUserId());
        return toAjax(rsOpSeaLclService.changeStatus(rsOpSeaLcl));
    }

    /**
     * 删除拼柜海运服务
     */
    @PreAuthorize("@ss.hasPermi('system:opsealcl:remove')")
    @Log(title = "拼柜海运服务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{seaLclIds}")
    public AjaxResult remove(@PathVariable Long[] seaLclIds) {
        return toAjax(rsOpSeaLclService.deleteRsOpSeaLclBySeaLclIds(seaLclIds));
    }
}
