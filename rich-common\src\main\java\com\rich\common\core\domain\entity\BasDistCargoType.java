package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 bas_dist_cargo_type
 *
 * <AUTHOR>
 * @date 2022-08-29
 */
public class BasDistCargoType extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long cargoTypeId;

    /**
     * 父ID
     */
    @Excel(name = "父ID")
    private Long parentId;

    private String ancestors;

    /**
     * 装箱类型名缩写
     */
    @Excel(name = "装箱类型名缩写")
    private String cargoTypeShortName;

    /**
     * 装箱类型中文名
     */
    @Excel(name = "装箱类型中文名")
    private String cargoTypeLocalName;

    /**
     * 装箱类型英文名
     */
    @Excel(name = "装箱类型英文名")
    private String cargoTypeEnName;

    /**
     * 货物特征等级
     */
    @Excel(name = "货物特征等级")
    private Long cargoTypeLevel;

    /**
     * 是否上锁
     */
    @Excel(name = "是否上锁")
    private String isLocked;

    /**
     * 上下层排序
     */
    @Excel(name = "上下层排序")
    private Long verticalSort;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Long orderNum;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;

    private String cargoTypeQuery;
    private String featureType;

    public String getFeatureType() {
        return featureType;
    }

    public void setFeatureType(String featureType) {
        this.featureType = featureType;
    }

    public String getCargoTypeQuery() {
        return cargoTypeQuery;
    }

    public void setCargoTypeQuery(String cargoTypeQuery) {
        this.cargoTypeQuery = cargoTypeQuery;
    }

    public String getAncestors() {
        return ancestors;
    }

    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }

    public void setCargoTypeId(Long cargoTypeId) {
        this.cargoTypeId = cargoTypeId;
    }

    public Long getCargoTypeId() {
        return cargoTypeId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setCargoTypeShortName(String cargoTypeShortName) {
        this.cargoTypeShortName = cargoTypeShortName;
    }

    public String getCargoTypeShortName() {
        return cargoTypeShortName;
    }

    public void setCargoTypeLocalName(String cargoTypeLocalName) {
        this.cargoTypeLocalName = cargoTypeLocalName;
    }

    public String getCargoTypeLocalName() {
        return cargoTypeLocalName;
    }

    public void setCargoTypeEnName(String cargoTypeEnName) {
        this.cargoTypeEnName = cargoTypeEnName;
    }

    public String getCargoTypeEnName() {
        return cargoTypeEnName;
    }

    public void setCargoTypeLevel(Long cargoTypeLevel) {
        this.cargoTypeLevel = cargoTypeLevel;
    }

    public Long getCargoTypeLevel() {
        return cargoTypeLevel;
    }

    public void setIsLocked(String isLocked) {
        this.isLocked = isLocked;
    }

    public String getIsLocked() {
        return isLocked;
    }

    public void setVerticalSort(Long verticalSort) {
        this.verticalSort = verticalSort;
    }

    public Long getVerticalSort() {
        return verticalSort;
    }

    public void setOrderNum(Long orderNum) {
        this.orderNum = orderNum;
    }

    public Long getOrderNum() {
        return orderNum;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

}
