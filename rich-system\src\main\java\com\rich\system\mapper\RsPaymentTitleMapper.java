package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsPaymentTitle;
import org.apache.ibatis.annotations.Mapper;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-11-29
 */
@Mapper
public interface RsPaymentTitleMapper {
    /**
     * 查询【请填写功能名称】
     *
     * @param code 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    RsPaymentTitle selectRsPaymentTitleByCode(String code);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param rsPaymentTitle 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    List<RsPaymentTitle> selectRsPaymentTitleList(RsPaymentTitle rsPaymentTitle);

    /**
     * 新增【请填写功能名称】
     *
     * @param rsPaymentTitle 【请填写功能名称】
     * @return 结果
     */
    int insertRsPaymentTitle(RsPaymentTitle rsPaymentTitle);

    /**
     * 修改【请填写功能名称】
     *
     * @param rsPaymentTitle 【请填写功能名称】
     * @return 结果
     */
    int updateRsPaymentTitle(RsPaymentTitle rsPaymentTitle);

    /**
     * 删除【请填写功能名称】
     *
     * @param code 【请填写功能名称】主键
     * @return 结果
     */
    int deleteRsPaymentTitleByCode(String code);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param codes 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsPaymentTitleByCodes(String[] codes);
}
