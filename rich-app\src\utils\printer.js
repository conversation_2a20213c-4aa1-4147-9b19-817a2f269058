/**
 * 打印服务，提供蓝牙打印机相关功能
 */
import store from '@/store'
import tsc from '@/print/gprint/tsc.js'
import esc from '@/print/gprint/esc.js'
import defaultTemplates from '@/print/template/default-templates.js'

/**
 * 检查打印机是否已连接并准备好
 * @returns {Object} 包含状态和消息的对象
 */
export function checkPrinterReady() {
    const currDev = store.getters.currentBluetoothDevice

    if (!currDev) {
        return {ready: false, message: '请先连接打印机'}
    }

    if (!currDev.services || currDev.services.length === 0) {
        return {ready: false, message: '设备服务未就绪'}
    }

    return {ready: true, message: '打印机已就绪'}
}

/**
 * 获取字体名称
 * @param {Number} index 字体索引
 * @returns {String} 字体名称
 */
export function getFontName(index) {
    const fonts = ['TSS24.BF2', '8x12', '12x20', '16x24', '20x24', '24x32', '28x48', 'ARIAL.TTF']
    return fonts[index] || fonts[0]
}

/**
 * 获取字体大小
 * @param {Number} index 大小索引
 * @returns {Object} 包含x和y的对象
 */
export function getFontSize(index) {
    const sizes = [
        {x: 1, y: 1},
        {x: 1, y: 2},
        {x: 2, y: 1},
        {x: 2, y: 2},
        {x: 3, y: 3},
        {x: 4, y: 4},
        {x: 5, y: 5},
        {x: 6, y: 6}
    ]
    return sizes[index] || sizes[0]
}

/**
 * 获取条形码类型
 * @param {Number} index 类型索引
 * @returns {String} 条形码类型
 */
export function getBarcodeType(index) {
    const types = ['128', '39', '93', 'EAN8', 'EAN13', 'UPC-A', 'UPC-E', 'ITF', 'Codabar']
    return types[index] || types[0]
}

/**
 * 获取二维码等级
 * @param {Number} index 等级索引
 * @returns {String} 二维码等级
 */
export function getQRLevel(index) {
    const levels = ['L', 'M', 'Q', 'H']
    return levels[index] || levels[0]
}

/**
 * 加载标签模板
 * @param {String} templateId 模板ID，如果不传则返回默认模板
 * @param {Boolean} useDefaultJson 是否使用default-templates.json中的默认模板
 * @returns {Object} 模板对象
 */
export function loadTemplate(templateId, useDefaultJson = false) {
    // 如果指定使用默认JSON模板
    if (useDefaultJson && defaultTemplates && defaultTemplates.length > 0) {
        return defaultTemplates[0]
    }

    try {
        const templates = JSON.parse(uni.getStorageSync('label_templates') || '[]')

        if (templates.length === 0) {
            // 如果本地无模板，则使用default-templates.json中的默认模板
            if (defaultTemplates && defaultTemplates.length > 0) {
                return defaultTemplates[0]
            }
            return null
        }

        if (templateId) {
            return templates.find(item => item.id === templateId) || templates[0]
        }

        // 获取默认模板
        const defaultTemplateId = uni.getStorageSync('default_template_id')
        if (defaultTemplateId) {
            const defaultTemplate = templates.find(item => item.id === defaultTemplateId)
            if (defaultTemplate) {
                return defaultTemplate
            }
        }

        // 没有默认模板则返回第一个
        return templates[0]
    } catch (e) {
        console.error('加载模板失败', e)
        // 如果出错，尝试使用default-templates.json中的默认模板
        if (defaultTemplates && defaultTemplates.length > 0) {
            return defaultTemplates[0]
        }
        return null
    }
}

/**
 * 打印标签
 * @param {Object} options 打印选项
 * @param {Object} options.template 标签模板，如不传则使用默认模板
 * @param {Boolean} options.useDefaultJson 是否使用default-templates.json中的默认模板
 * @param {Object} options.data 打印数据，用于替换模板中的占位符
 * @param {Function} options.onSuccess 打印成功回调
 * @param {Function} options.onError 打印失败回调
 * @returns {Promise} 打印结果的Promise
 */
export function printLabel(options = {}) {
    // 检查打印机状态
    const printerStatus = checkPrinterReady()
    if (!printerStatus.ready) {
        if (options.onError) {
            options.onError(printerStatus.message)
        }
        return Promise.reject(new Error(printerStatus.message))
    }

    // 获取模板
    const template = options.template || loadTemplate(null, options.useDefaultJson)
    if (!template) {
        const message = '请先选择或创建标签模板'
        if (options.onError) {
            options.onError(message)
        }
        return Promise.reject(new Error(message))
    }

    try {
        const currDev = store.getters.currentBluetoothDevice
        const deviceId = currDev.deviceId
        const serviceId = currDev.services[0].serviceId
        const characteristicId = currDev.services[0].characteristicId

        // 创建TSC打印命令
        const command = tsc.jpPrinter.createNew()

        // 设置标签大小和间距
        command.setSize(template.width, template.height)
        command.setGap(template.gap || 2)
        command.setCls()

        // 添加打印机DPI设置（一般为203DPI）
        const DPI = 203; // 打印机分辨率，通常为203DPI

        /**
         * 坐标单位转换函数 - 将毫米转换为点
         * @param {Number} mm 毫米值
         * @returns {Number} 点值
         */
        const mmToDots = (mm) => {
            return Math.round(mm * DPI / 25.4); // 1英寸=25.4毫米，1英寸=DPI点
        };

        // 处理文本元素
        if (template.elements.text && template.elements.text.length > 0) {
            template.elements.text.forEach(item => {
                const rotation = item.rotationIndex ? item.rotationIndex * 90 : 0
                const fontName = getFontName(item.fontIndex)
                const fontSize = getFontSize(item.sizeIndex)

                // 支持数据替换，如果有传入数据并且内容中包含占位符，则替换
                let content = item.content || ''
                if (options.data && typeof content === 'string') {
                    // 使用正则表达式替换所有 ${key} 形式的占位符
                    content = content.replace(/\${([\w.]+)}/g, (match, key) => {
                        // 支持多级属性，如 ${user.name}
                        const value = key.split('.').reduce((obj, prop) => obj && obj[prop], options.data)
                        return value !== undefined ? String(value) : match
                    })
                }

                // 确保内容为字符串
                if (content === undefined || content === null) {
                    content = '';
                } else if (typeof content !== 'string') {
                    content = String(content);
                }

                // 坐标转换
                const x = mmToDots(parseInt(item.x));
                const y = mmToDots(parseInt(item.y));

                command.setText(
                    x,
                    y,
                    fontName,
                    parseInt(fontSize.x),
                    parseInt(fontSize.y),
                    content
                )
            })
        }

        // 处理条形码元素
        if (template.elements.barcode && template.elements.barcode.length > 0) {
            template.elements.barcode.forEach(item => {
                const barcodeType = getBarcodeType(item.typeIndex)

                // 支持数据替换
                let content = item.content || ''
                if (options.data && typeof content === 'string') {
                    content = content.replace(/\${([\w.]+)}/g, (match, key) => {
                        const value = key.split('.').reduce((obj, prop) => obj && obj[prop], options.data)
                        return value !== undefined ? String(value) : match
                    })
                }

                // 确保内容为字符串
                if (content === undefined || content === null) {
                    content = '';
                } else if (typeof content !== 'string') {
                    content = String(content);
                }

                // 坐标转换
                const x = mmToDots(parseInt(item.x));
                const y = mmToDots(parseInt(item.y));
                const height = mmToDots(parseInt(item.height));

                command.setBar(
                    x,
                    y,
                    barcodeType,
                    height,
                    item.readable,
                    parseInt(item.narrow || 2),
                    parseInt(item.wide || 4),
                    content
                )
            })
        }

        // 处理二维码元素
        if (template.elements.qrcode && template.elements.qrcode.length > 0) {
            template.elements.qrcode.forEach(item => {
                const qrLevel = getQRLevel(item.levelIndex)

                // 支持数据替换
                let content = item.content || ''
                if (options.data && typeof content === 'string') {
                    content = content.replace(/\${([\w.]+)}/g, (match, key) => {
                        const value = key.split('.').reduce((obj, prop) => obj && obj[prop], options.data)
                        return value !== undefined ? String(value) : match
                    })
                }

                // 确保内容为字符串
                if (content === undefined || content === null) {
                    content = '';
                } else if (typeof content !== 'string') {
                    content = String(content);
                }

                // 坐标转换
                const x = mmToDots(parseInt(item.x));
                const y = mmToDots(parseInt(item.y));
                // const width = mmToDots(parseInt(item.width));
                const width = parseInt(item.width);

                command.setQR(
                    x,
                    y,
                    qrLevel,
                    width,
                    item.mode || 'A',
                    content
                )
            })
        }

        // 打印页面
        command.setPagePrint()

        // 发送数据
        return store.dispatch('bluetooth/sendBluetoothData', {
            deviceId,
            serviceId,
            characteristicId,
            data: command.getData()
        })
            .then(() => {
                const message = '正在打印标签...'
                if (options.onSuccess) {
                    options.onSuccess(message)
                }
                return Promise.resolve(message)
            })
            .catch(err => {
                const message = err.message || '打印失败'
                if (options.onError) {
                    options.onError(message)
                }
                return Promise.reject(new Error(message))
            })
    } catch (error) {
        console.error("打印标签出错", error)
        const message = error.message || '打印失败'
        if (options.onError) {
            options.onError(message)
        }
        return Promise.reject(new Error(message))
    }
}

/**
 * 打印票据
 * @param {Object} options 打印选项
 * @param {String} options.text 票据文本内容
 * @param {Function} options.onSuccess 打印成功回调
 * @param {Function} options.onError 打印失败回调
 * @returns {Promise} 打印结果的Promise
 */
export function printReceipt(options = {}) {
    // 检查打印机状态
    const printerStatus = checkPrinterReady()
    if (!printerStatus.ready) {
        if (options.onError) {
            options.onError(printerStatus.message)
        }
        return Promise.reject(new Error(printerStatus.message))
    }

    if (!options.text) {
        const message = '请输入票据信息'
        if (options.onError) {
            options.onError(message)
        }
        return Promise.reject(new Error(message))
    }

    try {
        const currDev = store.getters.currentBluetoothDevice
        const deviceId = currDev.deviceId
        const serviceId = currDev.services[0].serviceId
        const characteristicId = currDev.services[0].characteristicId

        // 创建ESC打印命令
        const command = esc.jpPrinter.createNew()
        command.init()
        command.setText(options.text)
        command.setPrintAndFeedRow(1)

        // 发送数据
        return store.dispatch('bluetooth/sendBluetoothData', {
            deviceId,
            serviceId,
            characteristicId,
            data: command.getData()
        })
            .then(() => {
                const message = '正在打印票据...'
                if (options.onSuccess) {
                    options.onSuccess(message)
                }
                return Promise.resolve(message)
            })
            .catch(err => {
                const message = err.message || '打印失败'
                if (options.onError) {
                    options.onError(message)
                }
                return Promise.reject(new Error(message))
            })
    } catch (error) {
        console.error("打印票据出错", error)
        const message = error.message || '打印失败'
        if (options.onError) {
            options.onError(message)
        }
        return Promise.reject(new Error(message))
    }
}

/**
 * 初始化打印机模块
 * @returns {Promise} 初始化结果的Promise
 */
export function initPrinter() {
    return store.dispatch('bluetooth/initBluetooth')
}

/**
 * 搜索蓝牙打印机
 * @returns {Promise} 搜索结果的Promise
 */
export function searchPrinter() {
    return store.dispatch('bluetooth/searchBluetooth')
}

/**
 * 连接打印机
 * @param {Object} device 设备对象
 * @returns {Promise} 连接结果的Promise
 */
export function connectPrinter(device) {
    return store.dispatch('bluetooth/connectDevice', device)
}

/**
 * 显示消息提示
 * @param {String} message 提示消息
 */
export function showToast(message) {
    uni.showToast({
        title: message,
        icon: 'none',
        duration: 2000
    })
}

/**
 * 直接使用默认JSON模板打印标签
 * @param {Object} data 打印数据，用于替换模板中的占位符
 * @param {Function} onSuccess 打印成功回调
 * @param {Function} onError 打印失败回调
 * @returns {Promise} 打印结果的Promise
 */
export function printWithDefaultTemplate(data = {}, onSuccess, onError) {
    return printLabel({
        useDefaultJson: true,
        data,
        onSuccess,
        onError
    })
}

export default {
    checkPrinterReady,
    printLabel,
    printReceipt,
    initPrinter,
    searchPrinter,
    connectPrinter,
    loadTemplate,
    showToast,
    printWithDefaultTemplate
} 