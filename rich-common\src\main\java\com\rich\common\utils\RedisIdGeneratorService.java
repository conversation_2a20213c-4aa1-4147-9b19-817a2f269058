package com.rich.common.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/3/27 10:46
 * @Version 1.0
 * 利用redis生成自增id且每月自动重置
 */
@Service
public class RedisIdGeneratorService {
    // Redis中存储ID的键名
    private static final String WRITE_OFF_ID_KEY = "write_off_unique_id";
    private static final String PSA_NO_ID_KEY = "psa_unique_id";
    private static final String WAREHOUSE_ENTRY_ID_KEY = "warehouse_entry_unique_id"; // 新增入仓号的键
    private static final String OUTBOUND_ID_KEY = "outbound_unique_id"; // 新增出仓号的键

    // Redis中存储上次重置日期的键名
    private static final String WRITE_OFF_LAST_RESET_KEY = "write_off_last_reset_date";
    private static final String PSA_NO_LAST_RESET_KEY = "write_off_last_reset_date";
    private static final String WAREHOUSE_ENTRY_LAST_RESET_KEY = "warehouse_entry_last_reset_date"; // 新增入仓号的重置日期键
    private static final String OUTBOUND_LAST_RESET_KEY = "outbound_last_reset_date"; // 新增出仓号的重置日期键


    @Autowired
    private StringRedisTemplate redisTemplate;

    public String generateUniqueId(String type) {
        // 当天
        LocalDate currentDate = LocalDate.now();
        // 上次重置
        LocalDate lastResetDate = getLastResetDate(type);

        // 如果当前日期在上次重置日期之后，或者是重置日期当天
        if (currentDate.isAfter(lastResetDate) || currentDate.isEqual(Objects.requireNonNull(lastResetDate))) {
            // 检查是否是本月的第一天，如果是，则重置ID计数器
            if (currentDate.getDayOfMonth() == 1) {
                resetIdCounter(currentDate, type);
            }
        } else {
            // 如果当前日期在上次重置日期之前，说明已经过了当前月的重置日，需要重置ID计数器
            resetIdCounter(currentDate, type);
        }
        if (type.equals("write_off")) {
            Long id = redisTemplate.opsForValue().increment(WRITE_OFF_ID_KEY);
            return String.format("%03d", id);
        }
        if (type.equals("psa_no")) {
            Long id = redisTemplate.opsForValue().increment(PSA_NO_ID_KEY);
            return String.format("%03d", id);
        }
        if (type.equals("warehouse_entry")) { // 新增入仓号的生成逻辑
            Long id = redisTemplate.opsForValue().increment(WAREHOUSE_ENTRY_ID_KEY);
            return String.format("WH-%03d", id); // 示例格式：WH-001
        }
        if (type.equals("outbound")) { // 新增出仓号的生成逻辑
            Long id = redisTemplate.opsForValue().increment(OUTBOUND_ID_KEY);
            return String.format("%03d", id); // 格式：001
        }
        return null;
    }

    private LocalDate getLastResetDate(String key) {
        if (key.equals("write_off")) {
            String lastResetDateString = redisTemplate.opsForValue().get(WRITE_OFF_LAST_RESET_KEY);
            return (lastResetDateString != null) ? LocalDate.parse(lastResetDateString) : LocalDate.now();
        }
        if (key.equals("psa_no")) {
            String lastResetDateString = redisTemplate.opsForValue().get(PSA_NO_LAST_RESET_KEY);
            return (lastResetDateString != null) ? LocalDate.parse(lastResetDateString) : LocalDate.now();
        }
        if (key.equals("warehouse_entry")) { // 新增入仓号的重置日期获取逻辑
            String lastResetDateString = redisTemplate.opsForValue().get(WAREHOUSE_ENTRY_LAST_RESET_KEY);
            return (lastResetDateString != null) ? LocalDate.parse(lastResetDateString) : LocalDate.now();
        }
        if (key.equals("outbound")) { // 新增出仓号的重置日期获取逻辑
            String lastResetDateString = redisTemplate.opsForValue().get(OUTBOUND_LAST_RESET_KEY);
            return (lastResetDateString != null) ? LocalDate.parse(lastResetDateString) : LocalDate.now();
        }
        return null;
    }

    private void resetIdCounter(LocalDate currentDate, String key) {
        // 将上次重置日期设置为当月的第一天
        LocalDate resetDate = currentDate.with(TemporalAdjusters.firstDayOfMonth());
        if (key.equals("write_off")) {
            redisTemplate.opsForValue().set(WRITE_OFF_ID_KEY, "0");
            redisTemplate.opsForValue().set(WRITE_OFF_LAST_RESET_KEY, resetDate.toString());
        }
        if (key.equals("psa_no")) {
            redisTemplate.opsForValue().set(PSA_NO_ID_KEY, "0");
            redisTemplate.opsForValue().set(PSA_NO_LAST_RESET_KEY, resetDate.toString());
        }
        if (key.equals("warehouse_entry")) { // 新增入仓号的重置逻辑
            redisTemplate.opsForValue().set(WAREHOUSE_ENTRY_ID_KEY, "0");
            redisTemplate.opsForValue().set(WAREHOUSE_ENTRY_LAST_RESET_KEY, resetDate.toString());
        }
        if (key.equals("outbound")) { // 新增出仓号的重置逻辑
            redisTemplate.opsForValue().set(OUTBOUND_ID_KEY, "0");
            redisTemplate.opsForValue().set(OUTBOUND_LAST_RESET_KEY, resetDate.toString());
        }
    }
}
