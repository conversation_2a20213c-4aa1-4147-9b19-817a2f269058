package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 用户信息对象 mp_user
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
public class MpUser extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 角色
     */
    @Excel(name = "角色")
    private String role;

    /**
     * 瑞旗员工ID
     */
    @Excel(name = "瑞旗员工ID")
    private Long ruichiEmployeeId;

    /**
     * 姓名称谓
     */
    @Excel(name = "姓名称谓")
    private String fullName;

    /**
     * 用户头像
     */
    @Excel(name = "用户头像")
    private String avatarUrl;

    /**
     * 微信识别码
     */
    @Excel(name = "微信识别码")
    private String wechatId;

    /**
     * 手机号码
     */
    @Excel(name = "手机号码")
    private String phoneNumber;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remarks;

    private MpWarehouseClient mpWarehouseClient;

    private List<String> roles;

    public List<String> getRoles() {
        return roles;
    }

    public void setRoles(List<String> roles) {
        this.roles = roles;
    }

    public MpWarehouseClient getMpWarehouseClient() {
        return mpWarehouseClient;
    }

    public void setMpWarehouseClient(MpWarehouseClient mpWarehouseClient) {
        this.mpWarehouseClient = mpWarehouseClient;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public Long getRuichiEmployeeId() {
        return ruichiEmployeeId;
    }

    public void setRuichiEmployeeId(Long ruichiEmployeeId) {
        this.ruichiEmployeeId = ruichiEmployeeId;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public String getWechatId() {
        return wechatId;
    }

    public void setWechatId(String wechatId) {
        this.wechatId = wechatId;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("userId", getUserId())
                .append("role", getRole())
                .append("ruichiEmployeeId", getRuichiEmployeeId())
                .append("fullName", getFullName())
                .append("avatarUrl", getAvatarUrl())
                .append("wechatId", getWechatId())
                .append("phoneNumber", getPhoneNumber())
                .append("remarks", getRemarks())
                .toString();
    }
}
