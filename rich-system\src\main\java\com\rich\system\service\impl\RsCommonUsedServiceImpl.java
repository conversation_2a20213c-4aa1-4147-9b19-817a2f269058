package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsCommonUsed;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsCommonUsedMapper;
import com.rich.system.service.RsCommonUsedService;

/**
 * 常用信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-07-31
 */
@Service
public class RsCommonUsedServiceImpl implements RsCommonUsedService
{
    @Autowired
    private RsCommonUsedMapper rsCommonUsedMapper;

    /**
     * 查询常用信息
     * 
     * @param commonUsedId 常用信息主键
     * @return 常用信息
     */
    @Override
    public RsCommonUsed selectRsCommonUsedByCommonUsedId(Long commonUsedId)
    {
        return rsCommonUsedMapper.selectRsCommonUsedByCommonUsedId(commonUsedId);
    }

    /**
     * 查询常用信息列表
     * 
     * @param rsCommonUsed 常用信息
     * @return 常用信息
     */
    @Override
    public List<RsCommonUsed> selectRsCommonUsedList(RsCommonUsed rsCommonUsed)
    {
        return rsCommonUsedMapper.selectRsCommonUsedList(rsCommonUsed);
    }

    /**
     * 新增常用信息
     * 
     * @param rsCommonUsed 常用信息
     * @return 结果
     */
    @Override
    public int insertRsCommonUsed(RsCommonUsed rsCommonUsed)
    {
        rsCommonUsed.setCreateTime(DateUtils.getNowDate());
        rsCommonUsed.setCreateBy(SecurityUtils.getUserId());
        return rsCommonUsedMapper.insertRsCommonUsed(rsCommonUsed);
    }

    /**
     * 修改常用信息
     * 
     * @param rsCommonUsed 常用信息
     * @return 结果
     */
    @Override
    public int updateRsCommonUsed(RsCommonUsed rsCommonUsed)
    {
        rsCommonUsed.setUpdateTime(DateUtils.getNowDate());
        rsCommonUsed.setUpdateBy(SecurityUtils.getUserId());
        return rsCommonUsedMapper.updateRsCommonUsed(rsCommonUsed);
    }

    /**
     * 修改常用信息状态
     *
     * @param rsCommonUsed 常用信息
     * @return 常用信息
     */
    @Override
    public int changeStatus(RsCommonUsed rsCommonUsed) {
        return rsCommonUsedMapper.updateRsCommonUsed(rsCommonUsed);
    }

    /**
     * 批量删除常用信息
     * 
     * @param commonUsedIds 需要删除的常用信息主键
     * @return 结果
     */
    @Override
    public int deleteRsCommonUsedByCommonUsedIds(Long[] commonUsedIds)
    {
        return rsCommonUsedMapper.deleteRsCommonUsedByCommonUsedIds(commonUsedIds);
    }

    /**
     * 删除常用信息信息
     * 
     * @param commonUsedId 常用信息主键
     * @return 结果
     */
    @Override
    public int deleteRsCommonUsedByCommonUsedId(Long commonUsedId)
    {
        return rsCommonUsedMapper.deleteRsCommonUsedByCommonUsedId(commonUsedId);
    }
}
