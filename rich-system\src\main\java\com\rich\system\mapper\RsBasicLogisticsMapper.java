package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsBasicLogistics;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 基础物流Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@Mapper
public interface RsBasicLogisticsMapper {
    /**
     * 查询基础物流
     *
     * @param basicLogisticsId 基础物流主键
     * @return 基础物流
     */
    RsBasicLogistics selectRsBasicLogisticsByBasicLogisticsId(Long basicLogisticsId);

    /**
     * 查询基础物流列表
     *
     * @param rsBasicLogistics 基础物流
     * @return 基础物流集合
     */
    List<RsBasicLogistics> selectRsBasicLogisticsList(RsBasicLogistics rsBasicLogistics);

    /**
     * 新增基础物流
     *
     * @param rsBasicLogistics 基础物流
     * @return 结果
     */
    int insertRsBasicLogistics(RsBasicLogistics rsBasicLogistics);

    /**
     * 修改基础物流
     *
     * @param rsBasicLogistics 基础物流
     * @return 结果
     */
    int updateRsBasicLogistics(RsBasicLogistics rsBasicLogistics);

    /**
     * 删除基础物流
     *
     * @param basicLogisticsId 基础物流主键
     * @return 结果
     */
    int deleteRsBasicLogisticsByBasicLogisticsId(Long basicLogisticsId);

    /**
     * 批量删除基础物流
     *
     * @param basicLogisticsIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsBasicLogisticsByBasicLogisticsIds(Long[] basicLogisticsIds);

    RsBasicLogistics selectRsBasicLogisticsByServiceInstance(Long rctId);

    RsBasicLogistics selectRsBasicLogistics(Long serviceInstanceId);
}
