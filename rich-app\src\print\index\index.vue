<template>
  <view class="bluetooth-printer">
    <view class="action-bar">
      <button type="primary" @click="searchBluetooth">搜索蓝牙</button>
      <text v-if="isSearching" class="status-text">搜索中...</text>
    </view>

    <view v-if="devices.length > 0" class="device-list">
      <view v-for="(item, index) in devices" :key="index" class="device-item">
        <button
            :class="{'connected-device': item.deviceId === connId}"
            @click="connectDevice(item)"
        >
          {{ item.name }}
        </button>
      </view>
    </view>
    <view v-else class="empty-tip">
      <text>暂无可用设备</text>
    </view>

    <view v-if="currDev" class="print-area">
      <!-- 标签打印部分 -->
      <view class="print-section">
        <view class="section-header">
          <view class="section-title">标签打印</view>
        </view>

        <view v-if="currentTemplate" class="template-info">
          <view class="template-name">当前模板: {{ currentTemplate.name }}</view>
        </view>
        <view class="template-actions">
          <button type="primary" @click="printWithDefaultTemplate">使用默认模板打印</button>
        </view>
      </view>

      <!-- 票据打印部分 -->
      <view class="print-section">
        <view class="section-header">
          <view class="section-title">票据打印</view>
        </view>

        <textarea
            v-model="receiptText"
            auto-height
            class="receipt-input"
            placeholder="请输入票据信息"
            placeholder-style="color:#F76260"
            @blur="onTextAreaBlur"
        />
        <button type="primary" @click="printReceipt">打印票据</button>
      </view>
    </view>
  </view>
</template>

<script>
import {mapGetters, mapActions} from 'vuex'
import printTemplate from '../template/default-templates.js'
import printerService from '@/utils/printer'

export default {
  data() {
    return {
      receiptText: '', // 票据文本内容
      currentTemplate: null, // 当前选中的模板
      templates: [], // 模板列表
    }
  },

  computed: {
    ...mapGetters([
      'bluetoothDevices',
      'currentBluetoothDevice',
      'bluetoothConnectionId',
      'isBluetoothSearching',
      'isBluetoothConnected'
    ]),
    // 设备列表
    devices() {
      return this.bluetoothDevices
    },
    // 当前连接的设备
    currDev() {
      return this.currentBluetoothDevice
    },
    // 当前连接ID
    connId() {
      return this.bluetoothConnectionId
    },
    // 是否正在搜索
    isSearching() {
      return this.isBluetoothSearching
    }
  },

  onLoad() {
    // 页面加载时初始化蓝牙
    this.initPrinter()
    // 加载模板
    this.loadTemplates()

    // 监听模板选择事件
    uni.$on('template-selected', this.handleTemplateSelected)
  },

  onUnload() {
    // 页面卸载时只移除事件监听，不断开蓝牙连接
    uni.$off('template-selected', this.handleTemplateSelected)
  },

  onShow() {
    // 每次显示页面时重新加载模板
    this.loadTemplates()
  },

  methods: {
    /**
     * 初始化打印机
     */
    initPrinter() {
      printerService.initPrinter().catch(() => {
        printerService.showToast('请打开蓝牙后再试')
      })
    },

    /**
     * 加载模板列表
     */
    loadTemplates() {
      try {
        const templates = uni.getStorageSync('label_templates') || '[]'
        this.templates = JSON.parse(templates)

        // 获取当前模板
        this.currentTemplate = printerService.loadTemplate()
      } catch (e) {
      }
    },

    /**
     * 处理模板选择事件
     */
    handleTemplateSelected(data) {
      if (data && data.template) {
        this.currentTemplate = data.template
        // 设置为默认模板
        uni.setStorageSync('default_template_id', data.template.id)
      }
    },

    /**
     * 前往模板列表页
     */
    goToTemplateList() {
      uni.navigateTo({
        url: '/print/template/list'
      })
    },

    /**
     * 创建新模板
     */
    createNewTemplate() {
      uni.navigateTo({
        url: '/print/template/index'
      })
    },

    /**
     * 搜索蓝牙设备
     */
    searchBluetooth() {
      printerService.searchPrinter()
          .then(() => {
            printerService.showToast('开始搜索设备')
          })
          .catch(err => {
            printerService.showToast(err.message || '搜索失败')
          })
    },

    /**
     * 连接设备
     */
    connectDevice(device) {
      printerService.connectPrinter(device)
          .then(res => {
            printerService.showToast(res.message)
          })
          .catch(err => {
            printerService.showToast(err.message || '连接失败')
          })
    },

    /**
     * 打印自定义标签
     */
    printCustomLabel() {
      // 使用打印服务打印标签
      printerService.printLabel({
        template: this.currentTemplate,
        onSuccess: (message) => {
          printerService.showToast(message)
        },
        onError: (message) => {
          printerService.showToast(message)
        }
      })
    },

    /**
     * 使用默认JSON模板打印标签
     */
    printWithDefaultTemplate() {
      try {

        // 使用hardcode的模板数据直接打印，调整为76×130规格
        const template = printTemplate

        const data = {
          companyName: 'NODAT CARGO AND LOGISTICS LTD',
          cargoNumber: 'NODAT-10071',
          cargoName: 'CYNTHSCARE',
          cargoPhone: '+********** 456',
          cargoAddress: 'GUANGZHOU - GHANA',
        }

        // 使用模板直接打印
        printerService.printLabel({
          template: template, // 直接传递模板对象
          data: data, // 传递数据
          onSuccess: (message) => {
            printerService.showToast(message)
          },
          onError: (message) => {
            printerService.showToast(message)
          }
        })
      } catch (error) {
        printerService.showToast('打印发生错误: ' + error.message);
      }
    },

    /**
     * 打印票据
     */
    printReceipt() {
      // 使用打印服务打印票据
      printerService.printReceipt({
        text: this.receiptText,
        onSuccess: (message) => {
          printerService.showToast(message)
        },
        onError: (message) => {
          printerService.showToast(message)
        }
      })
    },

    /**
     * 文本框失焦事件
     */
    onTextAreaBlur(e) {
      // 可以在这里添加文本验证逻辑
    }
  }
}
</script>

<style lang="scss">
.bluetooth-printer {
  padding: 30rpx;

  .action-bar {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;

    button {
      margin-right: 20rpx;
      background-color: #0081FF;
    }

    .status-text {
      font-size: 28rpx;
      color: #999;
    }
  }

  .device-list {
    margin-bottom: 40rpx;

    .device-item {
      margin-bottom: 20rpx;

      button {
        width: 400rpx;
        color: #0081FF;
        background-color: #f5f5f5;

        &.connected-device {
          color: #ffffff;
          background-color: #0081FF;
        }
      }
    }
  }

  .empty-tip {
    text-align: center;
    padding: 30rpx 0;
    color: #999;
    font-size: 28rpx;
  }

  .print-area {
    margin-top: 50rpx;
    border-top: 1px solid #eee;
    padding-top: 30rpx;

    .print-section {
      margin-bottom: 50rpx;

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20rpx;

        .section-title {
          font-size: 32rpx;
          font-weight: bold;
        }
      }

      .template-info {
        background-color: #f5f5f5;
        padding: 20rpx;
        border-radius: 8rpx;
        margin-bottom: 20rpx;

        .template-name {
          font-weight: bold;
          margin-bottom: 10rpx;
        }

        .template-size {
          font-size: 24rpx;
          color: #666;
        }
      }

      .template-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 20rpx;
        margin-bottom: 20rpx;

        button {
          flex: 1;
          min-width: 200rpx;
          margin: 0;
          background-color: #0081FF;

          &:disabled {
            background-color: #cccccc;
          }
        }
      }

      .receipt-input {
        width: 100%;
        padding: 20rpx;
        border: 1px solid #ddd;
        border-radius: 8rpx;
        min-height: 200rpx;
        box-sizing: border-box;
        margin-bottom: 20rpx;
      }

      button {
        width: 100%;
        background-color: #0081FF;
      }
    }
  }
}
</style>
