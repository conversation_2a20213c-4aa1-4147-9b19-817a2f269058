package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsPsaRct;
import org.apache.ibatis.annotations.Mapper;

/**
 * 商务订舱Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-01
 */
@Mapper
public interface RsPsaRctMapper {
    /**
     * 查询商务订舱
     *
     * @param psaRctId 商务订舱主键
     * @return 商务订舱
     */
    RsPsaRct selectRsPsaRctByPsaRctId(Long psaRctId);

    /**
     * 查询商务订舱列表
     *
     * @param rsPsaRct 商务订舱
     * @return 商务订舱集合
     */
    List<RsPsaRct> selectRsPsaRctList(RsPsaRct rsPsaRct);

    /**
     * 新增商务订舱
     *
     * @param rsPsaRct 商务订舱
     * @return 结果
     */
    int insertRsPsaRct(RsPsaRct rsPsaRct);

    /**
     * 修改商务订舱
     *
     * @param rsPsaRct 商务订舱
     * @return 结果
     */
    int updateRsPsaRct(RsPsaRct rsPsaRct);

    /**
     * 删除商务订舱
     *
     * @param psaRctId 商务订舱主键
     * @return 结果
     */
    int deleteRsPsaRctByPsaRctId(Long psaRctId);

    /**
     * 批量删除商务订舱
     *
     * @param psaRctIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsPsaRctByPsaRctIds(Long[] psaRctIds);
}
