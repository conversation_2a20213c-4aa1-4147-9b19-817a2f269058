{"_id": "@anthropic-ai/claude-code", "_rev": "5066187-67bcb4aa35a24fadc18b78af", "dist-tags": {"latest": "1.0.33", "next": "1.0.33"}, "name": "@anthropic-ai/claude-code", "time": {"created": "2025-02-24T18:04:26.895Z", "modified": "2025-06-24T00:05:58.281Z", "0.2.9": "2025-02-24T20:50:11.262Z", "0.2.14": "2025-02-25T22:53:18.971Z", "0.2.18": "2025-02-26T21:59:37.558Z", "0.2.19": "2025-02-27T01:08:32.650Z", "0.2.25": "2025-02-27T23:08:59.360Z", "0.2.27": "2025-02-28T01:40:04.642Z", "0.2.29": "2025-02-28T20:20:23.700Z", "0.2.30": "2025-03-04T18:47:05.839Z", "0.2.31": "2025-03-05T17:48:20.869Z", "0.2.32": "2025-03-05T21:28:22.509Z", "0.2.33": "2025-03-07T23:05:16.317Z", "0.2.34": "2025-03-08T00:48:44.306Z", "0.2.35": "2025-03-08T01:23:34.228Z", "0.2.36": "2025-03-10T23:28:47.867Z", "0.2.37": "2025-03-11T20:49:25.325Z", "0.2.38": "2025-03-12T17:35:30.488Z", "0.2.39": "2025-03-13T01:21:08.162Z", "0.2.40": "2025-03-13T21:05:47.827Z", "0.2.41": "2025-03-14T01:34:42.240Z", "0.2.42": "2025-03-14T16:29:50.959Z", "0.2.43": "2025-03-14T22:43:00.299Z", "0.2.44": "2025-03-15T01:04:24.513Z", "0.2.45": "2025-03-15T03:23:03.350Z", "0.2.46": "2025-03-17T22:13:12.958Z", "0.2.47": "2025-03-18T01:42:37.884Z", "0.2.48": "2025-03-18T01:53:09.696Z", "0.2.49": "2025-03-18T19:18:21.616Z", "0.2.50": "2025-03-19T21:06:46.787Z", "0.2.51": "2025-03-20T17:15:04.548Z", "0.2.52": "2025-03-20T17:21:06.572Z", "0.2.53": "2025-03-21T18:04:02.580Z", "0.2.54": "2025-03-25T20:44:47.993Z", "0.2.55": "2025-03-26T21:40:53.726Z", "0.2.56": "2025-03-27T22:25:37.204Z", "0.2.57": "2025-03-31T23:49:44.153Z", "0.2.59": "2025-04-02T00:24:59.952Z", "0.2.60": "2025-04-02T17:30:14.740Z", "0.2.61": "2025-04-03T01:04:19.442Z", "0.2.62": "2025-04-04T02:56:58.288Z", "0.2.64": "2025-04-04T20:58:54.659Z", "0.2.65": "2025-04-07T22:29:15.495Z", "0.2.66": "2025-04-09T00:04:46.906Z", "0.2.67": "2025-04-09T22:17:47.830Z", "0.2.68": "2025-04-10T20:04:54.725Z", "0.2.69": "2025-04-11T22:30:28.481Z", "0.2.70": "2025-04-15T17:45:55.713Z", "0.2.72": "2025-04-17T14:21:16.372Z", "0.2.73": "2025-04-18T16:34:43.734Z", "0.2.74": "2025-04-18T22:33:26.843Z", "0.2.76": "2025-04-21T21:59:25.609Z", "0.2.77": "2025-04-22T18:07:27.857Z", "0.2.78": "2025-04-22T19:48:55.543Z", "0.2.79": "2025-04-23T17:24:18.815Z", "0.2.80": "2025-04-24T16:07:52.297Z", "0.2.81": "2025-04-24T20:31:29.117Z", "0.2.83": "2025-04-25T17:15:09.772Z", "0.2.84": "2025-04-25T18:55:56.821Z", "0.2.85": "2025-04-25T21:48:38.527Z", "0.2.86": "2025-04-26T00:07:48.834Z", "0.2.89": "2025-04-28T20:15:12.241Z", "0.2.90": "2025-04-29T00:45:19.628Z", "0.2.91": "2025-04-29T20:19:06.809Z", "0.2.92": "2025-04-29T21:02:29.314Z", "0.2.93": "2025-04-30T18:14:42.819Z", "0.2.94": "2025-04-30T19:21:17.898Z", "0.2.96": "2025-05-01T16:23:49.548Z", "0.2.97": "2025-05-01T16:53:07.961Z", "0.2.98": "2025-05-01T23:48:31.409Z", "0.2.99": "2025-05-02T02:28:15.844Z", "0.2.100": "2025-05-02T17:15:38.454Z", "0.2.101": "2025-05-05T15:24:19.332Z", "0.2.102": "2025-05-05T22:58:51.202Z", "0.2.103": "2025-05-06T23:59:01.370Z", "0.2.104": "2025-05-07T19:53:37.472Z", "0.2.105": "2025-05-08T16:41:34.425Z", "0.2.106": "2025-05-09T02:33:11.820Z", "0.2.107": "2025-05-09T16:15:03.847Z", "0.2.108": "2025-05-13T01:55:52.356Z", "0.2.109": "2025-05-13T17:57:15.176Z", "0.2.113": "2025-05-13T23:53:09.326Z", "0.2.114": "2025-05-14T16:30:35.917Z", "0.2.115": "2025-05-15T15:57:31.601Z", "0.2.116": "2025-05-17T20:21:49.934Z", "0.2.117": "2025-05-17T23:51:27.284Z", "0.2.118": "2025-05-18T16:54:30.382Z", "0.2.119": "2025-05-19T04:26:55.850Z", "0.2.120": "2025-05-19T04:59:13.833Z", "0.2.122": "2025-05-19T16:49:36.680Z", "0.2.123": "2025-05-20T19:22:31.365Z", "0.2.124": "2025-05-20T20:28:20.021Z", "0.2.125": "2025-05-21T18:00:11.547Z", "0.2.126": "2025-05-22T02:22:07.953Z", "1.0.0": "2025-05-22T16:57:18.061Z", "1.0.1": "2025-05-22T19:23:37.662Z", "1.0.2": "2025-05-22T21:46:50.893Z", "1.0.3": "2025-05-23T22:46:13.282Z", "1.0.4": "2025-05-28T17:42:44.392Z", "1.0.5": "2025-05-28T23:08:03.022Z", "1.0.6": "2025-05-29T16:33:59.854Z", "1.0.7": "2025-05-30T16:29:49.709Z", "1.0.8": "2025-06-02T16:16:32.619Z", "1.0.9": "2025-06-02T20:37:14.617Z", "1.0.10": "2025-06-03T18:01:21.056Z", "1.0.11": "2025-06-04T16:41:55.904Z", "1.0.14": "2025-06-05T20:48:28.251Z", "1.0.15": "2025-06-05T21:03:35.216Z", "1.0.16": "2025-06-06T01:11:02.870Z", "1.0.17": "2025-06-06T18:44:07.505Z", "1.0.18": "2025-06-09T23:27:59.204Z", "1.0.19": "2025-06-10T18:36:18.261Z", "1.0.20": "2025-06-11T18:19:49.429Z", "1.0.21": "2025-06-11T20:06:48.186Z", "1.0.22": "2025-06-12T15:53:29.220Z", "1.0.23": "2025-06-13T18:50:20.083Z", "1.0.24": "2025-06-14T00:57:41.575Z", "1.0.25": "2025-06-16T22:11:09.938Z", "1.0.26": "2025-06-17T17:08:31.002Z", "1.0.27": "2025-06-17T17:52:47.797Z", "1.0.28": "2025-06-18T17:37:33.722Z", "1.0.29": "2025-06-18T21:49:40.075Z", "1.0.30": "2025-06-19T03:32:45.914Z", "1.0.31": "2025-06-20T18:55:21.438Z", "1.0.32": "2025-06-23T17:30:48.758Z", "1.0.33": "2025-06-23T20:31:06.405Z"}, "versions": {"0.2.9": {"name": "@anthropic-ai/claude-code", "version": "0.2.9", "bin": {"claude": "cli.mjs"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "repository": {"type": "git", "url": "git+https://github.com/anthropics/claude-cli-internal.git"}, "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\""}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.9", "gitHead": "c6582a012422fa8e35c5c41fdb0bd2c8a3b013ec", "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-UGSEQbgDvhlEXC8rf5ASDXRSaq6Nfd4owY7k9bDdRhX9N5q8cMN+5vfTN1ezZhBcRFMOnpEK4eRSEgXW3eDeOQ==", "shasum": "66dcb65aad58b0c4a7e288db287b162ee97518e6", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.9.tgz", "fileCount": 424, "unpackedSize": 35099447, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCBHUgjeXUkAbLYQeg6EHmrn6SvwzbXdXWfxlBN6koVAAIgIcj0MNWmkC9U9cjJIW2IlhRDq27KGM6Mb/SXeIOFzbE="}], "size": 12426984}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.9_1740430210904_0.4569467867181731"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-24T20:50:11.262Z", "publish_time": 1740430211262, "_source_registry_name": "default"}, "0.2.14": {"name": "@anthropic-ai/claude-code", "version": "0.2.14", "bin": {"claude": "cli.mjs"}, "engines": {"node": ">=18.0.0"}, "os": ["!win32"], "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "repository": {"type": "git", "url": "git+https://github.com/anthropics/claude-cli-internal.git"}, "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\""}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.14", "gitHead": "1399236bb93691ebcdfedf56ec789bc2977eb886", "_nodeVersion": "23.7.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-p729wIUq9/K/TecpE64nzWKEZJ1qddn20eQg1nUoMdEQtwWjwWYiecwJ6lDxCmNRWr0ukC7ovu0Kgtmh+uOPYg==", "shasum": "df66859eb9c25189d802478da1c6678bccaf611f", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.14.tgz", "fileCount": 424, "unpackedSize": 35104130, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQC4sj3iGzZ61q5CDR5AIknhQWY3oHo25eb4SMMQCJZDVwIgXGJ6A+dhJiGl06+9C7YDZ4G1VctbA4r+GfS3emjJ1Js="}], "size": 12072705}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.14_1740523998534_0.005835264527071171"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-25T22:53:18.971Z", "publish_time": 1740523998971, "_source_registry_name": "default"}, "0.2.18": {"name": "@anthropic-ai/claude-code", "version": "0.2.18", "bin": {"claude": "cli.mjs"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "repository": {"type": "git", "url": "git+https://github.com/anthropics/claude-cli-internal.git"}, "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.18", "gitHead": "5fa957f499f8b3d6e84f9d4f5f2cba1c210e38a1", "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-Yx3SsVQd3VKDHGswAvPpoT/JqHhJj6B+CFSshb0+rI6KG3Nn5Fv3zQdU8XWfAZwLnVZrUjSAO0JcVqoU+6f5Eg==", "shasum": "3ffed8740eab30acb1089b741aba959b2dd8eee9", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.18.tgz", "fileCount": 325, "unpackedSize": 33032909, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDMU9NBZnlf1S77tNIk+w+8uneJCh1RdpI/ZiJPAtVfogIge0eejC9tNlwu0Y+jWHDj+m5WJsExYel5ubl7FZViF+c="}], "size": 12047855}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.18_1740607177177_0.8010180455389544"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-26T21:59:37.558Z", "publish_time": 1740607177558, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.19": {"name": "@anthropic-ai/claude-code", "version": "0.2.19", "bin": {"claude": "cli.mjs"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "repository": {"type": "git", "url": "git+https://github.com/anthropics/claude-cli-internal.git"}, "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.19", "gitHead": "c38e0c56dbce4bc060329cff1acc6c68d462277c", "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-3A9K8438+eYysRjdq2NLGkyETwKOR5CIKh/I03kMahRFnB3h/YwL/b9M1JX38qR1Chna1hNgZRurFOK9Z+pofg==", "shasum": "4df2fbed2731ad4c1c371284f8bfaa15fc3927c0", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.19.tgz", "fileCount": 325, "unpackedSize": 33039102, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIFA2nT5DkZNVQ2N0zrXvC3VCpFZ0qvxi5SSf//Kx3/u+AiBnUomrmDkWce0SXNjmo/G/tppEjPwPhof/b/WuQOEh7A=="}], "size": 12049583}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.19_1740618512272_0.46104933236722667"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-27T01:08:32.650Z", "publish_time": 1740618512650, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.25": {"name": "@anthropic-ai/claude-code", "version": "0.2.25", "bin": {"claude": "cli.mjs"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "repository": {"type": "git", "url": "git+https://github.com/anthropics/claude-cli-internal.git"}, "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.25", "_nodeVersion": "23.7.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-n0hQshFUyrkQ5gFj1yW0gXU78aZlnGgURRcUXuU19JLpxdXpVhS7blBqSs1NtWLGwthD5o2YJCO3dJE9YG0CUQ==", "shasum": "56be74cce8d94829e4da57d477db662aa8833010", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.25.tgz", "fileCount": 425, "unpackedSize": 35161000, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCwxFZODJzkL2j1MV/NPctdk6qtKzJZN+j+qH/ao/m3IAIgGUeLgo0hetiSsp1whUyn6MniuiyfuJvWTY0aiIaNbrI="}], "size": 12091866}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.25_1740697738936_0.5426880188021768"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-27T23:08:59.360Z", "publish_time": 1740697739360, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.27": {"name": "@anthropic-ai/claude-code", "version": "0.2.27", "bin": {"claude": "cli.mjs"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "repository": {"type": "git", "url": "git+https://github.com/anthropics/claude-cli-internal.git"}, "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.27", "gitHead": "df460a81cf9984608483e1034a6cc2494971306f", "_nodeVersion": "23.7.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-j7YqM5FmbHBdG5a1RVy95JMmZ5xef2/rbKI6zTeTohBI1XxEYyrXJcG4IsfD+Nf3vYwuzb4YaVYJGX+Oo3KZcA==", "shasum": "dc86b8b7441749b1c60ed38d9c88f722bc8bb58e", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.27.tgz", "fileCount": 425, "unpackedSize": 35165865, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCPHc58LrjOXMJQM2Qq516hQPmC++0NkETNAOAwV+q1qgIgWR73R7YErxaa1FJs9rFQgDmfdmDTeqmThRYA5Wf4SPg="}], "size": 12093310}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.27_1740706804267_0.9595676374878523"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-28T01:40:04.642Z", "publish_time": 1740706804642, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.29": {"name": "@anthropic-ai/claude-code", "version": "0.2.29", "bin": {"claude": "cli.mjs"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "repository": {"type": "git", "url": "git+https://github.com/anthropics/claude-cli-internal.git"}, "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.29", "gitHead": "78afaffea882fbb7fda2a538054d4f153c5d9cd2", "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-4lZbbg+Qqz+mY3SLKcUYjnOd6/PIB+SQh7ICVIXFYGB1zOB4mVfiY7l2nBt67iw+Rxko2wGJAfg6gPM57S/Q/g==", "shasum": "75ee591ca9509eb189c7c1fcf19af3dfc6e26563", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.29.tgz", "fileCount": 425, "unpackedSize": 35173456, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIAjoZ63XAZjLFA729YEF9HaUF9jLL+G7kxUGqwWbr2d1AiEA9ReLlwtW+9zh0lDH9w2fxNwDNPwoY/KMBWimGQzTtIw="}], "size": 12453090}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.29_1740774023012_0.7247592029313852"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-02-28T20:20:23.700Z", "publish_time": 1740774023700, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.30": {"name": "@anthropic-ai/claude-code", "version": "0.2.30", "bin": {"claude": "cli.mjs"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.30", "gitHead": "ff6434b402b4adb0cc0387944d89c83bf28cde66", "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-dX0CNMRpbJ3ZrIPuDzCowAdn5P0CZsa8ncBHOHt9WbqFy7y1wMbgUacJbGB41AgKp9YC7QD0C3G4nhfjW9onUA==", "shasum": "fb07cef75b6a61e35988d017697ec98974969ab7", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.30.tgz", "fileCount": 12, "unpackedSize": 31755894, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIDCkzKopFxHx7in3iP75o1xygWLlBFLj95oe0bTuG/6WAiBGuJ/bKlMtv8UW8595drVcMQaVNcogk1WLyae3IRlH+Q=="}], "size": 11823127}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.30_1741114025509_0.052986483673783"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-04T18:47:05.839Z", "publish_time": 1741114025839, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.31": {"name": "@anthropic-ai/claude-code", "version": "0.2.31", "bin": {"claude": "cli.mjs"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.31", "gitHead": "2ea95d3ecf4b77ca8dfe36d95bced823a81472a0", "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-GHoO6Mzowf3fXdhDX8lXB0Lb38OwsPizeh2kkCCDj9/lJKUGUNtSMQBXO4nZkNzGVVLXh7/aReLcVNxmD1/QMA==", "shasum": "f2c12711190efe832f6864da0deec058abad1999", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.31.tgz", "fileCount": 12, "unpackedSize": 31756151, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIHQcPMlu51xcn1uLxMEkdYcloTlveXTbDEOLfnw39miUAiEAmuP854tSrRI8riONh1yGQUZP273MMEonR02g0XPxWPY="}], "size": 11823263}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.31_1741196900527_0.6929535482839542"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-05T17:48:20.869Z", "publish_time": 1741196900869, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.32": {"name": "@anthropic-ai/claude-code", "version": "0.2.32", "bin": {"claude": "cli.mjs"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.32", "gitHead": "6a1860b990462fc7d744d7d155f056d60dbfa88a", "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-BhVAlBGkgMbkiWPein6fADLgfZKakR9FQNYGzReSebvBxxQRy9jypYuuZgd+4p5RIYsOtyevlUltAm0KHDgs7A==", "shasum": "53c4ebd82d5222118b7b287f274ddca0e8f2834d", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.32.tgz", "fileCount": 12, "unpackedSize": 31981128, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC58upDQu2oEO6rhEKK/23aBRLFRj9yq2P1YUSxcTLl0AIhAPw6UDusfhbkkDozzIujnanoN+vxV0byHCXUY2JEAmxU"}], "size": 11875503}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.32_1741210101883_0.3860210542902278"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-05T21:28:22.509Z", "publish_time": 1741210102509, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.33": {"name": "@anthropic-ai/claude-code", "version": "0.2.33", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.33", "gitHead": "9115987c23c365a3226e7e63d30898611a2ef933", "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-aW0UKF0wfbArvj9mzbt51Zj+ldqNnSeIN9kFNIzOB79VuDjWlz26iVNjDA1FHM+pARXzypohanMMrbLg4uveSg==", "shasum": "f85396eab69f9c94e536a4d63683dfca9ad1bd32", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.33.tgz", "fileCount": 13, "unpackedSize": 32245406, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDaTLnOeAWk0Ao3b85Fx4e6eZ38wnZzXHcqCByu2X92KAIhAPPrGSsZhGEVS5kOUA8WTzJLoURGg0iD6U3/Yy+IXx8G"}], "size": 11949925}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.33_1741388715682_0.9710562460923444"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-07T23:05:16.317Z", "publish_time": 1741388716317, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.34": {"name": "@anthropic-ai/claude-code", "version": "0.2.34", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.34", "gitHead": "8d023c14a8e10028411fe747f76d7d4b76d93098", "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-VGE45sXuJZW62gTF7oEN6ToMZB82yZOUNrCQozZC4/EJq13UHG5fiApxPR31QPUIb7SViTtiL7Q79ZyMbHh9QQ==", "shasum": "0be9d1983c46f49e4ee6cc427936a7789a18f16e", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.34.tgz", "fileCount": 13, "unpackedSize": 32245477, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIDJZW8AfcnMcQm4aQ1Sg4GnzPTJaGM+xxuG4JAKEQxX1AiEA/72cCHd2kdOAgUpvDgVDRBzNmfqhkTaTuOzViMDCLYM="}], "size": 11949939}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.34_1741394923918_0.6568134126700891"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-08T00:48:44.306Z", "publish_time": 1741394924306, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.35": {"name": "@anthropic-ai/claude-code", "version": "0.2.35", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.35", "gitHead": "27b85cae4448556c05d76f4b440d6f3a8203fcf6", "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-OTCLa2kvtbYJ2tDbKNxZ7N2kUMXGXTIzr5DW18zYSMMu5RpOLyL+A/DfKXNgcvri76ttttj7lcQRLmQ0pCqZDA==", "shasum": "3309167f18e2e8ca40c42846ca213c65c1ad94f3", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.35.tgz", "fileCount": 13, "unpackedSize": 32245697, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCxzvdNyAGqfN2xkndySm6uNfK1xrnRE7B0/+yIbbWU+wIhALyVXw5YAA+PqzLMNPzlQBwAM4pVtunNhJ891pGkvTPf"}], "size": 11950092}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.35_1741397013878_0.6135542620554097"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-08T01:23:34.228Z", "publish_time": 1741397014228, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.36": {"name": "@anthropic-ai/claude-code", "version": "0.2.36", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.36", "gitHead": "b84e4d7a594d45ca3fa642ffe5ad995ca351b7e6", "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-RDUigrVYkV/Ws4q05tt5m9zDFVHuI1iciIzE3jwK+0ULF/H3lXh3zzl92ZyFmY2yeYWGUk/tHJPeySOI6FNAdA==", "shasum": "f6cd7b22ee29ae3fb4d70c844528048fd1410f56", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.36.tgz", "fileCount": 13, "unpackedSize": 32260482, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCN+TgCnI0q6bLYGGai4YRZq5klt7j4vWJMVSPU+DtkWQIhAK7j862mZx6RsNnshFdlBbPapdmTXzk3hcCkGF5ljS1U"}], "size": 11953607}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.36_1741649327526_0.5115649587166398"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-10T23:28:47.867Z", "publish_time": 1741649327867, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.37": {"name": "@anthropic-ai/claude-code", "version": "0.2.37", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.37", "gitHead": "6a2ba5d4047be6c2019ee955285aa9a3864ae2b0", "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-3ZIAV8ttVj2jlAMGPNmz4D+Au2jkSni0zfQX89FqxPBkBa9dm0i5A3SYQFA1FO+uvWxCpBuqdv6gZEy1woYjrQ==", "shasum": "dd88782669ab82727b2ba61e36aec02ad82164a9", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.37.tgz", "fileCount": 13, "unpackedSize": 32260188, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIGzXKoXgizxcqhGgtFsZtbWFGc/Ze/9u6wgRHTpG1RrrAiEA7Q5n3tJsSqQeVFus0jzq6CNtaZIEk5n2QjE/QKfinxM="}], "size": 11953714}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.37_1741726164968_0.6950854719714206"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-11T20:49:25.325Z", "publish_time": 1741726165325, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.38": {"name": "@anthropic-ai/claude-code", "version": "0.2.38", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.38", "gitHead": "e684ca20053daa2e4e140c0033bb4c597fabebe8", "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-lhRYLx46lsfrAHsQuPbHLfERs+hzPVz0KNzyC0cwXXgdn4wt7YXExV0X5DrbIMl1Qqn1ehCgYz+v0Uagr1sfMA==", "shasum": "56ba1004f2f3bd106f5be89ba74df4fac16fab84", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.38.tgz", "fileCount": 13, "unpackedSize": 32377755, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDujR0ka2HgcJiYoHmVEhgd/yAcpYQ3moaLFXjPGKUQZgIhAIB9X/D3CwwJFkYOU/8daJ/ukIaJ+2FTDo4hX+aHFxpS"}], "size": 11988931}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.38_1741800930154_0.44620726981400294"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-12T17:35:30.488Z", "publish_time": 1741800930488, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.39": {"name": "@anthropic-ai/claude-code", "version": "0.2.39", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.39", "gitHead": "c74c4712bcc4db21a1d014589c1091851ca3a510", "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-Gil5H8Ejv6ZMGL/HVsIF85bUX6YDdqEz7kmCTletx9RHGyttgrwMtaN5SI+r3gSu1T3iKyiAGSzxbCWv/id+Hg==", "shasum": "2e451bba32b6f87473a0f832026c5ffdd717f1cb", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.39.tgz", "fileCount": 13, "unpackedSize": 32379960, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQD035wzxBc1HsYvjAwsAlaTSDmA1sBGOkoH+hzPLOqiZAIgfNK3bktqrt+9wFpUPzyPPsXGuOaQr7PkDAYnPZ92ilM="}], "size": 11989615}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.39_1741828867633_0.6600288905843044"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-13T01:21:08.162Z", "publish_time": 1741828868162, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.40": {"name": "@anthropic-ai/claude-code", "version": "0.2.40", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.40", "gitHead": "f58c80dce1d6463075a44841dbe1a5d0239f9014", "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-StMSirDZ6nJSBiIx6nETJ0V7nBp245tsPbfsZ/X6Jf6FygH5/dgQctjSUuJXcDMuQqfM2gwjRiThbwflJwp/+w==", "shasum": "3fb17ea3e550b6fa7b65d30bdd46653a4fe454c4", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.40.tgz", "fileCount": 13, "unpackedSize": 32382669, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCyGefA3yrDr3cwh4dWC46Bix4JvpjZ1Dbz5rdUjYN9BgIgRQnCZy55jfyBACOWezlxMjlTOvjOpU1+10UjQAItIs0="}], "size": 11989708}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.40_1741899947400_0.6189100508159535"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-13T21:05:47.827Z", "publish_time": 1741899947827, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.41": {"name": "@anthropic-ai/claude-code", "version": "0.2.41", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.41", "gitHead": "0bda5fdde4bf8b8581531d625a7a48bbacc06d02", "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-tbzmDPsD+WQ/KnA92kKpxb3/PEYk1FDbpIMvbzXFuXDONXW66o4seTl4JcpBVtb9zk5wv6srTlB7M9Nn7Tel1A==", "shasum": "00476653b6638ebd4abfcd7f51ab76356e471f80", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.41.tgz", "fileCount": 13, "unpackedSize": 32384631, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCDC7v8MRlsEdTS8JanszRTf8zlECQjXsPSfwocD5E8fwIgKMLoUwDxNTN+hKWK2Fplb/vH26fs2qSVlhig5omyU4o="}], "size": 11990538}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.41_1741916081894_0.21641176849745203"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-14T01:34:42.240Z", "publish_time": 1741916082240, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.42": {"name": "@anthropic-ai/claude-code", "version": "0.2.42", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.42", "gitHead": "23fd33e9cc3c0606add986b88a433d5efe222a99", "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-8UBLTUhlh9cDgt1QWZ+FEmlkga9ZFLFffj7brcmIrxao0pLFlZjFcGWLjmJ21GlITdmLpk9gqIrONIXoSqjU+w==", "shasum": "b24561a6ff8f3f988f6762a4b0f260cdfe3811ad", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.42.tgz", "fileCount": 13, "unpackedSize": 32390966, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCcwzE73oZL4QnSdLresLQj30yK4oBz3e+GlFBCA5npewIhAIYGVRZ5c2+ooKpntVQj3Vn3So7T0tFMUwRNLIqQPgVr"}], "size": 11992497}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.42_1741969790559_0.30154955092230873"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-14T16:29:50.959Z", "publish_time": 1741969790959, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.43": {"name": "@anthropic-ai/claude-code", "version": "0.2.43", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.43", "gitHead": "5d59b6e3c0e0ee86c6c3dd4d0e9ccf5f6fef1e3a", "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-0PYNn1j5hIWSF1KdHXU5BPmtlLLHExSaoyMGmb48VaE58LHUvywLpF5u73OlbpRPnRT26/mJp68NUcfn6mLYCw==", "shasum": "fee7a457426f7eb505af578e118049bd1d5824f1", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.43.tgz", "fileCount": 13, "unpackedSize": 32390137, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIEsSbXdbZf8nq0oVtdv6gpUD3skKjV87Je0s00ssFSLxAiAgnj/Q68sFjVNsaZO0QtTUD/mGT6vBCtVDsG3ylZFizg=="}], "size": 11992093}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.43_1741992179746_0.29126856837269677"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-14T22:43:00.299Z", "publish_time": 1741992180299, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.44": {"name": "@anthropic-ai/claude-code", "version": "0.2.44", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.44", "gitHead": "ade213bf44b5c8c1014ce796fe55efd091c95edf", "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-hs0+xTsyS6DiBVUFlA/xNApoUcsooTbFFutLNM8zOA+klrcUMj1AAYlXSI/KiHt1t2AjYOXS4/qh+IxyQSJTIw==", "shasum": "0c7b7936c90eabc7e339e2546d325695a59f2f82", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.44.tgz", "fileCount": 13, "unpackedSize": 32396636, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQD9hAwGYkaYqUgk2dhDG9cILz3rMHXDs8CS4Z+QXz+sGQIgExUuMmTw3omh1CSwkVwgkS1HFSX0WFEMhAUTzGRmRVI="}], "size": 11993869}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.44_1742000664175_0.48250094493781237"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-15T01:04:24.513Z", "publish_time": 1742000664513, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.45": {"name": "@anthropic-ai/claude-code", "version": "0.2.45", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.45", "gitHead": "30fdc071a3be011f374a2aa2a59953d51f469ea1", "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-r8uSA59wuNUHjlU+snwkZBHGsuv7z5+sxDFhLxI+1wz6PU9CU2/V37k26c7YpN9OFfeRmt9hk7gb3KaIlTH2ZA==", "shasum": "1e5886be5e256f3023be8b45e82bafc58b3af812", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.45.tgz", "fileCount": 13, "unpackedSize": 32397061, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCICnPxLCznzT4aaPekqraVao+sZzVhZO+wwDEIh1qM8g/AiEAgVjM3HK9EFFa0CDeSPdQvA+OibR3BoVja1NIHmKac0U="}], "size": 11994028}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.45_1742008982813_0.010614511061515008"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-15T03:23:03.350Z", "publish_time": 1742008983350, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.46": {"name": "@anthropic-ai/claude-code", "version": "0.2.46", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.46", "gitHead": "d17b6674656c1806a3206365cadb449091dd150a", "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-6SAadsCBqYHjDAFoe8UCK012P91mYgdZet8cmqEjKoiwALxwknA1ngvF07Sklx0tFmpeGc1nFMF9VNH6Cnw5zQ==", "shasum": "b85cfaa5b90fc3792e057ab9ef343631315cb79b", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.46.tgz", "fileCount": 13, "unpackedSize": 32407988, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIBNKPytcBMA6yfy1NT2WBr/0/PASFBdctuVh8ue2l+lnAiEAiAY/KpIuwdukyN0vpSAgF7c37GSag92O6QSAD/PmGk8="}], "size": 11996793}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.46_1742249592606_0.5800376053234966"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-17T22:13:12.958Z", "publish_time": 1742249592958, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.47": {"name": "@anthropic-ai/claude-code", "version": "0.2.47", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.47", "gitHead": "a711577bfb3100fd7608691395ebc59bbe10e1e5", "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-3035KwTBfdhyDqyG5B+i/Sr11OkVItQWS8bUFdjhf577ywzKPbnZ4UKPbfP/AUFaxVQmCwx2HnnXaEtq9MiFDg==", "shasum": "09de2139fb1a605f2c8e3defce82b97e7b9eea52", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.47.tgz", "fileCount": 13, "unpackedSize": 32412057, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIBJDRzAlrrpAT5GJQVJ6/EmwzcYmt+uvr5x2xkNitTBxAiBoBcWWrguAEoBoW+Z9iOaql5ePpiocYTQUVqJhc8qw2Q=="}], "size": 11998209}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.47_1742262157512_0.016568177138406304"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-18T01:42:37.884Z", "publish_time": 1742262157884, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.48": {"name": "@anthropic-ai/claude-code", "version": "0.2.48", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.48", "gitHead": "82adef1b0705d462d8311120dd5c9de7f6987eef", "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-Av9EC7oBGSgNzJ8IFyYQnzCURtzmUZb1fRL+4vKJunCF33nCkTW82DEOKsfcj+Z268/Pf8WhXuI/vLbwpMdymw==", "shasum": "fcd7ecfc522f6f7386e7e92c044882099d42724c", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.48.tgz", "fileCount": 13, "unpackedSize": 32412209, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIA1/OrYYmE6ibFzbIpYSMQh/XqPujfguDip22Uk9QoRuAiEA6tt7Jtp6mKf3SDh1XvI0SXY890vHUqbqdCKh4Regals="}], "size": 11998255}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.48_1742262789280_0.7674440911436708"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-18T01:53:09.696Z", "publish_time": 1742262789696, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.49": {"name": "@anthropic-ai/claude-code", "version": "0.2.49", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.49", "gitHead": "971bfcb7076a78eb049da73e0b399834f097e8e1", "_nodeVersion": "20.18.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-65X8dOKwZxohHOk3T3m3udqDC7bMCQJIYcCOA704IH3dM6TvvgJANAYR3rAaIL3/eSOijOFjMUfDWifLNv/OCQ==", "shasum": "5f9a7d1130638cf8bd9d3eb5b25bfd4b8354da3d", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.49.tgz", "fileCount": 13, "unpackedSize": 32412105, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIAornPuHut2h8NJN7FaK52xMe21S91t74e/wJ3zN4DmdAiEAmYanPDy6rhFroEfWxgBJuuoQxgRQaYhSW+1rt3l31ag="}], "size": 11998298}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.49_1742325501241_0.19695459068316046"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-18T19:18:21.616Z", "publish_time": 1742325501616, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.50": {"name": "@anthropic-ai/claude-code", "version": "0.2.50", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.50", "gitHead": "9c4f2b649edd7832f36375cad44dd45b26a24b51", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-YUQFmrRQ/SSngRq9ZMXY49EsPiZJzd33uXAQLWZjVKUrJ9eKXXZOKfcofXnxXE2fPy1qpfZbroGqcPOHnB9DzQ==", "shasum": "1cd39246f14e5d67eead21aaabe2ea5a336f5929", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.50.tgz", "fileCount": 13, "unpackedSize": 32415690, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQClVc0QZu0tdVrmcHZOYau6Z5qbuwvmkEMS2EFAAjQWvwIgLoN4uNHRkIfPTXC7lKd0RUDAvdv1nkUB7GjmBM+P0LA="}], "size": 11998602}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.50_1742418406389_0.8833111913500431"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-19T21:06:46.787Z", "publish_time": 1742418406787, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.51": {"name": "@anthropic-ai/claude-code", "version": "0.2.51", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.51", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "95ee7657754999fdaadc03eaff86aea7918bbf92", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.51.tgz", "fileCount": 13, "integrity": "sha512-LRx8clB3/+NiNDiZSaIK7+OvqnFsOhjIzQxSLPIuKFd5WMPmzmWZtu4XLwKMu6DRVK4VceIQElkeu5yNZ/a0Hg==", "signatures": [{"sig": "MEUCIDVfVyh58j94qFyiRsl6SPLYIZ6J8oGzE7J0RX7akkxKAiEA9Zdw4oXEHmjVNqlFNcrIpNGl1nnQ/Q9utyjn6Mq7fk8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32426927, "size": 12000792}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "439c50889a21a37f4905918e0a338d5e014f342e", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.51_1742490904091_0.3775367269484835", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-03-20T17:15:04.548Z", "publish_time": 1742490904548, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.52": {"name": "@anthropic-ai/claude-code", "version": "0.2.52", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.52", "gitHead": "335deb5adc4b1dba8da3c886e6219e8362112a40", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-r+t2xAEoimXNJCytZ2uPGx3sP7dG6sA7kGoV16QtnAYjghB/k8YHB4Fq5sIumekYfHUWEp75CcwZ73cmIlrjYg==", "shasum": "f514d43625b7e5a502949c162c42b3163901a666", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.52.tgz", "fileCount": 13, "unpackedSize": 32426721, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC2pXZfZSQgEcq2zo8aLSb51vlsmyFZ8ZWYJ0T4hWEYEwIhAMdPb9ctOvq0t0e+QO+XDAuugUsjNW7NTKk5BXtyWodo"}], "size": 12000760}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.52_1742491266082_0.3287632373747609"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-20T17:21:06.572Z", "publish_time": 1742491266572, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.53": {"name": "@anthropic-ai/claude-code", "version": "0.2.53", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.53", "gitHead": "bd1cc0c667ca842527d1e9893d695e5f6db20b8a", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-DKXGjSsu2+rc1GaAdOjRqD7fMLvyQgwi/sqf6lLHWQAarwYxR/ahbSheu7h1Ub0wm0htnuIqgNnmNZUM43w/3Q==", "shasum": "b580b619d1c46689f76b7fe880cc70d547207ee6", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.53.tgz", "fileCount": 13, "unpackedSize": 32512834, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCwTsTJRqOcQo/cQhVZauTPcSx9eVTRzdrwl+3JLDZLDQIgC1z40i7YTPfDOZUaU5M/bbQD1XvCcyx2cIdf3UiW7tI="}], "size": 12012315}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.53_1742580242109_0.27470029680800767"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-21T18:04:02.580Z", "publish_time": 1742580242580, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.54": {"name": "@anthropic-ai/claude-code", "version": "0.2.54", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.54", "gitHead": "81a03368e5e8d4bddead44f390f14b33c5093d71", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-kl/g4oBg+FBQHRQv3INaiDuNWoYIUm4BbIk4qd5YbesFQVMPGtblh0Avst61FUo9ZCm64iBPWupPe5aaiJ6Y9Q==", "shasum": "ce6b0e1b03510224ca5f4fe23218c47acd02c9c8", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.54.tgz", "fileCount": 13, "unpackedSize": 32617343, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCrNE+wbcVyBEa9JaahQOYacIgkq6ZekiWKNImL1bmD9gIhAOOuo5O+GdblI1D7oT8HxDwOuSYC4zNk7t4kQx9rCquw"}], "size": 12046593}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.54_1742935487643_0.6445271571704305"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-25T20:44:47.993Z", "publish_time": 1742935487993, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.55": {"name": "@anthropic-ai/claude-code", "version": "0.2.55", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.55", "gitHead": "5861834efd4e5c30835f47728ec35c8995ea1408", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-FYKNHeV7uwovW4jvOA3VVTqx3nOgNPVCHmD9RXv75w2/WJpINIyjyUEaOa79bnKw5vBokZfD3GXQ9Ofe5JOuoQ==", "shasum": "3040cfc747bdeb157fe9e3c9834409f5b7827b40", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.55.tgz", "fileCount": 13, "unpackedSize": 32618765, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCEGGHoDIikIGR/Ih5ZIfKrvSP8w6lmIxaEY0msiWmBjgIgDa12NMhcQHHK9SDiW+pfxY7WaSFHrJDIQZ1ODEYnFZA="}], "size": 12047331}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.55_1743025253151_0.59502043567796"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-26T21:40:53.726Z", "publish_time": 1743025253726, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.56": {"name": "@anthropic-ai/claude-code", "version": "0.2.56", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.56", "gitHead": "a17dfd2ab4939309007d6c17a3b6e9e1ad04dfc5", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-+ouNnASXYEgR2RNB5LDTVsR8bzUBNU3n8ZN16tIzlm0jIO/kWL243zBubdrjwmQcqroYqyu1CtN2Aav/vaouUA==", "shasum": "ddfedcd11df07492e05cb0b1e9dacce1b2e4cbbf", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.56.tgz", "fileCount": 13, "unpackedSize": 32629954, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIEWxUTanCmbNf7AaK/f07+J/Zlnjtml62eI6d0n4OfsvAiBMZk0ainvqcUipt9QozRJwBbbyHoIzDthlvdFN3k5ZeA=="}], "size": 12050223}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.56_1743114336770_0.28481400351318986"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-27T22:25:37.204Z", "publish_time": 1743114337204, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.57": {"name": "@anthropic-ai/claude-code", "version": "0.2.57", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.57", "gitHead": "38f194000a8eb565aa6293dcf06666750efcc101", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-/NS22ojBphLj0zhEWV26WIcLS1JcMT809tmkiwHrb1D9fGJccbbfkLyMI7eg7TAmgnXUINWxlwmMIExdOsRXfQ==", "shasum": "095a6a96d0a92ba6c4e740299d6a0272dfb76fd1", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.57.tgz", "fileCount": 13, "unpackedSize": 32649208, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIBhRoDiQcmcKbeXsECbjWE5tVrNAVbZXTQa9BvoTWlKDAiEAwXMKa1NJJekpBtGytMDvlIhCBrJWFrbxGOY+UTBwKmw="}], "size": 12056547}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.57_1743464983534_0.6730559933380937"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-03-31T23:49:44.153Z", "publish_time": 1743464984153, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.59": {"name": "@anthropic-ai/claude-code", "version": "0.2.59", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.59", "gitHead": "5d1e781b4761548ca63cb79394dcbd44b538669b", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-hcUHEiPUmkgU00J4/1dlLgWvf5ZkWOjMpUrXhMq2o143LOElKuTGxPGt2RtmFHKk6DesFZcV/gabZYkcTqraBw==", "shasum": "9e1811d9fb5c45a60c541ac667b94507fcb515a7", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.59.tgz", "fileCount": 13, "unpackedSize": 32656814, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCICeomHTKtrCptoIYA1U9GtpYnptaSOCCmXIKI4wo07mLAiAEfp5xHvaaVEYmDyhty4N0Ix3tC5AQojiIeO2Wdan4VA=="}], "size": 12058201}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.59_1743553499194_0.5814819396390729"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-02T00:24:59.952Z", "publish_time": 1743553499952, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.60": {"name": "@anthropic-ai/claude-code", "version": "0.2.60", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.60", "gitHead": "5bea6885b7dd5d400842b497e332d2e8f4f2323b", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-axaARQIqPWdDfcS+ZyHNomIRmfkPr6AL51Tb+BtvtWrs0mP1kHPVoLP/o290U7X8XaYlP5DIpWkxjag4ES5+Dg==", "shasum": "f76a93878c7e20ac757c881fd95088d9275f969d", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.60.tgz", "fileCount": 13, "unpackedSize": 32660475, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCICRI13UdyKkcolKsyO7lOCQeqwm5BTcFUU1xRaPTcaFqAiEAhv65K4c5ImeLGjmB39oWgtAWkRp7i5wsxpt6JH9glWI="}], "size": 12059373}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.60_1743615013510_0.7811449057933926"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-02T17:30:14.740Z", "publish_time": 1743615014740, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.61": {"name": "@anthropic-ai/claude-code", "version": "0.2.61", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.61", "gitHead": "17a2f2c2b0c986eef9f116b60e946b1eef293955", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-GBSY2hNZg1MdcZpRzGinW3ouwM0vOF16ZaHLC6G9c7lJPtQzHdd228M2n4sUPPvnAUBwe8CQceo4fq01mMgcYw==", "shasum": "8f6b88959ed1f7062459b70af1ed0ade3f74e61f", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.61.tgz", "fileCount": 13, "unpackedSize": 32666995, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIBKDWd8Qpmf5fiH2kdFHEOUeom6ndbFPQqSavfAVlEwvAiB6xdl8P3L5P69L9XQD0+HvIYJuG6PHeeAqNcDWiVHwEQ=="}], "size": 12060006}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.61_1743642259084_0.2254775674899503"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-03T01:04:19.442Z", "publish_time": 1743642259442, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.62": {"name": "@anthropic-ai/claude-code", "version": "0.2.62", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.62", "gitHead": "6c05347dec8c20baf49d6e72a6b8819153c9609d", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-Mod9/kbqKy344lm5YmDJLn8dR3HYlA2zGCQy4exU7hmECNqg3KlTAz8u4O4YdiRMxXeUJ3Izi9YSJUT7oZOKdg==", "shasum": "562a787d5ea1deb0a2851dcddf82d5b01ec1e57d", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.62.tgz", "fileCount": 13, "unpackedSize": 32568484, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDjeItgxPSD+uikWcwCozw/CgRvsx5YGBa0Gl+IssAuHwIhAN16vD3W3WdYpBUY7Y+rX0A5HvXjJ6EdrqSp3/0Vv7VQ"}], "size": 12049098}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.62_1743735417716_0.8156312348082202"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-04T02:56:58.288Z", "publish_time": 1743735418288, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.64": {"name": "@anthropic-ai/claude-code", "version": "0.2.64", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.64", "gitHead": "050482b70e840ded5808ba6067a1cd1366eb6a37", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-qwXePRKDi1P/r1ya96tGXN4agHX4OnAtHj5kkL1W17CKJP+JZa64izCQL2z/iEXvA4KV59gWiYogBHzFgBqH3Q==", "shasum": "0d74a95fa735c8123073c7adea7fd66ce85402d9", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.64.tgz", "fileCount": 13, "unpackedSize": 32573661, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCzTYQMLtWMzvVidc8y4AHk41AOOi/QIFMHMBeNzbB1bAIge19o0fpdlJyFQFG/vvgr6vRxaRkRTyQE8k2e5akJvxg="}], "size": 12050835}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.64_1743800334237_0.3961025668311746"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-04T20:58:54.659Z", "publish_time": 1743800334659, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.65": {"name": "@anthropic-ai/claude-code", "version": "0.2.65", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.65", "gitHead": "cd10fd917c4768a521732614665627575bfc8c5c", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-LCxFb/WeHoHfVhQfEQGbGlFURYCm5Brcff4GHD+lVX2N3GtexLTcf0iXElAYz3S2vlWX9km8nGVfB/Yd/ieVUw==", "shasum": "f7de0d6124a56e03abcb6c837ab206e251cad209", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.65.tgz", "fileCount": 13, "unpackedSize": 32588242, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIF9Jay6gAnKgX6GY9JzAGaGK+DOqbLKxosuKaJkNuhQdAiEA08bNIIni5eJEFJaZZjmYoRwG8TjJKs+teEp54Bbxa1A="}], "size": 12055977}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.65_1744064955132_0.5503865973932316"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-07T22:29:15.495Z", "publish_time": 1744064955495, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.66": {"name": "@anthropic-ai/claude-code", "version": "0.2.66", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.66", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "76387f08a565ec678dd3d2c550aa47506042f2bb", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.66.tgz", "fileCount": 13, "integrity": "sha512-5SipNatUIQTgDLyFmrke1yv3DKaKoLcNFW/8bRagab2mtrDQWC4F9g6hZOjeXx9gEPE0ZooZDpO/Cxd6q+jjPw==", "signatures": [{"sig": "MEUCIF/bUXxzHmTg/Vcc91YHoHeN4k/9whJ7Je6SNT88IFHCAiEAlczll2cA7qd/LxJqv96J/q4rS1KuCnxoYvgpnUMvrr0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32599596, "size": 12060119}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "ebdd48769f45332db43912f7ac8665c95fc4b566", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.66_1744157085989_0.10753911141764694", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-04-09T00:04:46.906Z", "publish_time": 1744157086906, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.67": {"name": "@anthropic-ai/claude-code", "version": "0.2.67", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.67", "gitHead": "0ce8b5d93de6e04ba84280b46e983e05f2263dbd", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-z+9luCzhbmTzi/s550fMZCPbDOdc7sre4v0Ig72Svu+Ny+bNQ4TfGua5BP6E97hIummHq0DnYblkev8+RnCmlg==", "shasum": "7c5dbd7f0527740586fa2cb7e45809e9040028ac", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.67.tgz", "fileCount": 13, "unpackedSize": 32601866, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIAti0noePHNdY/KTMR79jWuWwWnMmoSKggoPPA9cp8dLAiEAtD/8fwcYO7bPgH4gSshDHbsvTaZTbSbH/JjZliYqokc="}], "size": 12060736}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.67_1744237067393_0.03214581353820578"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-09T22:17:47.830Z", "publish_time": 1744237067830, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.68": {"name": "@anthropic-ai/claude-code", "version": "0.2.68", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.68", "gitHead": "a850df8728279d2ab64d68414a3510114cd8cab1", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-3CPQbSVqBhk1ZvI/JWFGsZTTZXKihr3+yGlCt8j0WSawOw0iEIwPVr2SoFgkDvd61vq+a6M53qveUnHqwHjP+w==", "shasum": "44b806da5229df11ba16e3a1793c45eb1d90da0d", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.68.tgz", "fileCount": 13, "unpackedSize": 32602372, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIAn1h0a7oRqChQBD7tamMxrSlCpUJOxOIXcKAsF6KYwhAiEAySjy6yj3LR9RFrE4pH6EuV80cjpHXZPYDA9VgfulqTo="}], "size": 12060889}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.68_1744315494057_0.34854054053965866"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-10T20:04:54.725Z", "publish_time": 1744315494725, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.69": {"name": "@anthropic-ai/claude-code", "version": "0.2.69", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.69", "gitHead": "92679a14de100234f9614d7f774a37b7312622e2", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-41rEx924nNM8k8pJqj8aeLOYtQYFyw94aKmKQdBUoewSPIpySiJVC7VUDxm7Q+k+oJhnXB6YQY/8+qxofyuJNA==", "shasum": "109bc5f0cf7e991b68442119a6ec0ceed1c5634e", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.69.tgz", "fileCount": 13, "unpackedSize": 32603635, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIDgWzvL5q2d/mAHSYMmpEwu1x385i5ueLG0UZkP1ViZwAiEA8v9H5GkxSevG7Yd0SluPn18LZpiK576Bv005aANZIrk="}], "size": 12061132}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.69_1744410628073_0.28444906934062386"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-11T22:30:28.481Z", "publish_time": 1744410628481, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.70": {"name": "@anthropic-ai/claude-code", "version": "0.2.70", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.70", "gitHead": "95c332182721baab880d93c2c878bf00e92ea6b8", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-OwwTPIjnz2O36yaby/7kYu/94bG2AZaRvZ8BfWdDHMbGhRO+MxpEVjwy9E5mIWKv0y5IgZljqg+mfxAqghoMpQ==", "shasum": "e44519a9cc77c3363c2d3f35eb5666082aa2e844", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.70.tgz", "fileCount": 13, "unpackedSize": 32604909, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDGwvDiQu5CMTDtLbypCFeMqAYKpP75V1lH9SXvV7mSqQIgMjHTPqK9yZMRkSxrKEBBw3z0Bj9JKwWQFT5C8xHvN0c="}], "size": 12061017}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.70_1744739155151_0.20120111761067716"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-15T17:45:55.713Z", "publish_time": 1744739155713, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.72": {"name": "@anthropic-ai/claude-code", "version": "0.2.72", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.72", "gitHead": "e8c4bcbff9a0ae19c384a82fa4451226ce991a7a", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-46h1S1/ZA7ktx4kwIY2VWZmSL3wMqPrWGYDhcS8kieelxOx4u8dLxLs1einVCmNB5Jgsr3Tz9YLF69NRnEUvlg==", "shasum": "c22bc6736612d9255952c5adbb64e33d9ae297fa", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.72.tgz", "fileCount": 13, "unpackedSize": 32602849, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIHVhKaHNUjbcfHQ+f0adJf2dlUYO67KTPF+db+/Zt3bBAiEA+wg2F+Qo3ygv7q3cxp8Nozh9DPLJZDaoAASeVF8mkxs="}], "size": 12060424}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.72_1744899675919_0.07119319018635073"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-17T14:21:16.372Z", "publish_time": 1744899676372, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.73": {"name": "@anthropic-ai/claude-code", "version": "0.2.73", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.73", "gitHead": "c0a275898eeca5f0bba2120918c86172e9f80570", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-VlEfyjuOQbY93gcD7SzQ2aIJBnmHceMWPntX75suPzOVwi7wf97wnfVwYe5LTObBkAbjMDD4d56GbYz54VNKKQ==", "shasum": "06d51461fda357142f1d91433c5d65dc1508d34f", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.73.tgz", "fileCount": 13, "unpackedSize": 32604804, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIHia3nuaBJNMQ/bkYcEQAC1gMf4LlgJj5FW30J70RAfjAiEAqzsz5LBYCAXDQIGiGiBsd0eC1CPVlBCfdGZOXWLPhxY="}], "size": 12061081}, "_npmUser": {"name": "wolffiex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.73_1744994083387_0.5531566556987253"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-18T16:34:43.734Z", "publish_time": 1744994083734, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.74": {"name": "@anthropic-ai/claude-code", "version": "0.2.74", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.74", "gitHead": "26157af5cec08417a5f0e90b9e8b99ea058a9cdf", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-/ACVmaEyWTPuFeRAujnCYcDI9cBqY8J4jUgZYQU+71Px8KMmWL6Sk0LrdfQsZLtRUHLRtjUF5qvTM6Nuzxl5dQ==", "shasum": "7f23c6887ed86854a412c6787ca93349668d91dc", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.74.tgz", "fileCount": 13, "unpackedSize": 32605624, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIAMz9NDOl6E6GC6FxrhL/Da8IeAmH5bsPVX/TtLEDGC9AiEAgJ5qEtoRPPVCK/oCoFr1f172hQwSdTzGUHy0yfEkf9o="}], "size": 12061495}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.74_1745015606337_0.7167692013478033"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-18T22:33:26.843Z", "publish_time": 1745015606843, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.76": {"name": "@anthropic-ai/claude-code", "version": "0.2.76", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.76", "gitHead": "dcb9104e3625204e92fd61cbc78a4ab4ac9be881", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-O/Wl5skIOp+BuaxGoQJiMEu3Ovo4/LxivLwDFwcH/I1LSQxt4fn5D4j1hMKBOFNDLKsVY3vZdMHSHYgw2gzQZQ==", "shasum": "6d6d0114c5cf1da2b48eddd19a16ee967d880c54", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.76.tgz", "fileCount": 13, "unpackedSize": 33463365, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCICg/XcfpH9CAL9WwDwDaTb8N51zR081Afvi05d4nlrLeAiEAzBNa6y74gCZlIGifkHCMY9eebq37qFrbT6+rf3UBxpY="}], "size": 12259087}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.76_1745272765255_0.19165552054625978"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-21T21:59:25.609Z", "publish_time": 1745272765609, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.77": {"name": "@anthropic-ai/claude-code", "version": "0.2.77", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.77", "gitHead": "9e2419a0c7a9c90135aa8f73bddc9ee54ae7b627", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-txcUbMhQdTS6keQEZJTk/+evjHgDZJxZivjgTtlpw9ny2/lCphyhPGl5IelW9Un/tjVFE8t0lDKWajJGtjqFYw==", "shasum": "6032c5393dcdd400ddbeeaa5aa073e2c9992370e", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.77.tgz", "fileCount": 13, "unpackedSize": 33465090, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCZy7t7lVKwuo5w7Nty7oV5P8twTr6DbfrQB5Qi9X3KMQIgYTNu6ZnqfyCgxePZ/SMgik6o+8GFHF+dNCLCi2jAW/s="}], "size": 12259817}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.77_1745345247458_0.8688198328303751"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-22T18:07:27.857Z", "publish_time": 1745345247857, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.78": {"name": "@anthropic-ai/claude-code", "version": "0.2.78", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.78", "gitHead": "1df6c274a8acef08ff9d19e9031cefe0cc105bd6", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-kXjjTYKTp6rG7Vis8ryC4tRNij+jO75zG7y6ymotTFMTuZljGcG07daO8KuGHBz8ZGAxnzWbEQqTB7gkZ2/0HA==", "shasum": "9eef1b14d007e572c03f8cb370aa8109a70ccea0", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.78.tgz", "fileCount": 13, "unpackedSize": 33467330, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCNmN5mNJoC3KfcArGZGnEdT0cda9/g9dOJGNRc9lWKmQIhAPWv1Io4VYIN/InmZ+NeFAEdvbJe5UKZ6LRhmt6/NEJl"}], "size": 12260624}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.78_1745351334569_0.06146140091822794"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-22T19:48:55.543Z", "publish_time": 1745351335543, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.79": {"name": "@anthropic-ai/claude-code", "version": "0.2.79", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.79", "gitHead": "4937b1160de94b65e32507d6b31ee9a6402beb0c", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-9qB8yvaSDeKhVv6ITsEjTCs35OUD7hXbrS7KpAVFtc8iDLwJx6HNiwjy12amvFXOSlmrnIku7U4kOo2aql3lrA==", "shasum": "9ea27ebe40cfa18d1815b2df94f03d57d8bfa964", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.79.tgz", "fileCount": 13, "unpackedSize": 33470583, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIBEY3/1hiPOqNa5Ep3nnj8sGnvgTkh6iB301D3gyouSgAiEAqWuhxFNMXjDZ0wPs8sC0zeFjDAApv9/xeNNSfinDYSE="}], "size": 12261686}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.79_1745429058130_0.3349708110418934"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-23T17:24:18.815Z", "publish_time": 1745429058815, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.80": {"name": "@anthropic-ai/claude-code", "version": "0.2.80", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.80", "gitHead": "e8a924b5128a852e23b383c017eafcc2732ee22e", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-rkzmAcmr840DS7ErLLQH7lhWahT+3EhYXZ0ln2UdWX6YFvN7Z4yxRC4JQjrKXO0oTN3Au4QZ3AqERlB3XihvSA==", "shasum": "f6d56df697e1d94aaa00ed64068b3d6a8a83d4b7", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.80.tgz", "fileCount": 13, "unpackedSize": 33477394, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCICJPLrVRp+yhXOj0JK7jch9pZrBvynflWFKPkShAC0qbAiEA1eHXJ4A5eJYx3cnelEnRfWpWTqnGfw9MuQHytvvjvHk="}], "size": 12264494}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.80_1745510871952_0.43252215735501665"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-24T16:07:52.297Z", "publish_time": 1745510872297, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.81": {"name": "@anthropic-ai/claude-code", "version": "0.2.81", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.81", "gitHead": "a7c88aad64f41812285f5b46eea329d4c7d24409", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-7SoVXeIc+J6E3nMdyD0LOyApHgP/RCwsDDQ7va4s6146qgbCIM4Zh+L/hAJcKemBm+X6UX31QQWXnfwKmalFiw==", "shasum": "c8d6eb749f98e320b81222a00af7bc171cabd8c9", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.81.tgz", "fileCount": 13, "unpackedSize": 33477780, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIBIw8KdiK4LXlCyvJC7amGDfMya1rRj64JUX2NKhHoW+AiACI/7fEclBVccK3u32/H1uNSQcVj9fZk8muyDcPD7VJQ=="}], "size": 12264449}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.81_1745526688652_0.22981315937081215"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-24T20:31:29.117Z", "publish_time": 1745526689117, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.83": {"name": "@anthropic-ai/claude-code", "version": "0.2.83", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.83", "gitHead": "e270f64f728c402fe85e63ec3b0f41e9426d2d57", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-Mu7cIGKjNf9VMHPzNliaSuASILXIt2Vl6d28O8PQZHTfmFE1r4c+r/sExqwMN49pFAqKaJF37WfyfcJFPrhsQg==", "shasum": "31d8e4bf209bc0746437c4000c741e2eb7e59905", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.83.tgz", "fileCount": 16, "unpackedSize": 34207996, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDwDMYaltP5XkcJ5UuqYGr5wKOVuJeYnXH9QkkMdvVvFQIhAPvjOPvox6C8ar3o5tLcnozthx5vn271u3FeVgL4ODkk"}], "size": 12392906}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.83_1745601309351_0.9407609037957503"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-25T17:15:09.772Z", "publish_time": 1745601309772, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.84": {"name": "@anthropic-ai/claude-code", "version": "0.2.84", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {"better-sqlite3": "^11.9.1"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.84", "gitHead": "494b1872fe673261d188abe9238b42e179cfd859", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-9L5Z00U93mAu9nKdmpIRB9YM2Z1LDYExQtaPqrKBQV91N/JjtdXkVGaAkNUnhHbYHj/eXPJX2VDYFriZV7JA4Q==", "shasum": "cf494651af07e2742159dc92e33297776ac1c8a9", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.84.tgz", "fileCount": 16, "unpackedSize": 34208158, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIFhMvVeom2rCw9yFwtNYTVsAJByoxY+/HVesaz50wdbGAiEA8baS07uZotDCQJzGnQKV8WxcS5XjHhfsUgYB1bz6VC4="}], "size": 12392986}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.84_1745607356411_0.4448589962085072"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-25T18:55:56.821Z", "publish_time": 1745607356821, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.85": {"name": "@anthropic-ai/claude-code", "version": "0.2.85", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {"better-sqlite3": "^11.9.1"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.85", "gitHead": "f5f3c58281c1ffb5b5467d01f8a454e909bc8729", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-B<PERSON>wueIzRT4HbWubYwWp9nj2Ju/0ZKDVNXUghflpztXyK91rTjv1amFIbfWbUEvS9GEQeDqeqO4l3xM1kfdee2Q==", "shasum": "d6e983def85427dedd509a806abf8a874339444a", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.85.tgz", "fileCount": 16, "unpackedSize": 34209038, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIFeMomNMrZN9MeWQYPhDz2bg4FJizrY3cyJ7sX7feijaAiEAnrXFOWSqj/PWQTVV46Pz9r52TgbD/vBKmvmnJPRERtE="}], "size": 12393390}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.85_1745617718159_0.6373624723901985"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-25T21:48:38.527Z", "publish_time": 1745617718527, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.86": {"name": "@anthropic-ai/claude-code", "version": "0.2.86", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {"better-sqlite3": "^11.9.1"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.86", "gitHead": "1504c78d21c5d70d7d5ebfb1dff7ca11c99bf5b2", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-/tXtae5a8DzTbFJiGe1mHsgeVlD5hG5ObAsnc2yWu35vk8oCjU8w/mw5PFTMVR8K+Lcv9FNM8jmX8UfNwJSNyA==", "shasum": "b17968ae54006c4460d49bf3a48b043017a6894c", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.86.tgz", "fileCount": 16, "unpackedSize": 34210004, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQD7oWo/C+RVYflWuar8WnUacjXO4Lo92CT5uM+yRBkqGgIgDxq23B4w275S9OLucfhZmW83yblKn4B8sljyLPeGCUM="}], "size": 12393217}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.86_1745626068427_0.3766191463205586"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-26T00:07:48.834Z", "publish_time": 1745626068834, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.89": {"name": "@anthropic-ai/claude-code", "version": "0.2.89", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {"better-sqlite3": "^11.9.1"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.89", "gitHead": "d1be1e7a5d650dbe46c731b4dc862560e8718013", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-FXk1k86N2ep+YS6Pc87gRZyMnqpUue3GP4uIP0K6I6W+GSQseZEwZa8FbKcYgoqCNz1QCs9Yamk+raJ66htnJA==", "shasum": "c439144e2c88a4355436836c9b4938cf53a95c29", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.89.tgz", "fileCount": 17, "unpackedSize": 34186485, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCiicZzb/avJRsANyBw89hqMaz1b65gIGDcnT6iqIwo9AIgZ3u+2rrKQMPKsxyIX5oSwqkwawRDsVSV5bdrjK6hpHc="}], "size": 12851536}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.89_1745871311853_0.5963619737429626"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-28T20:15:12.241Z", "publish_time": 1745871312241, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.90": {"name": "@anthropic-ai/claude-code", "version": "0.2.90", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {"better-sqlite3": "^11.9.1"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.90", "gitHead": "6188d6ba704d492efeae7eb89735897def3c5b16", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-2EkO2Nna4EOe0Umy/LGOrxrkLQj/bEvLm3lpuFj+IhNT9FWT4mN28aiHxDc8BiV2TPRx3qCCyAbPqRNBf+sd/A==", "shasum": "7d695b9b7d02ef27f3796381d64aad83dd3cbcb3", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.90.tgz", "fileCount": 50, "unpackedSize": 46470349, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDVi7ZL2QRseYiVZqgyOT6iKL9DcnNcx7b3MeM3MhRzlwIgIaEXW8SmOmfHuJWdKsvXsl3BFmV7D775jjOl6bA20ng="}], "size": 24044933}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.90_1745887519130_0.6942478758982324"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-29T00:45:19.628Z", "publish_time": 1745887519628, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.91": {"name": "@anthropic-ai/claude-code", "version": "0.2.91", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {"better-sqlite3": "^11.9.1"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.91", "gitHead": "2ff33eff5ee16dce312187ac3de359461826bd31", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-7wStLc6CUfpK0j2wLV/Jr/YwVY6eXBzRirTNS4uujzgUKwXpofoSOE7R8R6GmSBkIC2eBkGWBNwR6pcwhinvow==", "shasum": "d1c6a23b424a33b25677b58dd2c99c9995b331d8", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.91.tgz", "fileCount": 54, "unpackedSize": 46497679, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCupGB6eQTLlQFP/m4lRlMorUpeVC2rNlXBAXVRaG665QIhALhZeUKCDmrN8aPF7AbDpn2psMUEKBvatXRbtZK+hOYc"}], "size": 24049770}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.91_1745957946313_0.07788436795046083"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-29T20:19:06.809Z", "publish_time": 1745957946809, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.92": {"name": "@anthropic-ai/claude-code", "version": "0.2.92", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {"better-sqlite3": "^11.9.1"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.92", "gitHead": "df866f7494268150423b4a35a0bd233eb101faa3", "_nodeVersion": "20.19.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-S/JaRJdenrN++TI7cTBUzu1PwckLlo7rAm/dNo0mSLVzLIIsCxwtTbsGZF+gDFYG3l9D8LymCr/HsuuUKG+rjQ==", "shasum": "d25619e84add08c43f8d835a7daea5a5169f1be8", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.92.tgz", "fileCount": 54, "unpackedSize": 46498317, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDBvc3xDzzJ5RuqeQh62jd9wqkpbn6O9A5OEHLk5OLxgQIgQ5V3lpyyCUAXTEOCXCGXW0E96+IFjP+/42XNJm/vAcU="}], "size": 24049837}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.92_1745960548840_0.35333437906059717"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-29T21:02:29.314Z", "publish_time": 1745960549314, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.93": {"name": "@anthropic-ai/claude-code", "version": "0.2.93", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {"better-sqlite3": "^11.9.1"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.93", "gitHead": "36e38766819a3148ad6fbd2ea98b58f12c3f8023", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-1cd1NK94a6Hhl2eUBLN4HiGIBvb2mNelKQjYYYV9+rAzohv4OyMyMvsuSUzEqMUWs4Zdrv+iNbP8bBmHn+ekfA==", "shasum": "847854af41c1f99a82e5b89d7e170992e85f4464", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.93.tgz", "fileCount": 54, "unpackedSize": 46736301, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIBRBk0Z/ywNiEDXACoEvLYlXJqDRoqmjFxADwzV5y4lOAiALicvX9nDlMQ6L6S9HMsv8dFBekmnU6NkfSi2N6LqvAg=="}], "size": 24132462}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.93_1746036882332_0.23242352461720928"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-30T18:14:42.819Z", "publish_time": 1746036882819, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.94": {"name": "@anthropic-ai/claude-code", "version": "0.2.94", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {"better-sqlite3": "^11.9.1"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.94", "gitHead": "8088e35d629c840bc44332793c649ca2f92f85b0", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-IHYUqgX9FJbKoMSf1S0727wdmXENABE+fZii0CL2mBYuQ+yxg2FApz2JNX9I7J3Ss8EXZaw3AkoQe6138sSdBw==", "shasum": "96dff2d5a4261449fe78d490f4ae4cf82ed6c120", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.94.tgz", "fileCount": 54, "unpackedSize": 46736151, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIDrjGXXNRgNzR2DrtECQz7Q/G1bp/R5iG2upL7zfebAcAiEAlJzO+400LUrkxlIFa00wDJm/eaoPIh+INit+t0gKWG0="}], "size": 24132304}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.94_1746040877387_0.18872946056460793"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-04-30T19:21:17.898Z", "publish_time": 1746040877898, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.96": {"name": "@anthropic-ai/claude-code", "version": "0.2.96", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {"better-sqlite3": "^11.9.1"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.96", "gitHead": "b436bc4c95c22fb901a5664e1e81f5ca83b34bff", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-B5t84vvH6bdIdBTViQ0osPSVfrhkLEksyipI7T/y7UzcKVKLnI7jW3HYF21bKvYdq6rJ41VWl/guowK1sSX/Gg==", "shasum": "a5bcc90f65c28993bff004809d954f031c3e0c7e", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.96.tgz", "fileCount": 54, "unpackedSize": 46737831, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIAgNXz4CdkrLMJGEmK00PHgAHAvL+7wllmp4VG980CspAiEAqQwC5c371sOB6IC2BqeUjTKHX2HihWGJ6Br7Sh2k4MQ="}], "size": 24132520}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.96_1746116628990_0.88518087772371"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-01T16:23:49.548Z", "publish_time": 1746116629548, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.97": {"name": "@anthropic-ai/claude-code", "version": "0.2.97", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {"better-sqlite3": "^11.9.1"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.97", "gitHead": "1dca6348f8d30d5f7f5ac0d90975c8e256e1586f", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-bAaxFyhlT3ZbHQwQdnw0UJ37qhCRwI6BTB4YssXgOe83A+vCpMJErE4g1gURMEPjmRAAALRXOvK2KpffW7djHA==", "shasum": "36aad9e6598e587730cb76bc3692b4113a6253fa", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.97.tgz", "fileCount": 54, "unpackedSize": 46737822, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIAwcV4/IKjcXDMaRpWnxgq4/GWErDS5QBZmyl/a4/7ywAiEAn4xw2v3DDml/OcQt8G7gvsMaRbQOgpbwTpmNAqvpeig="}], "size": 24132518}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.97_1746118387475_0.03874535053902051"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-01T16:53:07.961Z", "publish_time": 1746118387961, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.98": {"name": "@anthropic-ai/claude-code", "version": "0.2.98", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {"better-sqlite3": "^11.9.1"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.98", "gitHead": "ba0c266bf404e604dddece9003c5aa47269b68b0", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-V7gBJrebvyHGWXx/w36O9vXXPEWlrPtesRh4vLStZ8JyHyvRCHD0xd9/hRwi1VlJHSy62kIffZj/JbjoYmWVbg==", "shasum": "14c370b401f0129eb30a2a98cbbd9614d86f0bce", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.98.tgz", "fileCount": 54, "unpackedSize": 46739225, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDdwmUE6QvgkBqreZ6OpPH26A4YMqEUhyv4TncTr1JTqgIgdlr9Zz/VqtaSNtcVyxY3JwYyR64x+FsH6EbVKe2U+FY="}], "size": 24133105}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.98_1746143310360_0.2670325527896773"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-01T23:48:31.409Z", "publish_time": 1746143311409, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.99": {"name": "@anthropic-ai/claude-code", "version": "0.2.99", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {"better-sqlite3": "^11.9.1"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.99", "gitHead": "4a7a597d6e1c01a82e9448e1aa58735eaca83b89", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-KG+pFtiqSRmrSzcBRtgiPSqu9zaGg7GIlppKLEyDBlTn9M9JJbe1SLY5W8eB2AXC/fro+ePmM9cHzHO+zhkr7g==", "shasum": "6acf88172c6c10a3d962d87ab43ec3052eb304e6", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.99.tgz", "fileCount": 54, "unpackedSize": 46739627, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCUgK/fwSTy4emG2/xgKwRK53D/HbeEleWrLtWX3Sl6/AIhAJ9DDXMUHH/RTucfZOMv+WEMSi0xBKoGZWLWjGacvVXO"}], "size": 24132603}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.99_1746152895230_0.8196838270267828"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-02T02:28:15.844Z", "publish_time": 1746152895844, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.100": {"name": "@anthropic-ai/claude-code", "version": "0.2.100", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {"better-sqlite3": "^11.9.1"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.100", "gitHead": "372b4558fc69698281c4a996cd00f055716a22b8", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-SzYrQz6GtNRbVR37j0rz0bzMlbGOVrysua6/JfpPVYqeb3bjtnoOuHEebM0ko3K/bkNg68lca+WsT96tjnxvtA==", "shasum": "844e341f636f779e696bc0db285bc7bfe57842a1", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.100.tgz", "fileCount": 54, "unpackedSize": 46739303, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIAnlW+U5ZYDLp96L6jMmYw4ZLsXcYm3zJnyOu7wvp8iUAiBMiEMBVb0aRKocUUrrsyxaOD8WjBuZc96AGtVdHjGxsQ=="}], "size": 24132298}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.100_1746206137957_0.7257265206512553"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-02T17:15:38.454Z", "publish_time": 1746206138454, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.101": {"name": "@anthropic-ai/claude-code", "version": "0.2.101", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {"better-sqlite3": "^11.9.1"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.101", "gitHead": "cae5b1764510d991517073c4b28aa946cbdb9dce", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-WFfJ4LFize/YOBruAu8/4kX9AWnCWMUSvvaISvJQYxFZwUUVsPGAgtVf4VsWJMYLOSO99gU8unyyGRfRYfretg==", "shasum": "a6b3dca19119af385eeac5a17f4137ed6836163b", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.101.tgz", "fileCount": 61, "unpackedSize": 168811278, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIAXG2h7EOvFuokA11+l6aV0Etlh2EbQZSwklCIL/bQSgAiAvooU+08KWN20LI6tVctvGVheuGARdz0qOKQnBlOsSDg=="}], "size": 56141031}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.101_1746458658368_0.8626106709116772"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-05T15:24:19.332Z", "publish_time": 1746458659332, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.102": {"name": "@anthropic-ai/claude-code", "version": "0.2.102", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {"better-sqlite3": "^11.9.1"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.102", "gitHead": "8a05d20d51bd05fd1606f99c538ec0b8fb89910c", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-zJV6aOHoPhOe3n6TLd4zhoHHG0T/lc7iWiW17xIOvnE31lEALTZZa/a6VBUR+sTTOr3RAy/QfYOs5zq51qIKvA==", "shasum": "b6f59391184900ebeb0e89e75245accc6436c576", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.102.tgz", "fileCount": 62, "unpackedSize": 168291421, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIEM4anvPPhVMLdoI8gb5TyA1rC67Hj/m0yOK1ZTkm0U0AiAj8ucVVfCsLFXHesEyZEYRfJeXBq5/ijUgV+VrGTXUrg=="}], "size": 55612643}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.102_1746485930366_0.15886117355279095"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-05T22:58:51.202Z", "publish_time": 1746485931202, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.103": {"name": "@anthropic-ai/claude-code", "version": "0.2.103", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.103", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "5706f1f48e6de9773a0e5e9f4f68d4c7fe90f917", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.103.tgz", "fileCount": 61, "integrity": "sha512-xIQf2JyOiuOz6D4QEUVsFb00Wn6WfYHAGs+iB9trB7jfbxzctr9iYC2gRptSzzy7B8Y/MU8cpW8+lH5qjA7c+Q==", "signatures": [{"sig": "MEQCICiMtkZJ6eOUQDhPMuzeXi+/eadTuAS8PktZPYnGR/00AiA6TW6+CHJUeTzt0cL/YckvxQqiUHNKla8z1gYQvZ3Yrw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168296460, "size": 55613408}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "ca1f1c99b89e7455d8ff77139a8762ccc5546c25", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.103_1746575939927_0.31290019403321123", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-05-06T23:59:01.370Z", "publish_time": 1746575941370, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.104": {"name": "@anthropic-ai/claude-code", "version": "0.2.104", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.104", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "e5a4f8ed38433ffc4488e6c7603c02406dc7adf8", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.104.tgz", "fileCount": 61, "integrity": "sha512-HYeJ/G3Wa0YgDrYFSoFVfH374rmxX7T00LMuEUsLdpzJhKx6qsnfpqQmCKLCLtNda5NG5F/P7OkFSIjJSqEEkA==", "signatures": [{"sig": "MEUCIQCiN12Icl8tZzDGdeVehWXEOj7hIVm1COJ44t+jLU52UQIgNf3Ys2hKzSyQZIY+GykXRNzP2FyW7+/k91YRkHC9k0I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168329912, "size": 55623015}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "185635ce73941cd47086040d1164ace860e77ed0", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.104_1746647616412_0.25573887769583536", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-05-07T19:53:37.472Z", "publish_time": 1746647617472, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.105": {"name": "@anthropic-ai/claude-code", "version": "0.2.105", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.105", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "202540137819974a3e2505c6d6e54bca257f2553", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.105.tgz", "fileCount": 61, "integrity": "sha512-GR4fOK6WTTgMzM66eHBZsaOA2fKZULgfeZoi87v6Fv6hXGy8SPbrdAH8ULKAPlBqxGlPkUo1KcfyXD5VE28Olw==", "signatures": [{"sig": "MEUCIDaQG8T3KuHnJOP1XJaFFF44nhMp9M4A922qV8tlmKReAiEAp5x6qgnOmNPzfpiETF9BUU/ORnNgCKvy0yw5VZhIH5c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168335877, "size": 55625652}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "dd8ac684b869e7255757e0117f83eeab08df99ef", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.105_1746722493584_0.5995376791312468", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-05-08T16:41:34.425Z", "publish_time": 1746722494425, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.106": {"name": "@anthropic-ai/claude-code", "version": "0.2.106", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.106", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "845b28d84edac39ce9b23f6193a2526dbbe48015", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.106.tgz", "fileCount": 61, "integrity": "sha512-bryGjkA4VhQ4E7McxHVGgectYP/J9zhtkOP9nMNkLPepi5m85tyPHOZgTIhA3R3N7wrIRHPlpMv5y2GjGOo5TQ==", "signatures": [{"sig": "MEUCIG6ORxtmKBVPGJ57As2MIAA9kxJ9WqqbsA6AGpriRwt5AiEA3V4xVzm7wpxrNeoBtrqr28DKftdHgePk7T3LqBnY0hc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168359635, "size": 55637550}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "562a18b74f88b87874421cf12358c8bcb28a95a1", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.106_1746757990978_0.68926497417792", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-05-09T02:33:11.820Z", "publish_time": 1746757991820, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.107": {"name": "@anthropic-ai/claude-code", "version": "0.2.107", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.107", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "c4c7e6308bb8b58a98fec91ec1a499a8495430b9", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.107.tgz", "fileCount": 61, "integrity": "sha512-Z5UzGWfK3ohvuVBG0NZfPFP2S8PXZk37c6hmoWI7vtPNa7rCtKNyexqCs1kjN4oW8Ou9eIooeQd+FOXdKTTJFw==", "signatures": [{"sig": "MEUCIEWKSQ+3kAbgMFgD3WnX+nXyMbfepPcNSS5dWpyhSJN+AiEAhKHYbecxks6hGdHOk/R9qaYYPCuInSI1YCs7vS3JP20=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168359934, "size": 55637819}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "44f9ffa45f28f04074726570f595dfd930638f39", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.107_1746807302993_0.03459097147505208", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-05-09T16:15:03.847Z", "publish_time": 1746807303847, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.108": {"name": "@anthropic-ai/claude-code", "version": "0.2.108", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.108", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "4348a6a8e9fd28fcf32958cd3aa994ebadeb8005", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.108.tgz", "fileCount": 62, "integrity": "sha512-ponVKdxl4JNPVlrcYZ0+GPUkgwhRsyYxVWrF7F+15052JRAE8BI6y98FEC7Ne87/5jesXIhEz/CG77MKomxD/w==", "signatures": [{"sig": "MEUCIBaJabnPOqZSYNLeKzUf0htSUJZuvfP5BY6429ycmVxyAiEAgJObLaBgA9fvStdtddwShBKYhGpGx9x869DZC0/R41U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168886148, "size": 56168366}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "b7951cbfd93373fcd60282635d253ffd6f0c7326", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"better-sqlite3": "^11.9.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.108_1747101351459_0.8183634163677127", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-05-13T01:55:52.356Z", "publish_time": 1747101352356, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.109": {"name": "@anthropic-ai/claude-code", "version": "0.2.109", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {"better-sqlite3": "^11.9.1"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.109", "gitHead": "286ce553fe3be522d984ee0adc40a846208fe37a", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-dxOUgATehMCBZHzC47H3i6CCAf2YlXQMcTxrfzl0XEkwqEhbT8EzwWVbqDVc7n/eLykY2SK7xf5hD1iK8kr/dw==", "shasum": "539eb2fb082d631c0fa0e3d214f06d30136d2490", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.109.tgz", "fileCount": 62, "unpackedSize": 168892168, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIF6pd3fg0HowaVl/766+xNMEXSQ/8W8vRKN+N45XQXkYAiEA4vwE+9lKqN+v/sjN0QvXzTiUO6BD+mOGuj+EFqqkXTo="}], "size": 56171718}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.109_1747159034301_0.5908879686238491"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-13T17:57:15.176Z", "publish_time": 1747159035176, "_source_registry_name": "default", "hasInstallScript": true, "deprecated": "use 0.2.113 instead"}, "0.2.113": {"name": "@anthropic-ai/claude-code", "version": "0.2.113", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@0.2.113", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "dba44bbb5f4786b87ae4e221abd52a1182592ba1", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.113.tgz", "fileCount": 51, "integrity": "sha512-f+EeSzkSph8G5r45yexfGFH1TYxd0XxDe2FUCQzn1XUorFyREtNivVRZRiR6ZfWnl7D1yC2O0vaMrIvfy9vKKQ==", "signatures": [{"sig": "MEUCIHFHQMzIsHri5JcRS5/v1axIIIcY6hKeg3+tYbfZ/rgpAiEAjJDGYzmR9qi29Db6tHwXq5DhoIIOqRTVXa7aXK39nic=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168614994, "size": 56108780}, "type": "module", "engines": {"node": ">=18.0.0"}, "gitHead": "72117ef64a0432ae79f90e7644add22ac997de90", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_0.2.113_1747180388436_0.2983733785890186", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-05-13T23:53:09.326Z", "publish_time": 1747180389326, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.114": {"name": "@anthropic-ai/claude-code", "version": "0.2.114", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.114", "gitHead": "23c34bd6c3c942d0c01bbf27f927d84f3cdf4bed", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-Wa+5VcY0jTWAuwBjguPzER5qNXpaT2Ye2ssaCADrWjWHv1gRp9OEtnX8mYPypywyB6rmKeMzioMDrtHAW3VD0w==", "shasum": "8e1a63665e047982d2fe49d0a1aec891dd054124", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.114.tgz", "fileCount": 51, "unpackedSize": 168613687, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCm+Az0NdVyC7riVzkIcIgoe3pBvG87FvmJJ2kljjpvWgIhAJ/ybDxmb8cfxaLaKzRyWSNOFjS3t/Aai5fM+JVxdpJ4"}], "size": 56108513}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.114_1747240234947_0.7248795366562046"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-14T16:30:35.917Z", "publish_time": 1747240235917, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.115": {"name": "@anthropic-ai/claude-code", "version": "0.2.115", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.115", "gitHead": "9de6fa75a372026e7f71d98cd73ac8fa93efee25", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-hyCDa/WQTbPZEhnrzOfOMsJVaZNMZVHKeMwQ8KQVjSvCXlsEaOotSJWB62uyAXCam1DXJ3ZDcrHNvpodMzJgaA==", "shasum": "c1e365e8ba870d36bea3642df6af01f96d0f792c", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.115.tgz", "fileCount": 51, "unpackedSize": 168619084, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIDpOZ2FQQkp6zjZjA9+8Omoz7AbcYKczkQfyOdFyGLspAiBOyDSClV+ri7ntzyw6vERpoD7NWB7UDk59oP1CU4RJHg=="}], "size": 56111764}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.115_1747324650733_0.5156721663524555"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-15T15:57:31.601Z", "publish_time": 1747324651601, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.116": {"name": "@anthropic-ai/claude-code", "version": "0.2.116", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.116", "gitHead": "3e33f57a63cc6b6ec5fd17cd0950308611dd5aee", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-APM1K+tY1IJ5lbvy8KUgokll1BJF+/Id5UmT/vTzuqAdiVHi7bBHe3a5fpI7nP/ye3T2T9kS2Lc26oQfRUfRrA==", "shasum": "d4bfb2d8b0184fa481f5c35d7218c5b3e77587d6", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.116.tgz", "fileCount": 51, "unpackedSize": 168165052, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCahIUjS6vHwmAJpkJTyqetqAIgcpc5L4lKDrWzx7tJLgIgRAkJZRtefMX89oo2Q/YHE7Q6YDFL+3Lji+U6U8Qt1RU="}], "size": 55649597}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.116_1747513309093_0.6558324843379173"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-17T20:21:49.934Z", "publish_time": 1747513309934, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.117": {"name": "@anthropic-ai/claude-code", "version": "0.2.117", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.117", "gitHead": "782ccd48dcfa9c81bd87d9a16b03906c00a539c6", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-0mJr2n7bxoBLpxT7DjhOXkVK7lZB3ML5uUAhC3pny/IipF6Q+05XPO2UNoaNLB/9yhXSUmu0cNwmHz6m1e2Oqw==", "shasum": "63f1c138e2c47320665e6627f112398d2ec8afcf", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.117.tgz", "fileCount": 51, "unpackedSize": 168173417, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIGk4QPL3qiSo97kZlXq+Yuz6OcrlJV7Tmb/lY9yBvBEAAiEAzqmzmzIefj5JJE8m/4ODdiKZsLfTueB2OpU1MrcU5es="}], "size": 55650352}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.117_1747525886430_0.37135216539670246"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-17T23:51:27.284Z", "publish_time": 1747525887284, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.118": {"name": "@anthropic-ai/claude-code", "version": "0.2.118", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.118", "gitHead": "6233cf933fe9b2e18f49d42fd4eaa2c59db4e625", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-pMz0P7rn/Dl/epaoPCPJnK8yFy7HlqmbFgHU/Jx/WNSlCkyEYWJil2aaGoCcFlnv5FVWak5eU5uXOcSGfopfGQ==", "shasum": "8bf0740da7a8cf27e20ce5af074e1b21753a8865", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.118.tgz", "fileCount": 51, "unpackedSize": 168172469, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIA/83zYO2g28k7tg7ysbKwT7cZ5la8RoeEewQIdJV5N6AiEAiOU7fSenKrjwL0IaJnnxC1g2V5OXcReiMmRyJhDuVbQ="}], "size": 55650111}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.118_1747587269527_0.16812414206641613"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-18T16:54:30.382Z", "publish_time": 1747587270382, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.119": {"name": "@anthropic-ai/claude-code", "version": "0.2.119", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.119", "gitHead": "31463d251f0d62ba912022a06c74b636a990d76b", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-fS73GNzZi7kr8tmohj3aFckhHmf6/65hD7YzXSO7PgokLkU+xldZmo+QEYHG35dyLnnh34Tyewc16usSToB45A==", "shasum": "71b83fbd0d0be9d6b15fd1b6d9856949831bbb12", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.119.tgz", "fileCount": 52, "unpackedSize": 174630535, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDsVUr+h+kLq50ToPw4G+MAQXPT6R/ouX1K78Pht6yhCAIhANtZDdmDB6vi4xYNCNOdDM9yPy4XyNqjUoApOCwRklLC"}], "size": 57477527}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.119_1747628814963_0.27746598700598835"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-19T04:26:55.850Z", "publish_time": 1747628815850, "_source_registry_name": "default", "hasInstallScript": true, "deprecated": "deprecated"}, "0.2.120": {"name": "@anthropic-ai/claude-code", "version": "0.2.120", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.120", "gitHead": "769a92bce62ce2fd988ced63100fa16d2f12e941", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-VRKDu1iYAExfFi24o3etWvXCq7WJlXYTZ412Qh7kUarxlMF+3Sq1KMrBdLWffZiT944EC7O7l92EbRUD87Mmpg==", "shasum": "cfe62a5d27953e0f1a7515a1b6c39bb1bedc4f4e", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.120.tgz", "fileCount": 51, "unpackedSize": 168175314, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCNdvcyzIQ9dfa0x31TPcub3gY/NkGwkf/F1Qmp9NpAywIhAKTA+o6s0vbovfElc6fk4TUR9U+NWiMa6M0psGnLyoc8"}], "size": 55650765}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.120_1747630752972_0.05394457359110949"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-19T04:59:13.833Z", "publish_time": 1747630753833, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.122": {"name": "@anthropic-ai/claude-code", "version": "0.2.122", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.122", "gitHead": "f3471df94638fdb6497321443b4cdac8fd601a51", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-q9XnW6a4btqHM2XYxkcl2d7dDNRTX8pvaeisiNWYzAOSKC+wUfOrkioUUS3BG+i6sNtJB03jPKJdqvEvtXbZjw==", "shasum": "01c6cbbe3d860934816db191202e46dd915d2eb0", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.122.tgz", "fileCount": 51, "unpackedSize": 168208129, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIGdjBPePccYsgapGUee61y3EOM8Zu8GCIEEXJPUDjs4dAiBsECDw0aCP63r0ML9AN71wuvmky3UMqPB/EOs65bGD9A=="}], "size": 55683807}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.122_1747673375827_0.9468288382497045"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-19T16:49:36.680Z", "publish_time": 1747673376680, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.123": {"name": "@anthropic-ai/claude-code", "version": "0.2.123", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.123", "gitHead": "a1af6adf60dfd5ac18cdffb27d723e84602ef9f9", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-UpnhrDAh8UUWU+lVuhJl27sn/CEMtjMsM1/jJJvv0JPjNacIhl/tlkSoXFzE+eu40z7P1GNX3HCvLXFu3FemQg==", "shasum": "782265c9c6320cf25dad7993c3474a9461aae30f", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.123.tgz", "fileCount": 51, "unpackedSize": 168417440, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIDH7NIImelLHqwVPFV5MJToUs1C1N8Y4Ja6zS21wmVNkAiAtnCbJNXGQ3gwCYpPUs07db3hwGR74nDineESbEdlkhA=="}], "size": 55726262}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.123_1747768950507_0.04372770640102086"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-20T19:22:31.365Z", "publish_time": 1747768951365, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.124": {"name": "@anthropic-ai/claude-code", "version": "0.2.124", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.124", "gitHead": "b5b62056f0519e200da8bfee037321c5b5b8293b", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-W/sPzrDhZq/M6vOK0SsWBWbHZ1SMsstq+tPjNCz10e7l6QVrCxfSSHAKIJRgmXAJRl5cEAt2pcu4Wor0cGimbA==", "shasum": "a2c7fd137a2ae0697ef8855f0f47f0e380a5fb5c", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.124.tgz", "fileCount": 51, "unpackedSize": 168417445, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC64g0ZZv9M7hY2bhl0ZQ2Sleryif8LdDd7Cq21UD6zywIhAP3SQ23l0hB98LL+dx3yfSIqQCWU3TsTUK2waQ0+D5E5"}], "size": 55726059}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.124_1747772899190_0.8796575084565779"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-20T20:28:20.021Z", "publish_time": 1747772900021, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.125": {"name": "@anthropic-ai/claude-code", "version": "0.2.125", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.125", "gitHead": "368e9c778ca28481fea03451fca72c68c05fa922", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-HP1COHPpsF3aJC3RxsaMp+utiBoCId3FSBhSvGPi8w3y9MBnYE1S6ku75AV9PCm+bY8wxGgDNXfHhhqqaxLXLw==", "shasum": "3af47707395eb958a6315ece3617519300179761", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.125.tgz", "fileCount": 51, "unpackedSize": 168787650, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIC7cUAuphs7SD1YKWr50ykKOJ4f46TzdkdPEGTY9MiDDAiEAndVX18Uok0uz2Nhq0F9HkSJc14F/BTAgizcvrnDBWiY="}], "size": 55873250}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.125_1747850410669_0.8796100950044463"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-21T18:00:11.547Z", "publish_time": 1747850411547, "_source_registry_name": "default", "hasInstallScript": true}, "0.2.126": {"name": "@anthropic-ai/claude-code", "version": "0.2.126", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@0.2.126", "gitHead": "a86f61319be23e0c03956a58c6abef370ed8accd", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-YBAtMs0OobKlrmvCicl8DZa8uLOSiaorW+V0aVYeGVU6q/lf7ZoPmckZT2B3e8rS43tgXmjadK/KHdJ1UztT6Q==", "shasum": "15ce31919d168b6c848d7268457b02b64db9a8b2", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-0.2.126.tgz", "fileCount": 51, "unpackedSize": 168797165, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIE3pv/bTyzMcpxAvjoXtxZK9YTNDBXlfqLf1k5+AtHzuAiEAinQPTlTjNK8nSbSsLPXJXiU6aSQ+EfHJh85aICR4LyM="}], "size": 55873913}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_0.2.126_1747880527027_0.5528580262120526"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-22T02:22:07.953Z", "publish_time": 1747880527953, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.0": {"name": "@anthropic-ai/claude-code", "version": "1.0.0", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@1.0.0", "gitHead": "7313027bbfb09f241fac7c78bf29135a87f8037b", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-5+FV9oNtV3sZ8rq1pw3B6KX24Ib+qs9FoLR1ghxOAzk4ZXKzCSSHhW4GOWJy+6N9926KHN2wFNVcuZC9NW75Xg==", "shasum": "e28b1848914925f16c086f555e6c1351f73463a0", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.0.tgz", "fileCount": 51, "unpackedSize": 168828650, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIAv+WbwXmIMWVX1dhOKr6bGjlMxakWUD827oSvItDpiYAiAGpp3wntfr8QPBWT29GrtKa7mX9Xr8UcyMdMu6cu9hjQ=="}], "size": 55881682}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_1.0.0_1747933037194_0.8931532341735808"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-22T16:57:18.061Z", "publish_time": 1747933038061, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.1": {"name": "@anthropic-ai/claude-code", "version": "1.0.1", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@1.0.1", "gitHead": "5034bc9b81029d9fbd5c6cda0e5a5d5e5bf17e06", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-tMwmy2ESCrGd+mgiW45wCQtQAtsVtQSt2pcz+RkkKt9zgHaO5UqqzCJEmpf+4UBJitDp1ZQ3NiL+rxYtTwI51A==", "shasum": "d28ac92f34b84d6d8d0adfb101609dced7272e49", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.1.tgz", "fileCount": 51, "unpackedSize": 168828876, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIBJyqC2Yp36O8qlDQY6dxkWmOe64W68jMYxGShQ2RXPGAiEA7ZnG61pxoagAD3Hn0HsZv57nzngKk5b3WGxYUs+hIz4="}], "size": 55881831}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_1.0.1_1747941816743_0.43994540457029774"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-22T19:23:37.662Z", "publish_time": 1747941817662, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.2": {"name": "@anthropic-ai/claude-code", "version": "1.0.2", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@1.0.2", "gitHead": "08ac91e2586dfff9e78091eb0002d1c53c470c40", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-6p7RMXEcVfymsJ7v0GyMfA3gcD2AbFVSb9b1Py4tbxM2ensjyaSx3wVQW2QiFk+9mhoRkYc0ETVnJ1x/G0LW7g==", "shasum": "8708037720c7ffbe96c4a4b3eec458b57891a7bb", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.2.tgz", "fileCount": 51, "unpackedSize": 168828831, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIG33LnhvL6QRNEoz0M/SEGEVg2mL543IWAvnYznlUzWqAiBpvEB+a//48lbLF6Y/0B//9FFIu5D6JJxGaPdAs6fekA=="}], "size": 55881850}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_1.0.2_1747950409986_0.08438328100352721"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-22T21:46:50.893Z", "publish_time": 1747950410893, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.3": {"name": "@anthropic-ai/claude-code", "version": "1.0.3", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@1.0.3", "gitHead": "54491bd2926f5856e119903a4c58a1eddccd0b76", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-a4aBhcXGG7k4YtyF2aYVKt08h48BG+F12RA/G3K1vb6unl9CAvWDP/A7xudtCOzxFKxLwxctrn74pHEdpmHSPw==", "shasum": "8bf41d5c338f66737c25c48df500088e5343c1d2", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.3.tgz", "fileCount": 51, "unpackedSize": 168827655, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDB6ygq0x0F+8ixd+xmVQmxWczzmAMSox3WetM88c+SMwIhAODegvYtRcCf4ugUIzDLdbT6wBEavCbuuHFV3+0+Vd5v"}], "size": 55881488}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_1.0.3_1748040372403_0.15166697725473477"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-23T22:46:13.282Z", "publish_time": 1748040373282, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.4": {"name": "@anthropic-ai/claude-code", "version": "1.0.4", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@1.0.4", "gitHead": "6bb6ecc153901077f6287ccbc0fb489143dfba66", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-INnMutAQ4K7wk7Luau9IQ+YSqTls4UUMYq9tQjwH3H/rqOn46XHW1Sg8qO01uobyuijut66XDQJYY7yC6nq22Q==", "shasum": "34bc3a8239a9481c9f1561578b03c54a43a64272", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.4.tgz", "fileCount": 52, "unpackedSize": 168841040, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDb5HN/P1zet2+wPHyFfq8wRTKXuS/WHOtiJgA6DwYfBwIgKNpg8B0IEUPvQXCGo7IxAH3E/i6sSsLEYKt+Sp0iwCs="}], "size": 55890059}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_1.0.4_1748454163459_0.9337302704658563"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-28T17:42:44.392Z", "publish_time": 1748454164392, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.5": {"name": "@anthropic-ai/claude-code", "version": "1.0.5", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@1.0.5", "readmeFilename": "README.md", "gitHead": "edb526975a3cce5a458d90a6ca84c48ab1dc188f", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-A4RkIuHktcNygeZXfj/EcF1M5KXlPSwBBXLQLldUNyX4DFAeCpKh9b0UqnLlMd519QBmfmRYDvZqUlt7Fx0a7g==", "shasum": "9ad1aca30d6efd4a499de20be4f2e22d9e8383ee", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.5.tgz", "fileCount": 52, "unpackedSize": 168842788, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCICKJ9Q/ffJJ/WweAGx91EhJhtGyXFvzSKxX664ncPRcoAiEA1gjBBYvYpg8EuXZXaD08YYQzwQelZnHLauz4pyH3ZOE="}], "size": 55891435}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_1.0.5_1748473682036_0.5705154015547615"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-28T23:08:03.022Z", "publish_time": 1748473683022, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.6": {"name": "@anthropic-ai/claude-code", "version": "1.0.6", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@1.0.6", "readmeFilename": "README.md", "gitHead": "80c26058757c6423dc2b4413e22bbd2739455034", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-QqPZZ4IXbUaSLqXEx4z68Stjc7NlGh/b4woyYyMks+cMUJ5ZYDEkAe09cRZQ6tjubZNgBaGTOqfTaT8NgnkioQ==", "shasum": "971dcaac7ce01a94325e8307b5d64654a933d000", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.6.tgz", "fileCount": 51, "unpackedSize": 168814346, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIEdQb03J8rpuJ1NM3H6BSgHqFWV8uLOAb6f81I7p6bSiAiEAtdzofm+XyDG2fvQVWmdvdDIf6Qr94JE2Be7KD8d/8bM="}], "size": 55883495}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_1.0.6_1748536438345_0.9433181591643349"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-29T16:33:59.854Z", "publish_time": 1748536439854, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.7": {"name": "@anthropic-ai/claude-code", "version": "1.0.7", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@1.0.7", "readmeFilename": "README.md", "gitHead": "0549cd730d0e412aacecfc2d0570fdd67e3ce7dc", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-DVkLtDVz9xKHsgm7L9AEcelWGQ6EYEKCKyytKKWHOT4pZ0Y1zUC8kZJtpOhT6MFO9p3TUvVIifJuZ3QUTqc0cw==", "shasum": "da4c4b583820683644e04d3742f6dfafad7eb59c", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.7.tgz", "fileCount": 51, "unpackedSize": 168847815, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIDBUN+svKLyCnMuIMohJ9QEt6oLIilzeiIATkkTU5iOjAiEAraaEzAZaTOJJM0mQH7RHAWNZ61QttPzAwO6JXd9HZ4s="}], "size": 55893692}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_1.0.7_1748622588817_0.3186010652797706"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-05-30T16:29:49.709Z", "publish_time": 1748622589709, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.8": {"name": "@anthropic-ai/claude-code", "version": "1.0.8", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-darwin-x64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-arm64": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@1.0.8", "readmeFilename": "README.md", "gitHead": "945dbd8ff57da023d8d6e6ea554be76a6ceefc2f", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-jY4FMOARziPKi8E4ceQ9sof4gknUrWS7/LdzRU8Wr8ynsGoxkOjSxjXQKDjA4M8i+nxs6uJIV/s43hS/cIOkDg==", "shasum": "dd14378250c153c4edc003451e336e390326fb91", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.8.tgz", "fileCount": 51, "unpackedSize": 168855274, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDqPOoP6pnw89S8+XEZoVI2BGp2VGTgL7cdTELSq5mcXQIgI7/tN0NfdD5iZnWGcKUG9amDfpim5eU8DHhVhzYdh2U="}], "size": 55894956}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_1.0.8_1748880991684_0.4732990983561929"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-02T16:16:32.619Z", "publish_time": 1748880992619, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.9": {"name": "@anthropic-ai/claude-code", "version": "1.0.9", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-darwin-x64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-arm64": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@1.0.9", "readmeFilename": "README.md", "gitHead": "12a7d6c8a2d2921848c4743565fc4d124baab3c5", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-bLjWQV3QLw5kn2glu3ncjUDtB9zJzuzRjqGDjfzXEA3QbXSL7E/x5o+06qu7w9hd8CQzIXq6t2hKTET3Hlvitg==", "shasum": "afab4aaebbe63b8a13472d45df154e90c2a36512", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.9.tgz", "fileCount": 51, "unpackedSize": 168858056, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCgYvqZy+x/iNI6oU9KqxISclcZpO6EtIebOx14keXhvwIgYLrEaQ96nFRvmH+r7Ccvf1mErc7o5Be2RMWp6Rgjbs4="}], "size": 55895314}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_1.0.9_1748896633705_0.14757059839129294"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-02T20:37:14.617Z", "publish_time": 1748896634617, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.10": {"name": "@anthropic-ai/claude-code", "version": "1.0.10", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-darwin-x64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-arm64": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@1.0.10", "readmeFilename": "README.md", "gitHead": "56c1a59c90015e0de0837d80973785b9fa0943ad", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-MQ4+bDwjaf5BzAlujREyhwJJuBtgnbNI7DYiEaRB+BeoMxovRYLQXyo9/WK+NIhovf8rJQNXP9q6+wHrN1BpZw==", "shasum": "3d1d8db7a4c6a7514a9105fb60d83576304cd5e2", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.10.tgz", "fileCount": 51, "unpackedSize": 168891643, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCacquJiH5hMSJaBQSnmeLug6K0J97Baok3vl4gHvhDoAIgfLU/F8csivB5JMaSWzvus6Nab6VTeT/ah7djNs9NUkM="}], "size": 55904599}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_1.0.10_1748973680159_0.5469149829274984"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-03T18:01:21.056Z", "publish_time": 1748973681056, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.11": {"name": "@anthropic-ai/claude-code", "version": "1.0.11", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-darwin-x64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-arm64": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@1.0.11", "readmeFilename": "README.md", "gitHead": "0b0572b0f00104363fb61692e9273134758b4e12", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-ezYfkSxCiQjReJoBJkayTpzhWRWKmfEy6Ria2TdufBmDR7Kj/iP4IY10M5JOTgB8pw7XfjYpijgnomiFZmVRbg==", "shasum": "fef12708b5150c868a82c361341f3d7672551c11", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.11.tgz", "fileCount": 51, "unpackedSize": 168908839, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCRzTfCaT5EoaD8SzgMO39sVGHqUvkXVYssz44evu95UQIhAK6/mGWuUqOYn3jtAFlcIvdJzOd/+kjQl+ajPy7+Ee1v"}], "size": 55912657}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_1.0.11_1749055315037_0.7203923884374761"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-04T16:41:55.904Z", "publish_time": 1749055315904, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.14": {"name": "@anthropic-ai/claude-code", "version": "1.0.14", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-darwin-x64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-arm64": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@1.0.14", "readmeFilename": "README.md", "gitHead": "60e0f07d109b6ecbcb04e09e759f37ab931cc96c", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-A4YGHvWJ5aYIiFlKvr64ZT4l2wit33E35w8dCxRI2LcC47+dJpCiEHjDy6D+08c7W3DdUf9WUC+AmoDQ5Dxdkw==", "shasum": "0fe73c3c2030adb952f153df806b1f68eca0309a", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.14.tgz", "fileCount": 51, "unpackedSize": 168911161, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIELa5m/5VuFlr34u2+RAcQn3DnWkoMCqwuisjz/vmTUmAiEAtGMu7boONe1mQEWa9VGOnuqSjx/bdBmVW6dVPr43Dko="}], "size": 55913330}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_1.0.14_1749156507354_0.7095138147688611"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-05T20:48:28.251Z", "publish_time": 1749156508251, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.15": {"name": "@anthropic-ai/claude-code", "version": "1.0.15", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-darwin-x64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-arm64": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@1.0.15", "readmeFilename": "README.md", "gitHead": "1ffbd69be402bcd6acf8898ae8164fc553d60987", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-hsmHwFDqLhQqvULY84rLYnv7xAzVJ4HwyTyI2f5B5PCd0pQEdGdEzB1wMvWo9UGuTJdF6zhkJ9PMgqejJc6zhg==", "shasum": "dadc0daf2c58419ac31de1c76d21ac9f26c7e85f", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.15.tgz", "fileCount": 51, "unpackedSize": 168911244, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDjRmA4rEBpA3tLEgDpWWB1j7vvHmAmZusuuwAzRZWcQQIgYsjX/vkNfLNPl3OUW3wWBbwBhTVVFCEk+Ns4IUlFWJo="}], "size": 55913742}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_1.0.15_1749157414290_0.3408763847431848"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-05T21:03:35.216Z", "publish_time": 1749157415216, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.16": {"name": "@anthropic-ai/claude-code", "version": "1.0.16", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-darwin-x64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-arm64": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@1.0.16", "readmeFilename": "README.md", "gitHead": "44a0592d2efb607a1a3ec3dd441103789782db9e", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-Lqf2yPnNF5fMZ4xw41lON+4J5RLNjIYO+2OgoLqzxRTT4u3gLQJ2uSrW0lSL9otDThMiY5SokzbMU1MAm7KKPA==", "shasum": "2c2759078f01977a4394966a62c164ea022a6c69", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.16.tgz", "fileCount": 51, "unpackedSize": 168913086, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIG01A9edFxE6BTi5sGiQJdMOVvLvQJv4E3A4QFWEaYkmAiB1uQ/1Ya925VfGz80jE713e231UViVKVM0YzqlYzHs8A=="}], "size": 55914158}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_1.0.16_1749172261977_0.5162259801441564"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-06T01:11:02.870Z", "publish_time": 1749172262870, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.17": {"name": "@anthropic-ai/claude-code", "version": "1.0.17", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-darwin-x64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-arm64": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@1.0.17", "readmeFilename": "README.md", "gitHead": "0aead18e65cbb31883e71e64077d17c873370bbb", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-+MX/pKOKXG2HLSh7WJIgiILdumqRgcORUX0iSQmAfn+UEoHSYSuFWZWuWBpixaa8W4tiTCC06uN4pgzinCU6jw==", "shasum": "f1f238d122bbdbdbf1f2e17b3bbeaaa6a379baa8", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.17.tgz", "fileCount": 51, "unpackedSize": 168913216, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQD5CVcZYLpcipPLQ2Uspu/2CuKtejen+qhbZqAUQc9RrQIhANqxNfeNfLhWCnIhvSgkAnOtFTGpEgt6OSXepI9BnCGN"}], "size": 55914403}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "jspahrsummers", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "jenan-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_1.0.17_1749235446625_0.19989086275689472"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-06T18:44:07.505Z", "publish_time": 1749235447505, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.18": {"name": "@anthropic-ai/claude-code", "version": "1.0.18", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-darwin-x64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-arm64": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@1.0.18", "readmeFilename": "README.md", "gitHead": "48b29cd2e89eb255c219e237d078ab441fcdb956", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-UXg3DkT64IVbtdHyrqUly5/IHGC1J4MXyNP6R4yycePucNK59ViVprMHKkWMdhCeiYMx8DkA6frbuBTYYKavwQ==", "shasum": "2521695a1f55dc830fe64af62b7b277e2c083775", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.18.tgz", "fileCount": 51, "unpackedSize": 168928477, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIF7k3+TF4MWqmy+fCUnBkf/gcIG3XOoLtuLql6c90BitAiEA3NL9JMHJc6Mc2RfZBeaqv42WoBkkkLuIkhW8zHC1l2E="}], "size": 55917243}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_1.0.18_1749511678290_0.5290679296261493"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-09T23:27:59.204Z", "publish_time": 1749511679204, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.19": {"name": "@anthropic-ai/claude-code", "version": "1.0.19", "main": "sdk.mjs", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-darwin-x64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-arm64": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@1.0.19", "readmeFilename": "README.md", "gitHead": "08726bc80a6a1ab2713da145449a99497e7a2669", "types": "./sdk.d.ts", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-lejDYytnk5Zh/qhHDplTSNDslpEJOn4V4GN3yVry8ZAdXhc5JGtOpyCoDZPTagtK3yuZ1SpS3R0XFDIPzdJ+eQ==", "shasum": "a022e4f7b3e8d37e3ef1b80695f69fb0787728f2", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.19.tgz", "fileCount": 53, "unpackedSize": 168940905, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCS/Mh9FZ1LI4kVYd6lz4ydPVLUSpt+q+Pw4oLc9SfoSwIhAIjVYIUn4ozWy3q7JbW78kyzkS/dmHHUwiO5sX/sxmzN"}], "size": 55919220}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_1.0.19_1749580577369_0.09308785646292028"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-10T18:36:18.261Z", "publish_time": 1749580578261, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.20": {"name": "@anthropic-ai/claude-code", "version": "1.0.20", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@1.0.20", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "da606e6d870c4080d3a7e1bf17c6b9c5a7afb7d8", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.20.tgz", "fileCount": 53, "integrity": "sha512-GdgDgbVBHxSYkFahpxhZWNs9MMScyr9c+w6GhHYviPWLAnEIA6mHyDDWg96Q3Q62vQJ5IDnerUCY/Fd31E3V+w==", "signatures": [{"sig": "MEYCIQC6YshtUlHV5of73Ikon03cz6ggPLSev0KFDohRFeWiLwIhAKHn0Xe/9yRiTbkMUEJRD05pXS2UOSj2T5Xhb5FAI030", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 169088967, "size": 55957073}, "main": "sdk.mjs", "type": "module", "types": "./sdk.d.ts", "engines": {"node": ">=18.0.0"}, "gitHead": "09ba443d278d9dcc038f61c395d032b36bff7557", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-x64": "^0.33.5", "@img/sharp-linux-arm64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_1.0.20_1749665986699_0.4306234660669348", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-06-11T18:19:49.429Z", "publish_time": 1749665989429, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.21": {"name": "@anthropic-ai/claude-code", "version": "1.0.21", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@1.0.21", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "1bbcc3a4fff7ffc65b76cd16b1a6dda77f57be90", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.21.tgz", "fileCount": 53, "integrity": "sha512-Ig+OPSl7e77SJrE2jB8p4qnnyS/+iGItttIxh0oxkZI3qSKu/K3Z7zx8r4YTDCo7XJsoYnV0MNsDBcXWa/YKUg==", "signatures": [{"sig": "MEYCIQCrE6gbC3STV0yU+8Gb6edVhHLC7WcLT80TzfrsOjh4BwIhAIafr5pUO3D9zF021TAhNX63BHEFRuNlhd1v104JJFoB", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168958590, "size": 55923241}, "main": "sdk.mjs", "type": "module", "types": "./sdk.d.ts", "engines": {"node": ">=18.0.0"}, "gitHead": "86ead8151a648abc787003d8905fb6af68813de8", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-x64": "^0.33.5", "@img/sharp-linux-arm64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_1.0.21_1749672407313_0.8872543450810788", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-06-11T20:06:48.186Z", "publish_time": 1749672408186, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.22": {"name": "@anthropic-ai/claude-code", "version": "1.0.22", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@1.0.22", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "69a8a9e812704a3269c11f2b736e2698fe97f72f", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.22.tgz", "fileCount": 53, "integrity": "sha512-vqfDHq4KNtUzUiwdsls8+2aTrC1Rn96iH+yOyCrBjHjJwojzU6pt3MOAMkJHMeErKeGqve+I7HM6mFw0OgZgqw==", "signatures": [{"sig": "MEUCIQCv64h1qNIbDP5vJAtFIFzZ9Xa/OSsuHBbOrvquiS7tnAIgJJ1XRI3RBRnsQvjkc3W+6eGtHlKYpwY7TkeQq7QxsEw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168967762, "size": 55925069}, "main": "sdk.mjs", "type": "module", "types": "./sdk.d.ts", "engines": {"node": ">=18.0.0"}, "gitHead": "d0812f4244d0f2536f9d09e8b0dbce39afbb7c22", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-x64": "^0.33.5", "@img/sharp-linux-arm64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_1.0.22_1749743608200_0.5377392667188174", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-06-12T15:53:29.220Z", "publish_time": 1749743609220, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.23": {"name": "@anthropic-ai/claude-code", "version": "1.0.23", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@1.0.23", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "224c603db069fcecf535608187517d5cd4504675", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.23.tgz", "fileCount": 53, "integrity": "sha512-bOoA0G0RKGoN9wCX5WlCj41rxk7yyXtCN5K43uc8TVgs4mOOVZySKuw4HuZ/aPOl1khGDG+F2y4ny2lMICpiSQ==", "signatures": [{"sig": "MEUCIQDrqnG3P7EIh2JqsIjFH21/0k0e+qM8CJf3FNZ5dBeahAIgCLEJwiIaCXc+C9/mcHHwMFO1qtErJ9iWESvDkxpGdyQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 168971291, "size": 55926687}, "main": "sdk.mjs", "type": "module", "types": "sdk.d.ts", "engines": {"node": ">=18.0.0"}, "gitHead": "0be78b1734be15b4fd8753b988d361a29b113998", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-x64": "^0.33.5", "@img/sharp-linux-arm64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_1.0.23_1749840619201_0.5107370476581405", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-06-13T18:50:20.083Z", "publish_time": 1749840620083, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.24": {"name": "@anthropic-ai/claude-code", "version": "1.0.24", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@1.0.24", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "3b42026d183ada41a6e372afa5a6e71df69024fe", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.24.tgz", "fileCount": 53, "integrity": "sha512-4S6ly2297ngNlto7IFZeEicS9u0yRDhocOzndWFovGBb+iUoEPKdZa/rhVk/tcyCADL6S+mMkiGQOlqFDrN3JQ==", "signatures": [{"sig": "MEUCIG73qSLgsShTnc3UMkTDt3OXWEJUKeZzJJ6S2oXZ+KWsAiEA8aQvBxIBw8OkqlV5G6YqQ3JDcIYsb6vd+iIw+mmYDtc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 169001504, "size": 55953336}, "main": "sdk.mjs", "type": "module", "types": "sdk.d.ts", "engines": {"node": ">=18.0.0"}, "gitHead": "bf14938f433b6a2918973e69c5f57e792e9cf763", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-x64": "^0.33.5", "@img/sharp-linux-arm64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_1.0.24_1749862660727_0.9770856769188656", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-06-14T00:57:41.575Z", "publish_time": 1749862661575, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.25": {"name": "@anthropic-ai/claude-code", "version": "1.0.25", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@1.0.25", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "9cf7124a32ff9ff1a9ed1b9a80123c25493285a8", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.25.tgz", "fileCount": 53, "integrity": "sha512-5p4FLlFO4TuRf0zV0axiOxiAkUC8eer0lqJi/A/pA46LESv31Alw6xaNYgwQVkP6oSbP5PydK36u7YrB9QSaXQ==", "signatures": [{"sig": "MEUCIQCnSFSzmsiPxU5/1L2OKq1j+Kgg0bJmRQ/htc8BYc7cPAIgafuzgGYg66Jz/1g6Yz6Yy4JrrdvYmcUo94OhWShFL1g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 169012235, "size": 55955820}, "main": "sdk.mjs", "type": "module", "types": "sdk.d.ts", "engines": {"node": ">=18.0.0"}, "gitHead": "a56e0fb59a69b8f118f65a06d686bcb4eb757ef8", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-x64": "^0.33.5", "@img/sharp-linux-arm64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_1.0.25_1750111869022_0.4180195229009691", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-06-16T22:11:09.938Z", "publish_time": 1750111869938, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.26": {"name": "@anthropic-ai/claude-code", "version": "1.0.26", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@1.0.26", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "f4183b0a0e79c2f36d10118fb525d5b292bce939", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.26.tgz", "fileCount": 53, "integrity": "sha512-0xjPyZkUCttpi/ehKRKowPFdbMSj3LWK3x+8gHNLRRXHMwxUvz7qCWSmjpGs3jJ9o5G0weKgF1TI7SPCcOy7Dg==", "signatures": [{"sig": "MEQCIDYCE0qd6oBchDkbmXazzcIrSCEeydODN1iNAhdRNrzHAiAqJJgB3O38UBvtyN9I0qk7jVRwZw0nBtJNaTJgX9/WPg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 169016120, "size": 55957770}, "main": "sdk.mjs", "type": "module", "types": "sdk.d.ts", "engines": {"node": ">=18.0.0"}, "gitHead": "840ccd03b7ec717b1a32b2be13d6b10bbbbb2118", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "actor": {"name": "boris-anthropic", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-x64": "^0.33.5", "@img/sharp-linux-arm64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_1.0.26_1750180110124_0.5597709929100343", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-06-17T17:08:31.002Z", "publish_time": 1750180111002, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.27": {"name": "@anthropic-ai/claude-code", "version": "1.0.27", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "_id": "@anthropic-ai/claude-code@1.0.27", "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "bin": {"claude": "cli.js"}, "dist": {"shasum": "55082e45c99a46554615bd361d8f83f606f4a4b1", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.27.tgz", "fileCount": 53, "integrity": "sha512-iRKi8Ll/y/GxFqM6UZUMPTyJVirgeR/dH6WY8XnKGNL5ye4DDHRAdE4Ux4N8zssq7rriNVRPWoQGKfqmw/0foQ==", "signatures": [{"sig": "MEYCIQC2Fb6+t4DWg6zWyCcp2MExJpXU7+81BSTncQr7hh/PsgIhALNLwF34UIT95LIHFy+Ka90UTyFCvWK89Ve/jPRvhAgO", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 169016721, "size": 55957819}, "main": "sdk.mjs", "type": "module", "types": "sdk.d.ts", "engines": {"node": ">=18.0.0"}, "gitHead": "52e2e352f0e8a6f32cd708c879713e4548290419", "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "_npmUser": {"name": "boris-anthropic", "actor": {"name": "boris-anthropic", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_npmVersion": "10.8.2", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {}, "_hasShrinkwrap": false, "optionalDependencies": {"@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5", "@img/sharp-darwin-x64": "^0.33.5", "@img/sharp-linux-arm64": "^0.33.5", "@img/sharp-darwin-arm64": "^0.33.5"}, "_npmOperationalInternal": {"tmp": "tmp/claude-code_1.0.27_1750182766951_0.8326840172496786", "host": "s3://npm-registry-packages-npm-production"}, "_cnpmcore_publish_time": "2025-06-17T17:52:47.797Z", "publish_time": 1750182767797, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.28": {"name": "@anthropic-ai/claude-code", "version": "1.0.28", "main": "sdk.mjs", "types": "sdk.d.ts", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-darwin-x64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-arm64": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@1.0.28", "readmeFilename": "README.md", "gitHead": "dce5e75b0c8364a476e0c1b07243a4606774cfe3", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-pid61uQWuMFp3UVPeTUC1oPGFYB/yxmGPtE3vvttdQ3DjMlcGZkcWGbvs2AwXqupbNb6YcSI20j6HbEtat4Miw==", "shasum": "f7a92af49b6965a7203b884a23bb7c66d35054f4", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.28.tgz", "fileCount": 53, "unpackedSize": 169022399, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIHXhmEOyZwpLxf1gonBxSEw+u98phTHz1+wucPzBEBXYAiEA0lhYsHGuZqdrZfycfVAdqJdYOir7yDlkleCY0p3WLVs="}], "size": 55958813}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>", "actor": {"name": "boris-anthropic", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_1.0.28_1750268252821_0.5106006834084975"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-18T17:37:33.722Z", "publish_time": 1750268253722, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.29": {"name": "@anthropic-ai/claude-code", "version": "1.0.29", "main": "sdk.mjs", "types": "sdk.d.ts", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-darwin-x64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-arm64": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@1.0.29", "readmeFilename": "README.md", "gitHead": "c6d4f62e2859d60d411c0f8918b0929f969ec63b", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-ZHw84ZJTxtG/jTLioTkF2ck+whQEKQvCrYvjP+nZnVFirOPV0QwpJyhKsdHFUufnbO4lqACs4dNNnhy0IlKTzQ==", "shasum": "7b5a21f72fa70f0ad67ef1a38705c25887607abb", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.29.tgz", "fileCount": 53, "unpackedSize": 169025923, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCICjyskpv/cyl+IhVei1tfookYb3WyrHyGkvxJQ1pRNjWAiEA48LiiAyTS9R8u0VDB3sWHZelWmfIk3HpgyT9CIvBVfg="}], "size": 55959581}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>", "actor": {"name": "boris-anthropic", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_1.0.29_1750283379172_0.9052478642135948"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-18T21:49:40.075Z", "publish_time": 1750283380075, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.30": {"name": "@anthropic-ai/claude-code", "version": "1.0.30", "main": "sdk.mjs", "types": "sdk.d.ts", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-darwin-x64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-arm64": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@1.0.30", "readmeFilename": "README.md", "gitHead": "a2991a105449a702e021f9ec16f92a3adb078005", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-qIs92Cq3hFwn9/lZBta+wWJfGoQsrbFuiVm0bkurwGKxaJV69Ibr6hYfSU/lIKLcbvSygkZ/tWRxFQt44gnFhQ==", "shasum": "5bfe20896c6875161443b5b15f7e88c939a8a855", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.30.tgz", "fileCount": 53, "unpackedSize": 169024121, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQC9lYCj2XzdMuLRpEgr7eyI7h8sRVN4XBUslH3A/K1f5AIgVSzvMKRIJmXGNqdrp49iBW2SjKYI7j1m0wCvhT/xcnE="}], "size": 55958500}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>", "actor": {"name": "boris-anthropic", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_1.0.30_1750303965019_0.5920190728836845"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-19T03:32:45.914Z", "publish_time": 1750303965914, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.31": {"name": "@anthropic-ai/claude-code", "version": "1.0.31", "main": "sdk.mjs", "types": "sdk.d.ts", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-darwin-x64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-arm64": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@1.0.31", "readmeFilename": "README.md", "gitHead": "c68c1cb6b179ed4ee2fc92bf8921d9271aa128c9", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-prn3DEIBm5ALgCjp0sCcXwNbfBR5w98bEOXQbWViow/3BwkTgW784V8i0S/kfIWDVorz0o4cqR5D0fB4hbjNIg==", "shasum": "9af7bac5af979c990d855409e12ddb172e8a0549", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.31.tgz", "fileCount": 53, "unpackedSize": 169036494, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQD8OGWtbohj+poXbEMk1W1Fokq/FibWGw9pg/oBpM2fgQIhAMrP+iog3LmyHzBkYcobHRx2FeJpShlEG3CF/9vaL7Q0"}], "size": 55960642}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>", "actor": {"name": "boris-anthropic", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_1.0.31_1750445720523_0.7172036468746095"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-20T18:55:21.438Z", "publish_time": 1750445721438, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.32": {"name": "@anthropic-ai/claude-code", "version": "1.0.32", "main": "sdk.mjs", "types": "sdk.d.ts", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-darwin-x64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-arm64": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@1.0.32", "readmeFilename": "README.md", "gitHead": "4eff9c0ca0925c029d4059bc3bf8b68243a4c740", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-1MfCozlLmXQltOcK198i6EYNYL/jwY1YHQZiJWEru3UenSaJUXyyw39m8SKYmZUxOt3oP7z75mZqVDkl9Gdfdg==", "shasum": "505aaeb553d0bb5a3a63f01343c650e4f8a90a8f", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.32.tgz", "fileCount": 53, "unpackedSize": 169019389, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC9cQ6iIBMxEjf/HnJVX/89kSVHMqt+EDib75X377lbNwIhAK1DeZGi70stZG5san9aisxxSuKSCloszNpkZn4rLvsu"}], "size": 55968771}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>", "actor": {"name": "boris-anthropic", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_1.0.32_1750699847860_0.5203329387981046"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-23T17:30:48.758Z", "publish_time": 1750699848758, "_source_registry_name": "default", "hasInstallScript": true}, "1.0.33": {"name": "@anthropic-ai/claude-code", "version": "1.0.33", "main": "sdk.mjs", "types": "sdk.d.ts", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN README.md", "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "scripts": {"prepare": "node -e \"if (!process.env.AUTHORIZED) { console.error('ERROR: Direct publishing is not allowed.\\nPlease use the publish-external.sh script to publish this package.'); process.exit(1); }\"", "preinstall": "node scripts/preinstall.js"}, "dependencies": {}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-darwin-x64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-arm64": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}, "_id": "@anthropic-ai/claude-code@1.0.33", "readmeFilename": "README.md", "gitHead": "0f7152a5494fd6f54f4ca4693a702aaf9d1e0502", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-rKQ1C0+iSV/bS4LVfyCt2FIkIc8MnFi5EbmRAXEunNkXLCQLHfXjsqx7cLOy7c11vZwGkyf/wEp5LwaDQHdjCQ==", "shasum": "3250f167f1c815cf398e0e180328513dbaec3d55", "tarball": "https://registry.npmmirror.com/@anthropic-ai/claude-code/-/claude-code-1.0.33.tgz", "fileCount": 53, "unpackedSize": 169021269, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQD3utOvfZ1xyNIaFmayT72xTRgbcD3HOglOGXz1SyYEBQIgH441RZUtDEX1bH7FUlJ+8JknZjvu97kCkdWIb4RywM4="}], "size": 55968991}, "_npmUser": {"name": "boris-anthropic", "email": "<EMAIL>", "actor": {"name": "boris-anthropic", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/claude-code_1.0.33_1750710664363_0.23219500567894946"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2025-06-23T20:31:06.405Z", "publish_time": 1750710666405, "_source_registry_name": "default", "hasInstallScript": true}}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/anthropics/claude-code/issues"}, "description": "Use <PERSON>, <PERSON><PERSON><PERSON>'s AI assistant, right from your terminal. <PERSON> can understand your codebase, edit files, run terminal commands, and handle entire workflows for you.", "homepage": "https://github.com/anthropics/claude-code", "license": "SEE LICENSE IN README.md", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nikhil-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "jv-anthropic", "email": "<EMAIL>"}, {"name": "zak-anthropic", "email": "<EMAIL>"}, {"name": "sbidasaria", "email": "<EMAIL>"}, {"name": "wolffiex", "email": "<EMAIL>"}, {"name": "boris-anthropic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# Claude Code\n\n![](https://img.shields.io/badge/Node.js-18%2B-brightgreen?style=flat-square) [![npm]](https://www.npmjs.com/package/@anthropic-ai/claude-code)\n\n[npm]: https://img.shields.io/npm/v/@anthropic-ai/claude-code.svg?style=flat-square\n\nClaude Code is an agentic coding tool that lives in your terminal, understands your codebase, and helps you code faster by executing routine tasks, explaining complex code, and handling git workflows -- all through natural language commands. Use it in your terminal, IDE, or tag @claude on Github.\n\n**Learn more in the [official documentation](https://docs.anthropic.com/en/docs/claude-code/overview)**.\n\n<img src=\"https://github.com/anthropics/claude-code/blob/main/demo.gif?raw=1\" />\n\n## Get started\n\n1. Install Claude Code:\n\n```sh\nnpm install -g @anthropic-ai/claude-code\n```\n\n2. Navigate to your project directory and run `claude`.\n\n## Reporting Bugs\n\nWe welcome feedback during this beta period. Use the `/bug` command to report issues directly within <PERSON> Code, or file a [GitHub issue](https://github.com/anthropics/claude-code/issues).\n\n## Data collection, usage, and retention\n\nWhen you use Claude Code, we collect feedback, which includes usage data (such as code acceptance or rejections), associated conversation data, and user feedback submitted via the `/bug` command.\n\n### How we use your data\n\nWe may use feedback to improve our products and services, but we will not train generative models using your feedback from <PERSON> Code. Given their potentially sensitive nature, we store user feedback transcripts for only 30 days.\n\nIf you choose to send us feedback about Claude Code, such as transcripts of your usage, Anthropic may use that feedback to debug related issues and improve Claude Code's functionality (e.g., to reduce the risk of similar bugs occurring in the future).\n\n### Privacy safeguards\n\nWe have implemented several safeguards to protect your data, including limited retention periods for sensitive information, restricted access to user session data, and clear policies against using feedback for model training.\n\nFor full details, please review our [Commercial Terms of Service](https://www.anthropic.com/legal/commercial-terms) and [Privacy Policy](https://www.anthropic.com/legal/privacy).\n", "repository": {"type": "git", "url": "git+https://github.com/anthropics/claude-cli-internal.git"}, "_source_registry_name": "default", "readmeFilename": "README.md"}