package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasCreditLevel;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasCreditLevelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 【请填写功能名称】Controller
 *
 * <AUTHOR>
 * @date 2023-11-14
 */
@RestController
@RequestMapping("/system/creditlevel")
public class BasCreditLevelController extends BaseController {
    @Autowired
    private BasCreditLevelService basCreditLevelService;

    /**
     * 查询【请填写功能名称】列表
     */
//    @PreAuthorize("@ss.hasPermi('system:creditlevel:list')")
    @GetMapping("/list")
    public AjaxResult list(BasCreditLevel basCreditLevel) {
        return AjaxResult.success(basCreditLevelService.selectBasCreditLevelList(basCreditLevel));
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:creditlevel:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasCreditLevel basCreditLevel) {
        List<BasCreditLevel> list = basCreditLevelService.selectBasCreditLevelList(basCreditLevel);
        ExcelUtil<BasCreditLevel> util = new ExcelUtil<BasCreditLevel>(BasCreditLevel.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:creditlevel:query')")
    @GetMapping(value = "/{creditLevel}")
    public AjaxResult getInfo(@PathVariable("creditLevel") String creditLevel) {
        return AjaxResult.success(basCreditLevelService.selectBasCreditLevelByCreditLevel(creditLevel));
    }

    /**
     * 新增【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:creditlevel:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasCreditLevel basCreditLevel) {
        return toAjax(basCreditLevelService.insertBasCreditLevel(basCreditLevel));
    }

    /**
     * 修改【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:creditlevel:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasCreditLevel basCreditLevel) {
        return toAjax(basCreditLevelService.updateBasCreditLevel(basCreditLevel));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:creditlevel:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasCreditLevel basCreditLevel) {
        return toAjax(basCreditLevelService.changeStatus(basCreditLevel));
    }

    /**
     * 删除【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:creditlevel:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{creditLevels}")
    public AjaxResult remove(@PathVariable String[] creditLevels) {
        return toAjax(basCreditLevelService.deleteBasCreditLevelByCreditLevels(creditLevels));
    }
}
