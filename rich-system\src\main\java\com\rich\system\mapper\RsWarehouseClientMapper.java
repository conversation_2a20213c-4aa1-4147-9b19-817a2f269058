package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsWarehouseClient;
import org.apache.ibatis.annotations.Mapper;

/**
 * 仓库客户信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-11
 */
@Mapper
public interface RsWarehouseClientMapper {
    /**
     * 查询仓库客户信息
     *
     * @param warehouseClientId 仓库客户信息主键
     * @return 仓库客户信息
     */
    RsWarehouseClient selectRsWarehouseClientByWarehouseClientId(Long warehouseClientId);

    /**
     * 查询仓库客户信息列表
     *
     * @param rsWarehouseClient 仓库客户信息
     * @return 仓库客户信息集合
     */
    List<RsWarehouseClient> selectRsWarehouseClientList(RsWarehouseClient rsWarehouseClient);

    /**
     * 新增仓库客户信息
     *
     * @param rsWarehouseClient 仓库客户信息
     * @return 结果
     */
    int insertRsWarehouseClient(RsWarehouseClient rsWarehouseClient);

    /**
     * 修改仓库客户信息
     *
     * @param rsWarehouseClient 仓库客户信息
     * @return 结果
     */
    int updateRsWarehouseClient(RsWarehouseClient rsWarehouseClient);

    /**
     * 删除仓库客户信息
     *
     * @param warehouseClientId 仓库客户信息主键
     * @return 结果
     */
    int deleteRsWarehouseClientByWarehouseClientId(Long warehouseClientId);

    /**
     * 批量删除仓库客户信息
     *
     * @param warehouseClientIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsWarehouseClientByWarehouseClientIds(Long[] warehouseClientIds);

    int checkClientCode(String clientCode);

    RsWarehouseClient selectRsWarehouseClientByClientCode(String clientCode);
}
