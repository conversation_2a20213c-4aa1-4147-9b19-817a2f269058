package com.rich.system.service.impl;

import com.rich.common.constant.UserConstants;
import com.rich.common.core.domain.entity.BasPosition;
import com.rich.common.core.domain.entity.SysRole;
import com.rich.common.exception.ServiceException;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.StringUtils;
import com.rich.system.mapper.BasPositionMapper;
import com.rich.system.mapper.SysRoleMapper;
import com.rich.system.service.BasPositionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 岗位信息 服务层处理
 *
 * <AUTHOR>
 */
@Service

public class BasPositionServiceImpl implements BasPositionService {

    @Autowired
    private BasPositionMapper postMapper;

    @Autowired
    private SysRoleMapper roleMapper;

    /**
     * 查询岗位信息集合
     *
     * @param post 岗位信息
     * @return 岗位信息集合
     */
    @Override
    public List<BasPosition> selectPostList(BasPosition post) {
        return postMapper.selectPostList(post);
    }

    /**
     * 查询所有岗位
     *
     * @return 岗位列表
     */
    @Override
    public List<BasPosition> selectPostAll() {
        return postMapper.selectPostAll();
    }

    /**
     * 通过岗位ID查询岗位信息
     *
     * @param postId 岗位ID
     * @return 角色对象信息
     */
    @Override
    public BasPosition selectPostById(Long postId) {
        return postMapper.selectPostById(postId);
    }

    /**
     * 根据用户ID获取岗位选择框列表
     *
     * @param userId 用户ID
     * @return 选中岗位ID列表
     */
    @Override
    public List<Long> selectPostListByUserId(Long userId) {
        return postMapper.selectPostListByUserId(userId);
    }

    /**
     * 校验岗位名称是否唯一
     *
     * @param post 岗位信息
     * @return 结果
     */
    @Override
    public boolean checkPostNameUnique(BasPosition post) {
        Long postId = StringUtils.isNull(post.getPositionId()) ? -1L : post.getPositionId();
        BasPosition info = postMapper.checkPostNameUnique(post.getPositionLocalName());
        if (StringUtils.isNotNull(info) && info.getPositionId().longValue() != postId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验岗位编码是否唯一
     *
     * @param post 岗位信息
     * @return 结果
     */
    @Override
    public boolean checkPostCodeUnique(BasPosition post) {
        Long postId = StringUtils.isNull(post.getPositionId()) ? -1L : post.getPositionId();
        BasPosition info = postMapper.checkPostCodeUnique(post.getPositionEnName());
        if (StringUtils.isNotNull(info) && info.getPositionId().longValue() != postId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 通过岗位ID查询岗位使用数量
     *
     * @param postId 岗位ID
     * @return 结果
     */
    @Override
    public int countPositionById(Long postId) {
        return roleMapper.countPositionById(postId);
    }

    /**
     * 删除岗位信息
     *
     * @param postId 岗位ID
     * @return 结果
     */
    @Override
    public int deletePostById(Long postId) {
        return postMapper.deletePostById(postId);
    }

    /**
     * 批量删除岗位信息
     *
     * @param postIds 需要删除的岗位ID
     * @return 结果
     */
    @Override
    public int deletePostByIds(Long[] postIds) {
        for (Long postId : postIds) {
            BasPosition post = selectPostById(postId);
            if (countPositionById(postId) > 0) {
                throw new ServiceException(String.format("%1$s已分配,不能删除", post.getPositionLocalName()));
            }
        }
        return postMapper.deletePostByIds(postIds);
    }

    /**
     * 新增保存岗位信息
     *
     * @param post 岗位信息
     * @return 结果
     */
    @Override
    public int insertPost(BasPosition post) {
        return postMapper.insertPost(post);
    }

    /**
     * 修改保存岗位信息
     *
     * @param post 岗位信息
     * @return 结果
     */
    @Override
    public int updatePost(BasPosition post) {
        return postMapper.updatePost(post);
    }

    @Override
    public List<BasPosition> selectPostListUnderUser(Long userId) {

        List<BasPosition> basPositions = Collections.emptyList();
        if (SecurityUtils.getDeptId().equals(102L) || SecurityUtils.getUserId().equals(1L)) {
            basPositions = postMapper.selectPostUnderUser(null);
        } else {
            basPositions = postMapper.selectPostUnderUser(userId);
        }
        return basPositions;
    }

    @Override
    public Long selectPostByUserId(Long userId) {
        List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
        Long max = 0L;
        for (SysRole role : roles) {
            if (role.getPositionId() != null && role.getPositionId() > max) {
                max = role.getPositionId();
            }
        }

        return max;
    }
}
