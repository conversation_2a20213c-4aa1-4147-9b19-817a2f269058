package com.rich.common.core.domain.entity;

import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 【请填写功能名称】对象 bas_charge_unit
 *
 * <AUTHOR>
 * @date 2022-08-30
 */
public class BasDistUnit extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 计费单位编码
     */
    private Long unitId;

    private Long parentId;

    private Integer main;

    private String unitShortName;


    private String unitEnName;


    private String unitLocalName;


    private Integer orderNum;

    private String status;

    private String unitQuery;

    private String unitCode;

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public Integer getMain() {
        return main;
    }

    public void setMain(Integer main) {
        this.main = main;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getUnitQuery() {
        return unitQuery;
    }

    public void setUnitQuery(String unitQuery) {
        this.unitQuery = unitQuery;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitShortName(String unitShortName) {
        this.unitShortName = unitShortName;
    }

    public String getUnitShortName() {
        return unitShortName;
    }

    public void setUnitEnName(String unitEnName) {
        this.unitEnName = unitEnName;
    }

    public String getUnitEnName() {
        return unitEnName;
    }

    public void setUnitLocalName(String unitLocalName) {
        this.unitLocalName = unitLocalName;
    }

    public String getUnitLocalName() {
        return unitLocalName;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("unitId", getUnitId()).append("unitShortName", getUnitShortName()).append("unitEnName", getUnitEnName()).append("unitLocalName", getUnitLocalName()).append("orderNum", getOrderNum()).append("remark", getRemark()).toString();
    }
}
