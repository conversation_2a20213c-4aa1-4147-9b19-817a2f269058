package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasIssue;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 问题Mapper接口
 *
 * <AUTHOR>
 * @date 2022-10-18
 */
@Mapper
public interface BasIssueMapper {
    /**
     * 查询问题
     *
     * @param issueId 问题主键
     * @return 问题
     */
    BasIssue selectBasIssueByIssueId(Long issueId);

    /**
     * 查询问题列表
     *
     * @param basIssue 问题
     * @return 问题集合
     */
    List<BasIssue> selectBasIssueList(BasIssue basIssue);

    /**
     * 新增问题
     *
     * @param basIssue 问题
     * @return 结果
     */
    int insertBasIssue(BasIssue basIssue);

    /**
     * 修改问题
     *
     * @param basIssue 问题
     * @return 结果
     */
    int updateBasIssue(BasIssue basIssue);

    /**
     * 删除问题
     *
     * @param issueId 问题主键
     * @return 结果
     */
    int deleteBasIssueByIssueId(Long issueId);

    /**
     * 批量删除问题
     *
     * @param issueIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasIssueByIssueIds(Long[] issueIds);
}
