---
description: 
globs: *.java,*.xml
alwaysApply: false
---
# API接口与数据模型规范

本文档提供瑞旗管理系统中API接口与数据模型的详细规范和示例，帮助开发人员理解系统中的数据流转和接口调用方式。

## 统一响应格式

所有API接口都使用统一的响应格式 `R<T>` 进行封装：

```java
public class R<T> {
    /** 状态码 */
    private Integer code;
    
    /** 返回信息 */
    private String msg;
    
    /** 数据对象 */
    private T data;
    
    // 静态方法
    public static <T> R<T> success() {...}
    public static <T> R<T> success(T data) {...}
    public static <T> R<T> error(String msg) {...}
    public static <T> R<T> error(Integer code, String msg) {...}
}
```

## 常用状态码

| 状态码 | 说明 |
| ------ | ---- |
| 200    | 成功 |
| 401    | 未授权 |
| 403    | 拒绝访问 |
| 404    | 资源不存在 |
| 500    | 服务器内部错误 |

## 分页请求与响应

### 分页请求参数

```java
public class PageQuery {
    /** 页码 */
    private Integer pageNum = 1;
    
    /** 每页记录数 */
    private Integer pageSize = 10;
    
    /** 排序字段 */
    private String orderByColumn;
    
    /** 排序方向 */
    private String isAsc;
}
```

### 分页响应数据

```java
public class PageInfo<T> {
    /** 总记录数 */
    private long total;
    
    /** 列表数据 */
    private List<T> list;
    
    /** 页码 */
    private int pageNum;
    
    /** 每页记录数 */
    private int pageSize;
}
```

## 接口认证与鉴权

### Token认证

所有需要认证的接口都需要在请求头中携带Token：

```
Authorization: Bearer {token}
```

### 权限控制

使用注解方式进行权限控制：

```java
// 需要登录才能访问
@RequiresLogin

// 需要指定权限才能访问
@RequiresPermissions("system:user:list")

// 需要指定角色才能访问
@RequiresRoles("admin")
```

## 接口命名规范

### RESTful风格接口

| 请求方式 | URL模式 | 说明 |
| ------- | ------- | ---- |
| GET     | /api/{资源名称} | 获取资源列表 |
| GET     | /api/{资源名称}/{id} | 获取单个资源 |
| POST    | /api/{资源名称} | 创建资源 |
| PUT     | /api/{资源名称}/{id} | 更新资源 |
| DELETE  | /api/{资源名称}/{id} | 删除资源 |

### 自定义操作接口

对于不符合RESTful风格的操作，使用以下命名规范：

| 请求方式 | URL模式 | 说明 |
| ------- | ------- | ---- |
| POST    | /api/{资源名称}/export | 导出资源 |
| POST    | /api/{资源名称}/import | 导入资源 |
| PUT     | /api/{资源名称}/{id}/status | 修改资源状态 |

## 数据验证规范

使用JSR-303注解进行数据验证：

```java
public class UserDTO {
    @NotBlank(message = "用户名不能为空")
    private String userName;
    
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    private String password;
    
    @Email(message = "邮箱格式不正确")
    private String email;
    
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号码格式不正确")
    private String mobile;
}
```

## 前端API调用示例

### 基于Axios的请求封装

```javascript
// 请求拦截器
request.interceptors.request.use(
  config => {
    // 添加Token
    const token = getToken();
    if (token) {
      config.headers['Authorization'] = 'Bearer ' + token;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  response => {
    const res = response.data;
    if (res.code !== 200) {
      // 处理错误
      Message.error(res.msg || '系统错误');
      
      // 处理特定错误码
      if (res.code === 401) {
        // 未授权，跳转到登录页
      }
      
      return Promise.reject(new Error(res.msg || '系统错误'));
    } else {
      return res;
    }
  },
  error => {
    Message.error(error.message || '请求失败');
    return Promise.reject(error);
  }
);
```

### API方法定义

```javascript
// 用户管理API
export function listUsers(query) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: query
  });
}

export function getUser(userId) {
  return request({
    url: `/system/user/${userId}`,
    method: 'get'
  });
}

export function addUser(data) {
  return request({
    url: '/system/user',
    method: 'post',
    data: data
  });
}

export function updateUser(data) {
  return request({
    url: '/system/user',
    method: 'put',
    data: data
  });
}

export function deleteUser(userId) {
  return request({
    url: `/system/user/${userId}`,
    method: 'delete'
  });
}
```

## 移动端API调用示例

### uni-app请求封装

```javascript
// 请求拦截器
const httpInterceptor = {
  invoke(options) {
    // 添加Token
    const token = uni.getStorageSync('token');
    if (token) {
      options.header = {
        ...options.header,
        'Authorization': `Bearer ${token}`
      };
    }
    
    // 添加基础URL
    options.url = baseUrl + options.url;
    
    // 添加请求超时
    options.timeout = 10000;
  }
};

// 注册拦截器
uni.addInterceptor('request', httpInterceptor);
uni.addInterceptor('uploadFile', httpInterceptor);

// 请求方法封装
export const http = {
  request(options) {
    return new Promise((resolve, reject) => {
      uni.request({
        ...options,
        success: (res) => {
          // 处理响应
          if (res.statusCode === 200) {
            const data = res.data;
            if (data.code === 200) {
              resolve(data);
            } else {
              uni.showToast({
                title: data.msg || '请求失败',
                icon: 'none'
              });
              
              // 处理特定错误码
              if (data.code === 401) {
                // 未授权，跳转到登录页
              }
              
              reject(data);
            }
          } else {
            uni.showToast({
              title: '网络错误',
              icon: 'none'
            });
            reject(res);
          }
        },
        fail: (err) => {
          uni.showToast({
            title: '请求失败',
            icon: 'none'
          });
          reject(err);
        }
      });
    });
  },
  
  get(url, params = {}) {
    return this.request({
      url,
      method: 'GET',
      data: params
    });
  },
  
  post(url, data = {}) {
    return this.request({
      url,
      method: 'POST',
      data
    });
  }
};
```

