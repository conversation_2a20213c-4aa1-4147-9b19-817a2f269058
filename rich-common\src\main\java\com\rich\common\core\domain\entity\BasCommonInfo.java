package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

/**
 * 通用信息对象 bas_common_info
 *
 * <AUTHOR>
 * @date 2022-12-07
 */
public class BasCommonInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 通用信息
     */
    private Long infoId;

    /**
     * 简称
     */
    @Excel(name = "简称")
    private String infoShortName;

    /**
     * 中文名
     */
    @Excel(name = "中文名")
    private String infoLocalName;

    /**
     * 英文名
     */
    @Excel(name = "英文名")
    private String infoEnName;

    /**
     * 默认信息
     */
    @Excel(name = "默认字段")
    private String infoDefault;

    /**
     * 通用信息类型ID
     */
    @Excel(name = "通用信息类型ID")
    private Long infoTypeId;

    /**
     * 信息来源
     */
    @Excel(name = "信息来源")
    private Long roleTypeId;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Integer orderNum;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;

    private BasCommonInfoType infoType;

    private BasCompanyRoleType roleType;

    private Long[] serviceTypeIds;

    private String serviceType;

    private String infoQuery;

    public Long[] getServiceTypeIds() {
        return serviceTypeIds;
    }

    public void setServiceTypeIds(Long[] serviceTypeIds) {
        this.serviceTypeIds = serviceTypeIds;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public String getInfoQuery() {
        return infoQuery;
    }

    public void setInfoQuery(String infoQuery) {
        this.infoQuery = infoQuery;
    }

    public BasCommonInfoType getInfoType() {
        return infoType;
    }

    public void setInfoType(BasCommonInfoType infoType) {
        this.infoType = infoType;
    }

    public BasCompanyRoleType getRoleType() {
        return roleType;
    }

    public void setRoleType(BasCompanyRoleType roleType) {
        this.roleType = roleType;
    }

    public void setInfoId(Long infoId) {
        this.infoId = infoId;
    }

    public Long getInfoId() {
        return infoId;
    }

    public void setInfoShortName(String infoShortName) {
        this.infoShortName = infoShortName;
    }

    public String getInfoShortName() {
        return infoShortName;
    }

    public void setInfoLocalName(String infoLocalName) {
        this.infoLocalName = infoLocalName;
    }

    public String getInfoLocalName() {
        return infoLocalName;
    }

    public void setInfoEnName(String infoEnName) {
        this.infoEnName = infoEnName;
    }

    public String getInfoEnName() {
        return infoEnName;
    }

    public void setInfoDefault(String infoDefault) {
        this.infoDefault = infoDefault;
    }

    public String getInfoDefault() {
        return infoDefault;
    }

    public void setInfoTypeId(Long infoTypeId) {
        this.infoTypeId = infoTypeId;
    }

    public Long getInfoTypeId() {
        return infoTypeId;
    }

    public void setRoleTypeId(Long roleTypeId) {
        this.roleTypeId = roleTypeId;
    }

    public Long getRoleTypeId() {
        return roleTypeId;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

}
