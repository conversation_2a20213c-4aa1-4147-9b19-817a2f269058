package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsWarehouse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsWarehouseMapper;
import com.rich.system.service.RsWarehouseService;

/**
 * 仓储服务Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@Service
public class RsWarehouseServiceImpl implements RsWarehouseService {
    @Autowired
    private RsWarehouseMapper rsWarehouseMapper;

    /**
     * 查询仓储服务
     *
     * @param warehouseId 仓储服务主键
     * @return 仓储服务
     */
    @Override
    public RsWarehouse selectRsWarehouseByWarehouseId(Long warehouseId) {
        return rsWarehouseMapper.selectRsWarehouseByWarehouseId(warehouseId);
    }

    /**
     * 查询仓储服务列表
     *
     * @param rsWarehouse 仓储服务
     * @return 仓储服务
     */
    @Override
    public List<RsWarehouse> selectRsWarehouseList(RsWarehouse rsWarehouse) {
        return rsWarehouseMapper.selectRsWarehouseList(rsWarehouse);
    }

    /**
     * 新增仓储服务
     *
     * @param rsWarehouse 仓储服务
     * @return 结果
     */
    @Override
    public int insertRsWarehouse(RsWarehouse rsWarehouse) {
        return rsWarehouseMapper.insertRsWarehouse(rsWarehouse);
    }

    /**
     * 修改仓储服务
     *
     * @param rsWarehouse 仓储服务
     * @return 结果
     */
    @Override
    public int updateRsWarehouse(RsWarehouse rsWarehouse) {
        return rsWarehouseMapper.updateRsWarehouse(rsWarehouse);
    }

    /**
     * 修改仓储服务状态
     *
     * @param rsWarehouse 仓储服务
     * @return 仓储服务
     */
    @Override
    public int changeStatus(RsWarehouse rsWarehouse) {
        return rsWarehouseMapper.updateRsWarehouse(rsWarehouse);
    }

    /**
     * 批量删除仓储服务
     *
     * @param warehouseIds 需要删除的仓储服务主键
     * @return 结果
     */
    @Override
    public int deleteRsWarehouseByWarehouseIds(Long[] warehouseIds) {
        return rsWarehouseMapper.deleteRsWarehouseByWarehouseIds(warehouseIds);
    }

    /**
     * 删除仓储服务信息
     *
     * @param warehouseId 仓储服务主键
     * @return 结果
     */
    @Override
    public int deleteRsWarehouseByWarehouseId(Long warehouseId) {
        return rsWarehouseMapper.deleteRsWarehouseByWarehouseId(warehouseId);
    }
}
