package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsAggregatorConfigs;
import org.apache.ibatis.annotations.Mapper;

/**
 * 数据汇总配置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-18
 */
@Mapper
public interface RsAggregatorConfigsMapper {
    /**
     * 查询数据汇总配置
     *
     * @param id 数据汇总配置主键
     * @return 数据汇总配置
     */
    RsAggregatorConfigs selectRsAggregatorConfigsById(Long id);

    /**
     * 查询数据汇总配置列表
     *
     * @param rsAggregatorConfigs 数据汇总配置
     * @return 数据汇总配置集合
     */
    List<RsAggregatorConfigs> selectRsAggregatorConfigsList(RsAggregatorConfigs rsAggregatorConfigs);

    /**
     * 新增数据汇总配置
     *
     * @param rsAggregatorConfigs 数据汇总配置
     * @return 结果
     */
    int insertRsAggregatorConfigs(RsAggregatorConfigs rsAggregatorConfigs);

    /**
     * 修改数据汇总配置
     *
     * @param rsAggregatorConfigs 数据汇总配置
     * @return 结果
     */
    int updateRsAggregatorConfigs(RsAggregatorConfigs rsAggregatorConfigs);

    /**
     * 删除数据汇总配置
     *
     * @param id 数据汇总配置主键
     * @return 结果
     */
    int deleteRsAggregatorConfigsById(Long id);

    /**
     * 批量删除数据汇总配置
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsAggregatorConfigsByIds(Long[] ids);
}
