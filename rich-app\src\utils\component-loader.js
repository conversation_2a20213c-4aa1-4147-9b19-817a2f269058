/**
 * 组件动态加载工具
 * 用于优化组件加载，减少vendor.js体积
 */

// 高频使用的组件可以全局注册
export const registerCommonComponents = (Vue) => {
    // 表单相关组件（使用频率最高）
    Vue.component("uni-forms", () => import("@/components/uni-forms/uni-forms"));
    Vue.component("uni-forms-item", () =>
        import("@/components/uni-forms-item/uni-forms-item")
    );
    Vue.component("uni-easyinput", () =>
        import("@/components/uni-easyinput/uni-easyinput")
    );

    // 图标组件（使用频率较高）
    Vue.component("uni-icons", () => import("@/components/uni-icons/uni-icons"));
};

// 按需加载组件的创建函数
export const createComponentLoader = (componentName) => {
    return () => {
        // 根据组件名称动态导入
        return import(`@/components/${componentName}/${componentName}.vue`);
    };
};

// 懒加载列表类组件
export const lazyLoadListComponents = (instance) => {
    // 仅当页面需要时才注册这些组件
    instance.$options.components = {
        ...(instance.$options.components || {}),
        "uni-list": () => import("@/components/uni-list/uni-list"),
        "uni-list-item": () => import("@/components/uni-list-item/uni-list-item"),
    };
};

// 懒加载表格类组件
export const lazyLoadTableComponents = (instance) => {
    // 仅当页面需要时才注册这些组件
    instance.$options.components = {
        ...(instance.$options.components || {}),
        "uni-table": () => import("@/components/uni-table/uni-table"),
        "uni-tr": () => import("@/components/uni-tr/uni-tr"),
        "uni-th": () => import("@/components/uni-th/uni-th"),
        "uni-td": () => import("@/components/uni-td/uni-td"),
    };
};

// 懒加载宫格组件
export const lazyLoadGridComponents = (instance) => {
    // 仅当页面需要时才注册这些组件
    instance.$options.components = {
        ...(instance.$options.components || {}),
        "uni-grid": () => import("@/components/uni-grid/uni-grid"),
        "uni-grid-item": () => import("@/components/uni-grid-item/uni-grid-item"),
    };
};
