package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.RsRctOld;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.domain.vo.RcRctStatisticsVO;
import com.rich.system.service.BasDistLocationService;
import com.rich.system.service.ExtCompanyService;
import com.rich.system.service.RsRctOldService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 操作单列表Controller
 *
 * <AUTHOR>
 * @date 2023-08-15
 */
@RestController
@RequestMapping("/system/rctold")
public class RsRctOldController extends BaseController {
    @Autowired
    private RsRctOldService rsRctService;

    @Autowired
    private BasDistLocationService basDistLocationService;

    @Autowired
    private ExtCompanyService extCompanyService;

    /**
     * 查询操作单列表列表
     */
    @PreAuthorize("@ss.hasPermi('system:rct:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsRctOld rsRct) {
        if (SecurityUtils.getDeptId().equals(102L) || SecurityUtils.isAdmin(SecurityUtils.getLoginUser().getUser().getRole())) {
            rsRct.setPermissionLevel(null);
        }
        List<RsRctOld> list = rsRctService.selectRsRctList(rsRct);
        if (list != null && !list.isEmpty()) {
            return getDataTable(list);
        } else {
            return null;
        }
    }

    @GetMapping("/mon")
    public AjaxResult getMon() {
        return AjaxResult.success(rsRctService.getMon());
    }

    /**
     * 导出操作单列表列表
     */
    @PreAuthorize("@ss.hasPermi('system:rct:export')")
    @Log(title = "操作单列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsRctOld rsRct) {
        List<RsRctOld> list = rsRctService.selectRsRctList(rsRct);
        ExcelUtil<RsRctOld> util = new ExcelUtil<RsRctOld>(RsRctOld.class);
        util.exportExcel(response, list, "操作单列表数据");
    }

    /**
     * 获取操作单列表详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:rct:query')")
    @GetMapping(value = "/{rctId}")
    public AjaxResult getInfo(@PathVariable("rctId") Long rctId) {
        RsRctOld rsRct = rsRctService.selectRsRctByRctId(rctId);
        AjaxResult ajaxResult = AjaxResult.success();
        Set<Long> set = new HashSet<>();
        rsRct.setCarrierIds(rsRctService.getCarrierIds(rctId).toArray(new Long[0]));
        rsRct.setCargoTypeIds(rsRctService.getCargoTypeIds(rctId).toArray(new Long[0]));
        rsRct.setServiceTypeIds(rsRctService.getServiceTypeIds(rctId).toArray(new Long[0]));
        if (rsRct.getPolId() != null) {
            set.add(rsRct.getPolId());
        }
        if (rsRct.getDestinationPortId() != null) {
            set.add(rsRct.getDestinationPortId());
        }
        if (rsRct.getRsRctImportClearanceBasicInfo() != null && rsRct.getRsRctLogisticsTypeBasicInfo().getPolId() != null) {
            set.add(rsRct.getRsRctLogisticsTypeBasicInfo().getPolId());
        }
        if (rsRct.getRsRctImportClearanceBasicInfo() != null && rsRct.getRsRctLogisticsTypeBasicInfo().getLocalBasicPortId() != null) {
            set.add(rsRct.getRsRctLogisticsTypeBasicInfo().getLocalBasicPortId());
        }
        if (rsRct.getRsRctLogisticsTypeBasicInfo() != null && rsRct.getRsRctLogisticsTypeBasicInfo().getTransitPortId() != null) {
            set.add(rsRct.getRsRctLogisticsTypeBasicInfo().getTransitPortId());
        }
        if (rsRct.getRsRctLogisticsTypeBasicInfo() != null && rsRct.getRsRctLogisticsTypeBasicInfo().getPodId() != null) {
            set.add(rsRct.getRsRctLogisticsTypeBasicInfo().getPodId());
        }
        if (rsRct.getRsRctLogisticsTypeBasicInfo() != null && rsRct.getRsRctLogisticsTypeBasicInfo().getDestinationPortId() != null) {
            set.add(rsRct.getRsRctLogisticsTypeBasicInfo().getDestinationPortId());
        }
        if (rsRct.getRsRctPreCarriageBasicInfo() != null && rsRct.getRsRctPreCarriageBasicInfo().getPreCarriageRegionId() != null) {
            set.add(rsRct.getRsRctPreCarriageBasicInfo().getPreCarriageRegionId());
        }
        if (rsRct.getRsRctExportDeclarationBasicInfo() != null && rsRct.getRsRctExportDeclarationBasicInfo().getDispatchRegionId() != null) {
            set.add(rsRct.getRsRctExportDeclarationBasicInfo().getDispatchRegionId());
        }
        ajaxResult.put(AjaxResult.DATA_TAG, rsRct);
        ajaxResult.put("locationOptions", !set.isEmpty() ? basDistLocationService.selectBasDistLocationByIds(set) : null);
        Set<Long> companySet = new HashSet<>();
        if (rsRct.getRelationClientIds() != null && !rsRct.getRelationClientIds().isEmpty()) {
            String[] relationClientIds = rsRct.getRelationClientIds().split(",");
            for (String relationClientId : relationClientIds) {
                companySet.add(Long.parseLong(relationClientId));
            }
        }
        if (rsRct.getClientId() != null) {
            companySet.add(rsRct.getClientId());
        }
        if (rsRct.getBookingAgent() != null) {
            companySet.add(rsRct.getBookingAgent());
        }
        ajaxResult.put("companyList", set.isEmpty() ? null : extCompanyService.selectExtCompanyByCompanyIds(companySet));
        return ajaxResult;
    }

    /**
     * 新增操作单列表
     */
    @PreAuthorize("@ss.hasPermi('system:rct:add')")
    @Log(title = "操作单列表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsRctOld rsRct) {
        return AjaxResult.success(rsRctService.insertRsRct(rsRct));
    }

    /**
     * 修改操作单列表
     */
    @PreAuthorize("@ss.hasPermi('system:rct:edit')")
    @Log(title = "操作单列表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsRctOld rsRct) {
        return toAjax(rsRctService.updateRsRct(rsRct));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:rct:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsRctOld rsRct) {
        rsRct.setUpdateBy(getUserId());
        return toAjax(rsRctService.changeStatus(rsRct));
    }

    /**
     * 删除操作单列表
     */
    @PreAuthorize("@ss.hasPermi('system:rct:remove')")
    @Log(title = "操作单列表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{rctIds}")
    public AjaxResult remove(@PathVariable Long[] rctIds) {
        return toAjax(rsRctService.deleteRsRctByRctIds(rctIds));
    }

    @PreAuthorize("@ss.hasAnyPermi('system:rct:add,system:rct:edit')")
    @PostMapping("/saveRctLogistics")
    public AjaxResult saveRctLogistics(@RequestBody RsRctOld rsRct) {
        return toAjax(rsRctService.saveRctLogistics(rsRct));
    }

    @PreAuthorize("@ss.hasAnyPermi('system:rct:add,system:rct:edit')")
    @PostMapping("/saveRctPreCarriage")
    public AjaxResult saveRctPreCarriage(@RequestBody RsRctOld rsRct) {
        return toAjax(rsRctService.saveRctPreCarriage(rsRct));
    }

    @PreAuthorize("@ss.hasAnyPermi('system:rct:add,system:rct:edit')")
    @PostMapping("/saveRctExportDeclaration")
    public AjaxResult saveRctExportDeclaration(@RequestBody RsRctOld rsRct) {
        return toAjax(rsRctService.saveRctExportDeclaration(rsRct));
    }

    @PreAuthorize("@ss.hasAnyPermi('system:rct:add,system:rct:edit')")
    @PostMapping("/saveRctImportClearance")
    public AjaxResult saveRctImportClearance(@RequestBody RsRctOld rsRct) {
        return toAjax(rsRctService.saveRctImportClearance(rsRct));
    }

    @GetMapping("/getRctStatistics")
    public AjaxResult getRctStatistics() {
        AjaxResult ajaxResult = AjaxResult.success();
        List<RcRctStatisticsVO> statisticsList = rsRctService.getRctStatistics();
        ajaxResult.put(AjaxResult.DATA_TAG, statisticsList);
        return ajaxResult;
    }
}
