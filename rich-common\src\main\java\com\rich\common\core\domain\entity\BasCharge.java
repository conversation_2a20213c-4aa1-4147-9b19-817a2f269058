package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 【请填写功能名称】对象 bas_charge
 *
 * <AUTHOR>
 * @date 2022-08-30
 */
public class BasCharge extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 费用id
     */
    private Long chargeId;

    /**
     * 已有通用英文简称的直接用英文，中文习惯用中文，方便录入为准
     */
    @Excel(name = "简称")
    private String chargeShortName;

    /**
     * 不一定写全称，内行能看懂就用英文简称
     */
    @Excel(name = "英文简称")
    private String chargeEnName;

    /**
     * 不一定写全称，内行能看懂就用英文或中文简称
     */
    @Excel(name = "中文简称")
    private String chargeLocalName;

    /**
     * 默认的币种类型列表，多选
     */
    @Excel(name = "默认的币种类型列表")
    private String currencyCodeList;

    /**
     * 默认的计费单位列表，多选
     */
    @Excel(name = "默认的计费单位列表")
    private String chargeUnitCodeList;

    /**
     * 费用类别，查询
     */
    @Excel(name = "费用类别")
    private Long chargeTypeId;

    /**
     * 水平优先级，排序方式：费用类别-计费单位-水平优先级
     */
    private Integer orderNum;

    private String unit;

    private Long[] unitIds;

    private String currency;

    private Long[] currencyIds;

    private String status;

    private String currencies;

    private String units;

    private String chargeTypeName;

    private String chargeQuery;
    private String unitCode;
    private String currencyCode;

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getChargeQuery() {
        return chargeQuery;
    }

    public void setChargeQuery(String chargeQuery) {
        this.chargeQuery = chargeQuery;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCurrencies() {
        return currencies;
    }

    public void setCurrencies(String currencies) {
        this.currencies = currencies;
    }

    public String getUnits() {
        return units;
    }

    public void setUnits(String units) {
        this.units = units;
    }

    public String getChargeTypeName() {
        return chargeTypeName;
    }

    public void setChargeTypeName(String chargeTypeName) {
        this.chargeTypeName = chargeTypeName;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Long[] getUnitIds() {
        return unitIds;
    }

    public void setUnitIds(Long[] unitIds) {
        this.unitIds = unitIds;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Long[] getCurrencyIds() {
        return currencyIds;
    }

    public void setCurrencyIds(Long[] currencyIds) {
        this.currencyIds = currencyIds;
    }

    public void setChargeId(Long chargeId) {
        this.chargeId = chargeId;
    }

    public Long getChargeId() {
        return chargeId;
    }

    public void setChargeShortName(String chargeShortName) {
        this.chargeShortName = chargeShortName;
    }

    public String getChargeShortName() {
        return chargeShortName;
    }

    public void setChargeEnName(String chargeEnName) {
        this.chargeEnName = chargeEnName;
    }

    public String getChargeEnName() {
        return chargeEnName;
    }

    public void setChargeLocalName(String chargeLocalName) {
        this.chargeLocalName = chargeLocalName;
    }

    public String getChargeLocalName() {
        return chargeLocalName;
    }

    public void setCurrencyCodeList(String currencyCodeList) {
        this.currencyCodeList = currencyCodeList;
    }

    public String getCurrencyCodeList() {
        return currencyCodeList;
    }

    public void setChargeUnitCodeList(String chargeUnitCodeList) {
        this.chargeUnitCodeList = chargeUnitCodeList;
    }

    public String getChargeUnitCodeList() {
        return chargeUnitCodeList;
    }

    public void setChargeTypeId(Long chargeTypeId) {
        this.chargeTypeId = chargeTypeId;
    }

    public Long getChargeTypeId() {
        return chargeTypeId;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }
}
