package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasDocIssueType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 文件出单方式Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@Mapper
public interface BasDocIssueTypeMapper {
    /**
     * 查询文件出单方式
     *
     * @param issueTypeId 文件出单方式主键
     * @return 文件出单方式
     */
    BasDocIssueType selectBasDocIssueTypeByIssueTypeId(Long issueTypeId);

    /**
     * 查询文件出单方式列表
     *
     * @param basDocIssueType 文件出单方式
     * @return 文件出单方式集合
     */
    List<BasDocIssueType> selectBasDocIssueTypeList(BasDocIssueType basDocIssueType);

    /**
     * 新增文件出单方式
     *
     * @param basDocIssueType 文件出单方式
     * @return 结果
     */
    int insertBasDocIssueType(BasDocIssueType basDocIssueType);

    /**
     * 修改文件出单方式
     *
     * @param basDocIssueType 文件出单方式
     * @return 结果
     */
    int updateBasDocIssueType(BasDocIssueType basDocIssueType);

    /**
     * 删除文件出单方式
     *
     * @param issueTypeId 文件出单方式主键
     * @return 结果
     */
    int deleteBasDocIssueTypeByIssueTypeId(Long issueTypeId);

    /**
     * 批量删除文件出单方式
     *
     * @param issueTypeIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasDocIssueTypeByIssueTypeIds(Long[] issueTypeIds);
}
