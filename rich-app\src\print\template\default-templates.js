export default {
    "id": "nodat-cargo-logistics-label",
    "name": "NODAT CARGO AND LOGISTICS LTD",
    "width": 76,
    "height": 130,
    "gap": 1,
    "elements": {
        "text": [
            {
                "content": "CARGO ENTRY INFORMATION",
                "x": 3,
                "y": 2,
                "fontIndex": 7,
                "rotationIndex": 0,
                "sizeIndex": 3,
                "alignment": "center"
            },
            {
                "content": "${inboundNo}",
                "x": 3,
                "y": 12,
                "fontIndex": 1,
                "rotationIndex": 0,
                "sizeIndex": 3,
                "alignment": "left"
            },
            {
                "content": "${inboundDate}",
                "x": 45,
                "y": 14,
                "fontIndex": 1,
                "rotationIndex": 0,
                "sizeIndex": 0,
                "alignment": "left"
            },
            {
                "content": "${clientCode}",
                "x": 30,
                "y": 16,
                "fontIndex": 3,
                "rotationIndex": 0,
                "sizeIndex": 7,
                "alignment": "left"
            },
            {
                "content": "${clientCode}-${consigneeCode}",
                "x": 30,
                "y": 33,
                "fontIndex": 1,
                "rotationIndex": 0,
                "sizeIndex": 3,
                "alignment": "left"
            },
            {
                "content": "${consigneeName}",
                "x": 30,
                "y": 39,
                "fontIndex": 1,
                "rotationIndex": 0,
                "sizeIndex": 0,
                "alignment": "left"
            },
            {
                "content": "${consigneeTel}",
                "x": 30,
                "y": 44,
                "fontIndex": 1,
                "rotationIndex": 0,
                "sizeIndex": 0,
                "alignment": "left"
            },
            {
                "content": "GUANGZHOU - ${clientRegion}",
                "x": 3,
                "y": 47,
                "fontIndex": 1,
                "rotationIndex": 0,
                "sizeIndex": 3,
                "alignment": "center"
            },
            {
                "content": "${totalBoxes}",
                "x": 10,
                "y": 54,
                "fontIndex": 1,
                "rotationIndex": 0,
                "sizeIndex": 3,
                "alignment": "left"
            },
            {
                "content": "${specialMark}",
                "x": 65,
                "y": 54,
                "fontIndex": 1,
                "rotationIndex": 0,
                "sizeIndex": 3,
                "alignment": "left"
            },
            {
                "content": "物流信息: ",
                "x": 2,
                "y": 64,
                "fontIndex": 0,
                "rotationIndex": 0,
                "sizeIndex": 0,
                "alignment": "left"
            },
            {
                "content": "Logistics info",
                "x": 2,
                "y": 68,
                "fontIndex": 0,
                "rotationIndex": 0,
                "sizeIndex": 0,
                "alignment": "left"
            },
            {
                "content": "${logisticsInfo}",
                "x": 25,
                "y": 64,
                "fontIndex": 0,
                "rotationIndex": 0,
                "sizeIndex": 3,
                "alignment": "left"
            },
            {
                "content": "物流条码",
                "x": 2,
                "y": 73,
                "fontIndex": 0,
                "rotationIndex": 0,
                "sizeIndex": 0,
                "alignment": "left"
            },
            {
                "content": "Source Code",
                "x": 2,
                "y": 77,
                "fontIndex": 0,
                "rotationIndex": 0,
                "sizeIndex": 0,
                "alignment": "left"
            },
            {
                "content": "收货地点: ",
                "x": 2,
                "y": 82,
                "fontIndex": 0,
                "rotationIndex": 0,
                "sizeIndex": 0,
                "alignment": "left"
            },
            {
                "content": "Entry Add",
                "x": 2,
                "y": 86,
                "fontIndex": 0,
                "rotationIndex": 0,
                "sizeIndex": 0,
                "alignment": "left"
            },
            {
                "content": "佛山里水镇沿江路3号力进物流园",
                "x": 25,
                "y": 82,
                "fontIndex": 0,
                "rotationIndex": 0,
                "sizeIndex": 0,
                "alignment": "left"
            },
            {
                "content": "B仓18库-${clientCode}-${consigneeCode}",
                "x": 25,
                "y": 86,
                "fontIndex": 0,
                "rotationIndex": 0,
                "sizeIndex": 0,
                "alignment": "left"
            },
            {
                "content": "运抵仓库: ",
                "x": 2,
                "y": 91,
                "fontIndex": 0,
                "rotationIndex": 0,
                "sizeIndex": 0,
                "alignment": "left"
            },
            {
                "content": "Delivery Fm",
                "x": 2,
                "y": 95,
                "fontIndex": 0,
                "rotationIndex": 0,
                "sizeIndex": 0,
                "alignment": "left"
            },
            {
                "content": "${cargoAddress1}",
                "x": 25,
                "y": 92,
                "fontIndex": 0,
                "rotationIndex": 0,
                "sizeIndex": 0,
                "alignment": "left"
            },
            {
                "content": "${cargoAddress2}",
                "x": 25,
                "y": 96,
                "fontIndex": 0,
                "rotationIndex": 0,
                "sizeIndex": 0,
                "alignment": "left"
            },
            {
                "content": "提货联系: ",
                "x": 2,
                "y": 100,
                "fontIndex": 0,
                "rotationIndex": 0,
                "sizeIndex": 0,
                "alignment": "left"
            },
            {
                "content": "Delivery Attn",
                "x": 2,
                "y": 104,
                "fontIndex": 0,
                "rotationIndex": 0,
                "sizeIndex": 0,
                "alignment": "left"
            },
            {
                "content": "${cargoPhone}",
                "x": 25,
                "y": 102,
                "fontIndex": 0,
                "rotationIndex": 0,
                "sizeIndex": 0,
                "alignment": "left"
            },
            {
                "content": "入仓费用",
                "x": 3,
                "y": 110,
                "fontIndex": 0,
                "rotationIndex": 0,
                "sizeIndex": 0,
                "alignment": "center"
            },
            {
                "content": "破损标记",
                "x": 22,
                "y": 110,
                "fontIndex": 0,
                "rotationIndex": 0,
                "sizeIndex": 0,
                "alignment": "center"
            },
            {
                "content": "拆箱验视",
                "x": 41,
                "y": 110,
                "fontIndex": 0,
                "rotationIndex": 0,
                "sizeIndex": 0,
                "alignment": "center"
            },
            {
                "content": "秤重复尺",
                "x": 60,
                "y": 110,
                "fontIndex": 0,
                "rotationIndex": 0,
                "sizeIndex": 0,
                "alignment": "center"
            }
        ],
        "qrcode": [
            {
                "content": "${inboundSerialNo}",
                "x": 3,
                "y": 18,
                "levelIndex": 0,
                "width": 10,
                "mode": "A"
            }
        ],
        "barcode": [
            {
                "content": "${logisticsInfo}",
                "x": 25,
                "y": 73,
                "type": "CODE128",
                "width": 45,
                "height": 8,
                "humanReadable": 1
            }
        ],
        "line": [
            {
                "x1": 0,
                "y1": 0,
                "x2": 76,
                "y2": 0,
                "thickness": 1
            },
            {
                "x1": 0,
                "y1": 15,
                "x2": 76,
                "y2": 15,
                "thickness": 1
            },
            {
                "x1": 0,
                "y1": 51,
                "x2": 76,
                "y2": 51,
                "thickness": 1
            },
            {
                "x1": 0,
                "y1": 65,
                "x2": 76,
                "y2": 65,
                "thickness": 1
            },
            {
                "x1": 0,
                "y1": 117,
                "x2": 76,
                "y2": 117,
                "thickness": 1
            },
            {
                "x1": 19,
                "y1": 117,
                "x2": 19,
                "y2": 130,
                "thickness": 1
            },
            {
                "x1": 38,
                "y1": 117,
                "x2": 38,
                "y2": 130,
                "thickness": 1
            },
            {
                "x1": 57,
                "y1": 117,
                "x2": 57,
                "y2": 130,
                "thickness": 1
            },
            {
                "x1": 0,
                "y1": 130,
                "x2": 76,
                "y2": 130,
                "thickness": 1
            }
        ],
        "box": [
            {
                "x": 0,
                "y": 0,
                "width": 76,
                "height": 130,
                "thickness": 1,
                "filled": 0
            }
        ]
    }
}