package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpExpress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsOpExpressMapper;
import com.rich.system.service.RsOpExpressService;

/**
 * 快递服务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Service
public class RsOpExpressServiceImpl implements RsOpExpressService {
    @Autowired
    private RsOpExpressMapper rsOpExpressMapper;

    /**
     * 查询快递服务
     *
     * @param expressId 快递服务主键
     * @return 快递服务
     */
    @Override
    public RsOpExpress selectRsOpExpressByExpressId(Long expressId) {
        return rsOpExpressMapper.selectRsOpExpressByExpressId(expressId);
    }

    /**
     * 查询快递服务列表
     *
     * @param rsOpExpress 快递服务
     * @return 快递服务
     */
    @Override
    public List<RsOpExpress> selectRsOpExpressList(RsOpExpress rsOpExpress) {
        return rsOpExpressMapper.selectRsOpExpressList(rsOpExpress);
    }

    /**
     * 新增快递服务
     *
     * @param rsOpExpress 快递服务
     * @return 结果
     */
    @Override
    public int insertRsOpExpress(RsOpExpress rsOpExpress) {
        return rsOpExpressMapper.insertRsOpExpress(rsOpExpress);
    }

    /**
     * 修改快递服务
     *
     * @param rsOpExpress 快递服务
     * @return 结果
     */
    @Override
    public int updateRsOpExpress(RsOpExpress rsOpExpress) {
        return rsOpExpressMapper.updateRsOpExpress(rsOpExpress);
    }

    /**
     * 修改快递服务状态
     *
     * @param rsOpExpress 快递服务
     * @return 快递服务
     */
    @Override
    public int changeStatus(RsOpExpress rsOpExpress) {
        return rsOpExpressMapper.updateRsOpExpress(rsOpExpress);
    }

    /**
     * 批量删除快递服务
     *
     * @param expressIds 需要删除的快递服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpExpressByExpressIds(Long[] expressIds) {
        return rsOpExpressMapper.deleteRsOpExpressByExpressIds(expressIds);
    }

    /**
     * 删除快递服务信息
     *
     * @param expressId 快递服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpExpressByExpressId(Long expressId) {
        return rsOpExpressMapper.deleteRsOpExpressByExpressId(expressId);
    }
}
