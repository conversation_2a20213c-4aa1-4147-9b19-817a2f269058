package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpTruck;
import com.rich.system.mapper.RsOpTruckMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.service.RsOpExportTruckService;

/**
 * 出口拖车服务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Service
public class RsOpExportTruckServiceImpl implements RsOpExportTruckService {
    @Autowired
    private RsOpTruckMapper rsOpTruckMapper;

    /**
     * 查询出口拖车服务
     *
     * @param exportTruckId 出口拖车服务主键
     * @return 出口拖车服务
     */
    @Override
    public RsOpTruck selectRsOpExportTruckByExportTruckId(Long exportTruckId) {
        return rsOpTruckMapper.selectRsOpExportTruckByExportTruckId(exportTruckId);
    }

    /**
     * 查询出口拖车服务列表
     *
     * @param rsOpExportTruck 出口拖车服务
     * @return 出口拖车服务
     */
    @Override
    public List<RsOpTruck> selectRsOpExportTruckList(RsOpTruck rsOpExportTruck) {
        return rsOpTruckMapper.selectRsOpExportTruckList(rsOpExportTruck);
    }

    /**
     * 新增出口拖车服务
     *
     * @param rsOpExportTruck 出口拖车服务
     * @return 结果
     */
    @Override
    public int insertRsOpExportTruck(RsOpTruck rsOpExportTruck) {
        return rsOpTruckMapper.insertRsOpTruck(rsOpExportTruck);
    }

    /**
     * 修改出口拖车服务
     *
     * @param rsOpExportTruck 出口拖车服务
     * @return 结果
     */
    @Override
    public int updateRsOpExportTruck(RsOpTruck rsOpExportTruck) {
        return rsOpTruckMapper.updateRsOpExportTruck(rsOpExportTruck);
    }

    /**
     * 修改出口拖车服务状态
     *
     * @param rsOpExportTruck 出口拖车服务
     * @return 出口拖车服务
     */
    @Override
    public int changeStatus(RsOpTruck rsOpExportTruck) {
        return rsOpTruckMapper.updateRsOpExportTruck(rsOpExportTruck);
    }

    /**
     * 批量删除出口拖车服务
     *
     * @param exportTruckIds 需要删除的出口拖车服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpExportTruckByExportTruckIds(Long[] exportTruckIds) {
        return rsOpTruckMapper.deleteRsOpExportTruckByExportTruckIds(exportTruckIds);
    }

    /**
     * 删除出口拖车服务信息
     *
     * @param exportTruckId 出口拖车服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpExportTruckByExportTruckId(Long exportTruckId) {
        return rsOpTruckMapper.deleteRsOpExportTruckByExportTruckId(exportTruckId);
    }
}
