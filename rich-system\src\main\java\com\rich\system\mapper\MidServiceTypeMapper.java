package com.rich.system.mapper;

import com.rich.system.domain.MidServiceType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 服务类型Mapper接口
 *
 * <AUTHOR>
 * @date 2022-09-07
 */
@Mapper
public interface MidServiceTypeMapper {
    /**
     * 查询服务类型
     *
     * @param belongId 服务类型主键
     * @param belongTo 属于
     * @return 服务类型
     */
    List<Long> selectMidServiceTypeById(Long belongId, String belongTo);

    /**
     * 查询服务类型列表
     *
     * @param MidServiceType 服务类型
     * @return 服务类型集合
     */
    List<MidServiceType> selectMidServiceTypeList(MidServiceType MidServiceType);

    /**
     * 新增服务类型
     *
     * @param MidServiceType 服务类型
     * @return 结果
     */
    int insertMidServiceType(MidServiceType MidServiceType);


    /**
     * 删除服务类型
     *
     * @param belongTo 属于
     * @param belongId 服务类型主键
     * @return 结果
     */
    int deleteMidServiceTypeById(Long belongId, String belongTo);

    /**
     * 批量删除服务类型
     *
     * @param belongTo  属于
     * @param belongIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteMidServiceTypeByIds(Long[] belongIds, String belongTo);

    int batchServiceType(List<MidServiceType> list);
}
