package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpPortService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsOpPortServiceMapper;
import com.rich.system.service.RsOpPortServiceService;

/**
 * 码头服务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Service
public class RsOpPortServiceServiceImpl implements RsOpPortServiceService {
    @Autowired
    private RsOpPortServiceMapper rsOpPortServiceMapper;

    /**
     * 查询码头服务
     *
     * @param portServiceId 码头服务主键
     * @return 码头服务
     */
    @Override
    public RsOpPortService selectRsOpPortServiceByPortServiceId(Long portServiceId) {
        return rsOpPortServiceMapper.selectRsOpPortServiceByPortServiceId(portServiceId);
    }

    /**
     * 查询码头服务列表
     *
     * @param rsOpPortService 码头服务
     * @return 码头服务
     */
    @Override
    public List<RsOpPortService> selectRsOpPortServiceList(RsOpPortService rsOpPortService) {
        return rsOpPortServiceMapper.selectRsOpPortServiceList(rsOpPortService);
    }

    /**
     * 新增码头服务
     *
     * @param rsOpPortService 码头服务
     * @return 结果
     */
    @Override
    public int insertRsOpPortService(RsOpPortService rsOpPortService) {
        return rsOpPortServiceMapper.insertRsOpPortService(rsOpPortService);
    }

    /**
     * 修改码头服务
     *
     * @param rsOpPortService 码头服务
     * @return 结果
     */
    @Override
    public int updateRsOpPortService(RsOpPortService rsOpPortService) {
        return rsOpPortServiceMapper.updateRsOpPortService(rsOpPortService);
    }

    /**
     * 修改码头服务状态
     *
     * @param rsOpPortService 码头服务
     * @return 码头服务
     */
    @Override
    public int changeStatus(RsOpPortService rsOpPortService) {
        return rsOpPortServiceMapper.updateRsOpPortService(rsOpPortService);
    }

    /**
     * 批量删除码头服务
     *
     * @param portServiceIds 需要删除的码头服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpPortServiceByPortServiceIds(Long[] portServiceIds) {
        return rsOpPortServiceMapper.deleteRsOpPortServiceByPortServiceIds(portServiceIds);
    }

    /**
     * 删除码头服务信息
     *
     * @param portServiceId 码头服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpPortServiceByPortServiceId(Long portServiceId) {
        return rsOpPortServiceMapper.deleteRsOpPortServiceByPortServiceId(portServiceId);
    }
}
