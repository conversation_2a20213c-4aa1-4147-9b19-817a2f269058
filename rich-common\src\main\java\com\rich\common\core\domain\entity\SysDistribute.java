package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

/**
 * 权限分配对象 sys_distribute
 *
 * <AUTHOR>
 * @date 2023-02-13
 */
public class SysDistribute extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 批量权限分配
     */
    private Long distributeId;

    private String distributeName;

    /**
     * 部门列表
     */
    @Excel(name = "部门列表")
    private String deptList;

    private String position;

    /**
     * 菜单权限
     */
    @Excel(name = "菜单权限")
    private String menuList;

    /**
     * 分配权限
     */
    @Excel(name = "分配权限")
    private String permsList;

    private Long[] deptIds;

    private Long[] menuIds;

    private Long[] permsIds;

    private Long positionId;

    private String orderNum;

    private String status;

    public String getPermsList() {
        return permsList;
    }

    public void setPermsList(String permsList) {
        this.permsList = permsList;
    }

    public Long[] getPermsIds() {
        return permsIds;
    }

    public void setPermsIds(Long[] permsIds) {
        this.permsIds = permsIds;
    }

    public String getDistributeName() {
        return distributeName;
    }

    public void setDistributeName(String distributeName) {
        this.distributeName = distributeName;
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long[] getDeptIds() {
        return deptIds;
    }

    public void setDeptIds(Long[] deptIds) {
        this.deptIds = deptIds;
    }

    public Long[] getMenuIds() {
        return menuIds;
    }

    public void setMenuIds(Long[] menuIds) {
        this.menuIds = menuIds;
    }

    public Long getPositionId() {
        return positionId;
    }

    public void setPositionId(Long positionId) {
        this.positionId = positionId;
    }

    public Long getDistributeId() {
        return distributeId;
    }

    public void setDistributeId(Long distributeId) {
        this.distributeId = distributeId;
    }

    public String getDeptList() {
        return deptList;
    }

    public void setDeptList(String deptList) {
        this.deptList = deptList;
    }

    public String getMenuList() {
        return menuList;
    }

    public void setMenuList(String menuList) {
        this.menuList = menuList;
    }


    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }
}
