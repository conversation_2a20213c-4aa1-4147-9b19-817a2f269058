package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsExportCustoms;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 出口报关Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@Mapper
public interface RsExportCustomsMapper {
    /**
     * 查询出口报关
     *
     * @param exportCustomsId 出口报关主键
     * @return 出口报关
     */
    RsExportCustoms selectRsExportCustomsByExportCustomsId(Long exportCustomsId);

    /**
     * 查询出口报关列表
     *
     * @param rsExportCustoms 出口报关
     * @return 出口报关集合
     */
    List<RsExportCustoms> selectRsExportCustomsList(RsExportCustoms rsExportCustoms);

    /**
     * 新增出口报关
     *
     * @param rsExportCustoms 出口报关
     * @return 结果
     */
    int insertRsExportCustoms(RsExportCustoms rsExportCustoms);

    /**
     * 修改出口报关
     *
     * @param rsExportCustoms 出口报关
     * @return 结果
     */
    int updateRsExportCustoms(RsExportCustoms rsExportCustoms);

    /**
     * 删除出口报关
     *
     * @param exportCustomsId 出口报关主键
     * @return 结果
     */
    int deleteRsExportCustomsByExportCustomsId(Long exportCustomsId);

    /**
     * 批量删除出口报关
     *
     * @param exportCustomsIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsExportCustomsByExportCustomsIds(Long[] exportCustomsIds);

    RsExportCustoms selectRsExportCustoms(Long serviceInstanceId);

    RsExportCustoms selectRsExportCustomsByServiceInstance(Long rctId);
}
