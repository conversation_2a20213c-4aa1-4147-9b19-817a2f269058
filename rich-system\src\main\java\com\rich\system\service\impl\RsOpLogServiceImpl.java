package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsOpLogMapper;
import com.rich.system.service.RsOpLogService;

/**
 * 操作单操作记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-03-08
 */
@Service
public class RsOpLogServiceImpl implements RsOpLogService {
    @Autowired
    private RsOpLogMapper rsOpLogMapper;

    /**
     * 查询操作单操作记录
     * 
     * @param opLogId 操作单操作记录主键
     * @return 操作单操作记录
     */
    @Override
    public RsOpLog selectRsOpLogByOpLogId(Long opLogId) {
        return rsOpLogMapper.selectRsOpLogByOpLogId(opLogId);
    }

    /**
     * 查询操作单操作记录列表
     * 
     * @param rsOpLog 操作单操作记录
     * @return 操作单操作记录
     */
    @Override
    public List<RsOpLog> selectRsOpLogList(RsOpLog rsOpLog) {
        return rsOpLogMapper.selectRsOpLogList(rsOpLog);
    }

    /**
     * 新增操作单操作记录
     * 
     * @param rsOpLog 操作单操作记录
     * @return 结果
     */
    @Override
    public int insertRsOpLog(RsOpLog rsOpLog) {
        return rsOpLogMapper.insertRsOpLog(rsOpLog);
    }

    /**
     * 修改操作单操作记录
     * 
     * @param rsOpLog 操作单操作记录
     * @return 结果
     */
    @Override
    public int updateRsOpLog(RsOpLog rsOpLog) {
        return rsOpLogMapper.updateRsOpLog(rsOpLog);
    }

    /**
     * 修改操作单操作记录状态
     *
     * @param rsOpLog 操作单操作记录
     * @return 操作单操作记录
     */
    @Override
    public int changeStatus(RsOpLog rsOpLog) {
        return rsOpLogMapper.updateRsOpLog(rsOpLog);
    }

    /**
     * 批量删除操作单操作记录
     * 
     * @param opLogIds 需要删除的操作单操作记录主键
     * @return 结果
     */
    @Override
    public int deleteRsOpLogByOpLogIds(Long[] opLogIds) {
        return rsOpLogMapper.deleteRsOpLogByOpLogIds(opLogIds);
    }

    /**
     * 删除操作单操作记录信息
     * 
     * @param opLogId 操作单操作记录主键
     * @return 结果
     */
    @Override
    public int deleteRsOpLogByOpLogId(Long opLogId) {
        return rsOpLogMapper.deleteRsOpLogByOpLogId(opLogId);
    }
}
