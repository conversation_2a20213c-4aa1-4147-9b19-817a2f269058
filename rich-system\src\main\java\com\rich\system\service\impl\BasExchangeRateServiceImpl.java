package com.rich.system.service.impl;

import com.rich.common.constant.CacheConstants;
import com.rich.common.core.domain.entity.BasExchangeRate;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.system.mapper.BasExchangeRateMapper;
import com.rich.system.mapper.RsChargeMapper;
import com.rich.system.service.BasExchangeRateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 汇率Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-30
 */
@Service
public class BasExchangeRateServiceImpl implements BasExchangeRateService {
    @Autowired
    private BasExchangeRateMapper basExchangeRateMapper;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisCacheImpl RedisCache;

    @Resource
    private RsChargeMapper rsChargeMapper;

    /**
     * 查询汇率
     *
     * @param exchangeRateId 汇率主键
     * @return 汇率
     */
    @Override
    public BasExchangeRate selectBasExchangeRateByExchangeRateId(Long exchangeRateId) {
        return basExchangeRateMapper.selectBasExchangeRateByExchangeRateId(exchangeRateId);
    }

    /**
     * 查询汇率列表
     *
     * @param basExchangeRate 汇率
     * @return 汇率
     */
    @Override
    public List<BasExchangeRate> selectBasExchangeRateList(BasExchangeRate basExchangeRate) {
        return basExchangeRateMapper.selectBasExchangeRateList(basExchangeRate);
    }

    /**
     * 新增汇率
     *
     * @param basExchangeRate 汇率
     * @return 结果
     */
    @Override
    public int insertBasExchangeRate(BasExchangeRate basExchangeRate) {
        basExchangeRate.setCreateTime(DateUtils.getNowDate());
        basExchangeRate.setCreateBy(SecurityUtils.getUserId());
        return basExchangeRateMapper.insertBasExchangeRate(basExchangeRate);
    }

    /**
     * 修改汇率
     *
     * @param basExchangeRate 汇率
     * @return 结果
     */
    @Override
    public int updateBasExchangeRate(BasExchangeRate basExchangeRate) {
        basExchangeRate.setUpdateTime(DateUtils.getNowDate());
        basExchangeRate.setUpdateBy(SecurityUtils.getUserId());
        return basExchangeRateMapper.updateBasExchangeRate(basExchangeRate);
    }

    /**
     * 根据财务更新的汇率去刷新对应时间内的费用汇率
     *
     * @param basExchangeRate
     */
    public void flushExchangeRate(BasExchangeRate basExchangeRate) {
        BigDecimal exchangeRate = basExchangeRate.getSellRate().divide(basExchangeRate.getBase(), RoundingMode.CEILING);
        rsChargeMapper.flushExchangeRate(exchangeRate, basExchangeRate.getValidFrom(), basExchangeRate.getValidTo());
    }

    /**
     * 修改汇率状态
     *
     * @param basExchangeRate 汇率
     * @return 汇率
     */
    @Override
    public int changeStatus(BasExchangeRate basExchangeRate) {
        return basExchangeRateMapper.updateBasExchangeRate(basExchangeRate);
    }

    @Override
    public List<BasExchangeRate> selectList() {
        List<BasExchangeRate> basExchangeRates = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "exchangeRateList");
        if (basExchangeRates == null) {
            RedisCache.exchangeRate();
            basExchangeRates = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "exchangeRateList");
        }
        return basExchangeRates;
    }

    /**
     * 批量删除汇率
     *
     * @param exchangeRateIds 需要删除的汇率主键
     * @return 结果
     */
    @Override
    public int deleteBasExchangeRateByExchangeRateIds(Long[] exchangeRateIds) {
        return basExchangeRateMapper.deleteBasExchangeRateByExchangeRateIds(exchangeRateIds);
    }

    /**
     * 删除汇率信息
     *
     * @param exchangeRateId 汇率主键
     * @return 结果
     */
    @Override
    public int deleteBasExchangeRateByExchangeRateId(Long exchangeRateId) {
        return basExchangeRateMapper.deleteBasExchangeRateByExchangeRateId(exchangeRateId);
    }
}
