package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.BasTransportationTerms;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.system.mapper.BasTransportationTermsMapper;
import com.rich.system.service.BasTransportationTermsService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 运输条款Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-05
 */
@Service
public class BasTransportationTermsServiceImpl implements BasTransportationTermsService {
    @Autowired
    private  BasTransportationTermsMapper basTransportationTermsMapper;

    /**
     * 查询运输条款
     *
     * @param transportationTermsId 运输条款主键
     * @return 运输条款
     */
    @Override
    public BasTransportationTerms selectBasTransportationTermsByTransportationTermsId(Long transportationTermsId) {
        return basTransportationTermsMapper.selectBasTransportationTermsByTransportationTermsId(transportationTermsId);
    }

    /**
     * 查询运输条款列表
     *
     * @param basTransportationTerms 运输条款
     * @return 运输条款
     */
    @Override
    public List<BasTransportationTerms> selectBasTransportationTermsList(BasTransportationTerms basTransportationTerms) {
        return basTransportationTermsMapper.selectBasTransportationTermsList(basTransportationTerms);
    }

    /**
     * 新增运输条款
     *
     * @param basTransportationTerms 运输条款
     * @return 结果
     */
    @Override
    public int insertBasTransportationTerms(BasTransportationTerms basTransportationTerms) {
        basTransportationTerms.setCreateTime(DateUtils.getNowDate());
        basTransportationTerms.setCreateBy(SecurityUtils.getUserId());
        return basTransportationTermsMapper.insertBasTransportationTerms(basTransportationTerms);
    }

    /**
     * 修改运输条款
     *
     * @param basTransportationTerms 运输条款
     * @return 结果
     */
    @Override
    public int updateBasTransportationTerms(BasTransportationTerms basTransportationTerms) {
        basTransportationTerms.setUpdateTime(DateUtils.getNowDate());
        basTransportationTerms.setUpdateBy(SecurityUtils.getUserId());
        return basTransportationTermsMapper.updateBasTransportationTerms(basTransportationTerms);
    }

    /**
     * 修改运输条款状态
     *
     * @param basTransportationTerms 运输条款
     * @return 运输条款
     */
    @Override
    public int changeStatus(BasTransportationTerms basTransportationTerms) {
        return basTransportationTermsMapper.updateBasTransportationTerms(basTransportationTerms);
    }

    /**
     * 批量删除运输条款
     *
     * @param transportationTermsIds 需要删除的运输条款主键
     * @return 结果
     */
    @Override
    public int deleteBasTransportationTermsByTransportationTermsIds(Long[] transportationTermsIds) {
        return basTransportationTermsMapper.deleteBasTransportationTermsByTransportationTermsIds(transportationTermsIds);
    }

    /**
     * 删除运输条款信息
     *
     * @param transportationTermsId 运输条款主键
     * @return 结果
     */
    @Override
    public int deleteBasTransportationTermsByTransportationTermsId(Long transportationTermsId) {
        return basTransportationTermsMapper.deleteBasTransportationTermsByTransportationTermsId(transportationTermsId);
    }
}
