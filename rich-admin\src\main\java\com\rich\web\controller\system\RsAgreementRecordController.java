package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.RsAgreementRecord;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.RsAgreementRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;

/**
 * 协议记录Controller
 *
 * <AUTHOR>
 * @date 2022-10-28
 */
@RestController
@RequestMapping("/system/agreementrecord")
public class RsAgreementRecordController extends BaseController {

    @Autowired
    private RsAgreementRecordService rsAgreementRecordService;

    /**
     * 查询协议记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:agreementrecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsAgreementRecord rsAgreementRecord) {
        startPage();
        List<RsAgreementRecord> list = rsAgreementRecordService.selectRsAgreementRecordList(rsAgreementRecord);
        return getDataTable(list);
    }

    /**
     * 导出协议记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:agreementrecord:export')")
    @Log(title = "协议记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsAgreementRecord rsAgreementRecord) {
        List<RsAgreementRecord> list = rsAgreementRecordService.selectRsAgreementRecordList(rsAgreementRecord);
        ExcelUtil<RsAgreementRecord> util = new ExcelUtil<RsAgreementRecord>(RsAgreementRecord.class);
        util.exportExcel(response, list, "协议记录数据");
    }

    /**
     * 获取协议记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:agreementrecord:edit')")
    @GetMapping(value = "/{agreementId}")
    public AjaxResult getInfo(@PathVariable("agreementId") Long agreementId) {
        return AjaxResult.success(rsAgreementRecordService.selectRsAgreementRecordByAgreementId(agreementId));
    }

    /**
     * 新增协议记录
     */
    @PreAuthorize("@ss.hasPermi('system:agreementrecord:add')")
    @Log(title = "协议记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsAgreementRecord rsAgreementRecord) {
        rsAgreementRecord.setStaffId(SecurityUtils.getUserId());
        return toAjax(rsAgreementRecordService.insertRsAgreementRecord(rsAgreementRecord));
    }

    /**
     * 修改协议记录
     */
    @PreAuthorize("@ss.hasPermi('system:agreementrecord:edit')")
    @Log(title = "协议记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsAgreementRecord rsAgreementRecord) {
        if (rsAgreementRecord.getFinanceConfirmed().equals("1") && rsAgreementRecord.getDeptConfirmed() != null) {
            rsAgreementRecord.setFinanceConfirmedDate(DateUtils.getNowDate());
        }
        if (rsAgreementRecord.getDeptConfirmed().equals("1") && rsAgreementRecord.getFinanceConfirmed() != null) {
            rsAgreementRecord.setDeptConfirmedDate(DateUtils.getNowDate());
        }
        if (rsAgreementRecord.getDeptConfirmed().equals("1") || Objects.equals(rsAgreementRecord.getFinanceConfirmed(), "1")) {
            rsAgreementRecord.setIsLocked("1");
        } else {
            rsAgreementRecord.setIsLocked("0");
        }
        return toAjax(rsAgreementRecordService.updateRsAgreementRecord(rsAgreementRecord));
    }

    /**
     * 删除协议记录
     */
    @PreAuthorize("@ss.hasPermi('system:agreementrecord:remove')")
    @Log(title = "协议记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{agreementIds}")
    public AjaxResult remove(@PathVariable Long[] agreementIds) {
        return toAjax(rsAgreementRecordService.deleteRsAgreementRecordByAgreementIds(agreementIds));
    }
}
