package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpRail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsOpRailMapper;
import com.rich.system.service.RsOpRailService;

/**
 * 铁路服务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Service
public class RsOpRailServiceImpl implements RsOpRailService {
    @Autowired
    private RsOpRailMapper rsOpRailMapper;

    /**
     * 查询铁路服务
     *
     * @param railId 铁路服务主键
     * @return 铁路服务
     */
    @Override
    public RsOpRail selectRsOpRailByRailId(Long railId) {
        return rsOpRailMapper.selectRsOpRailByRailId(railId);
    }

    /**
     * 查询铁路服务列表
     *
     * @param rsOpRail 铁路服务
     * @return 铁路服务
     */
    @Override
    public List<RsOpRail> selectRsOpRailList(RsOpRail rsOpRail) {
        return rsOpRailMapper.selectRsOpRailList(rsOpRail);
    }

    /**
     * 新增铁路服务
     *
     * @param rsOpRail 铁路服务
     * @return 结果
     */
    @Override
    public int insertRsOpRail(RsOpRail rsOpRail) {
        return rsOpRailMapper.insertRsOpRail(rsOpRail);
    }

    /**
     * 修改铁路服务
     *
     * @param rsOpRail 铁路服务
     * @return 结果
     */
    @Override
    public int updateRsOpRail(RsOpRail rsOpRail) {
        return rsOpRailMapper.updateRsOpRail(rsOpRail);
    }

    /**
     * 修改铁路服务状态
     *
     * @param rsOpRail 铁路服务
     * @return 铁路服务
     */
    @Override
    public int changeStatus(RsOpRail rsOpRail) {
        return rsOpRailMapper.updateRsOpRail(rsOpRail);
    }

    /**
     * 批量删除铁路服务
     *
     * @param railIds 需要删除的铁路服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpRailByRailIds(Long[] railIds) {
        return rsOpRailMapper.deleteRsOpRailByRailIds(railIds);
    }

    /**
     * 删除铁路服务信息
     *
     * @param railId 铁路服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpRailByRailId(Long railId) {
        return rsOpRailMapper.deleteRsOpRailByRailId(railId);
    }
}
