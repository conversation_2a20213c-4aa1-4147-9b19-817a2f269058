package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsBankRecord;
import org.apache.ibatis.annotations.Mapper;

/**
 * 记录公司账户出入账明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-04-29
 */
@Mapper
public interface RsBankRecordMapper {
    /**
     * 查询记录公司账户出入账明细
     * 
     * @param bankRecordId 记录公司账户出入账明细主键
     * @return 记录公司账户出入账明细
     */
    RsBankRecord selectRsBankRecordByBankRecordId(Long bankRecordId);

    /**
     * 查询记录公司账户出入账明细列表
     * 
     * @param rsBankRecord 记录公司账户出入账明细
     * @return 记录公司账户出入账明细集合
     */
    List<RsBankRecord> selectRsBankRecordList(RsBankRecord rsBankRecord);

    /**
     * 新增记录公司账户出入账明细
     * 
     * @param rsBankRecord 记录公司账户出入账明细
     * @return 结果
     */
    int insertRsBankRecord(RsBankRecord rsBankRecord);

    /**
     * 修改记录公司账户出入账明细
     * 
     * @param rsBankRecord 记录公司账户出入账明细
     * @return 结果
     */
    int updateRsBankRecord(RsBankRecord rsBankRecord);

    /**
     * 删除记录公司账户出入账明细
     * 
     * @param bankRecordId 记录公司账户出入账明细主键
     * @return 结果
     */
    int deleteRsBankRecordByBankRecordId(Long bankRecordId);

    /**
     * 批量删除记录公司账户出入账明细
     * 
     * @param bankRecordIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsBankRecordByBankRecordIds(Long[] bankRecordIds);

    RsBankRecord selectRsBankRecordByReimburseId(RsBankRecord rsBankRecordDTO);

    List<RsBankRecord> getAccountFundStatistics(RsBankRecord rsBankRecord);
}
