package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsInventory;
import org.apache.ibatis.annotations.Mapper;

/**
 * 库存Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-08
 */
@Mapper
public interface RsInventoryMapper {
    /**
     * 查询库存
     *
     * @param inventoryId 库存主键
     * @return 库存
     */
    RsInventory selectRsInventoryByInventoryId(Long inventoryId);

    /**
     * 查询库存列表
     *
     * @param rsInventory 库存
     * @return 库存集合
     */
    List<RsInventory> selectRsInventoryList(RsInventory rsInventory);

    /**
     * 新增库存
     *
     * @param rsInventory 库存
     * @return 结果
     */
    int insertRsInventory(RsInventory rsInventory);

    /**
     * 修改库存
     *
     * @param rsInventory 库存
     * @return 结果
     */
    int updateRsInventory(RsInventory rsInventory);

    /**
     * 删除库存
     *
     * @param inventoryId 库存主键
     * @return 结果
     */
    int deleteRsInventoryByInventoryId(Long inventoryId);

    /**
     * 批量删除库存
     *
     * @param inventoryIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsInventoryByInventoryIds(Long[] inventoryIds);

    int getInboundNo();

    List<RsInventory> getPackages(RsInventory rsInventory);

    /**
     * 批量插入库存记录
     *
     * @param inventoryList 库存列表
     * @return 结果
     */
    int batchInsertInventory(List<RsInventory> inventoryList);

    void updateRsInventoryByPackageTo(RsInventory queryParam);
}
