package com.rich.common.core.domain.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 问题对象 bas_issue
 * 
 * <AUTHOR>
 * @date 2022-10-18
 */
public class BasIssue extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 问题 */
    private Long issueId;

    private String issueShortName;

    /** 中文名 */
    @Excel(name = "中文名")
    private String issueLocalName;

    /** 英文名 */
    @Excel(name = "英文名")
    private String issueEnName;

    /** 优先度 */
    @Excel(name = "优先度")
    private Integer orderNum;

    private String status;

    private String issueQuery;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getIssueQuery() {
        return issueQuery;
    }

    public void setIssueQuery(String issueQuery) {
        this.issueQuery = issueQuery;
    }

    public String getIssueShortName() {
        return issueShortName;
    }

    public void setIssueShortName(String issueShortName) {
        this.issueShortName = issueShortName;
    }

    public void setIssueId(Long issueId)
    {
        this.issueId = issueId;
    }

    public Long getIssueId() 
    {
        return issueId;
    }
    public void setIssueLocalName(String issueLocalName) 
    {
        this.issueLocalName = issueLocalName;
    }

    public String getIssueLocalName() 
    {
        return issueLocalName;
    }
    public void setIssueEnName(String issueEnName) 
    {
        this.issueEnName = issueEnName;
    }

    public String getIssueEnName() 
    {
        return issueEnName;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("issueId", getIssueId())
            .append("issueLocalName", getIssueLocalName())
            .append("issueEnName", getIssueEnName())
            .append("orderNum", getOrderNum())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("deleteBy", getDeleteBy())
            .append("deleteTime", getDeleteTime())
            .append("deleteStatus", getDeleteStatus())
            .toString();
    }
}
