package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsImportCustoms;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 进口清关Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@Mapper
public interface RsImportCustomsMapper {
    /**
     * 查询进口清关
     *
     * @param importCustomsId 进口清关主键
     * @return 进口清关
     */
    RsImportCustoms selectRsImportCustomsByImportCustomsId(Long importCustomsId);

    /**
     * 查询进口清关列表
     *
     * @param rsImportCustoms 进口清关
     * @return 进口清关集合
     */
    List<RsImportCustoms> selectRsImportCustomsList(RsImportCustoms rsImportCustoms);

    /**
     * 新增进口清关
     *
     * @param rsImportCustoms 进口清关
     * @return 结果
     */
    int insertRsImportCustoms(RsImportCustoms rsImportCustoms);

    /**
     * 修改进口清关
     *
     * @param rsImportCustoms 进口清关
     * @return 结果
     */
    int updateRsImportCustoms(RsImportCustoms rsImportCustoms);

    /**
     * 删除进口清关
     *
     * @param importCustomsId 进口清关主键
     * @return 结果
     */
    int deleteRsImportCustomsByImportCustomsId(Long importCustomsId);

    /**
     * 批量删除进口清关
     *
     * @param importCustomsIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsImportCustomsByImportCustomsIds(Long[] importCustomsIds);

    RsImportCustoms selectRsImportCustoms(Long serviceInstanceId);

    RsImportCustoms selectRsImportCustomsByServiceInstance(Long rctId);
}
