package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsOpImportCustomsClearance;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsOpImportCustomsClearanceService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 进口清关服务Controller
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@RestController
@RequestMapping("/system/opimportcustomsclearance")
public class RsOpImportCustomsClearanceController extends BaseController {
    @Autowired
    private RsOpImportCustomsClearanceService rsOpImportCustomsClearanceService;

    /**
     * 查询进口清关服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opimportcustomsclearance:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsOpImportCustomsClearance rsOpImportCustomsClearance) {
        startPage();
        List<RsOpImportCustomsClearance> list = rsOpImportCustomsClearanceService.selectRsOpImportCustomsClearanceList(rsOpImportCustomsClearance);
        return getDataTable(list);
    }

    /**
     * 导出进口清关服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opimportcustomsclearance:export')")
    @Log(title = "进口清关服务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsOpImportCustomsClearance rsOpImportCustomsClearance) {
        List<RsOpImportCustomsClearance> list = rsOpImportCustomsClearanceService.selectRsOpImportCustomsClearanceList(rsOpImportCustomsClearance);
        ExcelUtil<RsOpImportCustomsClearance> util = new ExcelUtil<RsOpImportCustomsClearance>(RsOpImportCustomsClearance.class);
        util.exportExcel(response, list, "进口清关服务数据");
    }

    /**
     * 获取进口清关服务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:opimportcustomsclearance:query')")
    @GetMapping(value = "/{importCustomsClearanceId}")
    public AjaxResult getInfo(@PathVariable("importCustomsClearanceId") Long importCustomsClearanceId) {
        return AjaxResult.success(rsOpImportCustomsClearanceService.selectRsOpImportCustomsClearanceByImportCustomsClearanceId(importCustomsClearanceId));
    }

    /**
     * 新增进口清关服务
     */
    @PreAuthorize("@ss.hasPermi('system:opimportcustomsclearance:add')")
    @Log(title = "进口清关服务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsOpImportCustomsClearance rsOpImportCustomsClearance) {
        return toAjax(rsOpImportCustomsClearanceService.insertRsOpImportCustomsClearance(rsOpImportCustomsClearance));
    }

    /**
     * 修改进口清关服务
     */
    @PreAuthorize("@ss.hasPermi('system:opimportcustomsclearance:edit')")
    @Log(title = "进口清关服务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsOpImportCustomsClearance rsOpImportCustomsClearance) {
        return toAjax(rsOpImportCustomsClearanceService.updateRsOpImportCustomsClearance(rsOpImportCustomsClearance));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:opimportcustomsclearance:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsOpImportCustomsClearance rsOpImportCustomsClearance) {
        rsOpImportCustomsClearance.setUpdateBy(getUserId());
        return toAjax(rsOpImportCustomsClearanceService.changeStatus(rsOpImportCustomsClearance));
    }

    /**
     * 删除进口清关服务
     */
    @PreAuthorize("@ss.hasPermi('system:opimportcustomsclearance:remove')")
    @Log(title = "进口清关服务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{importCustomsClearanceIds}")
    public AjaxResult remove(@PathVariable Long[] importCustomsClearanceIds) {
        return toAjax(rsOpImportCustomsClearanceService.deleteRsOpImportCustomsClearanceByImportCustomsClearanceIds(importCustomsClearanceIds));
    }
}
