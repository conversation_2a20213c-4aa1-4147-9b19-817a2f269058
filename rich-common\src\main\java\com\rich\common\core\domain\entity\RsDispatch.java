package com.rich.common.core.domain.entity;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 尾程运输对象 rs_dispatch
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
public class RsDispatch extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long dispatchId;

    /**
     * 所属服务实例id ,
     */
    @Excel(name = "所属服务实例id ,")
    private Long serviceId;

    /**
     * 所属服务类型id ,
     */
    @Excel(name = "所属服务类型id ,")
    private Long sqdServiceTypeId;

    /**
     * 所属操作单号 ,
     */
    @Excel(name = "所属操作单号 ,")
    private String sqdRctNo;

    /**
     * 派送司机姓名 ,
     */
    @Excel(name = "派送司机姓名 ,")
    private String dispatchDriverName;

    /**
     * 派送司机电话 ,
     */
    @Excel(name = "派送司机电话 ,")
    private String dispatchDriverTel;

    /**
     * 派送司机车牌 ,
     */
    @Excel(name = "派送司机车牌 ,")
    private String dispatchTruckNo;

    /**
     * 派送司机备注 ,
     */
    @Excel(name = "派送司机备注 ,")
    private String dispatchTruckRemark;

    /**
     * 派送须知 ,
     */
    @Excel(name = "派送须知 ,")
    private String precarriageNote;

    /**
     * 派送备注 ,
     */
    @Excel(name = "派送备注 ,")
    private String precarriageRemark;

    public Long getDispatchId() {
        return dispatchId;
    }

    public void setDispatchId(Long dispatchId) {
        this.dispatchId = dispatchId;
    }

    public Long getServiceId() {
        return serviceId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    public Long getSqdServiceTypeId() {
        return sqdServiceTypeId;
    }

    public void setSqdServiceTypeId(Long sqdServiceTypeId) {
        this.sqdServiceTypeId = sqdServiceTypeId;
    }

    public String getSqdRctNo() {
        return sqdRctNo;
    }

    public void setSqdRctNo(String sqdRctNo) {
        this.sqdRctNo = sqdRctNo;
    }

    public String getDispatchDriverName() {
        return dispatchDriverName;
    }

    public void setDispatchDriverName(String dispatchDriverName) {
        this.dispatchDriverName = dispatchDriverName;
    }

    public String getDispatchDriverTel() {
        return dispatchDriverTel;
    }

    public void setDispatchDriverTel(String dispatchDriverTel) {
        this.dispatchDriverTel = dispatchDriverTel;
    }

    public String getDispatchTruckNo() {
        return dispatchTruckNo;
    }

    public void setDispatchTruckNo(String dispatchTruckNo) {
        this.dispatchTruckNo = dispatchTruckNo;
    }

    public String getDispatchTruckRemark() {
        return dispatchTruckRemark;
    }

    public void setDispatchTruckRemark(String dispatchTruckRemark) {
        this.dispatchTruckRemark = dispatchTruckRemark;
    }

    public String getPrecarriageNote() {
        return precarriageNote;
    }

    public void setPrecarriageNote(String precarriageNote) {
        this.precarriageNote = precarriageNote;
    }

    public String getPrecarriageRemark() {
        return precarriageRemark;
    }

    public void setPrecarriageRemark(String precarriageRemark) {
        this.precarriageRemark = precarriageRemark;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("dispatchId", getDispatchId())
                .append("serviceId", getServiceId())
                .append("sqdServiceTypeId", getSqdServiceTypeId())
                .append("sqdRctNo", getSqdRctNo())
                .append("dispatchDriverName", getDispatchDriverName())
                .append("dispatchDriverTel", getDispatchDriverTel())
                .append("dispatchTruckNo", getDispatchTruckNo())
                .append("dispatchTruckRemark", getDispatchTruckRemark())
                .append("precarriageNote", getPrecarriageNote())
                .append("precarriageRemark", getPrecarriageRemark())
                .toString();
    }
}
