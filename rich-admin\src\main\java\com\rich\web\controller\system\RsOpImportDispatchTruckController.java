package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsOpImportDispatchTruck;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsOpImportDispatchTruckService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 进口派送服务Controller
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@RestController
@RequestMapping("/system/opimportdispatchtruck")
public class RsOpImportDispatchTruckController extends BaseController {
    @Autowired
    private RsOpImportDispatchTruckService rsOpImportDispatchTruckService;

    /**
     * 查询进口派送服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opimportdispatchtruck:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsOpImportDispatchTruck rsOpImportDispatchTruck) {
        startPage();
        List<RsOpImportDispatchTruck> list = rsOpImportDispatchTruckService.selectRsOpImportDispatchTruckList(rsOpImportDispatchTruck);
        return getDataTable(list);
    }

    /**
     * 导出进口派送服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opimportdispatchtruck:export')")
    @Log(title = "进口派送服务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsOpImportDispatchTruck rsOpImportDispatchTruck) {
        List<RsOpImportDispatchTruck> list = rsOpImportDispatchTruckService.selectRsOpImportDispatchTruckList(rsOpImportDispatchTruck);
        ExcelUtil<RsOpImportDispatchTruck> util = new ExcelUtil<RsOpImportDispatchTruck>(RsOpImportDispatchTruck.class);
        util.exportExcel(response, list, "进口派送服务数据");
    }

    /**
     * 获取进口派送服务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:opimportdispatchtruck:query')")
    @GetMapping(value = "/{importDispatchTruckId}")
    public AjaxResult getInfo(@PathVariable("importDispatchTruckId") Long importDispatchTruckId) {
        return AjaxResult.success(rsOpImportDispatchTruckService.selectRsOpImportDispatchTruckByImportDispatchTruckId(importDispatchTruckId));
    }

    /**
     * 新增进口派送服务
     */
    @PreAuthorize("@ss.hasPermi('system:opimportdispatchtruck:add')")
    @Log(title = "进口派送服务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsOpImportDispatchTruck rsOpImportDispatchTruck) {
        return toAjax(rsOpImportDispatchTruckService.insertRsOpImportDispatchTruck(rsOpImportDispatchTruck));
    }

    /**
     * 修改进口派送服务
     */
    @PreAuthorize("@ss.hasPermi('system:opimportdispatchtruck:edit')")
    @Log(title = "进口派送服务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsOpImportDispatchTruck rsOpImportDispatchTruck) {
        return toAjax(rsOpImportDispatchTruckService.updateRsOpImportDispatchTruck(rsOpImportDispatchTruck));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:opimportdispatchtruck:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsOpImportDispatchTruck rsOpImportDispatchTruck) {
        rsOpImportDispatchTruck.setUpdateBy(getUserId());
        return toAjax(rsOpImportDispatchTruckService.changeStatus(rsOpImportDispatchTruck));
    }

    /**
     * 删除进口派送服务
     */
    @PreAuthorize("@ss.hasPermi('system:opimportdispatchtruck:remove')")
    @Log(title = "进口派送服务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{importDispatchTruckIds}")
    public AjaxResult remove(@PathVariable Long[] importDispatchTruckIds) {
        return toAjax(rsOpImportDispatchTruckService.deleteRsOpImportDispatchTruckByImportDispatchTruckIds(importDispatchTruckIds));
    }
}
