package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 权限类型对象 sys_perms_type
 *
 * <AUTHOR>
 * @date 2023-03-21
 */
public class SysPermsType extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long permsId;

    private Integer permsType;


    private String permsDetail;

    public void setPermsId(Long permsId) {
        this.permsId = permsId;
    }

    public Long getPermsId() {
        return permsId;
    }

    public void setPermsType(Integer permsType) {
        this.permsType = permsType;
    }

    public Integer getPermsType() {
        return permsType;
    }

    public void setPermsDetail(String permsDetail) {
        this.permsDetail = permsDetail;
    }

    public String getPermsDetail() {
        return permsDetail;
    }

}
