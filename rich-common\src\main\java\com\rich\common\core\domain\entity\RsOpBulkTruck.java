package com.rich.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 散货拖车
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
public class RsOpBulkTruck extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long truckId;

    /**
     * 所属服务实例id ,
     */
    @Excel(name = "所属服务实例id ,")
    private Long serviceId;

    /**
     * 所属服务类型id ,
     */
    @Excel(name = "所属服务类型id ,")
    private Long sqdServiceTypeId;

    /**
     * 所属操作单号 ,
     */
    @Excel(name = "所属操作单号 ,")
    private String sqdRctNo;

    /**
     * 提货司机姓名 ,
     */
    @Excel(name = "提货司机姓名 ,")
    private String precarriageDriverName;

    /**
     * 提货司机电话 ,
     */
    @Excel(name = "提货司机电话 ,")
    private String precarriageDriverTel;

    /**
     * 提货司机车牌 ,
     */
    @Excel(name = "提货司机车牌 ,")
    private String precarriageTruckNo;

    /**
     * 提货司机备注 ,
     */
    @Excel(name = "提货司机备注 ,")
    private String precarriageTruckRemark;

    /**
     * 柜号 ,
     */
    @Excel(name = "柜号 ,")
    private String containerNo;

    /**
     * 柜型 ,
     */
    @Excel(name = "柜型 ,")
    private Long containerTypeId;

    /**
     * 封条 ,
     */
    @Excel(name = "封条 ,")
    private String sealNo;

    /**
     * 磅单 ,
     */
    @Excel(name = "磅单 ,")
    private String weightPaper;

    /**
     * 提货须知 ,
     */
    @Excel(name = "提货须知 ,")
    private String precarriageNote;

    /**
     * 提货备注 ,
     */
    @Excel(name = "提货备注 ,")
    private String precarriageRemark;

    private RsServiceInstances rsServiceInstances;

    private List<RsCharge> rsChargeList;

    private List<RsDoc> rsDocList;

    private List<RsOpLog> rsOpLogList;

    private List<RsOpTruckList> rsOpTruckList;
    private String containerTypeCode;
    private String precarriageSupplierNo;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date precarriageTime;
    private Long precarriageRegionId;
    private String precarriageAddress;
    private String precarriageContact;
    private String precarriageTel;
    private BigDecimal payable;
    private Long rctId;
    private BigDecimal payableRMB;
    private BigDecimal payableUSD;
    private BigDecimal payableUSDTax;
    private BigDecimal payableRMBTax;

    public BigDecimal getPayableRMBTax() {
        return payableRMBTax;
    }

    public void setPayableRMBTax(BigDecimal payableRMBTax) {
        this.payableRMBTax = payableRMBTax;
    }

    public BigDecimal getPayableUSDTax() {
        return payableUSDTax;
    }

    public void setPayableUSDTax(BigDecimal payableUSDTax) {
        this.payableUSDTax = payableUSDTax;
    }

    public BigDecimal getPayableUSD() {
        return payableUSD;
    }

    public void setPayableUSD(BigDecimal payableUSD) {
        this.payableUSD = payableUSD;
    }

    public BigDecimal getPayableRMB() {
        return payableRMB;
    }

    public void setPayableRMB(BigDecimal payableRMB) {
        this.payableRMB = payableRMB;
    }

    public Long getRctId() {
        return rctId;
    }

    public void setRctId(Long rctId) {
        this.rctId = rctId;
    }

    public BigDecimal getPayable() {
        return payable;
    }

    public void setPayable(BigDecimal payable) {
        this.payable = payable;
    }


    public List<RsOpTruckList> getRsOpTruckList() {
        return rsOpTruckList;
    }

    public void setRsOpTruckList(List<RsOpTruckList> rsOpTruckList) {
        this.rsOpTruckList = rsOpTruckList;
    }

    public String getPrecarriageTel() {
        return precarriageTel;
    }

    public void setPrecarriageTel(String precarriageTel) {
        this.precarriageTel = precarriageTel;
    }

    public String getPrecarriageContact() {
        return precarriageContact;
    }

    public void setPrecarriageContact(String precarriageContact) {
        this.precarriageContact = precarriageContact;
    }

    public String getPrecarriageAddress() {
        return precarriageAddress;
    }

    public void setPrecarriageAddress(String precarriageAddress) {
        this.precarriageAddress = precarriageAddress;
    }

    public Long getPrecarriageRegionId() {
        return precarriageRegionId;
    }

    public void setPrecarriageRegionId(Long precarriageRegionId) {
        this.precarriageRegionId = precarriageRegionId;
    }

    public Date getPrecarriageTime() {
        return precarriageTime;
    }

    public void setPrecarriageTime(Date precarriageTime) {
        this.precarriageTime = precarriageTime;
    }

    public String getPrecarriageSupplierNo() {
        return precarriageSupplierNo;
    }

    public void setPrecarriageSupplierNo(String precarriageSupplierNo) {
        this.precarriageSupplierNo = precarriageSupplierNo;
    }

    public String getContainerTypeCode() {
        return containerTypeCode;
    }

    public void setContainerTypeCode(String containerTypeCode) {
        this.containerTypeCode = containerTypeCode;
    }

    private String sqdServiceDetailsCode;

    public String getSqdServiceDetailsCode() {
        return sqdServiceDetailsCode;
    }

    public void setSqdServiceDetailsCode(String sqdServiceDetailsCode) {
        this.sqdServiceDetailsCode = sqdServiceDetailsCode;
    }

    public List<RsOpLog> getRsOpLogList() {
        return rsOpLogList;
    }

    public void setRsOpLogList(List<RsOpLog> rsOpLogList) {
        this.rsOpLogList = rsOpLogList;
    }

    public RsServiceInstances getRsServiceInstances() {
        return rsServiceInstances;
    }

    public void setRsServiceInstances(RsServiceInstances rsServiceInstances) {
        this.rsServiceInstances = rsServiceInstances;
    }

    public List<RsCharge> getRsChargeList() {
        return rsChargeList;
    }

    public void setRsChargeList(List<RsCharge> rsChargeList) {
        this.rsChargeList = rsChargeList;
    }

    public List<RsDoc> getRsDocList() {
        return rsDocList;
    }

    public void setRsDocList(List<RsDoc> rsDocList) {
        this.rsDocList = rsDocList;
    }

    public Long getTruckId() {
        return truckId;
    }

    public void setTruckId(Long truckId) {
        this.truckId = truckId;
    }

    public Long getServiceId() {
        return serviceId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    public Long getSqdServiceTypeId() {
        return sqdServiceTypeId;
    }

    public void setSqdServiceTypeId(Long sqdServiceTypeId) {
        this.sqdServiceTypeId = sqdServiceTypeId;
    }

    public String getSqdRctNo() {
        return sqdRctNo;
    }

    public void setSqdRctNo(String sqdRctNo) {
        this.sqdRctNo = sqdRctNo;
    }

    public String getPrecarriageDriverName() {
        return precarriageDriverName;
    }

    public void setPrecarriageDriverName(String precarriageDriverName) {
        this.precarriageDriverName = precarriageDriverName;
    }

    public String getPrecarriageDriverTel() {
        return precarriageDriverTel;
    }

    public void setPrecarriageDriverTel(String precarriageDriverTel) {
        this.precarriageDriverTel = precarriageDriverTel;
    }

    public String getPrecarriageTruckNo() {
        return precarriageTruckNo;
    }

    public void setPrecarriageTruckNo(String precarriageTruckNo) {
        this.precarriageTruckNo = precarriageTruckNo;
    }

    public String getPrecarriageTruckRemark() {
        return precarriageTruckRemark;
    }

    public void setPrecarriageTruckRemark(String precarriageTruckRemark) {
        this.precarriageTruckRemark = precarriageTruckRemark;
    }

    public String getContainerNo() {
        return containerNo;
    }

    public void setContainerNo(String containerNo) {
        this.containerNo = containerNo;
    }

    public Long getContainerTypeId() {
        return containerTypeId;
    }

    public void setContainerTypeId(Long containerTypeId) {
        this.containerTypeId = containerTypeId;
    }

    public String getSealNo() {
        return sealNo;
    }

    public void setSealNo(String sealNo) {
        this.sealNo = sealNo;
    }

    public String getWeightPaper() {
        return weightPaper;
    }

    public void setWeightPaper(String weightPaper) {
        this.weightPaper = weightPaper;
    }

    public String getPrecarriageNote() {
        return precarriageNote;
    }

    public void setPrecarriageNote(String precarriageNote) {
        this.precarriageNote = precarriageNote;
    }

    public String getPrecarriageRemark() {
        return precarriageRemark;
    }

    public void setPrecarriageRemark(String precarriageRemark) {
        this.precarriageRemark = precarriageRemark;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("exportTruckId", getTruckId())
                .append("serviceId", getServiceId())
                .append("sqdServiceTypeId", getSqdServiceTypeId())
                .append("sqdRctNo", getSqdRctNo())
                .append("precarriageDriverName", getPrecarriageDriverName())
                .append("precarriageDriverTel", getPrecarriageDriverTel())
                .append("precarriageTruckNo", getPrecarriageTruckNo())
                .append("precarriageTruckRemark", getPrecarriageTruckRemark())
                .append("containerNo", getContainerNo())
                .append("containerTypeId", getContainerTypeId())
                .append("sealNo", getSealNo())
                .append("weightPaper", getWeightPaper())
                .append("precarriageNote", getPrecarriageNote())
                .append("precarriageRemark", getPrecarriageRemark())
                .toString();
    }
}
