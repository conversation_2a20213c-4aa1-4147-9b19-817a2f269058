package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 【请填写功能名称】对象 bas_company_role
 *
 * <AUTHOR>
 * @date 2022-08-29
 */
public class BasCompanyRole extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 公司角色英文名。不区别是航司还是船东，直接写carrier，然后由它经营的运输类型综合判断是航司还是船东（多选）
     * 是否为客户还是供应商，在公司表里定义。此处仅仅描述一个公司在物流视角下所从事的本职业务。
     */
    private Long roleId;

    private String roleShortName;

    /**
     * 母语名称
     */
    @Excel(name = "母语名称")
    private String roleLocalName;

    /**
     * 英文名
     */
    @Excel(name = "英文名")
    private String roleEnName;

    /**
     * 横向优先级sort
     */
    @Excel(name = "横向优先级sort")
    private Integer orderNum;

    private String deleteBy;

    private Date deleteTime;

    private Integer deleteStatus;

    private String status;

    private String roleQuery;

    public String getRoleQuery() {
        return roleQuery;
    }

    public void setRoleQuery(String roleQuery) {
        this.roleQuery = roleQuery;
    }

    public String getRoleShortName() {
        return roleShortName;
    }

    public void setRoleShortName(String roleShortName) {
        this.roleShortName = roleShortName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleLocalName(String roleLocalName) {
        this.roleLocalName = roleLocalName;
    }

    public String getRoleLocalName() {
        return roleLocalName;
    }

    public void setRoleEnName(String roleEnName) {
        this.roleEnName = roleEnName;
    }

    public String getRoleEnName() {
        return roleEnName;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("roleId", getRoleId())
                .append("roleLocalName", getRoleLocalName())
                .append("roleEnName", getRoleEnName())
                .append("orderNum", getOrderNum())
                .append("remark", getRemark())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("deleteBy", getDeleteBy())
                .append("deleteTime", getDeleteTime())
                .append("deleteStatus", getDeleteStatus())
                .toString();
    }
}
