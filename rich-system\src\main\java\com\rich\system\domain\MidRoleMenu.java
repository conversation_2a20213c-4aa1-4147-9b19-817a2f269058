package com.rich.system.domain;

/**
 * @TableName mid_role_menu
 */
public class MidRoleMenu {
    private static final long serialVersionUID = 1L;
    /**
     * 权限
     */
    private Long roleId;
    /**
     * 菜单
     */
    private Long menuId;
    private Long distributeId;
    private Integer open;

    public Integer getOpen() {
        return open;
    }

    public void setOpen(Integer open) {
        this.open = open;
    }

    public Long getDistributeId() {
        return distributeId;
    }

    public void setDistributeId(Long distributeId) {
        this.distributeId = distributeId;
    }

    /**
     * 权限
     */
    public Long getRoleId() {
        return roleId;
    }

    /**
     * 权限
     */
    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    /**
     * 菜单
     */
    public Long getMenuId() {
        return menuId;
    }

    /**
     * 菜单
     */
    public void setMenuId(Long menuId) {
        this.menuId = menuId;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MidRoleMenu other = (MidRoleMenu) that;
        return (this.getRoleId() == null ? other.getRoleId() == null : this.getRoleId().equals(other.getRoleId()))
                && (this.getMenuId() == null ? other.getMenuId() == null : this.getMenuId().equals(other.getMenuId()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getRoleId() == null) ? 0 : getRoleId().hashCode());
        result = prime * result + ((getMenuId() == null) ? 0 : getMenuId().hashCode());
        return result;
    }

    @Override
    public String toString() {
        String sb = getClass().getSimpleName() +
                " [" +
                "Hash = " + hashCode() +
                ", roleId=" + roleId +
                ", menuId=" + menuId +
                ", serialVersionUID=" + serialVersionUID +
                "]";
        return sb;
    }
}