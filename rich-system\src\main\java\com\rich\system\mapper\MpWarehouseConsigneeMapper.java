package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.MpWarehouseConsignee;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 收货人信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Mapper
public interface MpWarehouseConsigneeMapper {
    /**
     * 查询收货人信息
     *
     * @param consigneeId 收货人信息主键
     * @return 收货人信息
     */
    MpWarehouseConsignee selectMpWarehouseConsigneeByConsigneeId(Long consigneeId);

    /**
     * 查询收货人信息列表
     *
     * @param mpWarehouseConsignee 收货人信息
     * @return 收货人信息集合
     */
    List<MpWarehouseConsignee> selectMpWarehouseConsigneeList(MpWarehouseConsignee mpWarehouseConsignee);

    /**
     * 新增收货人信息
     *
     * @param mpWarehouseConsignee 收货人信息
     * @return 结果
     */
    int insertMpWarehouseConsignee(MpWarehouseConsignee mpWarehouseConsignee);

    /**
     * 修改收货人信息
     *
     * @param mpWarehouseConsignee 收货人信息
     * @return 结果
     */
    int updateMpWarehouseConsignee(MpWarehouseConsignee mpWarehouseConsignee);

    /**
     * 删除收货人信息
     *
     * @param consigneeId 收货人信息主键
     * @return 结果
     */
    int deleteMpWarehouseConsigneeByConsigneeId(Long consigneeId);

    /**
     * 批量删除收货人信息
     *
     * @param consigneeIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteMpWarehouseConsigneeByConsigneeIds(Long[] consigneeIds);

    MpWarehouseConsignee selectMpWarehouseConsigneeByuserId(Long userId);

    /**
     * 根据收货人代码查询收货人信息
     *
     * @param consigneeCode 收货人代码
     * @return 收货人信息
     */
    MpWarehouseConsignee selectMpWarehouseConsigneeByConsigneeCode(String consigneeCode);

    MpWarehouseConsignee selectMpWarehouseConsigneeByConsigneeCodeAndClientCode(@Param("consigneeCode") String consigneeCode, @Param("clientCode") String clientCode);
}
