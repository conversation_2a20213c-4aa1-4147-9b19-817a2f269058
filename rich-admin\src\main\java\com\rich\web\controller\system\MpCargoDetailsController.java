package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.MpCargoDetails;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.MpCargoDetailsService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 客户在仓货物明细Controller
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@RestController
@RequestMapping("/mp/cargodetails")
public class MpCargoDetailsController extends BaseController {
    @Autowired
    private MpCargoDetailsService mpCargoDetailsService;

    /**
     * 查询客户在仓货物明细列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MpCargoDetails mpCargoDetails) {
        startPage();
        List<MpCargoDetails> list = mpCargoDetailsService.selectMpCargoDetailsList(mpCargoDetails);
        return getDataTable(list);
    }

    /**
     * 导出客户在仓货物明细列表
     */
    @Log(title = "客户在仓货物明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MpCargoDetails mpCargoDetails) {
        List<MpCargoDetails> list = mpCargoDetailsService.selectMpCargoDetailsList(mpCargoDetails);
        ExcelUtil<MpCargoDetails> util = new ExcelUtil<MpCargoDetails>(MpCargoDetails.class);
        util.exportExcel(response, list, "客户在仓货物明细数据");
    }

    /**
     * 获取客户在仓货物明细详细信息
     */
    @GetMapping(value = "/{cargoDetailsId}")
    public AjaxResult getInfo(@PathVariable("cargoDetailsId") Long cargoDetailsId) {
        return AjaxResult.success(mpCargoDetailsService.selectMpCargoDetailsByCargoDetailsId(cargoDetailsId));
    }

    /**
     * 新增客户在仓货物明细
     */
    @Log(title = "客户在仓货物明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MpCargoDetails mpCargoDetails) {
        return toAjax(mpCargoDetailsService.insertMpCargoDetails(mpCargoDetails));
    }

    /**
     * 修改客户在仓货物明细
     */
    @Log(title = "客户在仓货物明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MpCargoDetails mpCargoDetails) {
        return toAjax(mpCargoDetailsService.updateMpCargoDetails(mpCargoDetails));
    }

    /**
     * 状态状态
     */
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody MpCargoDetails mpCargoDetails) {
        mpCargoDetails.setUpdateBy(getUserId());
        return toAjax(mpCargoDetailsService.changeStatus(mpCargoDetails));
    }

    /**
     * 删除客户在仓货物明细
     */
    @Log(title = "客户在仓货物明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{cargoDetailsIds}")
    public AjaxResult remove(@PathVariable Long[] cargoDetailsIds) {
        return toAjax(mpCargoDetailsService.deleteMpCargoDetailsByCargoDetailsIds(cargoDetailsIds));
    }
}
