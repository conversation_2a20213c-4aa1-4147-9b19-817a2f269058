package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.RsCharacteristics;
import com.rich.common.core.domain.entity.RsFreight;
import com.rich.common.core.domain.entity.RsQuotation;
import com.rich.common.core.page.PageDomain;
import com.rich.common.core.page.TableSupport;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 报价列表Controller
 *
 * <AUTHOR>
 * @date 2023-05-04
 */
@RestController
@RequestMapping("/system/quotation")
public class RsQuotationController extends BaseController {
    @Autowired
    private RsQuotationService rsQuotationService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private BasDistLocationService basDistLocationService;

    @Autowired
    private ExtCompanyService extCompanyService;

    @Autowired
    private RsFreightService rsFreightService;

    @Autowired
    private RsCharacteristicsService rsCharacteristicsService;

    /**
     * 查询运费
     * 手动分页
     *
     * @param rsQuotation
     * @return
     */
    @PostMapping("/queryFreight")
    public AjaxResult queryFreight(@RequestBody RsQuotation rsQuotation) {
        AjaxResult ajaxResult = AjaxResult.success();
        List<RsFreight> list = rsQuotationService.queryFreight(rsQuotation);
        if (list != null) {
            ajaxResult.put("rows", list.stream().skip((rsQuotation.getPageNum() - 1) * rsQuotation.getPageSize()).limit(rsQuotation.getPageSize()).collect(Collectors.toList()));
            ajaxResult.put("total", list.size());
        }
        return ajaxResult;
    }

    /**
     * 查询附加费
     *
     * @param rsFreight
     * @return
     */
    @PostMapping("/queryLocal")
    public AjaxResult queryLocal(@RequestBody RsFreight rsFreight) {
        if (rsFreight.isQuery()) {
            rsFreight = rsFreightService.selectRsFreightByFreightId(rsFreight.getFreightId());
            rsFreight.setCargoTypeIds(rsFreightService.selectCargoTypes(rsFreight.getFreightId()).toArray(new Long[0]));
        }
        return AjaxResult.success(rsQuotationService.queryLocal(rsFreight));
    }

    /**
     * 查询物流特征
     * 注意事项
     *
     * @param rsCharacteristics
     * @return
     */
    @PostMapping("/queryCharacteristics")
    public AjaxResult queryCharacteristics(@RequestBody RsCharacteristics rsCharacteristics) {
        AjaxResult ajaxResult = AjaxResult.success();
        List<RsCharacteristics> list = rsQuotationService.queryCharacteristics(rsCharacteristics);
        ajaxResult.put("rows", list.stream().skip((rsCharacteristics.getPageNum() - 1) * rsCharacteristics.getPageSize()).limit(rsCharacteristics.getPageSize()).collect(Collectors.toList()));
        ajaxResult.put("total", list.size());
        return ajaxResult;
    }

    /**
     * 查询报价列表列表
     */
    @PreAuthorize("@ss.hasPermi('system:quotation:list')")
    @GetMapping("/list")
    public AjaxResult list(RsQuotation rsQuotation) {
        AjaxResult ajaxResult = AjaxResult.success();
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        rsQuotation.setPageSize(0L);
        if (SecurityUtils.getDeptId().equals(102L) || SecurityUtils.isAdmin(SecurityUtils.getLoginUser().getUser().getRole())) {
            rsQuotation.setPermissionLevel(null);
        }
        List<RsQuotation> list = rsQuotationService.selectRsQuotationList(rsQuotation);
        if (list == null) {
            return null;
        }
        ajaxResult.put("rows", list.stream().skip((long) (pageNum - 1) * pageSize).limit(pageSize).collect(Collectors.toList()));
        ajaxResult.put("total", list.size());
        return ajaxResult;
    }

    /**
     * 导出报价列表列表
     */
    @PreAuthorize("@ss.hasPermi('system:quotation:export')")
    @Log(title = "报价列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsQuotation rsQuotation) {
        List<RsQuotation> list = rsQuotationService.selectRsQuotationList(rsQuotation);
        ExcelUtil<RsQuotation> util = new ExcelUtil<RsQuotation>(RsQuotation.class);
        util.exportExcel(response, list, "报价列表数据");
    }

    /**
     * 获取报价列表详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:quotation:query')")
    @GetMapping(value = "/{quotationId}")
    public AjaxResult getInfo(@PathVariable("quotationId") Long quotationId) {
        RsQuotation quotation = rsQuotationService.selectRsQuotationByQuotationId(quotationId);
        List<Long> locationLoadingIds = rsQuotationService.selectLocationLoading(quotationId);
        AjaxResult ajaxResult = AjaxResult.success();
        Set<Long> set = new HashSet<>();
        if (!locationLoadingIds.isEmpty()) {
            set.addAll(locationLoadingIds);
        }
        if (quotation.getDepartureId() != null) {
            set.add(quotation.getDepartureId());
        }
        if (quotation.getDestinationId() != null) {
            set.add(quotation.getDestinationId());
        }
        quotation.setLoadingIds(locationLoadingIds.toArray(new Long[0]));
        ajaxResult.put(AjaxResult.DATA_TAG, quotation);
        ajaxResult.put("quotationFreight", rsQuotationService.selectQuotationFreightList(quotationId));
        ajaxResult.put("quotationCharacteristics", rsQuotationService.selectQuotationCharacteristicsList(quotationId));
        ajaxResult.put("characteristics", rsQuotationService.selectCharacteristicsList(quotationId));
        ajaxResult.put("midRevenueTonsList", rsQuotationService.selectMidRevenueTonsList(quotationId));
        ajaxResult.put("serviceTypeIds", rsQuotationService.selectServiceTypes(quotationId));
        ajaxResult.put("carrierIds", rsQuotationService.selectCarriers(quotationId));
        ajaxResult.put("cargoTypeIds", rsQuotationService.selectCargoTypes(quotationId));
        ajaxResult.put("cargoTypeCodeSum", rsQuotationService.selectCargoTypeCodeSum(quotationId));
        ajaxResult.put("company", extCompanyService.selectExtCompanyByCompanyId(quotation.getCompanyId()));
        ajaxResult.put("roleIds", extCompanyService.selectCompanyRoles(quotation.getCompanyId()));
        ajaxResult.put("locationLoadingIds", locationLoadingIds);
        ajaxResult.put("locationOptions", !set.isEmpty() ? basDistLocationService.selectBasDistLocationByIds(set) : null);
        return ajaxResult;
    }

    /**
     * 新增或更新报价列表
     */
    @PreAuthorize("@ss.hasPermi('system:quotation:add')")
    @Log(title = "报价列表", businessType = BusinessType.INSERT)
    @PostMapping("/quotationFreight")
    public AjaxResult quotationFreight(@RequestBody RsQuotation rsQuotation) {
        return AjaxResult.success(rsQuotationService.updateQuotationFreight(rsQuotation));
    }

    /**
     * 新增报价列表
     */
    @PreAuthorize("@ss.hasPermi('system:quotation:add')")
    @Log(title = "报价列表", businessType = BusinessType.INSERT)
    @PostMapping()
    public AjaxResult add(@RequestBody RsQuotation rsQuotation) {
        return toAjax(rsQuotationService.insertRsQuotation(rsQuotation));
    }

    /**
     * 修改报价列表
     */
    @PreAuthorize("@ss.hasPermi('system:quotation:edit')")
    @Log(title = "报价列表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsQuotation rsQuotation) {
        return toAjax(rsQuotationService.updateRsQuotation(rsQuotation));
    }

    @PreAuthorize("@ss.hasPermi('system:quotation:edit')")
    @Log(title = "报价列表", businessType = BusinessType.UPDATE)
    @PutMapping("/remark")
    public AjaxResult editRemark(@RequestBody RsQuotation rsQuotation) {
        return toAjax(rsQuotationService.updateRsQuotationRemark(rsQuotation));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:quotation:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsQuotation rsQuotation) {
        rsQuotation.setUpdateBy(getUserId());
        return toAjax(rsQuotationService.changeStatus(rsQuotation));
    }

    /**
     * 删除报价列表
     */
    @PreAuthorize("@ss.hasPermi('system:quotation:remove')")
    @Log(title = "报价列表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{quotationIds}")
    public AjaxResult remove(@PathVariable Long[] quotationIds) {
        return toAjax(rsQuotationService.deleteRsQuotationByQuotationIds(quotationIds));
    }
}
