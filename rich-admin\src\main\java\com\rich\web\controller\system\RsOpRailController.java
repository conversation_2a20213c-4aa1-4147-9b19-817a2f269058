package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsOpRail;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsOpRailService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 铁路服务Controller
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@RestController
@RequestMapping("/system/oprail")
public class RsOpRailController extends BaseController {
    @Autowired
    private RsOpRailService rsOpRailService;

    /**
     * 查询铁路服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:oprail:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsOpRail rsOpRail) {
        startPage();
        List<RsOpRail> list = rsOpRailService.selectRsOpRailList(rsOpRail);
        return getDataTable(list);
    }

    /**
     * 导出铁路服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:oprail:export')")
    @Log(title = "铁路服务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsOpRail rsOpRail) {
        List<RsOpRail> list = rsOpRailService.selectRsOpRailList(rsOpRail);
        ExcelUtil<RsOpRail> util = new ExcelUtil<RsOpRail>(RsOpRail.class);
        util.exportExcel(response, list, "铁路服务数据");
    }

    /**
     * 获取铁路服务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:oprail:query')")
    @GetMapping(value = "/{railId}")
    public AjaxResult getInfo(@PathVariable("railId") Long railId) {
        return AjaxResult.success(rsOpRailService.selectRsOpRailByRailId(railId));
    }

    /**
     * 新增铁路服务
     */
    @PreAuthorize("@ss.hasPermi('system:oprail:add')")
    @Log(title = "铁路服务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsOpRail rsOpRail) {
        return toAjax(rsOpRailService.insertRsOpRail(rsOpRail));
    }

    /**
     * 修改铁路服务
     */
    @PreAuthorize("@ss.hasPermi('system:oprail:edit')")
    @Log(title = "铁路服务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsOpRail rsOpRail) {
        return toAjax(rsOpRailService.updateRsOpRail(rsOpRail));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:oprail:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsOpRail rsOpRail) {
        rsOpRail.setUpdateBy(getUserId());
        return toAjax(rsOpRailService.changeStatus(rsOpRail));
    }

    /**
     * 删除铁路服务
     */
    @PreAuthorize("@ss.hasPermi('system:oprail:remove')")
    @Log(title = "铁路服务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{railIds}")
    public AjaxResult remove(@PathVariable Long[] railIds) {
        return toAjax(rsOpRailService.deleteRsOpRailByRailIds(railIds));
    }
}
