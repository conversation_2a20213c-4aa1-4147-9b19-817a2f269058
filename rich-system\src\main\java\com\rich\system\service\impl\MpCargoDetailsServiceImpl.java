package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.MpCargoDetails;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.MpCargoDetailsMapper;
import com.rich.system.service.MpCargoDetailsService;

/**
 * 客户在仓货物明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Service
public class MpCargoDetailsServiceImpl implements MpCargoDetailsService {
    @Autowired
    private MpCargoDetailsMapper mpCargoDetailsMapper;

    /**
     * 查询客户在仓货物明细
     *
     * @param cargoDetailsId 客户在仓货物明细主键
     * @return 客户在仓货物明细
     */
    @Override
    public MpCargoDetails selectMpCargoDetailsByCargoDetailsId(Long cargoDetailsId) {
        return mpCargoDetailsMapper.selectMpCargoDetailsByCargoDetailsId(cargoDetailsId);
    }

    /**
     * 查询客户在仓货物明细列表
     *
     * @param mpCargoDetails 客户在仓货物明细
     * @return 客户在仓货物明细
     */
    @Override
    public List<MpCargoDetails> selectMpCargoDetailsList(MpCargoDetails mpCargoDetails) {
        return mpCargoDetailsMapper.selectMpCargoDetailsList(mpCargoDetails);
    }

    /**
     * 新增客户在仓货物明细
     *
     * @param mpCargoDetails 客户在仓货物明细
     * @return 结果
     */
    @Override
    public int insertMpCargoDetails(MpCargoDetails mpCargoDetails) {
        return mpCargoDetailsMapper.insertMpCargoDetails(mpCargoDetails);
    }

    /**
     * 修改客户在仓货物明细
     *
     * @param mpCargoDetails 客户在仓货物明细
     * @return 结果
     */
    @Override
    public int updateMpCargoDetails(MpCargoDetails mpCargoDetails) {
        return mpCargoDetailsMapper.updateMpCargoDetails(mpCargoDetails);
    }

    /**
     * 修改客户在仓货物明细状态
     *
     * @param mpCargoDetails 客户在仓货物明细
     * @return 客户在仓货物明细
     */
    @Override
    public int changeStatus(MpCargoDetails mpCargoDetails) {
        return mpCargoDetailsMapper.updateMpCargoDetails(mpCargoDetails);
    }

    /**
     * 批量删除客户在仓货物明细
     *
     * @param cargoDetailsIds 需要删除的客户在仓货物明细主键
     * @return 结果
     */
    @Override
    public int deleteMpCargoDetailsByCargoDetailsIds(Long[] cargoDetailsIds) {
        return mpCargoDetailsMapper.deleteMpCargoDetailsByCargoDetailsIds(cargoDetailsIds);
    }

    /**
     * 删除客户在仓货物明细信息
     *
     * @param cargoDetailsId 客户在仓货物明细主键
     * @return 结果
     */
    @Override
    public int deleteMpCargoDetailsByCargoDetailsId(Long cargoDetailsId) {
        return mpCargoDetailsMapper.deleteMpCargoDetailsByCargoDetailsId(cargoDetailsId);
    }
}
