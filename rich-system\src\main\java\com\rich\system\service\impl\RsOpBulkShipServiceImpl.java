package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpBulkShip;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsOpBulkShipMapper;
import com.rich.system.service.RsOpBulkShipService;

/**
 * 散杂船服务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Service
public class RsOpBulkShipServiceImpl implements RsOpBulkShipService {
    @Autowired
    private RsOpBulkShipMapper rsOpBulkShipMapper;

    /**
     * 查询散杂船服务
     *
     * @param bulkShipId 散杂船服务主键
     * @return 散杂船服务
     */
    @Override
    public RsOpBulkShip selectRsOpBulkShipByBulkShipId(Long bulkShipId) {
        return rsOpBulkShipMapper.selectRsOpBulkShipByBulkShipId(bulkShipId);
    }

    /**
     * 查询散杂船服务列表
     *
     * @param rsOpBulkShip 散杂船服务
     * @return 散杂船服务
     */
    @Override
    public List<RsOpBulkShip> selectRsOpBulkShipList(RsOpBulkShip rsOpBulkShip) {
        return rsOpBulkShipMapper.selectRsOpBulkShipList(rsOpBulkShip);
    }

    /**
     * 新增散杂船服务
     *
     * @param rsOpBulkShip 散杂船服务
     * @return 结果
     */
    @Override
    public int insertRsOpBulkShip(RsOpBulkShip rsOpBulkShip) {
        return rsOpBulkShipMapper.insertRsOpBulkShip(rsOpBulkShip);
    }

    /**
     * 修改散杂船服务
     *
     * @param rsOpBulkShip 散杂船服务
     * @return 结果
     */
    @Override
    public int updateRsOpBulkShip(RsOpBulkShip rsOpBulkShip) {
        return rsOpBulkShipMapper.updateRsOpBulkShip(rsOpBulkShip);
    }

    /**
     * 修改散杂船服务状态
     *
     * @param rsOpBulkShip 散杂船服务
     * @return 散杂船服务
     */
    @Override
    public int changeStatus(RsOpBulkShip rsOpBulkShip) {
        return rsOpBulkShipMapper.updateRsOpBulkShip(rsOpBulkShip);
    }

    /**
     * 批量删除散杂船服务
     *
     * @param bulkShipIds 需要删除的散杂船服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpBulkShipByBulkShipIds(Long[] bulkShipIds) {
        return rsOpBulkShipMapper.deleteRsOpBulkShipByBulkShipIds(bulkShipIds);
    }

    /**
     * 删除散杂船服务信息
     *
     * @param bulkShipId 散杂船服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpBulkShipByBulkShipId(Long bulkShipId) {
        return rsOpBulkShipMapper.deleteRsOpBulkShipByBulkShipId(bulkShipId);
    }
}
