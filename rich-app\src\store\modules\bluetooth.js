// 蓝牙状态管理模块
const state = {
    devices: [], // 蓝牙设备列表
    currentDevice: null, // 当前连接的设备
    connectionId: '', // 当前连接ID
    isSearching: false, // 是否正在搜索
}

const mutations = {
    // 设置设备列表
    SET_DEVICES(state, devices) {
        state.devices = devices
    },

    // 添加设备到列表
    ADD_DEVICE(state, device) {
        // 检查是否已存在
        const existDevice = state.devices.find(item => item.deviceId === device.deviceId)
        if (!existDevice) {
            state.devices.push(device)
        }
    },

    // 清空设备列表
    CLEAR_DEVICES(state) {
        state.devices = []
    },

    // 设置当前连接的设备
    SET_CURRENT_DEVICE(state, device) {
        state.currentDevice = device
    },

    // 设置当前连接ID
    SET_CONNECTION_ID(state, id) {
        state.connectionId = id
    },

    // 设置搜索状态
    SET_SEARCHING(state, isSearching) {
        state.isSearching = isSearching
    },

    // 更新设备服务信息
    UPDATE_DEVICE_SERVICE(state, {deviceId, service}) {
        const device = state.devices.find(item => item.deviceId === deviceId)
        if (device) {
            if (!device.services) {
                device.services = []
            }

            // 检查服务是否已存在
            const existService = device.services.find(item =>
                item.serviceId === service.serviceId &&
                item.characteristicId === service.characteristicId
            )

            if (!existService) {
                device.services.push(service)
            }
        }
    },

    // 断开连接时清除状态
    DISCONNECT_DEVICE(state) {
        state.connectionId = ''
        state.currentDevice = null
    }
}

const actions = {
    // 初始化蓝牙
    initBluetooth({commit}) {
        return new Promise((resolve, reject) => {
            wx.openBluetoothAdapter({
                success: (res) => {
                    console.log("蓝牙初始化成功", res)
                    resolve(res)
                },
                fail: (err) => {
                    console.error("蓝牙初始化失败", err)
                    uni.showToast({
                        title: '蓝牙初始化失败' + err,
                        icon: 'none'
                    })
                    reject(err)
                }
            })
        })
    },

    // 搜索蓝牙设备
    searchBluetooth({commit, dispatch}) {
        if (state.isSearching) {
            return Promise.reject(new Error('正在搜索中...'))
        }

        commit('SET_SEARCHING', true)
        commit('CLEAR_DEVICES')

        return dispatch('initBluetooth')
            .then(() => {
                return new Promise((resolve, reject) => {
                    uni.getBluetoothAdapterState({
                        success: (res) => {
                            if (res.available) {
                                // 如果正在搜索则先停止
                                if (res.discovering) {
                                    dispatch('stopDeviceDiscovery')
                                }

                                // 监听设备发现事件
                                uni.onBluetoothDeviceFound((result) => {
                                    if (result.devices && result.devices.length > 0) {
                                        const device = result.devices[0]

                                        // 过滤未知设备
                                        if (device.name && device.name !== '未知设备') {
                                            commit('ADD_DEVICE', {
                                                name: device.name,
                                                deviceId: device.deviceId,
                                                services: []
                                            })
                                        }
                                    }
                                })

                                // 开始搜索设备
                                uni.startBluetoothDevicesDiscovery({
                                    success: (res) => {
                                        console.log("开始搜索设备", res)
                                        resolve(res)

                                        // 10秒后自动停止搜索
                                        setTimeout(() => {
                                            if (state.isSearching) {
                                                dispatch('stopDeviceDiscovery')
                                            }
                                        }, 10000)
                                    },
                                    fail: (err) => {
                                        commit('SET_SEARCHING', false)
                                        console.error("搜索设备失败", err)
                                        reject(err)
                                    }
                                })
                            } else {
                                commit('SET_SEARCHING', false)
                                reject(new Error('蓝牙不可用'))
                            }
                        },
                        fail: (err) => {
                            commit('SET_SEARCHING', false)
                            reject(err)
                        }
                    })
                })
            })
            .catch(err => {
                commit('SET_SEARCHING', false)
                return Promise.reject(err)
            })
    },

    // 停止设备搜索
    stopDeviceDiscovery({commit}) {
        return new Promise((resolve) => {
            uni.stopBluetoothDevicesDiscovery({
                success: (res) => {
                    console.log("停止搜索设备", res)
                    commit('SET_SEARCHING', false)
                    resolve(res)
                },
                complete: () => {
                    commit('SET_SEARCHING', false)
                }
            })
        })
    },

    // 连接设备
    connectDevice({commit, dispatch}, device) {
        // 如果已连接该设备则不重复连接
        if (state.connectionId === device.deviceId) {
            return Promise.resolve({message: '设备已连接'})
        }

        return new Promise((resolve, reject) => {
            uni.createBLEConnection({
                deviceId: device.deviceId,
                success: (res) => {
                    console.log("连接设备成功", res)
                    commit('SET_CONNECTION_ID', device.deviceId)
                    commit('SET_CURRENT_DEVICE', device)

                    // 获取设备服务
                    setTimeout(() => {
                        dispatch('getDeviceServices', device.deviceId)
                    }, 1000)

                    resolve({message: `已连接 ${device.name}`})
                },
                fail: (err) => {
                    console.error("连接设备失败", err)
                    reject(new Error('连接失败'))
                },
                complete: () => {
                    // 连接后停止搜索
                    dispatch('stopDeviceDiscovery')
                }
            })
        })
    },

    // 获取设备服务
    getDeviceServices({commit, dispatch}, deviceId) {
        return new Promise((resolve, reject) => {
            uni.getBLEDeviceServices({
                deviceId: deviceId,
                success: (res) => {
                    console.log("获取设备服务成功", res)

                    // 遍历所有服务
                    for (let i = 0; i < res.services.length; i++) {
                        const serviceId = res.services[i].uuid

                        // 获取每个服务的特征值
                        dispatch('getServiceCharacteristics', {deviceId, serviceId})
                    }

                    resolve(res)
                },
                fail: (err) => {
                    console.error("获取设备服务失败", err)
                    reject(new Error('获取设备服务失败'))
                }
            })
        })
    },

    // 获取服务特征值
    getServiceCharacteristics({commit}, {deviceId, serviceId}) {
        uni.getBLEDeviceCharacteristics({
            deviceId: deviceId,
            serviceId: serviceId,
            success: (res) => {
                const characteristics = res.characteristics

                // 查找可写的特征值
                for (let i = 0; i < characteristics.length; i++) {
                    if (characteristics[i].properties.write) {
                        const characteristicId = characteristics[i].uuid

                        // 更新设备的服务信息
                        commit('UPDATE_DEVICE_SERVICE', {
                            deviceId,
                            service: {
                                serviceId: serviceId,
                                characteristicId: characteristicId
                            }
                        })
                    }
                }
            }
        })
    },

    // 发送蓝牙数据
    sendBluetoothData({dispatch}, {deviceId, serviceId, characteristicId, data}) {
        // 转换数据格式
        const dataArray = Array.from(data)
        // 分片处理数据
        const chunks = []
        for (let i = 0; i < dataArray.length; i += 20) {
            chunks.push(dataArray.slice(i, i + 20))
        }

        // 依次发送数据分片
        return dispatch('writeDataSequentially', {deviceId, serviceId, characteristicId, chunks, index: 0})
    },

    // 按顺序写入数据分片
    writeDataSequentially({dispatch}, {deviceId, serviceId, characteristicId, chunks, index}) {
        // 检查是否已完成所有分片
        if (index >= chunks.length) {
            return Promise.resolve()
        }

        const chunk = chunks[index]
        // 创建ArrayBuffer
        const buffer = new ArrayBuffer(chunk.length)
        const dataView = new DataView(buffer)

        // 填充数据
        for (let i = 0; i < chunk.length; i++) {
            dataView.setUint8(i, chunk[i])
        }

        return new Promise((resolve, reject) => {
            uni.writeBLECharacteristicValue({
                deviceId: deviceId,
                serviceId: serviceId,
                characteristicId: characteristicId,
                value: buffer,
                success: () => {
                    // 写入成功后继续下一个分片
                    dispatch('writeDataSequentially', {deviceId, serviceId, characteristicId, chunks, index: index + 1})
                        .then(resolve)
                        .catch(reject)
                },
                fail: (err) => {
                    console.error("写入数据失败", err)
                    reject(new Error('打印失败'))
                }
            })
        })
    },

    // 断开蓝牙连接
    disconnectBluetooth({commit}) {
        if (!state.connectionId) {
            return Promise.resolve()
        }

        return new Promise((resolve) => {
            uni.closeBLEConnection({
                deviceId: state.connectionId,
                success: (res) => {
                    console.log("断开蓝牙连接", res)
                    commit('DISCONNECT_DEVICE')
                    resolve(res)
                },
                complete: () => {
                    commit('DISCONNECT_DEVICE')
                }
            })
        })
    }
}

const getters = {
    devices: state => state.devices,
    currentDevice: state => state.currentDevice,
    connectionId: state => state.connectionId,
    isSearching: state => state.isSearching,
    isConnected: state => !!state.currentDevice && !!state.connectionId
}

export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
} 