package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.ExtCompany;
import com.rich.common.core.domain.entity.ExtStaff;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.system.mapper.ExtCompanyMapper;
import com.rich.system.mapper.ExtStaffMapper;
import com.rich.system.service.ExtStaffService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 外部员工Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-09-27
 */
@Service

public class ExtStaffServiceImpl implements ExtStaffService {

    @Autowired
    private ExtStaffMapper extStaffMapper;

    @Autowired
    private ExtCompanyMapper extCompanyMapper;

    /**
     * 查询外部员工
     *
     * @param staffId 外部员工主键
     * @return 外部员工
     */
    @Override
    public ExtStaff selectExtStaffByStaffId(Long staffId) {
        return extStaffMapper.selectExtStaffByStaffId(staffId);
    }

    /**
     * 查询外部员工列表
     *
     * @param extStaff 外部员工
     * @return 外部员工
     */
    @Override
    public List<ExtStaff> selectExtStaffList(ExtStaff extStaff) {
        return extStaffMapper.selectExtStaffList(extStaff);
    }

    /**
     * 新增外部员工
     *
     * @param extStaff 外部员工
     * @return 结果
     */
    @Override
    public int insertExtStaff(ExtStaff extStaff) {
        extStaff.setCreateTime(DateUtils.getNowDate());
        extStaff.setCreateBy(SecurityUtils.getUserId());
        // 如果添加的是公司主联系人，则添加到公司表中的主联系人中
        updateCompanyContact(extStaff);
        return extStaffMapper.insertExtStaff(extStaff);
    }

    /**
     * 修改外部员工
     *
     * @param extStaff 外部员工
     * @return 结果
     */
    @Override
    public int updateExtStaff(ExtStaff extStaff) {
        extStaff.setUpdateTime(DateUtils.getNowDate());
        extStaff.setUpdateBy(SecurityUtils.getUserId());
        updateCompanyContact(extStaff);
        return extStaffMapper.updateExtStaff(extStaff);
    }

    private void updateCompanyContact(ExtStaff extStaff) {
        // 只能存在一个主联系人
        ExtStaff extStaff1 = new ExtStaff();
        extStaff1.setIsMain("Y");
        extStaff1.setSqdCompanyId(extStaff.getSqdCompanyId());
        List<ExtStaff> extStaffs = extStaffMapper.selectExtStaffList(extStaff1);
        if (extStaffs.size() != 1 && !extStaffs.get(0).getStaffId().equals(extStaff.getStaffId())) {
            throw new RuntimeException("只能存在一个主联系人");
        }
        // 如果添加的是公司主联系人，则添加到公司表中的主联系人中
        if (extStaff.getIsMain().equals("Y")) {
            ExtCompany extCompany = new ExtCompany();
            extCompany.setCompanyId(extStaff.getSqdCompanyId());
            extCompany.setSqdMainAttn(extStaff.getStaffShortName());
            extCompany.setMainStaffOfficialName(extStaff.getStaffShortName());
            extCompany.setStaffWechat(extStaff.getStaffWechat());
            extCompany.setStaffQq(extStaff.getStaffQq());
            extCompany.setStaffMobile(extStaff.getStaffPhoneNum());
            extCompany.setStaffEmail(extStaff.getStaffEmailEnterprise());
            extCompanyMapper.updateExtCompany(extCompany);
        }
    }

    /**
     * 批量删除外部员工
     *
     * @param staffIds 需要删除的外部员工主键
     * @return 结果
     */
    @Override
    public int deleteExtStaffByStaffIds(Long[] staffIds) {
        return extStaffMapper.deleteExtStaffByStaffIds(staffIds);
    }

    /**
     * 删除外部员工信息
     *
     * @param staffId 外部员工主键
     * @return 结果
     */
    @Override
    public int deleteExtStaffByStaffId(Long staffId) {
        return extStaffMapper.deleteExtStaffByStaffId(staffId);
    }
}
