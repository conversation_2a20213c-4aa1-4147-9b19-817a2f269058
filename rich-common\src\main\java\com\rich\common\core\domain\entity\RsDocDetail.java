package com.rich.common.core.domain.entity;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

/**
 * 操作文件对象 rs_doc_detail
 *
 * <AUTHOR>
 * @date 2023-09-21
 */
public class RsDocDetail extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 文件需求ID
     */
    private Long docDetailId;

    /**
     * 物流进度流水
     */
    @Excel(name = "物流进度流水")
    private Long operationalProcessId;

    /**
     * 文件名/流向编号
     */
    @Excel(name = "文件名/流向编号")
    private String flowNo;

    /**
     * 文件类型
     */
    @Excel(name = "文件类型")
    private Long docId;

    /**
     * 文件流向
     */
    @Excel(name = "文件流向")
    private Long docFlowDirectionId;

    /**
     * 文件形式
     */
    @Excel(name = "文件形式")
    private Long issueTypeId;

    /**
     * 随附文件
     */
    @Excel(name = "随附文件")
    private String fileList;

    public Long getDocDetailId() {
        return docDetailId;
    }

    public void setDocDetailId(Long docDetailId) {
        this.docDetailId = docDetailId;
    }

    public Long getOperationalProcessId() {
        return operationalProcessId;
    }

    public void setOperationalProcessId(Long operationalProcessId) {
        this.operationalProcessId = operationalProcessId;
    }

    public String getFlowNo() {
        return flowNo;
    }

    public void setFlowNo(String flowNo) {
        this.flowNo = flowNo;
    }

    public Long getDocId() {
        return docId;
    }

    public void setDocId(Long docId) {
        this.docId = docId;
    }

    public Long getDocFlowDirectionId() {
        return docFlowDirectionId;
    }

    public void setDocFlowDirectionId(Long docFlowDirectionId) {
        this.docFlowDirectionId = docFlowDirectionId;
    }

    public Long getIssueTypeId() {
        return issueTypeId;
    }

    public void setIssueTypeId(Long issueTypeId) {
        this.issueTypeId = issueTypeId;
    }

    public String getFileList() {
        return fileList;
    }

    public void setFileList(String fileList) {
        this.fileList = fileList;
    }

}
