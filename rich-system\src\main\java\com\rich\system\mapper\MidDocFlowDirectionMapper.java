package com.rich.system.mapper;

import com.rich.system.domain.MidDocFlowDirection;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@Mapper
public interface MidDocFlowDirectionMapper {
    /**
     * 查询【请填写功能名称】
     *
     * @param docFlowDirectionId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    MidDocFlowDirection selectMidDocFlowDirectionByDocFlowDirectionId(Long docFlowDirectionId);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param midDocFlowDirection 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    List<MidDocFlowDirection> selectMidDocFlowDirectionList(MidDocFlowDirection midDocFlowDirection);

    /**
     * 新增【请填写功能名称】
     *
     * @param midDocFlowDirection 【请填写功能名称】
     * @return 结果
     */
    int insertMidDocFlowDirection(MidDocFlowDirection midDocFlowDirection);

    /**
     * 修改【请填写功能名称】
     *
     * @param midDocFlowDirection 【请填写功能名称】
     * @return 结果
     */
    int updateMidDocFlowDirection(MidDocFlowDirection midDocFlowDirection);

    /**
     * 删除【请填写功能名称】
     *
     * @param docFlowDirectionId 【请填写功能名称】主键
     * @return 结果
     */
    int deleteMidDocFlowDirectionByDocFlowDirectionId(Long docFlowDirectionId);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param docFlowDirectionIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteMidDocFlowDirectionByDocFlowDirectionIds(Long[] docFlowDirectionIds);

    int batchDocFlowDirection(List<MidDocFlowDirection> list);
}
