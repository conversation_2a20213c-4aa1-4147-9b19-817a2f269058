package com.rich.common.config;

import org.springframework.stereotype.Controller;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * <AUTHOR>
 * @Date 2023/2/15 9:32
 * @Version 1.0
 */
@Controller
@ServerEndpoint("/websocket/{tableId}")
public class WebSocket {

    public static CopyOnWriteArraySet<WebSocket> webSockets = new CopyOnWriteArraySet<>();
    private static Map<String, Session> sessionPool = new HashMap<>();
    //session集合
    private static List<Map<String, Object>> sessionList = new ArrayList<>();
    //信息集合
    private static List<Map<String, String>> MessageList = new ArrayList<>();
    private Session session;
    private String uid;

    @OnOpen
    public void onOpen(Session session, @PathParam(value = "tableId") String code) {
        Map<String, Object> map = new HashMap<String, Object>();
        this.session = session;
        this.uid = code;
        webSockets.add(this);
        sessionPool.put(code, session);
        map.put("uid", code);
        map.put("session", session);
        sessionList.add(map);
//        Constants.WEBSOCKET = true;//定义常量  是否开启websocket连接
//        System.out.println("【客户端】有新的连接，总数为:" + webSockets.size());
    }

    @OnClose
    public void onClose() {
        webSockets.remove(this);
        //Constants.WEBSOCKET = false;
//        System.out.println("【客户端】连接断开，总数为:" + webSockets.size());
        for (int i = 0; i < MessageList.size(); i++) {
            if (this.uid.equals(MessageList.get(i).get("uid"))) {
                MessageList.remove(i);
                break;
            }
        }
        for (int i = 0; i < sessionList.size(); i++) {
            if (this.uid.equals(sessionList.get(i).get("uid"))) {
                sessionList.remove(i);
                break;
            }
        }

    }

    @OnMessage
    public void onMessage(String message) {
//        System.out.println("【客户端】收到消息:" + message);
        if (MessageList != null && MessageList.size() > 0) {
            for (Map<String, String> messageMap : MessageList) {
                if (this.uid.equals(messageMap.get("uid"))) {
                    messageMap.put("message", message);
                    return;
                }
            }
        }
        Map<String, String> map = new HashMap<String, String>();
        map.put("message", message);
        map.put("uid", this.uid);
        MessageList.add(map);
    }

    @OnError
    public void onError(Session session, Throwable throwable) {
        // 记录错误信息
        System.err.println("WebSocket 错误: " + throwable.getMessage());
        throwable.printStackTrace();
    }

    // 此为广播消息
    public static void sendAllMessage(String message) {
        for (WebSocket webSocket : webSockets) {
            try {
                webSocket.session.getAsyncRemote().sendText(message);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    // 此为单点消息
    public void sendOneMessage(String code, String message) {
        Session session = sessionPool.get(code);
        /*在发送数据之前先确认 session是否已经打开 使用session.isOpen() 为true 则发送消息
         * 不然会报错:The WebSocket session [0] has been closed and no method (apart from close()) may be called on a closed session */
        if (session != null && session.isOpen()) {
            try {
                session.getAsyncRemote().sendText(message);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    // 此为单点消息
    public void sendOneObject(String code, Object obj) {
        Session session = sessionPool.get(code);
        /*在发送数据之前先确认 session是否已经打开 使用session.isOpen() 为true 则发送消息
         * 不然会报错:The WebSocket session [0] has been closed and no method (apart from close()) may be called on a closed session */
        if (session != null && session.isOpen()) {
            try {
                session.getAsyncRemote().sendObject(obj);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    // 此为对应的广播消息
    public void sendAllMessageBySelf() {

    }
}
