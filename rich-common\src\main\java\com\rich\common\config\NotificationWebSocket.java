package com.rich.common.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * WebSocket 通知服务
 */
@Controller
@ServerEndpoint("/websocket/notification/{userId}")
public class NotificationWebSocket {

    private static final Logger log = LoggerFactory.getLogger(NotificationWebSocket.class);

    public static CopyOnWriteArraySet<NotificationWebSocket> webSockets = new CopyOnWriteArraySet<>();
    private static Map<String, Session> sessionPool = new HashMap<>();
    private Session session;
    private String userId;

    /**
     * 广播通知消息给所有连接的客户端
     */
    public static void broadcastNotification(int opCount, int psaCount) {
        String message = String.format("{\"type\":\"notification\",\"opCount\":%d,\"psaCount\":%d}", opCount, psaCount);
        sendAllMessage(message);
    }

    /**
     * 向指定用户发送通知消息
     *
     * @param userId 用户ID
     * @param opCount 操作通知数量
     * @param psaCount PSA通知数量
     * @return 是否发送成功
     */
    public static boolean sendNotificationToUser(String userId, int opCount, int psaCount) {
        String message = String.format("{\"type\":\"notification\",\"opCount\":%d,\"psaCount\":%d}", opCount, psaCount);
        return sendMessageToUser(userId, message);
    }

    /**
     * 向指定用户发送消息
     *
     * @param userId  用户ID
     * @param message 消息内容
     * @return 是否发送成功
     */
    public static boolean sendMessageToUser(String userId, String message) {
        Session session = sessionPool.get(userId);
        if (session != null && session.isOpen()) {
            try {
                session.getAsyncRemote().sendText(message);
                log.info("【通知】已向用户 {} 发送消息: {}", userId, message);
                return true;
            } catch (Exception e) {
                log.error("【通知】向用户 {} 发送消息失败: {}", userId, e.getMessage());
            }
        } else {
            log.warn("【通知】用户 {} 不在线或会话已关闭", userId);
        }
        return false;
    }

    /**
     * 广播消息
     */
    public static void sendAllMessage(String message) {
        for (NotificationWebSocket webSocket : webSockets) {
            try {
                if (webSocket.session != null && webSocket.session.isOpen()) {
                    webSocket.session.getAsyncRemote().sendText(message);
                }
            } catch (Exception e) {
                log.error("发送WebSocket消息失败", e);
            }
        }
    }

    @OnOpen
    public void onOpen(Session session, @PathParam("userId") String userId) {
        this.session = session;
        this.userId = userId;
        webSockets.add(this);
        sessionPool.put(userId, session);
        log.info("【通知客户端】用户 {} 已连接，当前总连接数: {}", userId, webSockets.size());
    }

    @OnClose
    public void onClose() {
        webSockets.remove(this);
        sessionPool.remove(this.userId);
        log.info("【通知客户端】用户 {} 已断开连接，当前总连接数: {}", this.userId, webSockets.size());
    }

    @OnMessage
    public void onMessage(String message) {
        log.info("【通知客户端】收到用户 {} 的消息: {}", this.userId, message);
    }

    @OnError
    public void onError(Session session, Throwable throwable) {
        log.error("WebSocket用户 {} 出现错误: {}", this.userId, throwable.getMessage());
        throwable.printStackTrace();
    }
} 