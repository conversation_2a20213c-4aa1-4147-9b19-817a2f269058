package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

/**
 * 文件流向对象 bas_doc_flow_direction
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
public class BasDocFlowDirection extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 文件流向表
     */
    private Long docFlowDirectionId;

    /**
     * 简称
     */
    @Excel(name = "简称")
    private String docFlowDirectionShortName;

    /**
     * 中文名
     */
    @Excel(name = "中文名")
    private String docFlowDirectionLocalName;

    /**
     * 英文名
     */
    @Excel(name = "英文名")
    private String docFlowDirectionEnName;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Integer orderNum;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;

    private String code;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setDocFlowDirectionId(Long docFlowDirectionId) {
        this.docFlowDirectionId = docFlowDirectionId;
    }

    public Long getDocFlowDirectionId() {
        return docFlowDirectionId;
    }

    public void setDocFlowDirectionShortName(String docFlowDirectionShortName) {
        this.docFlowDirectionShortName = docFlowDirectionShortName;
    }

    public String getDocFlowDirectionShortName() {
        return docFlowDirectionShortName;
    }

    public void setDocFlowDirectionLocalName(String docFlowDirectionLocalName) {
        this.docFlowDirectionLocalName = docFlowDirectionLocalName;
    }

    public String getDocFlowDirectionLocalName() {
        return docFlowDirectionLocalName;
    }

    public void setDocFlowDirectionEnName(String docFlowDirectionEnName) {
        this.docFlowDirectionEnName = docFlowDirectionEnName;
    }

    public String getDocFlowDirectionEnName() {
        return docFlowDirectionEnName;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
}
