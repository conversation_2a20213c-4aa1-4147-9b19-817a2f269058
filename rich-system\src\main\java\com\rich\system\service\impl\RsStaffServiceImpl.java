package com.rich.system.service.impl;

import com.rich.common.annotation.DataScope;
import com.rich.common.constant.UserConstants;
import com.rich.common.core.domain.entity.RsStaff;
import com.rich.common.exception.ServiceException;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.StringUtils;
import com.rich.common.utils.bean.BeanValidators;
import com.rich.common.utils.spring.SpringUtils;
import com.rich.system.mapper.MidRsStaffRoleMapper;
import com.rich.system.mapper.RsStaffMapper;
import com.rich.system.service.RsStaffService;
import com.rich.system.service.SysConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Validator;
import java.util.*;

/**
 * 用户 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class RsStaffServiceImpl implements RsStaffService {
    private static final Logger log = LoggerFactory.getLogger(RsStaffServiceImpl.class);

    @Autowired
    private RsStaffMapper userMapper;
    @Autowired
    private MidRsStaffRoleMapper userRoleMapper;
    @Autowired
    private SysConfigService configService;
    @Autowired
    protected Validator validator;

    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", subDeptAlias = "d", userAlias = "u", subUserAlias = "u")
    public List<RsStaff> selectUserList(RsStaff user) {
        return userMapper.selectUserList(user);
    }

    @Override
    public List<RsStaff> selectList(RsStaff user) {
        return userMapper.selectUserList(user);
    }


    @Override
    public List<RsStaff> selectUserBasicList(RsStaff user) {
        return userMapper.selectUserBasicList(user);
    }

    @Override
    public boolean validateUnid(String userName, String unid) {
        return userMapper.selectUnidByUserName(userName, unid);
    }

    @Override
    public Map<String, List<Long>> selectPermissionLevelList(Long staffId) {
        Map<String, List<Long>> permissionLevel = new HashMap<>();
        Long userId = SecurityUtils.getUserId();
        // 全部员工
        permissionLevel.put("A", userMapper.selectPermissionLevelA());
        // 登录人所在业务部门的全部同事。
        permissionLevel.put("B", userMapper.selectPermissionLevelB(SecurityUtils.getUserId()));
        // 登录人自己的及其组员（不包含组内其他领导同事）的业务员/业务助理身份。
        List<Long> C = userMapper.selectPermissionLevelC(SecurityUtils.getUserId());
        C.add(userId);
        permissionLevel.put("C", C);
        permissionLevel.put("D", new ArrayList<>(Collections.singletonList(userId)));
        return permissionLevel;
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public RsStaff selectUserByUserName(String userName) {
        return userMapper.selectUserByUserName(userName);
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public RsStaff selectUserById(Long userId) {
        return userMapper.selectUserById(userId);
    }

    /**
     * 校验用户名称是否唯一
     *
     * @return 结果
     */
    @Override
    public boolean checkUserNameUnique(RsStaff user) {
        Long userId = StringUtils.isNull(user.getStaffId()) ? -1L : user.getStaffId();
        RsStaff info = userMapper.checkUserNameUnique(user.getStaffUsername());
        if (StringUtils.isNotNull(info) && info.getStaffId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public boolean checkPhoneUnique(RsStaff user) {
        Long userId = StringUtils.isNull(user.getStaffId()) ? -1L : user.getStaffId();
        RsStaff info = userMapper.checkPhoneUnique(user.getStaffPhoneNum());
        if (StringUtils.isNotNull(info) && info.getStaffId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public boolean checkEmailUnique(RsStaff user) {
        Long userId = StringUtils.isNull(user.getStaffId()) ? -1L : user.getStaffId();
        RsStaff info = userMapper.checkEmailUnique(user.getStaffEmailEnterprise());
        if (StringUtils.isNotNull(info) && info.getStaffId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(RsStaff user) {
        if (StringUtils.isNotNull(user.getStaffId()) && user.isAdmin()) {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(Long userId) {
        if (!RsStaff.isAdmin(SecurityUtils.getLoginUser().getUser().getRole())) {
            RsStaff user = new RsStaff();
            user.setStaffId(userId);
            List<RsStaff> users = SpringUtils.getAopProxy(this).selectUserList(user);
            if (StringUtils.isEmpty(users)) {
                throw new ServiceException("没有权限访问用户数据！");
            }
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertUser(RsStaff user) {
        return userMapper.insertUser(user);
    }

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(RsStaff user) {
        return userMapper.insertUser(user) > 0;
    }

    /**
     * 修改保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateUser(RsStaff user) {
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserStatus(RsStaff user) {
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserProfile(RsStaff user) {
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar) {
        return userMapper.updateUserAvatar(userName, avatar) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int resetPwd(RsStaff user) {
        return userMapper.updateUser(user);
    }

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(String userName, String password) {
        return userMapper.resetUserPwd(userName, password);
    }

    /**
     * 新增用户角色信息
     *
     * @param user 用户对象
     */
    public void insertUserRole(RsStaff user) {

    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    public void insertUserRole(Long userId, Long[] roleIds) {

    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteUserById(Long userId) {
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        return userMapper.deleteUserById(userId);
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteUserByIds(Long[] userIds) {
        for (Long userId : userIds) {
            checkUserAllowed(new RsStaff(userId));
            checkUserDataScope(userId);
        }
        // 删除用户与角色关联
        userRoleMapper.deleteUserRole(userIds);

        return userMapper.deleteUserByIds(userIds);
    }

    /**
     * 导入用户数据
     *
     * @param userList        用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importUser(List<RsStaff> userList, Boolean isUpdateSupport) {
        if (StringUtils.isNull(userList) || userList.size() == 0) {
            throw new ServiceException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        String password = configService.selectConfigByKey("sys.user.initPassword");
        for (RsStaff user : userList) {
            try {
                // 验证是否存在这个用户
                RsStaff u = userMapper.selectUserByUserName(user.getStaffUsername());
                if (StringUtils.isNull(u)) {
                    BeanValidators.validateWithException(validator, user);
                    user.setStaffPassword(SecurityUtils.encryptPassword(password));
                    user.setCreateBy(SecurityUtils.getUserId());
                    userMapper.insertUser(user);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、账号 ").append(user.getStaffUsername()).append(" 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, user);
                    checkUserAllowed(u);
                    checkUserDataScope(u.getStaffId());
                    user.setStaffId(u.getStaffId());
                    user.setUpdateBy(SecurityUtils.getUserId());
                    userMapper.updateUser(user);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、账号 ").append(user.getStaffUsername()).append(" 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、账号 ").append(user.getStaffUsername()).append(" 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + user.getStaffUsername() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 通过微信OpenID查询用户
     *
     * @param openid 微信OpenID
     * @return 用户对象信息
     */
    @Override
    public RsStaff selectUserByOpenid(String openid) {
        return userMapper.selectUserByOpenid(openid);
    }

    /**
     * 绑定微信用户信息
     *
     * @param staffId    用户ID
     * @param openid     微信OpenID
     * @param unionid    微信UnionID
     * @param wxNickName 微信昵称
     * @return 结果
     */
    @Override
    public int bindWechatUser(Long staffId, String openid, String unionid, String wxNickName) {
        return userMapper.bindWechatUser(staffId, openid, unionid, wxNickName);
    }

    /**
     * 更新用户的微信信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserWechatInfo(RsStaff user) {
        return userMapper.updateUserWechatInfo(user);
    }

}
