package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.MpWarehouseClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.MpWarehouseClientMapper;
import com.rich.system.service.MpWarehouseClientService;

/**
 * 仓库客户信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Service
public class MpWarehouseClientServiceImpl implements MpWarehouseClientService {
    @Autowired
    private MpWarehouseClientMapper mpWarehouseClientMapper;

    /**
     * 查询仓库客户信息
     *
     * @param warehouseClientId 仓库客户信息主键
     * @return 仓库客户信息
     */
    @Override
    public MpWarehouseClient selectMpWarehouseClientByWarehouseClientId(Long warehouseClientId) {
        return mpWarehouseClientMapper.selectMpWarehouseClientByWarehouseClientId(warehouseClientId);
    }

    /**
     * 查询仓库客户信息列表
     *
     * @param mpWarehouseClient 仓库客户信息
     * @return 仓库客户信息
     */
    @Override
    public List<MpWarehouseClient> selectMpWarehouseClientList(MpWarehouseClient mpWarehouseClient) {
        return mpWarehouseClientMapper.selectMpWarehouseClientList(mpWarehouseClient);
    }

    /**
     * 新增仓库客户信息
     *
     * @param mpWarehouseClient 仓库客户信息
     * @return 结果
     */
    @Override
    public int insertMpWarehouseClient(MpWarehouseClient mpWarehouseClient) {
        return mpWarehouseClientMapper.insertMpWarehouseClient(mpWarehouseClient);
    }

    /**
     * 修改仓库客户信息
     *
     * @param mpWarehouseClient 仓库客户信息
     * @return 结果
     */
    @Override
    public int updateMpWarehouseClient(MpWarehouseClient mpWarehouseClient) {
        return mpWarehouseClientMapper.updateMpWarehouseClient(mpWarehouseClient);
    }

    /**
     * 修改仓库客户信息状态
     *
     * @param mpWarehouseClient 仓库客户信息
     * @return 仓库客户信息
     */
    @Override
    public int changeStatus(MpWarehouseClient mpWarehouseClient) {
        return mpWarehouseClientMapper.updateMpWarehouseClient(mpWarehouseClient);
    }

    /**
     * 批量删除仓库客户信息
     *
     * @param warehouseClientIds 需要删除的仓库客户信息主键
     * @return 结果
     */
    @Override
    public int deleteMpWarehouseClientByWarehouseClientIds(Long[] warehouseClientIds) {
        return mpWarehouseClientMapper.deleteMpWarehouseClientByWarehouseClientIds(warehouseClientIds);
    }

    /**
     * 删除仓库客户信息信息
     *
     * @param warehouseClientId 仓库客户信息主键
     * @return 结果
     */
    @Override
    public int deleteMpWarehouseClientByWarehouseClientId(Long warehouseClientId) {
        return mpWarehouseClientMapper.deleteMpWarehouseClientByWarehouseClientId(warehouseClientId);
    }
}
