package com.rich.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 【请填写功能名称】对象 ext_account
 *
 * <AUTHOR>
 * @date 2022-09-06
 */
public class BasAccount extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 银行账户ID
     */
    private Long bankAccId;


    /**
     * 银行账户代码
     */
    @Excel(name = "银行账户代码")
    private String bankAccCode;

    /**
     * 银行账户
     */
    @Excel(name = "银行账户")
    private String bankAccount;

    /**
     * 币种ID
     */
    @Excel(name = "币种ID")
    private String defaultCurrencyCode;

    private Long belongToCompany;

    private String belongToStaff;

    /**
     * 开户地
     */
    private String branchLocation;

    /**
     * 银行本名
     */
    @Excel(name = "银行本名")
    private String benefitLocalName;

    /**
     * 开户详细地址
     */
    @Excel(name = "银行地址")
    private String branchAddress;

    /**
     * 银行英文名
     */
    @Excel(name = "银行英文名")
    private String benefitEnName;

    /**
     * 银行英文地址
     */
    @Excel(name = "银行英文地址")
    private String accountAddressEnName;

    /**
     * 公司税号
     */
    @Excel(name = "公司税号")
    private String benefitTax;

    /**
     * SWIFT编码
     */
    @Excel(name = "SWIFT编码")
    private String swiftCode;

    /**
     * 国际汇款编码，用于非SWIFT的备用
     */
    @Excel(name = "国际汇款编码，用于非SWIFT的备用")
    private String intlExchangeCode;

    /**
     * 公账还是私账，默认公账(0:公账，1为私账)，如非财务人员，私账则不给别人用。
     */
    @Excel(name = "公账还是私账，默认公账(0:公账，1为私账)，如非财务人员，私账则不给别人用。")
    private String isOfficialAcc;

    /**
     * 银行卡优先级
     */
    @Excel(name = "银行卡优先级")
    private Long accountPriority;

    /**
     * 业务录入完成后锁定
     */
    @Excel(name = "业务录入完成后锁定")
    private String isLocked;

    /**
     * 经理确认
     */
    @Excel(name = "部门确认")
    private String salesConfirmed;

    private Long salesConfirmedId;

    /**
     * 经理确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "部门确认时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date salesConfirmedDate;

    /**
     * 财务确认
     */
    @Excel(name = "财务确认")
    private String accConfirmed;

    private Long accConfirmedId;

    /**
     * 财务确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "财务确认时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date accConfirmedDate;

    private RsStaff rsStaff;

    private ExtCompany company;

    private BasCurrency currency;

    private String accountQuery;

    //开户总行
    private String bankName;

    //开户支行
    private String bankBranchName;

    private String isMain;

    /**
     * 商务确认
     */
    @Excel(name = "商务确认")
    private String psaConfirmed;

    private Long psaConfirmedId;

    /**
     * 商务确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "商务确认时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date psaConfirmedDate;

    /**
     * 操作确认
     */
    @Excel(name = "操作确认")
    private String opConfirmed;

    private Long opConfirmedId;

    private String bankAccSummary;
    private String sqdBelongToCompanyCode;
    private String bankBranchLocation;
    private String bankBranchAddress;
    private String bankBranchAddressEnName;

    public String getBankBranchAddressEnName() {
        return bankBranchAddressEnName;
    }

    public void setBankBranchAddressEnName(String bankBranchAddressEnName) {
        this.bankBranchAddressEnName = bankBranchAddressEnName;
    }

    public String getBankBranchAddress() {
        return bankBranchAddress;
    }

    public void setBankBranchAddress(String bankBranchAddress) {
        this.bankBranchAddress = bankBranchAddress;
    }

    public String getBankBranchLocation() {
        return bankBranchLocation;
    }

    public void setBankBranchLocation(String bankBranchLocation) {
        this.bankBranchLocation = bankBranchLocation;
    }

    public String getSqdBelongToCompanyCode() {
        return sqdBelongToCompanyCode;
    }

    public void setSqdBelongToCompanyCode(String sqdBelongToCompanyCode) {
        this.sqdBelongToCompanyCode = sqdBelongToCompanyCode;
    }

    public String getBankAccCode() {
        return bankAccCode;
    }

    public void setBankAccCode(String bankAccCode) {
        this.bankAccCode = bankAccCode;
    }

    public String getBankAccSummary() {
        return bankAccSummary;
    }

    public void setBankAccSummary(String bankAccSummary) {
        this.bankAccSummary = bankAccSummary;
    }

    /**
     * 财务确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "操作确认时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date opConfirmedDate;

    public String getPsaConfirmed() {
        return psaConfirmed;
    }

    public void setPsaConfirmed(String psaConfirmed) {
        this.psaConfirmed = psaConfirmed;
    }

    public Long getPsaConfirmedId() {
        return psaConfirmedId;
    }

    public void setPsaConfirmedId(Long psaConfirmedId) {
        this.psaConfirmedId = psaConfirmedId;
    }

    public Date getPsaConfirmedDate() {
        return psaConfirmedDate;
    }

    public void setPsaConfirmedDate(Date psaConfirmedDate) {
        this.psaConfirmedDate = psaConfirmedDate;
    }

    public String getOpConfirmed() {
        return opConfirmed;
    }

    public void setOpConfirmed(String opConfirmed) {
        this.opConfirmed = opConfirmed;
    }

    public Long getOpConfirmedId() {
        return opConfirmedId;
    }

    public void setOpConfirmedId(Long opConfirmedId) {
        this.opConfirmedId = opConfirmedId;
    }

    public Date getOpConfirmedDate() {
        return opConfirmedDate;
    }

    public void setOpConfirmedDate(Date opConfirmedDate) {
        this.opConfirmedDate = opConfirmedDate;
    }

    public String getIsMain() {
        return isMain;
    }

    public void setIsMain(String isMain) {
        this.isMain = isMain;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankBranchName() {
        return bankBranchName;
    }

    public void setBankBranchName(String bankBranchName) {
        this.bankBranchName = bankBranchName;
    }

    public Long getSalesConfirmedId() {
        return salesConfirmedId;
    }

    public void setSalesConfirmedId(Long salesConfirmedId) {
        this.salesConfirmedId = salesConfirmedId;
    }

    public Long getAccConfirmedId() {
        return accConfirmedId;
    }

    public void setAccConfirmedId(Long accConfirmedId) {
        this.accConfirmedId = accConfirmedId;
    }

    public String getIsLocked() {
        return isLocked;
    }

    public void setIsLocked(String isLocked) {
        this.isLocked = isLocked;
    }

    public String getSalesConfirmed() {
        return salesConfirmed;
    }

    public void setSalesConfirmed(String salesConfirmed) {
        this.salesConfirmed = salesConfirmed;
    }

    public Date getSalesConfirmedDate() {
        return salesConfirmedDate;
    }

    public void setSalesConfirmedDate(Date salesConfirmedDate) {
        this.salesConfirmedDate = salesConfirmedDate;
    }

    public String getAccConfirmed() {
        return accConfirmed;
    }

    public void setAccConfirmed(String accConfirmed) {
        this.accConfirmed = accConfirmed;
    }

    public Date getAccConfirmedDate() {
        return accConfirmedDate;
    }

    public void setAccConfirmedDate(Date accConfirmedDate) {
        this.accConfirmedDate = accConfirmedDate;
    }

    public String getAccountQuery() {
        return accountQuery;
    }

    public void setAccountQuery(String accountQuery) {
        this.accountQuery = accountQuery;
    }

    public BasCurrency getCurrency() {
        return currency;
    }

    public void setCurrency(BasCurrency currency) {
        this.currency = currency;
    }

    public String getBranchLocation() {
        return branchLocation;
    }

    public void setBranchLocation(String branchLocation) {
        this.branchLocation = branchLocation;
    }

    public Long getBelongToCompany() {
        return belongToCompany;
    }

    public void setBelongToCompany(Long belongToCompany) {
        this.belongToCompany = belongToCompany;
    }

    public String getBelongToStaff() {
        return belongToStaff;
    }

    public void setBelongToStaff(String belongToStaff) {
        this.belongToStaff = belongToStaff;
    }

    public RsStaff getRsStaff() {
        return rsStaff;
    }

    public void setRsStaff(RsStaff rsStaff) {
        this.rsStaff = rsStaff;
    }

    public ExtCompany getCompany() {
        return company;
    }

    public void setCompany(ExtCompany company) {
        this.company = company;
    }

    public Long getBankAccId() {
        return bankAccId;
    }

    public void setBankAccId(Long bankAccId) {
        this.bankAccId = bankAccId;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getDefaultCurrencyCode() {
        return defaultCurrencyCode;
    }

    public void setDefaultCurrencyCode(String defaultCurrencyCode) {
        this.defaultCurrencyCode = defaultCurrencyCode;
    }

    public String getBenefitLocalName() {
        return benefitLocalName;
    }

    public void setBenefitLocalName(String benefitLocalName) {
        this.benefitLocalName = benefitLocalName;
    }

    public String getBranchAddress() {
        return branchAddress;
    }

    public void setBranchAddress(String branchAddress) {
        this.branchAddress = branchAddress;
    }

    public String getBenefitEnName() {
        return benefitEnName;
    }

    public void setBenefitEnName(String benefitEnName) {
        this.benefitEnName = benefitEnName;
    }

    public void setAccountAddressEnName(String accountAddressEnName) {
        this.accountAddressEnName = accountAddressEnName;
    }

    public String getAccountAddressEnName() {
        return accountAddressEnName;
    }

    public String getBenefitTax() {
        return benefitTax;
    }

    public void setBenefitTax(String benefitTax) {
        this.benefitTax = benefitTax;
    }

    public String getSwiftCode() {
        return swiftCode;
    }

    public void setSwiftCode(String swiftCode) {
        this.swiftCode = swiftCode;
    }

    public String getIntlExchangeCode() {
        return intlExchangeCode;
    }

    public void setIntlExchangeCode(String intlExchangeCode) {
        this.intlExchangeCode = intlExchangeCode;
    }

    public String getIsOfficialAcc() {
        return isOfficialAcc;
    }

    public void setIsOfficialAcc(String isOfficialAcc) {
        this.isOfficialAcc = isOfficialAcc;
    }

    public void setAccountPriority(Long accountPriority) {
        this.accountPriority = accountPriority;
    }

    public Long getAccountPriority() {
        return accountPriority;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("accountId", getBankAccId())
                .append("bankAccount", getBankAccount())
                .append("defaultCurrencyCode", getDefaultCurrencyCode())
                .append("benefitLocalName", getBenefitLocalName())
                .append("branchAddress", getBranchAddress())
                .append("benefitEnName", getBenefitEnName())
                .append("accountAddressEnName", getAccountAddressEnName())
                .append("benefitTax", getBenefitTax())
                .append("swiftCode", getSwiftCode())
                .append("intlExchangeCode", getIntlExchangeCode())
                .append("isOfficialAcc", getIsOfficialAcc())
                .append("accountPriority", getAccountPriority())
                .append("remark", getRemark())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
