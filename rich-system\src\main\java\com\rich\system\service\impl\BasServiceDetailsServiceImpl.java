package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.BasServiceDetails;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.BasServiceDetailsMapper;
import com.rich.system.service.BasServiceDetailsService;

/**
 * 服务明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@Service
public class BasServiceDetailsServiceImpl implements BasServiceDetailsService {
    @Autowired
    private BasServiceDetailsMapper basServiceDetailsMapper;

    /**
     * 查询服务明细
     *
     * @param serviceDetailsCode 服务明细主键
     * @return 服务明细
     */
    @Override
    public BasServiceDetails selectBasServiceDetailsByServiceDetailsCode(String serviceDetailsCode) {
        return basServiceDetailsMapper.selectBasServiceDetailsByServiceDetailsCode(serviceDetailsCode);
    }

    /**
     * 查询服务明细列表
     *
     * @param basServiceDetails 服务明细
     * @return 服务明细
     */
    @Override
    public List<BasServiceDetails> selectBasServiceDetailsList(BasServiceDetails basServiceDetails) {
        return basServiceDetailsMapper.selectBasServiceDetailsList(basServiceDetails);
    }

    /**
     * 新增服务明细
     *
     * @param basServiceDetails 服务明细
     * @return 结果
     */
    @Override
    public int insertBasServiceDetails(BasServiceDetails basServiceDetails) {
        return basServiceDetailsMapper.insertBasServiceDetails(basServiceDetails);
    }

    /**
     * 修改服务明细
     *
     * @param basServiceDetails 服务明细
     * @return 结果
     */
    @Override
    public int updateBasServiceDetails(BasServiceDetails basServiceDetails) {
        return basServiceDetailsMapper.updateBasServiceDetails(basServiceDetails);
    }

    /**
     * 修改服务明细状态
     *
     * @param basServiceDetails 服务明细
     * @return 服务明细
     */
    @Override
    public int changeStatus(BasServiceDetails basServiceDetails) {
        return basServiceDetailsMapper.updateBasServiceDetails(basServiceDetails);
    }

    /**
     * 批量删除服务明细
     *
     * @param serviceDetailsCodes 需要删除的服务明细主键
     * @return 结果
     */
    @Override
    public int deleteBasServiceDetailsByServiceDetailsCodes(String[] serviceDetailsCodes) {
        return basServiceDetailsMapper.deleteBasServiceDetailsByServiceDetailsCodes(serviceDetailsCodes);
    }

    /**
     * 删除服务明细信息
     *
     * @param serviceDetailsCode 服务明细主键
     * @return 结果
     */
    @Override
    public int deleteBasServiceDetailsByServiceDetailsCode(String serviceDetailsCode) {
        return basServiceDetailsMapper.deleteBasServiceDetailsByServiceDetailsCode(serviceDetailsCode);
    }
}
