package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.BasDocIssueType;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.system.mapper.BasDocIssueTypeMapper;
import com.rich.system.service.BasDocIssueTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 文件出单方式Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@Service
public class BasDocIssueTypeServiceImpl implements BasDocIssueTypeService {
    @Autowired
    private BasDocIssueTypeMapper basDocIssueTypeMapper;

    /**
     * 查询文件出单方式
     *
     * @param issueTypeId 文件出单方式主键
     * @return 文件出单方式
     */
    @Override
    public BasDocIssueType selectBasDocIssueTypeByIssueTypeId(Long issueTypeId) {
        return basDocIssueTypeMapper.selectBasDocIssueTypeByIssueTypeId(issueTypeId);
    }

    /**
     * 查询文件出单方式列表
     *
     * @param basDocIssueType 文件出单方式
     * @return 文件出单方式
     */
    @Override
    public List<BasDocIssueType> selectBasDocIssueTypeList(BasDocIssueType basDocIssueType) {
        return basDocIssueTypeMapper.selectBasDocIssueTypeList(basDocIssueType);
    }

    /**
     * 新增文件出单方式
     *
     * @param basDocIssueType 文件出单方式
     * @return 结果
     */
    @Override
    public int insertBasDocIssueType(BasDocIssueType basDocIssueType) {
        basDocIssueType.setCreateTime(DateUtils.getNowDate());
        basDocIssueType.setCreateBy(SecurityUtils.getUserId());
        return basDocIssueTypeMapper.insertBasDocIssueType(basDocIssueType);
    }

    /**
     * 修改文件出单方式
     *
     * @param basDocIssueType 文件出单方式
     * @return 结果
     */
    @Override
    public int updateBasDocIssueType(BasDocIssueType basDocIssueType) {
        basDocIssueType.setUpdateTime(DateUtils.getNowDate());
        basDocIssueType.setUpdateBy(SecurityUtils.getUserId());
        return basDocIssueTypeMapper.updateBasDocIssueType(basDocIssueType);
    }

    /**
     * 修改文件出单方式状态
     *
     * @param basDocIssueType 文件出单方式
     * @return 文件出单方式
     */
    @Override
    public int changeStatus(BasDocIssueType basDocIssueType) {
        return basDocIssueTypeMapper.updateBasDocIssueType(basDocIssueType);
    }

    /**
     * 批量删除文件出单方式
     *
     * @param issueTypeIds 需要删除的文件出单方式主键
     * @return 结果
     */
    @Override
    public int deleteBasDocIssueTypeByIssueTypeIds(Long[] issueTypeIds) {
        return basDocIssueTypeMapper.deleteBasDocIssueTypeByIssueTypeIds(issueTypeIds);
    }

    /**
     * 删除文件出单方式信息
     *
     * @param issueTypeId 文件出单方式主键
     * @return 结果
     */
    @Override
    public int deleteBasDocIssueTypeByIssueTypeId(Long issueTypeId) {
        return basDocIssueTypeMapper.deleteBasDocIssueTypeByIssueTypeId(issueTypeId);
    }
}
