<template>
  <view class="cargo-notification">
    <view class="header">
      <view class="title">
        <text>送货指示单</text>
        <text class="sub-title">(Cargo Entry Notification)</text>
      </view>
      <image class="logo" mode="aspectFit" src="/static/logo.png"></image>
    </view>

    <view class="notice-message">
      <text>Please fill full information to the cells carefully to ensure the safe and rapid delivery of your cargo.
      </text>
    </view>

    <uni-forms ref="uForm" :label-width="105" :model="formData" class="edit" label-position="left">
      <view class="form-table">
        <uni-forms-item label="Serial No.:" name="serialNo">
          <uni-easyinput v-model="formData.inboundSerialNo" disabled placeholder="自动生成"/>
        </uni-forms-item>
        <uni-forms-item label="Entry Code" required>
          <view class="flex-row">
            <view class="flex-1">
              <rich-search-select v-model="formData.clientCode" :disabled="!checkRole(['warehouse']) || isDisabled()"
                                  :localdata="clientOptions"
                                  @change="handleClientCodeChange"></rich-search-select>
            </view>
            <view class="flex-1">
              <uni-easyinput v-model="formData.clientRegion" disabled
                             placeholder="目的地"/>
            </view>
          </view>
        </uni-forms-item>
        <uni-forms-item label="Sub Code:" required>
          <rich-search-select v-model="formData.consigneeCode" :disabled="formType !== 'add'"
                              :keep-input-value="true" :localdata="consigneeOptions"
                              @change="handleConsigneeChange" @focus="loadConsigneeOptions"></rich-search-select>
        </uni-forms-item>
        <uni-forms-item label="Cnee Name:" required>
          <uni-easyinput v-model="formData.consigneeName" :disabled="formType !== 'add'"/>
        </uni-forms-item>
        <uni-forms-item label="Cnee Tel:" required>
          <uni-easyinput v-model="formData.consigneeTel" :disabled="formType !== 'add'"/>
        </uni-forms-item>

        <view class="cargo-items-section">
          <view class="section-title">Cargo Details</view>

          <!-- 货物明细 -->
          <uni-forms ref="cargoForm" :label-width="105" :model="currentCargoItem" :rules="cargoRules" class="edit"
                     label-position="left">
            <view class="form-table">
              <uni-forms-item label="Purchase Ref" name="purchaseNo">
                <uni-easyinput v-model="formData.purchaseNo" :disabled="isDisabled()"
                               placeholder="采购单号"/>
              </uni-forms-item>
              <uni-forms-item label="Mark" name="shippingMark">
                <uni-easyinput v-model="currentCargoItem.shippingMark" :disabled="isDisabled()"
                               placeholder="Shipping Mark"/>
              </uni-forms-item>

              <uni-forms-item label="CN Name" name="itemName" required>
                <uni-easyinput v-model="currentCargoItem.itemName" :disabled="isDisabled()"
                               placeholder="Cargo Chinese Name"/>
              </uni-forms-item>

              <uni-forms-item label="EN Name" name="itemEnName">
                <uni-easyinput v-model="currentCargoItem.itemEnName" :disabled="isDisabled()"
                               placeholder="Cargo English Name"/>
              </uni-forms-item>

              <uni-forms-item label="Goods Type" name="cargoNature">
                <!-- 多选框 (''普货'', ''大件'', ''鲜活'', ''危品'', ''冷冻'', ''标记'') -->
                <rich-search-select v-model="formData.cargoNature" :disabled="isDisabled()"
                                    :localdata="cargoNatureOptions" @change="onCargoNatureChange"></rich-search-select>
              </uni-forms-item>

              <uni-forms-item label="PKG" name="boxCount" required>
                <uni-easyinput v-model="currentCargoItem.boxCount" :disabled="isDisabled()"
                               placeholder="Packages Amount"
                               type="number" @input="calculateVolume; calculateTotalWeight"/>
              </uni-forms-item>
              <uni-forms-item label="Unit GW" name="singlePieceWeight">
                <uni-easyinput v-model="currentCargoItem.singlePieceWeight" :disabled="isDisabled()"
                               placeholder="Unit Gross Weight(kg)" type="digit" @input="calculateTotalWeight"/>
              </uni-forms-item>

              <uni-forms-item label="Total GW" name="unitGrossWeight">
                <uni-easyinput v-model="currentCargoItem.unitGrossWeight" :disabled="isDisabled()"
                               placeholder="自动计算，也可手动填写总毛重"
                               type="digit"/>
              </uni-forms-item>

              <uni-forms-item label="L*W*H(cm)" name="dimensions">
                <view class="dimension-container">
                  <uni-easyinput v-model="currentCargoItem.unitLength" :disabled="isDisabled()" placeholder="L"
                                 style="flex: 1; margin-right: 5px;" type="digit" @input="calculateVolume"/>
                  <text style="margin: 0 5px;">*</text>
                  <uni-easyinput v-model="currentCargoItem.unitWidth" :disabled="isDisabled()" placeholder="W"
                                 style="flex: 1; margin-right: 5px;" type="digit" @input="calculateVolume"/>
                  <text style="margin: 0 5px;">*</text>
                  <uni-easyinput v-model="currentCargoItem.unitHeight" :disabled="isDisabled()" placeholder="H"
                                 style="flex: 1;" type="digit" @input="calculateVolume"/>
                </view>
              </uni-forms-item>

              <uni-forms-item label="Volume" name="unitVolume">
                <uni-easyinput v-model="currentCargoItem.unitVolume" :disabled="isDisabled()"
                               placeholder="自动结算，也可手段填写总体积"/>
              </uni-forms-item>
            </view>
          </uni-forms>

          <view class="add-cargo-btn">
            <text class="total-text">Total: {{ totalCargoText }}</text>
          </view>

        </view>

        <uni-forms-item label="E-Entry Time:">
          <view class="datetime-picker-container">
            <picker :disabled="isDisabled()" :value="formData.entryDate1" mode="date" @change="onDateChange1">
              <view :class="['picker-item', 'picker-item-date', isDisabled() ? 'picker-disabled' : '']">
                {{ formData.entryDate1 || 'Date' }}
              </view>
            </picker>
            <picker :disabled="isDisabled()" :value="formData.entryTime1" end="24:00" mode="time"
                    start="00:00" @change="onTimeChange1">
              <view :class="['picker-item', 'picker-item-time', isDisabled() ? 'picker-disabled' : '']">
                {{ formData.entryTime1 || 'Time' }}
              </view>
            </picker>
            <button :class="['now-btn', isDisabled() ? 'btn-disabled' : '']" :disabled="isDisabled()"
                    @click="setCurrentTime1">Now
            </button>
          </view>
        </uni-forms-item>

        <uni-forms-item label="Sent By:" name="deliveryType">
          <uni-data-select v-model="formData.deliveryType" :disabled="isDisabled()"
                           :localdata="entryMethodOptions"></uni-data-select>
        </uni-forms-item>

        <!-- <uni-forms-item label="包装方式:" name="packageType">
          <rich-search-select v-model="formData.packageType" :localdata="packageTypeOptions"></rich-search-select>
        </uni-forms-item> -->

        <uni-forms-item label="Logistics Info:" name="logisticsInfo" required>
          <uni-easyinput v-model="formData.logisticsInfo" :disabled="isDisabled()" placeholder="请输入物流单号"/>
        </uni-forms-item>

        <uni-forms-item label="Supplier:" name="supplier" >
          <uni-easyinput v-model="formData.supplier" :disabled="isDisabled()" placeholder="请输入供应商名称"/>
        </uni-forms-item>

        <uni-forms-item label="Contact:" name="contractType" >
          <uni-easyinput v-model="formData.contractType" :disabled="isDisabled()" placeholder="请输入联系电话"/>
        </uni-forms-item>

        <!-- <uni-forms-item label="货物特征:" name="specialMark" required>
          <uni-easyinput v-model="formData.specialMark" placeholder="请输入特殊标记"/>
        </uni-forms-item> -->

        <uni-forms-item v-if="checkRole(['warehouse'])" label="Packed Into:" name="packageTo">
          <rich-search-select v-model="formData.packageTo" :disabled="isDisabled()" :localdata="packageOptions"
                              @change="onPackageSelect" @focus="loadPackageOptions"></rich-search-select>
        </uni-forms-item>

        <uni-forms-item label="Remarks:">
          <uni-easyinput v-model="formData.remarks" :disabled="isDisabled()" :maxlength="-1"
                         placeholder="如无特别注明,则默认为普货,无牌,无电,无危险性,不超长,不超重,非贵重物品,非易损。"
                         type="textarea"/>
        </uni-forms-item>

        <uni-forms-item label="Created By:">
          <uni-easyinput v-model="formData.createdUser" disabled/>
        </uni-forms-item>
      </view>
    </uni-forms>

    <view class="button-container">
      <view class="button-row">
        <button :class="{'btn-disabled': !consigneeInfo ||  isDisabled()}"
                :disabled="!consigneeInfo ||  isDisabled()"
                class="action-btn"
                type="primary" @click="submitForm">
          Send Entry Info to Warehouse Manager
        </button>
      </view>
      <view class="button-row">
        <button class="action-btn" type="primary" @click="shareQrImage">
          Create Entry QR and Sent to Driver
        </button>
      </view>
    </view>


    <view class="disclaimer">
      <text>特别提示：订单确认后，如需修改地址、改动数量、添加物品信息等，如果有责任，本公司不负责！</text>
    </view>
  </view>
</template>

<script>
import uniForms from '../components/uni-forms/uni-forms.vue'
import uniFormsItem from '../components/uni-forms-item/uni-forms-item.vue'
import uniEasyinput from '../components/uni-easyinput/uni-easyinput.vue'
import dayjs from 'dayjs';
import uniDataSelect from '../components/uni-data-select/uni-data-select.vue'
import richSearchSelect from '../components/rich-search-select/rich-search-select.vue'
import {checkRole} from '@/utils/permission'
import {addConsignee, getConsignee, getConsigneeList, updateConsignee} from '@/api/system/consignee'
import QRCode from '@/packageB/qrcode.js'
import {decrypt, encrypt} from "../../utils/common";
import {addInventory, deleteInventory, getPackageList, updateInventory} from '@/api/system/invenory'
import printerService from '@/utils/printer'
import {getClientList} from '@/api/system/client'
import printTemplate from '@/print/template/default-templates'
import {getToken} from '@/utils/auth'

export default {
  components: {
    uniForms,
    uniFormsItem,
    uniEasyinput,
    richSearchSelect,
    uniDataSelect
  },
  data() {
    return {
      cargoNatureOptions: [
        {value: '普货', text: '普货'},
        {value: '大件', text: '大件'},
        {value: '鲜活', text: '鲜活'},
        {value: '危品', text: '危品'},
        {value: '冷冻', text: '冷冻'},
        {value: '标记', text: '标记'}
      ],
      isPrinting: false,
      claimed: false,
      consigneeList: [],
      clientList: [],
      packageList: [],
      consigneeOptions: [],
      clientOptions: [],
      packageOptions: [],
      show: false,
      entryMethodShow: false,
      tempEntryMethod: null,
      consigneeInfo: {
        warehouse: '',
        contact: ''
      },
      formType: '',
      formData: {
        cargoNature: '',
        clientCode: '',
        cargoCode: '',
        destination: '',
        entryDate: '',
        entryDate1: '',
        entryTime: '',
        entryTime1: '',
        deliveryType: '快递公司',
        packageType: '纸箱',
        logisticsInfo: '',
        supplier: '',
        contractType: '',
        remarks: '',
        createdUser: this.$store.state.user.name,
        mpCargoDetails: [],
        // 添加表单中使用的其他字段
        inboundSerialNo: '',
        clientRegion: '',
        consigneeCode: '',
        consigneeName: '',
        consigneeTel: '',
        purchaseNo: '',
        totalBoxes: '',
        totalGrossWeight: '',
        totalVolume: '',
        packageTo: '',
        packageInto: '',
        recordStatus: '',
        cargoStatus: '',
        goodsStatus: '',
        inboundDate: '',
        estimatedArrivalTime: '',
        inventoryId: '',
        specialMark: '',
        consigneeId: '',
        // 目的地信息
        destinationCompanyName: '',
        destinationWarehouseAddress: '',
        destinationWarehouseContact: ''
      },
      entryMethodOptions: [
        {value: '客户自送', text: 'Brought by Customer'},
        {value: '工厂直送', text: 'Deliverid by Factory'},
        {value: '国内物流', text: 'Sent by Logisitics'},
        {value: '快递公司', text: 'Auto Express by O2O'}
      ],
      packageTypeOptions: [
        {value: '纸箱', text: 'cartonBox'},
        {value: '木箱', text: 'woodenCase'},
        {value: '托盘', text: 'pallet'},
        {value: '吨袋', text: 'bulkBag'}
      ],
      cargoNatureOptions: [
        {value: '普货', text: '普货'},
        {value: '大件', text: '大件'},
        {value: '鲜活', text: '鲜活'},
        {value: '危品', text: '危品'},
        {value: '冷冻', text: '冷冻'},
        {value: '标记', text: '标记'}
      ],
      rules: {
        supplier: {
          rules: [{required: true, errorMessage: '供应商名称/Supplier Name'}]
        },
        contactNumber: {
          rules: [{required: true, errorMessage: '联系方式/Supplier Tel'}]
        }
      },
      destinationCompany: '',
      destinationAddress: '',
      destinationContact: '',
      cargoDialogVisible: false,
      currentCargoItem: {
        description: '',
        itemName: '',
        boxCount: '',
        unitLength: '',
        unitWidth: '',
        unitHeight: '',
        singlePieceVolume: '',
        singlePieceWeight: '',
        unitGrossWeight: '',
        shippingMark: '',
        index: -1
      },
      cargoRules: {
        boxCount: {
          rules: [{
            required: true,
            errorMessage: '请输入件数'
          }, {
            format: 'number',
            errorMessage: '件数必须是数字'
          }]
        },
        unitLength: {
          rules: [{
            required: true,
            errorMessage: '请输入长度'
          }, {
            format: 'number',
            errorMessage: '长度必须是数字'
          }]
        },
        unitWidth: {
          rules: [{
            required: true,
            errorMessage: '请输入宽度'
          }, {
            format: 'number',
            errorMessage: '宽度必须是数字'
          }]
        },
        unitHeight: {
          rules: [{
            required: true,
            errorMessage: '请输入高度'
          }, {
            format: 'number',
            errorMessage: '高度必须是数字'
          }]
        },
        singlePieceWeight: {
          rules: [{
            required: true,
            errorMessage: '请输入单件重量'
          }, {
            format: 'number',
            errorMessage: '单件重量必须是数字'
          }]
        },
        unitGrossWeight: {
          rules: [{
            required: true,
            errorMessage: '请输入重量'
          }, {
            format: 'number',
            errorMessage: '重量必须是数字'
          }]
        }
      },
      totalCargoText: '',
      shareCanvasWidth: 0,
      shareCanvasHeight: 0,
    }
  },
  computed: {},
  watch: {
    'formData.mpCargoDetails': {
      handler(newVal) {
        // 货物明细变化时自动重新计算
        this.calculateTotalCargo();
      },
      deep: true // 深度监听数组内部属性的变化
    }
  },
  onLoad(options) {
    // 检查是否登录
    if (!getToken()) {
      // 所有平台环境都跳转到登录页，携带当前页面路径用于登录后跳转回来
      const currentPage = '/packageA/inventory/index'
      const redirectUrl = encodeURIComponent(currentPage + (options ? '?' + Object.keys(options).map(key => `${key}=${options[key]}`).join('&') : ''))
      this.$tab.reLaunch(`/pages/login?redirect=${redirectUrl}`)
      return
    }

    // 默认初始化收货人信息，确保表单提交按钮可用
    this.consigneeInfo = {
      warehouse: '',
      contact: ''
    };

    // 获取客户列表
    getClientList().then((res) => {
      this.clientList = res.rows || [];
      // 转换为uni-data-select需要的格式
      this.clientOptions = this.clientList.map(item => {
        return {
          value: item.clientCode, // 或者其他作为值的属性
          text: item.clientCode // 或者其他作为显示文本的属性
        }
      });

      // 在clientOptions加载完成后，如果是添加模式，再设置客户代码
      if (options && options.add) {
        this.formType = 'add'
        const client = this.$store.state.user.mpWarehouseClient
        // 使用$set确保响应式更新
        this.$set(this.formData, 'clientCode', client.clientCode)
        this.$set(this.formData, 'clientRegion', client.clientRegion)
        this.$set(this.formData, 'destinationCompanyName', client.destinationCompanyName)
        this.$set(this.formData, 'destinationWarehouseAddress', client.destinationWarehouseAddress)
        this.$set(this.formData, 'destinationWarehouseContact', client.destinationWarehouseContact)
        this.$set(this.formData, 'cargoStatus', 'Pre-entry')
        this.$set(this.formData, 'recordStatus', '1')

        // 初始化收货人信息
        this.consigneeInfo = {
          warehouse: client.destinationWarehouseAddress || '',
          contact: client.destinationWarehouseContact || ''
        };

        // 确保值更新后视图也更新
        this.$nextTick(() => {
          this.$forceUpdate()
        })
      }
    })

    // 如果是微信扫码进来的,获取收货人id
    if (options && options.q) {
      let url = decodeURIComponent(options.q)
      let query = this.GetWxMiniProgramUrlParam(url); //此处就是我们要获取的参数 json

      const consigneeId = decrypt(query.consigneeId.toString());
      console.log(consigneeId)
      if (consigneeId) {
        this.formType = 'enter'
        getConsignee(consigneeId).then((res) => {
          console.log(res.data)
          // 使用对象解构赋值更新表单数据
          this.$set(this.formData, 'clientCode', res.data.clientCode)
          this.$set(this.formData, 'consigneeCode', res.data.consigneeCode)
          this.$set(this.formData, 'clientRegion', res.data.clientRegion)
          this.$set(this.formData, 'consigneeName', res.data.consigneeName)
          this.$set(this.formData, 'consigneeTel', res.data.consigneeTel)

          // 确保mpWarehouseClient存在再赋值
          if (res.data.mpWarehouseClient) {
            this.$set(this.formData, 'destinationCompanyName', res.data.mpWarehouseClient.destinationCompanyName)
            this.$set(this.formData, 'destinationWarehouseAddress', res.data.mpWarehouseClient.destinationWarehouseAddress)
            this.$set(this.formData, 'destinationWarehouseContact', res.data.mpWarehouseClient.destinationWarehouseContact)
          }

          this.$set(this.formData, 'cargoStatus', 'Pre-entry')

          // 使用$nextTick确保DOM更新
          this.$nextTick(() => {
            // 设置默认时间为当前时间
            this.setCurrentTime1()
            // 手动触发表单更新
            this.$forceUpdate()

            uni.showToast({
              title: '收货人信息已加载',
              icon: 'success',
              duration: 2000
            })
          })
        }).catch(err => {
          console.error('获取收货人信息失败:', err)
          uni.showToast({
            title: '获取收货人信息失败',
            icon: 'none',
            duration: 2000
          })
        })
      }
    }

    // 初始化计算总计
    this.calculateTotalCargo();
  },
  onReady() {
    // 设置表单校验规则
    this.$refs.uForm.setRules(this.rules);
  },
  mounted() {
    // 确保在组件挂载完成后，所有绑定的数据都能正确显示
    this.$nextTick(() => {
      // 如果clientCode已设置，但rich-search-select组件未正确显示，则手动刷新
      if (this.formData.clientCode && this.clientOptions.length > 0) {
        // 找到匹配的选项以确认数据正确
        const matchedOption = this.clientOptions.find(opt => opt.value === this.formData.clientCode);
        if (matchedOption) {
          // 设置正确的值并强制更新视图
          this.$set(this.formData, 'clientCode', matchedOption.value);
          this.$forceUpdate();
        }
      }
    });
  },
  methods: {
    onCargoNatureChange(v) {
      this.formData.cargoNature = v
    },
    GetWxMiniProgramUrlParam(url) {
      let theRequest = {};
      if (url.indexOf("#") != -1) {
        const str = url.split("#")[1];
        const strs = str.split("&");
        for (let i = 0; i < strs.length; i++) {
          theRequest[strs[i].split("=")[0]] = decodeURI(strs[i].split("=")[1]);
        }
      } else if (url.indexOf("?") != -1) {
        const str = url.split("?")[1];
        const strs = str.split("&");
        for (let i = 0; i < strs.length; i++) {
          theRequest[strs[i].split("=")[0]] = decodeURI(strs[i].split("=")[1]);
        }
      }
      return theRequest;
    },
    deleteEntry() {
      uni.showModal({
        title: '提示',
        content: '确定要删除该入库单吗？',
        success: (res) => {
          if (res.confirm) {
            // 调用删除接口
            deleteInventory(this.formData).then(() => {
              uni.navigateBack();
            });
          }
        }
      });
    },
    loadPackageOptions() {
      const clientCode = this.formData.clientCode;
      if (clientCode) {
        getPackageList({clientCode}).then((packRes) => {
          this.packageList = packRes.data || [];
          this.packageOptions = this.packageList.map(item => {
            return {
              value: item.inventoryId,
              text: item.subOrderNo
            }
          });
        });
      }
    },
    isDisabled() {
      return this.formData.recordStatus === '5';
    },
    loadConsigneeOptions() {
      if (this.formData.clientCode) {
        // 如果已有客户代码，则根据客户代码加载对应的收货人列表
        getConsigneeList({clientCode: this.formData.clientCode}).then((res) => {
          this.consigneeList = res.rows || [];
          // 转换为搜索下拉框需要的格式
          this.consigneeOptions = this.consigneeList.map(item => {
            return {
              value: item.consigneeCode,
              text: item.consigneeCode
            }
          });
        }).catch(err => {
        });
      } else {
        uni.showToast({
          title: '请先选择客户代码',
          icon: 'none'
        });
      }
    },
    printLabel() {
      try {
        // 设置按钮为loading状态
        this.isPrinting = true;
        // 显示打印开始提示
        uni.showToast({
          title: '开始打印标签...',
          icon: 'loading',
          duration: 1000
        });


        // 使用hardcode的模板数据直接打印，调整为76×130规格
        const template = printTemplate

        // 处理地址分割
        let cargoAddress1 = '';
        let cargoAddress2 = '';
        const address = this.formData.destinationWarehouseAddress ? this.formData.destinationWarehouseAddress : '';
        if (address.length > 30) {
          cargoAddress1 = address.substring(0, 30);
          cargoAddress2 = address.substring(30);
        } else {
          cargoAddress1 = address;
          cargoAddress2 = '';
        }
        const data = {
          companyName: this.formData.destinationCompanyName ? this.formData.destinationCompanyName : '',
          cargoNumber: 'NODAT-10071',
          cargoName: 'CYNTHSCARE',
          cargoPhone: this.formData.destinationWarehouseContact ? this.formData.destinationWarehouseContact : '',
          cargoAddress1: cargoAddress1,
          cargoAddress2: cargoAddress2,
          inboundNo: this.formData.inboundSerialNo ? this.formData.inboundSerialNo : '',
          clientCode: this.formData.clientCode ? this.formData.clientCode : '',
          inboundSerialNo: this.formData.inboundSerialNo ? this.formData.inboundSerialNo : '',
          consigneeCode: this.formData.consigneeCode ? this.formData.consigneeCode : '',
          consigneeName: this.formData.consigneeName ? this.formData.consigneeName : '',
          consigneeTel: this.formData.consigneeTel ? this.formData.consigneeTel : '',
          clientRegion: this.formData.clientRegion ? this.formData.clientRegion : '',
          specialMark: this.formData.specialMark ? this.formData.specialMark : '',
          totalBoxes: this.formData.totalBoxes ? this.formData.totalBoxes : '',
          inboundDate: this.formData.inboundDate ? this.formData.inboundDate : '',
          logisticsInfo: this.formData.logisticsInfo ? this.formData.logisticsInfo : '',
        }

        // 使用模板直接打印
        printerService.printLabel({
          template: template, // 直接传递模板对象
          data: data, // 传递数据
          onSuccess: (message) => {
            // 打印成功后恢复按钮状态
            this.isPrinting = false;
            printerService.showToast(message || '打印成功');
          },
          onError: (message) => {
            // 打印失败后恢复按钮状态
            this.isPrinting = false;
            printerService.showToast(message || '打印失败');
          }
        })
      } catch (error) {
        // 发生错误时恢复按钮状态
        this.isPrinting = false;
        printerService.showToast('打印发生错误: ' + error.message);
      }
    },
    generateQRCode(text) {
      // 生成二维码
      let qrcode = new QRCode('canvas', {
        usingIn: this.formData.inventoryId,
        text: this.formData.inventoryId.toString(),
        width: 140,  // 对应280rpx (rpx与px的比例约为2:1)
        height: 140, // 对应280rpx (rpx与px的比例约为2:1)
        colorDark: "black",
        colorLight: "white",
        correctLevel: QRCode.CorrectLevel.H,
        // 添加logo图片
        logo: '/static/logo.png', // 使用红色logo图片
        logoWidth: 30, // logo宽度
        logoHeight: 30, // logo高度
        logoBackgroundColor: '#ffffff', // logo背景色为白色
      });
      // 二维码内容
      const encryptedId = encrypt('inventory' + '-' + this.formData.inventoryId);
      qrcode.makeCode(encryptedId)

      // 添加长按保存图片功能
      const that = this;
      uni.getSystemInfo({
        success: function (res) {
          // 在不同平台使用不同的事件绑定方式
          let canvasContext = uni.createCanvasContext('canvas', that);

          // 监听长按事件
          setTimeout(() => {
            const query = uni.createSelectorQuery().in(that);
            query.select('.ewm').boundingClientRect(data => {
              that.canvasWidth = data.width;
              that.canvasHeight = data.height;

              // 二维码生成后，绘制整个区域到shareCanvas
              that.drawEntireQrArea();
            }).exec();
          }, 500);
        }
      });
    },
    save() {
      const that = this;
      uni.canvasToTempFilePath({
        canvasId: 'canvas',
        success: function (res) {
          uni.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: function () {
              uni.showToast({
                title: '二维码已保存到相册',
                icon: 'success'
              });
            },
            fail: function (err) {
              console.error('保存失败:', err);
              if (err.errMsg.indexOf('auth deny') !== -1) {
                uni.showModal({
                  title: '提示',
                  content: '请授权保存图片到相册的权限',
                  success: function (res) {
                    if (res.confirm) {
                      uni.openSetting();
                    }
                  }
                });
              } else {
                uni.showToast({
                  title: '保存失败',
                  icon: 'none'
                });
              }
            }
          });
        },
        fail: function (err) {
          console.error('生成临时文件失败:', err);
          uni.showToast({
            title: '生成图片失败',
            icon: 'none'
          });
        }
      }, that);
    },
    handleConsigneeChange(value) {
      if (value) {
        // 在consigneeList中查找选中的收货人信息
        const selectedConsignee = this.consigneeList.find(item => item.consigneeCode === value);
        if (selectedConsignee) {
          // 根据选择的收货人信息填充表单的其他字段
          // this.formData.clientCode = selectedConsignee.clientCode || '';
          // this.formData.clientRegion = selectedConsignee.clientRegion || '';
          // this.formData.destinationCompanyName = selectedConsignee.mpWarehouseClient.destinationCompanyName || '';
          // this.formData.destinationWarehouseAddress = selectedConsignee.mpWarehouseClient.destinationWarehouseAddress || '';
          // this.formData.destinationWarehouseContact = selectedConsignee.mpWarehouseClient.destinationWarehouseContact || '';
          // 根据需要添加其他字段
          this.formData.consigneeName = selectedConsignee.consigneeName || '';
          this.formData.consigneeTel = selectedConsignee.consigneeTel || '';
        } else {
          this.formData.consigneeName = '';
          this.formData.consigneeTel = '';
        }
      }
    },
    handleClientCodeChange(clientCode) {
      // 选择了客户代码,自动填写地区
      if (clientCode) {
        getConsigneeList({clientCode}).then((res) => {
          this.consigneeList = res.rows || [];
          if (res.rows.length > 0) {
            // 尝试找到匹配的客户
            const matchClient = res.rows.find(item => item.clientCode === clientCode);
            if (matchClient) {
              // 如果找到匹配的客户，确保使用$set更新以保证响应式
              this.$set(this.formData, 'clientRegion', matchClient.clientRegion || '');
            }
          }

          // 转换为uni-data-select需要的格式
          this.consigneeOptions = this.consigneeList.map(item => {
            return {
              value: item.consigneeCode,
              text: item.consigneeCode
            }
          });
        })

        // 从clientList中查找匹配的客户
        const client = this.clientList.find(item => item.clientCode === clientCode);
        if (client) {
          // 使用$set确保Vue能跟踪这些变化
          this.$set(this.formData, 'clientCode', client.clientCode || '');
          this.$set(this.formData, 'clientRegion', client.clientRegion || '');
          this.$set(this.formData, 'destinationCompanyName', client.destinationCompanyName || '');
          this.$set(this.formData, 'destinationWarehouseAddress', client.destinationWarehouseAddress || '');
          this.$set(this.formData, 'destinationWarehouseContact', client.destinationWarehouseContact || '');

          // 确保UI更新
          this.$nextTick(() => {
            this.$forceUpdate();
          });
        }
      }
    },
    checkRole,
    calculateTotalCargo() {
      const validItems = this.formData.mpCargoDetails;
      let totalVolume = 0;
      let totalWeight = 0;
      let totalBoxes = 0;
      let totalGrossWeight = 0;

      validItems.forEach(item => {
        const boxes = parseInt(item.boxCount) || 0;
        const volume = parseFloat(item.unitVolume) || 0;
        const weight = parseFloat(item.singlePieceWeight) || 0;
        const grossWeight = parseFloat(item.unitGrossWeight) || 0;

        totalBoxes += boxes;
        totalVolume += volume;
        totalWeight += weight * boxes;
        totalGrossWeight += grossWeight;
      });

      // 格式化为最多2位小数
      totalVolume = totalVolume.toFixed(4);
      totalWeight = totalWeight.toFixed(2);

      this.formData.totalBoxes = totalBoxes;
      this.formData.totalGrossWeight = totalGrossWeight;
      this.formData.totalVolume = totalVolume;

      this.totalCargoText = `${totalBoxes}CTN / ${totalGrossWeight}KG / ${totalVolume}CBM`;
    },
    calculateVolume() {
      if (this.currentCargoItem.unitLength &&
          this.currentCargoItem.unitWidth &&
          this.currentCargoItem.unitHeight) {
        // 计算单件体积(立方米)：长x宽x高(cm) / 1000000
        const volume = (
            parseFloat(this.currentCargoItem.unitLength) *
            parseFloat(this.currentCargoItem.unitWidth) *
            parseFloat(this.currentCargoItem.unitHeight)
        ) / 1000000;

        // 格式化为最多3位小数
        this.currentCargoItem.unitVolume = volume.toFixed(4);
        this.formData.unitVolume = volume.toFixed(4);
      }
    },
    openEditCargoDialog(index) {
      // 深拷贝对象，避免直接引用
      this.currentCargoItem = {
        description: this.formData.mpCargoDetails[index].description || '',
        itemName: this.formData.mpCargoDetails[index].itemName || '',
        boxCount: this.formData.mpCargoDetails[index].boxCount || '',
        unitLength: this.formData.mpCargoDetails[index].unitLength || '',
        unitWidth: this.formData.mpCargoDetails[index].unitWidth || '',
        unitHeight: this.formData.mpCargoDetails[index].unitHeight || '',
        singlePieceVolume: this.formData.mpCargoDetails[index].singlePieceVolume || '',
        singlePieceWeight: this.formData.mpCargoDetails[index].singlePieceWeight || '',
        unitGrossWeight: this.formData.mpCargoDetails[index].unitGrossWeight || '',
        shippingMark: this.formData.mpCargoDetails[index].shippingMark || '',
        index: index
      };
      this.cargoDialogVisible = true;
    },
    closeCargoDialog() {
      this.cargoDialogVisible = false;
      // 重置当前编辑的货物数据
      this.currentCargoItem = {
        description: '',
        itemName: '',
        boxCount: '',
        unitLength: '',
        unitWidth: '',
        unitHeight: '',
        singlePieceVolume: '',
        singlePieceWeight: '',
        unitGrossWeight: '',
        shippingMark: '',
        index: -1
      };
    },
    validateAndSaveCargoItem() {
      // 先进行体积计算确保数据最新
      this.calculateVolume();
      this.calculateTotalWeight();

      if (this.checkRole(['warehouse'])) {
        // 仓管权限，跳过表单校验
        if (this.currentCargoItem.index !== -1) {
          // 更新已有货物，保留原有属性
          const originalItem = this.formData.mpCargoDetails[this.currentCargoItem.index];
          this.formData.mpCargoDetails[this.currentCargoItem.index] = {
            ...originalItem, // 保留原有属性
            description: this.currentCargoItem.description,
            itemName: this.currentCargoItem.itemName,
            boxCount: this.currentCargoItem.boxCount,
            unitLength: this.currentCargoItem.unitLength,
            unitWidth: this.currentCargoItem.unitWidth,
            unitHeight: this.currentCargoItem.unitHeight,
            singlePieceVolume: this.currentCargoItem.singlePieceVolume,
            singlePieceWeight: this.currentCargoItem.singlePieceWeight,
            unitGrossWeight: this.currentCargoItem.unitGrossWeight,
            shippingMark: this.currentCargoItem.shippingMark
          };
        } else {
          // 添加新货物
          this.formData.mpCargoDetails.push({
            description: this.currentCargoItem.description,
            itemName: this.currentCargoItem.itemName,
            boxCount: this.currentCargoItem.boxCount,
            unitLength: this.currentCargoItem.unitLength,
            unitWidth: this.currentCargoItem.unitWidth,
            unitHeight: this.currentCargoItem.unitHeight,
            singlePieceVolume: this.currentCargoItem.singlePieceVolume,
            singlePieceWeight: this.currentCargoItem.singlePieceWeight,
            unitGrossWeight: this.currentCargoItem.unitGrossWeight,
            shippingMark: this.currentCargoItem.shippingMark
          });
        }

        // 手动触发重新计算总计
        this.calculateTotalCargo();

        this.cargoDialogVisible = false;
      } else {
        // 非仓管权限，需要表单校验
        this.$refs.cargoForm.validate().then(valid => {
          if (valid) {
            if (this.currentCargoItem.index !== -1) {
              // 更新已有货物，保留原有属性
              const originalItem = this.formData.mpCargoDetails[this.currentCargoItem.index];
              this.formData.mpCargoDetails[this.currentCargoItem.index] = {
                ...originalItem, // 保留原有属性
                description: this.currentCargoItem.description,
                itemName: this.currentCargoItem.itemName,
                boxCount: this.currentCargoItem.boxCount,
                unitLength: this.currentCargoItem.unitLength,
                unitWidth: this.currentCargoItem.unitWidth,
                unitHeight: this.currentCargoItem.unitHeight,
                singlePieceVolume: this.currentCargoItem.singlePieceVolume,
                singlePieceWeight: this.currentCargoItem.singlePieceWeight,
                unitGrossWeight: this.currentCargoItem.unitGrossWeight,
                shippingMark: this.currentCargoItem.shippingMark
              };
            } else {
              // 添加新货物
              this.formData.mpCargoDetails.push({
                description: this.currentCargoItem.description,
                itemName: this.currentCargoItem.itemName,
                boxCount: this.currentCargoItem.boxCount,
                unitLength: this.currentCargoItem.unitLength,
                unitWidth: this.currentCargoItem.unitWidth,
                unitHeight: this.currentCargoItem.unitHeight,
                singlePieceVolume: this.currentCargoItem.singlePieceVolume,
                singlePieceWeight: this.currentCargoItem.singlePieceWeight,
                unitGrossWeight: this.currentCargoItem.unitGrossWeight,
                shippingMark: this.currentCargoItem.shippingMark
              });
            }

            // 手动触发重新计算总计
            this.calculateTotalCargo();

            this.cargoDialogVisible = false;
          }
        }).catch(err => {
        });
      }
    },
    loadConsigneeInfo(code) {
      // 这里应该调用API获取收货人信息
      // 模拟数据
      this.consigneeInfo = {
        warehouse: '佛山重水镇大步沿江路3号力进物流园B15-18仓',
        contact: '户小姐 18922752986'
      };
      this.formData.destination = '加纳（GHANA）';
      this.destinationCompany = 'Integrity Link Logistics';
      this.destinationAddress = 'Spintex Road Endpoint Homeopathic clinic, Ghana';
      this.destinationContact = '+233 *********; +8613265121207';

      // 设置默认时间为当前时间
      const now = dayjs();
      this.formData.entryDate = now.format('YYYY-MM-DD');
      // 设置时间为当前时间，取整到半小时
      const minute = Math.floor(now.minute() / 30) * 30;
      this.formData.entryTime = now.hour().toString().padStart(2, '0') + ':' + minute.toString().padStart(2, '0');
      this.updateEntryTime();
    },
    updateCargoItem(index) {
      // 如果用户正在输入最后一项，自动添加新的空行
      if (index === this.formData.mpCargoDetails.length - 1 && this.formData.mpCargoDetails[index].description.trim() !== '') {
        this.formData.mpCargoDetails.push({description: ''});
      }
    },
    openDatePicker() {
      this.show = true;
    },

    closeDatePicker() {
      this.show = false;
    },


    onPackageSelect(e) {
      const selected = this.packageList.filter(item => item.inventoryId === e)[0]
      if (selected) {
        this.formData.packageTo = e; // 确保正确设置packageTo值
        this.formData.packageInto = selected.inboundSerialNo
        console.log('选择了打包箱:', e, '对应的inboundSerialNo:', selected.inboundSerialNo);
      }
    },
    onDateChange(e) {
      this.formData.entryDate = e.detail.value;
      this.updateEntryTime();
    },
    onDateChange1(e) {
      this.formData.entryDate1 = e.detail.value;
      this.updateEntryTime();
    },

    onTimeChange(e) {
      this.formData.entryTime = e.detail.value;
      this.updateEntryTime();
    },
    onTimeChange1(e) {
      this.formData.entryTime1 = e.detail.value;
      this.updateEntryTime();
    },

    updateEntryTime() {
      if (this.formData.entryDate && this.formData.entryTime) {
        this.formData.estimatedArrivalTime = `${this.formData.entryDate} ${this.formData.entryTime}:00`;
      }
    },
    updateEntryTime1() {
      if (this.formData.entryDate1 && this.formData.entryTime1) {
        this.formData.inboundDate = `${this.formData.entryDate1} ${this.formData.entryTime1}:00`;
      }
    },

    openEntryMethodPicker() {
      this.entryMethodShow = true;
    },

    closeEntryMethodPicker() {
      this.entryMethodShow = false;
    },

    onEntryMethodConfirm(e) {
      const selectedItem = e.detail.value;
      this.formData.entryMethod = selectedItem.value || selectedItem.text;
      this.entryMethodShow = false;
    },
    unlockForm() {
      this.formData.recordStatus = '4'
    },
    submitForm(type) {
      // 确保日期时间格式正确
      this.updateEntryTime();
      this.updateEntryTime1();
      const that = this;
      // 检查收货人代码是否在现有列表中
      const consigneeExists = this.consigneeList.some(item => item.consigneeCode === this.formData.consigneeCode);
      // 需要自动新增收货人条件：代码不存在，且客户代码、收货人名称、收货人电话均已填写
      if (!consigneeExists && this.formData.clientCode && this.formData.consigneeCode && this.formData.consigneeName && this.formData.consigneeTel) {
        // 自动新增收货人
        const newConsignee = {
          clientCode: this.formData.clientCode,
          consigneeCode: this.formData.consigneeCode,
          consigneeName: this.formData.consigneeName,
          consigneeTel: this.formData.consigneeTel
        };
        addConsignee(newConsignee).then(res => {
          if (res.code === 200) {
            uni.showToast({title: '已自动新增收货人', icon: 'success'});
            // 新增成功后，刷新收货人列表并继续提交
            getConsigneeList({clientCode: this.formData.clientCode}).then((res) => {
              this.consigneeList = res.rows || [];
              this.consigneeOptions = this.consigneeList.map(item => ({
                value: item.consigneeCode,
                text: item.consigneeCode
              }));
              // 继续原有提交逻辑
              that._doSubmitForm(type);
            });
          } else {
            uni.showToast({title: '收货人新增失败', icon: 'none'});
          }
        }).catch(() => {
          uni.showToast({title: '收货人新增失败', icon: 'none'});
        });
        return;
      }

      // 检查收货人信息是否有修改
      if (consigneeExists && this.formData.consigneeCode) {
        const existingConsignee = this.consigneeList.find(item => item.consigneeCode === this.formData.consigneeCode);
        if (existingConsignee &&
            (existingConsignee.consigneeName !== this.formData.consigneeName ||
                existingConsignee.consigneeTel !== this.formData.consigneeTel)) {
          // 收货人信息有修改，询问用户是否更新
          uni.showModal({
            title: '提示',
            content: '检测到收货人信息已修改，是否更新收货人信息？',
            success: (res) => {
              if (res.confirm) {
                // 用户确认更新收货人信息
                const updatedConsignee = {
                  consigneeId: existingConsignee.consigneeId,
                  clientCode: this.formData.clientCode,
                  consigneeCode: this.formData.consigneeCode,
                  consigneeName: this.formData.consigneeName,
                  consigneeTel: this.formData.consigneeTel
                };
                updateConsignee(updatedConsignee).then(res => {
                  if (res.code === 200) {
                    uni.showToast({title: '收货人信息已更新', icon: 'success'});
                    // 更新成功后，刷新收货人列表并继续提交
                    getConsigneeList({clientCode: this.formData.clientCode}).then((res) => {
                      this.consigneeList = res.rows || [];
                      this.consigneeOptions = this.consigneeList.map(item => ({
                        value: item.consigneeCode,
                        text: item.consigneeCode
                      }));
                      // 继续原有提交逻辑
                      that._doSubmitForm(type);
                    });
                  } else {
                    uni.showToast({title: '收货人更新失败', icon: 'none'});
                    // 即使更新失败也继续提交
                    that._doSubmitForm(type);
                  }
                }).catch(() => {
                  uni.showToast({title: '收货人更新失败', icon: 'none'});
                  // 即使更新失败也继续提交
                  that._doSubmitForm(type);
                });
              } else {
                // 用户取消更新，使用原有收货人信息
                that.formData.consigneeName = existingConsignee.consigneeName;
                that.formData.consigneeTel = existingConsignee.consigneeTel;
                that._doSubmitForm(type);
              }
            }
          });
          return;
        }
      }

      // 正常提交
      this._doSubmitForm(type);
    },
    // 提交表单的原有逻辑，抽出为独立方法
    _doSubmitForm(type) {
      const that = this;

      // 处理货物明细
      this.formData.mpCargoDetails = [this.currentCargoItem];

      // 计算货物明细中的总数据
      let totalBoxCount = 0;
      let totalGrossWeight = 0;
      let totalVolume = 0;

      // 遍历货物明细，计算总和
      this.formData.mpCargoDetails.forEach(item => {
        totalBoxCount += parseInt(item.boxCount) || 0;
        totalGrossWeight += parseFloat(item.unitGrossWeight) || 0;
        totalVolume += (parseFloat(item.unitVolume)) || 0;
      });

      // 保存到formData中
      this.formData.totalBoxes = totalBoxCount;
      this.formData.totalGrossWeight = totalGrossWeight.toFixed(2);
      this.formData.totalVolume = totalVolume.toFixed(4);

      if (this.checkRole(['warehouse'])) {
        // 仓管权限，跳过表单校验

        // 如果是未完善的货物,更新后自动为已完善
        if (this.formData.recordStatus === '3') {
          this.formData.recordStatus = '4'
        }

        // 如果有客户代码为空则为未知客户
        if (!this.formData.clientCode) {
          this.formData.recordStatus = '1'
        }
        if (this.formData.inventoryId) {
          // 更新
          console.log(this.formData)
          updateInventory(this.formData).then(res => {
            uni.showToast({
              title: '保存成功',
              icon: 'success'
            });
          });
        } else {
          // 新增(仓管扫到未知快递单号进来新增)
          // 货物默认进仓
          this.formData.cargoStatus = 'Pre-entry'
          this.formData.goodsStatus = '预录入'

          this.formData.consigneeId = this.$store.state.user.id;
          addInventory(this.formData).then(res => {
            console.log(res.data)
            that.formData = res.data
            if (that.formData.inboundDate) {
              const inboundDateTime = new Date(that.formData.inboundDate);
              if (!isNaN(inboundDateTime.getTime())) {
                const year = inboundDateTime.getFullYear();
                const month = String(inboundDateTime.getMonth() + 1).padStart(2, '0');
                const day = String(inboundDateTime.getDate()).padStart(2, '0');
                that.formData.entryDate = `${year}-${month}-${day}`;

                const hours = String(inboundDateTime.getHours()).padStart(2, '0');
                const minutes = String(inboundDateTime.getMinutes()).padStart(2, '0');
                that.formData.entryTime = `${hours}:${minutes}`;
              }
            }
            if (that.formData.estimatedArrivalTime) {
              const inboundDateTime = new Date(that.formData.estimatedArrivalTime);
              if (!isNaN(inboundDateTime.getTime())) {
                const year = inboundDateTime.getFullYear();
                const month = String(inboundDateTime.getMonth() + 1).padStart(2, '0');
                const day = String(inboundDateTime.getDate()).padStart(2, '0');
                that.formData.entryDate1 = `${year}-${month}-${day}`;

                const hours = String(inboundDateTime.getHours()).padStart(2, '0');
                const minutes = String(inboundDateTime.getMinutes()).padStart(2, '0');
                that.formData.entryTime1 = `${hours}:${minutes}`;
              }
            }
            uni.showToast({
              title: '新增成功',
              icon: 'success'
            });
          });
        }
      } else {
        // 非仓管权限，需要表单校验
        this.$refs.uForm.validate().then(valid => {
          if (valid) {
            // 如果有客户代码为空则为未知客户
            if (!this.formData.clientCode) {
              this.formData.recordStatus = '1'
            }
            if (this.formData.inventoryId) {
              // 更新
              updateInventory(this.formData).then(res => {
                uni.showToast({
                  title: '保存成功',
                  icon: 'success'
                });
              });
            } else {
              // 新增(仓管扫到未知快递单号进来新增)
              // 货物默认进仓
              this.formData.cargoStatus = 'Pre-entry'

              this.formData.consigneeId = this.$store.state.user.id;
              addInventory(this.formData).then(res => {
                that.formData = res.data
                uni.showToast({
                  title: '新增成功',
                  icon: 'success'
                });
              });
            }
          }
        }).catch(err => {
        });
      }
    },

    addNewCargoItem() {
      this.currentCargoItem = {
        description: '',
        itemName: '',
        boxCount: '',
        unitLength: '',
        unitWidth: '',
        unitHeight: '',
        singlePieceVolume: '',
        singlePieceWeight: '',
        unitGrossWeight: '',
        shippingMark: '',
        index: -1
      };
      this.cargoDialogVisible = true;
    },

    deleteCargoItem(index) {
      uni.showModal({
        title: '提示',
        content: '确定要删除这条货物明细吗？',
        success: (res) => {
          if (res.confirm) {
            // 标记为已删除，或直接从数组中移除
            this.formData.mpCargoDetails.splice(index, 1);

            // 如果删除后没有货物，添加一个空的
            if (this.formData.mpCargoDetails.length === 0) {
              this.formData.mpCargoDetails.push({
                description: '',
                itemName: '',
                boxCount: '',
                unitLength: '',
                unitWidth: '',
                unitHeight: '',
                singlePieceVolume: '',
                singlePieceWeight: '',
                unitGrossWeight: '',
                shippingMark: ''
              });
            }

            // 重新计算总计
            this.calculateTotalCargo();
          }
        }
      });
    },
    formatCargoDisplay(item) {
      // 安全地格式化货物信息，避免undefined或null引起的问题
      const shippingMark = item.shippingMark || '';
      const itemName = item.itemName || '';
      const boxCount = item.boxCount || '';
      const unitLength = item.unitLength || '';
      const unitWidth = item.unitWidth || '';
      const unitHeight = item.unitHeight || '';
      const singlePieceVolume = item.singlePieceVolume || '';
      const singlePieceWeight = item.singlePieceWeight || '';
      const unitGrossWeight = item.unitGrossWeight || '';

      return `${shippingMark}/${itemName}/${boxCount}/${unitLength}*${unitWidth}*${unitHeight}/${singlePieceVolume}CBM/${unitGrossWeight}KG`;
    },
    formatDimensions(item) {
      if (item.unitLength && item.unitWidth && item.unitHeight) {
        return `${item.unitLength}×${item.unitWidth}×${item.unitHeight}`;
      }
      return '-';
    },
    calculateTotalWeight() {
      if (this.currentCargoItem.singlePieceWeight && this.currentCargoItem.boxCount) {
        // 计算总重量 = 单件重量 × 件数
        const totalWeight =
            parseFloat(this.currentCargoItem.singlePieceWeight) *
            parseFloat(this.currentCargoItem.boxCount);

        // 格式化为最多2位小数
        this.currentCargoItem.unitGrossWeight = totalWeight.toFixed(2);
        this.formData.totalGrossWeight = totalWeight.toFixed(2);
      }
    },
    setCurrentTime() {
      // 设置为当前时间
      const now = dayjs();
      this.formData.entryDate = now.format('YYYY-MM-DD');
      // 使用准确的当前时间
      this.formData.entryTime = now.format('HH:mm');
      this.updateEntryTime();

      uni.showToast({
        title: '已设置为当前时间',
        icon: 'none'
      });
    },
    setCurrentTime1() {
      // 设置为当前时间
      const now = dayjs();
      this.formData.entryDate1 = now.format('YYYY-MM-DD');
      // 使用准确的当前时间
      this.formData.entryTime1 = now.format('HH:mm');
      this.updateEntryTime1();

      uni.showToast({
        title: '已设置为当前时间',
        icon: 'none'
      });
    },
    debugFormData() {
      console.log('当前表单数据:', {
        packageTo: this.formData.packageTo,
        packageInto: this.formData.packageInto
      });
      console.log('当前打包箱列表:', this.packageOptions);

      // 查找匹配项
      if (this.formData.packageInto && this.packageList.length > 0) {
        const matchedPackage = this.packageList.find(item =>
            item.inboundSerialNo === this.formData.packageInto
        );
        console.log('根据packageInto找到对应项:', matchedPackage);
      }
    },

    // 打印二维码和详细信息
    printQrCode() {
      if (this.isPrinting) {
        return;
      }

      this.isPrinting = true;
      uni.showLoading({
        title: '准备打印...',
        mask: true
      });

      try {
        // 准备打印数据
        const printData = {
          cargoCode: this.formData.cargoCode || 'PreRS.250412345',
          destination: this.formData.destination || 'NODAT - 10071',
          supplier: this.formData.supplier || 'CYNTHSCARE',
          location: `Guangzhou- ${this.formData.destinationRegion || 'Ghana'}`,
          address: (this.destinationAddress || '佛山里水沿江路3号力进物流园B15仓') + (this.formData.clientCode + '-' + this.formData.consigneeCode),
          contact: this.destinationContact || '卢小姐 18922752986',
          serialNo: this.formData.inboundSerialNo || ''
        };

        // 获取二维码图片
        uni.canvasToTempFilePath({
          canvasId: 'canvas',
          success: (res) => {
            // 设置打印模板
            const template = {
              width: '80mm',
              height: 'auto',
              padding: '5mm',
              template: `
                <div style="display:flex;flex-direction:row;border:1px solid #ddd;">
                  <div style="width:100px;height:100px;padding:5px;border-right:1px solid #ddd;">
                    <img src="${res.tempFilePath}" style="width:90px;height:90px;" />
                  </div>
                  <div style="flex:1;padding:10px;">
                    <div style="font-size:14px;font-weight:bold;color:#003366;">${printData.cargoCode}</div>
                    <div style="font-size:12px;font-weight:bold;color:#003366;">${printData.destination}</div>
                    <div style="font-size:12px;font-weight:bold;color:#003366;">${printData.supplier}</div>
                    <div style="font-size:10px;color:#666;">${printData.location}</div>
                    <div style="font-size:10px;color:#666;margin-top:10px;">收货地址:</div>
                    <div style="font-size:10px;color:#333;">${printData.address}</div>
                    <div style="font-size:10px;color:#333;">${printData.contact}</div>
                  </div>
                </div>
                ${printData.serialNo ? `<div style="font-size:10px;color:#333;text-align:center;margin-top:5px;">单号: ${printData.serialNo}</div>` : ''}
              `
            };

            // 调用打印服务
            printerService.print(template)
                .then(() => {
                  uni.hideLoading();
                  uni.showToast({
                    title: '打印成功',
                    icon: 'success'
                  });
                })
                .catch(err => {
                  console.error('打印失败:', err);
                  uni.hideLoading();
                  uni.showToast({
                    title: '打印失败',
                    icon: 'none'
                  });
                })
                .finally(() => {
                  this.isPrinting = false;
                });
          },
          fail: (err) => {
            console.error('获取二维码图片失败:', err);
            uni.hideLoading();
            uni.showToast({
              title: '获取二维码图片失败',
              icon: 'none'
            });
            this.isPrinting = false;
          }
        }, this);
      } catch (error) {
        console.error('打印准备失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: '打印准备失败',
          icon: 'none'
        });
        this.isPrinting = false;
      }
    },
    // 绘制整个二维码区域到shareCanvas
    drawEntireQrArea() {
      const that = this;
      // 获取二维码区域的尺寸
      const query = uni.createSelectorQuery().in(this);
      query.select('.qr-flex-container').boundingClientRect(data => {
        if (!data) {
          console.error('获取二维码区域尺寸失败');
          return;
        }

        console.log('获取二维码区域尺寸:', data);

        // 设置固定的宽高比例，与示例图片一致
        const width = 400;
        const height = 240;

        // 设置画布尺寸
        that.shareCanvasWidth = width;
        that.shareCanvasHeight = height;

        // 创建画布上下文
        const ctx = uni.createCanvasContext('shareCanvas', that);

        // 绘制白色背景
        ctx.setFillStyle('#FFFFFF');
        ctx.fillRect(0, 0, width, height);

        // 绘制边框
        ctx.setStrokeStyle('#DDDDDD');
        ctx.strokeRect(0, 0, width, height);

        // 计算二维码在画布上的位置和尺寸
        const qrSize = 150; // 固定二维码大小
        const qrX = 20;
        const qrY = 20;

        // 获取二维码图片
        uni.canvasToTempFilePath({
          canvasId: 'canvas',
          success: function (res) {
            // 在shareCanvas上绘制二维码
            ctx.drawImage(res.tempFilePath, qrX, qrY, qrSize, qrSize);

            // 右侧信息区域起始位置
            const infoX = qrX + qrSize + 30;

            // 绘制标题和信息（按照示例图片的样式）
            ctx.setFillStyle('#003366'); // 深蓝色
            ctx.setFontSize(18);
            ctx.fillText(that.formData.preEntrySerialNo || 'PRE.25050007', infoX, 40);

            ctx.setFontSize(16);
            ctx.fillText(that.formData.clientCode ? `${that.formData.clientCode}-${that.formData.consigneeCode}` : 'NODAT-1005', infoX, 70);

            ctx.setFontSize(16);
            ctx.fillText(that.formData.consigneeName || 'BARRON', infoX, 100);

            // 灰色文本
            ctx.setFillStyle('#666666');
            ctx.setFontSize(14);
            ctx.fillText(`Guangzhou- ${that.formData.clientRegion || 'GHANA'}`, infoX, 130);

            // 收货地址信息
            ctx.setFillStyle('#666666');
            ctx.setFontSize(14);
            ctx.fillText('收货地址:', infoX, 160);

            ctx.setFillStyle('#333333');
            const address = (that.destinationAddress || '佛山里水沿江路3号力进物流园B15仓') + (that.formData.clientCode + '-' + that.formData.consigneeCode);

            // 地址可能较长，需要分行显示
            const maxWidth = width - infoX - 20;
            const charWidth = 14; // 估计每个字符的宽度
            const charsPerLine = Math.floor(maxWidth / charWidth);

            if (address.length > charsPerLine) {
              ctx.fillText(address.substring(0, charsPerLine), infoX, 185);
              ctx.fillText(address.substring(charsPerLine), infoX, 210);
            } else {
              ctx.fillText(address, infoX, 185);
            }

            // 联系人信息
            const contact = that.destinationContact || '卢小姐 18922752986';
            ctx.fillText(contact, infoX, 235);

            // 完成绘制
            ctx.draw(false, () => {
              console.log('二维码区域绘制完成');
            });
          },
          fail: function (err) {
            console.error('获取二维码图片失败:', err);
          }
        }, that);
      }).exec();
    },
    saveEntireQrArea() {
      const that = this;
      // 先重新绘制确保内容最新
      that.drawEntireQrArea();

      // 延迟执行保存，确保画布已经绘制完成
      setTimeout(() => {
        uni.canvasToTempFilePath({
          canvasId: 'shareCanvas',
          width: that.shareCanvasWidth,
          height: that.shareCanvasHeight,
          destWidth: that.shareCanvasWidth * 4, // 提高保存图片的清晰度
          destHeight: that.shareCanvasHeight * 4, // 提高保存图片的清晰度
          quality: 1, // 最高质量
          fileType: 'png', // 使用PNG格式保存更清晰
          success: function (res) {
            uni.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: function () {
                uni.showToast({
                  title: '二维码已保存到相册',
                  icon: 'success'
                });
              },
              fail: function (err) {
                console.error('保存失败:', err);
                if (err.errMsg.indexOf('auth deny') !== -1) {
                  uni.showModal({
                    title: '提示',
                    content: '请授权保存图片到相册的权限',
                    success: function (res) {
                      if (res.confirm) {
                        uni.openSetting();
                      }
                    }
                  });
                } else {
                  uni.showToast({
                    title: '保存失败',
                    icon: 'none'
                  });
                }
              }
            });
          },
          fail: function (err) {
            console.error('生成临时文件失败:', err);
            uni.showToast({
              title: '生成图片失败',
              icon: 'none'
            });
          }
        }, that);
      }, 600); // 增加延迟时间，确保画布完全渲染
    },
    showActionMenu() {
      const that = this;
      uni.showActionSheet({
        itemList: ['保存图片', '分享图片'],
        success: function (res) {
          if (res.tapIndex === 0) {
            that.saveEntireQrArea();
          } else if (res.tapIndex === 1) {
            that.shareQrImage();
          }
        },
        fail: function (res) {
          console.log('用户取消选择');
        }
      });
    },

    // 分享二维码图片
    shareQrImage() {
      const that = this;
      // 先绘制确保内容最新
      that.drawEntireQrArea();

      // 延迟执行分享，确保画布已经绘制完成
      setTimeout(() => {
        uni.canvasToTempFilePath({
          canvasId: 'shareCanvas',
          width: that.shareCanvasWidth,
          height: that.shareCanvasHeight,
          destWidth: that.shareCanvasWidth * 4,
          destHeight: that.shareCanvasHeight * 4,
          quality: 1,
          fileType: 'png',
          success: function (res) {
            // 获取图片临时路径
            const tempFilePath = res.tempFilePath;

            // 检查当前环境
            const systemInfo = uni.getSystemInfoSync();

            // 使用微信小程序的分享图片API

            // 使用wx.showShareImageMenu分享图片到聊天
            wx.showShareImageMenu({
              path: tempFilePath,
              success: function () {
                console.log('打开分享图片菜单成功');
              },
              fail: function (err) {
                console.error('打开分享图片菜单失败:', err);
                // 如果API不支持，尝试保存到相册
                if (err.errMsg && err.errMsg.indexOf('not support') !== -1) {
                  that.wxMpShare(tempFilePath);
                } else {
                  uni.showToast({
                    title: '分享失败',
                    icon: 'none'
                  });
                }
              }
            });
            return;
          },
          fail: function (err) {
            console.error('生成临时文件失败:', err);
            uni.showToast({
              title: '生成图片失败',
              icon: 'none'
            });
          }
        }, that);
      }, 600);
    },
    // 微信小程序分享
    wxMpShare(tempFilePath) {
      try {
        // 保存图片到相册，然后提示用户从相册分享
        uni.saveImageToPhotosAlbum({
          filePath: tempFilePath,
          success: function () {
            uni.showModal({
              title: '图片已保存',
              content: '图片已保存到相册，请从相册选择图片进行分享',
              showCancel: false,
              confirmText: '我知道了'
            });
          },
          fail: function (err) {
            console.error('保存失败:', err);
            if (err.errMsg.indexOf('auth deny') !== -1) {
              uni.showModal({
                title: '提示',
                content: '请授权保存图片到相册的权限',
                success: function (res) {
                  if (res.confirm) {
                    uni.openSetting();
                  }
                }
              });
            } else {
              uni.showToast({
                title: '保存失败',
                icon: 'none'
              });
            }
          }
        });
      } catch (e) {
        console.error('微信小程序分享出错:', e);
        uni.showToast({
          title: '分享功能异常',
          icon: 'none'
        });
      }
    },

    // 使用系统分享
    systemShare(tempFilePath) {
      // 小程序环境
      if (uni.getSystemInfoSync().platform === 'mp-weixin') {
        // 使用wx.showShareImageMenu分享图片到聊天
        wx.showShareImageMenu({
          path: tempFilePath,
          success: function () {
            console.log('打开分享图片菜单成功');
          },
          fail: function (err) {
            console.error('打开分享图片菜单失败:', err);
            // 如果API不支持，尝试保存到相册后手动分享
            uni.saveImageToPhotosAlbum({
              filePath: tempFilePath,
              success: function () {
                uni.showModal({
                  title: '图片已保存',
                  content: '图片已保存到相册，请从相册选择图片进行分享',
                  showCancel: false,
                  confirmText: '我知道了'
                });
              },
              fail: function (err) {
                console.error('保存失败:', err);
                uni.showToast({
                  title: '分享失败',
                  icon: 'none'
                });
              }
            });
          }
        });
      }
      // APP环境
      else if (typeof plus !== 'undefined') {
        plus.share.sendWithSystem({
          pictures: [tempFilePath],
          success: function () {
            console.log('系统分享成功');
          },
          fail: function (err) {
            console.error('系统分享失败:', err);
            uni.showToast({
              title: '分享失败',
              icon: 'none'
            });
          }
        });
      }
      // 其他环境，提示保存后分享
      else {
        uni.showModal({
          title: '提示',
          content: '当前环境不支持直接分享，请保存图片后手动分享',
          showCancel: true,
          cancelText: '取消',
          confirmText: '保存图片',
          success: (res) => {
            if (res.confirm) {
              this.saveEntireQrArea();
            }
          }
        });
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.cargo-notification {
  padding: 30rpx;
  background-color: #fff;
  font-size: 28rpx;
  color: #333;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;
    border-bottom: 1px solid #ddd;
    padding-bottom: 20rpx;

    .title {
      font-size: 40rpx;
      font-weight: bold;
      display: flex;
      flex-direction: column;

      .sub-title {
        font-size: 24rpx;
        color: #666;
        margin-top: 5rpx;
      }
    }

    .logo {
      width: 120rpx;
      height: 120rpx;
    }
  }

  .notification-message {
    background-color: #f8f8f8;
    padding: 15rpx;
    border-radius: 8rpx;
    margin-bottom: 20rpx;
    font-size: 26rpx;
  }

  .address-section {
    margin-bottom: 30rpx;
    padding: 15rpx;
    // border: 1px solid #eaeaea;
    border-radius: 8rpx;
    background-color: #fafafa;
  }

  .info-item {
    display: flex;
    align-items: center;
  }

  .section-label {
    width: 180rpx;
  }

  .cargo-items-section {
    margin-bottom: 30rpx;

    .section-title {
      font-weight: bold;
      margin-bottom: 10rpx;
      font-size: 30rpx;
    }

    .add-cargo-btn {
      display: flex;
      justify-content: space-around;
      padding: 20rpx 0;

      .total-text {
        line-height: 40rpx;
      }

      .add-btn {
        color: white;
        background-color: #2979ff;
      }
    }

    .total-info {
      margin-top: 15rpx;
      text-align: right;
      font-weight: bold;
      color: #333;
    }

    .table-btn {
      display: flex;
      justify-content: center;

      .table-btn-item {
        padding: 0 10rpx;
        font-size: 24rpx;

        &.edit-btn {
          color: #2979ff;
        }

        &.delete-btn {
          color: #fa3534;
          margin-left: 10rpx;
        }
      }
    }
  }

  .info-row {
    display: flex;
    margin-bottom: 15rpx;

    .info-label {
      width: 180rpx;
      color: #666;
    }

    .info-value {
      flex: 1;

      &.bold {
        font-weight: bold;
        color: #06c;
      }
    }
  }

  .cargo-edit-popup {
    padding: 0 20rpx;

    .dimensions-row {
      display: flex;
      justify-content: space-between;

      .dimension-item {
        width: 32%;
      }
    }

    ::v-deep .uni-forms-item {
      padding-bottom: 10rpx;
    }

    .popup-footer {
      display: flex;
      justify-content: flex-end;
      padding: 20rpx 30rpx;
      border-top: 1rpx solid #eee;
      margin-top: 20rpx;

      .btn {
        margin-left: 20rpx;
        font-size: 28rpx;
        padding: 6rpx 30rpx;
        border-radius: 6rpx;

        &.btn-cancel {
          background-color: #f8f8f8;
          color: #333;
        }

        &.btn-primary {
          background-color: #2979ff;
          color: #fff;
        }
      }
    }
  }

  .bottom-message {
    margin-top: 30rpx;
  }

  .instruction {
    text-align: center;
    color: #06c;
    font-weight: bold;
    margin: 40rpx 0 20rpx;
  }

  .submit-btn {
    width: 90%;
    margin: 0 auto 40rpx;
    background-color: #2979ff;
    color: #fff;
    font-size: 32rpx;
    border-radius: 8rpx;
    padding: 20rpx 0;

    &:disabled, &.btn-disabled {
      background-color: #cccccc;
      color: black;
      cursor: not-allowed;
      opacity: 0.6;
    }
  }

  .disclaimer {
    font-size: 24rpx;
    color: #ff6666;
    text-align: center;
    margin-bottom: 40rpx;
  }

  ::v-deep .uni-forms-item {
    padding-bottom: 0;
    margin-bottom: 0;
  }

  .datetime-picker-container {
    display: flex;
    width: 100%;
  }

  .picker-item {
    flex: 1;
    height: 35px;
    line-height: 35px;
    padding: 0 10px;
    background-color: rgb(255, 242, 204);
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-right: 10px;
  }

  .picker-item-date {
    width: 200rpx;
  }

  .picker-item-time {
    width: 100rpx;
  }

  .now-btn {
    height: 35px;
    line-height: 35px;
    padding: 0 10px;
    background-color: #2979ff;
    color: #fff;
    font-size: 28rpx;
    border-radius: 4px;
  }

  .flex-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    gap: 10rpx;
  }

  .flex-1 {
    flex: 1;
  }

  .flex-2 {
    flex: 2;
  }

  .button-container {
    width: 100%;
    margin: 20rpx 0;
  }

  .button-row {
    display: flex;
    width: 100%;
    margin-bottom: 10rpx;

    &.full-width {
      width: 100%;
    }
  }

  .action-btn {
    background-color: #2979ff;
    flex: 1;
    height: 80rpx;
    line-height: 80rpx;
    margin: 0 5rpx;
    font-size: 28rpx;
    color: #000;
    border: none;
    border-radius: 6rpx;

    &.full-width-btn {
      width: 100%;
    }
  }

  .btn-disabled {
    background-color: #f5f5f5 !important;
    color: black !important;
    opacity: 0.6;
  }

  .picker-disabled {
    background-color: #f5f5f5 !important;
    color: black;
  }

  .warehouse-btn {
    background-color: #67C23A;
    color: #fff;
  }

  .warehouse-delete-btn {
    background-color: #fa3534;
    color: #fff;
  }

  .dimension-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
  }

  .qr-info {
    margin-top: 20rpx;
    padding: 20rpx;
    background-color: #fff;
    border-radius: 8rpx;
    box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);

    .qr-flex-container {
      display: flex;
      flex-direction: row;
      align-items: flex-start;
      justify-content: flex-start;
      border: 1px solid #ddd;
      padding: 10rpx;
      margin-bottom: 10rpx;
      background-color: #fff;
    }

    .qr-container {
      padding: 10rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 280rpx;
      height: 280rpx;

      .ewm {
        width: 260rpx;
        height: 260rpx;
      }
    }

    .qr-details {
      flex: 1;
      padding: 20rpx;

      .qr-details-header {
        margin-bottom: 20rpx;

        .qr-details-title {
          font-size: 36rpx;
          font-weight: bold;
          color: #003366;
          display: block;
          margin-bottom: 10rpx;
        }

        .qr-details-subtitle {
          font-size: 32rpx;
          font-weight: bold;
          color: #003366;
          display: block;
          margin-bottom: 10rpx;
        }

        .qr-details-company {
          font-size: 32rpx;
          font-weight: bold;
          color: #003366;
          display: block;
          margin-bottom: 10rpx;
        }

        .qr-details-location {
          font-size: 28rpx;
          color: #666;
          display: block;
          margin-bottom: 10rpx;
        }
      }

      .qr-details-address {
        margin-top: 10rpx;

        .qr-details-address-label {
          font-size: 28rpx;
          color: #666;
          display: block;
          margin-bottom: 5rpx;
        }

        .qr-details-address-content {
          font-size: 28rpx;
          color: #333;
          display: block;
          margin-bottom: 5rpx;
        }

        .qr-details-contact {
          font-size: 28rpx;
          color: #333;
          display: block;
        }
      }
    }

    .qr-actions {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      margin-top: 10rpx;
      padding: 0 10rpx;
    }

    .qr-tip {
      font-size: 22rpx;
      color: #999;
    }

    .qr-print-btn {
      font-size: 24rpx;
      padding: 6rpx 20rpx;
      background-color: #e0ffe0;
      color: #006600;
      border: 1px solid #99cc99;
      border-radius: 6rpx;
      line-height: 1.5;
      height: auto;
    }

    .qr-serial {
      font-size: 24rpx;
      color: #333;
      margin-top: 10rpx;
      text-align: center;
    }

    .share-canvas {
      position: fixed;
      left: -9999px;
      top: -9999px;
      width: 900rpx;
      height: 700rpx;
      z-index: -1;
    }
  }
}

/* 全局样式，强制覆盖弹出层位置 */
::v-deep .rich-dialog .rich-dialog-box {
  top: 30% !important;
  transform: translate(-50%, -50%) !important;
}
</style>
