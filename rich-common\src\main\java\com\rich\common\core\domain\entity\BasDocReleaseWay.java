package com.rich.common.core.domain.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

/**
 * 交单方式对象 bas_doc_release_way
 * 
 * <AUTHOR>
 * @date 2023-06-16
 */
public class BasDocReleaseWay extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 交单方式 */
    private Long releaseWayId;

    private Long issueTypeId;
    private String issueType;

    /** 简称 */
    @Excel(name = "简称")
    private String releaseWayShortName;

    /** 中文名 */
    @Excel(name = "中文名")
    private String releaseWayLocalName;

    /** 英文名 */
    @Excel(name = "英文名")
    private String releaseWayEnName;

    /** 排序 */
    @Excel(name = "排序")
    private Integer orderNum;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    public String getIssueType() {
        return issueType;
    }

    public void setIssueType(String issueType) {
        this.issueType = issueType;
    }

    public Long getIssueTypeId() {
        return issueTypeId;
    }

    public void setIssueTypeId(Long issueTypeId) {
        this.issueTypeId = issueTypeId;
    }

    public void setReleaseWayId(Long releaseWayId)
    {
        this.releaseWayId = releaseWayId;
    }

    public Long getReleaseWayId() 
    {
        return releaseWayId;
    }
    public void setReleaseWayShortName(String releaseWayShortName) 
    {
        this.releaseWayShortName = releaseWayShortName;
    }

    public String getReleaseWayShortName() 
    {
        return releaseWayShortName;
    }
    public void setReleaseWayLocalName(String releaseWayLocalName) 
    {
        this.releaseWayLocalName = releaseWayLocalName;
    }

    public String getReleaseWayLocalName() 
    {
        return releaseWayLocalName;
    }
    public void setReleaseWayEnName(String releaseWayEnName) 
    {
        this.releaseWayEnName = releaseWayEnName;
    }

    public String getReleaseWayEnName() 
    {
        return releaseWayEnName;
    }
    public void setOrderNum(Integer orderNum) 
    {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() 
    {
        return orderNum;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
}
