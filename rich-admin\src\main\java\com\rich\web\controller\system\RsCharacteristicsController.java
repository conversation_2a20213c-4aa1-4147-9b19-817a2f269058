package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.RsCharacteristics;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasDistLocationService;
import com.rich.system.service.RsCharacteristicsService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 物流注意事项Controller
 *
 * <AUTHOR>
 * @date 2022-12-30
 */
@RestController
@RequestMapping("/system/characteristics")
public class RsCharacteristicsController extends BaseController {

   @Autowired
    private  RsCharacteristicsService rsCharacteristicsService;

   @Autowired
    private  BasDistLocationService basDistLocationService;

   @Autowired
    private  RedisCache redisCache;

    /**
     * 查询物流注意事项列表
     */
    @PreAuthorize("@ss.hasPermi('system:characteristics:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsCharacteristics rsCharacteristics) {
        List<RsCharacteristics> list = rsCharacteristicsService.selectRsCharacteristicsList(rsCharacteristics);
        if (list != null && list.size() > 0) {
            return getDataTable(list);
        } else {
            return null;
        }
    }

    /**
     * 导出物流注意事项列表
     */
    @PreAuthorize("@ss.hasPermi('system:characteristics:export')")
    @Log(title = "物流注意事项", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsCharacteristics rsCharacteristics) {
        List<RsCharacteristics> list = rsCharacteristicsService.selectRsCharacteristicsList(rsCharacteristics);
        ExcelUtil<RsCharacteristics> util = new ExcelUtil<RsCharacteristics>(RsCharacteristics.class);
        util.exportExcel(response, list, "物流注意事项数据");
    }

    /**
     * 获取物流注意事项详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:characteristics:edit')")
    @GetMapping(value = "/{characteristicsId}")
    public AjaxResult getInfo(@PathVariable("characteristicsId") Long characteristicsId) {
        AjaxResult ajaxResult = AjaxResult.success();
        Set<Long> set = new HashSet<>();
        List<Long> locationDestinationIds = rsCharacteristicsService.selectLocationDestination(characteristicsId);
        List<Long> locationDepartureIds = rsCharacteristicsService.selectLocationDeparture(characteristicsId);
        if (!locationDepartureIds.isEmpty()) {
            set.addAll(locationDepartureIds);
        }
        if (!locationDestinationIds.isEmpty()) {
            set.addAll(locationDestinationIds);
        }
        ajaxResult.put(AjaxResult.DATA_TAG, rsCharacteristicsService.selectRsCharacteristicsByCharacteristicsId(characteristicsId));
        ajaxResult.put("cargoTypeIds", rsCharacteristicsService.selectCargoTypes(characteristicsId));
        ajaxResult.put("locationDepartureIds", locationDepartureIds);
        ajaxResult.put("locationDestinationIds", locationDestinationIds);
        ajaxResult.put("locationOptions", set.size() > 0 ? basDistLocationService.selectBasDistLocationByIds(set) : null);
        ajaxResult.put("lineDepartureIds", rsCharacteristicsService.selectLineDeparture(characteristicsId));
        ajaxResult.put("lineDestinationIds", rsCharacteristicsService.selectLineDestination(characteristicsId));
        ajaxResult.put("carrierIds", rsCharacteristicsService.selectCarriers(characteristicsId));
        return ajaxResult;
    }

    /**
     * 新增物流注意事项
     */
    @PreAuthorize("@ss.hasPermi('system:characteristics:add')")
    @Log(title = "物流注意事项", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsCharacteristics rsCharacteristics) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "characteristics");
        return toAjax(rsCharacteristicsService.insertRsCharacteristics(rsCharacteristics));
    }

    /**
     * 修改物流注意事项
     */
    @PreAuthorize("@ss.hasPermi('system:characteristics:edit')")
    @Log(title = "物流注意事项", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsCharacteristics rsCharacteristics) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "characteristics");
        return toAjax(rsCharacteristicsService.updateRsCharacteristics(rsCharacteristics));
    }


    /**
     * 删除物流注意事项
     */
    @PreAuthorize("@ss.hasPermi('system:characteristics:remove')")
    @Log(title = "物流注意事项", businessType = BusinessType.DELETE)
    @DeleteMapping("/{characteristicsIds}")
    public AjaxResult remove(@PathVariable Long[] characteristicsIds) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "characteristics");
        return toAjax(rsCharacteristicsService.deleteRsCharacteristicsByCharacteristicsIds(characteristicsIds));
    }
}
