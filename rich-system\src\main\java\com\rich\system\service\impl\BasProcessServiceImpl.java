package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.BasProcess;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.system.mapper.BasProcessMapper;
import com.rich.system.service.BasProcessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 进程名称Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-05
 */
@Service
public class BasProcessServiceImpl implements BasProcessService {
    @Autowired
    private BasProcessMapper basProcessMapper;

    /**
     * 查询进程名称
     *
     * @param processId 进程名称主键
     * @return 进程名称
     */
    @Override
    public BasProcess selectBasProcessByProcessId(Long processId) {
        return basProcessMapper.selectBasProcessByProcessId(processId);
    }

    /**
     * 查询进程名称列表
     *
     * @param basProcess 进程名称
     * @return 进程名称
     */
    @Override
    public List<BasProcess> selectBasProcessList(BasProcess basProcess) {
        return basProcessMapper.selectBasProcessList(basProcess);
    }

    /**
     * 新增进程名称
     *
     * @param basProcess 进程名称
     * @return 结果
     */
    @Override
    public int insertBasProcess(BasProcess basProcess) {
        basProcess.setCreateTime(DateUtils.getNowDate());
        basProcess.setCreateBy(SecurityUtils.getUserId());
        return basProcessMapper.insertBasProcess(basProcess);
    }

    /**
     * 修改进程名称
     *
     * @param basProcess 进程名称
     * @return 结果
     */
    @Override
    public int updateBasProcess(BasProcess basProcess) {
        basProcess.setUpdateTime(DateUtils.getNowDate());
        basProcess.setUpdateBy(SecurityUtils.getUserId());
        return basProcessMapper.updateBasProcess(basProcess);
    }

    /**
     * 修改进程名称状态
     *
     * @param basProcess 进程名称
     * @return 进程名称
     */
    @Override
    public int changeStatus(BasProcess basProcess) {
        return basProcessMapper.updateBasProcess(basProcess);
    }

    @Override
    public List<BasProcess> selectBasProcessByServiceTypeId(Long serviceTypeId) {
        return basProcessMapper.selectBasProcessByServiceTypeId(serviceTypeId);
    }

    /**
     * 批量删除进程名称
     *
     * @param processIds 需要删除的进程名称主键
     * @return 结果
     */
    @Override
    public int deleteBasProcessByProcessIds(Long[] processIds) {
        return basProcessMapper.deleteBasProcessByProcessIds(processIds);
    }

    /**
     * 删除进程名称信息
     *
     * @param processId 进程名称主键
     * @return 结果
     */
    @Override
    public int deleteBasProcessByProcessId(Long processId) {
        return basProcessMapper.deleteBasProcessByProcessId(processId);
    }
}
