package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsDispatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsDispatchMapper;
import com.rich.system.service.RsDispatchService;

/**
 * 尾程运输Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@Service
public class RsDispatchServiceImpl implements RsDispatchService {
    @Autowired
    private RsDispatchMapper rsDispatchMapper;

    /**
     * 查询尾程运输
     *
     * @param dispatchId 尾程运输主键
     * @return 尾程运输
     */
    @Override
    public RsDispatch selectRsDispatchByDispatchId(Long dispatchId) {
        return rsDispatchMapper.selectRsDispatchByDispatchId(dispatchId);
    }

    /**
     * 查询尾程运输列表
     *
     * @param rsDispatch 尾程运输
     * @return 尾程运输
     */
    @Override
    public List<RsDispatch> selectRsDispatchList(RsDispatch rsDispatch) {
        return rsDispatchMapper.selectRsDispatchList(rsDispatch);
    }

    /**
     * 新增尾程运输
     *
     * @param rsDispatch 尾程运输
     * @return 结果
     */
    @Override
    public int insertRsDispatch(RsDispatch rsDispatch) {
        return rsDispatchMapper.insertRsDispatch(rsDispatch);
    }

    /**
     * 修改尾程运输
     *
     * @param rsDispatch 尾程运输
     * @return 结果
     */
    @Override
    public int updateRsDispatch(RsDispatch rsDispatch) {
        return rsDispatchMapper.updateRsDispatch(rsDispatch);
    }

    /**
     * 修改尾程运输状态
     *
     * @param rsDispatch 尾程运输
     * @return 尾程运输
     */
    @Override
    public int changeStatus(RsDispatch rsDispatch) {
        return rsDispatchMapper.updateRsDispatch(rsDispatch);
    }

    /**
     * 批量删除尾程运输
     *
     * @param dispatchIds 需要删除的尾程运输主键
     * @return 结果
     */
    @Override
    public int deleteRsDispatchByDispatchIds(Long[] dispatchIds) {
        return rsDispatchMapper.deleteRsDispatchByDispatchIds(dispatchIds);
    }

    /**
     * 删除尾程运输信息
     *
     * @param dispatchId 尾程运输主键
     * @return 结果
     */
    @Override
    public int deleteRsDispatchByDispatchId(Long dispatchId) {
        return rsDispatchMapper.deleteRsDispatchByDispatchId(dispatchId);
    }
}
