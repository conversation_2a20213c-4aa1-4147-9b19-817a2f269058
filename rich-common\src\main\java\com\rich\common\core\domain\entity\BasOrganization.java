package com.rich.common.core.domain.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 所属组织对象 bas_organization
 * 
 * <AUTHOR>
 * @date 2022-09-30
 */
public class BasOrganization extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 所属组织 */
    private Long organizationId;

    /** 简称 */
    @Excel(name = "简称")
    private String organizationShortName;

    /** 中文名 */
    @Excel(name = "中文名")
    private String organizationLocalName;

    /** 英文名 */
    @Excel(name = "英文名")
    private String organizationEnName;

    /** 展示优先级 */
    @Excel(name = "展示优先级")
    private Integer orderNum;

    private String status;

    private String organizationQuery;

    public String getOrganizationQuery() {
        return organizationQuery;
    }

    public void setOrganizationQuery(String organizationQuery) {
        this.organizationQuery = organizationQuery;
    }

    public void setOrganizationId(Long organizationId)
    {
        this.organizationId = organizationId;
    }

    public Long getOrganizationId() 
    {
        return organizationId;
    }
    public void setOrganizationShortName(String organizationShortName) 
    {
        this.organizationShortName = organizationShortName;
    }

    public String getOrganizationShortName() 
    {
        return organizationShortName;
    }
    public void setOrganizationLocalName(String organizationLocalName) 
    {
        this.organizationLocalName = organizationLocalName;
    }

    public String getOrganizationLocalName() 
    {
        return organizationLocalName;
    }
    public void setOrganizationEnName(String organizationEnName) 
    {
        this.organizationEnName = organizationEnName;
    }

    public String getOrganizationEnName() 
    {
        return organizationEnName;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("organizationId", getOrganizationId())
            .append("organizationShortName", getOrganizationShortName())
            .append("organizationLocalName", getOrganizationLocalName())
            .append("organizationEnName", getOrganizationEnName())
            .append("orderNum", getOrderNum())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("deleteBy", getDeleteBy())
            .append("deleteTime", getDeleteTime())
            .append("deleteStatus", getDeleteStatus())
            .toString();
    }
}
