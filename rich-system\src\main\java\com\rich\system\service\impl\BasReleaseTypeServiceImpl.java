package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.BasReleaseType;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.system.mapper.BasReleaseTypeMapper;
import com.rich.system.service.BasReleaseTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 放货方式Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@Service
public class BasReleaseTypeServiceImpl implements BasReleaseTypeService {
    @Autowired
    private BasReleaseTypeMapper basReleaseTypeMapper;

    /**
     * 查询放货方式
     *
     * @param releaseTypeId 放货方式主键
     * @return 放货方式
     */
    @Override
    public BasReleaseType selectBasReleaseTypeByReleaseTypeId(Long releaseTypeId) {
        return basReleaseTypeMapper.selectBasReleaseTypeByReleaseTypeId(releaseTypeId);
    }

    /**
     * 查询放货方式列表
     *
     * @param basReleaseType 放货方式
     * @return 放货方式
     */
    @Override
    public List<BasReleaseType> selectBasReleaseTypeList(BasReleaseType basReleaseType) {
        return basReleaseTypeMapper.selectBasReleaseTypeList(basReleaseType);
    }

    /**
     * 新增放货方式
     *
     * @param basReleaseType 放货方式
     * @return 结果
     */
    @Override
    public int insertBasReleaseType(BasReleaseType basReleaseType) {
        basReleaseType.setCreateTime(DateUtils.getNowDate());
        basReleaseType.setCreateBy(SecurityUtils.getUserId());
        return basReleaseTypeMapper.insertBasReleaseType(basReleaseType);
    }

    /**
     * 修改放货方式
     *
     * @param basReleaseType 放货方式
     * @return 结果
     */
    @Override
    public int updateBasReleaseType(BasReleaseType basReleaseType) {
        basReleaseType.setUpdateTime(DateUtils.getNowDate());
        basReleaseType.setUpdateBy(SecurityUtils.getUserId());
        return basReleaseTypeMapper.updateBasReleaseType(basReleaseType);
    }

    /**
     * 修改放货方式状态
     *
     * @param basReleaseType 放货方式
     * @return 放货方式
     */
    @Override
    public int changeStatus(BasReleaseType basReleaseType) {
        return basReleaseTypeMapper.updateBasReleaseType(basReleaseType);
    }

    /**
     * 批量删除放货方式
     *
     * @param releaseTypeIds 需要删除的放货方式主键
     * @return 结果
     */
    @Override
    public int deleteBasReleaseTypeByReleaseTypeIds(Long[] releaseTypeIds) {
        return basReleaseTypeMapper.deleteBasReleaseTypeByReleaseTypeIds(releaseTypeIds);
    }

    /**
     * 删除放货方式信息
     *
     * @param releaseTypeId 放货方式主键
     * @return 结果
     */
    @Override
    public int deleteBasReleaseTypeByReleaseTypeId(Long releaseTypeId) {
        return basReleaseTypeMapper.deleteBasReleaseTypeByReleaseTypeId(releaseTypeId);
    }
}
