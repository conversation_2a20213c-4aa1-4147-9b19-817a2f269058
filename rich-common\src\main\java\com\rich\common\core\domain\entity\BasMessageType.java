package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 bas_message_type
 *
 * <AUTHOR>
 * @date 2023-02-21
 */
public class BasMessageType extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 信息类型
     */
    private Long messageTypeId;

    /**
     * 简称
     */
    @Excel(name = "简称")
    private String messageTypeShortName;

    /**
     * 中文名
     */
    @Excel(name = "中文名")
    private String messageTypeLocalName;

    /**
     * 英文名
     */
    @Excel(name = "英文名")
    private String messageTypeEnName;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Integer orderNum;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;

    public Long getMessageTypeId() {
        return messageTypeId;
    }

    public void setMessageTypeId(Long messageTypeId) {
        this.messageTypeId = messageTypeId;
    }

    public String getMessageTypeShortName() {
        return messageTypeShortName;
    }

    public void setMessageTypeShortName(String messageTypeShortName) {
        this.messageTypeShortName = messageTypeShortName;
    }

    public String getMessageTypeLocalName() {
        return messageTypeLocalName;
    }

    public void setMessageTypeLocalName(String messageTypeLocalName) {
        this.messageTypeLocalName = messageTypeLocalName;
    }

    public String getMessageTypeEnName() {
        return messageTypeEnName;
    }

    public void setMessageTypeEnName(String messageTypeEnName) {
        this.messageTypeEnName = messageTypeEnName;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

}
