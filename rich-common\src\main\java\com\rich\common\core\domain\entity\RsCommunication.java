package com.rich.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 交流对象 rs_communication
 *
 * <AUTHOR>
 * @date 2022-10-18
 */
public class RsCommunication extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long communicationId;

    /**
     * 员工ID
     */
    @Excel(name = "员工ID")
    private Long staffId;

    private Long sqdCompanyId;

    /**
     * 公司ID
     */
    @Excel(name = "公司ID")
    private Long extStaffId;

    /**
     * 阶段
     */
    @Excel(name = "阶段")
    private Long stageId;

    /**
     * 问题
     */
    @Excel(name = "问题")
    private Long issueId;

    /**
     * 沟通详细
     */
    @Excel(name = "沟通详细")
    private String content;

    /**
     * 评分1~10
     */
    @Excel(name = "评分1~10")
    private Long score;

    /**
     * 优先度
     */
    @Excel(name = "优先度")
    private Integer orderNum;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date communicationDatetime;

    /**
     * 删除人
     */
    private String deleteBy;

    /**
     * 删除时间
     */
    private Date deleteTime;

    /**
     * 数据状态（-1:删除或不可用，0：正常）
     */
    private Integer deleteStatus;

    private BasIssue issue;

    private RsStaff rsStaff;

    private ExtStaff extStaff;

    public Long getSqdCompanyId() {
        return sqdCompanyId;
    }

    public void setSqdCompanyId(Long sqdCompanyId) {
        this.sqdCompanyId = sqdCompanyId;
    }

    public Date getCommunicationDatetime() {
        return communicationDatetime;
    }

    public void setCommunicationDatetime(Date communicationDatetime) {
        this.communicationDatetime = communicationDatetime;
    }

    public RsStaff getRsStaff() {
        return rsStaff;
    }

    public void setRsStaff(RsStaff rsStaff) {
        this.rsStaff = rsStaff;
    }

    public ExtStaff getExtStaff() {
        return extStaff;
    }

    public void setExtStaff(ExtStaff extStaff) {
        this.extStaff = extStaff;
    }

    public BasIssue getIssue() {
        return issue;
    }

    public void setIssue(BasIssue issue) {
        this.issue = issue;
    }

    public void setCommunicationId(Long communicationId) {
        this.communicationId = communicationId;
    }

    public Long getCommunicationId() {
        return communicationId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setExtStaffId(Long extStaffId) {
        this.extStaffId = extStaffId;
    }

    public Long getExtStaffId() {
        return extStaffId;
    }

    public void setStageId(Long stageId) {
        this.stageId = stageId;
    }

    public Long getStageId() {
        return stageId;
    }

    public void setIssueId(Long issueId) {
        this.issueId = issueId;
    }

    public Long getIssueId() {
        return issueId;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    public void setScore(Long score) {
        this.score = score;
    }

    public Long getScore() {
        return score;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("communicationId", getCommunicationId())
                .append("staffId", getStaffId())
                .append("extStaffId", getExtStaffId())
                .append("stageId", getStageId())
                .append("issueId", getIssueId())
                .append("content", getContent())
                .append("score", getScore())
                .append("orderNum", getOrderNum())
                .append("remark", getRemark())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("deleteBy", getDeleteBy())
                .append("deleteTime", getDeleteTime())
                .append("deleteStatus", getDeleteStatus())
                .toString();
    }
}
