package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsClientsInfo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsClientsInfoService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 用来记录客户常用的信息，避免重复劳动、错漏Controller
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@RestController
@RequestMapping("/system/clientsinfo")
public class RsClientsInfoController extends BaseController {
    @Autowired
    private RsClientsInfoService rsClientsInfoService;

    /**
     * 查询用来记录客户常用的信息，避免重复劳动、错漏列表
     */
    @PreAuthorize("@ss.hasPermi('system:clientsinfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsClientsInfo rsClientsInfo) {
        startPage();
        List<RsClientsInfo> list = rsClientsInfoService.selectRsClientsInfoList(rsClientsInfo);
        return getDataTable(list);
    }

    /**
     * 导出用来记录客户常用的信息，避免重复劳动、错漏列表
     */
    @PreAuthorize("@ss.hasPermi('system:clientsinfo:export')")
    @Log(title = "用来记录客户常用的信息，避免重复劳动、错漏", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsClientsInfo rsClientsInfo) {
        List<RsClientsInfo> list = rsClientsInfoService.selectRsClientsInfoList(rsClientsInfo);
        ExcelUtil<RsClientsInfo> util = new ExcelUtil<RsClientsInfo>(RsClientsInfo.class);
        util.exportExcel(response, list, "用来记录客户常用的信息，避免重复劳动、错漏数据");
    }

    /**
     * 获取用来记录客户常用的信息，避免重复劳动、错漏详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:clientsinfo:query')")
    @GetMapping(value = "/{clientsInfoId}")
    public AjaxResult getInfo(@PathVariable("clientsInfoId") Long clientsInfoId) {
        return AjaxResult.success(rsClientsInfoService.selectRsClientsInfoByClientsInfoId(clientsInfoId));
    }

    /**
     * 新增用来记录客户常用的信息，避免重复劳动、错漏
     */
    @PreAuthorize("@ss.hasPermi('system:clientsinfo:add')")
    @Log(title = "用来记录客户常用的信息，避免重复劳动、错漏", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsClientsInfo rsClientsInfo) {
        return toAjax(rsClientsInfoService.insertRsClientsInfo(rsClientsInfo));
    }

    /**
     * 修改用来记录客户常用的信息，避免重复劳动、错漏
     */
    @PreAuthorize("@ss.hasPermi('system:clientsinfo:edit')")
    @Log(title = "用来记录客户常用的信息，避免重复劳动、错漏", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsClientsInfo rsClientsInfo) {
        return toAjax(rsClientsInfoService.updateRsClientsInfo(rsClientsInfo));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:clientsinfo:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsClientsInfo rsClientsInfo) {
        rsClientsInfo.setUpdateBy(getUserId());
        return toAjax(rsClientsInfoService.changeStatus(rsClientsInfo));
    }

    /**
     * 删除用来记录客户常用的信息，避免重复劳动、错漏
     */
    @PreAuthorize("@ss.hasPermi('system:clientsinfo:remove')")
    @Log(title = "用来记录客户常用的信息，避免重复劳动、错漏", businessType = BusinessType.DELETE)
    @DeleteMapping("/{clientsInfoIds}")
    public AjaxResult remove(@PathVariable Long[] clientsInfoIds) {
        return toAjax(rsClientsInfoService.deleteRsClientsInfoByClientsInfoIds(clientsInfoIds));
    }
}
