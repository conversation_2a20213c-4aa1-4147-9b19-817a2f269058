package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpLog;
import org.apache.ibatis.annotations.Mapper;

/**
 * 操作单操作记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-08
 */
@Mapper
public interface RsOpLogMapper {
    /**
     * 查询操作单操作记录
     * 
     * @param opLogId 操作单操作记录主键
     * @return 操作单操作记录
     */
    RsOpLog selectRsOpLogByOpLogId(Long opLogId);

    /**
     * 查询操作单操作记录列表
     * 
     * @param rsOpLog 操作单操作记录
     * @return 操作单操作记录集合
     */
    List<RsOpLog> selectRsOpLogList(RsOpLog rsOpLog);

    /**
     * 新增操作单操作记录
     * 
     * @param rsOpLog 操作单操作记录
     * @return 结果
     */
    int insertRsOpLog(RsOpLog rsOpLog);

    /**
     * 修改操作单操作记录
     * 
     * @param rsOpLog 操作单操作记录
     * @return 结果
     */
    int updateRsOpLog(RsOpLog rsOpLog);

    /**
     * 删除操作单操作记录
     * 
     * @param opLogId 操作单操作记录主键
     * @return 结果
     */
    int deleteRsOpLogByOpLogId(Long opLogId);

    /**
     * 批量删除操作单操作记录
     * 
     * @param opLogIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsOpLogByOpLogIds(Long[] opLogIds);

    List<RsOpLog> selectRsOpLogByRctId(Long rctId);

    void deleteRsOpLogByServiceId(Long serviceId);
}
