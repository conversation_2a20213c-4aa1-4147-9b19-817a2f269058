package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasDistLocation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 区域Mapper接口
 *
 * <AUTHOR>
 * @date 2022-08-15
 */
@Mapper
public interface BasDistLocationMapper {
    /**
     * 查询区域
     *
     * @param locationId 区域主键
     * @return 区域
     */
    BasDistLocation selectBasDistLocationByLocationId(Long locationId);

    /**
     * 查询区域列表
     *
     * @param basDistLocation 区域
     * @return 区域集合
     */
    List<BasDistLocation> selectBasDistLocationList(BasDistLocation basDistLocation);

    List<BasDistLocation> selectLoadLocationList(BasDistLocation basDistLocation);

    BasDistLocation selectLocation(String name);

    /**
     * 新增区域
     *
     * @param basDistLocation 区域
     * @return 结果
     */
    int insertBasDistLocation(BasDistLocation basDistLocation);

    /**
     * 修改区域
     *
     * @param basDistLocation 区域
     * @return 结果
     */
    int updateBasDistLocation(BasDistLocation basDistLocation);

    /**
     * 删除区域
     *
     * @param locationId 区域主键
     * @return 结果
     */
    int deleteBasDistLocationByLocationId(Long locationId);

    /**
     * 批量删除区域
     *
     * @param locationIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasDistLocationByLocationIds(Long[] locationIds);

    List<BasDistLocation> selectChildrenLocationById(Long locationId);

    int updateLocationChildren(@Param("locations") List<BasDistLocation> locations);

    void updateLocationStatusNormal(Long[] locationIds);

    List<BasDistLocation> queryParentSonByLocationId(Long locationId);

    List<BasDistLocation> selectLocationByLineIds(Long[] lineIds);

    List<BasDistLocation> selectLocationByLocationIds(Long[] locationIds);

    void updateLineAncestors(@Param("lineId") Long lineId, @Param("lineAncestors") String lineAncestors);

    void updateLocationAncestors(@Param("id") Long id, @Param("pid") Long pid);

    void updateLocationPriority(@Param("id") Long id);

    List<BasDistLocation> selectLocationList();
}
