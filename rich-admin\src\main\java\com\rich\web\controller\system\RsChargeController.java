package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.MidChargeBankWriteoff;
import com.rich.common.core.domain.entity.RsCharge;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.DataAggregatorService;
import com.rich.system.service.RsChargeService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 费用明细Controller
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@RestController
@RequestMapping("/system/rscharge")
public class RsChargeController extends BaseController {
    @Autowired
    private RsChargeService rsChargeService;

    @Autowired
    private DataAggregatorService dataAggregatorService;

    /**
     * 查询费用明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:charge:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsCharge rsCharge) {
        startPage();
        List<RsCharge> list = rsChargeService.selectRsChargeList(rsCharge);
        return getDataTable(list);
    }

    /**
     * 查询费用明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:charge:list')")
    @PostMapping("/aggregator")
    public AjaxResult aggregator(@RequestBody RsCharge rsCharge) {

        List<RsCharge> list = new ArrayList<>();
        if (rsCharge.getRsChargeList() == null || rsCharge.getRsChargeList().isEmpty()) {
            list = rsChargeService.selectRsChargeList(rsCharge);
        } else {
            list = rsCharge.getRsChargeList();
        }

        // 3. 使用Service提取聚合配置
        Map<String, Object> aggregatorConfig;
        try {
            aggregatorConfig = dataAggregatorService.extractAggregatorConfig(rsCharge);
        } catch (RuntimeException e) {
            logger.error("提取聚合配置失败", e);
            return AjaxResult.error(e.getMessage());
        }

        // 5. 执行数据聚合
        List<Map<String, Object>> aggregatedData = dataAggregatorService.aggregateData(list, aggregatorConfig);

        return AjaxResult.success(aggregatedData);
    }


    /**
     * 导出费用明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:charge:export')")
    @Log(title = "费用明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsCharge rsCharge) {
        List<RsCharge> list = rsChargeService.selectRsChargeList(rsCharge);
        ExcelUtil<RsCharge> util = new ExcelUtil<RsCharge>(RsCharge.class);
        util.exportExcel(response, list, "费用明细数据");
    }

    /**
     * 获取费用明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:charge:query')")
    @GetMapping(value = "/{chargeId}")
    public AjaxResult getInfo(@PathVariable("chargeId") Long chargeId) {
        return AjaxResult.success(rsChargeService.selectRsChargeByChargeId(chargeId));
    }

    @GetMapping(value = "/service/{serviceId}")
    public AjaxResult getCharges(@PathVariable("serviceId") Long serviceId) {
        return AjaxResult.success(rsChargeService.selectRsChargeByServiceId(serviceId));
    }

    /**
     * 新增费用明细
     */
    @PreAuthorize("@ss.hasPermi('system:charge:add')")
    @Log(title = "费用明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsCharge rsCharge) {
        return toAjax(rsChargeService.insertRsCharge(rsCharge));
    }

    /**
     * 修改费用明细
     */
    @PreAuthorize("@ss.hasPermi('system:charge:edit')")
    @Log(title = "费用明细", businessType = BusinessType.UPDATE)
    @PostMapping("/batchCharges")
    public AjaxResult batchCharges(@RequestParam List<RsCharge> rsCharge) {
        for (RsCharge charge : rsCharge) {
            rsChargeService.updateRsCharge(charge);
        }
        return AjaxResult.success();
    }


    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:charge:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsCharge rsCharge) {
        rsCharge.setUpdateBy(getUserId());
        return toAjax(rsChargeService.changeStatus(rsCharge));
    }

    /**
     * 删除费用明细
     */
    @PreAuthorize("@ss.hasPermi('system:charge:remove')")
    @Log(title = "费用明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{chargeIds}")
    public AjaxResult remove(@PathVariable Long[] chargeIds) {
        return toAjax(rsChargeService.deleteRsChargeByChargeIds(chargeIds));
    }

    @GetMapping(value = "/charges")
    public TableDataInfo getRsChargeList(RsCharge rsCharge) {
        startPage();
        List<RsCharge> data = rsChargeService.selectRsChargeByrctId(rsCharge);
        return getDataTable(data);
    }

    /**
     * 销账时查询费用明细
     *
     * @param rsCharge
     * @return
     */
    @GetMapping("/selectList")
    public List<RsCharge> selectList(RsCharge rsCharge) {
        return rsChargeService.selectWriteOffRsChargeList(rsCharge);
    }

    /**
     * 增加对冲费用
     *
     * @param rsCharge
     * @return
     */
    @GetMapping("/findHedging")
    public List<RsCharge> findHedging(RsCharge rsCharge) {
        return rsChargeService.findHedging(rsCharge);
    }

    /**
     * 销账操作
     *
     * @param rsCharge
     * @return
     */
    @PostMapping("/writeoff")
    public AjaxResult writeOff(@RequestBody RsCharge rsCharge) {
        // 检查入参是否为空
        if (rsCharge == null) {
            return AjaxResult.success();
        }

        List<RsCharge> rsChargeList = rsCharge.getRsChargeList();
        List<MidChargeBankWriteoff> midChargeBankWriteoffList = rsCharge.getMidChargeBankWriteoffList();

        if (CollectionUtils.isEmpty(rsChargeList) || CollectionUtils.isEmpty(midChargeBankWriteoffList)) {
            return AjaxResult.success();
        }

        // 使用迭代器安全地移除元素
        Iterator<RsCharge> chargeIterator = rsChargeList.iterator();
        while (chargeIterator.hasNext()) {
            RsCharge charge = chargeIterator.next();
            // 增加空指针检查
            if (charge == null || charge.getChargeId() == null) {
                continue;
            }

            // 判断charge是否在midChargeBankWriteoffList中有对应记录
            boolean hasWriteoffRecord = false;
            for (MidChargeBankWriteoff writeoff : midChargeBankWriteoffList) {
                if (writeoff != null && writeoff.getChargeId() != null &&
                        writeoff.getChargeId().equals(charge.getChargeId())) {
                    hasWriteoffRecord = true;
                    break;
                }
            }


            // 只有在有销账记录且销账余额为0的情况下才移除
            if (charge.getMidChargeBankWriteoff() != null && charge.getMidChargeBankWriteoff().getSqdDnCurrencyBalance() != null &&
                    charge.getMidChargeBankWriteoff().getSqdDnCurrencyBalance().compareTo(BigDecimal.ZERO) == 0) {
                chargeIterator.remove();

                // 移除关联的销账记录
                Iterator<MidChargeBankWriteoff> writeoffIterator = midChargeBankWriteoffList.iterator();
                while (writeoffIterator.hasNext()) {
                    MidChargeBankWriteoff writeoff = writeoffIterator.next();
                    // 增加空指针检查
                    if (writeoff != null && writeoff.getChargeId() != null &&
                            writeoff.getChargeId().equals(charge.getChargeId())) {
                        writeoffIterator.remove();
                    }
                }
            }
        }

        if (!rsChargeList.isEmpty()) {
            rsChargeService.insertRsChargeAndMidChargeBankWriteoff(rsChargeList, midChargeBankWriteoffList);
        }

        return AjaxResult.success();
    }

    @PostMapping("/verify")
    public AjaxResult verify(@RequestBody RsCharge rsCharge) {
        List<RsCharge> rsChargeList = rsCharge.getRsChargeList();
        rsChargeService.verifyRsCharge(rsChargeList);
        return AjaxResult.success();
    }

    @PostMapping("/turnback")
    public AjaxResult turnBackWriteoff(@RequestBody RsCharge rsCharge) {
        List<RsCharge> rsChargeList = rsCharge.getRsChargeList();
        rsChargeService.turnBackWriteoffList(rsChargeList);
        return AjaxResult.success();
    }

    /**
     * 修改费用明细
     */
    @PreAuthorize("@ss.hasPermi('system:charge:edit')")
    @Log(title = "费用明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsCharge rsCharge) {
        return toAjax(rsChargeService.updateRsCharge(rsCharge));
    }

}
