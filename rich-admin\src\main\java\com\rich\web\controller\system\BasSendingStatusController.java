package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasSendingStatus;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasSendingStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 发送状态Controller
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@RestController
@RequestMapping("/system/sendingstatus")
public class BasSendingStatusController extends BaseController {
    @Autowired
    private BasSendingStatusService basSendingStatusService;

    /**
     * 查询发送状态列表
     */
    @PreAuthorize("@ss.hasPermi('system:sendingstatus:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasSendingStatus basSendingStatus) {
        startPage();
        List<BasSendingStatus> list = basSendingStatusService.selectBasSendingStatusList(basSendingStatus);
        return getDataTable(list);
    }

    /**
     * 导出发送状态列表
     */
    @PreAuthorize("@ss.hasPermi('system:sendingstatus:export')")
    @Log(title = "发送状态", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasSendingStatus basSendingStatus) {
        List<BasSendingStatus> list = basSendingStatusService.selectBasSendingStatusList(basSendingStatus);
        ExcelUtil<BasSendingStatus> util = new ExcelUtil<BasSendingStatus>(BasSendingStatus.class);
        util.exportExcel(response, list, "发送状态数据");
    }

    /**
     * 获取发送状态详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:sendingstatus:query')")
    @GetMapping(value = "/{sendingStatusId}")
    public AjaxResult getInfo(@PathVariable("sendingStatusId") Long sendingStatusId) {
        return AjaxResult.success(basSendingStatusService.selectBasSendingStatusBySendingStatusId(sendingStatusId));
    }

    /**
     * 新增发送状态
     */
    @PreAuthorize("@ss.hasPermi('system:sendingstatus:add')")
    @Log(title = "发送状态", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasSendingStatus basSendingStatus) {
        return toAjax(basSendingStatusService.insertBasSendingStatus(basSendingStatus));
    }

    /**
     * 修改发送状态
     */
    @PreAuthorize("@ss.hasPermi('system:sendingstatus:edit')")
    @Log(title = "发送状态", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasSendingStatus basSendingStatus) {
        return toAjax(basSendingStatusService.updateBasSendingStatus(basSendingStatus));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:sendingstatus:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasSendingStatus basSendingStatus) {
        basSendingStatus.setUpdateBy(getUserId());
        return toAjax(basSendingStatusService.changeStatus(basSendingStatus));
    }

    /**
     * 删除发送状态
     */
    @PreAuthorize("@ss.hasPermi('system:sendingstatus:remove')")
    @Log(title = "发送状态", businessType = BusinessType.DELETE)
    @DeleteMapping("/{sendingStatusIds}")
    public AjaxResult remove(@PathVariable Long[] sendingStatusIds) {
        return toAjax(basSendingStatusService.deleteBasSendingStatusBySendingStatusIds(sendingStatusIds));
    }
}
