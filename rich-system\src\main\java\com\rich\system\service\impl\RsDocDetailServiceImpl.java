package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.RsDocDetail;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.system.mapper.RsDocDetailMapper;
import com.rich.system.service.RsDocDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 操作文件Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-21
 */
@Service
public class RsDocDetailServiceImpl implements RsDocDetailService {
    @Autowired
    private RsDocDetailMapper rsDocDetailMapper;

    /**
     * 查询操作文件
     *
     * @param docDetailId 操作文件主键
     * @return 操作文件
     */
    @Override
    public RsDocDetail selectRsDocDetailByDocDetailId(Long docDetailId) {
        return rsDocDetailMapper.selectRsDocDetailByDocDetailId(docDetailId);
    }

    /**
     * 查询操作文件列表
     *
     * @param rsDocDetail 操作文件
     * @return 操作文件
     */
    @Override
    public List<RsDocDetail> selectRsDocDetailList(RsDocDetail rsDocDetail) {
        return rsDocDetailMapper.selectRsDocDetailList(rsDocDetail);
    }

    /**
     * 新增操作文件
     *
     * @param rsDocDetail 操作文件
     * @return 结果
     */
    @Override
    public int insertRsDocDetail(RsDocDetail rsDocDetail) {
        rsDocDetail.setCreateTime(DateUtils.getNowDate());
        rsDocDetail.setCreateBy(SecurityUtils.getUserId());
        return rsDocDetailMapper.insertRsDocDetail(rsDocDetail);
    }

    /**
     * 修改操作文件
     *
     * @param rsDocDetail 操作文件
     * @return 结果
     */
    @Override
    public int updateRsDocDetail(RsDocDetail rsDocDetail) {
        rsDocDetail.setUpdateTime(DateUtils.getNowDate());
        rsDocDetail.setUpdateBy(SecurityUtils.getUserId());
        return rsDocDetailMapper.updateRsDocDetail(rsDocDetail);
    }

    /**
     * 修改操作文件状态
     *
     * @param rsDocDetail 操作文件
     * @return 操作文件
     */
    @Override
    public int changeStatus(RsDocDetail rsDocDetail) {
        return rsDocDetailMapper.updateRsDocDetail(rsDocDetail);
    }

    /**
     * 批量删除操作文件
     *
     * @param docDetailIds 需要删除的操作文件主键
     * @return 结果
     */
    @Override
    public int deleteRsDocDetailByDocDetailIds(Long[] docDetailIds) {
        return rsDocDetailMapper.deleteRsDocDetailByDocDetailIds(docDetailIds);
    }

    /**
     * 删除操作文件信息
     *
     * @param docDetailId 操作文件主键
     * @return 结果
     */
    @Override
    public int deleteRsDocDetailByDocDetailId(Long docDetailId) {
        return rsDocDetailMapper.deleteRsDocDetailByDocDetailId(docDetailId);
    }
}
