package com.rich.common.core.domain.entity;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

/**
 * 操作单进口清关基础信息对象 rs_rct_import_clearance_basic_info
 * 
 * <AUTHOR>
 * @date 2023-08-15
 */
public class RsRctImportClearanceBasicInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**
     * 进口清关
     */
    private Long importClearanceId;

    /**
     * 操作单
     */
    @Excel(name = "操作单")
    private Long rctId;

    private Long typeId;

    /**
     * 报关方式
     */
    @Excel(name = "报关方式")
    private Long exportCustomsTypeId;

    /**
     * 清关方式
     */
    @Excel(name = "清关方式")
    private Long importCustomsTypeId;

    /**
     * 操作审批
     */
    @Excel(name = "操作审批")
    private String opConfirmed;

    /** 确认人ID */
    @Excel(name = "确认人ID")
    private Long opConfirmedId;

    /** 操作审批时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "操作审批时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date opConfirmedDate;

    /** 财务审批 */
    @Excel(name = "财务审批")
    private String financeConfirmed;

    /** 确认人ID */
    @Excel(name = "确认人ID")
    private Long financeConfirmedId;

    /** 财务审批时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "财务审批时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date financeConfirmedDate;

    /** 业务审批 */
    @Excel(name = "业务审批")
    private String salesConfirmed;

    /** 确认人ID */
    @Excel(name = "确认人ID")
    private Long salesConfirmedId;

    /** 业务审批时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "业务审批时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date salesConfirmedDate;

    /** 供应商审批 */
    @Excel(name = "供应商审批")
    private String supplierConfirmed;

    /** 确认人ID */
    @Excel(name = "确认人ID")
    private Long supplierConfirmedId;

    /**
     * 供应商审批时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "供应商审批时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date supplierConfirmedDate;

    /**
     * 发票查询编号
     */
    @Excel(name = "发票查询编号")
    private String invoiceQueryNo;

    private List<RsRctReceivablePayable> rsRctReceivablePayableList;

    private List<RsOperationalProcess> rsOperationalProcessList;

    public List<RsOperationalProcess> getRsOperationalProcessList() {
        return rsOperationalProcessList;
    }

    public void setRsOperationalProcessList(List<RsOperationalProcess> rsOperationalProcessList) {
        this.rsOperationalProcessList = rsOperationalProcessList;
    }

    public Long getTypeId() {
        return typeId;
    }

    public void setTypeId(Long typeId) {
        this.typeId = typeId;
    }

    public List<RsRctReceivablePayable> getRsRctReceivablePayableList() {
        return rsRctReceivablePayableList;
    }

    public void setRsRctReceivablePayableList(List<RsRctReceivablePayable> rsRctReceivablePayableList) {
        this.rsRctReceivablePayableList = rsRctReceivablePayableList;
    }

    public void setImportClearanceId(Long importClearanceId)
    {
        this.importClearanceId = importClearanceId;
    }

    public Long getImportClearanceId() 
    {
        return importClearanceId;
    }
    public void setRctId(Long rctId) 
    {
        this.rctId = rctId;
    }

    public Long getRctId() 
    {
        return rctId;
    }
    public void setExportCustomsTypeId(Long exportCustomsTypeId) 
    {
        this.exportCustomsTypeId = exportCustomsTypeId;
    }

    public Long getExportCustomsTypeId() 
    {
        return exportCustomsTypeId;
    }
    public void setImportCustomsTypeId(Long importCustomsTypeId) 
    {
        this.importCustomsTypeId = importCustomsTypeId;
    }

    public Long getImportCustomsTypeId() 
    {
        return importCustomsTypeId;
    }
    public void setOpConfirmed(String opConfirmed) 
    {
        this.opConfirmed = opConfirmed;
    }

    public String getOpConfirmed() 
    {
        return opConfirmed;
    }
    public void setOpConfirmedId(Long opConfirmedId) 
    {
        this.opConfirmedId = opConfirmedId;
    }

    public Long getOpConfirmedId() 
    {
        return opConfirmedId;
    }
    public void setOpConfirmedDate(Date opConfirmedDate) 
    {
        this.opConfirmedDate = opConfirmedDate;
    }

    public Date getOpConfirmedDate() 
    {
        return opConfirmedDate;
    }
    public void setFinanceConfirmed(String financeConfirmed) 
    {
        this.financeConfirmed = financeConfirmed;
    }

    public String getFinanceConfirmed() 
    {
        return financeConfirmed;
    }
    public void setFinanceConfirmedId(Long financeConfirmedId) 
    {
        this.financeConfirmedId = financeConfirmedId;
    }

    public Long getFinanceConfirmedId() 
    {
        return financeConfirmedId;
    }
    public void setFinanceConfirmedDate(Date financeConfirmedDate) 
    {
        this.financeConfirmedDate = financeConfirmedDate;
    }

    public Date getFinanceConfirmedDate() 
    {
        return financeConfirmedDate;
    }
    public void setSalesConfirmed(String salesConfirmed) 
    {
        this.salesConfirmed = salesConfirmed;
    }

    public String getSalesConfirmed() 
    {
        return salesConfirmed;
    }
    public void setSalesConfirmedId(Long salesConfirmedId) 
    {
        this.salesConfirmedId = salesConfirmedId;
    }

    public Long getSalesConfirmedId() 
    {
        return salesConfirmedId;
    }
    public void setSalesConfirmedDate(Date salesConfirmedDate) 
    {
        this.salesConfirmedDate = salesConfirmedDate;
    }

    public Date getSalesConfirmedDate() 
    {
        return salesConfirmedDate;
    }
    public void setSupplierConfirmed(String supplierConfirmed) 
    {
        this.supplierConfirmed = supplierConfirmed;
    }

    public String getSupplierConfirmed() 
    {
        return supplierConfirmed;
    }
    public void setSupplierConfirmedId(Long supplierConfirmedId) 
    {
        this.supplierConfirmedId = supplierConfirmedId;
    }

    public Long getSupplierConfirmedId() 
    {
        return supplierConfirmedId;
    }
    public void setSupplierConfirmedDate(Date supplierConfirmedDate) 
    {
        this.supplierConfirmedDate = supplierConfirmedDate;
    }

    public Date getSupplierConfirmedDate() 
    {
        return supplierConfirmedDate;
    }
    public void setInvoiceQueryNo(String invoiceQueryNo) 
    {
        this.invoiceQueryNo = invoiceQueryNo;
    }

    public String getInvoiceQueryNo() 
    {
        return invoiceQueryNo;
    }

}
