package com.rich.common.core.domain.entity;

import java.math.BigDecimal;
import java.util.List;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import com.rich.common.utils.RsRctCustomExcelHandler;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 仓库客户信息对象 rs_warehouse_client
 *
 * <AUTHOR>
 * @date 2024-11-11
 */
public class RsWarehouseClient extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long warehouseClientId;

    /**
     * 客户系统id，跟客户资料公用
     */
    @Excel(name = "客户系统id，跟客户资料公用")
    private Long clientSystemId;

    /**
     * 客户名称
     */
    @Excel(name = "客户名称")
    private String clientName;

    /**
     * 客户的仓库简称/编码
     */
    @Excel(name = "客户的仓库简称/编码")
    private String clientCode;

    /**
     * 客户性质
     */
    @Excel(name = "客户性质")
    private String clientType;

    /**
     * 目的国
     */
    @Excel(name = "目的国")
    private String destinationCountry;

    /**
     * 收货人电话
     */
    @Excel(name = "收货人电话")
    private String consigneePhone;

    /**
     * 报价LCL
     */
    @Excel(name = "报价LCL")
    private BigDecimal rateLcl;

    /**
     * 报价20GP
     */
    @Excel(name = "报价20GP")
    private BigDecimal rate20gp;

    /**
     * 报价40HQ
     */
    @Excel(name = "报价40HQ")
    private BigDecimal rate40hq;

    /**
     * 报价4
     */
    @Excel(name = "报价4")
    private BigDecimal rate4;

    /**
     * 报价5
     */
    @Excel(name = "报价5")
    private BigDecimal rate5;

    /**
     * 免堆期
     */
    @Excel(name = "免堆期")
    private Long freeStackPeriod;

    /**
     * 超期仓租
     */
    @Excel(name = "超期仓租")
    private BigDecimal overdueRent;

    /**
     * 卸货费
     */
    @Excel(name = "卸货费")
    private BigDecimal unloadingFee;

    /**
     * 扣货
     */
    @Excel(name = "扣货")
    private String cargoDeduction;

    /**
     * 有效
     */
    @Excel(name = "有效")
    private String isActive;

    /**
     * 业务员
     */
    @Excel(name = "业务", handler = RsRctCustomExcelHandler.class, args = {"staff"})
    private Long salesId;
    private BigDecimal singlePieceWeight;
    private BigDecimal singlePieceVolume;
    private BigDecimal inboundFee;
    private BigDecimal standardInboundFee;
    private BigDecimal preciseInboundFee;
    private BigDecimal expressInboundFee;
    private String defaultRecordMode;
    private int cashSettlementFee;
    private String immediatePaymentFee;
    private String includesUnloadingFee;
    private int includesInboundFee;
    private int includesPackingFee;
    private String follower;

    private List<ExtCompany> companyList;

    public List<ExtCompany> getCompanyList() {
        return companyList;
    }

    public void setCompanyList(List<ExtCompany> companyList) {
        this.companyList = companyList;
    }

    public String getFollower() {
        return follower;
    }

    public void setFollower(String follower) {
        this.follower = follower;
    }

    public int getIncludesPackingFee() {
        return includesPackingFee;
    }

    public void setIncludesPackingFee(int includesPackingFee) {
        this.includesPackingFee = includesPackingFee;
    }

    public int getIncludesInboundFee() {
        return includesInboundFee;
    }

    public void setIncludesInboundFee(int includesInboundFee) {
        this.includesInboundFee = includesInboundFee;
    }

    public String getIncludesUnloadingFee() {
        return includesUnloadingFee;
    }

    public void setIncludesUnloadingFee(String includesUnloadingFee) {
        this.includesUnloadingFee = includesUnloadingFee;
    }

    public String getImmediatePaymentFee() {
        return immediatePaymentFee;
    }

    public void setImmediatePaymentFee(String immediatePaymentFee) {
        this.immediatePaymentFee = immediatePaymentFee;
    }

    public int getCashSettlementFee() {
        return cashSettlementFee;
    }

    public void setCashSettlementFee(int cashSettlementFee) {
        this.cashSettlementFee = cashSettlementFee;
    }

    public String getDefaultRecordMode() {
        return defaultRecordMode;
    }

    public void setDefaultRecordMode(String defaultRecordMode) {
        this.defaultRecordMode = defaultRecordMode;
    }

    public BigDecimal getExpressInboundFee() {
        return expressInboundFee;
    }

    public void setExpressInboundFee(BigDecimal expressInboundFee) {
        this.expressInboundFee = expressInboundFee;
    }

    public BigDecimal getPreciseInboundFee() {
        return preciseInboundFee;
    }

    public void setPreciseInboundFee(BigDecimal preciseInboundFee) {
        this.preciseInboundFee = preciseInboundFee;
    }

    public BigDecimal getStandardInboundFee() {
        return standardInboundFee;
    }

    public void setStandardInboundFee(BigDecimal standardInboundFee) {
        this.standardInboundFee = standardInboundFee;
    }

    public BigDecimal getInboundFee() {
        return inboundFee;
    }

    public void setInboundFee(BigDecimal inboundFee) {
        this.inboundFee = inboundFee;
    }

    public BigDecimal getSinglePieceVolume() {
        return singlePieceVolume;
    }

    public void setSinglePieceVolume(BigDecimal singlePieceVolume) {
        this.singlePieceVolume = singlePieceVolume;
    }

    public BigDecimal getSinglePieceWeight() {
        return singlePieceWeight;
    }

    public void setSinglePieceWeight(BigDecimal singlePieceWeight) {
        this.singlePieceWeight = singlePieceWeight;
    }

    public Long getWarehouseClientId() {
        return warehouseClientId;
    }

    public void setWarehouseClientId(Long warehouseClientId) {
        this.warehouseClientId = warehouseClientId;
    }

    public Long getClientSystemId() {
        return clientSystemId;
    }

    public void setClientSystemId(Long clientSystemId) {
        this.clientSystemId = clientSystemId;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getClientCode() {
        return clientCode;
    }

    public void setClientCode(String clientCode) {
        this.clientCode = clientCode;
    }

    public String getClientType() {
        return clientType;
    }

    public void setClientType(String clientType) {
        this.clientType = clientType;
    }

    public String getDestinationCountry() {
        return destinationCountry;
    }

    public void setDestinationCountry(String destinationCountry) {
        this.destinationCountry = destinationCountry;
    }

    public String getConsigneePhone() {
        return consigneePhone;
    }

    public void setConsigneePhone(String consigneePhone) {
        this.consigneePhone = consigneePhone;
    }

    public BigDecimal getRateLcl() {
        return rateLcl;
    }

    public void setRateLcl(BigDecimal rateLcl) {
        this.rateLcl = rateLcl;
    }

    public BigDecimal getRate20gp() {
        return rate20gp;
    }

    public void setRate20gp(BigDecimal rate20gp) {
        this.rate20gp = rate20gp;
    }

    public BigDecimal getRate40hq() {
        return rate40hq;
    }

    public void setRate40hq(BigDecimal rate40hq) {
        this.rate40hq = rate40hq;
    }

    public BigDecimal getRate4() {
        return rate4;
    }

    public void setRate4(BigDecimal rate4) {
        this.rate4 = rate4;
    }

    public BigDecimal getRate5() {
        return rate5;
    }

    public void setRate5(BigDecimal rate5) {
        this.rate5 = rate5;
    }

    public Long getFreeStackPeriod() {
        return freeStackPeriod;
    }

    public void setFreeStackPeriod(Long freeStackPeriod) {
        this.freeStackPeriod = freeStackPeriod;
    }

    public BigDecimal getOverdueRent() {
        return overdueRent;
    }

    public void setOverdueRent(BigDecimal overdueRent) {
        this.overdueRent = overdueRent;
    }

    public BigDecimal getUnloadingFee() {
        return unloadingFee;
    }

    public void setUnloadingFee(BigDecimal unloadingFee) {
        this.unloadingFee = unloadingFee;
    }

    public String getCargoDeduction() {
        return cargoDeduction;
    }

    public void setCargoDeduction(String cargoDeduction) {
        this.cargoDeduction = cargoDeduction;
    }

    public String getIsActive() {
        return isActive;
    }

    public void setIsActive(String isActive) {
        this.isActive = isActive;
    }

    public Long getSalesId() {
        return salesId;
    }

    public void setSalesId(Long salesId) {
        this.salesId = salesId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("warehouseClientId", getWarehouseClientId())
                .append("clientSystemId", getClientSystemId())
                .append("clientName", getClientName())
                .append("clientCode", getClientCode())
                .append("clientType", getClientType())
                .append("destinationCountry", getDestinationCountry())
                .append("consigneePhone", getConsigneePhone())
                .append("rateLcl", getRateLcl())
                .append("rate20gp", getRate20gp())
                .append("rate40hq", getRate40hq())
                .append("rate4", getRate4())
                .append("rate5", getRate5())
                .append("freeStackPeriod", getFreeStackPeriod())
                .append("overdueRent", getOverdueRent())
                .append("unloadingFee", getUnloadingFee())
                .append("cargoDeduction", getCargoDeduction())
                .append("isActive", getIsActive())
                .append("salesId", getSalesId())
                .toString();
    }
}
