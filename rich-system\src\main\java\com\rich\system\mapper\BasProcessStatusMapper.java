package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasProcessStatus;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 进度状态Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-05
 */
@Mapper
public interface BasProcessStatusMapper {
    /**
     * 查询进度状态
     *
     * @param processStatusId 进度状态主键
     * @return 进度状态
     */
    BasProcessStatus selectBasProcessStatusByProcessStatusId(Long processStatusId);

    /**
     * 查询进度状态列表
     *
     * @param basProcessStatus 进度状态
     * @return 进度状态集合
     */
    List<BasProcessStatus> selectBasProcessStatusList(BasProcessStatus basProcessStatus);

    /**
     * 新增进度状态
     *
     * @param basProcessStatus 进度状态
     * @return 结果
     */
    int insertBasProcessStatus(BasProcessStatus basProcessStatus);

    /**
     * 修改进度状态
     *
     * @param basProcessStatus 进度状态
     * @return 结果
     */
    int updateBasProcessStatus(BasProcessStatus basProcessStatus);

    /**
     * 删除进度状态
     *
     * @param processStatusId 进度状态主键
     * @return 结果
     */
    int deleteBasProcessStatusByProcessStatusId(Long processStatusId);

    /**
     * 批量删除进度状态
     *
     * @param processStatusIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasProcessStatusByProcessStatusIds(Long[] processStatusIds);
}
