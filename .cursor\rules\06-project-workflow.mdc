---
description:
globs:
alwaysApply: false
---
# 项目工作流与开发流程

本文档描述瑞旗管理系统的开发、测试和部署流程，确保项目开发过程的规范性。

## 开发流程

### 1. 需求分析与任务拆分
- 明确业务需求，编写需求文档
- 进行任务拆分，评估工作量
- 制定开发计划和里程碑

### 2. 数据库设计
- 根据业务需求设计数据库表结构
- 编写数据库建表脚本
- 审核表结构设计，确保符合规范

### 3. 后端接口开发
- 根据业务需求开发 Controller 接口
- 实现 Service 业务逻辑
- 编写 Mapper 数据访问层
- 单元测试

### 4. 前端/移动端开发
- 页面布局与组件开发
- 对接后端接口
- 实现交互功能
- 界面优化与兼容性调整

### 5. 代码审核与测试
- 进行代码审核(Code Review)
- 功能测试与问题修复
- 接口联调与问题修复

### 6. 构建与集成
- 后端项目打包
- 前端/移动端项目构建
- 集成测试环境部署

### 7. 验收与上线
- 产品验收测试
- 问题修复
- 生产环境部署

## 代码管理流程

### Git 使用规范
- 主分支: `master` - 稳定版本，用于生产环境
- 开发分支: `develop` - 开发中的版本，用于集成测试
- 特性分支: `feature/xxx` - 用于开发新功能
- 修复分支: `hotfix/xxx` - 用于修复紧急 Bug
- 发布分支: `release/x.x.x` - 用于版本发布准备

### 提交规范
- 提交消息格式: `类型(范围): 简短描述`
- 类型: feat(新功能)、fix(Bug修复)、docs(文档)、style(格式)、refactor(重构)、perf(性能优化)、test(测试)、chore(构建/工具)
- 每次提交保持功能单一，避免大量修改一次提交

### 分支合并流程
- 特性开发完成后，提交 Merge Request 到 develop 分支
- 代码审核通过后才能合并
- 定期将 develop 分支合并到 master 分支进行版本发布

## 部署流程

### 开发环境部署
- 本地开发环境配置
- 后端服务本地启动
- 前端服务本地启动
- 移动端项目本地调试

### 测试环境部署
- Jenkins 自动化构建
- 后端服务打包为 jar 文件
- 前端项目编译为静态资源
- 移动端项目打包为测试版本
- 部署到测试服务器

### 生产环境部署
- 创建发布分支，进行版本发布准备
- 完成最终测试和确认
- 合并到 master 分支
- Jenkins 自动化构建生产版本
- 更新数据库（如有变更）
- 滚动更新应用服务
- 配置 Nginx 和负载均衡

## 持续集成与交付

### Jenkins 持续集成
- 代码提交触发自动构建
- 运行单元测试
- 执行代码质量检查
- 生成构建报告

### 自动化测试
- 单元测试: JUnit
- 接口测试: Postman/JMeter
- UI 测试: Selenium

### 监控与告警
- 应用性能监控
- 错误日志监控
- 系统资源监控
- 告警通知机制
