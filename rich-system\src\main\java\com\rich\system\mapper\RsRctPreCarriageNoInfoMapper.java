package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsRctPreCarriageNoInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 操作单前程运输编号信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-15
 */
@Mapper
public interface RsRctPreCarriageNoInfoMapper {
    /**
     * 查询操作单前程运输编号信息
     *
     * @param preCarriageNoInfoId 操作单前程运输编号信息主键
     * @return 操作单前程运输编号信息
     */
    RsRctPreCarriageNoInfo selectRsRctPreCarriageNoInfoByPreCarriageNoInfo(Long preCarriageNoInfoId);

    /**
     * 查询操作单前程运输编号信息列表
     *
     * @param rsRctPreCarriageNoInfo 操作单前程运输编号信息
     * @return 操作单前程运输编号信息集合
     */
    List<RsRctPreCarriageNoInfo> selectRsRctPreCarriageNoInfoList(RsRctPreCarriageNoInfo rsRctPreCarriageNoInfo);

    /**
     * 新增操作单前程运输编号信息
     *
     * @param rsRctPreCarriageNoInfo 操作单前程运输编号信息
     * @return 结果
     */
    int insertRsRctPreCarriageNoInfo(RsRctPreCarriageNoInfo rsRctPreCarriageNoInfo);

    /**
     * 修改操作单前程运输编号信息
     *
     * @param rsRctPreCarriageNoInfo 操作单前程运输编号信息
     * @return 结果
     */
    int updateRsRctPreCarriageNoInfo(RsRctPreCarriageNoInfo rsRctPreCarriageNoInfo);

    /**
     * 删除操作单前程运输编号信息
     *
     * @return 结果
     */
    int deleteRsRctPreCarriageNoInfoByRctId(Long rctId);

    /**
     * 批量删除操作单前程运输编号信息
     *
     * @return 结果
     */
    int deleteRsRctPreCarriageNoInfoByIds(Long[] rctIds);
}
