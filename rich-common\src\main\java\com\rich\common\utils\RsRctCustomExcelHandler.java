package com.rich.common.utils;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.domain.entity.BasDistServiceType;
import com.rich.common.core.domain.entity.RsStaff;
import com.rich.common.utils.poi.ExcelHandlerAdapter;
import com.rich.common.core.redis.RedisCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/6 9:44
 * @Version 1.0
 */
@Component
public class RsRctCustomExcelHandler implements ExcelHandlerAdapter {

    private RedisCache redisCache;

    @Override
    public Object format(Object value, String[] args) {
        if (redisCache == null) {
            redisCache = SpringContextHolder.getBean(RedisCache.class); // 在方法调用时获取
        }

        if (value == null) {
            return "";
        }

        if (args != null && args.length > 0 && "client".equals(args[0])) {
            String[] parts = value.toString().split("/");
            return parts.length > 1 ? parts[1] : value.toString();
        }

        if (args != null && args.length > 0 && "service".equals(args[0])) {
            String[] parts = value.toString().split(",");
            if (parts.length > 0) {
                Long serviceId = Long.parseLong(parts[0]);
                List<BasDistServiceType> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
                if (list == null) {
                    return "";
                }
                for (BasDistServiceType basDistServiceType : list) {
                    if (basDistServiceType.getServiceTypeId().equals(serviceId)) {
                        return basDistServiceType.getServiceShortName();
                    }
                }
            }
        }

        if (args != null && args.length > 0 && "staff".equals(args[0])) {
            List<RsStaff> rsStaffs = (List<RsStaff>) redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "allRsStaff");
            if (rsStaffs == null) {
                return "";
            }

            for (RsStaff staff : rsStaffs) {
                if (staff.getStaffId().equals(value)) {
                    return staff.getStaffFamilyLocalName() + staff.getStaffGivingLocalName() + staff.getStaffGivingEnName();
                }
            }
        }

        return null;
    }
}

