package com.rich.system.service.impl;

import com.rich.common.constant.CacheConstants;
import com.rich.common.core.domain.entity.*;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SearchUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.StringUtils;
import com.rich.system.domain.*;
import com.rich.system.mapper.*;
import com.rich.system.service.RsCharacteristicsService;
import com.rich.system.service.RsLocalService;
import com.rich.system.service.RsQuotationService;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.security.SecureRandom;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报价列表Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-04
 */
@Service

public class RsQuotationServiceImpl implements RsQuotationService {
    @Autowired
    private RsQuotationMapper rsQuotationMapper;

    @Autowired
    private MidLocationLoadingMapper midLocationLoadingMapper;

    @Autowired
    private MidLocationDepartureMapper midLocationDepartureMapper;

    @Autowired
    private MidServiceTypeMapper midServiceTypeMapper;

    @Autowired
    private MidCargoTypeMapper midCargoTypeMapper;

    @Autowired
    private MidCarrierMapper midCarrierMapper;

    @Autowired
    private RedisCacheImpl RedisCache;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RsLocalService rsLocalService;

    @Autowired
    private RsCharacteristicsService rsCharacteristicsService;

    @Autowired
    private MidRevenueTonsMapper midRevenueTonsMapper;

    @Autowired
    private RsQuotationFreightMapper rsQuotationFreightMapper;

    @Autowired
    private MidQuotationCharacteristicsMapper midQuotationCharacteristicsMapper;

    @Autowired
    private BasDistLocationMapper basDistLocationMapper;

    @Autowired
    private BasDistLineMapper basDistLineMapper;

    @Autowired
    private BasDistServiceTypeMapper basDistServiceTypeMapper;

    @Autowired
    private BasDistCargoTypeMapper basDistCargoTypeMapper;

    @Autowired
    private BasDistUnitMapper basDistUnitMapper;

    @Autowired
    private BasDistDeptMapper basDistDeptMapper;

    @Autowired
    private TransactionTemplate transactionTemplate;

    /**
     * 根据报价返回费用列表
     *
     * @param rsQuotation
     * @return
     */
    @Override
    public List<RsFreight> queryFreight(RsQuotation rsQuotation) {
        // 存的都是freight的id[[],[]]
        List<List<Long>> fl = new ArrayList<>();

        List<BasDistLocation> locations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        if (locations == null) {
            RedisCache.location();
            locations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        }
        List<RsFreight> freights = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "freight");
        if (freights == null) {
            RedisCache.freight();
            freights = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "freight");
        }
        List<BasDistUnit> units = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "unit");
        if (units == null) {
            RedisCache.unit();
            units = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "unit");
        }
        List<BasDistServiceType> serviceTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
        if (serviceTypes == null) {
            RedisCache.serviceType();
            serviceTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
        }
        Set<Long> fLogisticsType = new HashSet<>();
        // 物流类型
        Set<Long> logisticsTypeIds = new HashSet<>();
        // 获取报价的物流类型进行匹配
        if (rsQuotation.getLogisticsTypeId() != null) {
            for (BasDistServiceType serviceType : serviceTypes) {
                if (rsQuotation.getLogisticsTypeId().equals(serviceType.getServiceTypeId())) {
                    logisticsTypeIds.add(serviceType.getServiceTypeId());
                }
            }
        }
        Set<Long> fServiceType = new HashSet<>();
        Set<Long> serviceTypeIds = new HashSet<>();
        // 获取报价的物流类型进行匹配
        if (rsQuotation.getServiceTypeId() != null) {
            for (BasDistServiceType serviceType : serviceTypes) {
                if (rsQuotation.getServiceTypeId().equals(serviceType.getServiceTypeId())) {
                    serviceTypeIds.add(serviceType.getServiceTypeId());
                }
            }
        }
        Set<Long> fLoading = new HashSet<>();
        Set<Long> loadingIds = new HashSet<>();
        Set<Long> fDeparture = new HashSet<>();
        Set<Long> departureIds = new HashSet<>();
        Set<Long> fDestination = new HashSet<>();
        Set<Long> destinationIds = new HashSet<>();
        // 获取报价中的装运区域、启运港、目的港进行匹配
        if (rsQuotation.getLoadingIds() != null || rsQuotation.getDepartureId() != null || rsQuotation.getDestinationId() != null) {
            for (BasDistLocation location : locations) {
                // typeId 费用类型（海运、空运、快递等）                                                                      拖车或仓储
                // 匹配装运区域
                if (rsQuotation.getLoadingIds() != null && rsQuotation.getTypeId() != null && (rsQuotation.getTypeId() == 4 || rsQuotation.getTypeId() == 5 || rsQuotation.getTypeId() == 8)) {
                    if (ArrayUtils.contains(rsQuotation.getLoadingIds(), location.getLocationId())) {
                        loadingIds.add(location.getLocationId());
                        for (String s : location.getAncestors().split(",")) {
                            loadingIds.add(Long.parseLong(s));
                        }
                    }
                    if (ArrayUtils.isNotEmpty(rsQuotation.getLoadingIds()) && SearchUtils.existSame(rsQuotation.getLoadingIds(), location.getAncestors().split(","))) {
                        loadingIds.add(location.getLocationId());
                    }
                }
                // 匹配启运区域
                if (rsQuotation.getDepartureId() != null && rsQuotation.getTypeId() != null && rsQuotation.getTypeId() != 7 && rsQuotation.getTypeId() != 4 && rsQuotation.getTypeId() != 9) {
                    if (rsQuotation.getDepartureId().equals(location.getLocationId())) {
                        departureIds.add(location.getLocationId());
                        for (String s : location.getAncestors().split(",")) {
                            departureIds.add(Long.parseLong(s));
                        }
                    }
                    if (ArrayUtils.contains(location.getAncestors().split(","), rsQuotation.getDepartureId().toString())) {
                        departureIds.add(location.getLocationId());
                    }
                }
                // 匹配目的区域 （不是拖车和仓储的报价时）
                if (rsQuotation.getDestinationId() != null && rsQuotation.getTypeId() != null && rsQuotation.getTypeId() != 4 && rsQuotation.getTypeId() != 5 && rsQuotation.getTypeId() != 6 && rsQuotation.getTypeId() != 8) {
                    if (rsQuotation.getDestinationId().equals(location.getLocationId())) {
                        destinationIds.add(location.getLocationId());
                        for (String s : location.getAncestors().split(",")) {
                            destinationIds.add(Long.parseLong(s));
                        }
                    }
                    if (ArrayUtils.contains(location.getAncestors().split(","), rsQuotation.getDestinationId().toString())) {
                        destinationIds.add(location.getLocationId());
                    }

                }
            }
        }
        Set<Long> fUnit = new HashSet<>();
        Map<Long, String> unit = new HashMap<>();
        if (rsQuotation.getMidRevenueTonsList() != null && rsQuotation.getTypeId() != null && rsQuotation.getTypeId() != 6 && rsQuotation.getTypeId() != 7) {
            // 遍历货量
            for (MidRevenueTons midrevenueTons : rsQuotation.getMidRevenueTonsList()) {
                for (BasDistUnit basDistUnit : units) {
                    if (midrevenueTons.getUnitId() != null && midrevenueTons.getUnitId().equals(basDistUnit.getUnitId())) {
                        unit.put(basDistUnit.getUnitId(), basDistUnit.getUnitShortName());
                    }
                }
            }
        }
        if (rsQuotation.getUnitId() != null) {
            for (BasDistUnit basDistUnit : units) {
                if (rsQuotation.getUnitId().equals(basDistUnit.getUnitId()) || (rsQuotation.getUnitId().equals(basDistUnit.getParentId()) && basDistUnit.getMain().equals(1))) {
                    unit.put(basDistUnit.getUnitId(), basDistUnit.getUnitShortName());
                }
            }
        }
        Set<Long> fCarrier = new HashSet<>();
        Set<Long> fSupplier = new HashSet<>();
        Set<Long> time = new HashSet<>();
        // 匹配费用
        for (RsFreight freight : freights) {
            if (freight.getLogisticsTypeId() != null && !logisticsTypeIds.isEmpty() && logisticsTypeIds.contains(freight.getLogisticsTypeId())) {
                fLogisticsType.add(freight.getFreightId());
            }
            if (freight.getServiceTypeId() != null && !serviceTypeIds.isEmpty() && serviceTypeIds.contains(freight.getServiceTypeId())) {
                fServiceType.add(freight.getFreightId());
            }
            if (freight.getPrecarriageRegionId() != null && !loadingIds.isEmpty() && loadingIds.contains(freight.getPrecarriageRegionId())) {
                fLoading.add(freight.getFreightId());
            }
            if (rsQuotation.getGoodsTime() != null) {
                if (freight.getValidTo() == null || freight.getValidFrom() != null && freight.getValidFrom().compareTo(rsQuotation.getGoodsTime()) < 0) {
                    time.add(freight.getFreightId());
                }
            } else {
                Date date = DateUtils.getNowDate();
                if (freight.getValidTo() == null || freight.getValidFrom() != null && freight.getValidFrom().compareTo(date) < 0) {
                    time.add(freight.getFreightId());
                }
            }
            if (freight.getPolId() != null && !departureIds.isEmpty() && departureIds.contains(freight.getPolId())) {
                fDeparture.add(freight.getFreightId());
            }
            if ((freight.getDestinationPortId() != null && !destinationIds.isEmpty() && destinationIds.contains(freight.getDestinationPortId()))
                    || (rsQuotation.getTypeId() != null && rsQuotation.getTypeId() == 5 && freight.getDestinationPortId() == null)) {
                fDestination.add(freight.getFreightId());
            }
            /*if (freight.getCarrierCode() != null && rsQuotation.getCarrierIds() != null && rsQuotation.getTypeId() != null && (rsQuotation.getTypeId() == 1 || rsQuotation.getTypeId() == 2 || rsQuotation.getTypeId() == 3) && ArrayUtils.contains(rsQuotation.getCarrierIds(), freight.getCarrierCode())) {
                fCarrier.add(freight.getFreightId());
            }*/
            if (!unit.isEmpty() && ((unit.containsValue(freight.getUnitCode())) ||
                    (rsQuotation.getTypeId() != null
                            && rsQuotation.getTypeId() != 5
                            && (freight.getUnitId() != null || freight.getUnitCode() != null)
                            && ((freight.getUnitCode() != null && freight.getUnitCode().equals("Ctnr")) && (unit.containsValue("20GP") || unit.containsValue("40GP") || unit.containsValue("40HQ")) || unit.containsKey(freight.getUnitId()))))) {
                fUnit.add(freight.getFreightId());
            }
            if (rsQuotation.getCompanyId() != null && freight.getSupplierId() != null && freight.getSupplierId().equals(rsQuotation.getCompanyId())) {
                fSupplier.add(freight.getFreightId());
            }
        }
        if (rsQuotation.getLoadingIds() != null && rsQuotation.getLoadingIds().length > 0 && rsQuotation.getTypeId() != null && (rsQuotation.getTypeId() == 4 || rsQuotation.getTypeId() == 5 || rsQuotation.getTypeId() == 8)) {
            fl.add(new ArrayList<>(fLoading));
        }
        // 费用的物流类型
        if (rsQuotation.getLogisticsTypeId() != null) {
            fl.add(new ArrayList<>(fLogisticsType));
        }
        if (rsQuotation.getServiceTypeId() != null) {
            fl.add(new ArrayList<>(fServiceType));
        }
        if (rsQuotation.getDepartureId() != null && rsQuotation.getTypeId() != 4 && rsQuotation.getTypeId() != 7 && rsQuotation.getTypeId() != 8) {
            fl.add(new ArrayList<>(fDeparture));
        }
        if (rsQuotation.getDestinationId() != null && rsQuotation.getTypeId() != null && rsQuotation.getTypeId() != 4 && rsQuotation.getTypeId() != 5 && rsQuotation.getTypeId() != 6 && rsQuotation.getTypeId() != 8) {
            fl.add(new ArrayList<>(fDestination));
        }
        if ((rsQuotation.getUnitId() != null || rsQuotation.getMidRevenueTonsList() != null) && (rsQuotation.getMidRevenueTonsList() != null && !rsQuotation.getMidRevenueTonsList().isEmpty()) && rsQuotation.getTypeId() != null && rsQuotation.getTypeId() != 5 && rsQuotation.getTypeId() != 6) {
            fl.add(new ArrayList<>(fUnit));
        }
        /*if (rsQuotation.getCompanyId() != null) {
            fl.add(new ArrayList<>(fSupplier));
        }*/
        /*
        if (rsQuotation.getCarrierIds() != null && rsQuotation.getCarrierIds().length > 0 && rsQuotation.getTypeId() != null && (rsQuotation.getTypeId() == 1 || rsQuotation.getTypeId() == 2 || rsQuotation.getTypeId() == 3)) {
            fl.add(new ArrayList<>(fCarrier));
        }*/
        fl.add(new ArrayList<>(time));
        List<RsFreight> fList = new ArrayList<>();
        List<Long> fll = SearchUtils.getLongs(fl);
        HashSet<String> carriers = new HashSet<>();
        if (rsQuotation.getCarriers() != null) {
            carriers = new HashSet<>(Arrays.asList(rsQuotation.getCarriers()));
        }
        for (RsFreight freight : freights) {
            if (fll.contains(freight.getFreightId()) && Objects.equals(freight.getIsValid(), "Y")) {
                if ((rsQuotation.getCarriers() != null && rsQuotation.getCarriers().length > 0) || rsQuotation.getUnitCode() != null) {
                    if ((rsQuotation.getCarriers() != null && rsQuotation.getCarriers().length > 0) && rsQuotation.getUnitCode() != null) {
                        if ((rsQuotation.getUnitCode() != null && rsQuotation.getUnitCode().equals(freight.getUnitCode())) && (rsQuotation.getCarriers() != null && carriers.contains(freight.getCarrierCode()))) {
                            fList.add(freight);
                        }
                    } else if ((rsQuotation.getCarriers() != null && carriers.contains(freight.getCarrierCode()))) {
                        fList.add(freight);
                    } else if ((rsQuotation.getUnitCode() != null && rsQuotation.getUnitCode().equals(freight.getUnitCode()))) {
                        fList.add(freight);
                    }
                } else {
                    fList.add(freight);
                }
            }
        }
        /**
         * collectingAndThen  它能够将一个Collector收集器转换为另一个收集器，并在最终收集结果上应用一个Function函数  它接收两个参数：一个收集器和一个函数。首先，它会使用给定的收集器执行收集操作，然后将收集的结果传递给指定的函数进行处理，并返回处理后的结果。
         * toCollection      它将流中的元素收集到一个指定类型的集合中。
         * Comparator.comparing(RsFreight -> RsFreight.getCarrierId() + ";" + RsFreight.getValidFrom() + ";" + RsFreight.getValidTo()) 根据RsFreight的三个属性去重
         * TreeSet的泛型对象不是java的基本类型的包装类时，对象需要重写Comparable#compareTo()方法
         */
//        List<RsFreight> same = fList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(RsFreight -> RsFreight.getCarrierCode() + ";" + RsFreight.getValidFrom() + ";" + RsFreight.getValidTo()))), ArrayList::new));
//        List<RsFreight> same = fList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(RsFreight -> RsFreight.getValidFrom() + ";" + RsFreight.getValidTo()))), ArrayList::new));
        /**
         * nullsLast
         * comparing
         */
        return fList.stream().sorted(Comparator.comparing(RsFreight::getValidTo, Comparator.nullsLast(Comparator.naturalOrder())).reversed()).filter(fList::contains).collect(Collectors.toList());
    }

    @Override
    public List<RsLocalCharge> queryLocal(RsFreight rsFreight) {
        Long departureLineId = null;
        Long destinationLineId = null;
        // 区域
        List<BasDistLocation> locations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        if (locations == null) {
            RedisCache.location();
            locations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        }
        // 航线
        List<BasDistLine> lines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        if (lines == null) {
            RedisCache.line();
            lines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        }
        // 物流附加费
        List<RsLocalCharge> locals = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "local");
        if (locals == null) {
            RedisCache.local();
            locals = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "local");
        }
        // 货物类型
        List<BasDistCargoType> cargoTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        if (cargoTypes == null) {
            RedisCache.cargoType();
            cargoTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        }
        // 计费单位
        List<BasDistUnit> units = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "unit");
        if (units == null) {
            RedisCache.unit();
            units = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "unit");
        }
        // 将启运其余和目的区域筛选出来
        Set<Long> kidLocationsDeparture = new HashSet<>();
        Set<Long> kidLinesDeparture = new HashSet<>();
        Set<Long> kidLocationsDestination = new HashSet<>();
        Set<Long> kidLinesDestination = new HashSet<>();
        // 匹配起运港和目的港
        if (rsFreight.getPolId() != null || rsFreight.getDestinationPortId() != null) {
            // 遍历缓存中的区域 匹配启运和目的区域和航线
            for (BasDistLocation location : locations) {
                if (rsFreight.getPolId() != null) {
                    // 搜索对象的启运港(包括父级）
                    if (rsFreight.getPolId().equals(location.getLocationId())) {
                        kidLocationsDeparture.add(rsFreight.getPolId());
                        // 遍历父级
                        for (String a : location.getAncestors().split(",")) {
                            kidLocationsDeparture.add(Long.parseLong(a));
                        }
                        // 区域的航线,如果该区域有航线
                        if (location.getLineId() != null) {
                            departureLineId = location.getLineId();
                            kidLinesDeparture.add(location.getLineId());
                            // 遍历缓存中的航线
                            for (BasDistLine line : lines) {
                                if (line.getLineId().equals(location.getLineId())) {
                                    for (String a : line.getAncestors().split(",")) {
                                        kidLinesDeparture.add(Long.parseLong(a));
                                    }
                                }
                                if (ArrayUtils.contains(line.getAncestors().split(","), location.getLineId().toString())) {
                                    kidLinesDeparture.add(line.getLineId());
                                }
                            }
                        }
                    }
                    // 如果是上级区域也添加到数组中
                    if (ArrayUtils.contains(location.getAncestors().split(","), rsFreight.getPolId().toString())) {
                        kidLocationsDeparture.add(location.getLocationId());
                    }
                }
                if (rsFreight.getDestinationPortId() != null) {
                    if (rsFreight.getDestinationPortId().equals(location.getLocationId())) {
                        kidLocationsDestination.add(rsFreight.getDestinationPortId());
                        for (String a : location.getAncestors().split(",")) {
                            kidLocationsDestination.add(Long.parseLong(a));
                        }
                        if (location.getLineId() != null) {
                            destinationLineId = location.getLineId();
                            kidLinesDestination.add(location.getLineId());
                            for (BasDistLine line : lines) {
                                if (line.getLineId().equals(location.getLineId())) {
                                    for (String a : line.getAncestors().split(",")) {
                                        kidLinesDestination.add(Long.parseLong(a));
                                    }
                                }
                                if (ArrayUtils.contains(line.getAncestors().split(","), location.getLineId().toString())) {
                                    kidLinesDestination.add(line.getLineId());
                                }
                            }
                        }
                    }
                    if (ArrayUtils.contains(location.getAncestors().split(","), rsFreight.getDestinationPortId().toString())) {
                        kidLocationsDestination.add(location.getLocationId());
                    }
                }
            }
        }
        Set<Long> kidsCargoType = new HashSet<>();
        // 货物类型
        if (rsFreight.getCargoTypeIds() != null) {
            for (BasDistCargoType cargoType : cargoTypes) {
                String[] ancestors = cargoType.getAncestors().split(",");
                if (ArrayUtils.contains(rsFreight.getCargoTypeIds(), cargoType.getCargoTypeId())) {
                    for (String cg : ancestors) {
                        kidsCargoType.add(Long.parseLong(cg));
                    }
                }
                if (SearchUtils.existSame(ancestors, rsFreight.getCargoTypeIds())) {
                    kidsCargoType.add(cargoType.getCargoTypeId());
                }
            }
        }
        Set<Long> lCarrier = new HashSet<>();
        // 同一个服务类型的localId集合
        Set<Long> lServiceType = new HashSet<>();
        Set<Long> lCargoType = new HashSet<>();
        Set<Long> lTime = new HashSet<>();
        Set<Long> lCompany = new HashSet<>();
        // 费用和local有相同的起运区域（包括航线）将local的id放入其中
        Set<Long> departure = new HashSet<>();
        Set<Long> destination = new HashSet<>();
        // 遍历缓存中的基础附加费,将前面的kidXxx与之对比
        // 从中间表中获取local的起运区域航线和目的区域航线，跟当前费用做匹配
        for (RsLocalCharge local : locals) {
            // 启运（包括区域和航线）
            // 获取这个local对应的起运区域
            List<Long> locationDeparture = rsLocalService.selectLocationDeparture(local.getLocalChargeId());
            if (SearchUtils.existSame(locationDeparture.toArray(), kidLocationsDeparture.toArray())) {
                departure.add(local.getLocalChargeId());
            }
            // 获取这个local对应的起运航线
            List<Long> lineDeparture = rsLocalService.selectLineDeparture(local.getLocalChargeId());
            if (SearchUtils.existSame(lineDeparture.toArray(), kidLinesDeparture.toArray())) {
                departure.add(local.getLocalChargeId());
            }
            // 获取这个local对应的目的区域
            List<Long> locationDestination = rsLocalService.selectLocationDestination(local.getLocalChargeId());
            if (SearchUtils.existSame(locationDestination.toArray(), kidLocationsDestination.toArray())) {
                destination.add(local.getLocalChargeId());
            }
            // 获取这个local对应的目的航线
            List<Long> lineDestination = rsLocalService.selectLineDestination(local.getLocalChargeId());
            if (SearchUtils.existSame(lineDestination.toArray(), kidLinesDestination.toArray())) {
                destination.add(local.getLocalChargeId());
            }
            // 有效期
            if (rsFreight.getValidTo() != null) {
                if (local.getValidTo() == null || local.getValidTo().compareTo(rsFreight.getValidTo()) > 0 && local.getValidTo().compareTo(DateUtils.getNowDate()) > 0) {
                    lTime.add(local.getLocalChargeId());
                }
            } else {
                if (local.getValidTo() == null || local.getValidTo().compareTo(DateUtils.getNowDate()) > 0) {
                    lTime.add(local.getLocalChargeId());
                }
            }
            // 服务类型
            if (rsFreight.getServiceTypeId() != null && local.getServiceTypeId().equals(rsFreight.getServiceTypeId())) {
                lServiceType.add(local.getLocalChargeId());
            }
            // 货物类型
            List<Long> cargoTypeList = rsLocalService.selectCargoTypes(local.getLocalChargeId());
            if (rsFreight.getCargoTypeIds() != null && (SearchUtils.existSame(cargoTypeList.toArray(), kidsCargoType.toArray()) || SearchUtils.existSame(cargoTypeList.toArray(), rsFreight.getCargoTypeIds()) || cargoTypeList.isEmpty())) {
                lCargoType.add(local.getLocalChargeId());
            }
            // 相关客户
            /*if (local.getSupplierId() == null || rsFreight.getSupplierId() != null && local.getSupplierId().equals(rsFreight.getSupplierId())) {
                lCompany.add(local.getLocalChargeId());
            }*/
        }
        // 创建一个空的List<List<Long>>对象out，用于存储符合条件的RsLocal对象的ID列表。
        List<List<Long>> out = new ArrayList<>();
        // 将匹配的各个local的id都添加到out中
        if (rsFreight.getPolId() != null) {
            out.add(new ArrayList<>(departure));
        }
        if (rsFreight.getDestinationPortId() != null) {
            out.add(new ArrayList<>(destination));
        }
        if (rsFreight.getValidTo() != null) {
            out.add(new ArrayList<>(lTime));
        }
        if (rsFreight.getServiceTypeId() != null) {
            out.add(new ArrayList<>(lServiceType));
        }
        if (rsFreight.getCargoTypeIds() != null && rsFreight.getCargoTypeIds().length > 0) {
            out.add(new ArrayList<>(lCargoType));
        }
        /*if (rsFreight.getSupplierId() != null) {
            out.add(new ArrayList<>(lCompany));
        }*/
        // 找出在每个数组（departure、destination、lServiceType等）中都存在的local
        List<Long> longs = SearchUtils.getLongs(out);
        // 第一次筛选的基础附加费结果集
        List<RsLocalCharge> list = new ArrayList<>();
        // longs与缓存中的locals中匹配
        for (RsLocalCharge lo : locals) {
            if (longs.contains(lo.getLocalChargeId()) && Objects.equals(lo.getIsValid(), "Y")) {
                list.add(lo);
            }
        }
        List<RsLocalCharge> outData = new ArrayList<>();
        // 对匹配的local进行筛选
        for (RsLocalCharge lo : list) {
            // 如果outData中已经包含lo，则跳过当前循环。
            if (outData.contains(lo)) {
                continue;
            }
            // 根据lo的chargeId从list中筛选出所有chargeId相同的RsLocal对象列表rsLocals。
            // 只拿跟元素同一种费用，然后取最匹配的一个
            // 同一种服务类型只会有一个local，如有多个匹配的整柜海运local最终只能选择一条local
            List<RsLocalCharge> rsLocals = list.stream().filter(RsLocalCharge -> RsLocalCharge.getChargeId().equals(lo.getChargeId())).collect(Collectors.toList());
            if (!rsLocals.isEmpty()) {
                // 作为结果集之一，初始化
                RsLocalCharge output = null;
                // 将同一种费用类型（服务类型，如：整柜海运）的local进行匹配
                for (RsLocalCharge local : rsLocals) {
                    boolean con = false;
                    // 第一个元素先初始化用来做对比，第二个开始比较
                    if (output == null) {
                        output = local;
                        if (rsLocals.size() > 1) {
                            continue;
                        }
                    }

                    int scoreA = 0;//output
                    int scoreB = 0;//local

                    // 给output（RsLocalCharge）赋予各种属性值，用于决定返回local还是output
                    if (output.getLocationDepartureIds() == null) {
                        output.setLocationDepartureIds(rsLocalService.selectLocationDeparture(output.getLocalChargeId()).toArray(new Long[0]));
                    }
                    if (output.getLocationDestinationIds() == null) {
                        output.setLocationDestinationIds(rsLocalService.selectLocationDestination(output.getLocalChargeId()).toArray(new Long[0]));
                    }
                    if (output.getLineDepartureIds() == null) {
                        output.setLineDepartureIds(rsLocalService.selectLineDeparture(output.getLocalChargeId()).toArray(new Long[0]));
                    }
                    if (output.getLineDestinationIds() == null) {
                        output.setLineDestinationIds(rsLocalService.selectLineDestination(output.getLocalChargeId()).toArray(new Long[0]));
                    }
                    if (output.getCargoTypeIds() == null) {
                        output.setCargoTypeIds(rsLocalService.selectCargoTypes(output.getLocalChargeId()).toArray(new Long[0]));
                    }
                    // 给local也同样赋值，但只有一个的时候就不需要了（不用比较赋值没意义）
                    if (rsLocals.size() > 1) {
                        local.setLocationDepartureIds(rsLocalService.selectLocationDeparture(local.getLocalChargeId()).toArray(new Long[0]));
                        local.setLocationDestinationIds(rsLocalService.selectLocationDestination(local.getLocalChargeId()).toArray(new Long[0]));
                        local.setLineDepartureIds(rsLocalService.selectLineDeparture(local.getLocalChargeId()).toArray(new Long[0]));
                        local.setLineDestinationIds(rsLocalService.selectLineDestination(local.getLocalChargeId()).toArray(new Long[0]));
                        local.setCargoTypeIds(rsLocalService.selectCargoTypes(local.getLocalChargeId()).toArray(new Long[0]));
                    }

                    // 单位
//                    Long unitPid = null;
                    BasDistUnit outputP = null;
                    BasDistUnit localP = null;
                    // 遍历计费单位列表units
                    for (BasDistUnit basDistUnit : units) {
                        // 如果rsFreight的unitId不为空且与basDistUnit的unitId相等，则将unitPid设置为basDistUnit的parentId
                        /*if (rsFreight.getUnitCode() != null && rsFreight.getUnitCode().equals(basDistUnit.getUnitCode())) {
                            unitPid = basDistUnit.getParentId();
                        }*/
                        // 如果output的unitId不为空且与basDistUnit的unitId相等，则将outputP设置为basDistUnit
                        if (output.getUnitCode() != null && output.getUnitCode().equals(basDistUnit.getUnitCode())) {
                            outputP = basDistUnit;
                        }
                        // 如果local的unitId不为空且与basDistUnit的unitId相等，则将localP设置为basDistUnit
                        if (local.getUnitCode() != null && local.getUnitCode().equals(basDistUnit.getUnitCode())) {
                            localP = basDistUnit;
                        }
                    }
                    if (rsFreight.getUnitCode() != null) {
                        if (output.getUnitCode() != null) {
                            // 如果output的unitId与rsFreight的unitId相等，则将scoreA增加100。
                            if (output.getUnitCode().equals(rsFreight.getUnitCode())) {
                                scoreA += 100;
                            }
                            // 如果output的unitId与unitPid相等，则将scoreA增加50。
                            if (output.getUnitCode().equals("Ctnr")) {
                                scoreA += 50;
                            }
                            //如果outputP不为空且outputP的parentId与rsFreight的unitId相等且outputP的main为0(是否为标准柜)，
                            // 则将output加入outData，并且如果outData中不包含local，则将output设置为local，将con设置为true。
                            // 并将output设置为当前的local,后面就会continue
                            //                              柜子单位                                                   标准柜
                            // if (outputP != null && outputP.getParentId().equals(rsFreight.getUnitId()) && outputP.getMain() == 0) {
                            /*if (outputP != null && ((outputP.getParentId().equals(0L) && "Ctnr".equals(rsFreight.getUnitCode())) || (outputP.getParentId().equals(2L) && findUnit(rsFreight.getUnitCode()))) && outputP.getMain() == 0) {
                                outData.add(output);
                                // 输出结果集中没有当前的local,output为当前local
                                if (!outData.contains(local)) {
                                    output = local;
                                }
                                con = true;
                            }*/
                        }
                        if (local.getUnitCode() != null) {
                            if (local.getUnitCode().equals(rsFreight.getUnitCode())) {
                                scoreB += 100;
                            }
                            /*if (local.getUnitId().equals(unitPid)) {
                                scoreB += 50;
                            }
                            if (localP != null && ((outputP.getParentId().equals(0L) && "Ctnr".equals(rsFreight.getUnitCode())) || (outputP.getParentId().equals(2L) && findUnit(rsFreight.getUnitCode()))) && localP.getMain() == 0) {
                                outData.add(local);
                                con = true;
                            }*/
                        }
                    }
                    if (con) {
                        continue;
                        // 如果outData中已经包含output且rsLocals的大小为1，则跳出循环。
                    } else if (outData.contains(output) && rsLocals.size() == 1) {
                        break;
                    }

                    // 区域
                    for (BasDistLocation location : locations) {
                        if (output.getLocationDepartureIds().length > 0 && local.getLocationDepartureIds().length > 0) {
                            int a = 0;
                            int b = 0;
                            if (rsFreight.getPolId() != null && rsFreight.getPolId().equals(location.getLocationId())) {
                                String[] ancestors = location.getAncestors().split(",");
                                if (ArrayUtils.contains(output.getLocationDepartureIds(), rsFreight.getPolId())) {
                                    a += 100;
                                }
                                if (ArrayUtils.contains(local.getLocationDepartureIds(), rsFreight.getPolId())) {
                                    b += 100;
                                }
                                int A = 0;
                                int B = 0;
                                for (int i = 0; i < ancestors.length; i++) {
                                    for (Long l : output.getLocationDepartureIds()) {
                                        if (Objects.equals(ancestors[i], l.toString())) {
                                            if (A < i) {
                                                A = i;
                                            }
                                        }
                                    }
                                }
                                for (int i = 0; i < ancestors.length; i++) {
                                    for (Long l : local.getLocationDepartureIds()) {
                                        if (Objects.equals(ancestors[i], l.toString())) {
                                            if (B < i) {
                                                B = i;
                                            }
                                        }
                                    }
                                }
                                if (A > B) {
                                    a += 20;
                                }
                                if (A < B) {
                                    b += 20;
                                }
                            }
                            if (a > b) {
                                scoreA += 50;
                            }
                            if (a < b) {
                                scoreB += 50;
                            }
                        }
                        if (output.getLocationDepartureIds().length > 0 && local.getLocationDepartureIds().length == 0 && output.getLineDepartureIds().length == 0 && local.getLineDepartureIds().length > 0) {
                            if (rsFreight.getPolId() != null && rsFreight.getPolId().equals(location.getLocationId()) && ArrayUtils.contains(output.getLocationDepartureIds(), rsFreight.getPolId())) {
                                if (location.getLineId() != null) {
                                    for (BasDistLine line : lines) {
                                        if (line.getLineId().equals(location.getLineId())) {
                                            String[] ancestors = line.getAncestors().split(",");
                                            if (!ArrayUtils.contains(local.getLineDepartureIds(), line.getLineId()) && SearchUtils.existSame(ancestors, local.getLineDepartureIds())) {
                                                scoreA += 50;
                                            }
                                            if (ArrayUtils.contains(local.getLineDepartureIds(), line.getLineId())) {
                                                scoreA += 50;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        if (output.getLocationDepartureIds().length == 0 && local.getLocationDepartureIds().length > 0 && output.getLineDepartureIds().length > 0 && local.getLineDepartureIds().length == 0) {
                            if (rsFreight.getPolId() != null && rsFreight.getPolId().equals(location.getLocationId()) && ArrayUtils.contains(local.getLocationDepartureIds(), rsFreight.getPolId())) {
                                if (location.getLineId() != null) {
                                    for (BasDistLine line : lines) {
                                        if (line.getLineId().equals(location.getLineId())) {
                                            String[] ancestors = line.getAncestors().split(",");
                                            if (!ArrayUtils.contains(output.getLineDepartureIds(), line.getLineId()) && SearchUtils.existSame(ancestors, output.getLineDepartureIds())) {
                                                scoreB += 50;
                                            }
                                            if (ArrayUtils.contains(output.getLineDepartureIds(), line.getLineId())) {
                                                scoreB += 50;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        if (output.getLocationDestinationIds().length > 0 && local.getLocationDestinationIds().length > 0) {
                            int a = 0;
                            int b = 0;
                            if (rsFreight.getDestinationPortId() != null && rsFreight.getDestinationPortId().equals(location.getLocationId())) {
                                String[] ancestors = location.getAncestors().split(",");
                                int A = 0;
                                int B = 0;
                                if (ArrayUtils.contains(output.getLocationDestinationIds(), rsFreight.getDestinationPortId())) {
                                    a += 100;
                                }
                                if (ArrayUtils.contains(local.getLocationDestinationIds(), rsFreight.getDestinationPortId())) {
                                    b += 100;
                                }
                                // i代表第几层（如1广东-2肇庆）越大区域就越仔细
                                for (int i = 0; i < ancestors.length; i++) {
                                    for (Long l : output.getLocationDestinationIds()) {
                                        if (Objects.equals(ancestors[i], l.toString())) {
                                            if (A < i) {
                                                A = i;
                                            }
                                        }
                                    }
                                }
                                for (int i = 0; i < ancestors.length; i++) {
                                    for (Long l : local.getLocationDestinationIds()) {
                                        if (Objects.equals(ancestors[i], l.toString())) {
                                            if (B < i) {
                                                B = i;
                                            }
                                        }
                                    }
                                }
                                if (A > B) {
                                    a += 20;
                                }
                                if (A < B) {
                                    b += 20;
                                }
                            }
                            if (a > b) {
                                scoreA += 50;
                            }
                            if (a < b) {
                                scoreB += 50;
                            }
                        }
                        if (output.getLocationDestinationIds().length > 0 && local.getLocationDestinationIds().length == 0 && output.getLineDestinationIds().length == 0 && local.getLineDestinationIds().length > 0) {
                            if (rsFreight.getDestinationPortId() != null && rsFreight.getDestinationPortId().equals(location.getLocationId()) && ArrayUtils.contains(output.getLocationDestinationIds(), rsFreight.getDestinationPortId())) {
                                if (location.getLineId() != null) {
                                    for (BasDistLine line : lines) {
                                        if (line.getLineId().equals(location.getLineId())) {
                                            String[] ancestors = line.getAncestors().split(",");
                                            if (!ArrayUtils.contains(local.getLineDestinationIds(), line.getLineId()) && SearchUtils.existSame(ancestors, local.getLineDestinationIds())) {
                                                scoreA += 50;
                                            }
                                            if (ArrayUtils.contains(local.getLineDestinationIds(), line.getLineId())) {
                                                scoreA += 50;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        if (output.getLocationDestinationIds().length == 0 && local.getLocationDestinationIds().length > 0 && output.getLineDestinationIds().length > 0 && local.getLineDestinationIds().length == 0) {
                            if (rsFreight.getDestinationPortId() != null && rsFreight.getDestinationPortId().equals(location.getLocationId()) && ArrayUtils.contains(local.getLocationDestinationIds(), rsFreight.getDestinationPortId())) {
                                if (location.getLineId() != null) {
                                    for (BasDistLine line : lines) {
                                        if (line.getLineId().equals(location.getLineId())) {
                                            String[] ancestors = line.getAncestors().split(",");
                                            if (!ArrayUtils.contains(output.getLineDestinationIds(), line.getLineId()) && SearchUtils.existSame(ancestors, output.getLineDestinationIds())) {
                                                scoreB += 50;
                                            }
                                            if (ArrayUtils.contains(output.getLineDestinationIds(), line.getLineId())) {
                                                scoreB += 50;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (output.getLocationDepartureIds().length > 0 && local.getLocationDepartureIds().length == 0) {
                        scoreA += 100;
                    }
                    if (output.getLocationDepartureIds().length == 0 && local.getLocationDepartureIds().length > 0) {
                        scoreB += 100;
                    }
                    if (output.getLocationDestinationIds().length > 0 && local.getLocationDestinationIds().length == 0) {
                        scoreA += 100;
                    }
                    if (output.getLocationDestinationIds().length == 0 && local.getLocationDestinationIds().length > 0) {
                        scoreB += 100;
                    }
                    // 航线
                    for (BasDistLine line : lines) {
                        // 两个都有多个启运行航线时
                        if (output.getLineDepartureIds().length > 0 && local.getLineDepartureIds().length > 0) {
                            int a = 0;
                            int b = 0;
                            if (ArrayUtils.contains(output.getLineDepartureIds(), line.getLineId())) {
                                a = line.getAncestors().split(",").length;
                                if (departureLineId != null && departureLineId.equals(line.getLineId())) {
                                    a += 5;
                                }
                            }
                            if (ArrayUtils.contains(local.getLineDepartureIds(), line.getLineId())) {
                                b = line.getAncestors().split(",").length;
                                if (departureLineId != null && departureLineId.equals(line.getLineId())) {
                                    b += 5;
                                }
                            }
                            if (a > b) {
                                scoreA += 50;
                            }
                            if (a < b) {
                                scoreB += 50;
                            }
                        }
                        // 两个都有目的航线时
                        if (output.getLineDestinationIds().length > 0 && local.getLineDestinationIds().length > 0) {
                            int a = 0;
                            int b = 0;
                            if (ArrayUtils.contains(output.getLineDestinationIds(), line.getLineId())) {
                                a = line.getAncestors().split(",").length;
                                if (destinationLineId != null && destinationLineId.equals(line.getLineId())) {
                                    a += 5;
                                }
                            }
                            if (ArrayUtils.contains(local.getLineDestinationIds(), line.getLineId())) {
                                b = line.getAncestors().split(",").length;
                                if (destinationLineId != null && destinationLineId.equals(line.getLineId())) {
                                    b += 5;
                                }
                            }
                            if (a > b) {
                                scoreA += 50;
                            }
                            if (a < b) {
                                scoreB += 50;
                            }
                        }
                    }
                    if (output.getLineDepartureIds().length > 0 && local.getLineDepartureIds().length == 0) {
                        scoreA += 100;
                    }
                    if (output.getLineDepartureIds().length == 0 && local.getLineDepartureIds().length > 0) {
                        scoreB += 100;
                    }
                    if (output.getLineDestinationIds().length > 0 && local.getLineDestinationIds().length == 0) {
                        scoreA += 100;
                    }
                    if (output.getLineDestinationIds().length == 0 && local.getLineDestinationIds().length > 0) {
                        scoreB += 100;
                    }
                    if (rsFreight.getCargoTypeIds() != null && output.getCargoTypeIds() != null && SearchUtils.existSame(output.getCargoTypeIds(), rsFreight.getCargoTypeIds())) {
                        scoreA += 10;
                    }
                    if (rsFreight.getCargoTypeIds() != null && local.getCargoTypeIds() != null && SearchUtils.existSame(local.getCargoTypeIds(), rsFreight.getCargoTypeIds())) {
                        scoreB += 10;
                    }
                    if (rsFreight.getSupplierId() != null && output.getSupplierId() != null && output.getSupplierId().equals(rsFreight.getSupplierId())) {
                        ++scoreA;
                    }
                    if (rsFreight.getSupplierId() != null && local.getSupplierId() != null && local.getSupplierId().equals(rsFreight.getSupplierId())) {
                        ++scoreB;
                    }
                    if (output.getValidTo() != null && local.getValidTo() != null && output.getValidTo().compareTo(local.getValidTo()) < 0) {
                        scoreA += 100;
                    }
                    if (output.getValidTo() != null && local.getValidTo() != null && output.getValidTo().compareTo(local.getValidTo()) > 0) {
                        scoreB += 100;
                    }
                    if (rsLocals.size() > 1) {
                        if (scoreA > scoreB) {
                            outData.add(local);
                        } else {
                            outData.add(output);
                            output = local;
                        }
                    }
                }
            }
        }
        return list.stream().
                filter(RsLocalCharge -> !outData.contains(RsLocalCharge)).
                sorted(Comparator.comparing(RsLocalCharge::getChargeOrderNum, Comparator.nullsLast(Comparator.naturalOrder()))).
                collect(Collectors.toList());
    }

    @Override
    public List<RsCharacteristics> queryCharacteristics(RsCharacteristics rsCharacteristics) {
        List<List<Long>> cl = new ArrayList<>();
        List<BasDistLocation> locations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        if (locations == null) {
            RedisCache.location();
            locations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        }
        List<BasDistLine> lines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        if (lines == null) {
            RedisCache.line();
            lines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        }
        List<RsCharacteristics> characteristics = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "characteristics");
        if (characteristics == null) {
            RedisCache.characteristics();
            characteristics = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "characteristics");
        }
        List<BasDistCargoType> cargoTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        if (cargoTypes == null) {
            RedisCache.cargoType();
            cargoTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        }
        List<BasDistServiceType> serviceTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
        if (serviceTypes == null) {
            RedisCache.serviceType();
            serviceTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
        }
        List<MidCarrier> midCarriers = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "characteristicsCarriers");
        if (midCarriers == null) {
            RedisCache.midCarrier("characteristics", "characteristicsCarriers");
            midCarriers = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "characteristicsCarriers");
        }
        Set<Long> cServiceType = new HashSet<>();
        Set<Long> serviceTypeIds = new HashSet<>();
        // 服务类型
        if (rsCharacteristics.getServiceTypeIds() != null && rsCharacteristics.getServiceTypeIds().length > 0) {
            for (BasDistServiceType serviceType : serviceTypes) {
                if (ArrayUtils.contains(rsCharacteristics.getServiceTypeIds(), serviceType.getServiceTypeId())) {
                    serviceTypeIds.add(serviceType.getServiceTypeId());
                    /*for (String s : serviceType.getAncestors().split(",")) {
                        serviceTypeIds.add(Long.parseLong(s));
                    }*/
                }
                /*if (SearchUtils.existSame(serviceType.getAncestors().split(","), rsCharacteristics.getServiceTypeIds())) {
                    serviceTypeIds.add(serviceType.getServiceTypeId());
                }*/

            }
        }
        Set<Long> cDeparture = new HashSet<>();
        Set<Long> kidLocationsDeparture = new HashSet<>();
        Set<Long> kidLinesDeparture = new HashSet<>();
        Set<Long> cDestination = new HashSet<>();
        Set<Long> kidLocationsDestination = new HashSet<>();
        Set<Long> kidLinesDestination = new HashSet<>();
        // 启运港和目的港
        if (rsCharacteristics.getLocationDepartureIds() != null || rsCharacteristics.getLocationDestinationIds() != null) {
            for (BasDistLocation location : locations) {
                if (rsCharacteristics.getLocationDepartureIds() != null && rsCharacteristics.getLocationDepartureIds().length > 0) {
                    if (ArrayUtils.contains(rsCharacteristics.getLocationDepartureIds(), location.getLocationId())) {
                        kidLocationsDeparture.add(location.getLocationId());
                        for (String a : location.getAncestors().split(",")) {
                            kidLocationsDeparture.add(Long.parseLong(a));
                        }
                        if (location.getLineId() != null) {
                            kidLinesDeparture.add(location.getLineId());
                            for (BasDistLine line : lines) {
                                if (line.getLineId().equals(location.getLineId())) {
                                    for (String a : line.getAncestors().split(",")) {
                                        kidLinesDeparture.add(Long.parseLong(a));
                                    }
                                }
                                if (ArrayUtils.contains(line.getAncestors().split(","), location.getLineId().toString())) {
                                    kidLinesDeparture.add(line.getLineId());
                                }
                            }
                        }
                    }
                    if (SearchUtils.existSame(location.getAncestors().split(","), rsCharacteristics.getLocationDepartureIds())) {
                        kidLocationsDeparture.add(location.getLocationId());
                    }
                }
                if (rsCharacteristics.getLocationDestinationIds() != null && rsCharacteristics.getLocationDestinationIds().length > 0) {
                    if (ArrayUtils.contains(rsCharacteristics.getLocationDestinationIds(), location.getLocationId())) {
                        kidLocationsDestination.add(location.getLocationId());
                        for (String a : location.getAncestors().split(",")) {
                            kidLocationsDestination.add(Long.parseLong(a));
                        }
                        if (location.getLineId() != null) {
                            kidLinesDestination.add(location.getLineId());
                            for (BasDistLine line : lines) {
                                if (line.getLineId().equals(location.getLineId())) {
                                    for (String a : line.getAncestors().split(",")) {
                                        kidLinesDestination.add(Long.parseLong(a));
                                    }
                                }
                                if (ArrayUtils.contains(line.getAncestors().split(","), location.getLineId().toString())) {
                                    kidLinesDestination.add(line.getLineId());
                                }
                            }
                        }
                    }
                    if (SearchUtils.existSame(location.getAncestors().split(","), rsCharacteristics.getLocationDestinationIds())) {
                        kidLocationsDestination.add(location.getLocationId());
                    }
                }
            }
        }
        Set<Long> cCarrier = new HashSet<>();
        Set<Long> cCargoType = new HashSet<>();
        Set<Long> kidsCargoType = new HashSet<>();
        // 货物类型
        if (rsCharacteristics.getCargoTypeIds() != null && rsCharacteristics.getCargoTypeIds().length > 0) {
            for (BasDistCargoType cargoType : cargoTypes) {
                String[] ancestors = cargoType.getAncestors().split(",");
                if (ArrayUtils.contains(rsCharacteristics.getCargoTypeIds(), cargoType.getCargoTypeId())) {
                    kidsCargoType.add(cargoType.getCargoTypeId());
                    for (String cg : ancestors) {
                        kidsCargoType.add(Long.parseLong(cg));
                    }
                }
                if (SearchUtils.existSame(ancestors, rsCharacteristics.getCargoTypeIds())) {
                    kidsCargoType.add(cargoType.getCargoTypeId());
                }
            }
        }
        for (RsCharacteristics ch : characteristics) {
            List<Long> locationDeparture = rsCharacteristicsService.selectLocationDeparture(ch.getCharacteristicsId());
            if (SearchUtils.existSame(locationDeparture.toArray(), kidLocationsDeparture.toArray())) {
                cDeparture.add(ch.getCharacteristicsId());
            }
            List<Long> lineDeparture = rsCharacteristicsService.selectLineDeparture(ch.getCharacteristicsId());
            if (SearchUtils.existSame(lineDeparture.toArray(), kidLinesDeparture.toArray())) {
                cDeparture.add(ch.getCharacteristicsId());
            }
            List<Long> locationDestination = rsCharacteristicsService.selectLocationDestination(ch.getCharacteristicsId());
            if (SearchUtils.existSame(locationDestination.toArray(), kidLocationsDestination.toArray())) {
                cDestination.add(ch.getCharacteristicsId());
            }
            List<Long> lineDestination = rsCharacteristicsService.selectLineDestination(ch.getCharacteristicsId());
            if (SearchUtils.existSame(lineDestination.toArray(), kidLinesDestination.toArray())) {
                cDestination.add(ch.getCharacteristicsId());
            }
            List<Long> cargoTypeList = rsCharacteristicsService.selectCargoTypes(ch.getCharacteristicsId());
            if (SearchUtils.existSame(cargoTypeList.toArray(), kidsCargoType.toArray())) {
                cCargoType.add(ch.getCharacteristicsId());
            }
            if (ch.getServiceTypeId() != null && serviceTypeIds.contains(ch.getServiceTypeId())) {
                cServiceType.add(ch.getCharacteristicsId());
            }
            if (midCarriers.size() == 0 || rsCharacteristics.getCarrierIds() != null && SearchUtils.existSame(midCarriers.toArray(), rsCharacteristics.getCarrierIds())) {
                cCarrier.add(ch.getCharacteristicsId());
            }
        }
        if (rsCharacteristics.getServiceTypeIds() != null && rsCharacteristics.getServiceTypeIds().length > 0) {
            cl.add(new ArrayList<>(cServiceType));
        }
        if (rsCharacteristics.getLocationDepartureIds() != null && rsCharacteristics.getLocationDepartureIds().length > 0) {
            cl.add(new ArrayList<>(cDeparture));
        }
        if (rsCharacteristics.getLocationDestinationIds() != null && rsCharacteristics.getLocationDestinationIds().length > 0) {
            cl.add(new ArrayList<>(cDestination));
        }
        if (rsCharacteristics.getCargoTypeIds() != null && rsCharacteristics.getCargoTypeIds().length > 0) {
            cl.add(new ArrayList<>(cCargoType));
        }
        if (rsCharacteristics.getCarrierIds() != null && rsCharacteristics.getCarrierIds().length > 0) {
            cl.add(new ArrayList<>(cCarrier));
        }
        List<RsCharacteristics> cList = new ArrayList<>();
        List<Long> cll = SearchUtils.getLongs(cl);
        for (RsCharacteristics ch : characteristics) {
            if (Objects.equals(ch.getIsValid(), "Y") && cll.contains(ch.getCharacteristicsId())) {
                cList.add(ch);
            }
        }
        return cList;
    }

    /**
     * 查询报价列表
     *
     * @param quotationId 报价列表主键
     * @return 报价列表
     */
    @Override
    public RsQuotation selectRsQuotationByQuotationId(Long quotationId) {
        return rsQuotationMapper.selectRsQuotationByQuotationId(quotationId);
    }

    /**
     * 查询报价列表列表
     *
     * @param rsQuotation 报价列表
     * @return 报价列表
     */
    @Override
//    @DataScope(deptAlias = "d", userAlias = "u", subUserAlias = "u", subDeptAlias = "d")
    public List<RsQuotation> selectRsQuotationList(RsQuotation rsQuotation) {
        List<List<Long>> ql = new ArrayList<>();
        rsQuotation.setUserId(SecurityUtils.getUserId());
        List<RsQuotation> quotations = rsQuotationMapper.selectRsQuotationList(rsQuotation);

        // 获取收入吨相关数据
        List<MidRevenueTons> midRevenueTons = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "quotationRevenueTons");
        if (midRevenueTons == null) {
            midRevenueTons = midRevenueTonsMapper.selectMidRevenueTonsList(new MidRevenueTons());
            redisCache.deleteObject(CacheConstants.MID_CACHE_KEY + "quotationRevenueTons");
            redisCache.setCacheObject(CacheConstants.MID_CACHE_KEY + "quotationRevenueTons", midRevenueTons);
        }

        // 获取服务类型数据
        List<BasDistServiceType> serviceTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
        if (serviceTypes == null) {
            RedisCache.serviceType();
            serviceTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
        }

        // 获取报价服务类型关联数据
        List<MidServiceType> midServiceTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "quotationServiceType");
        if (midServiceTypes == null) {
            RedisCache.midServiceType("quotation", "quotationServiceType");
            midServiceTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "quotationServiceType");
        }

        // 获取货物类型数据
        List<BasDistCargoType> cargoTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        if (cargoTypes == null) {
            RedisCache.cargoType();
            cargoTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        }

        // 获取报价货物类型关联数据
        List<MidCargoType> midCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "quotationCargoType");
        if (midCargoTypes == null) {
            RedisCache.midCargoType("quotation", "quotationCargoType");
            midCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "quotationCargoType");
        }

        // 获取承运人数据
        List<MidCarrier> midCarriers = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "quotationCarriers");
        if (midCarriers == null) {
            RedisCache.midCarrier("quotation", "quotationCarriers");
            midCarriers = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "quotationCarriers");
        }

        // 获取地点数据
        List<BasDistLocation> locations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        if (locations == null) {
            RedisCache.location();
            locations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        }

        // 处理起始地和目的地过滤
        if (rsQuotation.getDepartureId() != null || rsQuotation.getDestinationId() != null) {
            Set<Long> departureIds = new HashSet<>();
            Set<Long> destinationIds = new HashSet<>();

            // 构建地点ID集合
            for (BasDistLocation location : locations) {
                if (rsQuotation.getDepartureId() != null) {
                    if (rsQuotation.getDepartureId().equals(location.getLocationId())) {
                        departureIds.add(location.getLocationId());
                        if (StringUtils.isNotEmpty(location.getAncestors())) {
                            Arrays.stream(location.getAncestors().split(","))
                                    .filter(StringUtils::isNotEmpty)
                                    .map(Long::parseLong)
                                    .forEach(departureIds::add);
                        }
                    }
                    if (location.getAncestors() != null && ArrayUtils.contains(location.getAncestors().split(","), rsQuotation.getDepartureId().toString())) {
                        departureIds.add(location.getLocationId());
                    }
                }

                if (rsQuotation.getDestinationId() != null) {
                    if (rsQuotation.getDestinationId().equals(location.getLocationId())) {
                        destinationIds.add(location.getLocationId());
                        if (StringUtils.isNotEmpty(location.getAncestors())) {
                            Arrays.stream(location.getAncestors().split(","))
                                    .filter(StringUtils::isNotEmpty)
                                    .map(Long::parseLong)
                                    .forEach(destinationIds::add);
                        }
                    }
                    if (location.getAncestors() != null && ArrayUtils.contains(location.getAncestors().split(","), rsQuotation.getDestinationId().toString())) {
                        destinationIds.add(location.getLocationId());
                    }
                }
            }

            // 过滤报价
            if (rsQuotation.getDepartureId() != null) {
                Set<Long> fDeparture = quotations.stream()
                        .filter(q -> departureIds.contains(q.getDepartureId()))
                        .map(RsQuotation::getQuotationId)
                        .collect(Collectors.toSet());
                ql.add(new ArrayList<>(fDeparture));
            }

            if (rsQuotation.getDestinationId() != null) {
                Set<Long> fDestination = quotations.stream()
                        .filter(q -> destinationIds.contains(q.getDestinationId()))
                        .map(RsQuotation::getQuotationId)
                        .collect(Collectors.toSet());
                ql.add(new ArrayList<>(fDestination));
            }
        }

        // 处理服务类型过滤
        if (rsQuotation.getServiceTypeId() != null) {
            Set<Long> serviceTypeIds = serviceTypes.stream()
                    .filter(st -> rsQuotation.getServiceTypeId().equals(st.getServiceTypeId()) ||
                            (st.getAncestors() != null && ArrayUtils.contains(st.getAncestors().split(","), rsQuotation.getServiceTypeId().toString())))
                    .map(BasDistServiceType::getServiceTypeId)
                    .collect(Collectors.toSet());

            Set<Long> quotationIds = midServiceTypes.stream()
                    .filter(mst -> serviceTypeIds.contains(mst.getServiceTypeId()))
                    .map(MidServiceType::getBelongId)
                    .collect(Collectors.toSet());

            ql.add(new ArrayList<>(quotationIds));
        }

        // 处理货物类型过滤
        if (rsQuotation.getCargoTypeIds() != null && rsQuotation.getCargoTypeIds().length > 0) {
            Set<Long> cargoTypeIds = new HashSet<>(Arrays.asList(rsQuotation.getCargoTypeIds()));
            cargoTypes.stream()
                    .filter(ct -> SearchUtils.existSame(rsQuotation.getCargoTypeIds(), ct.getAncestors().split(",")))
                    .map(BasDistCargoType::getCargoTypeId)
                    .forEach(cargoTypeIds::add);

            Set<Long> quotationIds = midCargoTypes.stream()
                    .filter(mct -> cargoTypeIds.contains(mct.getCargoTypeId()))
                    .map(MidCargoType::getBelongId)
                    .collect(Collectors.toSet());

            ql.add(new ArrayList<>(quotationIds));
        }

        // 处理单位ID过滤
        if (rsQuotation.getUnitId() != null) {
            Set<Long> quotationIds = midRevenueTons.stream()
                    .filter(mrt -> mrt.getUnitId().equals(rsQuotation.getUnitId()))
                    .map(MidRevenueTons::getQuotationId)
                    .collect(Collectors.toSet());
            ql.add(new ArrayList<>(quotationIds));
        }

        // 处理单位代码过滤
        if (rsQuotation.getUnitCode() != null) {
            Set<Long> quotationIds = midRevenueTons.stream()
                    .filter(mrt -> rsQuotation.getUnitCode().equals(mrt.getUnit()))
                    .map(MidRevenueTons::getQuotationId)
                    .collect(Collectors.toSet());
            ql.add(new ArrayList<>(quotationIds));
        }

        // 处理承运人过滤
        if (rsQuotation.getCarrierIds() != null && rsQuotation.getCarrierIds().length > 0) {
            Set<Long> quotationIds = midCarriers.stream()
                    .filter(mc -> ArrayUtils.contains(rsQuotation.getCarrierIds(), mc.getCarrierId()))
                    .map(MidCarrier::getBelongId)
                    .collect(Collectors.toSet());
            ql.add(new ArrayList<>(quotationIds));
        }

        // 获取最终结果
        List<Long> finalIds = SearchUtils.getLongs(ql);
        if (!finalIds.isEmpty()) {
            return quotations.stream()
                    .filter(q -> finalIds.contains(q.getQuotationId()))
                    .collect(Collectors.toList());
        }

        return ql.isEmpty() ? quotations : Collections.emptyList();
    }

    /**
     * 新增报价列表
     *
     * @param rsQuotation 报价列表
     * @return 结果
     */
    @Transactional
    @Override
    public int insertRsQuotation(RsQuotation rsQuotation) {
        rsQuotation.setCreateTime(DateUtils.getNowDate());
        rsQuotation.setCreateBy(SecurityUtils.getUserId());
        rsQuotation.setRichNo("RPQ" + DateUtils.dateTimeNow() + String.format("%05d", new SecureRandom().nextInt(100000)));
        int rows = rsQuotationMapper.insertRsQuotation(rsQuotation);
        insertMidRevenueTons(rsQuotation);
        insertServiceType(rsQuotation);
        insertCargoType(rsQuotation);
        if (rsQuotation.getLoadingIds() != null) {
            insertLoading(rsQuotation);
        }
//        insertDeparture(rsQuotation);
        insertCarriers(rsQuotation);
        return rows;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateQuotationFreight(RsQuotation rsQuotation) {
        int out = 0;
        // 先删除该报价的所有费用信息和报价特征（注意事项）
        out += rsQuotationFreightMapper.deleteRsQuotationFreightByQuotationId(rsQuotation.getQuotationId());
        out += midQuotationCharacteristicsMapper.deleteMidQuotationCharacteristicsByQuotationId(rsQuotation.getQuotationId());
        // 重新插入该报价的费用和报价特征
        // 费用
        if (!rsQuotation.getRsQuotationFreightList().isEmpty()) {
            out += rsQuotationFreightMapper.BatchRsQuotationFreight(rsQuotation.getRsQuotationFreightList());
            redisCache.deleteObject(CacheConstants.MID_CACHE_KEY + "quotationFreight");
        }
        // 插入注意事项
        if (rsQuotation.getCharacteristicsIds() != null && !rsQuotation.getCharacteristicsIds().isEmpty()) {
            List<MidQuotationCharacteristics> quotationCharacteristics = new ArrayList<>();
            for (Long c : rsQuotation.getCharacteristicsIds()) {
                MidQuotationCharacteristics midQuotationCharacteristics = new MidQuotationCharacteristics();
                midQuotationCharacteristics.setCharacteristicsId(c);
                midQuotationCharacteristics.setQuotationId(rsQuotation.getQuotationId());
                quotationCharacteristics.add(midQuotationCharacteristics);
            }
            out += midQuotationCharacteristicsMapper.BatchQuotationCharacteristics(quotationCharacteristics);
            redisCache.deleteObject(CacheConstants.MID_CACHE_KEY + "quotationCharacteristics");
        }
        return out;
    }

    /**
     * 修改报价列表
     *
     * @param rsQuotation 报价列表
     * @return 结果
     */
    @Override
    @Transactional
    public int updateRsQuotation(RsQuotation rsQuotation) {
        try {
            rsQuotation.setUpdateTime(DateUtils.getNowDate());
            rsQuotation.setUpdateBy(SecurityUtils.getUserId());
            // 先删除中间表 的全部相关记录在重新插入
            rsQuotationMapper.deleteMidRevenueTonsByQuotationId(rsQuotation.getQuotationId());
            insertMidRevenueTons(rsQuotation);
            midServiceTypeMapper.deleteMidServiceTypeById(rsQuotation.getQuotationId(), "quotation");
            insertServiceType(rsQuotation);
            midCargoTypeMapper.deleteMidCargoTypeById(rsQuotation.getQuotationId(), "quotation");
            insertCargoType(rsQuotation);
            midLocationLoadingMapper.deleteMidLocationLoadingByBelongId(rsQuotation.getQuotationId(), "quotation");
            insertLoading(rsQuotation);
//        midLocationDepartureMapper.deleteMidLocationDepartureById(rsQuotation.getQuotationId(), "quotation");
//        insertDeparture(rsQuotation);
            midCarrierMapper.deleteMidCarrierById(rsQuotation.getQuotationId(), "quotation");
            insertCarriers(rsQuotation);

            return rsQuotationMapper.updateRsQuotation(rsQuotation);
        } catch (Exception e) {
            // 这里你可以选择记录日志或者抛出自定义的异常
            throw e; // 让异常继续抛出，以触发事务回滚
        }
    }

    @Override
    public int updateRsQuotationRemark(RsQuotation rsQuotation) {
        return rsQuotationMapper.updateRsQuotation(rsQuotation);
    }

    /**
     * 修改报价列表状态
     *
     * @param rsQuotation 报价列表
     * @return 报价列表
     */
    @Override
    public int changeStatus(RsQuotation rsQuotation) {
        return rsQuotationMapper.updateRsQuotation(rsQuotation);
    }

    @Override
    public List<Long> selectLocationLoading(Long quotationId) {
        List<MidLocationLoading> locationLoading = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "quotationLocationLoading");
        if (locationLoading == null) {
            RedisCache.locationLoading("quotation", "quotationLocationLoading");
            locationLoading = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "quotationLocationLoading");
        }
        List<Long> r = new ArrayList<>();
        for (MidLocationLoading midLocationLoading : locationLoading) {
            if (midLocationLoading.getBelongId().equals(quotationId)) {
                r.add(midLocationLoading.getLocationId());
            }
        }
        return r;
    }

    @Override
    public List<Long> selectServiceTypes(Long quotationId) {
        List<MidServiceType> midServiceTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "quotationServiceType");
        if (midServiceTypes == null) {
            RedisCache.midServiceType("quotation", "quotationServiceType");
            midServiceTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "quotationServiceType");
        }
        List<Long> r = new ArrayList<>();
        for (MidServiceType midServiceType : midServiceTypes) {
            if (midServiceType.getBelongId().equals(quotationId)) {
                r.add(midServiceType.getServiceTypeId());
            }
        }
        return r;
    }

    @Override
    public List<Long> selectCarriers(Long quotationId) {
        List<MidCarrier> midCarriers = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "quotationCarriers");
        if (midCarriers == null) {
            RedisCache.midCarrier("quotation", "quotationCarriers");
            midCarriers = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "quotationCarriers");
        }
        List<Long> r = new ArrayList<>();
        for (MidCarrier midCarrier : midCarriers) {
            if (midCarrier.getBelongId().equals(quotationId)) {
                r.add(midCarrier.getCarrierId());
            }
        }
        return r;
    }

    @Override
    public List<Long> selectCargoTypes(Long quotationId) {
        List<MidCargoType> midCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "quotationCargoType");
        if (midCargoTypes == null) {
            RedisCache.midCargoType("quotation", "quotationCargoType");
            midCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "quotationCargoType");
        }
        List<Long> r = new ArrayList<>();
        for (MidCargoType midCargoType : midCargoTypes) {
            if (midCargoType.getBelongId().equals(quotationId)) {
                r.add(midCargoType.getCargoTypeId());
            }
        }
        return r;
    }


    @Override
    public List<MidRevenueTons> selectMidRevenueTonsList(Long quotationId) {
        List<MidRevenueTons> midRevenueTons = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "quotationRevenueTons");
        if (midRevenueTons == null) {
            midRevenueTons = midRevenueTonsMapper.selectMidRevenueTonsList(new MidRevenueTons());
            redisCache.setCacheObject(CacheConstants.MID_CACHE_KEY + "quotationRevenueTons", midRevenueTons);
        }
        List<MidRevenueTons> output = new ArrayList<>();
        for (MidRevenueTons midRevenueTon : midRevenueTons) {
            if (midRevenueTon.getQuotationId().equals(quotationId)) {
                output.add(midRevenueTon);
            }
        }
        return output;
    }

    /**
     * 根据报价id查询匹配的费用
     *
     * @param quotationId
     * @return
     */
    @Override
    public List<RsQuotationFreight> selectQuotationFreightList(Long quotationId) {
        List<RsQuotationFreight> rsQuotationFreights = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "quotationFreight");
        if (rsQuotationFreights == null) {
            rsQuotationFreights = rsQuotationFreightMapper.selectRsQuotationFreightList(new RsQuotationFreight());
            redisCache.setCacheObject(CacheConstants.MID_CACHE_KEY + "quotationFreight", rsQuotationFreights);
        }
        List<RsQuotationFreight> output = new ArrayList<>();
        for (RsQuotationFreight rsQuotationFreight : rsQuotationFreights) {
            if (rsQuotationFreight.getQuotationId().equals(quotationId)) {
                output.add(rsQuotationFreight);
            }
        }
        return output;
    }

    /**
     * 根据报价id查找匹配的注意事项返回id
     *
     * @param quotationId
     * @return
     */
    @Override
    public List<Long> selectQuotationCharacteristicsList(Long quotationId) {
        List<MidQuotationCharacteristics> midQuotationCharacteristics = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "quotationCharacteristics");
        if (midQuotationCharacteristics == null) {
            midQuotationCharacteristics = midQuotationCharacteristicsMapper.selectMidQuotationCharacteristicsList(new MidQuotationCharacteristics());
            redisCache.setCacheObject(CacheConstants.MID_CACHE_KEY + "quotationCharacteristics", midQuotationCharacteristics);
        }
        List<Long> output = new ArrayList<>();
        for (MidQuotationCharacteristics midQuotationCharacteristic : midQuotationCharacteristics) {
            if (midQuotationCharacteristic.getQuotationId().equals(quotationId)) {
                output.add(midQuotationCharacteristic.getCharacteristicsId());
            }
        }
        return output;
    }

    @Override
    public String selectCargoTypeCodeSum(Long quotationId) {
        List<MidCargoType> midCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "quotationCargoType");
        if (midCargoTypes == null) {
            RedisCache.midCargoType("quotation", "quotationCargoType");
            midCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "quotationCargoType");
        }
        List<BasDistCargoType> cargoTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        if (cargoTypes == null) {
            RedisCache.cargoType();
            cargoTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        }
        List<String> r = new ArrayList<>();
        for (MidCargoType midCargoType : midCargoTypes) {
            if (midCargoType.getBelongId().equals(quotationId)) {
                List<BasDistCargoType> collect = cargoTypes.stream().filter(basDistCargoType -> midCargoType.getCargoTypeId().equals(basDistCargoType.getCargoTypeId())).collect(Collectors.toList());
                if (!collect.isEmpty()) {
                    String cargoTypeShortName = collect.get(0).getCargoTypeShortName();
                    r.add(cargoTypeShortName);
                }
            }
        }
        return r.toString().replace("[", "").replace("]", "");
    }

    /**
     * 根据报价id查询注意事项并返回匹配的记录
     *
     * @param quotationId
     * @return
     */
    @Override
    public List<RsCharacteristics> selectCharacteristicsList(Long quotationId) {
        List<MidQuotationCharacteristics> midQuotationCharacteristics = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "quotationCharacteristics");
        if (midQuotationCharacteristics == null) {
            midQuotationCharacteristics = midQuotationCharacteristicsMapper.selectMidQuotationCharacteristicsList(new MidQuotationCharacteristics());
            redisCache.setCacheObject(CacheConstants.MID_CACHE_KEY + "quotationCharacteristics", midQuotationCharacteristics);
        }
        List<Long> output = new ArrayList<>();
        for (MidQuotationCharacteristics midQuotationCharacteristic : midQuotationCharacteristics) {
            if (midQuotationCharacteristic.getQuotationId().equals(quotationId)) {
                output.add(midQuotationCharacteristic.getCharacteristicsId());
            }
        }
        if (!output.isEmpty()) {
            RsCharacteristics rsCharacteristics = new RsCharacteristics();
            rsCharacteristics.setCharacteristicsIds(output);
            return rsCharacteristicsService.selectRsCharacteristicsList(rsCharacteristics);
        } else {
            return null;
        }
    }

    /**
     * 批量删除报价列表
     *
     * @param quotationIds 需要删除的报价列表主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteRsQuotationByQuotationIds(Long[] quotationIds) {
        rsQuotationMapper.deleteMidRevenueTonsByQuotationIds(quotationIds);
        midServiceTypeMapper.deleteMidServiceTypeByIds(quotationIds, "quotation");
        midCargoTypeMapper.deleteMidCargoTypeByIds(quotationIds, "quotation");
        midLocationLoadingMapper.deleteMidLocationLoadingByBelongIds(quotationIds, "quotation");
        midLocationDepartureMapper.deleteMidLocationDepartureByIds(quotationIds, "quotation");
        midCarrierMapper.deleteMidCarrierByIds(quotationIds, "quotation");
        midQuotationCharacteristicsMapper.deleteMidQuotationCharacteristicsByQuotationIds(quotationIds);
        return rsQuotationMapper.deleteRsQuotationByQuotationIds(quotationIds);
    }

    /**
     * 删除报价列表信息
     *
     * @param quotationId 报价列表主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteRsQuotationByQuotationId(Long quotationId) {
        rsQuotationMapper.deleteMidRevenueTonsByQuotationId(quotationId);
        midQuotationCharacteristicsMapper.selectMidQuotationCharacteristicsByQuotationId(quotationId);
        midServiceTypeMapper.deleteMidServiceTypeById(quotationId, "quotation");
        midCargoTypeMapper.deleteMidCargoTypeById(quotationId, "quotation");
        midLocationLoadingMapper.deleteMidLocationLoadingByBelongId(quotationId, "quotation");
        midLocationDepartureMapper.deleteMidLocationDepartureById(quotationId, "quotation");
        midCarrierMapper.deleteMidCarrierById(quotationId, "quotation");
        return rsQuotationMapper.deleteRsQuotationByQuotationId(quotationId);
    }

    /**
     * 新增货量信息
     *
     * @param rsQuotation 报价列表对象
     */
    public void insertMidRevenueTons(RsQuotation rsQuotation) {
        List<MidRevenueTons> midRevenueTonsList = rsQuotation.getMidRevenueTonsList();
        Long quotationId = rsQuotation.getQuotationId();
        if (StringUtils.isNotNull(midRevenueTonsList)) {
            List<MidRevenueTons> list = new ArrayList<>();
            for (MidRevenueTons midrevenueTons : midRevenueTonsList) {
                midrevenueTons.setQuotationId(quotationId);
                list.add(midrevenueTons);
            }
            if (!list.isEmpty()) {
                rsQuotationMapper.batchMidRevenueTons(list);
            }
        }
        redisCache.deleteObject(CacheConstants.MID_CACHE_KEY + "quotationRevenueTons");
    }


    public void insertCarriers(RsQuotation rsQuotation) {
        Long[] roles = rsQuotation.getCarrierIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidCarrier> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidCarrier midCarrier = new MidCarrier();
                midCarrier.setBelongId(rsQuotation.getQuotationId());
                midCarrier.setCarrierId(r);
                midCarrier.setBelongTo("quotation");
                list.add(midCarrier);
            }
            midCarrierMapper.batchCarrier(list);
        }
        RedisCache.midCarrier("quotation", "quotationCarriers");
    }

    public void insertLoading(RsQuotation rsQuotation) {
        Long[] roles = rsQuotation.getLoadingIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidLocationLoading> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidLocationLoading MidLocationLoading = new MidLocationLoading();
                MidLocationLoading.setBelongId(rsQuotation.getQuotationId());
                MidLocationLoading.setLocationId(r);
                MidLocationLoading.setBelongTo("quotation");
                list.add(MidLocationLoading);
            }
            midLocationLoadingMapper.batchLD(list);
        }
        RedisCache.locationLoading("quotation", "quotationLocationLoading");

    }

    public void insertCargoType(RsQuotation rsQuotation) {
        Long[] roles = rsQuotation.getCargoTypeIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidCargoType> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidCargoType MidCargoType = new MidCargoType();
                MidCargoType.setBelongId(rsQuotation.getQuotationId());
                MidCargoType.setBelongTo("quotation");
                MidCargoType.setCargoTypeId(r);
                list.add(MidCargoType);
            }
            midCargoTypeMapper.batchCargoType(list);
        }
        RedisCache.midCargoType("quotation", "quotationCargoType");
    }

    public void insertServiceType(RsQuotation rsQuotation) {
        Long[] roles = rsQuotation.getServiceTypeIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidServiceType> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidServiceType MidServiceType = new MidServiceType();
                MidServiceType.setBelongId(rsQuotation.getQuotationId());
                MidServiceType.setServiceTypeId(r);
                MidServiceType.setBelongTo("quotation");
                list.add(MidServiceType);
            }
            midServiceTypeMapper.batchServiceType(list);
        }
        RedisCache.midServiceType("quotation", "quotationServiceType");
    }

    public boolean findUnit(String unit) {
        String[] CtnrType = new String[]{"20GP", "20OT", "20FR", "TANK", "40GP", "40HQ", "40NOR", "40OT", "40FR", "40RH", "45HQ"};
        boolean exit = false;
        for (String s : CtnrType) {
            if (s.equals(unit)) {
                exit = true;
                break;
            }
        }
        return exit;
    }
}
