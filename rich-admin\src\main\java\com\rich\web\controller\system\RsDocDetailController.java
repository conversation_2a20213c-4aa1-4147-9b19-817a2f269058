package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.RsDocDetail;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.RsDocDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 操作文件Controller
 *
 * <AUTHOR>
 * @date 2023-09-21
 */
@RestController
@RequestMapping("/system/docdetail")
public class RsDocDetailController extends BaseController {
    @Autowired
    private RsDocDetailService rsDocDetailService;

    /**
     * 查询操作文件列表
     */
    @PreAuthorize("@ss.hasPermi('system:docdetail:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsDocDetail rsDocDetail) {
        startPage();
        List<RsDocDetail> list = rsDocDetailService.selectRsDocDetailList(rsDocDetail);
        return getDataTable(list);
    }

    /**
     * 导出操作文件列表
     */
    @PreAuthorize("@ss.hasPermi('system:docdetail:export')")
    @Log(title = "操作文件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsDocDetail rsDocDetail) {
        List<RsDocDetail> list = rsDocDetailService.selectRsDocDetailList(rsDocDetail);
        ExcelUtil<RsDocDetail> util = new ExcelUtil<RsDocDetail>(RsDocDetail.class);
        util.exportExcel(response, list, "操作文件数据");
    }

    /**
     * 获取操作文件详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:docdetail:query')")
    @GetMapping(value = "/{docDetailId}")
    public AjaxResult getInfo(@PathVariable("docDetailId") Long docDetailId) {
        return AjaxResult.success(rsDocDetailService.selectRsDocDetailByDocDetailId(docDetailId));
    }

    /**
     * 新增操作文件
     */
    @PreAuthorize("@ss.hasPermi('system:docdetail:add')")
    @Log(title = "操作文件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsDocDetail rsDocDetail) {
        return toAjax(rsDocDetailService.insertRsDocDetail(rsDocDetail));
    }

    /**
     * 修改操作文件
     */
    @PreAuthorize("@ss.hasPermi('system:docdetail:edit')")
    @Log(title = "操作文件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsDocDetail rsDocDetail) {
        return toAjax(rsDocDetailService.updateRsDocDetail(rsDocDetail));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:docdetail:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsDocDetail rsDocDetail) {
        rsDocDetail.setUpdateBy(getUserId());
        return toAjax(rsDocDetailService.changeStatus(rsDocDetail));
    }

    /**
     * 删除操作文件
     */
    @PreAuthorize("@ss.hasPermi('system:docdetail:remove')")
    @Log(title = "操作文件", businessType = BusinessType.DELETE)
    @DeleteMapping("/{docDetailIds}")
    public AjaxResult remove(@PathVariable Long[] docDetailIds) {
        return toAjax(rsDocDetailService.deleteRsDocDetailByDocDetailIds(docDetailIds));
    }
}
