package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasOrganization;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 所属组织Mapper接口
 *
 * <AUTHOR>
 * @date 2022-09-30
 */
@Mapper
public interface BasOrganizationMapper {
    /**
     * 查询所属组织
     *
     * @param organizationId 所属组织主键
     * @return 所属组织
     */
    BasOrganization selectBasOrganizationByOrganizationId(Long organizationId);

    /**
     * 查询所属组织列表
     *
     * @param basOrganization 所属组织
     * @return 所属组织集合
     */
    List<BasOrganization> selectBasOrganizationList(BasOrganization basOrganization);

    /**
     * 新增所属组织
     *
     * @param basOrganization 所属组织
     * @return 结果
     */
    int insertBasOrganization(BasOrganization basOrganization);

    /**
     * 修改所属组织
     *
     * @param basOrganization 所属组织
     * @return 结果
     */
    int updateBasOrganization(BasOrganization basOrganization);

    /**
     * 删除所属组织
     *
     * @param organizationId 所属组织主键
     * @return 结果
     */
    int deleteBasOrganizationByOrganizationId(Long organizationId);

    /**
     * 批量删除所属组织
     *
     * @param organizationIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasOrganizationByOrganizationIds(Long[] organizationIds);
}
