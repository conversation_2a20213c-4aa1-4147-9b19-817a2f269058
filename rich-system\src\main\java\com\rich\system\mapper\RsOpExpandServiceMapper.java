package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 扩展服务服务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Mapper
public interface RsOpExpandServiceMapper {
    /**
     * 查询扩展服务服务
     *
     * @param expandServiceId 扩展服务服务主键
     * @return 扩展服务服务
     */
    RsOpExpandService selectRsOpExpandServiceByExpandServiceId(Long expandServiceId);

    /**
     * 查询扩展服务服务列表
     *
     * @param rsOpExpandService 扩展服务服务
     * @return 扩展服务服务集合
     */
    List<RsOpExpandService> selectRsOpExpandServiceList(RsOpExpandService rsOpExpandService);

    /**
     * 新增扩展服务服务
     *
     * @param rsOpExpandService 扩展服务服务
     * @return 结果
     */
    int insertRsOpExpandService(RsOpExpandService rsOpExpandService);

    int insertRsOpExpandService(RsOpOther rsOpOther);

    int insertRsOpExpandService(RsOp3rdCert rsOpExpandService);

    int insertRsOpExpandService(RsOpINS rsOpExpandService);

    int insertRsOpExpandService(RsOpTrading rsOpExpandService);

    int insertRsOpExpandService(RsOpFumigation rsOpExpandService);

    int insertRsOpExpandService(RsOpCO rsOpExpandService);

    /**
     * 修改扩展服务服务
     *
     * @param rsOpExpandService 扩展服务服务
     * @return 结果
     */
    int updateRsOpExpandService(RsOpExpandService rsOpExpandService);

    int updateRsOpExpandService(RsOpOther rsOpOther);

    int updateRsOpExpandService(RsOp3rdCert rsOpExpandService);

    int updateRsOpExpandService(RsOpINS rsOpExpandService);

    int updateRsOpExpandService(RsOpTrading rsOpExpandService);

    int updateRsOpExpandService(RsOpFumigation rsOpExpandService);

    int updateRsOpExpandService(RsOpCO rsOpExpandService);

    /**
     * 删除扩展服务服务
     *
     * @param expandServiceId 扩展服务服务主键
     * @return 结果
     */
    int deleteRsOpExpandServiceByExpandServiceId(Long expandServiceId);

    /**
     * 批量删除扩展服务服务
     *
     * @param expandServiceIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsOpExpandServiceByExpandServiceIds(Long[] expandServiceIds);

    RsOpExpandService selectRsOpExpandServiceByRctId(Long rctId);

    RsOp3rdCert selectRsOp3rdCertByRctId(@Param("rctId") Long rctId, @Param("sqdServiceTypeId") Long sqdServiceTypeId);

    RsOpINS selectRsOpINSByRctId(@Param("rctId") Long rctId, @Param("sqdServiceTypeId") Long sqdServiceTypeId);

    RsOpTrading selectRsOpTradingByRctId(@Param("rctId") Long rctId, @Param("sqdServiceTypeId") Long sqdServiceTypeId);

    RsOpFumigation selectRsOpFumigationByRctId(@Param("rctId") Long rctId, @Param("sqdServiceTypeId") Long sqdServiceTypeId);

    RsOpCO selectRsOpCOByRctId(@Param("rctId") Long rctId, @Param("sqdServiceTypeId") Long sqdServiceTypeId);

    RsOpOther selectRsOpOtherByRctId(@Param("rctId") Long rctId, @Param("sqdServiceTypeId") Long sqdServiceTypeId);
}
