package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsOpBulkShip;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsOpBulkShipService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 散杂船服务Controller
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@RestController
@RequestMapping("/system/opbulkship")
public class RsOpBulkShipController extends BaseController {
    @Autowired
    private RsOpBulkShipService rsOpBulkShipService;

    /**
     * 查询散杂船服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opbulkship:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsOpBulkShip rsOpBulkShip) {
        startPage();
        List<RsOpBulkShip> list = rsOpBulkShipService.selectRsOpBulkShipList(rsOpBulkShip);
        return getDataTable(list);
    }

    /**
     * 导出散杂船服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opbulkship:export')")
    @Log(title = "散杂船服务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsOpBulkShip rsOpBulkShip) {
        List<RsOpBulkShip> list = rsOpBulkShipService.selectRsOpBulkShipList(rsOpBulkShip);
        ExcelUtil<RsOpBulkShip> util = new ExcelUtil<RsOpBulkShip>(RsOpBulkShip.class);
        util.exportExcel(response, list, "散杂船服务数据");
    }

    /**
     * 获取散杂船服务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:opbulkship:query')")
    @GetMapping(value = "/{bulkShipId}")
    public AjaxResult getInfo(@PathVariable("bulkShipId") Long bulkShipId) {
        return AjaxResult.success(rsOpBulkShipService.selectRsOpBulkShipByBulkShipId(bulkShipId));
    }

    /**
     * 新增散杂船服务
     */
    @PreAuthorize("@ss.hasPermi('system:opbulkship:add')")
    @Log(title = "散杂船服务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsOpBulkShip rsOpBulkShip) {
        return toAjax(rsOpBulkShipService.insertRsOpBulkShip(rsOpBulkShip));
    }

    /**
     * 修改散杂船服务
     */
    @PreAuthorize("@ss.hasPermi('system:opbulkship:edit')")
    @Log(title = "散杂船服务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsOpBulkShip rsOpBulkShip) {
        return toAjax(rsOpBulkShipService.updateRsOpBulkShip(rsOpBulkShip));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:opbulkship:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsOpBulkShip rsOpBulkShip) {
        rsOpBulkShip.setUpdateBy(getUserId());
        return toAjax(rsOpBulkShipService.changeStatus(rsOpBulkShip));
    }

    /**
     * 删除散杂船服务
     */
    @PreAuthorize("@ss.hasPermi('system:opbulkship:remove')")
    @Log(title = "散杂船服务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{bulkShipIds}")
    public AjaxResult remove(@PathVariable Long[] bulkShipIds) {
        return toAjax(rsOpBulkShipService.deleteRsOpBulkShipByBulkShipIds(bulkShipIds));
    }
}
