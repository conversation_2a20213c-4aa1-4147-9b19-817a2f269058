package com.rich.system.mapper;

import com.rich.system.domain.MidRoleMenu;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @Entity
 */
@Mapper
public interface MidRoleMenuMapper {

    /**
     * 查询菜单使用数量
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    int checkMenuExistRole(Long menuId);

    int checkRoleExistMenu(Long roleId);

    /**
     * 通过角色ID删除角色和菜单关联
     *
     * @param roleId 角色ID
     * @return 结果
     */
    int deleteRoleMenuByRoleId(Long roleId);

    int deleteRoleMenuByDistributeId(Long distributeId);

    /**
     * 批量删除角色菜单关联信息
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteRoleMenu(Long[] ids);

    /**
     * 批量新增角色菜单信息
     *
     * @param list 角色菜单列表
     * @return 结果
     */
    int batchRoleMenu(List<MidRoleMenu> list);

    int updateRoleMenu(@Param("roleId") Long roleId, @Param("menuIds") Long[] menuIds);

    /**
     * 根据菜单id数组，插入中间表数据
     *
     * @param
     * @return
     */
    int batchRoleMenuByMenuIds(List<MidRoleMenu> list);

    void takeBackMenuByDeptId(@Param("deptId") Long deptId, @Param("takeBackMenuIds") Long[] takeBackMenuIds);

    List<Long> selectUnlessMaxRoleIds(Long deptId);

    void deleteRoleMenuByRoleIds(@Param("selectUnlessMaxRoleId") Long selectUnlessMaxRoleId, @Param("menuIds") Set<Long> menuIds);

    List<Long> unlessMaxAndRoleIds(Long deptId);
}
