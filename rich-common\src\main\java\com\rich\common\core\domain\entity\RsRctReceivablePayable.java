package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 操作单应收应付对象 rs_rct_receivable_payable
 *
 * <AUTHOR>
 * @date 2023-08-15
 */
public class RsRctReceivablePayable extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 应收应付
     */
    private Long receivablePayableId;

    /**
     * 物流类型
     */
    @Excel(name = "物流类型")
    private Long typeId;

    /**
     * 基础信息ID
     */
    @Excel(name = "基础信息ID")
    private Long basicInfoId;

    /**
     * 操作单
     */
    @Excel(name = "操作单")
    private Long rctId;

    /**
     * 客户
     */
    @Excel(name = "客户")
    private Long clientId;
    private String client;

    /**
     * 费用
     */
    @Excel(name = "费用")
    private Long quotationChargeId;
    private String quotationCharge;

    /**
     * 策略
     */
    @Excel(name = "策略")
    private Integer quotationStrategyId;

    /**
     * 货币
     */
    @Excel(name = "货币")
    private Long quotationCurrencyId;
    private String quotationCurrency;

    /**
     * 单价
     */
    @Excel(name = "单价")
    private BigDecimal quotationPrice;

    /**
     * 单位
     */
    @Excel(name = "单位")
    private Long quotationUnitId;
    private String quotationUnit;

    /**
     * 数量
     */
    @Excel(name = "数量")
    private Integer quotationAmount;

    /**
     * 汇率
     */
    @Excel(name = "汇率")
    private BigDecimal quotationExchangeRate;

    /**
     * 税率
     */
    @Excel(name = "税率")
    private BigDecimal quotationTaxRate;

    private BigDecimal quotationTotal;

    /**
     * 供应商
     */
    @Excel(name = "供应商")
    private Long supplierId;
    private String supplier;

    /**
     * 费用
     */
    @Excel(name = "费用")
    private Long costChargeId;
    private String costCharge;

    /**
     * 策略
     */
    @Excel(name = "策略")
    private Integer costStrategy;

    /**
     * 货币
     */
    @Excel(name = "货币")
    private Long costCurrencyId;
    private String costCurrency;

    /**
     * 单价
     */
    @Excel(name = "单价")
    private BigDecimal costPrice;

    /**
     * 单位
     */
    @Excel(name = "单位")
    private Long costUnitId;
    private String costUnit;

    /**
     * 数量
     */
    @Excel(name = "数量")
    private Integer costAmount;

    /**
     * 汇率
     */
    @Excel(name = "汇率")
    private BigDecimal costExchangeRate;

    /**
     * 税率
     */
    @Excel(name = "税率")
    private BigDecimal costTaxRate;

    private BigDecimal costTotal;

    /**
     * 单项利润
     */
    @Excel(name = "单项利润")
    private BigDecimal profit;

    private boolean showClient;
    private boolean showSupplier;
    private boolean showQuotationCharge;
    private boolean showCostCharge;
    private boolean showQuotationCurrency;
    private boolean showCostCurrency;
    private boolean showQuotationUnit;
    private boolean showCostUnit;

    public boolean isShowClient() {
        return showClient;
    }

    public void setShowClient(boolean showClient) {
        this.showClient = showClient;
    }

    public boolean isShowSupplier() {
        return showSupplier;
    }

    public void setShowSupplier(boolean showSupplier) {
        this.showSupplier = showSupplier;
    }

    public boolean isShowQuotationCharge() {
        return showQuotationCharge;
    }

    public void setShowQuotationCharge(boolean showQuotationCharge) {
        this.showQuotationCharge = showQuotationCharge;
    }

    public boolean isShowCostCharge() {
        return showCostCharge;
    }

    public void setShowCostCharge(boolean showCostCharge) {
        this.showCostCharge = showCostCharge;
    }

    public boolean isShowQuotationCurrency() {
        return showQuotationCurrency;
    }

    public void setShowQuotationCurrency(boolean showQuotationCurrency) {
        this.showQuotationCurrency = showQuotationCurrency;
    }

    public boolean isShowCostCurrency() {
        return showCostCurrency;
    }

    public void setShowCostCurrency(boolean showCostCurrency) {
        this.showCostCurrency = showCostCurrency;
    }

    public boolean isShowQuotationUnit() {
        return showQuotationUnit;
    }

    public void setShowQuotationUnit(boolean showQuotationUnit) {
        this.showQuotationUnit = showQuotationUnit;
    }

    public boolean isShowCostUnit() {
        return showCostUnit;
    }

    public void setShowCostUnit(boolean showCostUnit) {
        this.showCostUnit = showCostUnit;
    }

    public BigDecimal getQuotationTotal() {
        return quotationTotal;
    }

    public void setQuotationTotal(BigDecimal quotationTotal) {
        this.quotationTotal = quotationTotal;
    }

    public BigDecimal getCostTotal() {
        return costTotal;
    }

    public void setCostTotal(BigDecimal costTotal) {
        this.costTotal = costTotal;
    }

    public String getClient() {
        return client;
    }

    public void setClient(String client) {
        this.client = client;
    }

    public String getQuotationCharge() {
        return quotationCharge;
    }

    public void setQuotationCharge(String quotationCharge) {
        this.quotationCharge = quotationCharge;
    }

    public String getQuotationCurrency() {
        return quotationCurrency;
    }

    public void setQuotationCurrency(String quotationCurrency) {
        this.quotationCurrency = quotationCurrency;
    }

    public String getQuotationUnit() {
        return quotationUnit;
    }

    public void setQuotationUnit(String quotationUnit) {
        this.quotationUnit = quotationUnit;
    }

    public String getSupplier() {
        return supplier;
    }

    public void setSupplier(String supplier) {
        this.supplier = supplier;
    }

    public String getCostCharge() {
        return costCharge;
    }

    public void setCostCharge(String costCharge) {
        this.costCharge = costCharge;
    }

    public String getCostCurrency() {
        return costCurrency;
    }

    public void setCostCurrency(String costCurrency) {
        this.costCurrency = costCurrency;
    }

    public String getCostUnit() {
        return costUnit;
    }

    public void setCostUnit(String costUnit) {
        this.costUnit = costUnit;
    }

    public void setReceivablePayableId(Long receivablePayableId) {
        this.receivablePayableId = receivablePayableId;
    }

    public Long getReceivablePayableId() {
        return receivablePayableId;
    }

    public Long getTypeId() {
        return typeId;
    }

    public void setTypeId(Long typeId) {
        this.typeId = typeId;
    }

    public void setBasicInfoId(Long basicInfoId) {
        this.basicInfoId = basicInfoId;
    }

    public Long getBasicInfoId() {
        return basicInfoId;
    }

    public Long getRctId() {
        return rctId;
    }

    public void setRctId(Long rctId) {
        this.rctId = rctId;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public Long getQuotationChargeId() {
        return quotationChargeId;
    }

    public void setQuotationChargeId(Long quotationChargeId) {
        this.quotationChargeId = quotationChargeId;
    }

    public Integer getQuotationStrategyId() {
        return quotationStrategyId;
    }

    public void setQuotationStrategyId(Integer quotationStrategyId) {
        this.quotationStrategyId = quotationStrategyId;
    }

    public Long getQuotationCurrencyId() {
        return quotationCurrencyId;
    }

    public void setQuotationCurrencyId(Long quotationCurrencyId) {
        this.quotationCurrencyId = quotationCurrencyId;
    }

    public BigDecimal getQuotationPrice() {
        return quotationPrice;
    }

    public void setQuotationPrice(BigDecimal quotationPrice) {
        this.quotationPrice = quotationPrice;
    }

    public Long getQuotationUnitId() {
        return quotationUnitId;
    }

    public void setQuotationUnitId(Long quotationUnitId) {
        this.quotationUnitId = quotationUnitId;
    }

    public Integer getQuotationAmount() {
        return quotationAmount;
    }

    public void setQuotationAmount(Integer quotationAmount) {
        this.quotationAmount = quotationAmount;
    }

    public BigDecimal getQuotationExchangeRate() {
        return quotationExchangeRate;
    }

    public void setQuotationExchangeRate(BigDecimal quotationExchangeRate) {
        this.quotationExchangeRate = quotationExchangeRate;
    }

    public BigDecimal getQuotationTaxRate() {
        return quotationTaxRate;
    }

    public void setQuotationTaxRate(BigDecimal quotationTaxRate) {
        this.quotationTaxRate = quotationTaxRate;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public Long getCostChargeId() {
        return costChargeId;
    }

    public void setCostChargeId(Long costChargeId) {
        this.costChargeId = costChargeId;
    }

    public Integer getCostStrategy() {
        return costStrategy;
    }

    public void setCostStrategy(Integer costStrategy) {
        this.costStrategy = costStrategy;
    }

    public Long getCostCurrencyId() {
        return costCurrencyId;
    }

    public void setCostCurrencyId(Long costCurrencyId) {
        this.costCurrencyId = costCurrencyId;
    }

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public Long getCostUnitId() {
        return costUnitId;
    }

    public void setCostUnitId(Long costUnitId) {
        this.costUnitId = costUnitId;
    }

    public Integer getCostAmount() {
        return costAmount;
    }

    public void setCostAmount(Integer costAmount) {
        this.costAmount = costAmount;
    }

    public BigDecimal getCostExchangeRate() {
        return costExchangeRate;
    }

    public void setCostExchangeRate(BigDecimal costExchangeRate) {
        this.costExchangeRate = costExchangeRate;
    }

    public BigDecimal getCostTaxRate() {
        return costTaxRate;
    }

    public void setCostTaxRate(BigDecimal costTaxRate) {
        this.costTaxRate = costTaxRate;
    }

    public BigDecimal getProfit() {
        return profit;
    }

    public void setProfit(BigDecimal profit) {
        this.profit = profit;
    }

}
