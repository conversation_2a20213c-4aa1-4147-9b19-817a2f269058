package com.rich.system.mapper;

import com.rich.system.domain.MidCurrency;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-05
 */
@Mapper
public interface MidCurrencyMapper {
    /**
     * 查询【请填写功能名称】
     *
     * @return 【请填写功能名称】
     */
    List<Long> selectMidCurrencyById(Long belongId, String belongTo);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param midCurrency 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    List<MidCurrency> selectMidCurrencyList(MidCurrency midCurrency);

    /**
     * 新增【请填写功能名称】
     *
     * @param midCurrency 【请填写功能名称】
     * @return 结果
     */
    int insertMidCurrency(MidCurrency midCurrency);

    /**
     * 修改【请填写功能名称】
     *
     * @param midCurrency 【请填写功能名称】
     * @return 结果
     */
    int updateMidCurrency(MidCurrency midCurrency);

    /**
     * 删除【请填写功能名称】
     *
     * @return 结果
     */
    int deleteMidCurrencyById(Long belongId, String belongTo);

    /**
     * 批量删除【请填写功能名称】
     *
     * @return 结果
     */
    int deleteMidCurrencyByIds(Long[] belongIds, String belongTo);

    int batchCurrency(List<MidCurrency> list);
}
