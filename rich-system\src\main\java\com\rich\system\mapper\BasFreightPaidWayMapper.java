package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.BasFreightPaidWay;
import org.apache.ibatis.annotations.Mapper;

/**
 * 付款方式Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-25
 */
@Mapper
public interface BasFreightPaidWayMapper {
    /**
     * 查询付款方式
     *
     * @param freightPaidWayCode 付款方式主键
     * @return 付款方式
     */
    BasFreightPaidWay selectBasFreightPaidWayByFreightPaidWayCode(String freightPaidWayCode);

    /**
     * 查询付款方式列表
     *
     * @param basFreightPaidWay 付款方式
     * @return 付款方式集合
     */
    List<BasFreightPaidWay> selectBasFreightPaidWayList(BasFreightPaidWay basFreightPaidWay);

    /**
     * 新增付款方式
     *
     * @param basFreightPaidWay 付款方式
     * @return 结果
     */
    int insertBasFreightPaidWay(BasFreightPaidWay basFreightPaidWay);

    /**
     * 修改付款方式
     *
     * @param basFreightPaidWay 付款方式
     * @return 结果
     */
    int updateBasFreightPaidWay(BasFreightPaidWay basFreightPaidWay);

    /**
     * 删除付款方式
     *
     * @param freightPaidWayCode 付款方式主键
     * @return 结果
     */
    int deleteBasFreightPaidWayByFreightPaidWayCode(String freightPaidWayCode);

    /**
     * 批量删除付款方式
     *
     * @param freightPaidWayCodes 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasFreightPaidWayByFreightPaidWayCodes(String[] freightPaidWayCodes);
}
