package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.MpWarehouseClient;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.MpWarehouseClientService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 仓库客户信息Controller
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@RestController
@RequestMapping("/mp/warehouseclient")
public class MpWarehouseClientController extends BaseController {
    @Autowired
    private MpWarehouseClientService mpWarehouseClientService;

    /**
     * 查询仓库客户信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MpWarehouseClient mpWarehouseClient) {
        // 检查是否有分页参数，如果有则启用分页，否则不分页
        if (mpWarehouseClient.getParams() != null &&
                (mpWarehouseClient.getParams().get("pageNum") != null ||
                        mpWarehouseClient.getParams().get("pageSize") != null)) {
            startPage();
        } else {
            clearPage();
        }
        List<MpWarehouseClient> list = mpWarehouseClientService.selectMpWarehouseClientList(mpWarehouseClient);
        return getDataTable(list);
    }

    /**
     * 导出仓库客户信息列表
     */
    @Log(title = "仓库客户信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MpWarehouseClient mpWarehouseClient) {
        List<MpWarehouseClient> list = mpWarehouseClientService.selectMpWarehouseClientList(mpWarehouseClient);
        ExcelUtil<MpWarehouseClient> util = new ExcelUtil<MpWarehouseClient>(MpWarehouseClient.class);
        util.exportExcel(response, list, "仓库客户信息数据");
    }

    /**
     * 获取仓库客户信息详细信息
     */
    @GetMapping(value = "/{warehouseClientId}")
    public AjaxResult getInfo(@PathVariable("warehouseClientId") Long warehouseClientId) {
        return AjaxResult.success(mpWarehouseClientService.selectMpWarehouseClientByWarehouseClientId(warehouseClientId));
    }

    /**
     * 新增仓库客户信息
     */
    @Log(title = "仓库客户信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MpWarehouseClient mpWarehouseClient) {
        return toAjax(mpWarehouseClientService.insertMpWarehouseClient(mpWarehouseClient));
    }

    /**
     * 修改仓库客户信息
     */
    @Log(title = "仓库客户信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MpWarehouseClient mpWarehouseClient) {
        return toAjax(mpWarehouseClientService.updateMpWarehouseClient(mpWarehouseClient));
    }

    /**
     * 状态状态
     */
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody MpWarehouseClient mpWarehouseClient) {
        mpWarehouseClient.setUpdateBy(getUserId());
        return toAjax(mpWarehouseClientService.changeStatus(mpWarehouseClient));
    }

    /**
     * 删除仓库客户信息
     */
    @Log(title = "仓库客户信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{warehouseClientIds}")
    public AjaxResult remove(@PathVariable Long[] warehouseClientIds) {
        return toAjax(mpWarehouseClientService.deleteMpWarehouseClientByWarehouseClientIds(warehouseClientIds));
    }
}
