package com.rich.system.mapper;


import com.rich.common.core.domain.entity.BasCompanyRole;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 公司角色Mapper接口
 *
 * <AUTHOR>
 * @date 2022-09-06
 */
@Mapper
public interface BasCompanyRoleMapper {
    /**
     * 查询公司角色
     *
     * @param roleId 公司角色主键
     * @return 公司角色
     */
    BasCompanyRole selectBasCompanyRoleByRoleId(Long roleId);

    /**
     * 查询公司角色列表
     *
     * @param basCompanyRole 公司角色
     * @return 公司角色集合
     */
    List<BasCompanyRole> selectBasCompanyRoleList(BasCompanyRole basCompanyRole);

    /**
     * 新增公司角色
     *
     * @param basCompanyRole 公司角色
     * @return 结果
     */
    int insertBasCompanyRole(BasCompanyRole basCompanyRole);

    /**
     * 修改公司角色
     *
     * @param basCompanyRole 公司角色
     * @return 结果
     */
    int updateBasCompanyRole(BasCompanyRole basCompanyRole);

    /**
     * 删除公司角色
     *
     * @param roleId 公司角色主键
     * @return 结果
     */
    int deleteBasCompanyRoleByRoleId(Long roleId);

    /**
     * 批量删除公司角色
     *
     * @param roleIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasCompanyRoleByRoleIds(Long[] roleIds);
}
