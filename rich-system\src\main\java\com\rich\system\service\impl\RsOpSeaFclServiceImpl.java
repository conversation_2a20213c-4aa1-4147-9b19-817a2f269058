package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.RsOpSeaFcl;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.RedisIdGeneratorService;
import com.rich.system.mapper.RsOpSeaFclMapper;
import com.rich.system.service.RsOpSeaFclService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 整柜海运服务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Service
public class RsOpSeaFclServiceImpl implements RsOpSeaFclService {
    @Autowired
    private RsOpSeaFclMapper rsOpSeaFclMapper;

    @Resource
    private RedisIdGeneratorService redisIdGeneratorService;

    /**
     * 查询整柜海运服务
     *
     * @param seaFclId 整柜海运服务主键
     * @return 整柜海运服务
     */
    @Override
    public RsOpSeaFcl selectRsOpSeaFclBySeaFclId(Long seaFclId) {
        return rsOpSeaFclMapper.selectRsOpSeaFclBySeaFclId(seaFclId);
    }

    /**
     * 查询整柜海运服务列表
     *
     * @param rsOpSeaFcl 整柜海运服务
     * @return 整柜海运服务
     */
    @Override
    public List<RsOpSeaFcl> selectRsOpSeaFclList(RsOpSeaFcl rsOpSeaFcl) {
        return rsOpSeaFclMapper.selectRsOpSeaFclList(rsOpSeaFcl);
    }

    /**
     * 查询商务订舱列表
     */
    @Override
    public List<RsOpSeaFcl> selectRsPsaRctList(RsOpSeaFcl rsOpSeaFcl) {
        return rsOpSeaFclMapper.selectRsPsaRctList(rsOpSeaFcl);
    }

    /**
     * 新增整柜海运服务
     *
     * @param rsOpSeaFcl 整柜海运服务
     * @return 结果
     */
    @Override
    public Long insertRsOpSeaFcl(RsOpSeaFcl rsOpSeaFcl) {
        if (rsOpSeaFcl.getSeaId() == null || (rsOpSeaFcl.getSeaId() != null && rsOpSeaFcl.getPsaNo() == null)) {
            String psaNo = redisIdGeneratorService.generateUniqueId("psa_no");
            String date = DateUtils.dateTime();
            rsOpSeaFcl.setSqdPsaNo("PSA" + date.substring(2, 6) + psaNo);
        }
        rsOpSeaFclMapper.insertRsOpSeaFcl(rsOpSeaFcl);
        return rsOpSeaFcl.getSeaId();
    }

    /**
     * 修改整柜海运服务
     *
     * @param rsOpSeaFcl 整柜海运服务
     * @return 结果
     */
    @Override
    public int updateRsOpSeaFcl(RsOpSeaFcl rsOpSeaFcl) {
        return rsOpSeaFclMapper.updateRsOpSeaFcl(rsOpSeaFcl);
    }

    /**
     * 修改整柜海运服务状态
     *
     * @param rsOpSeaFcl 整柜海运服务
     * @return 整柜海运服务
     */
    @Override
    public int changeStatus(RsOpSeaFcl rsOpSeaFcl) {
        return rsOpSeaFclMapper.updateRsOpSeaFcl(rsOpSeaFcl);
    }

    @Override
    public RsOpSeaFcl selectRsPsaRctBySeaId(Long seaId) {
        return rsOpSeaFclMapper.selectRsPsaRctBySeaId(seaId);
    }

    @Override
    public int updateRsOpSeaFclByRctId(RsOpSeaFcl rsOpSeaFcl) {
        if (rsOpSeaFcl.getSeaId() != null) {
            return 0;
        }

        if (rsOpSeaFcl.getPsaNo() == null) {
            String psaNo = redisIdGeneratorService.generateUniqueId("psa_no");
            String date = DateUtils.dateTime();
            rsOpSeaFcl.setSqdPsaNo("PSA" + date.substring(2, 6) + psaNo);
        }
        return rsOpSeaFclMapper.updateRsOpSeaFclByRctId(rsOpSeaFcl);
    }

    /**
     * 批量删除整柜海运服务
     *
     * @param seaFclIds 需要删除的整柜海运服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpSeaFclBySeaFclIds(Long[] seaFclIds) {
        return rsOpSeaFclMapper.deleteRsOpSeaFclBySeaFclIds(seaFclIds);
    }

    /**
     * 删除整柜海运服务信息
     *
     * @param seaFclId 整柜海运服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpSeaFclBySeaFclId(Long seaFclId) {
        return rsOpSeaFclMapper.deleteRsOpSeaFclBySeaFclId(seaFclId);
    }
}
