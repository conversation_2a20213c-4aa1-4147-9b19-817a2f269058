package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasDifficultyLevel;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/23 17:45
 * @Version 1.0
 */
@Mapper
public interface BasDifficultyLevelMapper {
    /**
     * 查询订单难度
     *
     * @param difficultyLevelId 订单难度主键
     * @return 订单难度
     */
    BasDifficultyLevel selectBasDifficultyLevelByDifficultyLevelId(Long difficultyLevelId);

    /**
     * 查询订单难度列表
     *
     * @param basDifficultyLevel 订单难度
     * @return 订单难度集合
     */
    List<BasDifficultyLevel> selectBasDifficultyLevelList(BasDifficultyLevel basDifficultyLevel);

    /**
     * 新增订单难度
     *
     * @param basDifficultyLevel 订单难度
     * @return 结果
     */
    int insertBasDifficultyLevel(BasDifficultyLevel basDifficultyLevel);

    /**
     * 修改订单难度
     *
     * @param basDifficultyLevel 订单难度
     * @return 结果
     */
    int updateBasDifficultyLevel(BasDifficultyLevel basDifficultyLevel);

    /**
     * 删除订单难度
     *
     * @param difficultyLevelId 订单难度主键
     * @return 结果
     */
    int deleteBasDifficultyLevelByDifficultyLevelId(Long difficultyLevelId);

    /**
     * 批量删除订单难度
     *
     * @param difficultyLevelIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasDifficultyLevelByDifficultyLevelIds(Long[] difficultyLevelIds);
}
