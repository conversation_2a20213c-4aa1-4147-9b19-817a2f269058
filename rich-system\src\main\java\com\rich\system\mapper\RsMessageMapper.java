package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsMessage;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 消息通知Mapper接口
 *
 * <AUTHOR>
 * @date 2023-02-15
 */
@Mapper
public interface RsMessageMapper {
    /**
     * 查询消息通知
     *
     * @param messageId 消息通知主键
     * @return 消息通知
     */
    RsMessage selectRsMessageByMessageId(Long messageId);

    /**
     * 查询消息通知列表
     *
     * @param rsMessage 消息通知
     * @return 消息通知集合
     */
    List<RsMessage> selectRsMessageList(RsMessage rsMessage);

    /**
     * 新增消息通知
     *
     * @param rsMessage 消息通知
     * @return 结果
     */
    int insertRsMessage(RsMessage rsMessage);

    /**
     * 修改消息通知
     *
     * @param rsMessage 消息通知
     * @return 结果
     */
    int updateRsMessage(RsMessage rsMessage);

    /**
     * 删除消息通知
     *
     * @param messageId 消息通知主键
     * @return 结果
     */
    int deleteRsMessageByMessageId(Long messageId);

    /**
     * 批量删除消息通知
     *
     * @param messageIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsMessageByMessageIds(Long[] messageIds);

    int countNewMessage(RsMessage rsMessage);
}
