package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.BasCtnrType;
import org.apache.ibatis.annotations.Mapper;

/**
 * 箱型特征Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Mapper
public interface BasCtnrTypeMapper {
    /**
     * 查询箱型特征
     *
     * @param ctnrTypeId 箱型特征主键
     * @return 箱型特征
     */
    BasCtnrType selectBasCtnrTypeByCtnrTypeId(Long ctnrTypeId);

    /**
     * 查询箱型特征列表
     *
     * @param basCtnrType 箱型特征
     * @return 箱型特征集合
     */
    List<BasCtnrType> selectBasCtnrTypeList(BasCtnrType basCtnrType);

    /**
     * 新增箱型特征
     *
     * @param basCtnrType 箱型特征
     * @return 结果
     */
    int insertBasCtnrType(BasCtnrType basCtnrType);

    /**
     * 修改箱型特征
     *
     * @param basCtnrType 箱型特征
     * @return 结果
     */
    int updateBasCtnrType(BasCtnrType basCtnrType);

    /**
     * 删除箱型特征
     *
     * @param ctnrTypeId 箱型特征主键
     * @return 结果
     */
    int deleteBasCtnrTypeByCtnrTypeId(Long ctnrTypeId);

    /**
     * 批量删除箱型特征
     *
     * @param ctnrTypeIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasCtnrTypeByCtnrTypeIds(Long[] ctnrTypeIds);
}
