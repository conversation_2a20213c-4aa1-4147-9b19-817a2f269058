package com.rich.common.core.domain.entity;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 币种对象 bas_currency
 * 
 * <AUTHOR>
 * @date 2022-08-29
 */
public class BasCurrency extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long currencyId;

    /** 直接编码，如 RMB ，USD */
    @Excel(name = "直接编码，如 RMB ，USD")
    private String currencyCode;

    /** 所属国家/地区 */
    @Excel(name = "所属国家/地区")
    private Long locationId;

    /** 币种本地语名称 */
    @Excel(name = "币种本地语名称")
    private String currencyLocalName;

    /**
     * 汇率
     */
    @Excel(name = "汇率")
    private BigDecimal exchangeRate;

    private String status;

    private String currencyQuery;

    private String orderNum;

    private String location;

    public BigDecimal getExchangeRate() {
        return exchangeRate;
    }

    public void setExchangeRate(BigDecimal exchangeRate) {
        this.exchangeRate = exchangeRate;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    public String getCurrencyQuery() {
        return currencyQuery;
    }

    public void setCurrencyQuery(String currencyQuery) {
        this.currencyQuery = currencyQuery;
    }

    public void setCurrencyId(Long currencyId)
    {
        this.currencyId = currencyId;
    }

    public Long getCurrencyId() 
    {
        return currencyId;
    }
    public void setCurrencyCode(String currencyCode) 
    {
        this.currencyCode = currencyCode;
    }

    public String getCurrencyCode() 
    {
        return currencyCode;
    }
    public void setLocationId(Long locationId) 
    {
        this.locationId = locationId;
    }

    public Long getLocationId() 
    {
        return locationId;
    }
    public void setCurrencyLocalName(String currencyLocalName) 
    {
        this.currencyLocalName = currencyLocalName;
    }

    public String getCurrencyLocalName() 
    {
        return currencyLocalName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("currencyId", getCurrencyId())
            .append("currencyCode", getCurrencyCode())
            .append("locationId", getLocationId())
            .append("currencyLocalName", getCurrencyLocalName())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("deleteBy", getDeleteBy())
            .append("deleteTime", getDeleteTime())
            .append("deleteStatus", getDeleteStatus())
            .toString();
    }
}
