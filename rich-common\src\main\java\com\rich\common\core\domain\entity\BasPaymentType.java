package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 收付顺序对象 bas_payment_type
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
public class BasPaymentType extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 收付顺序
     */
    private Long paymentTypeId;

    /**
     * 简称
     */
    @Excel(name = "简称")
    private String paymentTypeShortName;

    /**
     * 中文名
     */
    @Excel(name = "中文名")
    private String paymentTypeLocalName;

    /**
     * 英文名
     */
    @Excel(name = "英文名")
    private String paymentTypeEnName;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Integer orderNum;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;

    /**
     * 删除人
     */
    private String deleteBy;

    /**
     * 删除时间
     */
    private Date deleteTime;

    /**
     * 数据状态（-1:删除或不可用，0：正常）
     */
    private String deleteStatus;

    public void setPaymentTypeId(Long paymentTypeId) {
        this.paymentTypeId = paymentTypeId;
    }

    public Long getPaymentTypeId() {
        return paymentTypeId;
    }

    public void setPaymentTypeShortName(String paymentTypeShortName) {
        this.paymentTypeShortName = paymentTypeShortName;
    }

    public String getPaymentTypeShortName() {
        return paymentTypeShortName;
    }

    public void setPaymentTypeLocalName(String paymentTypeLocalName) {
        this.paymentTypeLocalName = paymentTypeLocalName;
    }

    public String getPaymentTypeLocalName() {
        return paymentTypeLocalName;
    }

    public void setPaymentTypeEnName(String paymentTypeEnName) {
        this.paymentTypeEnName = paymentTypeEnName;
    }

    public String getPaymentTypeEnName() {
        return paymentTypeEnName;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
}
