package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsOpTruck;
import com.rich.common.core.domain.entity.RsOpTruck;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsOpExportTruckService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 出口拖车服务Controller
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@RestController
@RequestMapping("/system/opexporttruck")
public class RsOpExportTruckController extends BaseController {
    @Autowired
    private RsOpExportTruckService rsOpExportTruckService;

    /**
     * 查询出口拖车服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opexporttruck:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsOpTruck rsOpTruck) {
        startPage();
        List<RsOpTruck> list = rsOpExportTruckService.selectRsOpExportTruckList(rsOpTruck);
        return getDataTable(list);
    }

    /**
     * 导出出口拖车服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opexporttruck:export')")
    @Log(title = "出口拖车服务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsOpTruck rsOpTruck) {
        List<RsOpTruck> list = rsOpExportTruckService.selectRsOpExportTruckList(rsOpTruck);
        ExcelUtil<RsOpTruck> util = new ExcelUtil<RsOpTruck>(RsOpTruck.class);
        util.exportExcel(response, list, "出口拖车服务数据");
    }

    /**
     * 获取出口拖车服务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:opexporttruck:query')")
    @GetMapping(value = "/{exportTruckId}")
    public AjaxResult getInfo(@PathVariable("exportTruckId") Long exportTruckId) {
        return AjaxResult.success(rsOpExportTruckService.selectRsOpExportTruckByExportTruckId(exportTruckId));
    }

    /**
     * 新增出口拖车服务
     */
    @PreAuthorize("@ss.hasPermi('system:opexporttruck:add')")
    @Log(title = "出口拖车服务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsOpTruck rsOpExportTruck) {
        return toAjax(rsOpExportTruckService.insertRsOpExportTruck(rsOpExportTruck));
    }

    /**
     * 修改出口拖车服务
     */
    @PreAuthorize("@ss.hasPermi('system:opexporttruck:edit')")
    @Log(title = "出口拖车服务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsOpTruck rsOpExportTruck) {
        return toAjax(rsOpExportTruckService.updateRsOpExportTruck(rsOpExportTruck));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:opexporttruck:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsOpTruck rsOpExportTruck) {
        rsOpExportTruck.setUpdateBy(getUserId());
        return toAjax(rsOpExportTruckService.changeStatus(rsOpExportTruck));
    }

    /**
     * 删除出口拖车服务
     */
    @PreAuthorize("@ss.hasPermi('system:opexporttruck:remove')")
    @Log(title = "出口拖车服务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{exportTruckIds}")
    public AjaxResult remove(@PathVariable Long[] exportTruckIds) {
        return toAjax(rsOpExportTruckService.deleteRsOpExportTruckByExportTruckIds(exportTruckIds));
    }
}
