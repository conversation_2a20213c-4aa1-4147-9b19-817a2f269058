package com.rich.system.service.impl;

import com.rich.common.config.WebSocket;
import com.rich.common.core.domain.entity.*;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.StringUtils;
import com.rich.system.domain.MidServiceType;
import com.rich.system.mapper.*;
import com.rich.system.service.RsRctService;
import com.rich.system.service.RsReimburseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 报销记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-30
 */
@Service

public class RsReimburseServiceImpl implements RsReimburseService {
    @Autowired
    private RsReimburseMapper rsReimburseMapper;
    @Autowired
    private RsMessageMapper rsMessageMapper;
    @Autowired
    private MidServiceTypeMapper midServiceTypeMapper;
    @Autowired
    private WebSocket webSocket;
    @Autowired
    private RsStaffMapper rsStaffMapper;
    @Autowired
    private MidReimburseConfirmMapper midReimburseConfirmMapper;
    @Resource
    private RsOpExpandServiceMapper rsOpExpandServiceMapper;
    @Resource
    private RsServiceInstancesMapper rsServiceInstancesMapper;
    @Resource
    private RsChargeMapper rsChargeMapper;
    @Resource
    private RsBankRecordServiceImpl rsBankRecordService;

    @Resource
    private RsRctService rsRctService;

    /**
     * 查询报销记录
     *
     * @param reimburseId 报销记录主键
     * @return 报销记录
     */
    @Override
    public RsReimburse selectRsReimburseByReimburseId(Long reimburseId) {
        return rsReimburseMapper.selectRsReimburseByReimburseId(reimburseId);
    }

    /**
     * 查询报销记录列表
     *
     * @param rsReimburse 报销记录
     * @return 报销记录
     */
    @Override
    public List<RsReimburse> selectRsReimburseList(RsReimburse rsReimburse) {
        return rsReimburseMapper.selectRsReimburseList(rsReimburse);
    }

    /**
     * 新增报销记录
     *
     * @param rsReimburse 报销记录
     * @return 结果
     */
    @Override
    public int insertRsReimburse(RsReimburse rsReimburse) {
        rsReimburse.setCreateTime(DateUtils.getNowDate());
        rsReimburse.setCreateBy(SecurityUtils.getUserId());
        return rsReimburseMapper.insertRsReimburse(rsReimburse);
    }

    /**
     * 修改报销记录
     *
     * @param rsReimburse 报销记录
     * @return 结果
     */
    @Override
    public int updateRsReimburse(RsReimburse rsReimburse) {
        rsReimburse.setUpdateBy(SecurityUtils.getUserId());
        rsReimburse.setUpdateTime(DateUtils.getNowDate());
        return rsReimburseMapper.updateRsReimburse(rsReimburse);
    }

    /**
     * 修改报销记录状态
     *
     * @param rsReimburse 报销记录
     * @return 报销记录
     */
    @Override
    public int changeStatus(RsReimburse rsReimburse) {
        return rsReimburseMapper.updateRsReimburse(rsReimburse);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int rsReimburseApproval(List<RsReimburse> rsReimburses) {
        int row = 0;
        for (RsReimburse rsReimburse : rsReimburses) {
            RsMessage message = new RsMessage();
            MidReimburseConfirm midReimburseConfirm = null;
            if (rsReimburse.getDeptReimburseConfirm() != null) {
                midReimburseConfirm = rsReimburse.getDeptReimburseConfirm();
            }
            if (rsReimburse.getHrReimburseConfirm() != null) {
                midReimburseConfirm = rsReimburse.getHrReimburseConfirm();
            }
            if (rsReimburse.getCeoReimburseConfirm() != null) {
                midReimburseConfirm = rsReimburse.getCeoReimburseConfirm();
            }
            if (rsReimburse.getFinanceReimburseConfirm() != null) {
                midReimburseConfirm = rsReimburse.getFinanceReimburseConfirm();
            }
            assert midReimburseConfirm != null;
            String messageFrom = midReimburseConfirm.getReimburseConfirmType();
            message.setCreateBy(SecurityUtils.getUserId());
            message.setCreateTime(DateUtils.getNowDate());
            message.setMessageType(7L);
            if (Objects.equals(messageFrom, "11")) {
                message.setMessageFrom(rsReimburse.getDeptConfirmedId());
            }
            if (Objects.equals(messageFrom, "12")) {
                message.setMessageFrom(rsReimburse.getHrConfirmedId());
            }
            if (Objects.equals(messageFrom, "13")) {
                message.setMessageFrom(rsReimburse.getFinanceConfirmedId());
            }
            if (Objects.equals(messageFrom, "6")) {
                message.setMessageFrom(rsReimburse.getCeoConfirmedId());
                // 如果报销费用是货运订单中的成本报销，将该成本制作一条费用插入到该票单中
                if (rsReimburse.getChargeTypeId() != null && rsReimburse.getChargeTypeId().equals(505L)) {
                    // TODO 添加一条杂费到成本中
                    String rctNo = rsReimburse.getRelationRct() != null ? rsReimburse.getRelationRct().replaceAll("\\s+", "") : "";
                    if (StringUtils.isNotBlank(rctNo)) {
                        RsRct rsRct = rsRctService.selectRsRctByRctId(rsRctService.getRctIdByRctNo(rctNo));
                        if (rsRct != null) {
                            // 杂费
                            RsCharge rsCharge = new RsCharge();
                            // 杂费名称id
                            rsCharge.setDnChargeNameId(20L);
                            rsCharge.setDnAmount(new BigDecimal(1));
                            rsCharge.setDnUnitRate(rsReimburse.getActualReimbursePrice());
                            rsCharge.setDnCurrencyCode("RMB");
                            rsCharge.setBasicCurrencyRate(new BigDecimal(1));
                            rsCharge.setDutyRate(new BigDecimal(0));
                            rsCharge.setSubtotal(rsReimburse.getActualReimbursePrice());
                            rsCharge.setIsRecievingOrPaying(1L);
                            rsCharge.setSqdDnCurrencyBalance(rsReimburse.getActualReimbursePrice());
                            rsCharge.setSqdRctNo(rctNo);
                            rsCharge.setSqdRctId(rsRct.getRctId());
                            rsCharge.setIsAccountConfirmed("1");

                            RsOpOther rsOpOther = rsRct.getRsOpOther();
                            RsServiceInstances rsServiceInstance;
                            if (rsRct.getRsOpOther() == null) {
                                // 当前操作单没有其他服务
                                rsServiceInstance = new RsServiceInstances();
                                rsOpOther = new RsOpOther();


                                // 插入服务实例
                                rsServiceInstance.setServiceBelongTo("报销");
                                rsServiceInstance.setServiceTypeId(104L);
                                rsServiceInstance.setRctId(rsRct.getRctId());
                                rsServiceInstance.setRctNo(rsRct.getRctNo());
                                rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);

                                // 插入服务
                                rsOpOther.setServiceId(rsServiceInstance.getServiceId());
                                rsOpOther.setSqdRctNo(rsRct.getRctNo());
                                rsOpOther.setSqdServiceTypeId(104L);
                                rsOpExpandServiceMapper.insertRsOpExpandService(rsOpOther);

                                // 还要更新操作单的服务列表，加上其他服务
                                rsRct.setServiceTypeIdList(rsRct.getServiceTypeIdList().isEmpty() ? rsRct.getServiceTypeIdList().concat("104") : rsRct.getServiceTypeIdList().concat(",104"));
                                MidServiceType MidServiceType = new MidServiceType();
                                MidServiceType.setBelongId(rsRct.getRctId());
                                MidServiceType.setServiceTypeId(104L);
                                MidServiceType.setBelongTo("rct");
                                midServiceTypeMapper.insertMidServiceType(MidServiceType);
                            } else {
                                rsServiceInstance = rsOpOther.getRsServiceInstances();
                                if (rsServiceInstance == null) {
                                    rsServiceInstance = new RsServiceInstances();
                                    // 插入服务实例
                                    rsServiceInstance.setServiceId(rsOpOther.getServiceId());
                                    rsServiceInstance.setServiceBelongTo("报销");
                                    rsServiceInstance.setServiceTypeId(104L);
                                    rsServiceInstance.setRctId(rsRct.getRctId());
                                    rsServiceInstance.setRctNo(rsRct.getRctNo());
                                    rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);
                                }
                            }
                            // 将杂费添加到其他操作对象的费用列表中
                            if (rsOpOther.getRsChargeList() != null) {
                                rsOpOther.getRsChargeList().add(rsCharge);
                            } else {
                                rsOpOther.setRsChargeList(new ArrayList<>());
                                rsOpOther.getRsChargeList().add(rsCharge);
                            }
                            updateFreight(rsServiceInstance, rsOpOther.getRsChargeList(), rsRct.getRctId(), 104L, rsRct.getRctNo(), rsRct.getRctCreateTime());
                        }
                    }

                }
            }
            message.setMessageOwner(rsReimburse.getStaffId().toString());
            message.setMessageDate(midReimburseConfirm.getCreateDate());
            RsStaff staff = rsStaffMapper.selectUserById(midReimburseConfirm.getStaffId());
            if (midReimburseConfirm.getReimburseConfirm().equals("1")) {
                message.setMessageTitle("同意审批");
                message.setMessageContent(SecurityUtils.getUsername() + "同意你的报销申请。" + "具体内容：" + midReimburseConfirm.getReimburseConfirmContent());
                webSocket.sendOneMessage(staff.getStaffId() + staff.getStaffCode(), staff.getStaffFamilyLocalName() + staff.getStaffGivingLocalName() + "同意你的报销申请");
            }
            if (midReimburseConfirm.getReimburseConfirm().equals("0")) {
                message.setMessageTitle("驳回审批");
                message.setMessageContent(SecurityUtils.getUsername() + "驳回了你的报销申请。" + "具体内容：" + midReimburseConfirm.getReimburseConfirmContent());
                webSocket.sendOneMessage(staff.getStaffId() + staff.getStaffCode(), staff.getStaffFamilyLocalName() + staff.getStaffGivingLocalName() + "驳回了你的报销申请");
            }
            // 更新报销记录中的确认信息
            rsReimburseMapper.rsReimburseApproval(rsReimburse);
            // TODO 消息通知
            rsMessageMapper.insertRsMessage(message);
            row += midReimburseConfirmMapper.insertMidReimburseConfirm(midReimburseConfirm);
        }
        return row;
    }

    @Override
    public List<RsReimburse> selectRsReimburseWriteOffList(RsReimburse rsReimburse) {
        return rsReimburseMapper.selectRsReimburseWritOffList(rsReimburse);
    }

    @Override
    public int rsReimburseWriteOff(List<RsReimburse> rsReimburse) {
        int i = 0;
        for (RsReimburse reimburse : rsReimburse) {
            i += rsReimburseMapper.updateRsReimburse(reimburse);
        }
        return i;
    }

    @Override
    public int rsReimburseApprovalCancel(RsReimburse rsReimburse) {
        int type = rsReimburse.getType();
        if (type == 11) {
            return midReimburseConfirmMapper.deleteMidReimburseConfirmByReimburseType(rsReimburse.getReimburseId(), type);
        }
        if (type == 12) {
            return midReimburseConfirmMapper.deleteMidReimburseConfirmByReimburseType(rsReimburse.getReimburseId(), type);
        }
        if (type == 13) {
            return midReimburseConfirmMapper.deleteMidReimburseConfirmByReimburseType(rsReimburse.getReimburseId(), type);
        }
        return 0;
    }

    /**
     * 批量删除报销记录
     *
     * @param reimburseIds 需要删除的报销记录主键
     * @return 结果
     */
    @Override
    public int deleteRsReimburseByReimburseIds(Long[] reimburseIds) {
        midReimburseConfirmMapper.deleteMidReimburseConfirmByReimburseIds(reimburseIds);
        return rsReimburseMapper.deleteRsReimburseByReimburseIds(reimburseIds);
    }

    /**
     * 删除报销记录信息
     *
     * @param reimburseId 报销记录主键
     * @return 结果
     */
    @Override
    public int deleteRsReimburseByReimburseId(Long reimburseId) {
        midReimburseConfirmMapper.deleteMidReimburseConfirmByReimburseId(reimburseId);
        return rsReimburseMapper.deleteRsReimburseByReimburseId(reimburseId);
    }

    public void updateFreight(RsServiceInstances rsServiceInstances, List<RsCharge> rsCharges, Long rctId, long sqdServiceTypeId, String rctNo, Date rctCreateTime) {
        if (rsCharges == null || rsCharges.isEmpty()) {
            return;
        }
        if (rsServiceInstances != null && rsServiceInstances.getServiceId() != null) {
            for (RsCharge rsCharge : rsCharges) {
                rsCharge.setServiceId(rsServiceInstances.getServiceId());
                rsCharge.setSqdRctId(rctId);
                rsCharge.setSqdServiceTypeId(sqdServiceTypeId);
                rsCharge.setPaymentTitleCode(rsServiceInstances.getPaymentTitleCode());
                rsCharge.setIsRecievingOrPaying(1L);
                rsCharge.setSqdRctNo(rctNo);
                rsCharge.setCurrencyRateCalculateDate(rctCreateTime);
                rsCharge.setSqdDnCurrencyBalance(rsCharge.getDnUnitRate() != null ? rsCharge.getDnUnitRate().multiply(rsCharge.getDnAmount()) : new BigDecimal(0));
                rsChargeMapper.upsertRsCharge(rsCharge);
            }
        }
    }

    /**
     * 创建 RsCharge 对象并初始化属性。
     */
    private RsCharge createRsCharge(BigDecimal reimbursePrice, String rctNo, Long rctId) {
        RsCharge rsCharge = new RsCharge();
        rsCharge.setDnChargeNameId(59L);
        rsCharge.setDnAmount(new BigDecimal(1));
        rsCharge.setDnUnitRate(reimbursePrice);
        rsCharge.setDnCurrencyCode("RMB");
        rsCharge.setBasicCurrencyRate(BigDecimal.ONE);
        rsCharge.setDutyRate(BigDecimal.ZERO);
        rsCharge.setSubtotal(reimbursePrice);
        rsCharge.setIsRecievingOrPaying(1L);
        rsCharge.setSqdDnCurrencyBalance(reimbursePrice);
        rsCharge.setSqdRctNo(rctNo);
        rsCharge.setSqdRctId(rctId);
        rsCharge.setIsAccountConfirmed("1");
        return rsCharge;
    }

}
