package com.rich.system.domain;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 mid_doc_issue_type
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
public class MidDocIssueType extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long docIssueTypeId;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long docId;

    public void setDocIssueTypeId(Long docIssueTypeId) {
        this.docIssueTypeId = docIssueTypeId;
    }

    public Long getDocIssueTypeId() {
        return docIssueTypeId;
    }

    public void setDocId(Long docId) {
        this.docId = docId;
    }

    public Long getDocId() {
        return docId;
    }

}
