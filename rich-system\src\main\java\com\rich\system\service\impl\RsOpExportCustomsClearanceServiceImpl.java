package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpExportCustomsClearance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsOpExportCustomsClearanceMapper;
import com.rich.system.service.RsOpExportCustomsClearanceService;

/**
 * 出口报关服务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Service
public class RsOpExportCustomsClearanceServiceImpl implements RsOpExportCustomsClearanceService {
    @Autowired
    private RsOpExportCustomsClearanceMapper rsOpExportCustomsClearanceMapper;

    /**
     * 查询出口报关服务
     *
     * @param exportCustomsClearanceId 出口报关服务主键
     * @return 出口报关服务
     */
    @Override
    public RsOpExportCustomsClearance selectRsOpExportCustomsClearanceByExportCustomsClearanceId(Long exportCustomsClearanceId) {
        return rsOpExportCustomsClearanceMapper.selectRsOpExportCustomsClearanceByExportCustomsClearanceId(exportCustomsClearanceId);
    }

    /**
     * 查询出口报关服务列表
     *
     * @param rsOpExportCustomsClearance 出口报关服务
     * @return 出口报关服务
     */
    @Override
    public List<RsOpExportCustomsClearance> selectRsOpExportCustomsClearanceList(RsOpExportCustomsClearance rsOpExportCustomsClearance) {
        return rsOpExportCustomsClearanceMapper.selectRsOpExportCustomsClearanceList(rsOpExportCustomsClearance);
    }

    /**
     * 新增出口报关服务
     *
     * @param rsOpExportCustomsClearance 出口报关服务
     * @return 结果
     */
    @Override
    public int insertRsOpExportCustomsClearance(RsOpExportCustomsClearance rsOpExportCustomsClearance) {
        return rsOpExportCustomsClearanceMapper.insertRsOpExportCustomsClearance(rsOpExportCustomsClearance);
    }

    /**
     * 修改出口报关服务
     *
     * @param rsOpExportCustomsClearance 出口报关服务
     * @return 结果
     */
    @Override
    public int updateRsOpExportCustomsClearance(RsOpExportCustomsClearance rsOpExportCustomsClearance) {
        return rsOpExportCustomsClearanceMapper.updateRsOpExportCustomsClearance(rsOpExportCustomsClearance);
    }

    /**
     * 修改出口报关服务状态
     *
     * @param rsOpExportCustomsClearance 出口报关服务
     * @return 出口报关服务
     */
    @Override
    public int changeStatus(RsOpExportCustomsClearance rsOpExportCustomsClearance) {
        return rsOpExportCustomsClearanceMapper.updateRsOpExportCustomsClearance(rsOpExportCustomsClearance);
    }

    /**
     * 批量删除出口报关服务
     *
     * @param exportCustomsClearanceIds 需要删除的出口报关服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpExportCustomsClearanceByExportCustomsClearanceIds(Long[] exportCustomsClearanceIds) {
        return rsOpExportCustomsClearanceMapper.deleteRsOpExportCustomsClearanceByExportCustomsClearanceIds(exportCustomsClearanceIds);
    }

    /**
     * 删除出口报关服务信息
     *
     * @param exportCustomsClearanceId 出口报关服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpExportCustomsClearanceByExportCustomsClearanceId(Long exportCustomsClearanceId) {
        return rsOpExportCustomsClearanceMapper.deleteRsOpExportCustomsClearanceByExportCustomsClearanceId(exportCustomsClearanceId);
    }
}
