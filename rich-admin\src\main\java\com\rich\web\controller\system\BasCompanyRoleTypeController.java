package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasCompanyRoleType;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasCompanyRoleTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 公司角色类型Controller
 *
 * <AUTHOR>
 * @date 2022-09-06
 */
@RestController
@RequestMapping("/system/companyroletype")
public class BasCompanyRoleTypeController extends BaseController {

    @Autowired
    private BasCompanyRoleTypeService basCompanyRoleTypeService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询公司角色类型列表
     */
    @PreAuthorize("@ss.hasPermi('system:companyroletype:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasCompanyRoleType basCompanyRoleType) {
        startPage();
        List<BasCompanyRoleType> list = basCompanyRoleTypeService.selectBasCompanyRoleTypeList(basCompanyRoleType);
        return getDataTable(list);
    }

    @GetMapping("/selectList")
    public AjaxResult selectList(BasCompanyRoleType basCompanyRoleType) {
        List<BasCompanyRoleType> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "companyRoleType");
        if (list == null) {
            basCompanyRoleType.setStatus("0");
            list = basCompanyRoleTypeService.selectBasCompanyRoleTypeList(basCompanyRoleType);
            redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "companyRoleType");
            redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "companyRoleType", list);
        }
        return AjaxResult.success(list);
    }

    /**
     * 导出公司角色类型列表
     */
    @PreAuthorize("@ss.hasPermi('system:companyroletype:export')")
    @Log(title = "公司角色类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasCompanyRoleType basCompanyRoleType) {
        List<BasCompanyRoleType> list = basCompanyRoleTypeService.selectBasCompanyRoleTypeList(basCompanyRoleType);
        ExcelUtil<BasCompanyRoleType> util = new ExcelUtil<BasCompanyRoleType>(BasCompanyRoleType.class);
        util.exportExcel(response, list, "公司角色类型数据");
    }

    /**
     * 获取公司角色类型详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:companyroletype:edit')")
    @GetMapping(value = "/{roleTypeId}")
    public AjaxResult getInfo(@PathVariable("roleTypeId") Long roleTypeId) {
        return AjaxResult.success(basCompanyRoleTypeService.selectBasCompanyRoleTypeByRoleTypeId(roleTypeId));
    }

    /**
     * 新增公司角色类型
     */
    @PreAuthorize("@ss.hasPermi('system:companyroletype:add')")
    @Log(title = "公司角色类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasCompanyRoleType basCompanyRoleType) {
        int out = basCompanyRoleTypeService.insertBasCompanyRoleType(basCompanyRoleType);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "companyRoleType");
        return toAjax(out);
    }

    /**
     * 修改公司角色类型
     */
    @PreAuthorize("@ss.hasPermi('system:companyroletype:edit')")
    @Log(title = "公司角色类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasCompanyRoleType basCompanyRoleType) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "companyRoleType");
        return toAjax(basCompanyRoleTypeService.updateBasCompanyRoleType(basCompanyRoleType));
    }

    /**
     * 修改公司角色类型
     */
    @PreAuthorize("@ss.hasPermi('system:companyroletype:edit')")
    @Log(title = "公司角色类型", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasCompanyRoleType basCompanyRoleType) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "companyRoleType");
        return toAjax(basCompanyRoleTypeService.changeStatus(basCompanyRoleType));
    }

    /**
     * 删除公司角色类型
     */
    @PreAuthorize("@ss.hasPermi('system:companyroletype:remove')")
    @Log(title = "公司角色类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{roleTypeIds}")
    public AjaxResult remove(@PathVariable Long[] roleTypeIds) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "companyRoleType");
        return toAjax(basCompanyRoleTypeService.deleteBasCompanyRoleTypeByRoleTypeIds(roleTypeIds));
    }
}
