package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpDocDeclare;
import com.rich.common.core.domain.entity.RsOpExportCustomsClearance;
import com.rich.common.core.domain.entity.RsOpFreeDeclare;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 出口报关服务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Mapper
public interface RsOpExportCustomsClearanceMapper {
    /**
     * 查询出口报关服务
     *
     * @param exportCustomsClearanceId 出口报关服务主键
     * @return 出口报关服务
     */
    RsOpExportCustomsClearance selectRsOpExportCustomsClearanceByExportCustomsClearanceId(Long exportCustomsClearanceId);

    /**
     * 查询出口报关服务列表
     *
     * @param rsOpExportCustomsClearance 出口报关服务
     * @return 出口报关服务集合
     */
    List<RsOpExportCustomsClearance> selectRsOpExportCustomsClearanceList(RsOpExportCustomsClearance rsOpExportCustomsClearance);

    /**
     * 新增出口报关服务
     *
     * @param rsOpExportCustomsClearance 出口报关服务
     * @return 结果
     */
    int insertRsOpExportCustomsClearance(RsOpExportCustomsClearance rsOpExportCustomsClearance);

    int insertRsOpExportCustomsClearance(RsOpDocDeclare rsOpDocDeclare);

    int insertRsOpExportCustomsClearance(RsOpFreeDeclare rsOpFreeDeclare);

    /**
     * 修改出口报关服务
     *
     * @param rsOpExportCustomsClearance 出口报关服务
     * @return 结果
     */
    int updateRsOpExportCustomsClearance(RsOpExportCustomsClearance rsOpExportCustomsClearance);

    int updateRsOpExportCustomsClearance(RsOpDocDeclare rsOpDocDeclare);

    int updateRsOpExportCustomsClearance(RsOpFreeDeclare rsOpFreeDeclare);

    /**
     * 删除出口报关服务
     *
     * @param exportCustomsClearanceId 出口报关服务主键
     * @return 结果
     */
    int deleteRsOpExportCustomsClearanceByExportCustomsClearanceId(Long exportCustomsClearanceId);

    /**
     * 批量删除出口报关服务
     *
     * @param exportCustomsClearanceIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsOpExportCustomsClearanceByExportCustomsClearanceIds(Long[] exportCustomsClearanceIds);

    RsOpExportCustomsClearance selectRsOpExportCustomsClearanceByRctId(Long rctId);

    List<RsOpDocDeclare> selectRsOpDocDeclareByRctId(@Param("rctId") Long rctId, @Param("sqdServiceTypeId") Long sqdServiceTypeId);

    List<RsOpFreeDeclare> selectRsOpFreeDeclareByRctId(@Param("rctId") Long rctId, @Param("sqdServiceTypeId") Long sqdServiceTypeId);

    void deleteRsOpFreeDeclareByRctIdAndServiceTypeId(@Param("rctId") Long rctId, @Param("sqdServiceTypeId") Long sqdServiceTypeId);

    void upsertRsOpExportCustomsClearance(RsOpFreeDeclare rsOpFreeDeclare);

    void upsertRsOpExportCustomsClearance(RsOpDocDeclare rsOpDocDeclare);
}
