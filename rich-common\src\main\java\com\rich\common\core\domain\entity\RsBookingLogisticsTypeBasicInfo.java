package com.rich.common.core.domain.entity;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

/**
 * 订舱单基础物流信息对象 rs_booking_logistics_type_basic_info
 * 
 * <AUTHOR>
 * @date 2023-08-15
 */
public class RsBookingLogisticsTypeBasicInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**
     * 基础物流信息
     */
    private Long logisticsTypeInfoId;

    /**
     * 订舱申请表
     */
    @Excel(name = "订舱申请表")
    private Long bookingId;

    private Long typeId;

    /**
     * 服务类型
     */
    @Excel(name = "服务类型")
    private Long logisticsTypeId;

    /**
     * 承运人
     */
    @Excel(name = "承运人")
    private Long carrierId;

    /**
     * 启运港
     */
    @Excel(name = "启运港")
    private Long polId;

    /** 头程截关 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "头程截关", width = 30, dateFormat = "yyyy-MM-dd")
    private Date firstCvClosingTime;

    /** 头程开舱 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "头程开舱", width = 30, dateFormat = "yyyy-MM-dd")
    private Date firstCyOpenTime;

    /** 头程截重 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "头程截重", width = 30, dateFormat = "yyyy-MM-dd")
    private Date firstCyClosingTime;

    /** 头程装船 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "头程装船", width = 30, dateFormat = "yyyy-MM-dd")
    private Date firstEtd;

    /** 头程船名 */
    @Excel(name = "头程船名")
    private String firstVessel;

    /** 头程航次 */
    @Excel(name = "头程航次")
    private String firstVoyage;

    /** 境内基港 */
    @Excel(name = "境内基港")
    private Long localBasicPortId;

    /** 基港截关 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "基港截关", width = 30, dateFormat = "yyyy-MM-dd")
    private Date basicClosingTime;

    /** 基港截重 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "基港截重", width = 30, dateFormat = "yyyy-MM-dd")
    private Date basicFinalGateinTime;

    /** 基港装船 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "基港装船", width = 30, dateFormat = "yyyy-MM-dd")
    private Date basicEtd;

    /** 基港船名 */
    @Excel(name = "基港船名")
    private String basicVessel;

    /** 基港航次 */
    @Excel(name = "基港航次")
    private String basicVoyage;

    /** 中转港 */
    @Excel(name = "中转港")
    private Long transitPortId;

    /** 卸货港 */
    @Excel(name = "卸货港")
    private Long podId;

    /** 卸货港到达 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "卸货港到达", width = 30, dateFormat = "yyyy-MM-dd")
    private Date podEta;

    /** 目的港 */
    @Excel(name = "目的港")
    private Long destinationPortId;

    /** 目的港到达 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "目的港到达", width = 30, dateFormat = "yyyy-MM-dd")
    private Date destinationPortEta;

    /** 操作审批 */
    @Excel(name = "操作审批")
    private String opConfirmed;

    /** 确认人ID */
    @Excel(name = "确认人ID")
    private Long opConfirmedId;

    /** 操作审批时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "操作审批时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date opConfirmedDate;

    /** 财务审批 */
    @Excel(name = "财务审批")
    private String financeConfirmed;

    /** 确认人ID */
    @Excel(name = "确认人ID")
    private Long financeConfirmedId;

    /** 财务审批时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "财务审批时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date financeConfirmedDate;

    /** 业务审批 */
    @Excel(name = "业务审批")
    private String salesConfirmed;

    /** 确认人ID */
    @Excel(name = "确认人ID")
    private Long salesConfirmedId;

    /** 业务审批时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "业务审批时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date salesConfirmedDate;

    /** 供应商审批 */
    @Excel(name = "供应商审批")
    private String supplierConfirmed;

    /** 确认人ID */
    @Excel(name = "确认人ID")
    private Long supplierConfirmedId;

    /**
     * 供应商审批时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "供应商审批时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date supplierConfirmedDate;

    /**
     * 发票查询编号
     */
    @Excel(name = "发票查询编号")
    private String invoiceQueryNo;

    public Long getTypeId() {
        return typeId;
    }

    public void setTypeId(Long typeId) {
        this.typeId = typeId;
    }

    private List<RsBookingReceivablePayable> rsBookingReceivablePayableList;

    public List<RsBookingReceivablePayable> getRsBookingReceivablePayableList() {
        return rsBookingReceivablePayableList;
    }

    public void setRsBookingReceivablePayableList(List<RsBookingReceivablePayable> rsBookingReceivablePayableList) {
        this.rsBookingReceivablePayableList = rsBookingReceivablePayableList;
    }

    public void setLogisticsTypeInfoId(Long logisticsTypeInfoId)
    {
        this.logisticsTypeInfoId = logisticsTypeInfoId;
    }

    public Long getLogisticsTypeInfoId() 
    {
        return logisticsTypeInfoId;
    }
    public void setBookingId(Long bookingId) 
    {
        this.bookingId = bookingId;
    }

    public Long getBookingId() 
    {
        return bookingId;
    }
    public void setLogisticsTypeId(Long logisticsTypeId) 
    {
        this.logisticsTypeId = logisticsTypeId;
    }

    public Long getLogisticsTypeId() 
    {
        return logisticsTypeId;
    }
    public void setCarrierId(Long carrierId) 
    {
        this.carrierId = carrierId;
    }

    public Long getCarrierId() 
    {
        return carrierId;
    }
    public void setPolId(Long polId) 
    {
        this.polId = polId;
    }

    public Long getPolId() 
    {
        return polId;
    }
    public void setFirstCvClosingTime(Date firstCvClosingTime) 
    {
        this.firstCvClosingTime = firstCvClosingTime;
    }

    public Date getFirstCvClosingTime() 
    {
        return firstCvClosingTime;
    }
    public void setFirstCyOpenTime(Date firstCyOpenTime) 
    {
        this.firstCyOpenTime = firstCyOpenTime;
    }

    public Date getFirstCyOpenTime() 
    {
        return firstCyOpenTime;
    }
    public void setFirstCyClosingTime(Date firstCyClosingTime) 
    {
        this.firstCyClosingTime = firstCyClosingTime;
    }

    public Date getFirstCyClosingTime() 
    {
        return firstCyClosingTime;
    }
    public void setFirstEtd(Date firstEtd) 
    {
        this.firstEtd = firstEtd;
    }

    public Date getFirstEtd() 
    {
        return firstEtd;
    }
    public void setFirstVessel(String firstVessel) 
    {
        this.firstVessel = firstVessel;
    }

    public String getFirstVessel() 
    {
        return firstVessel;
    }
    public void setFirstVoyage(String firstVoyage) 
    {
        this.firstVoyage = firstVoyage;
    }

    public String getFirstVoyage() 
    {
        return firstVoyage;
    }
    public void setLocalBasicPortId(Long localBasicPortId) 
    {
        this.localBasicPortId = localBasicPortId;
    }

    public Long getLocalBasicPortId() 
    {
        return localBasicPortId;
    }
    public void setBasicClosingTime(Date basicClosingTime) 
    {
        this.basicClosingTime = basicClosingTime;
    }

    public Date getBasicClosingTime() 
    {
        return basicClosingTime;
    }
    public void setBasicFinalGateinTime(Date basicFinalGateinTime) 
    {
        this.basicFinalGateinTime = basicFinalGateinTime;
    }

    public Date getBasicFinalGateinTime() 
    {
        return basicFinalGateinTime;
    }
    public void setBasicEtd(Date basicEtd) 
    {
        this.basicEtd = basicEtd;
    }

    public Date getBasicEtd() 
    {
        return basicEtd;
    }
    public void setBasicVessel(String basicVessel) 
    {
        this.basicVessel = basicVessel;
    }

    public String getBasicVessel() 
    {
        return basicVessel;
    }
    public void setBasicVoyage(String basicVoyage) 
    {
        this.basicVoyage = basicVoyage;
    }

    public String getBasicVoyage() 
    {
        return basicVoyage;
    }
    public void setTransitPortId(Long transitPortId) 
    {
        this.transitPortId = transitPortId;
    }

    public Long getTransitPortId() 
    {
        return transitPortId;
    }
    public void setPodId(Long podId) 
    {
        this.podId = podId;
    }

    public Long getPodId() 
    {
        return podId;
    }
    public void setPodEta(Date podEta) 
    {
        this.podEta = podEta;
    }

    public Date getPodEta() 
    {
        return podEta;
    }
    public void setDestinationPortId(Long destinationPortId) 
    {
        this.destinationPortId = destinationPortId;
    }

    public Long getDestinationPortId() 
    {
        return destinationPortId;
    }
    public void setDestinationPortEta(Date destinationPortEta) 
    {
        this.destinationPortEta = destinationPortEta;
    }

    public Date getDestinationPortEta() 
    {
        return destinationPortEta;
    }
    public void setOpConfirmed(String opConfirmed) 
    {
        this.opConfirmed = opConfirmed;
    }

    public String getOpConfirmed() 
    {
        return opConfirmed;
    }
    public void setOpConfirmedId(Long opConfirmedId) 
    {
        this.opConfirmedId = opConfirmedId;
    }

    public Long getOpConfirmedId() 
    {
        return opConfirmedId;
    }
    public void setOpConfirmedDate(Date opConfirmedDate) 
    {
        this.opConfirmedDate = opConfirmedDate;
    }

    public Date getOpConfirmedDate() 
    {
        return opConfirmedDate;
    }
    public void setFinanceConfirmed(String financeConfirmed) 
    {
        this.financeConfirmed = financeConfirmed;
    }

    public String getFinanceConfirmed() 
    {
        return financeConfirmed;
    }
    public void setFinanceConfirmedId(Long financeConfirmedId) 
    {
        this.financeConfirmedId = financeConfirmedId;
    }

    public Long getFinanceConfirmedId() 
    {
        return financeConfirmedId;
    }
    public void setFinanceConfirmedDate(Date financeConfirmedDate) 
    {
        this.financeConfirmedDate = financeConfirmedDate;
    }

    public Date getFinanceConfirmedDate() 
    {
        return financeConfirmedDate;
    }
    public void setSalesConfirmed(String salesConfirmed) 
    {
        this.salesConfirmed = salesConfirmed;
    }

    public String getSalesConfirmed() 
    {
        return salesConfirmed;
    }
    public void setSalesConfirmedId(Long salesConfirmedId) 
    {
        this.salesConfirmedId = salesConfirmedId;
    }

    public Long getSalesConfirmedId() 
    {
        return salesConfirmedId;
    }
    public void setSalesConfirmedDate(Date salesConfirmedDate) 
    {
        this.salesConfirmedDate = salesConfirmedDate;
    }

    public Date getSalesConfirmedDate() 
    {
        return salesConfirmedDate;
    }
    public void setSupplierConfirmed(String supplierConfirmed) 
    {
        this.supplierConfirmed = supplierConfirmed;
    }

    public String getSupplierConfirmed() 
    {
        return supplierConfirmed;
    }
    public void setSupplierConfirmedId(Long supplierConfirmedId) 
    {
        this.supplierConfirmedId = supplierConfirmedId;
    }

    public Long getSupplierConfirmedId() 
    {
        return supplierConfirmedId;
    }
    public void setSupplierConfirmedDate(Date supplierConfirmedDate) 
    {
        this.supplierConfirmedDate = supplierConfirmedDate;
    }

    public Date getSupplierConfirmedDate() 
    {
        return supplierConfirmedDate;
    }
    public void setInvoiceQueryNo(String invoiceQueryNo) 
    {
        this.invoiceQueryNo = invoiceQueryNo;
    }

    public String getInvoiceQueryNo() 
    {
        return invoiceQueryNo;
    }

}
