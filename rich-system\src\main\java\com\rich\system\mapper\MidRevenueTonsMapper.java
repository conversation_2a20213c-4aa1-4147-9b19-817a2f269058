package com.rich.system.mapper;

import com.rich.common.core.domain.entity.MidRevenueTons;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 货量Mapper接口
 *
 * <AUTHOR>
 * @date 2023-05-04
 */
@Mapper
public interface MidRevenueTonsMapper {
    /**
     * 查询货量
     *
     * @param quotationId 货量主键
     * @return 货量
     */
    List<MidRevenueTons> selectMidRevenueTonsByQuotationId(Long quotationId);

    /**
     * 查询货量列表
     *
     * @param midRevenueTon 货量
     * @return 货量集合
     */
    List<MidRevenueTons> selectMidRevenueTonsList(MidRevenueTons midRevenueTon);

    /**
     * 新增货量
     *
     * @param midRevenueTons 货量
     * @return 结果
     */
    int insertMidRevenueTons(MidRevenueTons midRevenueTons);

    /**
     * 修改货量
     *
     * @param midRevenueTons 货量
     * @return 结果
     */
    int updateMidRevenueTons(MidRevenueTons midRevenueTons);

    /**
     * 删除货量
     *
     * @param quotationId 货量主键
     * @return 结果
     */
    int deleteMidRevenueTonsByQuotationId(Long quotationId);

    /**
     * 批量删除货量
     *
     * @param quotationIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteMidRevenueTonsByQuotationIds(Long[] quotationIds);
}
