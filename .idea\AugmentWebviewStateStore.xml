<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="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" />
      </map>
    </option>
  </component>
</project>