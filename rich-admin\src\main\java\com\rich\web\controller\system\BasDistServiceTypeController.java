package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasDistServiceType;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasDistServiceTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 服务类型Controller
 *
 * <AUTHOR>
 * @date 2022-08-29
 */
@RestController
@RequestMapping("/system/servicetype")
public class BasDistServiceTypeController extends BaseController {

    @Autowired
    private  BasDistServiceTypeService basDistServiceTypeService;


    @Autowired
    private  RedisCache redisCache;

    /**
     * 查询服务类型列表
     */
    @PreAuthorize("@ss.hasPermi('system:servicetype:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasDistServiceType basDistServiceType) {
        List<BasDistServiceType> list = basDistServiceTypeService.selectBasDistServiceTypeList(basDistServiceType);
        return getDataTable(list);
    }

    @GetMapping("/selectList")
    public AjaxResult selectList(BasDistServiceType basDistServiceType) {
        List<BasDistServiceType> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
        if (list == null) {
            basDistServiceType.setStatus("0");
            list = basDistServiceTypeService.selectBasDistServiceTypeList(basDistServiceType);
            redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
            redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "serviceType", list);
        }
        return AjaxResult.success(AjaxResult.DATA_TAG, list);
    }

    /**
     * 导出服务类型列表
     */
    @PreAuthorize("@ss.hasPermi('system:servicetype:export')")
    @Log(title = "服务类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasDistServiceType basDistServiceType) {
        List<BasDistServiceType> list = basDistServiceTypeService.selectBasDistServiceTypeList(basDistServiceType);
        ExcelUtil<BasDistServiceType> util = new ExcelUtil<BasDistServiceType>(BasDistServiceType.class);
        util.exportExcel(response, list, "服务类型数据");
    }

    /**
     * 获取服务类型详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:servicetype:edit')")
    @GetMapping(value = "/{serviceTypeId}")
    public AjaxResult getInfo(@PathVariable("serviceTypeId") Long serviceTypeId) {
        AjaxResult ajaxResult = AjaxResult.success();
        ajaxResult.put(AjaxResult.DATA_TAG, basDistServiceTypeService.selectBasDistServiceTypeByServiceTypeId(serviceTypeId));
        ajaxResult.put("carrierIds", basDistServiceTypeService.getServiceTypeCarrier(serviceTypeId));
        return ajaxResult;
    }

    /**
     * 新增服务类型
     */
    @PreAuthorize("@ss.hasPermi('system:servicetype:add')")
    @Log(title = "服务类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasDistServiceType basDistServiceType) {
        int out = basDistServiceTypeService.insertBasDistServiceType(basDistServiceType);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
        return toAjax(out);
    }

    /**
     * 修改服务类型
     */
    @PreAuthorize("@ss.hasPermi('system:servicetype:edit')")
    @Log(title = "服务类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasDistServiceType basDistServiceType) {
        int out = basDistServiceTypeService.updateBasDistServiceType(basDistServiceType);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
        return toAjax(out);
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('system:servicetype:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasDistServiceType basDistServiceType) {
        basDistServiceType.setUpdateBy(getUserId());
        basDistServiceTypeService.changeBasDistServiceTypeStatus(basDistServiceType);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
        return AjaxResult.success();
    }

    /**
     * 删除服务类型
     */
    @PreAuthorize("@ss.hasPermi('system:servicetype:remove')")
    @Log(title = "服务类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{serviceTypeIds}")
    public AjaxResult remove(@PathVariable Long[] serviceTypeIds) {
        basDistServiceTypeService.deleteBasDistServiceTypeByServiceTypeIds(serviceTypeIds);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
        return AjaxResult.success();
    }
}
