package com.rich.common.constant;

/**
 * 缓存的key 常量
 *
 * <AUTHOR>
 */
public class CacheConstants
{
    /**
     * 登录用户 redis key
     */
    public static final String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 防重提交 redis key
     */
    public static final String REPEAT_SUBMIT_KEY = "repeat_submit:";

    /**
     * 限流 redis key
     */
    public static final String RATE_LIMIT_KEY = "rate_limit:";

    /**
     * 登录账户密码错误次数 redis key
     */
    public static final String PWD_ERR_CNT_KEY = "pwd_err_cnt:";

    /**
     * 数据缓存 redis key
     */
    public static final String DATA_CACHE_KEY = "data_cache:";

    /**
     * 隐藏指针缓存 redis key
     */
    public static final String MID_CACHE_KEY = "mid_cache:";

    /**
     * 微信扫码登录缓存 redis key
     */
    public static final String WECHAT_SCAN_LOGIN_KEY = "wechat_scan_login:";

    /**
     * 用户当日首次登录标记 redis key
     */
    public static final String USER_FIRST_LOGIN_KEY = "user_first_login:";
}
