package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasEmergencyLevel;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasEmergencyLevelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/23 17:52
 * @Version 1.0
 */
@RestController
@RequestMapping("/system/emergencylevel")
public class BasEmergencyLevelController extends BaseController {
    @Autowired
    private BasEmergencyLevelService basEmergencyLevelService;

    /**
     * 查询紧急程度列表
     */
    @GetMapping("/list")
    public TableDataInfo list(BasEmergencyLevel basEmergencyLevel) {
        startPage();
        List<BasEmergencyLevel> list = basEmergencyLevelService.selectBasEmergencyLevelList(basEmergencyLevel);
        return getDataTable(list);
    }

    /**
     * 导出紧急程度列表
     */
    @PreAuthorize("@ss.hasPermi('system:emergencylevel:export')")
    @Log(title = "紧急程度", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasEmergencyLevel basEmergencyLevel) {
        List<BasEmergencyLevel> list = basEmergencyLevelService.selectBasEmergencyLevelList(basEmergencyLevel);
        ExcelUtil<BasEmergencyLevel> util = new ExcelUtil<BasEmergencyLevel>(BasEmergencyLevel.class);
        util.exportExcel(response, list, "紧急程度数据");
    }

    /**
     * 获取紧急程度详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:emergencylevel:query')")
    @GetMapping(value = "/{emergencyLevelId}")
    public AjaxResult getInfo(@PathVariable("emergencyLevelId") Long emergencyLevelId) {
        return AjaxResult.success(basEmergencyLevelService.selectBasEmergencyLevelByEmergencyLevelId(emergencyLevelId));
    }

    /**
     * 新增紧急程度
     */
    @PreAuthorize("@ss.hasPermi('system:emergencylevel:add')")
    @Log(title = "紧急程度", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasEmergencyLevel basEmergencyLevel) {
        return toAjax(basEmergencyLevelService.insertBasEmergencyLevel(basEmergencyLevel));
    }

    /**
     * 修改紧急程度
     */
    @PreAuthorize("@ss.hasPermi('system:emergencylevel:edit')")
    @Log(title = "紧急程度", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasEmergencyLevel basEmergencyLevel) {
        return toAjax(basEmergencyLevelService.updateBasEmergencyLevel(basEmergencyLevel));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:emergencylevel:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasEmergencyLevel basEmergencyLevel) {
        basEmergencyLevel.setUpdateBy(getUserId());
        return toAjax(basEmergencyLevelService.changeStatus(basEmergencyLevel));
    }

    /**
     * 删除紧急程度
     */
    @PreAuthorize("@ss.hasPermi('system:emergencylevel:remove')")
    @Log(title = "紧急程度", businessType = BusinessType.DELETE)
    @DeleteMapping("/{emergencyLevelIds}")
    public AjaxResult remove(@PathVariable Long[] emergencyLevelIds) {
        return toAjax(basEmergencyLevelService.deleteBasEmergencyLevelByEmergencyLevelIds(emergencyLevelIds));
    }
}
