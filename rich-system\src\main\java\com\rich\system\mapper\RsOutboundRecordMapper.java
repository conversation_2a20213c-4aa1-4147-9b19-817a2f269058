package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsOutboundRecord;
import org.apache.ibatis.annotations.Mapper;

/**
 * 出仓记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-11
 */
@Mapper
public interface RsOutboundRecordMapper {
    /**
     * 查询出仓记录
     *
     * @param outboundRecordId 出仓记录主键
     * @return 出仓记录
     */
    RsOutboundRecord selectRsOutboundRecordByOutboundRecordId(Long outboundRecordId);

    /**
     * 查询出仓记录列表
     *
     * @param rsOutboundRecord 出仓记录
     * @return 出仓记录集合
     */
    List<RsOutboundRecord> selectRsOutboundRecordList(RsOutboundRecord rsOutboundRecord);

    /**
     * 新增出仓记录
     *
     * @param rsOutboundRecord 出仓记录
     * @return 结果
     */
    int insertRsOutboundRecord(RsOutboundRecord rsOutboundRecord);

    /**
     * 修改出仓记录
     *
     * @param rsOutboundRecord 出仓记录
     * @return 结果
     */
    int updateRsOutboundRecord(RsOutboundRecord rsOutboundRecord);

    /**
     * 删除出仓记录
     *
     * @param outboundRecordId 出仓记录主键
     * @return 结果
     */
    int deleteRsOutboundRecordByOutboundRecordId(Long outboundRecordId);

    /**
     * 批量删除出仓记录
     *
     * @param outboundRecordIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsOutboundRecordByOutboundRecordIds(Long[] outboundRecordIds);

    RsOutboundRecord selectRsOutboundRecordsByOutboundRecordId(Long outboundRecordId);

    List<RsOutboundRecord> selectRentalRecordList(RsOutboundRecord rsOutboundRecord);

    RsOutboundRecord selectRentalsByOutboundRecordId(Long outboundRecordId);

    RsOutboundRecord selectRsOutboundRecordByRctId(Long rctId);
}
