package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasDifficultyLevel;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasDifficultyLevelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/23 17:50
 * @Version 1.0
 */
@RestController
@RequestMapping("/system/difficultylevel")
public class BasDifficultyLevelController extends BaseController {
    @Autowired
    private BasDifficultyLevelService basDifficultyLevelService;

    /**
     * 查询订单难度列表
     */
    @GetMapping("/list")
    public TableDataInfo list(BasDifficultyLevel basDifficultyLevel) {
        startPage();
        List<BasDifficultyLevel> list = basDifficultyLevelService.selectBasDifficultyLevelList(basDifficultyLevel);
        return getDataTable(list);
    }

    /**
     * 导出订单难度列表
     */
    @PreAuthorize("@ss.hasPermi('system:difficultylevel:export')")
    @Log(title = "订单难度", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasDifficultyLevel basDifficultyLevel) {
        List<BasDifficultyLevel> list = basDifficultyLevelService.selectBasDifficultyLevelList(basDifficultyLevel);
        ExcelUtil<BasDifficultyLevel> util = new ExcelUtil<BasDifficultyLevel>(BasDifficultyLevel.class);
        util.exportExcel(response, list, "订单难度数据");
    }

    /**
     * 获取订单难度详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:difficultylevel:query')")
    @GetMapping(value = "/{difficultyLevelId}")
    public AjaxResult getInfo(@PathVariable("difficultyLevelId") Long difficultyLevelId) {
        return AjaxResult.success(basDifficultyLevelService.selectBasDifficultyLevelByDifficultyLevelId(difficultyLevelId));
    }

    /**
     * 新增订单难度
     */
    @PreAuthorize("@ss.hasPermi('system:difficultylevel:add')")
    @Log(title = "订单难度", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasDifficultyLevel basDifficultyLevel) {
        return toAjax(basDifficultyLevelService.insertBasDifficultyLevel(basDifficultyLevel));
    }

    /**
     * 修改订单难度
     */
    @PreAuthorize("@ss.hasPermi('system:difficultylevel:edit')")
    @Log(title = "订单难度", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasDifficultyLevel basDifficultyLevel) {
        return toAjax(basDifficultyLevelService.updateBasDifficultyLevel(basDifficultyLevel));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:difficultylevel:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasDifficultyLevel basDifficultyLevel) {
        basDifficultyLevel.setUpdateBy(getUserId());
        return toAjax(basDifficultyLevelService.changeStatus(basDifficultyLevel));
    }

    /**
     * 删除订单难度
     */
    @PreAuthorize("@ss.hasPermi('system:difficultylevel:remove')")
    @Log(title = "订单难度", businessType = BusinessType.DELETE)
    @DeleteMapping("/{difficultyLevelIds}")
    public AjaxResult remove(@PathVariable Long[] difficultyLevelIds) {
        return toAjax(basDifficultyLevelService.deleteBasDifficultyLevelByDifficultyLevelIds(difficultyLevelIds));
    }
}
