package com.rich.system.mapper;


import com.rich.common.core.domain.entity.BasCurrency;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 币种Mapper接口
 *
 * <AUTHOR>
 * @date 2022-09-06
 */
@Mapper
public interface BasCurrencyMapper {
    /**
     * 查询币种
     *
     * @param currencyId 币种主键
     * @return 币种
     */
    BasCurrency selectBasCurrencyByCurrencyId(Long currencyId);

    /**
     * 查询币种列表
     *
     * @param basCurrency 币种
     * @return 币种集合
     */
    List<BasCurrency> selectBasCurrencyList(BasCurrency basCurrency);

    /**
     * 新增币种
     *
     * @param basCurrency 币种
     * @return 结果
     */
    int insertBasCurrency(BasCurrency basCurrency);

    /**
     * 修改币种
     *
     * @param basCurrency 币种
     * @return 结果
     */
    int updateBasCurrency(BasCurrency basCurrency);

    /**
     * 删除币种
     *
     * @param currencyId 币种主键
     * @return 结果
     */
    int deleteBasCurrencyByCurrencyId(Long currencyId);

    /**
     * 批量删除币种
     *
     * @param currencyIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasCurrencyByCurrencyIds(Long[] currencyIds);
}
