package com.rich.web.controller.system;

import java.util.*;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.MpUser;
import com.rich.common.core.domain.entity.RsStaff;
import com.rich.common.core.domain.model.LoginUser;
import com.rich.common.utils.StringUtils;
import com.rich.common.utils.uuid.IdUtils;
import com.rich.framework.web.service.TokenService;
import com.rich.system.service.MpUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.http.ResponseEntity;

/**
 * 用户信息Controller
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@RestController
@RequestMapping("/mp/user")
public class MpUserController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(MpUserController.class);

    @Autowired
    private MpUserService mpUserService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private RestTemplate restTemplate;

    @Value("${mp.app-id}")
    private String appId;

    @Value("${mp.app-secret}")
    private String appSecret;

    /**
     * 查询用户信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MpUser mpUser) {
        startPage();
        List<MpUser> list = mpUserService.selectMpUserList(mpUser);
        return getDataTable(list);
    }

    /**
     * 导出用户信息列表
     */
    @Log(title = "用户信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MpUser mpUser) {
        List<MpUser> list = mpUserService.selectMpUserList(mpUser);
        ExcelUtil<MpUser> util = new ExcelUtil<MpUser>(MpUser.class);
        util.exportExcel(response, list, "用户信息数据");
    }

    /**
     * 获取用户信息详细信息
     */
    @GetMapping(value = "/{userId}")
    public AjaxResult getInfo(@PathVariable("userId") Long userId) {
        return AjaxResult.success(mpUserService.selectMpUserByUserId(userId));
    }

    /**
     * 新增用户信息
     */
    @Log(title = "用户信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MpUser mpUser) {
        return toAjax(mpUserService.insertMpUser(mpUser));
    }

    /**
     * 修改用户信息
     */
    @Log(title = "用户信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MpUser mpUser) {
        return toAjax(mpUserService.updateMpUser(mpUser));
    }

    /**
     * 状态状态
     */
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody MpUser mpUser) {
        mpUser.setUpdateBy(getUserId());
        return toAjax(mpUserService.changeStatus(mpUser));
    }

    /**
     * 删除用户信息
     */
    @Log(title = "用户信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable Long[] userIds) {
        return toAjax(mpUserService.deleteMpUserByUserIds(userIds));
    }

    /**
     * 小程序用户登录
     * 使用微信授权码获取openid进行登录或自动注册
     *
     * @param params 请求参数，包含微信code和用户信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody Map<String, Object> params) {
        String code = (String) params.get("code");
        Boolean autoRegister = (Boolean) params.get("autoRegister");

        if (code == null || code.isEmpty()) {
            return AjaxResult.error("微信授权码不能为空");
        }

        try {
            // 调用微信API获取openId和sessionKey
            Map<String, Object> wxLoginInfo = getWxLoginInfo(code);
            String openId = (String) wxLoginInfo.get("openid");
            String sessionKey = (String) wxLoginInfo.get("session_key");

            if (openId == null || openId.isEmpty()) {
                return AjaxResult.error("获取微信用户信息失败");
            }

            // 根据openId查询用户
            MpUser mpUser = new MpUser();
            mpUser.setWechatId(openId);
            List<MpUser> userList = mpUserService.selectMpUserList(mpUser);

            MpUser loginUser;
            if (userList.isEmpty()) {
                // 用户不存在，根据autoRegister决定是否自动注册
                if (autoRegister != null && autoRegister) {
                    // 自动注册新用户
                    loginUser = new MpUser();
                    loginUser.setWechatId(openId);

                    loginUser.setFullName(generateDefaultNickname());

                    // 设置默认角色
                    loginUser.setRole("user");

                    // 插入新用户
                    mpUserService.insertMpUser(loginUser);

                    log.info("自动注册新用户成功，openId: {}, 昵称: {}", openId, loginUser.getFullName());
                } else {
                    // 不自动注册
                    return AjaxResult.error("用户未注册");
                }
            } else {
                // 用户已存在
                loginUser = userList.get(0);
            }

            // 生成登录令牌
            Map<String, Object> tokenInfo = createLoginToken(loginUser);

            return AjaxResult.success("登录成功", tokenInfo);
        } catch (Exception e) {
            log.error("微信登录失败", e);
            return AjaxResult.error("登录失败: " + e.getMessage());
        }
    }

    /**
     * 更新小程序用户信息
     * 用于小程序获取用户信息后更新到服务端
     *
     * @param userInfo 用户信息
     * @return 结果
     */
    @PostMapping("/updateInfo")
    public AjaxResult updateUserInfo(@RequestBody MpUser userInfo) {
        Long userId = getUserId();
        if (userId == null) {
            return AjaxResult.error("未登录");
        }

        // 获取当前用户
        MpUser currentUser = mpUserService.selectMpUserByUserId(userId);
        if (currentUser == null) {
            return AjaxResult.error("用户不存在");
        }

        // 只允许更新部分字段，不允许修改关键信息如wechatId
        if (userInfo.getFullName() != null) {
            currentUser.setFullName(userInfo.getFullName());
        }
        if (userInfo.getAvatarUrl() != null) {
            currentUser.setAvatarUrl(userInfo.getAvatarUrl());
        }
        if (userInfo.getPhoneNumber() != null) {
            currentUser.setPhoneNumber(userInfo.getPhoneNumber());
        }

        // 更新用户信息
        return toAjax(mpUserService.updateMpUser(currentUser));
    }

    /**
     * 获取当前登录用户信息
     *
     * @return 结果
     */
    @GetMapping("/info")
    public AjaxResult getUserInfo() {
        Long userId = getUserId();
        if (userId == null) {
            return AjaxResult.error("未登录");
        }

        MpUser mpUser = mpUserService.selectMpUserByUserId(userId);
        mpUser.setRoles(Collections.singletonList(mpUser.getRole()));
        if (mpUser == null) {
            return AjaxResult.error("用户不存在");
        }

        return AjaxResult.success(mpUser);
    }

    /**
     * 通过微信Code获取OpenId和SessionKey
     * 调用微信小程序官方接口获取用户openid和session_key
     *
     * @param code 微信授权码
     * @return 结果，包含openid和session_key
     */
    private Map<String, Object> getWxLoginInfo(String code) {
        // 微信小程序登录接口URL
        String url = "https://api.weixin.qq.com/sns/jscode2session";

        // 构建请求参数
        StringBuilder params = new StringBuilder();
        params.append("appid=").append(appId);
        params.append("&secret=").append(appSecret);
        params.append("&js_code=").append(code);
        params.append("&grant_type=authorization_code");

        // 完整的请求URL
        String requestUrl = url + "?" + params.toString();

        try {
            // 修改：直接获取响应为Map类型而不是String
            ResponseEntity<Map> responseEntity = restTemplate.exchange(
                    requestUrl,
                    org.springframework.http.HttpMethod.GET,
                    null,
                    Map.class);

            Map<String, Object> jsonObject = responseEntity.getBody();
            if (jsonObject == null) {
                throw new RuntimeException("微信服务器返回空响应");
            }

            log.info("微信登录接口响应: {}", jsonObject);

            // 检查是否有错误
            if (jsonObject.containsKey("errcode") && !Integer.valueOf(0).equals(jsonObject.get("errcode"))) {
                log.error("微信登录接口返回错误: {}", jsonObject.get("errmsg"));
                throw new RuntimeException("微信授权失败: " + jsonObject.get("errmsg"));
            }

            // 提取openid和session_key
            String openid = (String) jsonObject.get("openid");
            String sessionKey = (String) jsonObject.get("session_key");

            if (StringUtils.isEmpty(openid)) {
                log.error("微信登录接口未返回openid");
                throw new RuntimeException("获取微信用户标识失败");
            }

            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("openid", openid);
            result.put("session_key", sessionKey);

            return result;
        } catch (Exception e) {
            log.error("调用微信登录接口失败", e);
            throw new RuntimeException("微信授权接口调用失败: " + e.getMessage());
        }
    }

    /**
     * 创建登录Token
     *
     * @param mpUser 小程序用户信息
     * @return 结果
     */
    private Map<String, Object> createLoginToken(MpUser mpUser) {
        try {
            // 构建登录用户对象
            Set<String> permissions = new HashSet<>();
            permissions.add("mp:user"); // 添加基础权限

            // 创建RsStaff对象来适配LoginUser需要
            RsStaff rsStaff = new RsStaff();
            rsStaff.setStaffId(mpUser.getUserId());

            // 设置必要的用户名信息，避免空指针异常
            // 使用微信ID作为登录用户名，确保不为null
            String username = mpUser.getWechatId() != null ? mpUser.getWechatId() : "mp_user_" + mpUser.getUserId();
            rsStaff.setStaffUsername(username);

            // 确保密码字段不为null，因为在某些操作可能需要用到
            rsStaff.setStaffPassword("");

            // 设置openid
            rsStaff.setOpenid(mpUser.getWechatId());

            // 设置昵称相关字段
            String nickName = mpUser.getFullName();
            if (nickName == null || nickName.isEmpty()) {
                nickName = generateDefaultNickname();
            }
            rsStaff.setWxNickName(nickName);
            rsStaff.setStaffShortName(nickName);

            // 设置手机号(如果有)
            if (mpUser.getPhoneNumber() != null) {
                rsStaff.setStaffPhoneNum(mpUser.getPhoneNumber());
            }

            // 设置默认角色
            rsStaff.setRole(mpUser.getRole());

            // 创建LoginUser对象，使用正确的构造函数以确保user字段被正确设置
            LoginUser loginUser = new LoginUser(rsStaff, permissions);

            // 设置用户ID
            loginUser.setUserId(mpUser.getUserId());

            // 生成token
            String token = tokenService.createToken(loginUser);

            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            result.put("userId", mpUser.getUserId());

            return result;
        } catch (Exception e) {
            log.error("创建登录Token失败", e);
            throw new RuntimeException("登录失败：" + e.getMessage());
        }
    }

    /**
     * 生成默认昵称：rich + 5位随机字符
     */
    private String generateDefaultNickname() {
        // 生成随机字符串并截取前5位
        String randomStr = IdUtils.fastSimpleUUID().substring(0, 5);
        return "rich" + randomStr;
    }
}
