<template>
  <view class="container">
    <view class="form-header">
      <view class="header-left">
        <view class="title-container">
          <text class="header-title">收货人信息</text>
          <text class="header-subtitle">(Cargo Entry Management)</text>
        </view>
      </view>
      <view class="header-logo">LOGO</view>
    </view>

    <!-- 表单区域 -->
    <uni-forms ref="uForm" :model="formData" :rules="rules" class="edit" label-position="left">
      <view class="form-table">
        <view class="form-row">
          <view class="form-label">
            <text>目的地:</text>
            <text class="label-en">Destination</text>
          </view>
          <view class="form-text-value">
            <text>{{ mpWarehouseClient.clientRegion }}</text>
          </view>
        </view>

        <view class="form-row">
          <view class="form-label">
            <text>入仓代码:</text>
            <text class="label-en">Entry Code</text>
          </view>
          <view class="form-text-value">
            <text>{{ mpWarehouseClient.clientCode }}</text>
          </view>
        </view>

        <uni-forms-item class="form-item" name="consigneeCode">
          <template #label>
            <view class="form-label">
              <text>收货人代码:</text>
              <text class="label-en">Customer Code</text>
            </view>
          </template>
          <uni-easyinput v-model="formData.consigneeCode" :clearable="false" placeholder="请输入收货人代码"
                         @blur="checkConsigneeCodeExist"/>
        </uni-forms-item>

        <uni-forms-item class="form-item" name="consigneeName">
          <template #label>
            <view class="form-label">
              <text>收货人名称:</text>
              <text class="label-en">Customer Name</text>
            </view>
          </template>
          <uni-easyinput v-model="formData.consigneeName" :clearable="false" placeholder="请输入收货人名称"/>
        </uni-forms-item>

        <uni-forms-item class="form-item" name="consigneeTel">
          <template #label>
            <view class="form-label">
              <text>收货人电话:</text>
              <text class="label-en">Customer Tel</text>
            </view>
          </template>
          <uni-easyinput v-model="formData.consigneeTel" :clearable="false" placeholder="请输入联系电话"/>
        </uni-forms-item>

        <uni-forms-item class="form-item" name="remarks">
          <template #label>
            <view class="form-label">
              <text>收货人备注:</text>
              <text class="label-en">Remark</text>
            </view>
          </template>
          <uni-easyinput v-model="formData.remarks" :clearable="false" placeholder="请输入备注信息"/>
        </uni-forms-item>
      </view>
    </uni-forms>

    <!-- 二维码和操作按钮区域 - 左右布局 -->
    <view class="bottom-container">
      <!-- 左侧二维码区域 -->
      <view class="qr-code-section">
        <view class="qr-title">
          <text>{{ mpWarehouseClient.clientCode }} Warehouse</text>
          <text>Entry Instruction</text>
        </view>

        <view class="qr-info">
          <canvas bindlongtap="save" canvas-id="canvas" class="ewm"></canvas>
        </view>

        <view class="qr-customer">
          <text class="qr-customer-code">{{ mpWarehouseClient.clientCode }} - {{ formData.consigneeCode }}
          </text>
          <text class="qr-customer-name">{{ formData.consigneeName }}</text>
          <text class="qr-customer-address">Guangzhou - {{ mpWarehouseClient.clientRegion }}</text>
        </view>
      </view>

      <!-- 右侧按钮区域 -->
      <view class="action-buttons-section">
        <button v-if="!formData.consigneeId" :style="buttonStyle" class="action-btn add-btn uni-btn"
                @click="addConsignee">
          <view class="btn-content">
            <text class="btn-text">新增收货人</text>
            <text class="btn-text-en">Add New Customer</text>
          </view>
        </button>

        <button v-if="formData.consigneeId" :style="buttonStyle" class="action-btn update-btn uni-btn"
                @click="updateConsignee">
          <view class="btn-content">
            <text class="btn-text">更新收货人</text>
            <text class="btn-text-en">Update Customer</text>
          </view>
        </button>

        <button v-if="formData.consigneeId" :style="buttonStyle" class="action-btn create-btn uni-btn"
                @click="createBarcode">
          <view class="btn-content">
            <text class="btn-text">生成入仓码</text>
            <text class="btn-text-en">Create Client BQ</text>
          </view>
        </button>

        <button v-if="formData.consigneeId" :style="buttonStyle" class="action-btn save-btn uni-btn"
                @click="saveBarcode">
          <view class="btn-content">
            <text class="btn-text">保存入仓码</text>
            <text class="btn-text-en">Save Client BQ</text>
          </view>
        </button>

        <button v-if="formData.consigneeId" :style="buttonStyle" class="action-btn send-btn uni-btn"
                @click="sendBarcode">
          <view class="btn-content">
            <text class="btn-text">发送入仓码</text>
            <text class="btn-text-en">Send Client BQ</text>
          </view>
        </button>
      </view>
    </view>

    <!-- 注意事项和版权信息区域 -->
    <view class="remark-container">
      <view class="remark-content">
        <text class="remark-title">Remark:</text>
        <text class="remark-text">
          BarCode will record all the information you input, but only show Entry code and Customer code. You
          can
          update the customer's name and tel number, remark if needed.
        </text>
      </view>
      <view class="copyright-content">
        <text class="copyright-text">Copy Right © 2005 by RICH TECH.</text>
      </view>
    </view>

    <!-- 用于生成分享图的隐藏canvas -->
    <canvas canvas-id="shareCanvas" class="share-canvas"></canvas>
  </view>
</template>

<script>
import uniForms from '../components/uni-forms/uni-forms.vue'
import uniFormsItem from '../components/uni-forms-item/uni-forms-item.vue'
import uniEasyinput from '../components/uni-easyinput/uni-easyinput.vue'

import {addConsignee, checkConsigneeCode, getConsignee, updateConsignee} from '@/api/system/consignee'
import QRCode from '@/packageB/qrcode.js'
import {encrypt} from "../../utils/common";

export default {
  components: {
    uniForms,
    uniFormsItem,
    uniEasyinput
  },
  data() {
    return {
      mpWarehouseClient: this.$store.state.user.mpWarehouseClient,
      // 表单数据
      formData: {
        consigneeCode: '',
        consigneeName: '',
        consigneeTel: '',
        address: '',
        remarks: ''
      },
      // 表单校验规则
      rules: {
        consigneeCode: {
          rules: [{required: true, errorMessage: '请输入收货人代码'}]
        },
        consigneeName: {
          rules: [{required: true, errorMessage: '请输入收货人名称'}]
        },
        consigneeTel: {
          rules: [{required: true, errorMessage: '请输入联系电话'}]
        }
      },
      buttonStyle: {
        backgroundColor: '#e0ffe0',
        color: '#006600',
        border: '1rpx solid #99cc99',
        borderRadius: '6rpx',
        paddingTop: '6rpx',
        paddingBottom: '6rpx',
        height: '80rpx',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: '12rpx'
      },
      qrcode: null,
      qrcodeText: '',
      _fromOnLoad: false,
      shareCanvasWidth: 0,
      shareCanvasHeight: 0,
    }
  },
  onLoad(options) {
    if (options.consigneeId) {
      this.getDetail(options.consigneeId)
      uni.setNavigationBarTitle({
        title: '修改收货人'
      })
    } else {
      uni.setNavigationBarTitle({
        title: '新增收货人'
      })
    }
  },
  methods: {
    // 自定义toast方法
    showToast(message, type = 'none') {
      uni.showToast({
        title: message,
        icon: type === 'success' ? 'success' : 'none'
      });
    },
    getDetail(id) {
      uni.showLoading({
        title: '加载中...',
        mask: true
      });

      getConsignee(id).then(res => {
        // 隐藏加载提示
        uni.hideLoading();

        if (res.data) {
          // 更新表单数据
          this.formData = res.data;

          // 确保表单数据更新后再生成二维码
          if (this.formData.consigneeId) {
            this._fromOnLoad = true;
            // 使用nextTick确保DOM更新后再生成二维码
            this.$nextTick(() => {
              setTimeout(() => {
                this.createBarcode();
              }, 300);
            });
          }
        } else {
          this.showToast('获取收货人详情失败，数据为空');
        }
      }).catch((err) => {
        uni.hideLoading();
        console.error('获取收货人详情失败:', err);
        this.showToast('获取收货人详情失败');
      });
    },
    // 新增收货人
    addConsignee() {
      this.submitForm();
    },
    // 更新收货人
    updateConsignee() {
      this.submitForm();
    },
    // 保存入仓码
    saveBarcode() {
      uni.showLoading({
        title: '正在保存...',
        mask: true
      });

      // 确保二维码已经生成
      if (!this.formData.consigneeId) {
        uni.hideLoading();
        this.showToast('请先生成入仓码');
        return;
      }

      // #ifdef MP-WEIXIN
      // 微信小程序使用canvas绘制整个二维码区域
      const ctx = uni.createCanvasContext('shareCanvas', this);

      // 获取二维码区域的信息
      const query = uni.createSelectorQuery().in(this);
      query.select('.qr-code-section').boundingClientRect(data => {
        if (!data) {
          uni.hideLoading();
          this.showToast('获取二维码区域失败');
          return;
        }

        // 设置画布尺寸和背景色，保持与实际显示比例一致
        const canvasWidth = 240;  // 减小宽度
        const canvasHeight = 400; // 减小高度

        // 清空画布并设置背景色
        ctx.setFillStyle('#f8f8f8'); // 设置背景色与二维码区域相同
        ctx.fillRect(0, 0, canvasWidth, canvasHeight);

        // 绘制边框
        ctx.setStrokeStyle('#ddd');
        ctx.strokeRect(0, 0, canvasWidth, canvasHeight);

        // 绘制标题区域
        const titleAreaHeight = 60;
        ctx.setFillStyle('#f8f8f8');
        ctx.fillRect(0, 0, canvasWidth, titleAreaHeight);

        // 绘制标题文字
        ctx.setFontSize(14);
        ctx.setFillStyle('#003366');
        ctx.setTextAlign('center');
        ctx.setTextBaseline('middle');
        ctx.fillText(`${this.mpWarehouseClient.clientCode} Warehouse`, canvasWidth / 2, 20);
        ctx.fillText('Entry Instruction', canvasWidth / 2, 40);

        // 获取二维码图像
        uni.canvasToTempFilePath({
          canvasId: 'canvas',
          success: (qrRes) => {
            // 计算二维码在画布中的位置和大小
            const qrSize = 180; // 减小二维码尺寸
            const qrX = (canvasWidth - qrSize) / 2;
            const qrY = titleAreaHeight + 10;

            // 绘制二维码
            ctx.drawImage(qrRes.tempFilePath, qrX, qrY, qrSize, qrSize);

            // 绘制底部客户信息区域
            const infoY = qrY + qrSize + 20;

            // 绘制客户代码
            ctx.setFontSize(13);
            ctx.setFillStyle('#666');
            ctx.setTextAlign('center');
            ctx.fillText(`${this.mpWarehouseClient.clientCode} - ${this.formData.consigneeCode}`, canvasWidth / 2, infoY);

            // 绘制客户名称
            ctx.setFontSize(14);
            ctx.setFillStyle('#333');
            ctx.fillText(this.formData.consigneeName || '', canvasWidth / 2, infoY + 25);

            // 绘制地址信息
            ctx.setFontSize(12);
            ctx.setFillStyle('#999');
            ctx.fillText(`Guangzhou - ${this.mpWarehouseClient.clientRegion}`, canvasWidth / 2, infoY + 45);

            // 完成绘制
            ctx.draw(false, () => {
              // 将画布内容导出为图片
              setTimeout(() => {
                uni.canvasToTempFilePath({
                  canvasId: 'shareCanvas',
                  x: 0,
                  y: 0,
                  width: canvasWidth,
                  height: canvasHeight,
                  destWidth: canvasWidth * 3, // 提高清晰度
                  destHeight: canvasHeight * 3,
                  success: (result) => {
                    this.saveImageToAlbum(result.tempFilePath);
                  },
                  fail: (err) => {
                    uni.hideLoading();
                    console.error('生成图片失败:', err);
                    this.showToast('生成图片失败: ' + (err.errMsg || '未知错误'));
                  }
                }, this);
              }, 500); // 延长延迟时间，确保绘制完成
            });
          },
          fail: (err) => {
            uni.hideLoading();
            console.error('获取二维码失败:', err);
            this.showToast('获取二维码失败');
          }
        }, this);
      }).exec();
      // #endif
    },

    // 保存图片到相册
    saveImageToAlbum(filePath) {
      uni.saveImageToPhotosAlbum({
        filePath: filePath,
        success: () => {
          uni.hideLoading();
          this.showToast('入仓码已保存到相册', 'success');
        },
        fail: (err) => {
          uni.hideLoading();
          console.error('保存失败:', err);
          if (err.errMsg && err.errMsg.indexOf('auth deny') >= 0) {
            uni.showModal({
              title: '提示',
              content: '需要授权保存图片到相册',
              success: (res) => {
                if (res.confirm) {
                  uni.openSetting();
                }
              }
            });
          } else {
            this.showToast('保存失败: ' + (err.errMsg || '未知错误'));
          }
        }
      });
    },
    // 发送入仓码
    sendBarcode() {
      const that = this;
      this.drawEntireQrArea()

      setTimeout(() => {
        uni.canvasToTempFilePath({
          canvasId: 'shareCanvas',
          x: 0,
          y: 0,
          width: that.shareCanvasWidth,
          height: that.shareCanvasHeight,
          destWidth: that.shareCanvasWidth * 4, // 提高保存图片的清晰度
          destHeight: that.shareCanvasHeight * 4, // 提高保存图片的清晰度
          quality: 1,
          fileType: 'png',
          success: function (res) {
            // 获取图片临时路径
            const tempFilePath = res.tempFilePath;

            // 检查当前环境
            const systemInfo = uni.getSystemInfoSync();

            // 使用微信小程序的分享图片API

            // 使用wx.showShareImageMenu分享图片到聊天
            wx.showShareImageMenu({
              path: tempFilePath,
              success: function () {
                console.log('打开分享图片菜单成功');
              },
              fail: function (err) {
                console.error('打开分享图片菜单失败:', err);
                // 如果API不支持，尝试保存到相册
                if (err.errMsg && err.errMsg.indexOf('not support') !== -1) {
                  that.wxMpShare(tempFilePath);
                } else {
                  uni.showToast({
                    title: '分享失败',
                    icon: 'none'
                  });
                }
              }
            });
            return;
          },
          fail: function (err) {
            console.error('生成临时文件失败:', err);
            uni.showToast({
              title: '生成图片失败',
              icon: 'none'
            });
          }
        }, that);
      }, 500); // 延长延迟时间，确保绘制完成
    },
    // 绘制整个二维码区域到shareCanvas
    drawEntireQrArea() {
      // 确保二维码已经生成
      if (!this.formData.consigneeId) {
        this.showToast('请先生成入仓码');
        return;
      }

      const that = this;

      // 获取二维码区域的尺寸
      const query = uni.createSelectorQuery().in(this);
      query.select('.qr-code-section').boundingClientRect(data => {
        if (!data) {
          console.error('获取二维码区域尺寸失败');
          return;
        }

        // 设置画布尺寸和背景色，保持与实际显示比例一致
        const canvasWidth = 240;  // 减小宽度
        const canvasHeight = 400; // 减小高度

        // 设置画布尺寸
        that.shareCanvasWidth = canvasWidth;
        that.shareCanvasHeight = canvasHeight;

        // 微信小程序使用canvas绘制整个二维码区域
        const ctx = uni.createCanvasContext('shareCanvas', that);

        // 清空画布并设置背景色
        ctx.setFillStyle('#f8f8f8'); // 设置背景色与二维码区域相同
        ctx.fillRect(0, 0, canvasWidth, canvasHeight);

        // 绘制边框
        ctx.setStrokeStyle('#ddd');
        ctx.strokeRect(0, 0, canvasWidth, canvasHeight);

        // 绘制标题区域
        const titleAreaHeight = 60;
        ctx.setFillStyle('#f8f8f8');
        ctx.fillRect(0, 0, canvasWidth, titleAreaHeight);

        // 绘制标题文字
        ctx.setFontSize(14);
        ctx.setFillStyle('#003366');
        ctx.setTextAlign('center');
        ctx.setTextBaseline('middle');
        ctx.fillText(`${that.formData.clientCode} Warehouse`, canvasWidth / 2, 20);
        ctx.fillText('Entry Instruction', canvasWidth / 2, 40);

        // 获取二维码图像
        uni.canvasToTempFilePath({
          canvasId: 'canvas',
          success: (qrRes) => {
            // 计算二维码在画布中的位置和大小
            const qrSize = 180; // 减小二维码尺寸
            const qrX = (canvasWidth - qrSize) / 2;
            const qrY = titleAreaHeight + 10;

            // 绘制二维码
            ctx.drawImage(qrRes.tempFilePath, qrX, qrY, qrSize, qrSize);

            // 绘制底部客户信息区域
            const infoY = qrY + qrSize + 20;

            // 绘制客户代码
            ctx.setFontSize(13);
            ctx.setFillStyle('#666');
            ctx.setTextAlign('center');
            ctx.fillText(`${that.formData.clientCode} - ${that.formData.consigneeCode}`, canvasWidth / 2, infoY);

            // 绘制客户名称
            ctx.setFontSize(14);
            ctx.setFillStyle('#333');
            ctx.fillText(that.formData.consigneeName || '', canvasWidth / 2, infoY + 25);

            // 绘制地址信息
            ctx.setFontSize(12);
            ctx.setFillStyle('#999');
            ctx.fillText(`Guangzhou - ${that.formData.clientRegion}`, canvasWidth / 2, infoY + 45);

            // 完成绘制
            ctx.draw(false, () => {
              // 将画布内容导出为图片
              console.log("二维码区域绘制完成")
            });
          },
          fail: (err) => {
            uni.hideLoading();
            console.error('获取二维码失败:', err);
            that.showToast('获取二维码失败');
          }
        }, that);
      }).exec()

    },
    // 生成入仓码
    createBarcode() {
      if (this._fromOnLoad) {
        this._fromOnLoad = false;
        this.generateQRCode();
        return;
      }

      this.$refs.uForm.validate().then((res) => {
        if (res) {
          this.generateQRCode();
        } else {
          this.showToast('请完善表单信息');
        }
      });
    },
    // 生成二维码方法 - 适用于uni-app小程序
    generateQRCode(text) {
      // 检查是否有consigneeId
      if (!this.formData.consigneeId) {
        console.error('缺少consigneeId，无法生成二维码');
        this.showToast('缺少必要信息，无法生成二维码');
        return;
      }

      try {
        // 生成二维码
        let qrcode = new QRCode('canvas', {
          usingIn: this.formData.consigneeId,
          text: this.formData.consigneeId,
          width: 100,
          height: 100,
          colorDark: "black",
          colorLight: "white",
          correctLevel: QRCode.CorrectLevel.H,
          logo: '/static/logo.png', // 这里使用项目中的logo图片路径
          logoWidth: 30, // logo宽度
          logoHeight: 30, // logo高度
        });
        // 二维码内容
        const encryptedId = encrypt(this.formData.consigneeId.toString());

        qrcode.makeCode("http://cnshipper.com/scan/inventory/index?consigneeId=" + encryptedId);
        // qrcode.makeCode("http://cnshipper.com/scan/test/inventory/index?consigneeId=" + encryptedId);
      } catch (error) {
        console.error('生成二维码失败:', error);
        this.showToast('生成二维码失败');
      }
    },
    // 提交表单
    submitForm() {
      this.$refs.uForm.validate().then((res) => {
        if (res) {
          // 组装提交数据
          const submitData = {
            ...this.formData,
            clientId: this.$store.state.user.id,
            clientCode: this.mpWarehouseClient.clientCode, // 使用入仓代码
            clientRegion: this.mpWarehouseClient.clientRegion // 使用目的地
          };

          if (this.formData.consigneeId) {
            updateConsignee(submitData).then(() => {
              this.showToast('修改成功', 'success');
            }).catch(err => {
              this.showToast('修改失败');
            });
          } else {
            addConsignee(submitData).then(() => {
              this.showToast('添加成功', 'success');
            }).catch(err => {
              this.showToast('添加失败');
            });
          }
        }
      }).catch(err => {
        this.showToast('表单校验失败');
      });
    },
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    /**
     * 检查收货人代码是否已存在
     */
    checkConsigneeCodeExist() {
      if (!this.formData.consigneeCode) return;

      uni.showLoading({
        title: '检查中...',
        mask: true
      });

      checkConsigneeCode(this.formData.consigneeCode)
          .then(res => {
            if (res.data && res.data.exist) {
              uni.hideLoading();
              uni.showModal({
                title: '提示',
                content: `收货人代码 [${this.formData.consigneeCode}] 已存在，请选择其它代码`,
                confirmText: '确定',
                cancelText: '取消',
                success: (result) => {

                }
              });
            } else {
              uni.hideLoading();
            }
          })
          .catch(() => {
            uni.hideLoading();
          });
    },
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background-color: #f8f8f8;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // padding: 20rpx 0;
  border-bottom: 2rpx solid #ddd;
  margin-bottom: 20rpx;

  .header-left {
    .title-container {
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      .header-title {
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
        line-height: 1.2;
      }

      .header-subtitle {
        font-size: 24rpx;
        color: #666;
        line-height: 1.2;
      }
    }
  }

  .header-logo {
    font-size: 36rpx;
    font-weight: bold;
    color: #003366;
  }
}

.form-table {
  border: 2rpx solid #ddd;
  border-radius: 6rpx;
  overflow: hidden;
  // margin-bottom: 30rpx;
}

.form-row {
  display: flex;
  border-bottom: 1rpx solid #ddd;
  // height: 70rpx;

  &:last-child {
    border-bottom: none;
  }
}

.form-label {
  width: 200rpx;
  padding: 10rpx 20rpx;
  background-color: #f0f0f0;
  border-right: 1rpx solid #ddd;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-bottom: 0;

  text {
    font-size: 26rpx;
    line-height: 1.2;
  }

  .label-en {
    font-size: 20rpx;
    color: #666;
    line-height: 1.1;
  }
}

.form-text-value {
  flex: 1;
  padding: 5rpx 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  text {
    font-size: 26rpx;
    text-align: center;
  }
}

/* 二维码和按钮的左右布局容器 - 固定高度 */
.bottom-container {
  display: flex;
  margin-top: 30rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  overflow: hidden;
  height: 500rpx;
  /* 固定整个容器的高度 */
}

/* 左侧二维码区域 - 固定高度 */
.qr-code-section {
  width: 280rpx;
  background-color: #f8f8f8;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-right: 1rpx solid #ddd;
  height: 100%;
  /* 填满父容器高度 */
  box-sizing: border-box;
  /* 确保padding不会增加总高度 */

  .qr-title {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20rpx;
    width: 100%;

    text {
      font-size: 28rpx;
      color: #003366;
      font-weight: bold;
      line-height: 1.2;
      text-align: center;
    }
  }

  .qr-info {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 200rpx;
    margin: 10rpx 0;
  }

  .qrcode-canvas {
    width: 180rpx;
    height: 180rpx;
  }

  .qr-customer {
    margin-top: 10rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;

    .qr-customer-code {
      font-size: 26rpx;
      color: #666;
      margin-bottom: 4rpx;
    }

    .qr-customer-name {
      font-size: 28rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 4rpx;
    }

    .qr-customer-address {
      font-size: 24rpx;
      color: #999;
    }
  }
}

/* 右侧按钮区域 - 固定高度 */
.action-buttons-section {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  /* 填满父容器高度 */
  box-sizing: border-box;
  /* 确保padding不会增加总高度 */

  .btn-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .btn-text {
    font-size: 26rpx;
    line-height: 1.2;
  }

  .btn-text-en {
    font-size: 20rpx;
    line-height: 1.2;
  }

  .uni-btn {
    margin: 0 0 12rpx 0;
    padding: 0;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #e0ffe0;
    border: 1rpx solid #99cc99;
  }
}

/* 注意事项和版权信息区域 */
.remark-container {
  margin-top: 20rpx;
  border: 1rpx solid #0099cc;
  border-radius: 6rpx;
  background-color: #f0faff;
  padding: 16rpx;

  .remark-content {
    margin-bottom: 10rpx;

    .remark-title {
      font-weight: bold;
      color: #003366;
      margin-right: 8rpx;
    }

    .remark-text {
      font-size: 24rpx;
      color: #333;
      line-height: 1.3;
    }
  }

  .copyright-content {
    display: flex;
    justify-content: center;
    border-top: 1rpx dashed #ccc;
    padding-top: 10rpx;

    .copyright-text {
      font-size: 22rpx;
      color: #999;
      font-style: italic;
    }
  }
}

/* uni-forms样式 */
:deep(.uni-forms-item) {
  padding-bottom: 0;
  margin-bottom: 0;
  border-bottom: 1rpx solid #ddd;
}

:deep(.uni-forms-item__inner) {
  padding-bottom: 0;
}

:deep(.uni-easyinput__content) {
  background-color: rgb(255, 242, 204);
  min-height: 40px;
}

.form-item {
  display: flex;
  align-items: stretch;
}

::v-deep .uni-forms-item {
  padding-bottom: 0;
  margin-bottom: 0;
}

.ewm {
  width: 200rpx;
  height: 200rpx;
}

.share-canvas {
  position: fixed;
  left: -9999rpx;
  top: -9999rpx;
  width: 480rpx;
  height: 800rpx;
  z-index: -1;
}
</style>