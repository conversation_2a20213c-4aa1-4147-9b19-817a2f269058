package com.rich.common.core.domain.entity;

import com.rich.common.core.domain.BaseEntity;
import com.rich.common.core.domain.TreeSelect;

import java.util.List;

/**
 * @TableName mid_rs_staff_role
 */
public class MidRsStaffRole extends BaseEntity {

    private Long staffRoleDeptId;

    private Long parentId;

    /**
     * 员工
     */
    private Long staffId;

    /**
     * 权限
     */
    private Long roleId;

    private Long deptId;

    private String isMain;

    private BasDistDept dept;

    private BasPosition position;

    private RsStaff staff;

    private SysRole role;

    // 用于搜索的userId
    private Long userId;
    private String ancestors;

    public String getAncestors() {
        return ancestors;
    }

    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    private List<TreeSelect> menuList;

    public List<TreeSelect> getMenuList() {
        return menuList;
    }

    public void setMenuList(List<TreeSelect> menuList) {
        this.menuList = menuList;
    }

    private static final long serialVersionUID = 1L;

    public BasDistDept getDept() {
        return dept;
    }

    public void setDept(BasDistDept dept) {
        this.dept = dept;
    }

    public BasPosition getPosition() {
        return position;
    }

    public void setPosition(BasPosition position) {
        this.position = position;
    }

    public RsStaff getStaff() {
        return staff;
    }

    public void setStaff(RsStaff staff) {
        this.staff = staff;
    }

    public SysRole getRole() {
        return role;
    }

    public void setRole(SysRole role) {
        this.role = role;
    }

    public Long getStaffRoleDeptId() {
        return staffRoleDeptId;
    }

    public void setStaffRoleDeptId(Long staffRoleDeptId) {
        this.staffRoleDeptId = staffRoleDeptId;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getIsMain() {
        return isMain;
    }

    public void setIsMain(String isMain) {
        this.isMain = isMain;
    }

    /**
     * 员工
     */
    public Long getStaffId() {
        return staffId;
    }

    /**
     * 员工
     */
    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    /**
     * 权限
     */
    public Long getRoleId() {
        return roleId;
    }

    /**
     * 权限
     */
    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }
}