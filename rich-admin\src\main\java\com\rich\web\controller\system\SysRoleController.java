package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasDistDept;
import com.rich.common.core.domain.entity.MidRsStaffRole;
import com.rich.common.core.domain.entity.SysRole;
import com.rich.common.core.domain.model.LoginUser;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.StringUtils;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.framework.web.service.SysPermissionService;
import com.rich.framework.web.service.TokenService;
import com.rich.system.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


/**
 * 角色信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/role")

public class SysRoleController extends BaseController {
    @Autowired
    private SysRoleService roleService;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private SysPermissionService permissionService;
    @Autowired
    private RsStaffService userService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private BasDistLocationService basDistLocationService;
    @Autowired
    private BasDistDeptService deptService;
    @Autowired
    private SysMenuService menuService;

    /**
     * 查询角色列表
     *
     * @param role
     * @return
     */
    @PreAuthorize("@ss.hasPermi('system:role:list')")
    @GetMapping("/list")
    public AjaxResult list(SysRole role) {
        return AjaxResult.success(AjaxResult.DATA_TAG, roleService.selectRoleList(role));
    }

    /**
     * 查询商务部、操作部、协助操作
     *
     * @param dept
     * @return
     */
    @PostMapping("/selectsList")
    public AjaxResult getSalesList(@RequestBody BasDistDept dept) {
        List list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "roleStaff" + dept.getDeptLocalName());
        if (list == null) {
            list = roleService.selectSales(dept);
            redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "roleStaff" + dept.getDeptLocalName());
            redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "roleStaff" + dept.getDeptLocalName(), list);
        }
//        list = roleService.selectSales(dept);
        return AjaxResult.success(list);
    }

    /**
     * 查询业务、业务助理
     *
     * @param dept
     * @return
     */
    @PostMapping("/belongList")
    public AjaxResult getBelongList(@RequestBody BasDistDept dept, @RequestParam(required = false, value = "level", defaultValue = "A") String level) {
        List<MidRsStaffRole> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "staffByRoleDept" + dept.getDeptLocalName());
        if (list == null) {
            list = roleService.selectBusinesses(dept);
            redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "staffByRoleDept" + dept.getDeptLocalName());
            redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "staffByRoleDept" + dept.getDeptLocalName(), list);
        }
//        list = roleService.selectBusinesses(dept);
        ArrayList<MidRsStaffRole> result = roleService.filterBusinessesListByRole(list, level);
        return AjaxResult.success(result);
    }

    @GetMapping("/getList")
    public AjaxResult getList() {
        /*List<SysRole> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "role");
        if (list == null || list.isEmpty()) {
            list = roleService.getList();
            redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "role");
            redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "role", list);
        }*/
        List<SysRole> list = roleService.getList();
        return AjaxResult.success(list);
    }

    @Log(title = "角色管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:role:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysRole role) {
        List<SysRole> list = roleService.selectRoleList(role);
        ExcelUtil<SysRole> util = new ExcelUtil<>(SysRole.class);
        util.exportExcel(response, list, "角色数据");
    }

    /**
     * 根据角色编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @GetMapping(value = "/{roleId}")
    public AjaxResult getInfo(@PathVariable Long roleId) {
        roleService.checkRoleDataScope(roleId);
        AjaxResult ajaxResult = AjaxResult.success();
        Set<Long> set = new HashSet<>();
        ajaxResult.put(AjaxResult.DATA_TAG, roleService.selectRoleById(roleId));
        List<Long> locationDepartureIds = roleService.selectLocationDepartureIds(roleId);
        ajaxResult.put("locationDepartureIds", locationDepartureIds);
        ajaxResult.put("lineDepartureIds", roleService.selectLineDepartureIds(roleId));
        List<Long> locationDestinationIds = roleService.selectLocationDestinationIds(roleId);
        ajaxResult.put("locationDestinationIds", locationDestinationIds);
        ajaxResult.put("lineDestinationIds", roleService.selectLineDestinationIds(roleId));
        ajaxResult.put("serviceTypeIds", roleService.selectServiceTypeIds(roleId));
        ajaxResult.put("cargoTypeIds", roleService.selectCargoTypeIds(roleId));
        ajaxResult.put("deptIds", deptService.selectDeptListByRoleId(roleId));
        ajaxResult.put("menuIds", menuService.selectMenuListByRoleId(roleId));
        set.addAll(locationDepartureIds);
        set.addAll(locationDestinationIds);
        ajaxResult.put("locationOptions", set.size() > 0 ? basDistLocationService.selectBasDistLocationByIds(set) : null);
        return ajaxResult;
    }

    @PreAuthorize("@ss.hasPermi('system:company:edit')")
    @GetMapping(value = "/s/{staffId}")
    public AjaxResult getInfoByStaffId(@PathVariable("staffId") Long staffId) {
        AjaxResult ajaxResult = AjaxResult.success();
        List<MidRsStaffRole> midRsStaffRoles = roleService.selectRolesByUserId(staffId);
        Set<Long> locationDepartureIds = new HashSet<>();
        Set<Long> lineDepartureIds = new HashSet<>();
        Set<Long> locationDestinationIds = new HashSet<>();
        Set<Long> lineDestinationIds = new HashSet<>();
        Set<Long> serviceTypeIds = new HashSet<>();
        Set<Long> cargoTypeIds = new HashSet<>();
        Set<Long> query = new HashSet<>();
        for (MidRsStaffRole d : midRsStaffRoles) {
            locationDepartureIds.addAll(roleService.selectLocationDepartureIds(d.getRoleId()));
            lineDepartureIds.addAll(roleService.selectLineDepartureIds(d.getRoleId()));
            locationDestinationIds.addAll(roleService.selectLocationDestinationIds(d.getRoleId()));
            lineDestinationIds.addAll(roleService.selectLineDestinationIds(d.getRoleId()));
            serviceTypeIds.addAll(roleService.selectServiceTypeIds(d.getRoleId()));
            cargoTypeIds.addAll(roleService.selectCargoTypeIds(d.getRoleId()));
        }
        query.addAll(locationDepartureIds);
        query.addAll(locationDestinationIds);
        ajaxResult.put(AjaxResult.DATA_TAG, midRsStaffRoles);
        ajaxResult.put("locationDepartureIds", locationDepartureIds);
        ajaxResult.put("lineDepartureIds", lineDepartureIds);
        ajaxResult.put("locationDestinationIds", locationDestinationIds);
        ajaxResult.put("lineDestinationIds", lineDestinationIds);
        ajaxResult.put("serviceTypeIds", serviceTypeIds);
        ajaxResult.put("cargoTypeIds", cargoTypeIds);
        ajaxResult.put("locationOptions", query.size() > 0 ? basDistLocationService.selectBasDistLocationByIds(query) : null);
        return ajaxResult;
    }

    /**
     * 新增角色
     */
    @PreAuthorize("@ss.hasPermi('system:role:add')")
    @Log(title = "角色管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysRole role) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "role");
        if (!roleService.checkroleLocalNameUnique(role)) {
            return AjaxResult.error("新增角色'" + role.getRoleLocalName() + "'失败，角色名称已存在");
        } else if (!roleService.checkRoleKeyUnique(role)) {
            return AjaxResult.error("新增角色'" + role.getRoleLocalName() + "'失败，角色权限已存在");
        }
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "role");
        for (String staffByRoleDept : redisCache.keys(CacheConstants.DATA_CACHE_KEY + "staffByRoleDept" + "*")) {
            redisCache.deleteObject(staffByRoleDept);
        }
        for (String roleStaff : redisCache.keys(CacheConstants.DATA_CACHE_KEY + "roleStaff" + "*")) {
            redisCache.deleteObject(roleStaff);
        }
        role.setCreateBy(getUserId());
        return toAjax(roleService.insertRole(role));
    }

    /**
     * 修改保存角色
     */
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysRole role) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "role");
        // 检查是否操作管理员角色
        roleService.checkRoleAllowed(role);
        // 检查是否为管理员，查看当前角色（角色id）是否含有角色列表
        roleService.checkRoleDataScope(role.getRoleId());
        // 角色名是否唯一
        if (!roleService.checkroleLocalNameUnique(role)) {
            return AjaxResult.error("修改角色'" + role.getRoleLocalName() + "'失败，角色名称已存在");
            // RoleKey是否唯一
        } else if (!roleService.checkRoleKeyUnique(role)) {
            return AjaxResult.error("修改角色'" + role.getRoleLocalName() + "'失败，角色权限已存在");
        }
        role.setUpdateBy(getUserId());

        // 更新角色
        if (roleService.updateRole(role) > 0) {
            // 角色更新成功（更新了角色，返回影响条数)
            // 更新缓存用户权限
            LoginUser loginUser = getLoginUser();
            // 当天用户不为空且不是管理员
            if (StringUtils.isNotNull(loginUser.getUser()) && !loginUser.getUser().isAdmin()) {
                loginUser.setPermissions(permissionService.getMenuPermission(loginUser.getUser()));
                loginUser.setUser(userService.selectUserByUserName(loginUser.getUser().getStaffUsername()));
                tokenService.setLoginUser(loginUser);
            }
            // 直接返回更新成功
            return AjaxResult.success();
        }
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "role");
        // 删除按角色划分的部门缓存(如商务部、业务部)
        for (String staffByRoleDept : redisCache.keys(CacheConstants.DATA_CACHE_KEY + "staffByRoleDept" + "*")) {
            redisCache.deleteObject(staffByRoleDept);
        }
        // 删除按角色划分的部门人员(如业务员、操作员)
        for (String roleStaff : redisCache.keys(CacheConstants.DATA_CACHE_KEY + "roleStaff" + "*")) {
            redisCache.deleteObject(roleStaff);
        }
        return AjaxResult.error("修改角色'" + role.getRoleLocalName() + "'失败，请联系管理员");
    }


    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysRole role) {
        roleService.checkRoleAllowed(role);
        roleService.checkRoleDataScope(role.getRoleId());
        role.setUpdateBy(getUserId());
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "role");
        for (String staffByRoleDept : redisCache.keys(CacheConstants.DATA_CACHE_KEY + "staffByRoleDept" + "*")) {
            redisCache.deleteObject(staffByRoleDept);
        }
        for (String roleStaff : redisCache.keys(CacheConstants.DATA_CACHE_KEY + "roleStaff" + "*")) {
            redisCache.deleteObject(roleStaff);
        }
        return toAjax(roleService.updateRoleStatus(role));
    }

    /**
     * 删除角色
     */
    @PreAuthorize("@ss.hasPermi('system:role:remove')")
    @Log(title = "角色管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{roleId}")
    public AjaxResult remove(@PathVariable Long roleId) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "role");
        for (String staffByRoleDept : redisCache.keys(CacheConstants.DATA_CACHE_KEY + "staffByRoleDept" + "*")) {
            redisCache.deleteObject(staffByRoleDept);
        }
        for (String roleStaff : redisCache.keys(CacheConstants.DATA_CACHE_KEY + "roleStaff" + "*")) {
            redisCache.deleteObject(roleStaff);
        }
        return toAjax(roleService.deleteRoleById(roleId));
    }

    /**
     * 获取角色选择框列表
     */
    @PreAuthorize("@ss.hasPermi('system:role:list')")
    @GetMapping("/optionSelect")
    public AjaxResult optionSelect() {
        return AjaxResult.success(roleService.selectRoleAll());
    }

}
