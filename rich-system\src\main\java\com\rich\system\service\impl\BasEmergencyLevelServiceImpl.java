package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.BasEmergencyLevel;
import com.rich.system.mapper.BasEmergencyLevelMapper;
import com.rich.system.service.BasEmergencyLevelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/23 17:49
 * @Version 1.0
 */
@Service
public class BasEmergencyLevelServiceImpl implements BasEmergencyLevelService {
    @Autowired
    private BasEmergencyLevelMapper basEmergencyLevelMapper;

    /**
     * 查询紧急程度
     *
     * @param emergencyLevelId 紧急程度主键
     * @return 紧急程度
     */
    @Override
    public BasEmergencyLevel selectBasEmergencyLevelByEmergencyLevelId(Long emergencyLevelId) {
        return basEmergencyLevelMapper.selectBasEmergencyLevelByEmergencyLevelId(emergencyLevelId);
    }

    /**
     * 查询紧急程度列表
     *
     * @param basEmergencyLevel 紧急程度
     * @return 紧急程度
     */
    @Override
    public List<BasEmergencyLevel> selectBasEmergencyLevelList(BasEmergencyLevel basEmergencyLevel) {
        return basEmergencyLevelMapper.selectBasEmergencyLevelList(basEmergencyLevel);
    }

    /**
     * 新增紧急程度
     *
     * @param basEmergencyLevel 紧急程度
     * @return 结果
     */
    @Override
    public int insertBasEmergencyLevel(BasEmergencyLevel basEmergencyLevel) {
        return basEmergencyLevelMapper.insertBasEmergencyLevel(basEmergencyLevel);
    }

    /**
     * 修改紧急程度
     *
     * @param basEmergencyLevel 紧急程度
     * @return 结果
     */
    @Override
    public int updateBasEmergencyLevel(BasEmergencyLevel basEmergencyLevel) {
        return basEmergencyLevelMapper.updateBasEmergencyLevel(basEmergencyLevel);
    }

    /**
     * 修改紧急程度状态
     *
     * @param basEmergencyLevel 紧急程度
     * @return 紧急程度
     */
    @Override
    public int changeStatus(BasEmergencyLevel basEmergencyLevel) {
        return basEmergencyLevelMapper.updateBasEmergencyLevel(basEmergencyLevel);
    }

    /**
     * 批量删除紧急程度
     *
     * @param emergencyLevelIds 需要删除的紧急程度主键
     * @return 结果
     */
    @Override
    public int deleteBasEmergencyLevelByEmergencyLevelIds(Long[] emergencyLevelIds) {
        return basEmergencyLevelMapper.deleteBasEmergencyLevelByEmergencyLevelIds(emergencyLevelIds);
    }

    /**
     * 删除紧急程度信息
     *
     * @param emergencyLevelId 紧急程度主键
     * @return 结果
     */
    @Override
    public int deleteBasEmergencyLevelByEmergencyLevelId(Long emergencyLevelId) {
        return basEmergencyLevelMapper.deleteBasEmergencyLevelByEmergencyLevelId(emergencyLevelId);
    }
}
