package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.MidRsStaffRole;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.MidRsStaffRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;

/**
 * 权限分配Controller
 *
 * <AUTHOR>
 * @date 2022-12-02
 */
@RestController
@RequestMapping("/system/staffrole")
public class MidRsStaffRoleController extends BaseController {

    @Autowired
    private MidRsStaffRoleService midRsStaffRoleService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询权限分配列表
     */
    @PreAuthorize("@ss.hasAnyPermi('system:role:list,system:user:list')")
    @GetMapping("/list")
    public TableDataInfo list(MidRsStaffRole midRsStaffRole) {
        startPage();
        List<MidRsStaffRole> list = midRsStaffRoleService.selectMidRsStaffRoleList(midRsStaffRole);
        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('system:role:list')")
    @GetMapping("/menuList")
    public AjaxResult menuList(MidRsStaffRole midRsStaffRole) {
        return AjaxResult.success(midRsStaffRoleService.selectMidRsStaffRoleMenu(midRsStaffRole));
    }

    /**
     * 导出权限分配列表
     */
    @PreAuthorize("@ss.hasPermi('system:role:export')")
    @Log(title = "权限分配", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MidRsStaffRole midRsStaffRole) {
        List<MidRsStaffRole> list = midRsStaffRoleService.selectMidRsStaffRoleList(midRsStaffRole);
        ExcelUtil<MidRsStaffRole> util = new ExcelUtil<>(MidRsStaffRole.class);
        util.exportExcel(response, list, "权限分配数据");
    }

    /**
     * 获取权限分配详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @GetMapping(value = "/{staffRoleDeptId}")
    public AjaxResult getInfo(@PathVariable("staffRoleDeptId") Long staffRoleDeptId) {
        return AjaxResult.success(midRsStaffRoleService.selectMidRsStaffRoleByStaffRoleDeptId(staffRoleDeptId));
    }

    /**
     * 新增权限分配
     */
    @PreAuthorize("@ss.hasPermi('system:role:add')")
    @Log(title = "权限分配", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MidRsStaffRole midRsStaffRole) {
        for (String staffByRoleDept : redisCache.keys(CacheConstants.DATA_CACHE_KEY + "staffByRoleDept" + "*")) {
            redisCache.deleteObject(staffByRoleDept);
        }
        for (String roleStaff : redisCache.keys(CacheConstants.DATA_CACHE_KEY + "roleStaff" + "*")) {
            redisCache.deleteObject(roleStaff);
        }
        return toAjax(midRsStaffRoleService.insertMidRsStaffRole(midRsStaffRole));
    }

    /**
     * 修改权限分配
     */
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @Log(title = "权限分配", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MidRsStaffRole midRsStaffRole) {
        MidRsStaffRole main = new MidRsStaffRole();
        main.setStaffId(midRsStaffRole.getStaffId());
        main.setIsMain("Y");
        List<MidRsStaffRole> midRsStaffRoles = midRsStaffRoleService.selectMidRsStaffRoleList(main);
        // 当前用户是否已经存在一个主角色
        if (!midRsStaffRoles.isEmpty() && midRsStaffRole.getIsMain().equals("Y") && !Objects.equals(midRsStaffRoles.get(0).getStaffRoleDeptId(), midRsStaffRole.getStaffRoleDeptId())) {
            return AjaxResult.error("该用户存在主职业");
        } else {
            // 清理缓存
            for (String staffByRoleDept : redisCache.keys(CacheConstants.DATA_CACHE_KEY + "staffByRoleDept" + "*")) {
                redisCache.deleteObject(staffByRoleDept);
            }
            for (String roleStaff : redisCache.keys(CacheConstants.DATA_CACHE_KEY + "roleStaff" + "*")) {
                redisCache.deleteObject(roleStaff);
            }
            return toAjax(midRsStaffRoleService.updateMidRsStaffRole(midRsStaffRole));
        }
    }


    /**
     * 删除权限分配
     */
    @PreAuthorize("@ss.hasPermi('system:role:remove')")
    @Log(title = "权限分配", businessType = BusinessType.DELETE)
    @DeleteMapping("/{staffRoleDeptIds}")
    public AjaxResult remove(@PathVariable Long[] staffRoleDeptIds) {
        for (String staffByRoleDept : redisCache.keys(CacheConstants.DATA_CACHE_KEY + "staffByRoleDept" + "*")) {
            redisCache.deleteObject(staffByRoleDept);
        }
        for (String roleStaff : redisCache.keys(CacheConstants.DATA_CACHE_KEY + "roleStaff" + "*")) {
            redisCache.deleteObject(roleStaff);
        }
        return toAjax(midRsStaffRoleService.deleteMidRsStaffRoleByStaffRoleDeptIds(staffRoleDeptIds));
    }
}
