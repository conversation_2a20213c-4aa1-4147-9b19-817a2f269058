package com.rich.system.service.impl;

import com.rich.common.constant.CacheConstants;
import com.rich.common.core.domain.entity.BasDistCargoType;
import com.rich.common.core.domain.entity.BasDistLine;
import com.rich.common.core.domain.entity.BasDistLocation;
import com.rich.common.core.domain.entity.RsCharacteristics;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.core.text.Convert;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SearchUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.StringUtils;
import com.rich.system.domain.*;
import com.rich.system.mapper.*;
import com.rich.system.service.RsCharacteristicsService;
import org.apache.commons.lang3.ArrayUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.rich.common.utils.PageUtils.startPage;

/**
 * 物流注意事项Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-30
 */
@Service

public class RsCharacteristicsServiceImpl implements RsCharacteristicsService {
    @Autowired
    private RsCharacteristicsMapper rsCharacteristicsMapper;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private RedisCacheImpl RedisCache;
    @Autowired
    private MidCargoTypeMapper midCargoTypeMapper;
    @Autowired
    private MidLocationDepartureMapper midLocationDepartureMapper;
    @Autowired
    private MidLineDepartureMapper midLineDepartureMapper;
    @Autowired
    private MidLocationDestinationMapper midLocationDestinationMapper;
    @Autowired
    private MidLineDestinationMapper midLineDestinationMapper;
    @Autowired
    private MidCarrierMapper midCarrierMapper;

    /**
     * 查询物流注意事项
     *
     * @param characteristicsId 物流注意事项主键
     * @return 物流注意事项
     */
    @Override
    public RsCharacteristics selectRsCharacteristicsByCharacteristicsId(Long characteristicsId) {
        return rsCharacteristicsMapper.selectRsCharacteristicsByCharacteristicsId(characteristicsId);
    }

    /**
     * 查询物流注意事项列表
     *
     * @param rsCharacteristics 物流注意事项
     * @return 物流注意事项
     */
    @Override
    public List<RsCharacteristics> selectRsCharacteristicsList(RsCharacteristics rsCharacteristics) {
        boolean search = rsCharacteristics.getCargoTypeIds() != null ||
                rsCharacteristics.getLocationDepartureIds() != null ||
                rsCharacteristics.getLineDepartureIds() != null ||
                rsCharacteristics.getLocationDestinationIds() != null ||
                rsCharacteristics.getLineDestinationIds() != null ||
                rsCharacteristics.getCarrierIds() != null;
        List<Long> qc;
        if (search) {
            qc = queryCharacteristics(rsCharacteristics);
            if (qc == null || qc.size() == 0) {
                return null;
            }
            rsCharacteristics.setCharacteristicsIds(qc);
        }
        startPage();
        return rsCharacteristicsMapper.selectRsCharacteristicsList(rsCharacteristics);
    }

    private List<Long> queryCharacteristics(RsCharacteristics rsCharacteristics) {
        List<List<Long>> lists = new ArrayList<>();
        List<RsCharacteristics> characteristics = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "characteristics");
        if (characteristics == null) {
            RedisCache.characteristics();
            characteristics = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "characteristics");
        }
        List<BasDistLocation> basDistLocations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        if (basDistLocations == null) {
            RedisCache.location();
            basDistLocations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        }
        List<BasDistLine> basDistLines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        if (basDistLines == null) {
            RedisCache.line();
            basDistLines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        }
        List<BasDistCargoType> basDistCargoTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        if (basDistCargoTypes == null) {
            RedisCache.cargoType();
            basDistCargoTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        }
        List<MidCarrier> midCarriers = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "characteristicsCarriers");
        if (midCarriers == null) {
            RedisCache.midCarrier("characteristics", "characteristicsCarriers");
            midCarriers = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "characteristicsCarriers");
        }
        List<MidCargoType> midCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "characteristicsCargoType");
        if (midCargoTypes == null) {
            RedisCache.midCargoType("characteristics", "characteristicsCargoType");
            midCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "characteristicsCargoType");
        }
        List<MidLocationDeparture> midLocationDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "characteristicsLocationDeparture");
        if (midLocationDepartures == null) {
            RedisCache.locationDeparture("characteristics", "characteristicsLocationDeparture");
            midLocationDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "characteristicsLocationDeparture");
        }
        List<MidLineDeparture> midLineDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "characteristicsLineDeparture");
        if (midLineDepartures == null) {
            RedisCache.lineDeparture("characteristics", "characteristicsLineDeparture");
            midLineDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "characteristicsLineDeparture");
        }
        List<MidLocationDestination> midLocationDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "characteristicsLocationDestination");
        if (midLocationDestinations == null) {
            RedisCache.locationDestination("characteristics", "characteristicsLocationDestination");
            midLocationDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "characteristicsLocationDestination");
        }
        List<MidLineDestination> midLineDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "characteristicsLineDestination");
        if (midLineDestinations == null) {
            RedisCache.lineDestination("characteristics", "characteristicsLineDestination");
            midLineDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "characteristicsLineDestination");
        }
        if (rsCharacteristics.getCarrierIds() != null) {
            Set<Long> set = new HashSet<>();
            for (MidCarrier d : midCarriers) {
                if (ArrayUtils.contains(rsCharacteristics.getCarrierIds(), d.getCarrierId())) {
                    set.add(d.getBelongId());
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (list.size() > 0) {
                lists.add(list);
            } else {
                return null;
            }
        }
        if (rsCharacteristics.getCargoTypeIds() != null) {
            Set<Long> set = new HashSet<>();
            if (ArrayUtils.contains(rsCharacteristics.getCargoTypeIds(), -1L)) {
                for (RsCharacteristics c : characteristics) {
                    set.add(c.getCharacteristicsId());
                }
            } else {
                Set<Long> c = new HashSet<>();
                for (BasDistCargoType cargoType : basDistCargoTypes) {
                    String[] ancestors = cargoType.getAncestors().split(",");
                    if (!rsCharacteristics.getAccurate().equals(4) && (rsCharacteristics.getAccurate().equals(1) || rsCharacteristics.getAccurate().equals(2)) && SearchUtils.existSame(ancestors, rsCharacteristics.getCargoTypeIds())) {
                        c.add(cargoType.getCargoTypeId());
                    }
                    if (ArrayUtils.contains(rsCharacteristics.getCargoTypeIds(), cargoType.getCargoTypeId())) {
                        c.add(cargoType.getCargoTypeId());
                        if (!rsCharacteristics.getAccurate().equals(4) && (rsCharacteristics.getAccurate().equals(1) || rsCharacteristics.getAccurate().equals(3))) {
                            for (String a : ancestors) {
                                c.add(Convert.toLong(a));
                            }
                        }
                    }
                }
                for (MidCargoType midCargoType : midCargoTypes) {
                    if (c.contains(midCargoType.getCargoTypeId())) {
                        set.add(midCargoType.getBelongId());
                    }
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (list.size() > 0) {
                lists.add(list);
            } else {
                return null;
            }
        }
        if (rsCharacteristics.getLocationDepartureIds() != null) {
            Set<Long> set = new HashSet<>();
            if (ArrayUtils.contains(rsCharacteristics.getLocationDepartureIds(), -1L)) {
                for (RsCharacteristics c : characteristics) {
                    set.add(c.getCharacteristicsId());
                }
            } else {
                Set<Long> olines = new HashSet<>();
                Set<Long> olocations = new HashSet<>();
                for (BasDistLocation location : basDistLocations) {
                    String[] ancestors = location.getAncestors().split(",");
                    if (ArrayUtils.contains(rsCharacteristics.getLocationDepartureIds(), location.getLocationId())) {
                        olocations.add(location.getLocationId());
                        if (!rsCharacteristics.getAccurate().equals(4) && (rsCharacteristics.getAccurate().equals(1) || rsCharacteristics.getAccurate().equals(3))) {
                            for (String a : ancestors) {
                                olocations.add(Convert.toLong(a));
                            }
                        }
                        if (location.getLineId() != null && !rsCharacteristics.getAccurate().equals(4)) {
                            for (BasDistLine line : basDistLines) {
                                String[] lineAncestors = line.getAncestors().split(",");
                                if (line.getLineId().equals(location.getLineId())) {
                                    olines.add(location.getLineId());
                                    if ((rsCharacteristics.getAccurate().equals(1) || rsCharacteristics.getAccurate().equals(3))) {
                                        for (String a : lineAncestors) {
                                            olines.add(Convert.toLong(a));
                                        }
                                    }
                                }
                                if (ArrayUtils.contains(lineAncestors, location.getLineId().toString())) {
                                    olines.add(line.getLineId());
                                }
                            }
                        }
                    }
                    if (!rsCharacteristics.getAccurate().equals(4) && (rsCharacteristics.getAccurate().equals(1) || rsCharacteristics.getAccurate().equals(2)) && SearchUtils.existSame(ancestors, rsCharacteristics.getLocationDepartureIds())) {
                        olocations.add(location.getLocationId());
                    }
                }
                for (MidLocationDeparture locationDeparture : midLocationDepartures) {
                    if (olocations.contains(locationDeparture.getLocationId())) {
                        set.add(locationDeparture.getBelongId());
                    }
                }
                for (MidLineDeparture lineDeparture : midLineDepartures) {
                    if (olines.contains(lineDeparture.getLineId())) {
                        set.add(lineDeparture.getBelongId());
                    }
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (list.size() > 0) {
                lists.add(list);
            } else {
                return null;
            }
        }
        if (rsCharacteristics.getLineDepartureIds() != null) {
            Set<Long> set = new HashSet<>();
            if (ArrayUtils.contains(rsCharacteristics.getLineDepartureIds(), -1L)) {
                for (RsCharacteristics c : characteristics) {
                    set.add(c.getCharacteristicsId());
                }
            } else {
                Set<Long> olocations = new HashSet<>();
                Set<Long> olines = new HashSet<>();
                for (BasDistLine line : basDistLines) {
                    String[] ancestors = line.getAncestors().split(",");
                    if (ArrayUtils.contains(rsCharacteristics.getLineDepartureIds(), line.getLineId())) {
                        olines.add(line.getLineId());
                        if (!rsCharacteristics.getAccurate().equals(4) && (rsCharacteristics.getAccurate().equals(1) || rsCharacteristics.getAccurate().equals(3))) {
                            for (String a : ancestors) {
                                olines.add(Convert.toLong(a));
                            }
                        }
                    }
                    if (!rsCharacteristics.getAccurate().equals(4) && (rsCharacteristics.getAccurate().equals(1) || rsCharacteristics.getAccurate().equals(2)) && SearchUtils.existSame(ancestors, rsCharacteristics.getLineDepartureIds())) {
                        olines.add(line.getLineId());
                    }
                }
                for (BasDistLocation location : basDistLocations) {
                    if (location.getLineId() != null && ArrayUtils.contains(olines.toArray(), location.getLineId())) {
                        String[] ancestors = location.getAncestors().split(",");
                        olocations.add(location.getLocationId());
                        if (!rsCharacteristics.getAccurate().equals(4) && (rsCharacteristics.getAccurate().equals(1) || rsCharacteristics.getAccurate().equals(3))) {
                            for (String a : ancestors) {
                                olocations.add(Convert.toLong(a));
                            }
                        }
                    }
                }
                for (MidLocationDeparture locationDeparture : midLocationDepartures) {
                    if (olocations.contains(locationDeparture.getLocationId())) {
                        set.add(locationDeparture.getBelongId());
                    }
                }
                for (MidLineDeparture lineDeparture : midLineDepartures) {
                    if (olines.contains(lineDeparture.getLineId())) {
                        set.add(lineDeparture.getBelongId());
                    }
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (list.size() > 0) {
                lists.add(list);
            } else {
                return null;
            }
        }
        if (rsCharacteristics.getLocationDestinationIds() != null) {
            Set<Long> set = new HashSet<>();
            if (ArrayUtils.contains(rsCharacteristics.getLocationDestinationIds(), -1L)) {
                for (RsCharacteristics c : characteristics) {
                    set.add(c.getCharacteristicsId());
                }
            } else {
                Set<Long> olines = new HashSet<>();
                Set<Long> olocations = new HashSet<>();
                for (BasDistLocation location : basDistLocations) {
                    String[] ancestors = location.getAncestors().split(",");
                    if (ArrayUtils.contains(rsCharacteristics.getLocationDestinationIds(), location.getLocationId())) {
                        olocations.add(location.getLocationId());
                        if (!rsCharacteristics.getAccurate().equals(4) && (rsCharacteristics.getAccurate().equals(1) || rsCharacteristics.getAccurate().equals(3))) {
                            for (String a : ancestors) {
                                olocations.add(Long.parseLong(a));
                            }
                        }
                        if (location.getLineId() != null && !rsCharacteristics.getAccurate().equals(4)) {
                            for (BasDistLine line : basDistLines) {
                                String[] lineAncestors = line.getAncestors().split(",");
                                if (line.getLineId().equals(location.getLineId())) {
                                    olines.add(location.getLineId());
                                    if (rsCharacteristics.getAccurate().equals(1) || rsCharacteristics.getAccurate().equals(3)) {
                                        for (String a : lineAncestors) {
                                            olines.add(Long.parseLong(a));
                                        }
                                    }
                                }
                                if (ArrayUtils.contains(lineAncestors, location.getLineId().toString())) {
                                    olines.add(line.getLineId());
                                }
                            }
                        }
                    }
                    if (!rsCharacteristics.getAccurate().equals(4) && (rsCharacteristics.getAccurate().equals(1) || rsCharacteristics.getAccurate().equals(2)) && SearchUtils.existSame(ancestors, rsCharacteristics.getLocationDestinationIds())) {
                        olocations.add(location.getLocationId());
                    }
                }
                for (MidLocationDestination locationDestination : midLocationDestinations) {
                    if (olocations.contains(locationDestination.getLocationId())) {
                        set.add(locationDestination.getBelongId());
                    }
                }
                for (MidLineDestination lineDestination : midLineDestinations) {
                    if (olines.contains(lineDestination.getLineId())) {
                        set.add(lineDestination.getBelongId());
                    }
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (list.size() > 0) {
                lists.add(list);
            } else {
                return null;
            }
        }
        if (rsCharacteristics.getLineDestinationIds() != null) {
            Set<Long> set = new HashSet<>();
            if (ArrayUtils.contains(rsCharacteristics.getLineDepartureIds(), -1L)) {
                for (RsCharacteristics c : characteristics) {
                    set.add(c.getCharacteristicsId());
                }
            } else {
                Set<Long> olocations = new HashSet<>();
                Set<Long> olines = new HashSet<>();
                for (BasDistLine line : basDistLines) {
                    String[] ancestors = line.getAncestors().split(",");
                    if (ArrayUtils.contains(rsCharacteristics.getLineDestinationIds(), line.getLineId())) {
                        olines.add(line.getLineId());
                        if (!rsCharacteristics.getAccurate().equals(4) && (rsCharacteristics.getAccurate().equals(1) || rsCharacteristics.getAccurate().equals(3))) {
                            for (String a : ancestors) {
                                olines.add(Convert.toLong(a));
                            }
                        }
                    }
                    if (!rsCharacteristics.getAccurate().equals(4) && (rsCharacteristics.getAccurate().equals(1) || rsCharacteristics.getAccurate().equals(2)) && SearchUtils.existSame(ancestors, rsCharacteristics.getLineDestinationIds())) {
                        olines.add(line.getLineId());
                    }
                }
                for (BasDistLocation location : basDistLocations) {
                    if (location.getLineId() != null && ArrayUtils.contains(olines.toArray(), location.getLineId())) {
                        String[] ancestors = location.getAncestors().split(",");
                        olocations.add(location.getLocationId());
                        if (!rsCharacteristics.getAccurate().equals(4) && (rsCharacteristics.getAccurate().equals(1) || rsCharacteristics.getAccurate().equals(3))) {
                            for (String a : ancestors) {
                                olocations.add(Convert.toLong(a));
                            }
                        }
                    }
                }
                for (MidLocationDestination locationDestination : midLocationDestinations) {
                    if (olocations.contains(locationDestination.getLocationId())) {
                        set.add(locationDestination.getBelongId());
                    }
                }
                for (MidLineDestination lineDestination : midLineDestinations) {
                    if (olines.contains(lineDestination.getLineId())) {
                        set.add(lineDestination.getBelongId());
                    }
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (list.size() > 0) {
                lists.add(list);
            } else {
                return null;
            }
        }
        if (lists.size() > 0) {
            return SearchUtils.getLongs(lists);
        } else {
            return null;
        }
    }

    /**
     * 新增物流注意事项
     *
     * @param rsCharacteristics 物流注意事项
     * @return 结果
     */
    @Override
    public int insertRsCharacteristics(RsCharacteristics rsCharacteristics) {
        rsCharacteristics.setCreateTime(DateUtils.getNowDate());
        rsCharacteristics.setCreateBy(SecurityUtils.getUserId());
        rsCharacteristics.setRichNo("RPC" + DateUtils.dateTimeNow() + String.format("%05d", new SecureRandom().nextInt(100000)));
        int out = rsCharacteristicsMapper.insertRsCharacteristics(rsCharacteristics);
        insertCarriers(rsCharacteristics);
        insertCargoType(rsCharacteristics);
        insertLineDeparture(rsCharacteristics);
        insertLocationDeparture(rsCharacteristics);
        insertLineDestination(rsCharacteristics);
        insertLocationDestination(rsCharacteristics);
        RedisCache.characteristics();
        return out;
    }

    /**
     * 修改物流注意事项
     *
     * @param rsCharacteristics 物流注意事项
     * @return 结果
     */
    @Override
    public int updateRsCharacteristics(RsCharacteristics rsCharacteristics) {
        rsCharacteristics.setUpdateTime(DateUtils.getNowDate());
        rsCharacteristics.setUpdateBy(SecurityUtils.getUserId());
        midCargoTypeMapper.deleteMidCargoTypeById(rsCharacteristics.getCharacteristicsId(), "characteristics");
        midLineDepartureMapper.deleteMidLineDepartureById(rsCharacteristics.getCharacteristicsId(), "characteristics");
        midLocationDepartureMapper.deleteMidLocationDepartureById(rsCharacteristics.getCharacteristicsId(), "characteristics");
        midLineDestinationMapper.deleteMidLineDestinationById(rsCharacteristics.getCharacteristicsId(), "characteristics");
        midLocationDestinationMapper.deleteMidLocationDestinationById(rsCharacteristics.getCharacteristicsId(), "characteristics");
        midCarrierMapper.deleteMidCarrierById(rsCharacteristics.getCharacteristicsId(), "characteristics");
        insertCarriers(rsCharacteristics);
        insertCargoType(rsCharacteristics);
        insertLocationDestination(rsCharacteristics);
        insertLineDestination(rsCharacteristics);
        insertLocationDeparture(rsCharacteristics);
        insertLineDeparture(rsCharacteristics);
        int out = rsCharacteristicsMapper.updateRsCharacteristics(rsCharacteristics);
        RedisCache.characteristics();
        return out;
    }

    /**
     * 批量删除物流注意事项
     *
     * @param characteristicsIds 需要删除的物流注意事项主键
     * @return 结果
     */
    @Override
    public int deleteRsCharacteristicsByCharacteristicsIds(Long[] characteristicsIds) {
        midCargoTypeMapper.deleteMidCargoTypeByIds(characteristicsIds, "characteristics");
        RedisCache.midCargoType("characteristics", "characteristicsCargoType");

        midLineDepartureMapper.deleteMidLineDepartureByIds(characteristicsIds, "characteristics");
        RedisCache.lineDeparture("characteristics", "characteristicsLineDeparture");

        midLocationDepartureMapper.deleteMidLocationDepartureByIds(characteristicsIds, "characteristics");
        RedisCache.locationDeparture("characteristics", "characteristicsLocationDeparture");

        midLineDestinationMapper.deleteMidLineDestinationByIds(characteristicsIds, "characteristics");
        RedisCache.lineDestination("characteristics", "characteristicsLineDestination");

        midLocationDestinationMapper.deleteMidLocationDestinationByIds(characteristicsIds, "characteristics");
        RedisCache.locationDestination("characteristics", "characteristicsLocationDestination");

        midCarrierMapper.deleteMidCarrierByIds(characteristicsIds, "characteristics");
        RedisCache.midCarrier("characteristics", "characteristicsCarriers");

        int out = rsCharacteristicsMapper.deleteRsCharacteristicsByCharacteristicsIds(characteristicsIds);
        RedisCache.characteristics();
        return out;
    }

    /**
     * 删除物流注意事项信息
     *
     * @param characteristicsId 物流注意事项主键
     * @return 结果
     */
    @Override
    public int deleteRsCharacteristicsByCharacteristicsId(Long characteristicsId) {
        midCargoTypeMapper.deleteMidCargoTypeById(characteristicsId, "characteristics");
        RedisCache.midCargoType("characteristics", "characteristicsCargoType");

        midLineDepartureMapper.deleteMidLineDepartureById(characteristicsId, "characteristics");
        RedisCache.lineDeparture("characteristics", "characteristicsLineDeparture");

        midLocationDepartureMapper.deleteMidLocationDepartureById(characteristicsId, "characteristics");
        RedisCache.locationDeparture("characteristics", "characteristicsLocationDeparture");

        midLineDestinationMapper.deleteMidLineDestinationById(characteristicsId, "characteristics");
        RedisCache.lineDestination("characteristics", "characteristicsLineDestination");

        midLocationDestinationMapper.deleteMidLocationDestinationById(characteristicsId, "characteristics");
        RedisCache.locationDestination("characteristics", "characteristicsLocationDestination");

        midCarrierMapper.deleteMidCarrierById(characteristicsId, "characteristics");
        RedisCache.midCarrier("characteristics", "characteristicsCarriers");

        int out = rsCharacteristicsMapper.deleteRsCharacteristicsByCharacteristicsId(characteristicsId);
        RedisCache.characteristics();
        return out;
    }

    @Override
    public List<Long> selectCargoTypes(Long characteristicsId) {
        List<MidCargoType> midCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "characteristicsCargoType");
        if (midCargoTypes == null) {
            RedisCache.midCargoType("characteristics", "characteristicsCargoType");
            midCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "characteristicsCargoType");
        }
        List<Long> r = new ArrayList<>();
        for (MidCargoType midCargoType : midCargoTypes) {
            if (midCargoType.getBelongId().equals(characteristicsId)) {
                r.add(midCargoType.getCargoTypeId());
            }
        }
        return r;
    }

    @Override
    public List<Long> selectLocationDeparture(Long characteristicsId) {
        List<MidLocationDeparture> midLocationDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "characteristicsLocationDeparture");
        if (midLocationDepartures == null) {
            RedisCache.locationDeparture("characteristics", "characteristicsLocationDeparture");
            midLocationDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "characteristicsLocationDeparture");
        }
        List<Long> r = new ArrayList<>();
        for (MidLocationDeparture midLocationDeparture : midLocationDepartures) {
            if (midLocationDeparture.getBelongId().equals(characteristicsId)) {
                r.add(midLocationDeparture.getLocationId());
            }
        }
        return r;
    }

    @Override
    public List<Long> selectLocationDestination(Long characteristicsId) {
        List<MidLocationDestination> midLocationDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "characteristicsLocationDestination");
        if (midLocationDestinations == null) {
            RedisCache.locationDestination("characteristics", "characteristicsLocationDestination");
            midLocationDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "characteristicsLocationDestination");
        }
        List<Long> r = new ArrayList<>();
        for (MidLocationDestination midLocationDestination : midLocationDestinations) {
            if (midLocationDestination.getBelongId().equals(characteristicsId)) {
                r.add(midLocationDestination.getLocationId());
            }
        }
        return r;
    }

    @Override
    public List<Long> selectLineDeparture(Long characteristicsId) {
        List<MidLineDeparture> midLineDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "characteristicsLineDeparture");
        if (midLineDepartures == null) {
            RedisCache.lineDeparture("characteristics", "characteristicsLineDeparture");
            midLineDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "characteristicsLineDeparture");
        }
        List<Long> r = new ArrayList<>();
        for (MidLineDeparture midLineDeparture : midLineDepartures) {
            if (midLineDeparture.getBelongId().equals(characteristicsId)) {
                r.add(midLineDeparture.getLineId());
            }
        }
        return r;
    }

    @Override
    public List<Long> selectLineDestination(Long characteristicsId) {
        List<MidLineDestination> midLineDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "characteristicsLineDestination");
        if (midLineDestinations == null) {
            RedisCache.lineDestination("characteristics", "characteristicsLineDestination");
            midLineDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "characteristicsLineDestination");
        }
        List<Long> r = new ArrayList<>();
        for (MidLineDestination midLineDestination : midLineDestinations) {
            if (midLineDestination.getBelongId().equals(characteristicsId)) {
                r.add(midLineDestination.getLineId());
            }
        }
        return r;
    }

    @Override
    public List<Long> selectCarriers(Long characteristicsId) {
        List<MidCarrier> midCarriers = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "characteristicsCarriers");
        if (midCarriers == null) {
            RedisCache.midCarrier("characteristics", "characteristicsCarriers");
            midCarriers = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "characteristicsCarriers");
        }
        List<Long> r = new ArrayList<>();
        for (MidCarrier midCarrier : midCarriers) {
            if (midCarrier.getBelongId().equals(characteristicsId)) {
                r.add(midCarrier.getCarrierId());
            }
        }
        return r;
    }

    private void insertCargoType(@NotNull RsCharacteristics rsCharacteristics) {
        Long[] roles = rsCharacteristics.getCargoTypeIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidCargoType> list = new ArrayList<>();
            for (Long r : roles) {
                MidCargoType MidCargoType = new MidCargoType();
                MidCargoType.setBelongId(rsCharacteristics.getCharacteristicsId());
                MidCargoType.setBelongTo("characteristics");
                MidCargoType.setCargoTypeId(r);
                list.add(MidCargoType);
            }
            midCargoTypeMapper.batchCargoType(list);
        }
        RedisCache.midCargoType("characteristics", "characteristicsCargoType");
    }

    private void insertCarriers(@NotNull RsCharacteristics rsCharacteristics) {
        Long[] roles = rsCharacteristics.getCarrierIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidCarrier> list = new ArrayList<>();
            for (Long r : roles) {
                MidCarrier midCarrier = new MidCarrier();
                midCarrier.setBelongId(rsCharacteristics.getCharacteristicsId());
                midCarrier.setCarrierId(r);
                midCarrier.setBelongTo("characteristics");
                list.add(midCarrier);
            }
            midCarrierMapper.batchCarrier(list);
        }
        RedisCache.midCarrier("characteristics", "characteristicsCarriers");
    }

    private void insertLineDeparture(@NotNull RsCharacteristics rsCharacteristics) {
        Long[] roles = rsCharacteristics.getLineDepartureIds();
        List<MidLineDeparture> list = new ArrayList<>();
        if (StringUtils.isNotEmpty(roles)) {
            for (Long r : roles) {
                MidLineDeparture MidLineDeparture = new MidLineDeparture();
                MidLineDeparture.setBelongId(rsCharacteristics.getCharacteristicsId());
                MidLineDeparture.setLineId(r);
                MidLineDeparture.setBelongTo("characteristics");
                list.add(MidLineDeparture);
            }
            midLineDepartureMapper.batchLD(list);
        }
        RedisCache.lineDeparture("characteristics", "characteristicsLineDeparture");
    }

    private void insertLocationDeparture(@NotNull RsCharacteristics rsCharacteristics) {
        Long[] roles = rsCharacteristics.getLocationDepartureIds();
        List<MidLocationDeparture> list = new ArrayList<>();
        if (StringUtils.isNotEmpty(roles)) {
            for (Long r : roles) {
                MidLocationDeparture MidLocationDeparture = new MidLocationDeparture();
                MidLocationDeparture.setBelongId(rsCharacteristics.getCharacteristicsId());
                MidLocationDeparture.setLocationId(r);
                MidLocationDeparture.setBelongTo("characteristics");
                list.add(MidLocationDeparture);
            }
            midLocationDepartureMapper.batchLD(list);
        }
        RedisCache.locationDeparture("characteristics", "characteristicsLocationDeparture");
    }

    private void insertLocationDestination(@NotNull RsCharacteristics rsCharacteristics) {
        Long[] roles = rsCharacteristics.getLocationDestinationIds();
        List<MidLocationDestination> list = new ArrayList<>();
        if (StringUtils.isNotEmpty(roles)) {
            for (Long r : roles) {
                MidLocationDestination MidLocationDestination = new MidLocationDestination();
                MidLocationDestination.setBelongId(rsCharacteristics.getCharacteristicsId());
                MidLocationDestination.setLocationId(r);
                MidLocationDestination.setBelongTo("characteristics");
                list.add(MidLocationDestination);
            }
            midLocationDestinationMapper.batchLD(list);
        }
        RedisCache.locationDestination("characteristics", "characteristicsLocationDestination");
    }

    private void insertLineDestination(@NotNull RsCharacteristics rsCharacteristics) {
        Long[] roles = rsCharacteristics.getLineDestinationIds();
        List<MidLineDestination> list = new ArrayList<>();
        if (StringUtils.isNotEmpty(roles)) {
            for (Long r : roles) {
                MidLineDestination MidLineDestination = new MidLineDestination();
                MidLineDestination.setBelongId(rsCharacteristics.getCharacteristicsId());
                MidLineDestination.setLineId(r);
                MidLineDestination.setBelongTo("characteristics");
                list.add(MidLineDestination);
            }
            midLineDestinationMapper.batchLD(list);
        }
        RedisCache.lineDestination("characteristics", "characteristicsLineDestination");
    }
}
