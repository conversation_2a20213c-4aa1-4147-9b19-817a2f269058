package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsLocalCharge;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 物流附加费策略Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-14
 */
@Mapper
public interface RsLocalMapper {
    /**
     * 查询物流附加费策略
     *
     * @param localChargeId 物流附加费策略主键
     * @return 物流附加费策略
     */
    RsLocalCharge selectRsLocalByLocalId(Long localChargeId);

    /**
     * 查询物流附加费策略列表
     *
     * @param RsLocalCharge 物流附加费策略
     * @return 物流附加费策略集合
     */
    List<RsLocalCharge> selectRsLocalList(RsLocalCharge RsLocalCharge);

    /**
     * 新增物流附加费策略
     *
     * @param RsLocalCharge 物流附加费策略
     * @return 结果
     */
    int insertRsLocal(RsLocalCharge RsLocalCharge);

    /**
     * 修改物流附加费策略
     *
     * @param RsLocalCharge 物流附加费策略
     * @return 结果
     */
    int updateRsLocal(RsLocalCharge RsLocalCharge);

    /**
     * 删除物流附加费策略
     *
     * @param localChargeId 物流附加费策略主键
     * @return 结果
     */
    int deleteRsLocalByLocalId(Long localChargeId);

    /**
     * 批量删除物流附加费策略
     *
     * @param localIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsLocalByLocalIds(Long[] localIds);
}
