package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasPaymentType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 收付顺序Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-06-16
 */
@Mapper
public interface BasPaymentTypeMapper 
{
    /**
     * 查询收付顺序
     * 
     * @param paymentTypeId 收付顺序主键
     * @return 收付顺序
     */
        BasPaymentType selectBasPaymentTypeByPaymentTypeId(Long paymentTypeId);

    /**
     * 查询收付顺序列表
     * 
     * @param basPaymentType 收付顺序
     * @return 收付顺序集合
     */
    List<BasPaymentType> selectBasPaymentTypeList(BasPaymentType basPaymentType);

    /**
     * 新增收付顺序
     * 
     * @param basPaymentType 收付顺序
     * @return 结果
     */
    int insertBasPaymentType(BasPaymentType basPaymentType);

    /**
     * 修改收付顺序
     * 
     * @param basPaymentType 收付顺序
     * @return 结果
     */
    int updateBasPaymentType(BasPaymentType basPaymentType);

    /**
     * 删除收付顺序
     * 
     * @param paymentTypeId 收付顺序主键
     * @return 结果
     */
    int deleteBasPaymentTypeByPaymentTypeId(Long paymentTypeId);

    /**
     * 批量删除收付顺序
     * 
     * @param paymentTypeIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasPaymentTypeByPaymentTypeIds(Long[] paymentTypeIds);
}
