package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsOpAir;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsOpAirService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 空运服务Controller
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@RestController
@RequestMapping("/system/opair")
public class RsOpAirController extends BaseController {
    @Autowired
    private RsOpAirService rsOpAirService;

    /**
     * 查询空运服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opair:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsOpAir rsOpAir) {
        startPage();
        List<RsOpAir> list = rsOpAirService.selectRsOpAirList(rsOpAir);
        return getDataTable(list);
    }

    /**
     * 导出空运服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opair:export')")
    @Log(title = "空运服务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsOpAir rsOpAir) {
        List<RsOpAir> list = rsOpAirService.selectRsOpAirList(rsOpAir);
        ExcelUtil<RsOpAir> util = new ExcelUtil<RsOpAir>(RsOpAir.class);
        util.exportExcel(response, list, "空运服务数据");
    }

    /**
     * 获取空运服务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:opair:query')")
    @GetMapping(value = "/{airId}")
    public AjaxResult getInfo(@PathVariable("airId") Long airId) {
        return AjaxResult.success(rsOpAirService.selectRsOpAirByAirId(airId));
    }

    /**
     * 新增空运服务
     */
    @PreAuthorize("@ss.hasPermi('system:opair:add')")
    @Log(title = "空运服务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsOpAir rsOpAir) {
        return toAjax(rsOpAirService.insertRsOpAir(rsOpAir));
    }

    /**
     * 修改空运服务
     */
    @PreAuthorize("@ss.hasPermi('system:opair:edit')")
    @Log(title = "空运服务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsOpAir rsOpAir) {
        return toAjax(rsOpAirService.updateRsOpAir(rsOpAir));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:opair:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsOpAir rsOpAir) {
        rsOpAir.setUpdateBy(getUserId());
        return toAjax(rsOpAirService.changeStatus(rsOpAir));
    }

    /**
     * 删除空运服务
     */
    @PreAuthorize("@ss.hasPermi('system:opair:remove')")
    @Log(title = "空运服务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{airIds}")
    public AjaxResult remove(@PathVariable Long[] airIds) {
        return toAjax(rsOpAirService.deleteRsOpAirByAirIds(airIds));
    }
}
