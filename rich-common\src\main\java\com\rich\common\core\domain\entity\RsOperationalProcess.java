package com.rich.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

import java.util.Date;
import java.util.List;

/**
 * 操作进度对象 rs_operational_process
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
public class RsOperationalProcess extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 操作进度流水
     */
    private Long operationalProcessId;

    /**
     * 操作单号
     */
    @Excel(name = "操作单号")
    private String operationNo;

    /**
     * 服务类型
     */
    @Excel(name = "服务类型")
    private Long serviceTypeId;

    private Long typeId;

    private Long basicInfoId;

    private String serviceType;

    /**
     * 服务ID
     */
    @Excel(name = "服务ID")
    private Long rctId;

    /**
     * 进度名称
     */
    @Excel(name = "进度名称")
    private Long processId;
    private String process;

    /**
     * 进度状态
     */
    @Excel(name = "进度状态")
    private Long processStatusId;
    private String processStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processStatusTime;

    private Long senderId;

    private String sender;

    private Long receiverId;

    private String receiver;

    private Long releaseWayId;

    private String releaseWay;

    private Long opId;

    private String opName;

    private List<RsDocDetail> docList;

    public List<RsDocDetail> getDocList() {
        return docList;
    }

    public void setDocList(List<RsDocDetail> docList) {
        this.docList = docList;
    }

    public Date getProcessStatusTime() {
        return processStatusTime;
    }

    public void setProcessStatusTime(Date processStatusTime) {
        this.processStatusTime = processStatusTime;
    }

    public Long getSenderId() {
        return senderId;
    }

    public void setSenderId(Long senderId) {
        this.senderId = senderId;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public Long getReceiverId() {
        return receiverId;
    }

    public void setReceiverId(Long receiverId) {
        this.receiverId = receiverId;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public Long getReleaseWayId() {
        return releaseWayId;
    }

    public void setReleaseWayId(Long releaseWayId) {
        this.releaseWayId = releaseWayId;
    }

    public String getReleaseWay() {
        return releaseWay;
    }

    public void setReleaseWay(String releaseWay) {
        this.releaseWay = releaseWay;
    }

    public Long getOpId() {
        return opId;
    }

    public void setOpId(Long opId) {
        this.opId = opId;
    }

    public String getOpName() {
        return opName;
    }

    public void setOpName(String opName) {
        this.opName = opName;
    }

    public Long getServiceTypeId() {
        return serviceTypeId;
    }

    public void setServiceTypeId(Long serviceTypeId) {
        this.serviceTypeId = serviceTypeId;
    }

    public Long getTypeId() {
        return typeId;
    }

    public void setTypeId(Long typeId) {
        this.typeId = typeId;
    }

    public Long getBasicInfoId() {
        return basicInfoId;
    }

    public void setBasicInfoId(Long basicInfoId) {
        this.basicInfoId = basicInfoId;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public String getProcess() {
        return process;
    }

    public void setProcess(String process) {
        this.process = process;
    }

    public String getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(String processStatus) {
        this.processStatus = processStatus;
    }

    public void setOperationalProcessId(Long operationalProcessId) {
        this.operationalProcessId = operationalProcessId;
    }

    public Long getOperationalProcessId() {
        return operationalProcessId;
    }

    public void setOperationNo(String operationNo) {
        this.operationNo = operationNo;
    }

    public String getOperationNo() {
        return operationNo;
    }

    public void setRctId(Long rctId) {
        this.rctId = rctId;
    }

    public Long getRctId() {
        return rctId;
    }

    public void setProcessId(Long processId) {
        this.processId = processId;
    }

    public Long getProcessId() {
        return processId;
    }

    public void setProcessStatusId(Long processStatusId) {
        this.processStatusId = processStatusId;
    }

    public Long getProcessStatusId() {
        return processStatusId;
    }
}
