package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasDistUnit;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2022-08-30
 */
@Mapper
public interface BasDistUnitMapper {
    /**
     * 查询【请填写功能名称】
     *
     * @param unitId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    BasDistUnit selectBasUnitByUnitId(Long unitId);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param basDistUnit 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    List<BasDistUnit> selectBasUnitList(BasDistUnit basDistUnit);

    /**
     * 新增【请填写功能名称】
     *
     * @param basDistUnit 【请填写功能名称】
     * @return 结果
     */
    int insertBasUnit(BasDistUnit basDistUnit);

    /**
     * 修改【请填写功能名称】
     *
     * @param basDistUnit 【请填写功能名称】
     * @return 结果
     */
    int updateBasUnit(BasDistUnit basDistUnit);

    /**
     * 删除【请填写功能名称】
     *
     * @param unitId 【请填写功能名称】主键
     * @return 结果
     */
    int deleteBasUnitByUnitId(Long unitId);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param unitIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasUnitByUnitIds(Long[] unitIds);

}
