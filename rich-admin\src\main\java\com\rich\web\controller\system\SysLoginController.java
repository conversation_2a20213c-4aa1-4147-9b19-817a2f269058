package com.rich.web.controller.system;

import com.rich.common.annotation.RateLimiter;
import com.rich.common.constant.Constants;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.RsStaff;
import com.rich.common.core.domain.entity.SysMenu;
import com.rich.common.core.domain.model.LoginBody;
import com.rich.common.enums.LimitType;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.ip.IpUtils;
import com.rich.framework.web.service.SysLoginService;
import com.rich.framework.web.service.SysPermissionService;
import com.rich.system.service.RsStaffService;
import com.rich.system.service.SysMenuService;
import com.rich.system.service.impl.RedisCacheImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@RestController

public class SysLoginController {

    @Autowired
    private SysLoginService loginService;
    @Autowired
    private SysMenuService menuService;
    @Autowired
    private SysPermissionService permissionService;
    @Autowired
    private RedisCacheImpl RedisCache;
    @Autowired
    private RsStaffService rsStaffService;

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody) {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(), loginBody.getUuid(),loginBody.getUnid());
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo() {
        RsStaff user = SecurityUtils.getLoginUser().getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        // 权限等级集合
        Map<String, List<Long>> permissionLevelList = rsStaffService.selectPermissionLevelList(user.getStaffId());
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        ajax.put("permissionLevelList", permissionLevelList);
        return ajax;
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters() {
        RsStaff user = SecurityUtils.getLoginUser().getUser();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(user);
        return AjaxResult.success(menuService.buildMenus(menus));
    }

    @GetMapping("getExist")
    public AjaxResult getExist(){
        return AjaxResult.success(RedisCache.exist());
    }

}
