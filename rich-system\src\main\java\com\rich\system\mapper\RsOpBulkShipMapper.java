package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpBulkShip;
import org.apache.ibatis.annotations.Mapper;

/**
 * 散杂船服务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Mapper
public interface RsOpBulkShipMapper {
    /**
     * 查询散杂船服务
     *
     * @param bulkShipId 散杂船服务主键
     * @return 散杂船服务
     */
    RsOpBulkShip selectRsOpBulkShipByBulkShipId(Long bulkShipId);

    /**
     * 查询散杂船服务列表
     *
     * @param rsOpBulkShip 散杂船服务
     * @return 散杂船服务集合
     */
    List<RsOpBulkShip> selectRsOpBulkShipList(RsOpBulkShip rsOpBulkShip);

    /**
     * 新增散杂船服务
     *
     * @param rsOpBulkShip 散杂船服务
     * @return 结果
     */
    int insertRsOpBulkShip(RsOpBulkShip rsOpBulkShip);

    /**
     * 修改散杂船服务
     *
     * @param rsOpBulkShip 散杂船服务
     * @return 结果
     */
    int updateRsOpBulkShip(RsOpBulkShip rsOpBulkShip);

    /**
     * 删除散杂船服务
     *
     * @param bulkShipId 散杂船服务主键
     * @return 结果
     */
    int deleteRsOpBulkShipByBulkShipId(Long bulkShipId);

    /**
     * 批量删除散杂船服务
     *
     * @param bulkShipIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsOpBulkShipByBulkShipIds(Long[] bulkShipIds);

    RsOpBulkShip selectRsOpBulkShipByRctId(Long rctId);

    void deleteRsOpBulkTruckByRctIdAndServiceTypeId(Long rctId, long l);
}
