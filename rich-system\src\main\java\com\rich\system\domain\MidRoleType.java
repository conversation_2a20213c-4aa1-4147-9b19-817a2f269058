package com.rich.system.domain;

import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 【请填写功能名称】对象 mid_role_type_menu
 *
 * <AUTHOR>
 * @date 2022-12-06
 */
public class MidRoleType extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long roleTypeId;

    private Long belongId;

    private String belongTo;

    public Long getRoleTypeId() {
        return roleTypeId;
    }

    public void setRoleTypeId(Long roleTypeId) {
        this.roleTypeId = roleTypeId;
    }

    public Long getBelongId() {
        return belongId;
    }

    public void setBelongId(Long belongId) {
        this.belongId = belongId;
    }

    public String getBelongTo() {
        return belongTo;
    }

    public void setBelongTo(String belongTo) {
        this.belongTo = belongTo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("roleTypeId", getRoleTypeId())
                .append("belongId", getBelongId())
                .append("belongTo", getBelongTo())
                .toString();
    }
}
