package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsBooking;
import org.apache.ibatis.annotations.Mapper;

/**
 * 订舱单列表Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-15
 */
@Mapper
public interface RsBookingMapper 
{
    /**
     * 查询订舱单列表
     * 
     * @param bookingId 订舱单列表主键
     * @return 订舱单列表
     */
        RsBooking selectRsBookingByBookingId(Long bookingId);

    /**
     * 查询订舱单列表列表
     * 
     * @param rsBooking 订舱单列表
     * @return 订舱单列表集合
     */
    List<RsBooking> selectRsBookingList(RsBooking rsBooking);

    /**
     * 新增订舱单列表
     * 
     * @param rsBooking 订舱单列表
     * @return 结果
     */
    int insertRsBooking(RsBooking rsBooking);

    /**
     * 修改订舱单列表
     * 
     * @param rsBooking 订舱单列表
     * @return 结果
     */
    int updateRsBooking(RsBooking rsBooking);

    /**
     * 删除订舱单列表
     * 
     * @param bookingId 订舱单列表主键
     * @return 结果
     */
    int deleteRsBookingByBookingId(Long bookingId);

    /**
     * 批量删除订舱单列表
     * 
     * @param bookingIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsBookingByBookingIds(Long[] bookingIds);

    int getMon();
}
