package com.rich.common.core.domain.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 商务订舱对象 rs_psa_rct
 *
 * <AUTHOR>
 * @date 2024-07-01
 */
public class RsPsaRct extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long psaRctId;

    /**
     * 商务单号
     */
    @Excel(name = "商务单号")
    private String psaNo;

    /**
     * 收款抬头 ,瑞旗收款公司(账号的抬头)
     */
    @Excel(name = "收款抬头 ,瑞旗收款公司(账号的抬头)")
    private String paymentTitleCode;

    /**
     * 进出口类型 ,
     */
    @Excel(name = "进出口类型 ,")
    private String impExpType;

    /**
     * 贸易条款 ,
     */
    @Excel(name = "贸易条款 ,")
    private String tradingTerms;

    /**
     * 运输条款 ,
     */
    @Excel(name = "运输条款 ,")
    private String logisticsTerms;

    /**
     * 收汇方式 ,客户与他的客户/供应商之间的收付方式
     */
    @Excel(name = "收汇方式 ,客户与他的客户/供应商之间的收付方式")
    private String tradingPaymentChannel;

    /**
     * 货名概要 ,
     */
    @Excel(name = "货名概要 ,")
    private String goodsNameSummary;

    /**
     * 件数 ,
     */
    @Excel(name = "件数 ,")
    private Long packageQuantity;

    /**
     * 体积 ,
     */
    @Excel(name = "体积 ,")
    private BigDecimal goodsVolume;

    /**
     * 毛重 ,
     */
    @Excel(name = "毛重 ,")
    private BigDecimal grossWeight;

    /**
     * 货值币种 ,
     */
    @Excel(name = "货值币种 ,")
    private String goodsCurrencyCode;

    /**
     * 货值 ,原：cargo_price
     */
    @Excel(name = "货值 ,原：cargo_price")
    private BigDecimal goodsValue;

    /**
     * 物流类型 ,在此，与主服务类型合用一个字段
     */
    @Excel(name = "物流类型 ,在此，与主服务类型合用一个字段")
    private Long logisticsTypeId;

    /**
     * 计费货量 ,
     */
    @Excel(name = "计费货量 ,")
    private String revenueTon;

    /**
     * 启运港 ,原：departure_id，询价/订舱起点
     */
    @Excel(name = "启运港 ,原：departure_id，询价/订舱起点")
    private Long polId;

    /**
     * 中转港 ,起运地境外中转港，for tracking
     */
    @Excel(name = "中转港 ,起运地境外中转港，for tracking")
    private Long transitPortId;

    /**
     * 卸货港 ,境外卸货大港，for bl&tracking
     */
    @Excel(name = "卸货港 ,境外卸货大港，for bl&tracking")
    private Long podId;

    /**
     * 目的港 ,询价/订舱终点，for bl&tracking
     */
    @Excel(name = "目的港 ,询价/订舱终点，for bl&tracking")
    private Long destinationPortId;

    /**
     * 截关时间 ,（启运港截关时间）
     */
    @Excel(name = "截关时间 ,", readConverterExp = "启=运港截关时间")
    private Date cvClosingTime;

    /**
     * 截补料 ,（时间）
     */
    @Excel(name = "截补料 ,", readConverterExp = "时=间")
    private Date siClosingTime;

    /**
     * 头程船名 ,
     */
    @Excel(name = "头程船名 ,")
    private String firstVessel;

    /**
     * 头程航次 ,
     */
    @Excel(name = "头程航次 ,")
    private String firstVoyage;

    /**
     * 头程开舱 ,（时间）
     */
    @Excel(name = "头程开舱 ,", readConverterExp = "时=间")
    private Date firstCyOpenTime;

    /**
     * 头程截重 ,（时间）
     */
    @Excel(name = "头程截重 ,", readConverterExp = "时=间")
    private Date firstCyClosingTime;

    /**
     * 头程装船 ,（时间）
     */
    @Excel(name = "头程装船 ,", readConverterExp = "时=间")
    private Date firstEtd;

    /**
     * 基港装船 ,（时间）
     */
    @Excel(name = "基港装船 ,", readConverterExp = "时=间")
    private Date basicEtd;

    /**
     * 卸货港到达 ,（时间）
     */
    @Excel(name = "卸货港到达 ,", readConverterExp = "时=间")
    private Date podEta;

    /**
     * 目的港到达 ,（时间）
     */
    @Excel(name = "目的港到达 ,", readConverterExp = "时=间")
    private Date destinationPortEta;

    /**
     * 承运人 ,
     */
    @Excel(name = "承运人 ,")
    private Long carrierId;

    /**
     * 航班时效综述 ,商务提供的船期综述
     */
    @Excel(name = "航班时效综述 ,商务提供的船期综述")
    private String inquiryScheduleSummary;

    /**
     * 服务类型List ,服务类型List集合字符串（逗号分隔）
     */
    @Excel(name = "服务类型List ,服务类型List集合字符串", readConverterExp = "逗=号分隔")
    private String serviceTypeIdList;

    /**
     * SO号码
     */
    @Excel(name = "SO号码")
    private String soNo;

    /**
     * 提单号码
     */
    @Excel(name = "提单号码")
    private String blNo;

    /**
     * 柜号和封条汇总 ,作为冗余字段
     */
    @Excel(name = "柜号和封条汇总 ,作为冗余字段")
    private String sqdContainersSealsSum;

    /**
     * 报价单号 ,（业务 QTT）
     */
    @Excel(name = "报价单号 ,", readConverterExp = "业=务,Q=TT")
    private String qoutationNo;

    /**
     * 业务报价综述 ,（给客户）
     */
    @Excel(name = "业务报价综述 ,", readConverterExp = "给=客户")
    private String qoutationSketch;

    /**
     * 业务员 ,
     */
    @Excel(name = "业务员 ,")
    private Long salesId;

    /**
     * 报价日期 ,
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "报价日期 ,", width = 30, dateFormat = "yyyy-MM-dd")
    private Date qoutationTime;

    /**
     * 订舱单号 ,（NBN）
     */
    @Excel(name = "订舱单号 ,", readConverterExp = "N=BN")
    private String newBookingNo;

    /**
     * 业务订舱备注 ,（给操作）
     */
    @Excel(name = "业务订舱备注 ,", readConverterExp = "给=操作")
    private String newBookingRemark;

    /**
     * 业务助理 ,
     */
    @Excel(name = "业务助理 ,")
    private Long salesAssistantId;

    /**
     * 协助业务员 ,
     */
    @Excel(name = "协助业务员 ,")
    private Long salesObserverId;

    /**
     * 订舱日期 ,
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "订舱日期 ,", width = 30, dateFormat = "yyyy-MM-dd")
    private Date newBookingTime;

    /**
     * 业务须知汇总 ,具体物流服务（商务给业务）
     */
    @Excel(name = "业务须知汇总 ,具体物流服务", readConverterExp = "商=务给业务")
    private String inquiryNoticeSum;

    /**
     * 商务备注汇总 ,具体物流服务（商务给操作）
     */
    @Excel(name = "商务备注汇总 ,具体物流服务", readConverterExp = "商=务给操作")
    private String inquiryInnerRemarkSum;

    /**
     * AMS/ENS ,作为临时冗余字段
     */
    @Excel(name = "AMS/ENS ,作为临时冗余字段")
    private String sqdAmsEnsPostStatus;

    /**
     * ISF/EMNF ,作为临时冗余字段
     */
    @Excel(name = "ISF/EMNF ,作为临时冗余字段")
    private String sqdIsfEmnfPostStatus;

    /**
     * 交单方式 ,作为冗余字段
     */
    @Excel(name = "交单方式 ,作为冗余字段")
    private String sqdDocDeliveryWay;

    /**
     * 发货人 ,客户填入的第一信息，仅供参考
     */
    @Excel(name = "发货人 ,客户填入的第一信息，仅供参考")
    private String bookingShipper;

    /**
     * 收货人 ,客户填入的第一信息，仅供参考
     */
    @Excel(name = "收货人 ,客户填入的第一信息，仅供参考")
    private String bookingConsignee;

    /**
     * 通知人 ,客户填入的第一信息，仅供参考
     */
    @Excel(name = "通知人 ,客户填入的第一信息，仅供参考")
    private String bookingNotifyParty;

    /**
     * 不可中转
     */
    @Excel(name = "不可中转")
    private String noTransferAllowed;

    /**
     * 不可分批
     */
    @Excel(name = "不可分批")
    private String noDividedAllowed;

    /**
     * 不可套约
     */
    @Excel(name = "不可套约")
    private String noAgreementShowed;

    /**
     * 属地清关
     */
    @Excel(name = "属地清关")
    private String isCustomsIntransitShowed;

    /**
     * 实际装船日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "实际装船日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date firstAtd;

    /**
     * 实际到达日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "实际到达日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date destinationPortAta;

    /**
     * 操作向订舱口订舱时的备注
     */
    @Excel(name = "操作向订舱口订舱时的备注")
    private String bookingAgentRemark;

    /**
     * 唛头
     */
    @Excel(name = "唛头")
    private String shippingMark;

    /**
     * 运费付于
     */
    @Excel(name = "运费付于")
    private String freightPaidWayCode;

    /**
     * 箱型特征
     */
    @Excel(name = "箱型特征")
    private String ctnrTypeCode;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date etd;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date eta;
    private String polName;
    private String podName;
    private String logisticsTypeEnName;
    private String pol;
    private String destinationPort;
    private String carrierEnName;
    private String bookingStatus;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date bookingTime;
    private Long bookingId;
    private String mainChargeUnitCode;
    private BigDecimal mainChargeRate;
    private String mainChargeCurrencyCode;
    private String distributionStatus;
    private String blTypeCode;
    private String sqdIssueType;
    private String clientContractNo;
    private String bookingRemark;
    private String inquiryNo;
    private Long supplierId;
    private Long opId;
    private Long psaId;
    private String rctNo;
    private String clientShortName;

    private String cargoTypeCodeSum;
    private String agreementTypeCode;
    private String supplierName;
    private String settledRate;
    private String purchaseCost;
    private String balanceProfit;
    private BigDecimal mainChargeRateA;
    private BigDecimal mainChargeRateB;
    private BigDecimal mainChargeRateC;
    private BigDecimal settledRateA;
    private BigDecimal settledRateB;
    private BigDecimal settledRateC;
    private BigDecimal balanceProfitA;
    private BigDecimal balanceProfitB;
    private BigDecimal balanceProfitC;

    public BigDecimal getBalanceProfitC() {
        return balanceProfitC;
    }

    public void setBalanceProfitC(BigDecimal balanceProfitC) {
        this.balanceProfitC = balanceProfitC;
    }

    public BigDecimal getBalanceProfitB() {
        return balanceProfitB;
    }

    public void setBalanceProfitB(BigDecimal balanceProfitB) {
        this.balanceProfitB = balanceProfitB;
    }

    public BigDecimal getBalanceProfitA() {
        return balanceProfitA;
    }

    public void setBalanceProfitA(BigDecimal balanceProfitA) {
        this.balanceProfitA = balanceProfitA;
    }

    public BigDecimal getSettledRateC() {
        return settledRateC;
    }

    public void setSettledRateC(BigDecimal settledRateC) {
        this.settledRateC = settledRateC;
    }

    public BigDecimal getSettledRateB() {
        return settledRateB;
    }

    public void setSettledRateB(BigDecimal settledRateB) {
        this.settledRateB = settledRateB;
    }

    public BigDecimal getSettledRateA() {
        return settledRateA;
    }

    public void setSettledRateA(BigDecimal settledRateA) {
        this.settledRateA = settledRateA;
    }

    public BigDecimal getMainChargeRateC() {
        return mainChargeRateC;
    }

    public void setMainChargeRateC(BigDecimal mainChargeRateC) {
        this.mainChargeRateC = mainChargeRateC;
    }

    public BigDecimal getMainChargeRateB() {
        return mainChargeRateB;
    }

    public void setMainChargeRateB(BigDecimal mainChargeRateB) {
        this.mainChargeRateB = mainChargeRateB;
    }

    public BigDecimal getMainChargeRateA() {
        return mainChargeRateA;
    }

    public void setMainChargeRateA(BigDecimal mainChargeRateA) {
        this.mainChargeRateA = mainChargeRateA;
    }

    public String getBalanceProfit() {
        return balanceProfit;
    }

    public void setBalanceProfit(String balanceProfit) {
        this.balanceProfit = balanceProfit;
    }

    public String getPurchaseCost() {
        return purchaseCost;
    }

    public void setPurchaseCost(String purchaseCost) {
        this.purchaseCost = purchaseCost;
    }

    public String getSettledRate() {
        return settledRate;
    }

    public void setSettledRate(String settledRate) {
        this.settledRate = settledRate;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getAgreementTypeCode() {
        return agreementTypeCode;
    }

    public void setAgreementTypeCode(String agreementTypeCode) {
        this.agreementTypeCode = agreementTypeCode;
    }

    public String getCargoTypeCodeSum() {
        return cargoTypeCodeSum;
    }

    public void setCargoTypeCodeSum(String cargoTypeCodeSum) {
        this.cargoTypeCodeSum = cargoTypeCodeSum;
    }

    public String getClientShortName() {
        return clientShortName;
    }

    public void setClientShortName(String clientShortName) {
        this.clientShortName = clientShortName;
    }

    public String getRctNo() {
        return rctNo;
    }

    public void setRctNo(String rctNo) {
        this.rctNo = rctNo;
    }

    public Long getPsaId() {
        return psaId;
    }

    public void setPsaId(Long psaId) {
        this.psaId = psaId;
    }

    public Long getOpId() {
        return opId;
    }

    public void setOpId(Long opId) {
        this.opId = opId;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public String getInquiryNo() {
        return inquiryNo;
    }

    public void setInquiryNo(String inquiryNo) {
        this.inquiryNo = inquiryNo;
    }

    public String getBookingRemark() {
        return bookingRemark;
    }

    public void setBookingRemark(String bookingRemark) {
        this.bookingRemark = bookingRemark;
    }

    public String getClientContractNo() {
        return clientContractNo;
    }

    public void setClientContractNo(String clientContractNo) {
        this.clientContractNo = clientContractNo;
    }

    public String getSqdIssueType() {
        return sqdIssueType;
    }

    public void setSqdIssueType(String sqdIssueType) {
        this.sqdIssueType = sqdIssueType;
    }

    public String getBlTypeCode() {
        return blTypeCode;
    }

    public void setBlTypeCode(String blTypeCode) {
        this.blTypeCode = blTypeCode;
    }

    public String getDistributionStatus() {
        return distributionStatus;
    }

    public void setDistributionStatus(String distributionStatus) {
        this.distributionStatus = distributionStatus;
    }

    public String getMainChargeCurrencyCode() {
        return mainChargeCurrencyCode;
    }

    public void setMainChargeCurrencyCode(String mainChargeCurrencyCode) {
        this.mainChargeCurrencyCode = mainChargeCurrencyCode;
    }

    public BigDecimal getMainChargeRate() {
        return mainChargeRate;
    }

    public void setMainChargeRate(BigDecimal mainChargeRate) {
        this.mainChargeRate = mainChargeRate;
    }

    public String getMainChargeUnitCode() {
        return mainChargeUnitCode;
    }

    public void setMainChargeUnitCode(String mainChargeUnitCode) {
        this.mainChargeUnitCode = mainChargeUnitCode;
    }

    public Long getBookingId() {
        return bookingId;
    }

    public void setBookingId(Long bookingId) {
        this.bookingId = bookingId;
    }

    public Date getBookingTime() {
        return bookingTime;
    }

    public void setBookingTime(Date bookingTime) {
        this.bookingTime = bookingTime;
    }

    public String getBookingStatus() {
        return bookingStatus;
    }

    public void setBookingStatus(String bookingStatus) {
        this.bookingStatus = bookingStatus;
    }

    public String getCarrierEnName() {
        return carrierEnName;
    }

    public void setCarrierEnName(String carrierEnName) {
        this.carrierEnName = carrierEnName;
    }

    public String getDestinationPort() {
        return destinationPort;
    }

    public void setDestinationPort(String destinationPort) {
        this.destinationPort = destinationPort;
    }

    public String getPol() {
        return pol;
    }

    public void setPol(String pol) {
        this.pol = pol;
    }

    public String getLogisticsTypeEnName() {
        return logisticsTypeEnName;
    }

    public void setLogisticsTypeEnName(String logisticsTypeEnName) {
        this.logisticsTypeEnName = logisticsTypeEnName;
    }

    public String getPodName() {
        return podName;
    }

    public void setPodName(String podName) {
        this.podName = podName;
    }

    public String getPolName() {
        return polName;
    }

    public void setPolName(String polName) {
        this.polName = polName;
    }

    public Long getPsaRctId() {
        return psaRctId;
    }

    public void setPsaRctId(Long psaRctId) {
        this.psaRctId = psaRctId;
    }

    public String getPsaNo() {
        return psaNo;
    }

    public void setPsaNo(String psaNo) {
        this.psaNo = psaNo;
    }

    public String getPaymentTitleCode() {
        return paymentTitleCode;
    }

    public void setPaymentTitleCode(String paymentTitleCode) {
        this.paymentTitleCode = paymentTitleCode;
    }

    public String getImpExpType() {
        return impExpType;
    }

    public void setImpExpType(String impExpType) {
        this.impExpType = impExpType;
    }

    public String getTradingTerms() {
        return tradingTerms;
    }

    public void setTradingTerms(String tradingTerms) {
        this.tradingTerms = tradingTerms;
    }

    public String getLogisticsTerms() {
        return logisticsTerms;
    }

    public void setLogisticsTerms(String logisticsTerms) {
        this.logisticsTerms = logisticsTerms;
    }

    public String getTradingPaymentChannel() {
        return tradingPaymentChannel;
    }

    public void setTradingPaymentChannel(String tradingPaymentChannel) {
        this.tradingPaymentChannel = tradingPaymentChannel;
    }

    public String getGoodsNameSummary() {
        return goodsNameSummary;
    }

    public void setGoodsNameSummary(String goodsNameSummary) {
        this.goodsNameSummary = goodsNameSummary;
    }

    public Long getPackageQuantity() {
        return packageQuantity;
    }

    public void setPackageQuantity(Long packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public BigDecimal getGoodsVolume() {
        return goodsVolume;
    }

    public void setGoodsVolume(BigDecimal goodsVolume) {
        this.goodsVolume = goodsVolume;
    }

    public BigDecimal getGrossWeight() {
        return grossWeight;
    }

    public void setGrossWeight(BigDecimal grossWeight) {
        this.grossWeight = grossWeight;
    }

    public String getGoodsCurrencyCode() {
        return goodsCurrencyCode;
    }

    public void setGoodsCurrencyCode(String goodsCurrencyCode) {
        this.goodsCurrencyCode = goodsCurrencyCode;
    }

    public BigDecimal getGoodsValue() {
        return goodsValue;
    }

    public void setGoodsValue(BigDecimal goodsValue) {
        this.goodsValue = goodsValue;
    }

    public Long getLogisticsTypeId() {
        return logisticsTypeId;
    }

    public void setLogisticsTypeId(Long logisticsTypeId) {
        this.logisticsTypeId = logisticsTypeId;
    }

    public String getRevenueTon() {
        return revenueTon;
    }

    public void setRevenueTon(String revenueTon) {
        this.revenueTon = revenueTon;
    }

    public Long getPolId() {
        return polId;
    }

    public void setPolId(Long polId) {
        this.polId = polId;
    }

    public Long getTransitPortId() {
        return transitPortId;
    }

    public void setTransitPortId(Long transitPortId) {
        this.transitPortId = transitPortId;
    }

    public Long getPodId() {
        return podId;
    }

    public void setPodId(Long podId) {
        this.podId = podId;
    }

    public Long getDestinationPortId() {
        return destinationPortId;
    }

    public void setDestinationPortId(Long destinationPortId) {
        this.destinationPortId = destinationPortId;
    }

    public Date getCvClosingTime() {
        return cvClosingTime;
    }

    public void setCvClosingTime(Date cvClosingTime) {
        this.cvClosingTime = cvClosingTime;
    }

    public Date getSiClosingTime() {
        return siClosingTime;
    }

    public void setSiClosingTime(Date siClosingTime) {
        this.siClosingTime = siClosingTime;
    }

    public String getFirstVessel() {
        return firstVessel;
    }

    public void setFirstVessel(String firstVessel) {
        this.firstVessel = firstVessel;
    }

    public String getFirstVoyage() {
        return firstVoyage;
    }

    public void setFirstVoyage(String firstVoyage) {
        this.firstVoyage = firstVoyage;
    }

    public Date getFirstCyOpenTime() {
        return firstCyOpenTime;
    }

    public void setFirstCyOpenTime(Date firstCyOpenTime) {
        this.firstCyOpenTime = firstCyOpenTime;
    }

    public Date getFirstCyClosingTime() {
        return firstCyClosingTime;
    }

    public void setFirstCyClosingTime(Date firstCyClosingTime) {
        this.firstCyClosingTime = firstCyClosingTime;
    }

    public Date getFirstEtd() {
        return firstEtd;
    }

    public void setFirstEtd(Date firstEtd) {
        this.firstEtd = firstEtd;
    }

    public Date getBasicEtd() {
        return basicEtd;
    }

    public void setBasicEtd(Date basicEtd) {
        this.basicEtd = basicEtd;
    }

    public Date getPodEta() {
        return podEta;
    }

    public void setPodEta(Date podEta) {
        this.podEta = podEta;
    }

    public Date getDestinationPortEta() {
        return destinationPortEta;
    }

    public void setDestinationPortEta(Date destinationPortEta) {
        this.destinationPortEta = destinationPortEta;
    }

    public Long getCarrierId() {
        return carrierId;
    }

    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    public String getInquiryScheduleSummary() {
        return inquiryScheduleSummary;
    }

    public void setInquiryScheduleSummary(String inquiryScheduleSummary) {
        this.inquiryScheduleSummary = inquiryScheduleSummary;
    }

    public String getServiceTypeIdList() {
        return serviceTypeIdList;
    }

    public void setServiceTypeIdList(String serviceTypeIdList) {
        this.serviceTypeIdList = serviceTypeIdList;
    }

    public String getSoNo() {
        return soNo;
    }

    public void setSoNo(String soNo) {
        this.soNo = soNo;
    }

    public String getBlNo() {
        return blNo;
    }

    public void setBlNo(String blNo) {
        this.blNo = blNo;
    }

    public String getSqdContainersSealsSum() {
        return sqdContainersSealsSum;
    }

    public void setSqdContainersSealsSum(String sqdContainersSealsSum) {
        this.sqdContainersSealsSum = sqdContainersSealsSum;
    }

    public String getQoutationNo() {
        return qoutationNo;
    }

    public void setQoutationNo(String qoutationNo) {
        this.qoutationNo = qoutationNo;
    }

    public String getQoutationSketch() {
        return qoutationSketch;
    }

    public void setQoutationSketch(String qoutationSketch) {
        this.qoutationSketch = qoutationSketch;
    }

    public Long getSalesId() {
        return salesId;
    }

    public void setSalesId(Long salesId) {
        this.salesId = salesId;
    }

    public Date getQoutationTime() {
        return qoutationTime;
    }

    public void setQoutationTime(Date qoutationTime) {
        this.qoutationTime = qoutationTime;
    }

    public String getNewBookingNo() {
        return newBookingNo;
    }

    public void setNewBookingNo(String newBookingNo) {
        this.newBookingNo = newBookingNo;
    }

    public String getNewBookingRemark() {
        return newBookingRemark;
    }

    public void setNewBookingRemark(String newBookingRemark) {
        this.newBookingRemark = newBookingRemark;
    }

    public Long getSalesAssistantId() {
        return salesAssistantId;
    }

    public void setSalesAssistantId(Long salesAssistantId) {
        this.salesAssistantId = salesAssistantId;
    }

    public Long getSalesObserverId() {
        return salesObserverId;
    }

    public void setSalesObserverId(Long salesObserverId) {
        this.salesObserverId = salesObserverId;
    }

    public Date getNewBookingTime() {
        return newBookingTime;
    }

    public void setNewBookingTime(Date newBookingTime) {
        this.newBookingTime = newBookingTime;
    }

    public String getInquiryNoticeSum() {
        return inquiryNoticeSum;
    }

    public void setInquiryNoticeSum(String inquiryNoticeSum) {
        this.inquiryNoticeSum = inquiryNoticeSum;
    }

    public String getInquiryInnerRemarkSum() {
        return inquiryInnerRemarkSum;
    }

    public void setInquiryInnerRemarkSum(String inquiryInnerRemarkSum) {
        this.inquiryInnerRemarkSum = inquiryInnerRemarkSum;
    }

    public String getSqdAmsEnsPostStatus() {
        return sqdAmsEnsPostStatus;
    }

    public void setSqdAmsEnsPostStatus(String sqdAmsEnsPostStatus) {
        this.sqdAmsEnsPostStatus = sqdAmsEnsPostStatus;
    }

    public String getSqdIsfEmnfPostStatus() {
        return sqdIsfEmnfPostStatus;
    }

    public void setSqdIsfEmnfPostStatus(String sqdIsfEmnfPostStatus) {
        this.sqdIsfEmnfPostStatus = sqdIsfEmnfPostStatus;
    }

    public String getSqdDocDeliveryWay() {
        return sqdDocDeliveryWay;
    }

    public void setSqdDocDeliveryWay(String sqdDocDeliveryWay) {
        this.sqdDocDeliveryWay = sqdDocDeliveryWay;
    }

    public String getBookingShipper() {
        return bookingShipper;
    }

    public void setBookingShipper(String bookingShipper) {
        this.bookingShipper = bookingShipper;
    }

    public String getBookingConsignee() {
        return bookingConsignee;
    }

    public void setBookingConsignee(String bookingConsignee) {
        this.bookingConsignee = bookingConsignee;
    }

    public String getBookingNotifyParty() {
        return bookingNotifyParty;
    }

    public void setBookingNotifyParty(String bookingNotifyParty) {
        this.bookingNotifyParty = bookingNotifyParty;
    }

    public String getNoTransferAllowed() {
        return noTransferAllowed;
    }

    public void setNoTransferAllowed(String noTransferAllowed) {
        this.noTransferAllowed = noTransferAllowed;
    }

    public String getNoDividedAllowed() {
        return noDividedAllowed;
    }

    public void setNoDividedAllowed(String noDividedAllowed) {
        this.noDividedAllowed = noDividedAllowed;
    }

    public String getNoAgreementShowed() {
        return noAgreementShowed;
    }

    public void setNoAgreementShowed(String noAgreementShowed) {
        this.noAgreementShowed = noAgreementShowed;
    }

    public String getIsCustomsIntransitShowed() {
        return isCustomsIntransitShowed;
    }

    public void setIsCustomsIntransitShowed(String isCustomsIntransitShowed) {
        this.isCustomsIntransitShowed = isCustomsIntransitShowed;
    }

    public Date getFirstAtd() {
        return firstAtd;
    }

    public void setFirstAtd(Date firstAtd) {
        this.firstAtd = firstAtd;
    }

    public Date getDestinationPortAta() {
        return destinationPortAta;
    }

    public void setDestinationPortAta(Date destinationPortAta) {
        this.destinationPortAta = destinationPortAta;
    }

    public String getBookingAgentRemark() {
        return bookingAgentRemark;
    }

    public void setBookingAgentRemark(String bookingAgentRemark) {
        this.bookingAgentRemark = bookingAgentRemark;
    }

    public String getShippingMark() {
        return shippingMark;
    }

    public void setShippingMark(String shippingMark) {
        this.shippingMark = shippingMark;
    }

    public String getFreightPaidWayCode() {
        return freightPaidWayCode;
    }

    public void setFreightPaidWayCode(String freightPaidWayCode) {
        this.freightPaidWayCode = freightPaidWayCode;
    }

    public String getCtnrTypeCode() {
        return ctnrTypeCode;
    }

    public void setCtnrTypeCode(String ctnrTypeCode) {
        this.ctnrTypeCode = ctnrTypeCode;
    }

    public Date getEtd() {
        return etd;
    }

    public void setEtd(Date etd) {
        this.etd = etd;
    }

    public Date getEta() {
        return eta;
    }

    public void setEta(Date eta) {
        this.eta = eta;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("psaRctId", getPsaRctId())
                .append("psaNo", getPsaNo())
                .append("paymentTitleCode", getPaymentTitleCode())
                .append("impExpType", getImpExpType())
                .append("tradingTerms", getTradingTerms())
                .append("logisticsTerms", getLogisticsTerms())
                .append("tradingPaymentChannel", getTradingPaymentChannel())
                .append("goodsNameSummary", getGoodsNameSummary())
                .append("packageQuantity", getPackageQuantity())
                .append("goodsVolume", getGoodsVolume())
                .append("grossWeight", getGrossWeight())
                .append("goodsCurrencyCode", getGoodsCurrencyCode())
                .append("goodsValue", getGoodsValue())
                .append("logisticsTypeId", getLogisticsTypeId())
                .append("revenueTon", getRevenueTon())
                .append("polId", getPolId())
                .append("transitPortId", getTransitPortId())
                .append("podId", getPodId())
                .append("destinationPortId", getDestinationPortId())
                .append("cvClosingTime", getCvClosingTime())
                .append("siClosingTime", getSiClosingTime())
                .append("firstVessel", getFirstVessel())
                .append("firstVoyage", getFirstVoyage())
                .append("firstCyOpenTime", getFirstCyOpenTime())
                .append("firstCyClosingTime", getFirstCyClosingTime())
                .append("firstEtd", getFirstEtd())
                .append("basicEtd", getBasicEtd())
                .append("podEta", getPodEta())
                .append("destinationPortEta", getDestinationPortEta())
                .append("carrierId", getCarrierId())
                .append("inquiryScheduleSummary", getInquiryScheduleSummary())
                .append("serviceTypeIdList", getServiceTypeIdList())
                .append("soNo", getSoNo())
                .append("blNo", getBlNo())
                .append("sqdContainersSealsSum", getSqdContainersSealsSum())
                .append("qoutationNo", getQoutationNo())
                .append("qoutationSketch", getQoutationSketch())
                .append("salesId", getSalesId())
                .append("qoutationTime", getQoutationTime())
                .append("newBookingNo", getNewBookingNo())
                .append("newBookingRemark", getNewBookingRemark())
                .append("salesAssistantId", getSalesAssistantId())
                .append("salesObserverId", getSalesObserverId())
                .append("newBookingTime", getNewBookingTime())
                .append("inquiryNoticeSum", getInquiryNoticeSum())
                .append("inquiryInnerRemarkSum", getInquiryInnerRemarkSum())
                .append("sqdAmsEnsPostStatus", getSqdAmsEnsPostStatus())
                .append("sqdIsfEmnfPostStatus", getSqdIsfEmnfPostStatus())
                .append("sqdDocDeliveryWay", getSqdDocDeliveryWay())
                .append("bookingShipper", getBookingShipper())
                .append("bookingConsignee", getBookingConsignee())
                .append("bookingNotifyParty", getBookingNotifyParty())
                .append("noTransferAllowed", getNoTransferAllowed())
                .append("noDividedAllowed", getNoDividedAllowed())
                .append("noAgreementShowed", getNoAgreementShowed())
                .append("isCustomsIntransitShowed", getIsCustomsIntransitShowed())
                .append("firstAtd", getFirstAtd())
                .append("destinationPortAta", getDestinationPortAta())
                .append("bookingAgentRemark", getBookingAgentRemark())
                .append("shippingMark", getShippingMark())
                .append("freightPaidWayCode", getFreightPaidWayCode())
                .append("ctnrTypeCode", getCtnrTypeCode())
                .append("etd", getEtd())
                .append("eta", getEta())
                .toString();
    }
}
