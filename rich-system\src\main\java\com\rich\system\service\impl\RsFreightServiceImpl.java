package com.rich.system.service.impl;

import com.rich.common.config.WebSocket;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.domain.entity.*;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.core.text.Convert;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SearchUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.StringUtils;
import com.rich.system.domain.*;
import com.rich.system.mapper.*;
import com.rich.system.service.RsFreightService;
import org.apache.commons.lang3.ArrayUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.rich.common.utils.PageUtils.startPage;

/**
 * 基础运费Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-01-04
 */
@Service
public class RsFreightServiceImpl implements RsFreightService {

    @Autowired
    private RsFreightMapper rsFreightMapper;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private RedisCacheImpl RedisCache;
    @Autowired
    private WebSocket webSocket;
    @Autowired
    private RsMessageMapper rsMessageMapper;
    @Autowired
    private RsStaffMapper rsStaffMapper;
    @Autowired
    private MidCargoTypeMapper midCargoTypeMapper;
    @Autowired
    private RsQuotationFreightMapper rsQuotationFreightMapper;
    @Autowired
    private MidRsStaffRoleMapper midRsStaffRoleMapper;

    /**
     * 查询基础运费
     *
     * @param freightId 基础运费主键
     * @return 基础运费
     */
    @Override
    public RsFreight selectRsFreightByFreightId(Long freightId) {
        return rsFreightMapper.selectRsFreightByFreightId(freightId);
    }

    /**
     * 查询基础运费列表
     *
     * @param rsFreight 基础运费
     * @return 基础运费
     */
    @Override
    public List<RsFreight> selectRsFreightList(RsFreight rsFreight) {
        // 搜索
        boolean search = rsFreight.getCargoTypeIds() != null
                || rsFreight.getDepartureIds() != null
                || rsFreight.getLineDepartureIds() != null
                || rsFreight.getTransitPortIds() != null
                || rsFreight.getDestinationIds() != null
                || rsFreight.getUnitIds() != null
                || rsFreight.getLineDestinationIds() != null
                || rsFreight.getPrecarriageRegionId() != null;
        List<Long> qc;
        if (search) {
            qc = queryFreights(rsFreight);
            if (qc == null || qc.isEmpty()) {
                return null;
            }
            rsFreight.setFreightIds(qc);
        }
        startPage();
        return rsFreightMapper.selectRsFreightList(rsFreight);
    }

    /**
     * 新增基础运费
     * 请求更新也会调用此方法
     *
     * @param rsFreight 基础运费
     * @return 结果
     */
    @Override
    public int insertRsFreight(RsFreight rsFreight) {
        rsFreight.setCreateTime(DateUtils.getNowDate());
        rsFreight.setCreateBy(SecurityUtils.getUserId());
        rsFreight.setUpdateBy(SecurityUtils.getUserId());
        rsFreight.setUpdateTime(DateUtils.getNowDate());
        rsFreight.setInquiryNo("RPF" + DateUtils.dateTimeNow() + String.format("%05d", new SecureRandom().nextInt(100000)));
        int out = rsFreightMapper.insertRsFreight(rsFreight);
        insertCargoType(rsFreight);
        updateMessage(rsFreight);
        return out;
    }

    /**
     * 修改基础运费
     *
     * @param rsFreight 基础运费
     * @return 结果
     */
    @Override
    public int updateRsFreight(RsFreight rsFreight) {
        rsFreight.setUpdateBy(SecurityUtils.getUserId());
        rsFreight.setUpdateTime(DateUtils.getNowDate());
        midCargoTypeMapper.deleteMidCargoTypeById(rsFreight.getFreightId(), "freight");
        int out = rsFreightMapper.updateRsFreight(rsFreight);
        insertCargoType(rsFreight);
        updateMessage(rsFreight);
        return out;
    }

    private void updateMessage(RsFreight rsFreight) {
        List<BasDistLocation> basDistLocations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        if (basDistLocations == null) {
            RedisCache.location();
            basDistLocations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        }
        List<BasDistLine> basDistLines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        if (basDistLines == null) {
            RedisCache.line();
            basDistLines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        }
        List<SysRole> sysRoles = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "role");
        if (sysRoles == null) {
            RedisCache.role();
            sysRoles = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "role");
        }
        List<MidLocationDeparture> midRoleLocationDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleLocationDeparture");
        if (midRoleLocationDepartures == null) {
            RedisCache.locationDeparture("role", "roleLocationDeparture");
            midRoleLocationDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleLocationDeparture");
        }
        List<MidLineDeparture> midRoleLineDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleLineDeparture");
        if (midRoleLineDepartures == null) {
            RedisCache.lineDeparture("role", "roleLineDeparture");
            midRoleLineDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleLineDeparture");
        }
        List<MidLocationDestination> midRoleLocationDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleLocationDestination");
        if (midRoleLocationDestinations == null) {
            RedisCache.locationDestination("role", "roleLocationDestination");
            midRoleLocationDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleLocationDestination");
        }
        List<MidLineDestination> midRoleLineDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleLineDestination");
        if (midRoleLineDestinations == null) {
            RedisCache.lineDestination("role", "roleLineDestination");
            midRoleLineDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleLineDestination");
        }
        Set<Long> locationDepartures = new HashSet<>();
        Set<Long> lineDepartures = new HashSet<>();
        Set<Long> locationDestinations = new HashSet<>();
        Set<Long> lineDestinations = new HashSet<>();
        Set<Long> roles = new HashSet<>();
        BasDistLocation departure = null;
        BasDistLocation destination = null;
        for (BasDistLocation location : basDistLocations) {
            if (rsFreight.getPolId() != null && rsFreight.getPolId().equals(location.getLocationId())) {
                departure = location;
                locationDepartures.add(location.getLocationId());
                for (String a : location.getAncestors().split(",")) {
                    locationDepartures.add(Long.parseLong(a));
                }
            }
            if (rsFreight.getDestinationPortId() != null && rsFreight.getDestinationPortId().equals(location.getLocationId())) {
                destination = location;
                locationDestinations.add(location.getLocationId());
                for (String a : location.getAncestors().split(",")) {
                    locationDestinations.add(Long.parseLong(a));
                }
            }
        }
        for (BasDistLine line : basDistLines) {
            if (departure != null && departure.getLineId() != null && departure.getLineId().equals(line.getLineId())) {
                lineDepartures.add(line.getLineId());
                for (String a : line.getAncestors().split(",")) {
                    lineDepartures.add(Long.parseLong(a));
                }
            }
            if (destination != null && destination.getLineId() != null && destination.getLineId().equals(line.getLineId())) {
                lineDestinations.add(line.getLineId());
                for (String a : line.getAncestors().split(",")) {
                    lineDestinations.add(Long.parseLong(a));
                }
            }
        }
        for (MidLocationDeparture d : midRoleLocationDepartures) {
            if (locationDepartures.contains(d.getLocationId())) {
                roles.add(d.getBelongId());
            }
        }
        for (MidLocationDestination d : midRoleLocationDestinations) {
            if (locationDestinations.contains(d.getLocationId())) {
                roles.add(d.getBelongId());
            }
        }
        for (MidLineDeparture d : midRoleLineDepartures) {
            if (lineDepartures.contains(d.getLineId())) {
                roles.add(d.getBelongId());
            }
        }
        for (MidLineDestination d : midRoleLineDestinations) {
            if (lineDestinations.contains(d.getLineId())) {
                roles.add(d.getBelongId());
            }
        }
        if (rsFreight.getIsSalesRequired() == 1 && rsFreight.getIsReplied() == 0) {
            for (SysRole r : sysRoles) {
                if (roles.contains(r.getRoleId()) && !r.getDeptId().equals(106L)) {
                    roles.remove(r.getRoleId());
                }
            }
            List<RsStaff> rsStaffs = rsStaffMapper.queryByIds(midRsStaffRoleMapper.selectStaffsByRoleId(roles.toArray(new Long[0])).toArray(new Long[0]));
            RsStaff require = rsStaffMapper.selectUserById(rsFreight.getRequireSalesId());
            for (RsStaff staff : rsStaffs) {
                webSocket.sendOneMessage(staff.getStaffId() + staff.getStaffCode(), require.getStaffLocalName() + "询价从：" + (departure != null ? departure.getLocationLocalName() : "") + "到" + (destination != null ? destination.getLocationLocalName() : "") + "的费用");
            }
        }
        if (rsFreight.getIsSalesRequired() == 1 && rsFreight.getIsReplied() == 1) {
            RsMessage message = new RsMessage();
            message.setMessageTitle("更新海运费");
            message.setMessageContent("已更新从：" + (departure != null ? departure.getLocationLocalName() : "") + "到" + (destination != null ? destination.getLocationLocalName() : "") + "的费用");
            message.setCreateTime(DateUtils.getNowDate());
            message.setMessageType(6L);
            message.setCreateBy(SecurityUtils.getUserId());
            message.setMessageOwner(rsFreight.getRequireSalesId().toString());
            rsMessageMapper.insertRsMessage(message);
            RsStaff staff = rsStaffMapper.selectUserById(rsFreight.getRequireSalesId());
            webSocket.sendOneMessage(staff.getStaffId() + staff.getStaffCode(), SecurityUtils.getUsername() + "更新了从：" + (departure != null ? departure.getLocationLocalName() : "") + "到" + (destination != null ? destination.getLocationLocalName() : "") + "的费用");
        }
    }

    /**
     * 批量删除基础运费
     *
     * @param freightIds 需要删除的基础运费主键
     * @return 结果
     */
    @Override
    public int deleteRsFreightByFreightIds(Long[] freightIds) {
        Set<Long> del = new HashSet<>();
        for (Long f : freightIds) {
            int e = rsQuotationFreightMapper.checkExist(f);
            if (e == 0) {
                del.add(f);
            }
        }
        midCargoTypeMapper.deleteMidCargoTypeByIds(del.toArray(new Long[0]), "freight");
        rsFreightMapper.deleteRsFreightByFreightIds(del.toArray(new Long[0]));
        return freightIds.length - del.size();
    }

    /**
     * 删除基础运费信息
     *
     * @param freightId 基础运费主键
     * @return 结果
     */
    @Override
    public int deleteRsFreightByFreightId(Long freightId) {
        midCargoTypeMapper.deleteMidCargoTypeById(freightId, "freight");
        return rsFreightMapper.deleteRsFreightByFreightId(freightId);
    }

    @Override
    public List<RsFreight> importFreight(List<RsFreight> freightList, boolean updateSupport, Long serviceTypeId, String typeId) {
        int out = 0;
        Set<RsFreight> failList = new HashSet<>();
        for (RsFreight rsFreight : freightList) {
            // 默认物流类型未海运
            if (serviceTypeId != null) {
                // 货物类型
                rsFreight.setServiceTypeId(serviceTypeId);
            } else {
                rsFreight.setLogisticsTypeId(1L);
                rsFreight.setServiceTypeId(1L);
            }
            // 获取服务大类,根据服务大类设置服务类型和物流类型
            if (rsFreight.getServiceType() != null && !Objects.equals(rsFreight.getServiceType(), "")) {
                boolean goOn = true;
                // typeId
                Long tId = RedisCache.getId("服务大类", rsFreight.getServiceType().replace(" ", ""));
                // 导入时只能导入当前服务大类的费用
                if (!typeId.equals(tId.toString())) {
                    failList.add(rsFreight);
                    goOn = false;
                    rsFreight.setServiceType("请上传当前费用类别的费用：" + rsFreight.getServiceType());
                }
                if (goOn) {
                    // 如果是主服务类型,服务项目和所属物流相同
                    if (tId.equals(1L) || tId.equals(2L) || tId.equals(3L) || tId.equals(4L)) {
                        // 设置服务类型
                        Long sId = RedisCache.getId("服务项目", rsFreight.getServiceType().replace(" ", ""));
                        if (sId == null) {
                            rsFreight.setServiceType("系统无法匹配：" + rsFreight.getServiceType());
                            failList.add(rsFreight);
                        }
                        rsFreight.setServiceTypeId(sId);
                        rsFreight.setLogisticsTypeId(sId);
                    } else {
                        // 设置服务类型
                        Long sId = RedisCache.getId("服务项目", rsFreight.getServiceType().replace(" ", ""));
                        if (sId == null) {
                            rsFreight.setServiceType("系统无法匹配：" + rsFreight.getServiceType());
                            failList.add(rsFreight);
                        }
                        rsFreight.setServiceTypeId(sId);
                        // 设置所属物流类型
                        if (rsFreight.getLogisticsType() != null && !Objects.equals(rsFreight.getLogisticsType(), "")) {
                            Long lId = RedisCache.getId("服务项目", rsFreight.getLogisticsType().replace(" ", ""));
                            if (lId == null) {
                                rsFreight.setLogisticsType("系统无法匹配：" + rsFreight.getLogisticsType());
                                failList.add(rsFreight);
                            }
                            rsFreight.setLogisticsTypeId(lId);
                        }
                    }
                }
            }

            // 费用id
            rsFreight.setChargeId(1L);
            // 物流时效单位
            rsFreight.setTtUnitCode("Day");
            // 货物单位
            rsFreight.setWeightUnitId(5L);
            // 设置合同类型
            if (rsFreight.getContractType() != null && !Objects.equals(rsFreight.getContractType(), "")) {
                /*Long id = RedisCache.getId("合约类别", rsFreight.getContractType().replace(" ", ""));
                if (id == null) {
                    rsFreight.setContractType("系统无法匹配：" + rsFreight.getContractType());
                    // 加入失败列表中
                    failList.add(rsFreight);
                }*/
                rsFreight.setAgreementCode(rsFreight.getContractType());
            }
            // 设置货物类型
            if (rsFreight.getCargoType() != null && !Objects.equals(rsFreight.getCargoType(), "")) {
                Set<Long> c = new HashSet<>();
                List<BasDistCargoType> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
                if (list == null) {
                    RedisCache.cargoType();
                    list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
                }
                String cargoType = rsFreight.getCargoType().replace(" ", "");
                for (BasDistCargoType o : list) {
                    if (o.getCargoTypeShortName() != null && cargoType.toLowerCase().contains(o.getCargoTypeShortName().toLowerCase())
                            || o.getCargoTypeLocalName() != null && cargoType.contains(o.getCargoTypeLocalName())
                            || o.getCargoTypeEnName() != null && cargoType.toLowerCase().contains(o.getCargoTypeEnName().toLowerCase())) {
                        c.add(o.getCargoTypeId());
                    }
                }
                if (!c.contains(1L)) {
                    c.add(1L);
                }
                rsFreight.setCargoTypeIds(c.toArray(new Long[0]));
            }
            // 设置订舱口
            if (rsFreight.getCompany() != null && !Objects.equals(rsFreight.getCompany(), "")) {
                Long id = RedisCache.getId("订舱口", rsFreight.getCompany().replace(" ", ""));
                if (id == null) {
                    rsFreight.setCompany("系统无法匹配：" + rsFreight.getCompany());
                    failList.add(rsFreight);
                }
                rsFreight.setSupplierId(id);
            }
            // 设置承运人
            if (rsFreight.getCarrier() != null && !Objects.equals(rsFreight.getCarrier(), "")) {
                Long id = RedisCache.getId("承运人", rsFreight.getCarrier().replace(" ", ""));
                List<BasCarrier> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "carrier");
                if (list == null) {
                    RedisCache.carrier();
                    list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "carrier");
                }
                if (id == null) {
                    rsFreight.setCarrier("系统无法匹配：" + rsFreight.getCarrier());
                    failList.add(rsFreight);
                }
                for (BasCarrier basCarrier : list) {
                    if (basCarrier.getCarrierId().equals(id)) {
                        rsFreight.setCarrierCode(basCarrier.getCarrierIntlCode());
                    }
                }
            }
            // 正则表达式,用于匹配括号中的内容
            Pattern pattern = Pattern.compile("\\((.*?)\\)");
            // 设置装柜区域
            if (rsFreight.getLoading() != null && !Objects.equals(rsFreight.getLoading(), "")) {
                Long id;
                if (rsFreight.getLoading().contains("(")) {
                    Matcher matcher = pattern.matcher(rsFreight.getLoading());
                    String portCode = "";
                    while (matcher.find()) {
                        portCode = matcher.group(1);
                    }
                    if (!portCode.isEmpty()) {
                        id = RedisCache.getId("装运区域", portCode);
                    } else {
                        id = RedisCache.getId("装运区域", rsFreight.getLoading().substring(0, rsFreight.getLoading().indexOf("(")));
                    }
                } else {
                    id = RedisCache.getId("装运区域", rsFreight.getLoading().replace(" ", ""));
                }
                if (id == null) {
                    rsFreight.setLoading("系统无法匹配：" + rsFreight.getLoading());
                    failList.add(rsFreight);
                }
                rsFreight.setPrecarriageRegionId(id);
            }
            // 设置启运港
            if (rsFreight.getDeparture() != null && !Objects.equals(rsFreight.getDeparture(), "")) {
                Long id;
                if (rsFreight.getDeparture().contains("(")) {
                    Matcher matcher = pattern.matcher(rsFreight.getDeparture());
                    String portCode = "";
                    while (matcher.find()) {
                        portCode = matcher.group(1);
                    }
                    if (!portCode.isEmpty()) {
                        id = RedisCache.getId("启运港", portCode);
                    } else {
                        id = RedisCache.getId("启运港", rsFreight.getDeparture().substring(0, rsFreight.getDeparture().indexOf("(")));
                    }
                } else {
                    id = RedisCache.getId("启运港", rsFreight.getDeparture().replace(" ", ""));
                }
                if (id == null) {
                    rsFreight.setDeparture("系统无法匹配：" + rsFreight.getDeparture());
                    failList.add(rsFreight);
                }
                rsFreight.setPolId(id);
            }
            // 设置中转港
            if (rsFreight.getTransitPort() != null && !Objects.equals(rsFreight.getTransitPort(), "")) {
                Long id;
                if (rsFreight.getTransitPort().contains("(")) {
                    Matcher matcher = pattern.matcher(rsFreight.getTransitPort());
                    String portCode = "";
                    while (matcher.find()) {
                        portCode = matcher.group(1);
                    }
                    if (!portCode.isEmpty()) {
                        id = RedisCache.getId("中转港", portCode);
                    } else {
                        id = RedisCache.getId("中转港", rsFreight.getTransitPort().substring(0, rsFreight.getTransitPort().indexOf("(")));
                    }
                } else {
                    id = RedisCache.getId("中转港", rsFreight.getTransitPort().replace(" ", ""));
                }
                if (id == null) {
                    rsFreight.setTransitPort("系统无法匹配：" + rsFreight.getTransitPort());
                    failList.add(rsFreight);
                }
                rsFreight.setTransitPortId(id);
            }
            // 设置目的港
            if (rsFreight.getDestination() != null && !Objects.equals(rsFreight.getDestination(), "")) {
                Long id;
                if (rsFreight.getDestination().contains("(")) {
                    Matcher matcher = pattern.matcher(rsFreight.getDestination());
                    String portCode = "";
                    while (matcher.find()) {
                        portCode = matcher.group(1);
                    }
                    if (!portCode.isEmpty()) {
                        id = RedisCache.getId("目的港", portCode);
                    } else {
                        id = RedisCache.getId("目的港", rsFreight.getDestination().substring(0, rsFreight.getDestination().indexOf("(")));
                    }
                } else {
                    id = RedisCache.getId("目的港", rsFreight.getDestination().replace(" ", ""));
                }
                if (id == null) {
                    rsFreight.setDestination("系统无法匹配：" + rsFreight.getDestination());
                    failList.add(rsFreight);
                }
                rsFreight.setDestinationPortId(id);
            }
            // 设置价格类型
            if (rsFreight.getCharge() != null && !Objects.equals(rsFreight.getCharge(), "")) {
                Long id = RedisCache.getId("价格类别", rsFreight.getCharge().replace(" ", ""));
                if (id == null) {
                    rsFreight.setCharge("系统无法匹配：" + rsFreight.getCharge());
                    failList.add(rsFreight);
                    rsFreight.setChargeId(1L);
                }
                rsFreight.setChargeId(id);
            }
            // 设置币种
            if (rsFreight.getCurrency() != null && !Objects.equals(rsFreight.getCurrency(), "")) {
                /*Long id = RedisCache.getId("币种", rsFreight.getCurrency().replace(" ", ""));
                List<BasCurrency> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "currency");
                if (list == null) {
                    RedisCache.currency();
                    list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "currency");
                }
                if (id == null) {
                    rsFreight.setCurrency("系统无法匹配：" + rsFreight.getCurrency());
                    failList.add(rsFreight);
                }
                for (BasCurrency basCurrency : list) {
                    if (basCurrency.getCurrencyId().equals(id)) {
                        rsFreight.setCurrencyCode(basCurrency.getCurrencyCode());
                    }
                }*/
                rsFreight.setCurrencyCode(rsFreight.getCurrency());
            }
            if (rsFreight.getPriceB() != null) {
                rsFreight.setUnitId(2L);
                rsFreight.setUnitCode("Ctnr");
            } else {
                if (rsFreight.getUnit() != null && !Objects.equals(rsFreight.getUnit(), "")) {
                    Long id = RedisCache.getId("柜型", rsFreight.getUnit().replace(" ", ""));
                    if (id == null) {
                        rsFreight.setUnit("系统无法匹配：" + rsFreight.getUnit());
                        failList.add(rsFreight);
                    }
                    rsFreight.setUnitId(id);
                    rsFreight.setUnitCode(rsFreight.getUnit());
                }
            }
            //物流时效单位
            if (rsFreight.getLogisticsUnit() != null && !Objects.equals(rsFreight.getLogisticsUnit(), "")) {
                Long id = RedisCache.getId("物流时效单位", rsFreight.getLogisticsUnit().replace(" ", ""));
                List<BasDistUnit> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "unit");
                if (list == null) {
                    RedisCache.unit();
                    list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "unit");
                }
                if (id == null) {
                    rsFreight.setLogisticsUnit("系统无法匹配：" + rsFreight.getLogisticsUnit());
                    failList.add(rsFreight);
                }
                for (BasDistUnit basDistUnit : list) {
                    if (basDistUnit.getUnitId().equals(id)) {
                        rsFreight.setTtUnitCode(basDistUnit.getUnitCode());
                    }
                }
            }
            // 匹配物流时效
            if (rsFreight.getLogisticsEfficiencyNode() != null && !Objects.equals(rsFreight.getLogisticsEfficiencyNode(), "")) {
                Long id = RedisCache.getId("物流时效节点", rsFreight.getLogisticsEfficiencyNode().replace(" ", ""));
                if (id == null) {
                    rsFreight.setLogisticsEfficiencyNode("系统无法匹配：" + rsFreight.getLogisticsEfficiencyNode());
                    failList.add(rsFreight);
                }
                rsFreight.setLogisticsEfficiencyNodeId(id);
            }
            // 有效期
            if (rsFreight.getValidPeriodTimeNode() != null && !Objects.equals(rsFreight.getValidPeriodTimeNode(), "")) {
                Long id = RedisCache.getId("有效期节点", rsFreight.getValidPeriodTimeNode().replace(" ", ""));
                if (id == null) {
                    rsFreight.setValidPeriodTimeNode("系统无法匹配：" + rsFreight.getValidPeriodTimeNode());
                    failList.add(rsFreight);
                }
                rsFreight.setValidPeriodTimeNodeId(id);
            }
            rsFreight.setCreateBy(SecurityUtils.getUserId());
            rsFreight.setIsSalesRequired(0);
            rsFreight.setIsReplied(0);
            if (!failList.contains(rsFreight)) {
                // 是否更新原有费用
                if (rsFreight.getInquiryNo() != null && !Objects.equals(rsFreight.getInquiryNo(), "") && updateSupport) {
                    int a = rsFreightMapper.updateImportFreight(rsFreight);
                    if (a > 0) {
                        midCargoTypeMapper.deleteMidCargoTypeById(rsFreight.getFreightId(), "freight");
                        insertCargoType(rsFreight);
                        out += a;
                    } else {
                        rsFreight.setInquiryNo("系统无法匹配：" + rsFreight.getInquiryNo());
                        failList.add(rsFreight);
                    }
                } else {
                    out += insertRsFreight(rsFreight);
                }
            }
        }
        return new ArrayList<>(failList);
    }

    @Override
    public List<Long> selectCargoTypes(Long freightId) {
        List<MidCargoType> midCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "freightCargoType");
        if (midCargoTypes == null) {
            RedisCache.midCargoType("freight", "freightCargoType");
            midCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "freightCargoType");
        }
        List<Long> r = new ArrayList<>();
        for (MidCargoType midCargoType : midCargoTypes) {
            if (midCargoType.getBelongId().equals(freightId)) {
                r.add(midCargoType.getCargoTypeId());
            }
        }
        return r;
    }

    @Override
    public List<RsFreight> selectRsFreightListExport(RsFreight rsFreight) {
        // 搜索
        boolean search = rsFreight.getCargoTypeIds() != null
                || rsFreight.getDepartureIds() != null
                || rsFreight.getLineDepartureIds() != null
                || rsFreight.getTransitPortIds() != null
                || rsFreight.getDestinationIds() != null
                || rsFreight.getUnitIds() != null
                || rsFreight.getLineDestinationIds() != null
                || rsFreight.getPrecarriageRegionId() != null;
        List<Long> qc;
        if (search) {
            qc = queryFreights(rsFreight);
            if (qc == null || qc.isEmpty()) {
                return null;
            }
            rsFreight.setFreightIds(qc);
        }
        return rsFreightMapper.selectRsFreightList(rsFreight);
    }

    private void insertCargoType(@NotNull RsFreight rsFreight) {
        Long[] roles = rsFreight.getCargoTypeIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidCargoType> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidCargoType MidCargoType = new MidCargoType();
                MidCargoType.setBelongId(rsFreight.getFreightId());
                MidCargoType.setBelongTo("freight");
                MidCargoType.setCargoTypeId(r);
                list.add(MidCargoType);
            }
            midCargoTypeMapper.batchCargoType(list);
        }
        RedisCache.midCargoType("freight", "freightCargoType");
    }

    private List<Long> queryFreights(RsFreight rsFreight) {
        List<List<Long>> lists = new ArrayList<>();
        List<RsFreight> rsFreights = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "freight");
        if (rsFreights == null) {
            RedisCache.freight();
            rsFreights = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "freight");
        }
        List<BasDistLocation> locations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        if (locations == null) {
            RedisCache.location();
            locations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        }
        List<BasDistLine> lines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        if (lines == null) {
            RedisCache.line();
            lines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        }
        List<BasDistCargoType> basDistCargoTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        if (basDistCargoTypes == null) {
            RedisCache.cargoType();
            basDistCargoTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        }
        List<MidCargoType> midCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "freightCargoType");
        if (midCargoTypes == null) {
            RedisCache.midCargoType("freight", "freightCargoType");
            midCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "freightCargoType");
        }
        List<BasDistUnit> basDistUnits = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "unit");
        if (basDistUnits == null) {
            RedisCache.unit();
            basDistUnits = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "unit");
        }
        // 单位匹配
        if (rsFreight.getUnitIds() != null && rsFreight.getUnitIds().length > 0) {
            Set<Long> set = new HashSet<>();
            Set<Long> u = new HashSet<>();
            for (BasDistUnit unit : basDistUnits) {
                if (ArrayUtils.contains(rsFreight.getUnitIds(), unit.getUnitCode())) {
                    u.add(unit.getUnitId());
                }
                if (ArrayUtils.contains(rsFreight.getUnitIds(), "Ctnr") && unit.getMain().equals(1)) {
                    u.add(unit.getUnitId());
                }
            }
            for (RsFreight f : rsFreights) {
                if (ArrayUtils.contains(u.toArray(), f.getUnitId())) {
                    set.add(f.getFreightId());
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (!list.isEmpty()) {
                lists.add(list);
            } else {
                return null;
            }
        }
        // 货物类型匹配
        if (rsFreight.getCargoTypeIds() != null && !ArrayUtils.contains(rsFreight.getCargoTypeIds(), -1L)) {
            Set<Long> set = new HashSet<>();
            Set<Long> c = new HashSet<>();
            for (BasDistCargoType cargoType : basDistCargoTypes) {
                String[] ancestors = cargoType.getAncestors().split(",");
                if (SearchUtils.existSame(ancestors, rsFreight.getCargoTypeIds())) {
                    c.add(cargoType.getCargoTypeId());
                }
                if (ArrayUtils.contains(rsFreight.getCargoTypeIds(), cargoType.getCargoTypeId())) {
                    c.add(cargoType.getCargoTypeId());
                    for (String a : ancestors) {
                        c.add(Convert.toLong(a));
                    }
                }
            }
            for (MidCargoType midCargoType : midCargoTypes) {
                if (ArrayUtils.contains(c.toArray(), midCargoType.getCargoTypeId())) {
                    set.add(midCargoType.getBelongId());
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (!list.isEmpty()) {
                lists.add(list);
            } else {
                return null;
            }
        }
        // 中转港
        if (rsFreight.getTransitPortIds() != null && !ArrayUtils.contains(rsFreight.getTransitPortIds(), -1L)) {
            Set<Long> set = new HashSet<>();
            Set<Long> olocations = new HashSet<>();
            for (BasDistLocation location : locations) {
                String[] ancestors = location.getAncestors().split(",");
                if (ArrayUtils.contains(rsFreight.getTransitPortIds(), location.getLocationId())) {
                    olocations.add(location.getLocationId());
                    for (String a : ancestors) {
                        olocations.add(Convert.toLong(a));
                    }
                }
                if (SearchUtils.existSame(ancestors, rsFreight.getTransitPortIds())) {
                    olocations.add(location.getLocationId());
                }
            }
            for (RsFreight f : rsFreights) {
                if (ArrayUtils.contains(olocations.toArray(), f.getTransitPortId())) {
                    set.add(f.getFreightId());
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (!list.isEmpty()) {
                lists.add(list);
            } else {
                return null;
            }
        }
        // 启运港
        if (rsFreight.getDepartureIds() != null && !ArrayUtils.contains(rsFreight.getDepartureIds(), -1L)) {
            Set<Long> set = new HashSet<>();
            Set<Long> olocations = new HashSet<>();
            for (BasDistLocation location : locations) {
                String[] ancestors = location.getAncestors().split(",");
                if (ArrayUtils.contains(rsFreight.getDepartureIds(), location.getLocationId())) {
                    olocations.add(location.getLocationId());
                    for (String a : ancestors) {
                        olocations.add(Convert.toLong(a));
                    }
                }
                if (SearchUtils.existSame(ancestors, rsFreight.getDepartureIds())) {
                    olocations.add(location.getLocationId());
                }
            }
            for (RsFreight f : rsFreights) {
                if (ArrayUtils.contains(olocations.toArray(), f.getPolId())) {
                    set.add(f.getFreightId());
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (!list.isEmpty()) {
                lists.add(list);
            } else {
                return null;
            }
        }
        // 目的港
        if (rsFreight.getDestinationIds() != null && !ArrayUtils.contains(rsFreight.getDestinationIds(), -1L)) {
            Set<Long> set = new HashSet<>();
            Set<Long> olocations = new HashSet<>();
            for (BasDistLocation location : locations) {
                String[] ancestors = location.getAncestors().split(",");
                if (ArrayUtils.contains(rsFreight.getDestinationIds(), location.getLocationId())) {
                    olocations.add(location.getLocationId());
                    for (String a : ancestors) {
                        olocations.add(Convert.toLong(a));
                    }
                }
                if (SearchUtils.existSame(ancestors, rsFreight.getDestinationIds())) {
                    olocations.add(location.getLocationId());
                }
            }
            for (RsFreight f : rsFreights) {
                if (ArrayUtils.contains(olocations.toArray(), f.getDestinationPortId())) {
                    set.add(f.getFreightId());
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (!list.isEmpty()) {
                lists.add(list);
            } else {
                return null;
            }
        }
        // 目的航线
        if (rsFreight.getLineDestinationIds() != null && !ArrayUtils.contains(rsFreight.getLineDepartureIds(), -1L)) {
            Set<Long> set = new HashSet<>();
            Set<Long> olocations = new HashSet<>();
            Set<Long> olines = new HashSet<>();
            for (BasDistLine line : lines) {
                String[] ancestors = line.getAncestors().split(",");
                if (ArrayUtils.contains(rsFreight.getLineDestinationIds(), line.getLineId())) {
                    olines.add(line.getLineId());
                    for (String a : ancestors) {
                        olines.add(Convert.toLong(a));
                    }
                }
                if (SearchUtils.existSame(ancestors, rsFreight.getLineDestinationIds())) {
                    olines.add(line.getLineId());
                }
            }
            for (BasDistLocation location : locations) {
                if (location.getLineId() != null && ArrayUtils.contains(olines.toArray(), location.getLineId())) {
                    String[] ancestors = location.getAncestors().split(",");
                    olocations.add(location.getLocationId());
                    for (String a : ancestors) {
                        olocations.add(Convert.toLong(a));
                    }
                }
            }
            for (RsFreight f : rsFreights) {
                if (ArrayUtils.contains(olocations.toArray(), f.getDestinationPortId())) {
                    set.add(f.getFreightId());
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (!list.isEmpty()) {
                lists.add(list);
            } else {
                return null;
            }
        }
        // 装运区域
        if (rsFreight.getPrecarriageRegionId() != null && !rsFreight.getPrecarriageRegionId().equals(-1L)) {
            Set<Long> set = new HashSet<>();
            /*Set<Long> olocations = new HashSet<>();
            for (BasDistLocation location : locations) {
                String[] ancestors = location.getAncestors().split(",");
                if (rsFreight.getPrecarriageRegionId().equals(location.getLocationId())) {
                    olocations.add(location.getLocationId());
                    for (String a : ancestors) {
                        olocations.add(Convert.toLong(a));
                    }
                }
                if (SearchUtils.existSame(ancestors, rsFreight.getDestinationIds())) {
                    olocations.add(location.getLocationId());
                }
            }*/
            for (RsFreight f : rsFreights) {
                if (rsFreight.getPrecarriageRegionId().equals(f.getPrecarriageRegionId())) {
                    set.add(f.getFreightId());
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (!list.isEmpty()) {
                lists.add(list);
            } else {
                return null;
            }
        }
        if (!lists.isEmpty()) {
            return SearchUtils.getLongs(lists);
        } else {
            return null;
        }
    }
}
