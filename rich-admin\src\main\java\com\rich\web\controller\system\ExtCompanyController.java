package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasAccount;
import com.rich.common.core.domain.entity.ExtCompany;
import com.rich.common.core.domain.entity.SysRole;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasAccountService;
import com.rich.system.service.BasDistLocationService;
import com.rich.system.service.ExtCompanyService;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 客户供应商公司Controller
 *
 * <AUTHOR>
 * @date 2022-08-18
 */
@RestController
@RequestMapping("/system/company")
public class ExtCompanyController extends BaseController {

    @Autowired
    private ExtCompanyService extCompanyService;


    @Autowired
    private BasAccountService basAccountService;


    @Autowired
    private BasDistLocationService basDistLocationService;

    @Autowired
    private RedisCache redisCache;


    /**
     * 查询客户供应商公司列表
     */
    @PreAuthorize("@ss.hasPermi('system:company:list')")
    @GetMapping("/listpage")
    public TableDataInfo listPage(ExtCompany extCompany) {
        if (SecurityUtils.getDeptId().equals(105L) || SecurityUtils.getDeptId().equals(102L) || SecurityUtils.isAdmin(SecurityUtils.getLoginUser().getUser().getRole())) {
            extCompany.setPermissionLevel(null);
        }
        List<ExtCompany> list = extCompanyService.selectExtCompanyList(extCompany);
        if (list != null && !list.isEmpty()) {
            return getDataTable(list);
        } else {
            return null;
        }
    }

    /**
     * 查询客户供应商公司列表
     */
    @PreAuthorize("@ss.hasPermi('system:company:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExtCompany extCompany) {
        List<ExtCompany> list = extCompanyService.selectExtCompanyListNoPage(extCompany);
        if (list != null && !list.isEmpty()) {
            return getDataTable(list);
        } else {
            return null;
        }
    }

    /**
     * 查询客户供应商公司列表
     */
//    @PreAuthorize("@ss.hasPermi('system:company:list')")
    @GetMapping("/queryList")
    public AjaxResult queryList(ExtCompany extCompany) {
        List<ExtCompany> list = extCompanyService.selectExtCompanyListByQuery(extCompany);
        return AjaxResult.success(list);
    }

    @PostMapping("/selectList")
    public AjaxResult selectList(@RequestBody ExtCompany extCompany) {
        if (SecurityUtils.getDeptId().equals(106L)) {
            extCompany.setPermissionLevel(null);
        }
        return AjaxResult.success(extCompanyService.selectList(extCompany));
    }

    /**
     * 导出客户供应商公司列表
     */
    @PreAuthorize("@ss.hasPermi('system:company:export')")
    @Log(title = "客户供应商公司", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExtCompany extCompany) {
        List<ExtCompany> list = extCompanyService.exportExtCompanyList(extCompany);
        ExcelUtil<ExtCompany> util = new ExcelUtil<>(ExtCompany.class);
        util.exportExcel(response, list, "客户供应商公司数据");
    }

    /**
     * 获取客户供应商公司详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:company:edit')")
    @GetMapping(value = "/{companyId}")
    public AjaxResult getInfo(@PathVariable("companyId") Long companyId) {
        ExtCompany extCompany = extCompanyService.selectExtCompanyByCompanyId(companyId);
        return getAjaxResult(extCompany);
    }

    /**
     * 获取客户供应商公司员工
     */
    @PreAuthorize("@ss.hasPermi('system:company:list')")
    @GetMapping(value = "/staff/{companyId}")
    public AjaxResult getStaffs(@PathVariable("companyId") Long companyId) {
        AjaxResult ajaxResult = AjaxResult.success();
        ajaxResult.put("staffList", extCompanyService.selectExtStaffListByCompanyId(companyId));
        return ajaxResult;
    }

    /**
     * 获取客户供应商公司账户
     */
    @PreAuthorize("@ss.hasPermi('system:company:list')")
    @GetMapping(value = "/account/{companyId}")
    public AjaxResult getAccounts(@PathVariable("companyId") Long companyId) {
        AjaxResult ajaxResult = AjaxResult.success();
        BasAccount basAccount = new BasAccount();
        basAccount.setBelongToCompany(companyId);
        ajaxResult.put("accountList", basAccountService.selectBasAccountList(basAccount));
        return ajaxResult;
    }

    /**
     * 修改客户供应商公司
     */
    @PreAuthorize("@ss.hasPermi('system:company:edit')")
    @Log(title = "客户供应商公司", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExtCompany extCompany) {
        ExtCompany company = extCompanyService.selectExtCompanyByCompanyId(extCompany.getCompanyId());

        // 获取到公司的类型(role_rich/role_client/role_supplier等)
        String roleType = "";
        if (extCompany.getRoleRich() != null) {
            roleType = "role_rich";
        }
        if (extCompany.getRoleClient() != null) {
            roleType = "role_client";
        }
        if (extCompany.getRoleSupplier() != null) {
            roleType = "role_supplier";
        }
        if (extCompany.getRoleSupport() != null) {
            roleType = "role_support";
        }

        String oldName = company.getCompanyShortName() == null || (company.getCompanyShortName() != null && company.getCompanyShortName().isEmpty()) ? null : company.getCompanyShortName();
        String oldEnName = company.getCompanyEnShortName() == null || (company.getCompanyEnShortName() != null && company.getCompanyEnShortName().isEmpty()) ? null : company.getCompanyEnShortName();
        String newName = extCompany.getCompanyShortName() == null || (extCompany.getCompanyShortName() != null && extCompany.getCompanyShortName().isEmpty()) ? null : extCompany.getCompanyShortName();
        String newEnName = extCompany.getCompanyEnShortName() == null || (extCompany.getCompanyEnShortName() != null && extCompany.getCompanyEnShortName().isEmpty()) ? null : extCompany.getCompanyEnShortName();
        ExtCompany c = extCompanyService.checkCompanyShortNameUnique(newName, newEnName);
        if (c != null && !Objects.equals(c.getCompanyId(), extCompany.getCompanyId())) {
            if (roleType.equals("role_supplier") && c.getDeleteStatus() == 0) {
                return AjaxResult.error("修改供应商'" + newName + "'失败，供应商简称已存在");
            } else if (roleType.equals("role_client") && extCompany.getRoleTypeId() == 1L && c.getDeleteStatus() == 0) {
                return AjaxResult.error("修改客户'" + newName + "'失败，客户简称已存在");
            }
        }
        if (!Objects.equals(extCompany.getCompanyLocalName(), null) && !Objects.equals(extCompany.getCompanyLocalName(), "") && extCompanyService.checkCompanyLocalNameUnique(extCompany.getCompanyLocalName(), extCompany.getRoleTypeId(), extCompany.getCompanyId(), roleType)) {
            if (roleType.equals("role_supplier")) {
                return AjaxResult.error("修改供应商'" + extCompany.getCompanyLocalName() + "'失败，供应商全称已存在");
            } else if (roleType.equals("role_client")) {
                return AjaxResult.error("修改客户'" + extCompany.getCompanyLocalName() + "'失败，客户全称已存在");
            }
        }
        if (!Objects.equals(extCompany.getCompanyEnName(), null) && !Objects.equals(extCompany.getCompanyEnName(), "") && extCompanyService.checkCompanyEnNameUnique(extCompany.getCompanyEnName(), extCompany.getRoleTypeId(), extCompany.getCompanyId())) {
            if (roleType.equals("role_supplier")) {
                return AjaxResult.error("修改供应商'" + extCompany.getCompanyEnName() + "'失败，供应商英文名已存在");
            } else if (roleType.equals("role_client")) {
                return AjaxResult.error("修改客户'" + extCompany.getCompanyEnName() + "'失败，客户英文名已存在");
            }
        }
        if (c != null && !Objects.equals(c.getCompanyId(), extCompany.getCompanyId()) && (oldName != null && oldName.equals(newName) || oldEnName != null && oldEnName.equals(newEnName))) {
            return AjaxResult.error("与旧名同名，请重新更改");
        }
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "supplierList");
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "clientList");
        return toAjax(extCompanyService.updateExtCompany(extCompany));
    }

    /**
     * 新增客户供应商公司
     * querySame
     */
    @PreAuthorize("@ss.hasPermi('system:company:add')")
    @Log(title = "客户供应商公司", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult querySame(@RequestBody ExtCompany extCompany) {
        // 获取到公司的类型(role_rich/role_client/role_supplier等)
        String roleType = "";
        if (extCompany.getRoleRich() != null) {
            roleType = "role_rich";
        }
        if (extCompany.getRoleClient() != null) {
            roleType = "role_client";
        }
        if (extCompany.getRoleSupplier() != null) {
            roleType = "role_supplier";
        }
        if (extCompany.getRoleSupport() != null) {
            roleType = "role_support";
        }

        if (extCompany.getDeleteStatus().equals(1)) {
            List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
            boolean isChief = false;
            for (SysRole r : roles) {
                if (r.getDeptId() == 106 && (r.getPositionId() > 15 || r.getPositionId() == 15)) {
                    isChief = true;
                    break;
                } else if (r.isAdmin()) {
                    isChief = true;
                }
            }
            // 查询公司名字是否唯一(需要根据不同的公司类型去找,如客户就查询rich_client=1的公司是否名字重复)
            ExtCompany c = extCompanyService.checkCompanyShortNameUnique(extCompany.getCompanyShortName(), extCompany.getCompanyEnShortName());

            ExtCompany c2 = extCompanyService.checkCompanyShortNameUnique(extCompany.getCompanyShortName(), extCompany.getCompanyEnShortName(), roleType);

            // 如果存在同名的公司
            if (c != null) {
                // 如果是删除(失效)的公司
                if (c.getDeleteStatus().equals(1)) {
                    ExtCompany extC = extCompanyService.getDelCompany(c.getCompanyId());
                    // 已经存在请求录入的公司信息但已被删除,直接返回前端让用户选择
                    return getAjaxResult(extC);
                    // 如果存在同名的公司(区分公司类型)且未被删除
                } else {
                    // 同名公司存在,且录入的是同一类型的存在
                    if (extCompany.getRoleClient() != null && extCompany.getRoleClient().equals("1") && c2 != null && !isChief) {
                        return AjaxResult.success("Error:系统已存在[" + extCompany.getCompanyShortName() + "]，录入人为[" + c.getUpdateByName() + "]\n" +
                                "请联系部门主管或技术部同时协调解决");
                    }
                    if (extCompany.getRoleSupplier() != null && extCompany.getRoleSupplier().equals("1") && c2 != null && !isChief) {
                        return AjaxResult.success("Error:系统已存在[" + extCompany.getCompanyShortName() + "]，录入人为[" + c.getUpdateByName() + "]\n" +
                                "请联系部门主管或技术部同时协调解决");
                    }
                    // 同名公司存在,且录入的不是同一类型的存在
                    if (c2 == null && isChief) {
                        return getInfo(c.getCompanyId());
                    }
                    if (c2 == null && !isChief) {
                        String role = "";
                        if (c.getRoleRich() != null && c.getRoleRich().equals("1")) {
                            role = "瑞旗分支";
                        }
                        if (c.getRoleClient() != null && c.getRoleClient().equals("1")) {
                            role = "客户";
                        }
                        if (c.getRoleSupplier() != null && c.getRoleSupplier().equals("1")) {
                            role = "供应商";
                        }
                        if (c.getRoleSupport() != null && c.getRoleSupport().equals("1")) {
                            role = "运营支持";
                        }
                        return AjaxResult.success("Error:新增客户'" + extCompany.getCompanyShortName() + "失败，此公司已存在，角色为" + role + "\n" +
                                "请联系部门主管或技术部同时协调解决");
                    }
                    return AjaxResult.success("Error:系统已存在[" + extCompany.getCompanyShortName() + "]，录入人为[" + c.getUpdateByName() + "]\n");
                }
                // 录入的公司名不为空 且 公司中文名不唯一
            } else if (!Objects.equals(extCompany.getCompanyLocalName(), "") && extCompanyService.checkCompanyLocalNameUnique(extCompany.getCompanyLocalName(), extCompany.getRoleTypeId(), extCompany.getCompanyId(), roleType)) {
                if (extCompany.getRoleSupplier() != null && extCompany.getRoleSupplier().equals("1")) {
                    return AjaxResult.error("修改供应商'" + extCompany.getCompanyLocalName() + "'失败，供应商全称已存在");
                } else if (extCompany.getRoleClient() != null && extCompany.getRoleClient().equals("1")) {
                    return AjaxResult.error("修改客户'" + extCompany.getCompanyLocalName() + "'失败，客户全称已存在");
                }
                // 录入的公司名不为空 且 公司英文名不唯一
            } else if (!Objects.equals(extCompany.getCompanyEnName(), "") && extCompanyService.checkCompanyEnNameUnique(extCompany.getCompanyEnName(), extCompany.getRoleTypeId(), extCompany.getCompanyId(), roleType)) {
                if (extCompany.getRoleSupplier().equals("1")) {
                    return AjaxResult.error("修改供应商'" + extCompany.getCompanyEnName() + "'失败，供应商英文名已存在");
                } else if (extCompany.getRoleClient().equals("1")) {
                    return AjaxResult.error("修改客户'" + extCompany.getCompanyEnName() + "'失败，客户英文名已存在");
                }
            } else {
                return AjaxResult.success("Success:不存在相同");
            }
        } else {
            redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "supplierList");
            redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "clientList");
            // 这里不可以直接插入,同一个公司有多个角色,如果只是新增一个角色的话更新即可
            ExtCompany company = extCompanyService.checkCompanyNameExit(extCompany.getCompanyShortName(), extCompany.getCompanyEnShortName());
            if (company != null) {
                if (roleType.equals("role_rich")) {
                    company.setRoleRich("1");
                }
                if (roleType.equals("role_client")) {
                    company.setRoleClient("1");
                }
                if (roleType.equals("role_supplier")) {
                    company.setRoleSupplier("1");
                }
                if (roleType.equals("role_support")) {
                    company.setRoleSupport("1");
                }
                extCompanyService.updateExtCompany(company);
                // 修改公司时,更新主联系人信息

                ExtCompany extC = extCompanyService.selectExtCompanyByCompanyId(company.getCompanyId());
                return getAjaxResult(extC);
            } else {
                // 插入公司信息时插入主联系人信息
                if (roleType.equals("role_supplier")) {
                    extCompany.setBelongTo(SecurityUtils.getUserId());
                }
                extCompanyService.insertExtCompany(extCompany);
            }
        }
        ExtCompany extC = extCompanyService.selectExtCompanyByCompanyId(extCompany.getCompanyId());
        return getAjaxResult(extC);
    }

    /**
     * 将查询到的公司对象进行包装,返回一个结果封装对象
     *
     * @param extC
     * @return
     */
    @NotNull
    private AjaxResult getAjaxResult(ExtCompany extC) {
        List<Long> locationDepartureIds = extCompanyService.selectCompanyLocationDeparture(extC.getCompanyId());
        List<Long> locationDestinationIds = extCompanyService.selectCompanyLocationDestination(extC.getCompanyId());
        AjaxResult ajaxResult = AjaxResult.success();
        Set<Long> set = new HashSet<>();
        if (!locationDepartureIds.isEmpty()) {
            set.addAll(locationDepartureIds);
        }
        if (!locationDestinationIds.isEmpty()) {
            set.addAll(locationDestinationIds);
        }
        if (extC.getLocationId() != null) {
            set.add(extC.getLocationId());
        }
        ajaxResult.put(AjaxResult.DATA_TAG, extC);
        ajaxResult.put("roleIds", extCompanyService.selectCompanyRoles(extC.getCompanyId()));
        ajaxResult.put("serviceTypeIds", extCompanyService.selectCompanyServiceTypes(extC.getCompanyId()));
        ajaxResult.put("cargoTypeIds", extCompanyService.selectCompanyCargoTypes(extC.getCompanyId()));
        ajaxResult.put("locationDepartureIds", locationDepartureIds);
        ajaxResult.put("locationDestinationIds", locationDestinationIds);
        ajaxResult.put("locationOptions", !set.isEmpty() ? basDistLocationService.selectBasDistLocationByIds(set) : null);
        ajaxResult.put("lineDepartureIds", extCompanyService.selectCompanyLineDeparture(extC.getCompanyId()));
        ajaxResult.put("lineDestinationIds", extCompanyService.selectCompanyLineDestination(extC.getCompanyId()));
        ajaxResult.put("carrierIds", extCompanyService.selectCompanyCarriers(extC.getCompanyId()));
        ajaxResult.put("organizationIds", extCompanyService.selectCompanyOrganizations(extC.getCompanyId()));
        return ajaxResult;
    }

    /**
     * 删除客户供应商公司
     * 删除的时候要判断类型,供应商类型删除的时候仅将role_supplier设为0即可,全部的公司类型都被删除(role_rich,role_client等值都为0)时是否将deleteStatus设为1
     */
    @PreAuthorize("@ss.hasPermi('system:company:remove')")
    @Log(title = "客户供应商公司", businessType = BusinessType.DELETE)
    @DeleteMapping
    public AjaxResult remove(@RequestBody ExtCompany extCompany) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "supplierList");
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "clientList");
        return toAjax(extCompanyService.deleteExtCompany(extCompany));
    }

    @Log(title = "客户供应商拉黑", businessType = BusinessType.OTHER)
    @PostMapping("/blackList")
    public AjaxResult joinInBlackList(@RequestBody ExtCompany extCompany) {
        extCompany.setBlacklistStaffId(SecurityUtils.getUserId());
        extCompany.setIsBlacklist("1");
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "supplierList");
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "clientList");
        return toAjax(extCompanyService.justUpdate(extCompany));
    }

    @Log(title = "客户供应商合并", businessType = BusinessType.OTHER)
    @GetMapping(value = "/{saveCompanyId}/{delCompanyId}")
    public AjaxResult mergeCompany(@PathVariable("saveCompanyId") Long saveCompanyId, @PathVariable("delCompanyId") Long delCompanyId) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "supplierList");
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "clientList");
        return AjaxResult.success(extCompanyService.mergeCompany(saveCompanyId, delCompanyId));
    }

    @PreAuthorize("@ss.hasPermi('system:company:list')")
    @GetMapping("/companys")
    public TableDataInfo companys(ExtCompany extCompany) {
        startPage();
        List<ExtCompany> list = extCompanyService.selectExtCompanys(extCompany);
        return getDataTable(list);
    }

    /**
     * 根据rct查询公司
     *
     * @param extCompany
     * @return
     */
    @GetMapping("/listByRct")
    public AjaxResult listByRct(ExtCompany extCompany) {
        List<ExtCompany> list = extCompanyService.selectExtCompanysByRct(extCompany);
        return AjaxResult.success(list);
    }

}
