package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.BasBlType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.BasBlTypeService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 提单类型Controller
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@RestController
@RequestMapping("/system/bltype")
public class BasBlTypeController extends BaseController {
    @Autowired
    private BasBlTypeService basBlTypeService;

    /**
     * 查询提单类型列表
     */
    @GetMapping("/list")
    public TableDataInfo list(BasBlType basBlType) {
        startPage();
        List<BasBlType> list = basBlTypeService.selectBasBlTypeList(basBlType);
        return getDataTable(list);
    }

    /**
     * 导出提单类型列表
     */
    @PreAuthorize("@ss.hasPermi('system:bltype:export')")
    @Log(title = "提单类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasBlType basBlType) {
        List<BasBlType> list = basBlTypeService.selectBasBlTypeList(basBlType);
        ExcelUtil<BasBlType> util = new ExcelUtil<BasBlType>(BasBlType.class);
        util.exportExcel(response, list, "提单类型数据");
    }

    /**
     * 获取提单类型详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:bltype:query')")
    @GetMapping(value = "/{blTypeCode}")
    public AjaxResult getInfo(@PathVariable("blTypeCode") String blTypeCode) {
        return AjaxResult.success(basBlTypeService.selectBasBlTypeByBlTypeCode(blTypeCode));
    }

    /**
     * 新增提单类型
     */
    @PreAuthorize("@ss.hasPermi('system:bltype:add')")
    @Log(title = "提单类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasBlType basBlType) {
        return toAjax(basBlTypeService.insertBasBlType(basBlType));
    }

    /**
     * 修改提单类型
     */
    @PreAuthorize("@ss.hasPermi('system:bltype:edit')")
    @Log(title = "提单类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasBlType basBlType) {
        return toAjax(basBlTypeService.updateBasBlType(basBlType));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:bltype:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasBlType basBlType) {
        basBlType.setUpdateBy(getUserId());
        return toAjax(basBlTypeService.changeStatus(basBlType));
    }

    /**
     * 删除提单类型
     */
    @PreAuthorize("@ss.hasPermi('system:bltype:remove')")
    @Log(title = "提单类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{blTypeCodes}")
    public AjaxResult remove(@PathVariable String[] blTypeCodes) {
        return toAjax(basBlTypeService.deleteBasBlTypeByBlTypeCodes(blTypeCodes));
    }
}
