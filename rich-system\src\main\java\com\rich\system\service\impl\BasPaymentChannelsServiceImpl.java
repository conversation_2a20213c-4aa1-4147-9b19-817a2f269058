package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.BasPaymentChannels;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.BasPaymentChannelsMapper;
import com.rich.system.service.BasPaymentChannelsService;

/**
 * 收汇方式Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-06-16
 */
@Service
public class BasPaymentChannelsServiceImpl implements BasPaymentChannelsService
{
    @Autowired
    private BasPaymentChannelsMapper basPaymentChannelsMapper;

    /**
     * 查询收汇方式
     * 
     * @param paymentChannelsId 收汇方式主键
     * @return 收汇方式
     */
    @Override
    public BasPaymentChannels selectBasPaymentChannelsByPaymentChannelsId(Long paymentChannelsId)
    {
        return basPaymentChannelsMapper.selectBasPaymentChannelsByPaymentChannelsId(paymentChannelsId);
    }

    /**
     * 查询收汇方式列表
     * 
     * @param basPaymentChannels 收汇方式
     * @return 收汇方式
     */
    @Override
    public List<BasPaymentChannels> selectBasPaymentChannelsList(BasPaymentChannels basPaymentChannels)
    {
        return basPaymentChannelsMapper.selectBasPaymentChannelsList(basPaymentChannels);
    }

    /**
     * 新增收汇方式
     * 
     * @param basPaymentChannels 收汇方式
     * @return 结果
     */
    @Override
    public int insertBasPaymentChannels(BasPaymentChannels basPaymentChannels)
    {
        basPaymentChannels.setCreateTime(DateUtils.getNowDate());
        basPaymentChannels.setCreateBy(SecurityUtils.getUserId());
        return basPaymentChannelsMapper.insertBasPaymentChannels(basPaymentChannels);
    }

    /**
     * 修改收汇方式
     * 
     * @param basPaymentChannels 收汇方式
     * @return 结果
     */
    @Override
    public int updateBasPaymentChannels(BasPaymentChannels basPaymentChannels)
    {
        basPaymentChannels.setUpdateTime(DateUtils.getNowDate());
        basPaymentChannels.setUpdateBy(SecurityUtils.getUserId());
        return basPaymentChannelsMapper.updateBasPaymentChannels(basPaymentChannels);
    }

    /**
     * 修改收汇方式状态
     *
     * @param basPaymentChannels 收汇方式
     * @return 收汇方式
     */
    @Override
    public int changeStatus(BasPaymentChannels basPaymentChannels) {
        return basPaymentChannelsMapper.updateBasPaymentChannels(basPaymentChannels);
    }

    /**
     * 批量删除收汇方式
     * 
     * @param paymentChannelsIds 需要删除的收汇方式主键
     * @return 结果
     */
    @Override
    public int deleteBasPaymentChannelsByPaymentChannelsIds(Long[] paymentChannelsIds)
    {
        return basPaymentChannelsMapper.deleteBasPaymentChannelsByPaymentChannelsIds(paymentChannelsIds);
    }

    /**
     * 删除收汇方式信息
     * 
     * @param paymentChannelsId 收汇方式主键
     * @return 结果
     */
    @Override
    public int deleteBasPaymentChannelsByPaymentChannelsId(Long paymentChannelsId)
    {
        return basPaymentChannelsMapper.deleteBasPaymentChannelsByPaymentChannelsId(paymentChannelsId);
    }
}
