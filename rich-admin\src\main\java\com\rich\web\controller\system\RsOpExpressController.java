package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsOpExpress;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsOpExpressService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 快递服务Controller
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@RestController
@RequestMapping("/system/opexpress")
public class RsOpExpressController extends BaseController {
    @Autowired
    private RsOpExpressService rsOpExpressService;

    /**
     * 查询快递服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opexpress:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsOpExpress rsOpExpress) {
        startPage();
        List<RsOpExpress> list = rsOpExpressService.selectRsOpExpressList(rsOpExpress);
        return getDataTable(list);
    }

    /**
     * 导出快递服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opexpress:export')")
    @Log(title = "快递服务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsOpExpress rsOpExpress) {
        List<RsOpExpress> list = rsOpExpressService.selectRsOpExpressList(rsOpExpress);
        ExcelUtil<RsOpExpress> util = new ExcelUtil<RsOpExpress>(RsOpExpress.class);
        util.exportExcel(response, list, "快递服务数据");
    }

    /**
     * 获取快递服务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:opexpress:query')")
    @GetMapping(value = "/{expressId}")
    public AjaxResult getInfo(@PathVariable("expressId") Long expressId) {
        return AjaxResult.success(rsOpExpressService.selectRsOpExpressByExpressId(expressId));
    }

    /**
     * 新增快递服务
     */
    @PreAuthorize("@ss.hasPermi('system:opexpress:add')")
    @Log(title = "快递服务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsOpExpress rsOpExpress) {
        return toAjax(rsOpExpressService.insertRsOpExpress(rsOpExpress));
    }

    /**
     * 修改快递服务
     */
    @PreAuthorize("@ss.hasPermi('system:opexpress:edit')")
    @Log(title = "快递服务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsOpExpress rsOpExpress) {
        return toAjax(rsOpExpressService.updateRsOpExpress(rsOpExpress));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:opexpress:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsOpExpress rsOpExpress) {
        rsOpExpress.setUpdateBy(getUserId());
        return toAjax(rsOpExpressService.changeStatus(rsOpExpress));
    }

    /**
     * 删除快递服务
     */
    @PreAuthorize("@ss.hasPermi('system:opexpress:remove')")
    @Log(title = "快递服务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{expressIds}")
    public AjaxResult remove(@PathVariable Long[] expressIds) {
        return toAjax(rsOpExpressService.deleteRsOpExpressByExpressIds(expressIds));
    }
}
