package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.RsOperationalProcess;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.RsOperationalProcessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 操作进度Controller
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@RestController
@RequestMapping("/system/operationalprocess")
public class RsOperationalProcessController extends BaseController {
    @Autowired
    private RsOperationalProcessService rsOperationalProcessService;

    /**
     * 查询操作进度列表
     */
    @PreAuthorize("@ss.hasPermi('system:operationalprocess:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsOperationalProcess rsOperationalProcess) {
        startPage();
        List<RsOperationalProcess> list = rsOperationalProcessService.selectRsOperationalProcessList(rsOperationalProcess);
        return getDataTable(list);
    }

    /**
     * 导出操作进度列表
     */
    @PreAuthorize("@ss.hasPermi('system:operationalprocess:export')")
    @Log(title = "操作进度", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsOperationalProcess rsOperationalProcess) {
        List<RsOperationalProcess> list = rsOperationalProcessService.selectRsOperationalProcessList(rsOperationalProcess);
        ExcelUtil<RsOperationalProcess> util = new ExcelUtil<RsOperationalProcess>(RsOperationalProcess.class);
        util.exportExcel(response, list, "操作进度数据");
    }

    /**
     * 获取操作进度详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:operationalprocess:query')")
    @GetMapping(value = "/{operationalProcessId}")
    public AjaxResult getInfo(@PathVariable("operationalProcessId") Long operationalProcessId) {
        return AjaxResult.success(rsOperationalProcessService.selectRsOperationalProcessByOperationalProcessId(operationalProcessId));
    }

    /**
     * 新增操作进度
     */
    @PreAuthorize("@ss.hasPermi('system:operationalprocess:add')")
    @Log(title = "操作进度", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsOperationalProcess rsOperationalProcess) {
        return AjaxResult.success(rsOperationalProcessService.insertRsOperationalProcess(rsOperationalProcess));
    }

    /**
     * 修改操作进度
     */
    @PreAuthorize("@ss.hasPermi('system:operationalprocess:edit')")
    @Log(title = "操作进度", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsOperationalProcess rsOperationalProcess) {
        return toAjax(rsOperationalProcessService.updateRsOperationalProcess(rsOperationalProcess));
    }

    /**
     * 删除操作进度
     */
    @PreAuthorize("@ss.hasPermi('system:operationalprocess:edit')")
    @Log(title = "操作进度", businessType = BusinessType.UPDATE)
    @PostMapping("/del")
    public AjaxResult del(@RequestBody RsOperationalProcess rsOperationalProcess) {
        return toAjax(rsOperationalProcessService.delRsOperationalProcess(rsOperationalProcess));
    }
}
