package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.config.RichConfig;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BankTransactionRecordDTO;
import com.rich.common.core.domain.entity.RsBankRecord;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.file.FileUploadUtils;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.framework.config.ServerConfig;
import com.rich.system.service.DataAggregatorService;
import com.rich.system.service.ExtCompanyService;
import com.rich.system.service.RsBankRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 记录公司账户出入账明细Controller
 *
 * <AUTHOR>
 * @date 2024-04-29
 */
@RestController
@RequestMapping("/system/bankrecord")
public class RsBankRecordController extends BaseController {
    @Autowired
    private RsBankRecordService rsBankRecordService;

    @Resource
    private ExtCompanyService extCompanyService;

    @Autowired
    private ServerConfig serverConfig;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private DataAggregatorService dataAggregatorService;


    /**
     * 查询记录公司账户出入账明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:bankrecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsBankRecord rsBankRecord) {
        startPage();
        List<RsBankRecord> list = rsBankRecordService.selectRsBankRecordList(rsBankRecord);
        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('system:bankrecord:list')")
    @GetMapping("/aggregator")
    public AjaxResult aggregator(RsBankRecord rsBankRecord) {

        try {
            List<RsBankRecord> list = rsBankRecordService.selectRsBankRecordList(rsBankRecord);

            Map<String, Object> aggregatorConfig;
            try {
                aggregatorConfig = dataAggregatorService.extractAggregatorConfig(rsBankRecord);
            } catch (RuntimeException e) {
                logger.error("提取聚合配置失败", e);
                return AjaxResult.error(e.getMessage());
            }

            if (aggregatorConfig.isEmpty() || aggregatorConfig.get("primaryField") == null) {
                return AjaxResult.success(list);
            }

            List<Map<String, Object>> aggregatedData = dataAggregatorService.aggregateData(list, aggregatorConfig);
            return AjaxResult.success(aggregatedData);
        } catch (Exception e) {
            logger.error("数据汇总处理失败", e);
            return AjaxResult.error("数据汇总处理失败: " + e.getMessage());
        }
    }

    /**
     * 导出记录公司账户出入账明细列表
     */
//    @PreAuthorize("@ss.hasPermi('system:bankrecord:export')")
    @Log(title = "记录公司账户出入账明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsBankRecord rsBankRecord) {
        List<RsBankRecord> list = rsBankRecordService.selectRsBankRecordList(rsBankRecord);
        ExcelUtil<RsBankRecord> util = new ExcelUtil<RsBankRecord>(RsBankRecord.class);
        util.exportExcel(response, list, "记录公司账户出入账明细数据");
    }

    /**
     * 获取记录公司账户出入账明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:bankrecord:list')")
    @GetMapping(value = "/{bankRecordId}")
    public AjaxResult getInfo(@PathVariable("bankRecordId") Long bankRecordId) {
        RsBankRecord data = rsBankRecordService.selectRsBankRecordByBankRecordId(bankRecordId);
        AjaxResult ajaxResult = AjaxResult.success();
        ajaxResult.put(AjaxResult.DATA_TAG, data);
        ajaxResult.put("companyList", data.getClearingCompanyId() != null ? extCompanyService.selectExtCompanyByCompanyId(data.getClearingCompanyId()) : new ArrayList<>());
        return ajaxResult;
    }

    /**
     * 新增记录公司账户出入账明细
     */
    @PreAuthorize("@ss.hasPermi('system:bankrecord:add')")
    @Log(title = "记录公司账户出入账明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsBankRecord rsBankRecord) {
        return AjaxResult.success(rsBankRecordService.insertRsBankRecord(rsBankRecord));
    }

    /**
     * 修改记录公司账户出入账明细
     */
    @PreAuthorize("@ss.hasPermi('system:bankrecord:edit')")
    @Log(title = "记录公司账户出入账明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsBankRecord rsBankRecord) {
        return toAjax(rsBankRecordService.updateRsBankRecord(rsBankRecord));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:bankrecord:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsBankRecord rsBankRecord) {
        rsBankRecord.setUpdateBy(getUserId());
        return toAjax(rsBankRecordService.changeStatus(rsBankRecord));
    }

    /**
     * 删除记录公司账户出入账明细
     */
    @PreAuthorize("@ss.hasPermi('system:bankrecord:remove')")
    @Log(title = "记录公司账户出入账明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{bankRecordIds}")
    public AjaxResult remove(@PathVariable Long[] bankRecordIds) {
        return toAjax(rsBankRecordService.deleteRsBankRecordByBankRecordIds(bankRecordIds));
    }

    @Value("${path.prefix}")
    private String pathPrefix;

    @PreAuthorize("@ss.hasPermi('system:bankrecord:add')")
    @PostMapping("/uploadImg")
    public AjaxResult uploadFile(@RequestParam("file") MultipartFile file) throws Exception {
        try {
            // 上传文件路径
            String filePath = RichConfig.getBankSlipUploadPath();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file);
            String url = serverConfig.getUrl() + pathPrefix + fileName;
            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", url);
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @DeleteMapping("/deleteImg")
    public AjaxResult deleteImg(@RequestParam("url") String url) {
        // 解析出文件路径 http://localhost/dev-api/profile/bankSlipImage/2024-06-24/AR240513023_20240624160621A001.jpg
        if (!url.startsWith(serverConfig.getUrl() + pathPrefix)) {
            return AjaxResult.error("图片路径不存在,删除失败");
        }

        // 提取相对路径部分 /profile/bankSlipImage/2024-06-24/AR240513023_20240624160621A001.jpg
        String relativePath = url.substring((serverConfig.getUrl() + pathPrefix).length());

        // 生成实际文件路径 /data/profile/bankSlipImage/profile/bankSlipImage/2024-06-24/AR240513023_20240624160621A001.jpg
        String filePath = Paths.get("/data", relativePath).toString();

        File file = new File(filePath);
        if (file.exists() && file.isFile()) {
            if (file.delete()) {
                return AjaxResult.success("删除成功");
            } else {
                return AjaxResult.error("删除失败");
            }
        } else {
            return AjaxResult.error("路径不存在");
        }
    }

    @PostMapping("/statistics")
    public AjaxResult accountFundStatistics(@RequestBody RsBankRecord rsBankRecord) {
        return AjaxResult.success(rsBankRecordService.getAccountFundStatistics(rsBankRecord));
    }

    /**
     * 下载导入模板
     *
     * @param response
     * @throws IOException
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException {
        try {
            ExcelUtil<RsBankRecord> util = new ExcelUtil<>(RsBankRecord.class);
            util.importTemplateExcel(response, "贡献流水");
        } catch (Exception ignore) {

        }
    }

    /**
     * 处理导入数据
     *
     * @param file
     * @param chargeTypeId
     * @param
     * @return
     * @throws Exception
     */
    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
//    @PreAuthorize("@ss.hasPermi('system:freight:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, Long chargeTypeId) throws Exception {
        // 移除缓存
//        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "freight");
        ExcelUtil<BankTransactionRecordDTO> util = new ExcelUtil<>(BankTransactionRecordDTO.class);
        // 解析excel文件,获取数据
        List<BankTransactionRecordDTO> bankRecordList = util.importExcel(file.getInputStream());
        List<BankTransactionRecordDTO> failList = rsBankRecordService.importBankRecord(bankRecordList, chargeTypeId);
        if (!failList.isEmpty()) {
            redisCache.setCacheObject("importFailList", failList);
            return AjaxResult.success("上传失败列表");
        } else {
            return AjaxResult.success("全部上传成功");
        }
    }

}
