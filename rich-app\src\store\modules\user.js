import config from "@/config";
import storage from "@/utils/storage";
import constant from "@/utils/constant";
import {isHttp, isEmpty} from "@/utils/validate";
import {login, logout, getInfo, wechatLogin} from "@/api/login";
import {getToken, setToken, removeToken} from "@/utils/auth";
import defAva from "@/static/images/profile.jpg";

const baseUrl = config.baseUrl;

const user = {
    state: {
        token: getToken(),
        id: storage.get(constant.id),
        name: storage.get(constant.name),
        avatar: storage.get(constant.avatar),
        roles: storage.get(constant.roles),
        permissions: storage.get(constant.permissions),
        mpWarehouseClient: storage.get(constant.mpWarehouseClient),
    },

    mutations: {
        SET_TOKEN: (state, token) => {
            state.token = token;
            setToken(token);
        },
        SET_ID: (state, id) => {
            state.id = id;
            storage.set(constant.id, id);
        },
        SET_NAME: (state, name) => {
            state.name = name;
            storage.set(constant.name, name);
        },
        SET_AVATAR: (state, avatar) => {
            state.avatar = avatar;
            storage.set(constant.avatar, avatar);
        },
        SET_ROLES: (state, roles) => {
            state.roles = roles;
            storage.set(constant.roles, roles);
        },
        SET_PERMISSIONS: (state, permissions) => {
            state.permissions = permissions;
            storage.set(constant.permissions, permissions);
        },
        SET_MP_WAREHOUSE_CLIENT: (state, mpWarehouseClient) => {
            state.mpWarehouseClient = mpWarehouseClient;
            storage.set(constant.mpWarehouseClient, mpWarehouseClient);
        },
    },

    actions: {
        // 登录
        Login({commit}, userInfo) {
            const username = userInfo.username.trim();
            const password = userInfo.password;
            const code = userInfo.code;
            const uuid = userInfo.uuid;
            return new Promise((resolve, reject) => {
                login(username, password, code, uuid)
                    .then((res) => {
                        setToken(res.token);
                        commit("SET_TOKEN", res.token);
                        resolve();
                    })
                    .catch((error) => {
                        reject(error);
                    });
            });
        },

        // 微信登录
        WechatLogin({commit}, code) {
            return new Promise((resolve, reject) => {
                wechatLogin(code)
                    .then((res) => {
                        // 后端登录成功，设置token
                        setToken(res.data.token);
                        commit("SET_TOKEN", res.data.token);
                        resolve(res);
                    })
                    .catch((error) => {
                        // 处理特定错误码
                        if (error.code === "A0204") {
                            // 用户不存在，需要注册
                            error.message = "user not found";
                        }
                        reject(error);
                    });
            });
        },

        // 获取用户信息
        GetInfo({commit, state}) {
            return new Promise((resolve, reject) => {
                getInfo()
                    .then((res) => {
                        const user = res.data;
                        console.log(res.data);

                        let avatar = user.avatar || "";
                        if (!isHttp(avatar)) {
                            avatar = isEmpty(avatar) ? defAva : baseUrl + avatar;
                        }
                        const userid =
                            isEmpty(user) || isEmpty(user.userId) ? "" : user.userId;
                        const username =
                            isEmpty(user) || isEmpty(user.userName) ? "" : user.userName;
                        if (res.data.roles && res.data.roles.length > 0) {
                            commit("SET_ROLES", res.data.roles);
                            commit("SET_PERMISSIONS", res.data.permissions);
                        } else {
                            commit("SET_ROLES", ["ROLE_DEFAULT"]);
                        }
                        commit("SET_MP_WAREHOUSE_CLIENT", user.mpWarehouseClient);
                        commit("SET_ID", userid);
                        commit("SET_NAME", user.fullName);
                        commit("SET_AVATAR", avatar);
                        resolve(res);
                    })
                    .catch((error) => {
                        reject(error);
                    });
            });
        },

        // 退出系统
        LogOut({commit, state}) {
            return new Promise((resolve, reject) => {
                logout(state.token)
                    .then(() => {
                        commit("SET_TOKEN", "");
                        commit("SET_ROLES", []);
                        commit("SET_PERMISSIONS", []);
                        removeToken();
                        storage.clean();
                        resolve();
                    })
                    .catch((error) => {
                        reject(error);
                    });
            });
        },
    },
};

export default user;
