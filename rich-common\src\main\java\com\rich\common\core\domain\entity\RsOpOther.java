package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/12/17 14:18
 * @Version 1.0
 */
public class RsOpOther extends BaseEntity {
    private List<RsCharge> rsChargeList;

    private List<RsDoc> rsDocList;
    private Long expandServiceId;

    private List<RsOpLog> rsOpLogList;
    private String sqdRctNo;
    private String sqdServiceDetailsCode;
    /**
     * 所属服务实例id ,
     */
    @Excel(name = "所属服务实例id ,")
    private Long serviceId;

    private RsServiceInstances rsServiceInstances;

    /**
     * 所属服务类型id ,
     */
    @Excel(name = "所属服务类型id ,")
    private Long sqdServiceTypeId;
    private BigDecimal payableRMB;
    private BigDecimal payableUSD;
    private BigDecimal payableUSDTax;
    private BigDecimal payableRMBTax;

    public BigDecimal getPayableRMBTax() {
        return payableRMBTax;
    }

    public void setPayableRMBTax(BigDecimal payableRMBTax) {
        this.payableRMBTax = payableRMBTax;
    }

    public BigDecimal getPayableUSDTax() {
        return payableUSDTax;
    }

    public void setPayableUSDTax(BigDecimal payableUSDTax) {
        this.payableUSDTax = payableUSDTax;
    }

    public BigDecimal getPayableUSD() {
        return payableUSD;
    }

    public void setPayableUSD(BigDecimal payableUSD) {
        this.payableUSD = payableUSD;
    }

    public BigDecimal getPayableRMB() {
        return payableRMB;
    }

    public void setPayableRMB(BigDecimal payableRMB) {
        this.payableRMB = payableRMB;
    }

    public Long getExpandServiceId() {
        return expandServiceId;
    }

    public void setExpandServiceId(Long expandServiceId) {
        this.expandServiceId = expandServiceId;
    }

    public String getSqdServiceDetailsCode() {
        return sqdServiceDetailsCode;
    }

    public void setSqdServiceDetailsCode(String sqdServiceDetailsCode) {
        this.sqdServiceDetailsCode = sqdServiceDetailsCode;
    }

    public String getSqdRctNo() {
        return sqdRctNo;
    }

    public void setSqdRctNo(String sqdRctNo) {
        this.sqdRctNo = sqdRctNo;
    }

    public RsServiceInstances getRsServiceInstances() {
        return rsServiceInstances;
    }

    public void setRsServiceInstances(RsServiceInstances rsServiceInstances) {
        this.rsServiceInstances = rsServiceInstances;
    }

    public List<RsDoc> getRsDocList() {
        return rsDocList;
    }

    public void setRsDocList(List<RsDoc> rsDocList) {
        this.rsDocList = rsDocList;
    }

    public List<RsOpLog> getRsOpLogList() {
        return rsOpLogList;
    }

    public void setRsOpLogList(List<RsOpLog> rsOpLogList) {
        this.rsOpLogList = rsOpLogList;
    }

    public List<RsCharge> getRsChargeList() {
        return rsChargeList;
    }

    public void setRsChargeList(List<RsCharge> rsChargeList) {
        this.rsChargeList = rsChargeList;
    }

    public Long getServiceId() {
        return serviceId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    public Long getSqdServiceTypeId() {
        return sqdServiceTypeId;
    }

    public void setSqdServiceTypeId(Long sqdServiceTypeId) {
        this.sqdServiceTypeId = sqdServiceTypeId;
    }
}
