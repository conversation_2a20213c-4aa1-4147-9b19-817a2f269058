package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasPaymentChannels;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasPaymentChannelsService;
import com.rich.system.service.impl.RedisCacheImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 收汇方式Controller
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@RestController
@RequestMapping("/system/paymentchannels")
public class BasPaymentChannelsController extends BaseController {
    @Autowired
    private BasPaymentChannelsService basPaymentChannelsService;

    @Autowired
    private com.rich.common.core.redis.RedisCache redisCache;

    @Autowired
    private RedisCacheImpl RedisCache;

    /**
     * 查询收汇方式列表
     */
    @PreAuthorize("@ss.hasPermi('system:paymentchannels:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasPaymentChannels basPaymentChannels) {
        startPage();
        List<BasPaymentChannels> list = basPaymentChannelsService.selectBasPaymentChannelsList(basPaymentChannels);
        return getDataTable(list);
    }

    /**
     * 查询收汇方式列表
     */
    @GetMapping("/selectList")
    public AjaxResult selectList() {
        List<BasPaymentChannels> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "paymentChannels");
        if (list == null) {
            RedisCache.paymentChannels();
            list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "paymentChannels");
        }
        return AjaxResult.success(list);
    }

    /**
     * 导出收汇方式列表
     */
    @PreAuthorize("@ss.hasPermi('system:paymentchannels:export')")
    @Log(title = "收汇方式", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasPaymentChannels basPaymentChannels) {
        List<BasPaymentChannels> list = basPaymentChannelsService.selectBasPaymentChannelsList(basPaymentChannels);
        ExcelUtil<BasPaymentChannels> util = new ExcelUtil<BasPaymentChannels>(BasPaymentChannels.class);
        util.exportExcel(response, list, "收汇方式数据");
    }

    /**
     * 获取收汇方式详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:paymentchannels:query')")
    @GetMapping(value = "/{paymentChannelsId}")
    public AjaxResult getInfo(@PathVariable("paymentChannelsId") Long paymentChannelsId) {
        return AjaxResult.success(basPaymentChannelsService.selectBasPaymentChannelsByPaymentChannelsId(paymentChannelsId));
    }

    /**
     * 新增收汇方式
     */
    @PreAuthorize("@ss.hasPermi('system:paymentchannels:add')")
    @Log(title = "收汇方式", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasPaymentChannels basPaymentChannels) {
        return toAjax(basPaymentChannelsService.insertBasPaymentChannels(basPaymentChannels));
    }

    /**
     * 修改收汇方式
     */
    @PreAuthorize("@ss.hasPermi('system:paymentchannels:edit')")
    @Log(title = "收汇方式", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasPaymentChannels basPaymentChannels) {
        return toAjax(basPaymentChannelsService.updateBasPaymentChannels(basPaymentChannels));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:paymentchannels:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasPaymentChannels basPaymentChannels) {
        basPaymentChannels.setUpdateBy(getUserId());
        return toAjax(basPaymentChannelsService.changeStatus(basPaymentChannels));
    }

    /**
     * 删除收汇方式
     */
    @PreAuthorize("@ss.hasPermi('system:paymentchannels:remove')")
    @Log(title = "收汇方式", businessType = BusinessType.DELETE)
    @DeleteMapping("/{paymentChannelsIds}")
    public AjaxResult remove(@PathVariable Long[] paymentChannelsIds) {
        return toAjax(basPaymentChannelsService.deleteBasPaymentChannelsByPaymentChannelsIds(paymentChannelsIds));
    }
}
