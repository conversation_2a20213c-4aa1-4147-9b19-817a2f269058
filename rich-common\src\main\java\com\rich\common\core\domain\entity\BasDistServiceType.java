package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 bas_dist_service_type
 *
 * <AUTHOR>
 * @date 2022-08-29
 */
public class BasDistServiceType extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 服务类型
     */
    private Long serviceTypeId;

    /**
     * 父ID
     */
    private Long parentId;

    private String typeId;

    private String ancestors;

    private String serviceShortName;

    /**
     * 服务类型中文名
     */
    @Excel(name = "服务类型中文名")
    private String serviceLocalName;

    /**
     * 服务类型英文名
     */
    @Excel(name = "服务类型英文名")
    private String serviceEnName;

    /**
     * 是否上锁(0：是，1：不是)
     */
    @Excel(name = "是否上锁")
    private String isLocked;

    /**
     * 涉及的收费项目列表，多选
     */
    @Excel(name = "涉及的收费项目列表，多选")
    private String chargeIdList;

    /**
     * 是否为基础物流服务
     */
    @Excel(name = "是否为基础物流服务")
    private String isBasicTransport;

    /**
     * 同级别知名度
     */
    @Excel(name = "同级别知名度")
    private Integer orderNum;

    /**
     * 上下层知名度（当上一层小于下层时，不显示上一层）
     */
    @Excel(name = "上下层知名度")
    private Long verticalSort;

    /**
     * 运输类型等级
     */
    @Excel(name = "运输类型等级")
    private Long serviceTypeLevel;

    /**
     * 使用状态
     */
    @Excel(name = "使用状态")
    private String status;

    private Long[] carrierIds;

    private BasCarrier carrier;

    private Boolean isDisable;

    private String serviceQuery;

    public String getTypeId() {
        return typeId;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

    public String getServiceQuery() {
        return serviceQuery;
    }

    public void setServiceQuery(String serviceQuery) {
        this.serviceQuery = serviceQuery;
    }

    public String getServiceShortName() {
        return serviceShortName;
    }

    public void setServiceShortName(String serviceShortName) {
        this.serviceShortName = serviceShortName;
    }

    public String getAncestors() {
        return ancestors;
    }

    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }

    public Boolean getIsDisable() {
        return isDisable;
    }

    public void setIsDisable(Boolean disable) {
        isDisable = disable;
    }

    public BasCarrier getCarrier() {
        return carrier;
    }

    public void setCarrier(BasCarrier carrier) {
        this.carrier = carrier;
    }

    public Long[] getCarrierIds() {
        return carrierIds;
    }

    public void setCarrierIds(Long[] carrierIds) {
        this.carrierIds = carrierIds;
    }

    public void setServiceTypeId(Long serviceTypeId) {
        this.serviceTypeId = serviceTypeId;
    }

    public Long getServiceTypeId() {
        return serviceTypeId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setServiceLocalName(String serviceLocalName) {
        this.serviceLocalName = serviceLocalName;
    }

    public String getServiceLocalName() {
        return serviceLocalName;
    }

    public void setServiceEnName(String serviceEnName) {
        this.serviceEnName = serviceEnName;
    }

    public String getServiceEnName() {
        return serviceEnName;
    }

    public void setIsLocked(String isLocked) {
        this.isLocked = isLocked;
    }

    public String getIsLocked() {
        return isLocked;
    }

    public void setChargeIdList(String chargeIdList) {
        this.chargeIdList = chargeIdList;
    }

    public String getChargeIdList() {
        return chargeIdList;
    }

    public void setIsBasicTransport(String isBasicTransport) {
        this.isBasicTransport = isBasicTransport;
    }

    public String getIsBasicTransport() {
        return isBasicTransport;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setVerticalSort(Long verticalSort) {
        this.verticalSort = verticalSort;
    }

    public Long getVerticalSort() {
        return verticalSort;
    }

    public void setServiceTypeLevel(Long serviceTypeLevel) {
        this.serviceTypeLevel = serviceTypeLevel;
    }

    public Long getServiceTypeLevel() {
        return serviceTypeLevel;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
}
