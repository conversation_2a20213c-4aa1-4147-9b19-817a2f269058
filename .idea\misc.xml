<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="EntryPointsManager">
    <list size="2">
      <item index="0" class="java.lang.String" itemvalue="com.rich.common.annotation.Excel" />
      <item index="1" class="java.lang.String" itemvalue="org.springframework.beans.factory.annotation.Autowired" />
    </list>
  </component>
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/pom.xml" />
      </list>
    </option>
  </component>
  <component name="PWA">
    <option name="wasEnabledAtLeastOnce" value="true" />
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_1_8" default="true" project-jdk-name="1.8" project-jdk-type="JavaSDK" />
</project>