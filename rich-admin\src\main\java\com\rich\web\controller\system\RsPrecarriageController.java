package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsPrecarriage;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsPrecarriageService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 前程运输，操作单中的记录包含的前程运输信息Controller
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@RestController
@RequestMapping("/system/precarriage")
public class RsPrecarriageController extends BaseController {
    @Autowired
    private RsPrecarriageService rsPrecarriageService;

    /**
     * 查询前程运输，操作单中的记录包含的前程运输信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:precarriage:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsPrecarriage rsPrecarriage) {
        startPage();
        List<RsPrecarriage> list = rsPrecarriageService.selectRsPrecarriageList(rsPrecarriage);
        return getDataTable(list);
    }

    /**
     * 导出前程运输，操作单中的记录包含的前程运输信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:precarriage:export')")
    @Log(title = "前程运输，操作单中的记录包含的前程运输信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsPrecarriage rsPrecarriage) {
        List<RsPrecarriage> list = rsPrecarriageService.selectRsPrecarriageList(rsPrecarriage);
        ExcelUtil<RsPrecarriage> util = new ExcelUtil<RsPrecarriage>(RsPrecarriage.class);
        util.exportExcel(response, list, "前程运输，操作单中的记录包含的前程运输信息数据");
    }

    /**
     * 获取前程运输，操作单中的记录包含的前程运输信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:precarriage:query')")
    @GetMapping(value = "/{precarriageId}")
    public AjaxResult getInfo(@PathVariable("precarriageId") Long precarriageId) {
        return AjaxResult.success(rsPrecarriageService.selectRsPrecarriageByPrecarriageId(precarriageId));
    }

    /**
     * 新增前程运输，操作单中的记录包含的前程运输信息
     */
    @PreAuthorize("@ss.hasPermi('system:precarriage:add')")
    @Log(title = "前程运输，操作单中的记录包含的前程运输信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsPrecarriage rsPrecarriage) {
        return toAjax(rsPrecarriageService.insertRsPrecarriage(rsPrecarriage));
    }

    /**
     * 修改前程运输，操作单中的记录包含的前程运输信息
     */
    @PreAuthorize("@ss.hasPermi('system:precarriage:edit')")
    @Log(title = "前程运输，操作单中的记录包含的前程运输信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsPrecarriage rsPrecarriage) {
        return toAjax(rsPrecarriageService.updateRsPrecarriage(rsPrecarriage));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:precarriage:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsPrecarriage rsPrecarriage) {
        rsPrecarriage.setUpdateBy(getUserId());
        return toAjax(rsPrecarriageService.changeStatus(rsPrecarriage));
    }

    /**
     * 删除前程运输，操作单中的记录包含的前程运输信息
     */
    @PreAuthorize("@ss.hasPermi('system:precarriage:remove')")
    @Log(title = "前程运输，操作单中的记录包含的前程运输信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{precarriageIds}")
    public AjaxResult remove(@PathVariable Long[] precarriageIds) {
        return toAjax(rsPrecarriageService.deleteRsPrecarriageByPrecarriageIds(precarriageIds));
    }
}
