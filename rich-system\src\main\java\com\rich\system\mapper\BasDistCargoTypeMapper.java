package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasDistCargoType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 货物特征Mapper接口
 *
 * <AUTHOR>
 * @date 2022-08-29
 */
@Mapper
public interface BasDistCargoTypeMapper {
    /**
     * 查询货物特征
     *
     * @param cargoTypeId 货物特征主键
     * @return 货物特征
     */
    BasDistCargoType selectBasDistCargoTypeByCargoTypeId(Long cargoTypeId);

    /**
     * 查询货物特征列表
     *
     * @param basDistCargoType 货物特征
     * @return 货物特征集合
     */
    List<BasDistCargoType> selectBasDistCargoTypeList(BasDistCargoType basDistCargoType);

    /**
     * 新增货物特征
     *
     * @param basDistCargoType 货物特征
     * @return 结果
     */
    int insertBasDistCargoType(BasDistCargoType basDistCargoType);

    /**
     * 修改货物特征
     *
     * @param basDistCargoType 货物特征
     * @return 结果
     */
    int updateBasDistCargoType(BasDistCargoType basDistCargoType);

    /**
     * 删除货物特征
     *
     * @param cargoTypeId 货物特征主键
     * @return 结果
     */
    int deleteBasDistCargoTypeByCargoTypeId(Long cargoTypeId);

    /**
     * 批量删除货物特征
     *
     * @param cargoTypeIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasDistCargoTypeByCargoTypeIds(Long[] cargoTypeIds);

    List<BasDistCargoType> selectChildrenCargoTypeById(Long cargoTypeId);

    void updateCargoTypeChildren(@Param("cargoTypes") List<BasDistCargoType> cargoTypes);

    void updateCargoTypeStatusNormal(Long[] cargoTypeIds);

}
