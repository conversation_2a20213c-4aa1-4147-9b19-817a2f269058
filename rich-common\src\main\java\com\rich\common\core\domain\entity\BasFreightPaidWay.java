package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 付款方式对象 bas_freight_paid_way
 *
 * <AUTHOR>
 * @date 2024-03-25
 */
public class BasFreightPaidWay extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private String freightPaidWayCode;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String freightPaidWayLocalName;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String freightPaidWayEnName;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String orderNum;

    public String getFreightPaidWayCode() {
        return freightPaidWayCode;
    }

    public void setFreightPaidWayCode(String freightPaidWayCode) {
        this.freightPaidWayCode = freightPaidWayCode;
    }

    public String getFreightPaidWayLocalName() {
        return freightPaidWayLocalName;
    }

    public void setFreightPaidWayLocalName(String freightPaidWayLocalName) {
        this.freightPaidWayLocalName = freightPaidWayLocalName;
    }

    public String getFreightPaidWayEnName() {
        return freightPaidWayEnName;
    }

    public void setFreightPaidWayEnName(String freightPaidWayEnName) {
        this.freightPaidWayEnName = freightPaidWayEnName;
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("freightPaidWayCode", getFreightPaidWayCode())
                .append("freightPaidWayLocalName", getFreightPaidWayLocalName())
                .append("freightPaidWayEnName", getFreightPaidWayEnName())
                .append("orderNum", getOrderNum())
                .append("remark", getRemark())
                .toString();
    }
}
