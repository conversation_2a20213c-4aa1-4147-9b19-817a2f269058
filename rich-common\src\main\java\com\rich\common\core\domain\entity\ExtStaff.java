package com.rich.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 外部员工对象 ext_staff
 *
 * <AUTHOR>
 * @date 2022-09-27
 */
public class ExtStaff extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long staffId;

    /**
     * 所属公司
     */
    @Excel(name = "所属公司")
    private Long sqdCompanyId;

    /**
     * 员工简称，用于商业正式称谓
     */
    @Excel(name = "员工简称，用于商业正式称谓")
    private String staffShortName;

    /**
     * 中文姓名
     */
    @Excel(name = "中文姓名")
    private String staffLocalName;

    /**
     * 英文姓名
     */
    @Excel(name = "英文姓名")
    private String staffEnName;

    /**
     * 个人邮箱
     */
    @Excel(name = "电话号码")
    private String staffTelNum;

    /**
     * 企业邮箱
     */
    @Excel(name = "企业邮箱")
    private String staffEmailEnterprise;

    /**
     * 手机号码
     */
    @Excel(name = "手机号码")
    private String staffPhoneNum;

    /**
     * 个人生日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "个人生日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date staffBirthday;

    /**
     * 性别（0：男，1：女
     */
    @Excel(name = "性别", readConverterExp = "性别（0：男，1：女")
    private String staffGender;

    @Excel(name = "微信")
    private String staffWechat;

    @Excel(name = "QQ")
    private String staffQq;

    /**
     * 地址
     */
    @Excel(name = "地址")
    private Long locationId;

    private String isMain;

    /**
     * 详细住址
     */
    @Excel(name = "详细住址")
    private String staffAddress;

    /**
     * 擅长语种
     */
    @Excel(name = "擅长语种")
    private String staffLanguage;

    @Excel(name = "国籍")
    private String staffCountry;

    @Excel(name = "民族")
    private String staffNation;

    @Excel(name = "宗教")
    private String staffReligion;

    /**
     * 部门名称，手动输入
     */
    @Excel(name = "部门名称，手动输入")
    private String deptName;

    /**
     * 职位名称，手动输入
     */
    @Excel(name = "职位名称，手动输入")
    private String deptPositionName;

    /**
     * 是否入职（0：未入职，1：在职，-1：离职
     */
    @Excel(name = "是否入职", readConverterExp = "是否入职（0：未入职，1：在职，-1：离职")
    private String staffJobStatus;
    private String staffWhatsapp;
    private String staffOtherContact;

    public String getStaffOtherContact() {
        return staffOtherContact;
    }

    public void setStaffOtherContact(String staffOtherContact) {
        this.staffOtherContact = staffOtherContact;
    }

    public String getStaffWhatsapp() {
        return staffWhatsapp;
    }

    public void setStaffWhatsapp(String staffWhatsapp) {
        this.staffWhatsapp = staffWhatsapp;
    }

    public String getStaffTelNum() {
        return staffTelNum;
    }

    public void setStaffTelNum(String staffTelNum) {
        this.staffTelNum = staffTelNum;
    }

    public String getStaffPhoneNum() {
        return staffPhoneNum;
    }

    public void setStaffPhoneNum(String staffPhoneNum) {
        this.staffPhoneNum = staffPhoneNum;
    }

    public String getStaffCountry() {
        return staffCountry;
    }

    public void setStaffCountry(String staffCountry) {
        this.staffCountry = staffCountry;
    }

    public String getStaffNation() {
        return staffNation;
    }

    public void setStaffNation(String staffNation) {
        this.staffNation = staffNation;
    }

    public String getStaffReligion() {
        return staffReligion;
    }

    public void setStaffReligion(String staffReligion) {
        this.staffReligion = staffReligion;
    }

    public String getIsMain() {
        return isMain;
    }

    public void setIsMain(String isMain) {
        this.isMain = isMain;
    }

    public String getStaffWechat() {
        return staffWechat;
    }

    public void setStaffWechat(String staffWechat) {
        this.staffWechat = staffWechat;
    }

    public String getStaffQq() {
        return staffQq;
    }

    public void setStaffQq(String staffQq) {
        this.staffQq = staffQq;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setSqdCompanyId(Long sqdCompanyId) {
        this.sqdCompanyId = sqdCompanyId;
    }

    public Long getSqdCompanyId() {
        return sqdCompanyId;
    }

    public void setStaffShortName(String staffShortName) {
        this.staffShortName = staffShortName;
    }

    public String getStaffShortName() {
        return staffShortName;
    }

    public void setStaffLocalName(String staffLocalName) {
        this.staffLocalName = staffLocalName;
    }

    public String getStaffLocalName() {
        return staffLocalName;
    }

    public void setStaffEnName(String staffEnName) {
        this.staffEnName = staffEnName;
    }

    public String getStaffEnName() {
        return staffEnName;
    }

    public void setStaffEmailEnterprise(String staffEmailEnterprise) {
        this.staffEmailEnterprise = staffEmailEnterprise;
    }

    public String getStaffEmailEnterprise() {
        return staffEmailEnterprise;
    }

    public void setStaffBirthday(Date staffBirthday) {
        this.staffBirthday = staffBirthday;
    }

    public Date getStaffBirthday() {
        return staffBirthday;
    }

    public void setStaffGender(String staffGender) {
        this.staffGender = staffGender;
    }

    public String getStaffGender() {
        return staffGender;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setStaffAddress(String staffAddress) {
        this.staffAddress = staffAddress;
    }

    public String getStaffAddress() {
        return staffAddress;
    }

    public void setStaffLanguage(String staffLanguage) {
        this.staffLanguage = staffLanguage;
    }

    public String getStaffLanguage() {
        return staffLanguage;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptPositionName(String deptPositionName) {
        this.deptPositionName = deptPositionName;
    }

    public String getDeptPositionName() {
        return deptPositionName;
    }

    public void setStaffJobStatus(String staffJobStatus) {
        this.staffJobStatus = staffJobStatus;
    }

    public String getStaffJobStatus() {
        return staffJobStatus;
    }
}
