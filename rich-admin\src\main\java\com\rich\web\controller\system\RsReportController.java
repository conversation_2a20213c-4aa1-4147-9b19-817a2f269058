package com.rich.web.controller.system;

import com.alibaba.fastjson2.JSONObject;
import com.rich.common.core.domain.entity.RsOpBulkTruck;
import com.rich.common.core.domain.entity.RsRct;
import com.rich.system.mapper.RsOpTruckMapper;
import com.rich.system.service.RsRctService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/26 15:09
 * @Version 1.0
 */
@RestController
@RequestMapping("/system/report")
public class RsReportController {

    @Resource
    private RsRctService rsRctService;
    @Resource
    private RsOpTruckMapper rsOpTruckMapper;

    @GetMapping("/dispatching")
    public JSONObject dispatchingBill(Long rctId) {
        JSONObject jsonObject = new JSONObject();
        if (rctId == null) {
            RsRct rsRct = new RsRct();
            jsonObject.put("data", rsRct);
            return jsonObject;
        }

        RsRct rsRct = rsRctService.selectRsRctByRctId(rctId);

        jsonObject.put("data", rsRct);
        return jsonObject;
    }

    @GetMapping("/trucks")
    public JSONObject trucks(Long rctId, Long serviceTypeId) {
        JSONObject jsonObject = new JSONObject();
        if (rctId == null || serviceTypeId == null) {
            ArrayList<RsOpBulkTruck> rsOpBulkTrucks = new ArrayList<>();
            rsOpBulkTrucks.add(new RsOpBulkTruck());
            jsonObject.put("data", rsOpBulkTrucks);
            jsonObject.put("total", rsOpBulkTrucks.size());
            jsonObject.put("count", 10);
            return jsonObject;
        }
        List<RsOpBulkTruck> rsOpBulkTruckList = rsOpTruckMapper.selectRsOpBulkTruckByRctId(rctId, serviceTypeId);
        jsonObject.put("data", rsOpBulkTruckList);
        jsonObject.put("total", rsOpBulkTruckList.size());
        jsonObject.put("count", 10);
        return jsonObject;
    }

}
