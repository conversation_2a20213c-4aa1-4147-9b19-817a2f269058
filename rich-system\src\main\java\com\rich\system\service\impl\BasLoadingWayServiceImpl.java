package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.BasLoadingWay;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.BasLoadingWayMapper;
import com.rich.system.service.BasLoadingWayService;

/**
 * 装柜方式Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-25
 */
@Service
public class BasLoadingWayServiceImpl implements BasLoadingWayService {
    @Autowired
    private BasLoadingWayMapper basLoadingWayMapper;

    /**
     * 查询装柜方式
     *
     * @param loadingWayCode 装柜方式主键
     * @return 装柜方式
     */
    @Override
    public BasLoadingWay selectBasLoadingWayByLoadingWayCode(String loadingWayCode) {
        return basLoadingWayMapper.selectBasLoadingWayByLoadingWayCode(loadingWayCode);
    }

    /**
     * 查询装柜方式列表
     *
     * @param basLoadingWay 装柜方式
     * @return 装柜方式
     */
    @Override
    public List<BasLoadingWay> selectBasLoadingWayList(BasLoadingWay basLoadingWay) {
        return basLoadingWayMapper.selectBasLoadingWayList(basLoadingWay);
    }

    /**
     * 新增装柜方式
     *
     * @param basLoadingWay 装柜方式
     * @return 结果
     */
    @Override
    public int insertBasLoadingWay(BasLoadingWay basLoadingWay) {
        return basLoadingWayMapper.insertBasLoadingWay(basLoadingWay);
    }

    /**
     * 修改装柜方式
     *
     * @param basLoadingWay 装柜方式
     * @return 结果
     */
    @Override
    public int updateBasLoadingWay(BasLoadingWay basLoadingWay) {
        return basLoadingWayMapper.updateBasLoadingWay(basLoadingWay);
    }

    /**
     * 修改装柜方式状态
     *
     * @param basLoadingWay 装柜方式
     * @return 装柜方式
     */
    @Override
    public int changeStatus(BasLoadingWay basLoadingWay) {
        return basLoadingWayMapper.updateBasLoadingWay(basLoadingWay);
    }

    /**
     * 批量删除装柜方式
     *
     * @param loadingWayCodes 需要删除的装柜方式主键
     * @return 结果
     */
    @Override
    public int deleteBasLoadingWayByLoadingWayCodes(String[] loadingWayCodes) {
        return basLoadingWayMapper.deleteBasLoadingWayByLoadingWayCodes(loadingWayCodes);
    }

    /**
     * 删除装柜方式信息
     *
     * @param loadingWayCode 装柜方式主键
     * @return 结果
     */
    @Override
    public int deleteBasLoadingWayByLoadingWayCode(String loadingWayCode) {
        return basLoadingWayMapper.deleteBasLoadingWayByLoadingWayCode(loadingWayCode);
    }
}
