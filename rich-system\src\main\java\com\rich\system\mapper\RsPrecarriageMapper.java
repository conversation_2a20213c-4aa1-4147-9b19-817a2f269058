package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsPrecarriage;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 前程运输，操作单中的记录包含的前程运输信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@Mapper
public interface RsPrecarriageMapper {
    /**
     * 查询前程运输，操作单中的记录包含的前程运输信息
     *
     * @param precarriageId 前程运输，操作单中的记录包含的前程运输信息主键
     * @return 前程运输，操作单中的记录包含的前程运输信息
     */
    RsPrecarriage selectRsPrecarriageByPrecarriageId(Long precarriageId);

    /**
     * 查询前程运输，操作单中的记录包含的前程运输信息列表
     *
     * @param rsPrecarriage 前程运输，操作单中的记录包含的前程运输信息
     * @return 前程运输，操作单中的记录包含的前程运输信息集合
     */
    List<RsPrecarriage> selectRsPrecarriageList(RsPrecarriage rsPrecarriage);

    /**
     * 新增前程运输，操作单中的记录包含的前程运输信息
     *
     * @param rsPrecarriage 前程运输，操作单中的记录包含的前程运输信息
     * @return 结果
     */
    int insertRsPrecarriage(RsPrecarriage rsPrecarriage);

    /**
     * 修改前程运输，操作单中的记录包含的前程运输信息
     *
     * @param rsPrecarriage 前程运输，操作单中的记录包含的前程运输信息
     * @return 结果
     */
    int updateRsPrecarriage(RsPrecarriage rsPrecarriage);

    /**
     * 删除前程运输，操作单中的记录包含的前程运输信息
     *
     * @param precarriageId 前程运输，操作单中的记录包含的前程运输信息主键
     * @return 结果
     */
    int deleteRsPrecarriageByPrecarriageId(Long precarriageId);

    /**
     * 批量删除前程运输，操作单中的记录包含的前程运输信息
     *
     * @param precarriageIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsPrecarriageByPrecarriageIds(Long[] precarriageIds);

    RsPrecarriage selectRsPrecarriage(Long serviceInstanceId);

    RsPrecarriage selectRsPrecarriageByServiceInstance(Long rctId);
}
