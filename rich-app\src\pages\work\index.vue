<template>
  <view class="work-container">
    <uni-section title="Clients Managerment" type="line"></uni-section>
    <view v-if="checkRole(['client','warehouse'])">
      <view class="grid-body">
        <uni-grid :column="3" :showBorder="false">
          <uni-grid-item>
            <view class="grid-item-box" @click="handleToAddConsignee">
              <image mode="widthFix" src="/static/icons/person-add.svg" style="width: 30px; height: 30px;"></image>
              <text class="text">新客直录</text>
              <text class="text">New Client</text>
            </view>
          </uni-grid-item>

          <uni-grid-item>
            <view class="grid-item-box" @click="handleToScan">
              <uni-icons size="30" type="scan"></uni-icons>
              <text class="text">客户扫码</text>
              <text class="text">Scan Client</text>
            </view>
          </uni-grid-item>

          <uni-grid-item>
            <view class="grid-item-box" @click="handleToConsignee">
              <uni-icons size="30" type="person-filled"></uni-icons>
              <text class="text">客户列表</text>
              <text class="text">Clients List</text>
            </view>
          </uni-grid-item>
        </uni-grid>
      </view>
    </view>
    <uni-section title="Cargo Entry Information" type="line"></uni-section>
      <view class="grid-body">
        <uni-grid :column="3" :showBorder="false">
          <uni-grid-item>
            <view class="grid-item-box" @click="handleAddInventory">
              <image mode="widthFix" src="/static/icons/add.svg" style="width: 30px; height: 30px;"></image>
              <text class="text">货物直录</text>
              <text class="text">New Cargo</text>
            </view>
          </uni-grid-item>

          <uni-grid-item>
            <view class="grid-item-box" @click="handleToScan">
              <uni-icons size="30" type="scan"></uni-icons>
              <text class="text">货物扫码</text>
              <text class="text">Scan Cargo</text>
            </view>
          </uni-grid-item>

          <uni-grid-item>
            <view class="grid-item-box" @click="handleToDeliveryList">
              <uni-icons size="30" type="search"></uni-icons>
              <text class="text">货物查询</text>
              <text class="text">Track Cargo</text>
            </view>
          </uni-grid-item>
        </uni-grid>
      </view>

    <!-- 客户服务 -->
    <view v-if="checkRole(['client','warehouse'])">
      <uni-section title="Cargo Managerment Short Cut" type="line"></uni-section>
      <view class="grid-body">
        <uni-grid :column="3" :showBorder="false">
          <!-- <uni-grid-item>
            <view class="grid-item-box" @click="handleToConsignee">
              <uni-icons size="30" type="person-filled"></uni-icons>
              <text class="text">Consignee Management</text>
            </view>
          </uni-grid-item> -->

          <!-- 在途 -->
          <uni-grid-item>
            <view class="grid-item-box" @click="handleToInTransit">
              <image mode="widthFix" src="/static/icons/delivery.svg" style="width: 30px; height: 30px;"></image>
              <text class="text">在途</text>
              <!-- <text class="text">Floating Cargo({{ preEntryCount }})</text> -->
              <text class="text">Floating Cargo</text>
              <view class="grid-dot">
                <uni-badge :max-num="9999" :text="preEntryCount" :type="'error' "/>
              </view>
            </view>
          </uni-grid-item>

          <uni-grid-item>
            <view class="grid-item-box" @click="handleToClaim">
              <image mode="widthFix" src="/static/icons/person-q.svg" style="width: 30px; height: 30px;"></image>
              <text class="text">未知归属</text>
              <text class="text">Unknown</text>
              <view class="grid-dot">
                <uni-badge :max-num="9999" :text="unknownCount" :type="'error' "/>
              </view>
            </view>
          </uni-grid-item>


          <!-- 未完成 -->
          <uni-grid-item>
            <view class="grid-item-box" @click="handleToUncompleted">
              <image mode="widthFix" src="/static/icons/unconfirmed.svg" style="width: 30px; height: 30px;"></image>
              <text class="text">信息未完善</text>
              <!-- <text class="text">Uncompleted({{ uncompletedCount }})</text> -->
              <text class="text">Uncompleted</text>
              <view class="grid-dot">
                <uni-badge :max-num="9999" :text="uncompletedCount" :type="'error' "/>
              </view>
            </view>
          </uni-grid-item>
          <!-- 未确认 -->
          <uni-grid-item>
            <view class="grid-item-box" @click="handleToUnconfirmed">
              <image mode="widthFix" src="/static/icons/uncompleted.svg" style="width: 30px; height: 30px;"></image>
              <text class="text">仓管未确认</text>
              <!-- <text class="text">Unconfirmed({{ unconfirmedCount }})</text> -->
              <text class="text">Unconfirmed</text>
              <view class="grid-dot">
                <uni-badge :max-num="9999" :text="unconfirmedCount" :type=" 'error' "/>
              </view>
            </view>
          </uni-grid-item>
          <!-- 已确认 -->
          <uni-grid-item>
            <view class="grid-item-box" @click="handleToConfirmed">
              <image mode="widthFix" src="/static/icons/confirmed.svg" style="width: 30px; height: 30px;"></image>
              <text class="text">已入仓</text>
              <!-- <text class="text">Printed In({{ confirmedCount }})</text> -->
              <text class="text">Printed In</text>
              <view class="grid-dot">
                <uni-badge :max-num="9999" :text="confirmedCount" :type="'primary'"/>
              </view>
            </view>
          </uni-grid-item>
        </uni-grid>
      </view>
    </view>

    <!-- 仓管服务 -->
    <view v-if="checkRole(['warehouse'])">
      <uni-section title="系统管理" type="line"></uni-section>
      <view class="grid-body">
        <uni-grid :column="3" :showBorder="false">
          <!-- <uni-grid-item>
            <view class="grid-item-box" @click="handleToInventory">
              <uni-icons size="30" type="email"></uni-icons>
              <text class="text">送货单填写</text>
            </view>
          </uni-grid-item> -->

          <!-- <uni-grid-item>
            <view class="grid-item-box" @click="handleToScan">
              <uni-icons size="30" type="scan"></uni-icons>
              <text class="text">扫一扫</text>
            </view>
          </uni-grid-item>

          <uni-grid-item>
            <view class="grid-item-box" @click="handleToDeliveryList">
              <uni-icons size="30" type="search"></uni-icons>
              <text class="text">货物查询</text>
            </view>
          </uni-grid-item> -->

          <uni-grid-item>
            <view class="grid-item-box" @click="handleToPrint">
              <uni-icons size="30" type="loop"></uni-icons>
              <text class="text">连接打印机</text>
            </view>
          </uni-grid-item>

          <!-- <uni-grid-item>
            <view class="grid-item-box" @click="handleToDeliveryList">
              <uni-icons size="30" type="list"></uni-icons>
              <text class="text">Goods List</text>
            </view>
          </uni-grid-item> -->
        </uni-grid>
      </view>
    </view>

  </view>
</template>

<script>
import uniSection from '@/componetsPackage/uni-section/uni-section.vue'
import uniGrid from '@/componetsPackage/uni-grid/uni-grid.vue'
import uniGridItem from '@/componetsPackage/uni-grid-item/uni-grid-item.vue'
import uniIcons from '@/componetsPackage/uni-icons/uni-icons.vue'
import uniBadge from '@/componetsPackage/uni-badge/uni-badge.vue'
import {checkRole} from '@/utils/permission'
import {decrypt} from "../../utils/common";
import {getInventoryByExpress, getStatusNumber, claimExpress} from '@/api/system/invenory'


export default {
  components: {
    uniSection,
    uniGrid,
    uniGridItem,
    uniIcons,
    uniBadge
  },
  data() {
    return {
      current: 0,
      swiperDotIndex: 0,
      expressNo: null,
      data: [{
        image: '/static/images/banner/banner01.jpg'
      },
        {
          image: '/static/images/banner/banner02.jpg'
        },
        {
          image: '/static/images/banner/banner03.jpg'
        }
      ],
      inTransitCount: 0,
      preEntryCount: 0,
      uncompletedCount: 0,
      unconfirmedCount: 0,
      confirmedCount: 0,
      unknownCount: 0
    }
  },
  onLoad() {
    // this.loadStatusNumber()
  },
  onShow() {
    this.loadStatusNumber()
  },
  methods: {
    handleToAddConsignee() {
      uni.navigateTo({
        url: '/packageA/consignee/add'
      })
    },
    handleAddInventory() {
      uni.navigateTo({
        url: '/packageA/inventory/index?add=true'
      });
    },
    inputDialogToggle() {
      this.$refs.inputDialog.open()
    },
    dialogInputConfirm(val) {
      uni.showLoading({
        title: '3秒后会关闭'
      })

      setTimeout(() => {
        uni.hideLoading()
        this.value = val
        // 关闭窗口后，恢复默认内容
        this.$refs.inputDialog.close()
      }, 3000)
    },
    handleToClaim() {
      this.$tab.navigateTo('/packageA/inventoryList/index?claim=true')
    },
    handleToPrint() {
      this.$tab.navigateTo('/print/index/index')
    },
    handleToDeliveryList() {
      this.$tab.navigateTo('/packageA/inventoryList/index')
    },
    checkRole,
    handleToConsignee() {
      this.$tab.navigateTo('/packageA/consignee/index')
    },
    handleToInventory() {
      this.$tab.navigateTo('/packageA/inventory/index')
    },
    handleToScan() {
      // this.$tab.navigateTo('/pages/scan/index')
      // 允许从相机和相册扫码
      const that = this;
      uni.scanCode({
        success: function (res) {
          if (res.scanType === 'QR_CODE' && res.result) {
            // 处理URL类型的二维码
            if (res.result.includes('cnshipper.com/scan/inventory/index')) {
              // 使用正则表达式提取consigneeId参数
              const regExp = /[?&]consigneeId=([^&]*)/;
              const match = regExp.exec(res.result);
              if (match && match[1]) {
                const consigneeIdEncoded = match[1];
                // 解密consigneeId
                const consigneeId = decrypt(consigneeIdEncoded);
                uni.navigateTo({
                  url: '/packageA/inventory/index?consigneeId=' + consigneeId
                });
                return;
              }
            }

            // 原有的解密逻辑保留
            const arr = decrypt(res.result).split('-');
            // 扫码填写该收货人送货单
            if (arr[0] === 'consignee') {
              uni.navigateTo({
                url: '/packageA/inventory/index?consigneeId=' + arr[1]
              })
            }
            if (arr[0] === 'inventory') {
              uni.navigateTo({
                url: '/packageA/inventory/index?inventoryId=' + arr[1]
              })
            }
          }
          // 如果是条形码,说明是快递单号
          if (res.scanType === 'CODE_128' && res.result) {
            const no = res.result;

            // 检查用户权限
            if (checkRole(['warehouse'])) {
              // 仓库人员的处理逻辑（原有逻辑）
              that.handleWarehouseExpressScan(no);
            } else if (checkRole(['client'])) {
              // 客户的处理逻辑
              that.handleClientExpressScan(no);
            } else {
              uni.showToast({
                title: '您没有权限查看此快递',
                icon: 'none'
              });
            }
          }
        }
      });
    },
    // 仓库人员扫码处理逻辑
    handleWarehouseExpressScan(no) {
      const that = this;
      // 根据快递单号查询库存
      getInventoryByExpress(no).then(res => {
        if (res.data) {
          uni.navigateTo({
            url: '/packageA/inventory/edit?inventoryId=' + res.data.inventoryId
          })
        } else {
          // 保存快递单号
          that.expressNo = no;
          // 使用uni.showModal替代rich-dialog
          uni.showModal({
            title: '新建送货单',
            content: '快递: ' + no + ' 不存在是否新建',
            cancelText: '取消',
            confirmText: '确定',
            success: function (res) {
              if (res.confirm) {
                that.handleConfirmExpress();
              }
            }
          });
        }
      });
    },

    // 客户扫码处理逻辑
    handleClientExpressScan(no) {
      const that = this;
      // 根据快递单号查询库存信息
      getInventoryByExpress(no).then(res => {
        if (!res.data) {
          // 情况1：不存在
          that.showExpressNotExistDialog(no);
        } else {
          const inventory = res.data;
          const currentUserId = that.$store.state.user.id;

          if (!inventory.clientCode || inventory.clientCode === 'unknown') {
            // 情况2：存在但属于未知归属
            that.showUnclaimedExpressDialog(no, inventory);
          } else if (inventory.clientCode !== currentUserId) {
            // 情况3：存在但不属于自己
            that.showNotYourExpressDialog(no);
          } else {
            // 属于自己的快递
            if (inventory.inventoryStatus === 'outbound') {
              // 情况5：已出仓
              that.showExpressOutboundDialog(no);
            } else if (inventory.inventoryStatus === 'inbound' && !that.isInfoComplete(inventory)) {
              // 情况4：信息未完善，可编辑
              uni.navigateTo({
                url: '/packageA/inventory/edit?inventoryId=' + inventory.inventoryId + '&mode=edit'
              });
            } else {
              // 情况4：已入仓且信息完善，仅查看
              uni.navigateTo({
                url: '/packageA/inventory/detail?inventoryId=' + inventory.inventoryId + '&mode=view'
              });
            }
          }
        }
      }).catch(err => {
        console.error('查询快递信息失败:', err);
        uni.showToast({
          title: '查询失败，请重试',
          icon: 'none'
        });
      });
    },

    handleConfirmExpress() {
      // 处理录入的快递信息
      if (this.expressNo && checkRole(['warehouse'])) {
        uni.navigateTo({
          url: '/packageA/inventory/edit?expressNo=' + this.expressNo
        });
      } else {
        uni.showToast({
          title: '您没有权限',
          icon: 'none'
        });
      }
    },
    changeGrid(e) {
      let {index} = e.detail
      if (index === 0) {
        this.$tab.navigateTo('/packageA/consignee/index')
      }
    },
    loadStatusNumber() {
      const clientCode = this.$store.state.user.mpWarehouseClient?.clientCode
      if (clientCode) {
        getStatusNumber({clientCode: clientCode}).then(res => {
          this.preEntryCount = res.data.preEntryCount
          this.inTransitCount = res.data.inTransitCount
          this.uncompletedCount = res.data.uncompletedCount
          this.unconfirmedCount = res.data.unconfirmedCount
          this.confirmedCount = res.data.confirmedCount
          this.unknownCount = res.data.unknownCount
        })
      } else {
        getStatusNumber({clientCode: null}).then(res => {
          this.preEntryCount = res.data.preEntryCount
          this.inTransitCount = res.data.inTransitCount
          this.uncompletedCount = res.data.uncompletedCount
          this.unconfirmedCount = res.data.unconfirmedCount
          this.confirmedCount = res.data.confirmedCount
          this.unknownCount = res.data.unknownCount
        })
      }
    },
    handleToInTransit() {
      this.$tab.navigateTo('/packageA/inventoryList/index?inTransit=true')
    },
    handleToUncompleted() {
      this.$tab.navigateTo('/packageA/inventoryList/index?uncompleted=true')
    },
    handleToUnconfirmed() {
      this.$tab.navigateTo('/packageA/inventoryList/index?unconfirmed=true')
    },
    handleToConfirmed() {
      this.$tab.navigateTo('/packageA/inventoryList/index?confirmed=true')
    },

    // 情况1：快递不存在的对话框
    showExpressNotExistDialog(no) {
      uni.showModal({
        title: '快递不存在',
        content: `此快递 ${no} 不存在，是否添加？`,
        cancelText: '取消',
        confirmText: '添加',
        success: (res) => {
          if (res.confirm) {
            // 跳转到添加快递页面
            uni.navigateTo({
              url: `/packageA/inventory/add?expressNo=${no}`
            });
          }
        }
      });
    },

    // 情况2：快递无人认领的对话框
    showUnclaimedExpressDialog(no, inventory) {
      uni.showModal({
        title: '快递无人认领',
        content: `此快递 ${no} 已到仓库但无人认领，是否认领？`,
        cancelText: '取消',
        confirmText: '认领',
        success: (res) => {
          if (res.confirm) {
            // 调用认领接口
            this.claimExpress(inventory.inventoryId, no);
          }
        }
      });
    },

    // 情况3：快递属于其他人的提示
    showNotYourExpressDialog(no) {
      uni.showModal({
        title: '快递归属错误',
        content: `此快递 ${no} 属于其他人，请您重新核对单号。`,
        showCancel: false,
        confirmText: '确定'
      });
    },

    // 情况5：快递已出仓的提示
    showExpressOutboundDialog(no) {
      uni.showModal({
        title: '货物已出仓',
        content: `快递 ${no} 货物已出仓，请联系瑞旗公司获取详情。`,
        showCancel: false,
        confirmText: '确定',
        success: (res) => {
          if (res.confirm) {
            // 可以添加联系客服的逻辑
            this.contactCustomerService();
          }
        }
      });
    },

    // 认领快递
    claimExpress(inventoryId, expressNo) {
      const currentUserId = this.$store.state.user.id;

      // 调用后端接口认领快递
      claimExpress(inventoryId, {
        clientCode: currentUserId
      }).then(res => {
        if (res.code === 200) {
          uni.showToast({
            title: '认领成功',
            icon: 'success'
          });

          // 跳转到编辑页面
          setTimeout(() => {
            uni.navigateTo({
              url: `/packageA/inventory/edit?inventoryId=${inventoryId}&mode=edit`
            });
          }, 1500);
        } else {
          uni.showToast({
            title: res.msg || '认领失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        console.error('认领快递失败:', err);
        uni.showToast({
          title: '认领失败，请重试',
          icon: 'none'
        });
      });
    },

    // 检查快递信息是否完善
    isInfoComplete(inventory) {
      // 检查必要字段是否完整
      return !!(
        inventory.clientName &&
        inventory.cargoName &&
        inventory.totalBoxes &&
        inventory.totalGrossWeight
      );
    },

    // 联系客服
    contactCustomerService() {
      uni.showActionSheet({
        itemList: ['拨打客服电话', '在线客服'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 拨打电话
            uni.makePhoneCall({
              phoneNumber: '************' // 替换为实际客服电话
            });
          } else if (res.tapIndex === 1) {
            // 跳转到在线客服页面
            uni.navigateTo({
              url: '/packageA/customer-service/index'
            });
          }
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
/* #ifndef APP-NVUE */
page {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background-color: #fff;
  min-height: 100%;
  height: auto;
}

view {
  font-size: 14px;
  line-height: inherit;
}

/* #endif */

.text {
  text-align: center;
  font-size: 26rpx;
  margin-top: 10rpx;
}

.grid-item-box {
  flex: 1;
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px 0;
}

.dialog-content {
  padding: 30rpx;
}

.input-field {
  width: 100%;
  height: 80rpx;
  border: 1px solid #eee;
  border-radius: 8rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx 30rpx;
}

.btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 8rpx;
  margin: 0 10rpx;
}

.btn-cancel {
  background-color: #f5f5f5;
  color: #666;
}

.btn-confirm {
  background-color: #007aff;
  color: #fff;
}

.grid-dot {
  position: absolute;
  top: 0;
  right: 0;
}
</style>
