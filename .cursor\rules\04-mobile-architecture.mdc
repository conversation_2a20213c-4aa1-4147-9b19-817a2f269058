---
description:
globs:
alwaysApply: false
---
# 移动端架构与开发规范 (rich-app)

移动端基于 uni-app + uni-ui 构建，支持微信小程序、H5 和 App 多端部署。

## 项目结构

```
rich-app/
├── src/                 # 源代码主目录
│   ├── api/             # API接口请求模块
│   ├── components/      # 公共组件
│   ├── pages/           # 页面文件
│   ├── static/          # 静态资源文件
│   ├── store/           # Vuex状态管理
│   ├── utils/           # 工具函数库
│   ├── App.vue          # 应用入口组件
│   ├── main.js          # 入口文件
│   ├── manifest.json    # 应用配置文件
│   ├── pages.json       # 页面配置
│   ├── permission.js    # 权限控制
│   └── uni.scss         # 全局样式变量
└── package.json         # 项目依赖配置
```

## 主要开发规范

### 页面开发规范
- 页面文件使用单文件组件（.vue）格式
- 页面路径在 pages.json 中配置
- 页面样式使用 rpx 单位实现响应式布局
- 页面组件使用 Options API 风格组织代码

### 组件开发规范
- 组件文件名：采用 kebab-case (短横线)命名法
- 组件属性使用小驼峰 (camelCase)
- 组件注册使用 easycom 规范
- 尽量使用 uni-ui 组件库提供的组件

### API 调用规范
- 统一使用封装的 request 模块发起请求
- API 接口按功能模块组织在 api 目录下
- 统一处理请求/响应拦截
- Token 处理与错误提示统一管理

### 路由与页面跳转规范
- 使用 uni.navigateTo 等原生方法进行页面跳转
- 使用 uni.$emit / uni.$on 进行页面间通信
- 避免多层嵌套跳转导致堆栈溢出

### 状态管理规范
- Vuex 按功能模块拆分
- 持久化数据使用 uni.storage API
- 全局共享状态统一通过 Vuex 管理
- 页面级状态在组件内部管理

### 样式规范
- 使用 scss 预处理器
- 全局样式变量在 uni.scss 中定义
- 颜色、字体、间距等使用变量统一管理
- 布局优先使用 flex

### 多端适配规范
- 使用条件编译处理平台差异 `<!-- #ifdef H5 -->`
- 优先使用跨平台组件
- 特殊平台功能使用条件编译隔离
- 避免使用平台私有API

### 性能优化规范
- 首屏加载优化
- 图片懒加载与压缩
- 分页加载大量数据
- 避免不必要的重渲染
- 定时器及时清除

### uni-ui 使用规范
- 表单组件统一使用 uni-forms 组件
- 弹窗提示使用 uni-popup 组件
- 列表使用 uni-list 组件
- 下拉刷新使用内置 refresh 组件
- 上拉加载使用内置 loadMore 组件

### 数据缓存规范
- 临时数据使用 Vuex 管理
- 持久化数据使用 uni.setStorage 存储
- 敏感数据加密后存储
- 大量数据分块存储，避免超出存储限制
