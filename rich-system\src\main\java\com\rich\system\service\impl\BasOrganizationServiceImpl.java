package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.BasOrganization;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.system.mapper.BasOrganizationMapper;
import com.rich.system.service.BasOrganizationService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 所属组织Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-09-30
 */
@Service

public class BasOrganizationServiceImpl implements BasOrganizationService {

    @Autowired
    private  BasOrganizationMapper basOrganizationMapper;

    /**
     * 查询所属组织
     *
     * @param organizationId 所属组织主键
     * @return 所属组织
     */
    @Override
    public BasOrganization selectBasOrganizationByOrganizationId(Long organizationId) {
        return basOrganizationMapper.selectBasOrganizationByOrganizationId(organizationId);
    }

    /**
     * 查询所属组织列表
     *
     * @param basOrganization 所属组织
     * @return 所属组织
     */
    @Override
    public List<BasOrganization> selectBasOrganizationList(BasOrganization basOrganization) {
        return basOrganizationMapper.selectBasOrganizationList(basOrganization);
    }

    /**
     * 新增所属组织
     *
     * @param basOrganization 所属组织
     * @return 结果
     */
    @Override
    public int insertBasOrganization(BasOrganization basOrganization) {
        basOrganization.setCreateTime(DateUtils.getNowDate());
        basOrganization.setCreateBy(SecurityUtils.getUserId());
        return basOrganizationMapper.insertBasOrganization(basOrganization);
    }

    /**
     * 修改所属组织
     *
     * @param basOrganization 所属组织
     * @return 结果
     */
    @Override
    public int updateBasOrganization(BasOrganization basOrganization) {
        basOrganization.setUpdateTime(DateUtils.getNowDate());
        basOrganization.setUpdateBy(SecurityUtils.getUserId());
        return basOrganizationMapper.updateBasOrganization(basOrganization);
    }

    /**
     * 批量删除所属组织
     *
     * @param organizationIds 需要删除的所属组织主键
     * @return 结果
     */
    @Override
    public int deleteBasOrganizationByOrganizationIds(Long[] organizationIds) {
        return basOrganizationMapper.deleteBasOrganizationByOrganizationIds(organizationIds);
    }

    /**
     * 删除所属组织信息
     *
     * @param organizationId 所属组织主键
     * @return 结果
     */
    @Override
    public int deleteBasOrganizationByOrganizationId(Long organizationId) {
        return basOrganizationMapper.deleteBasOrganizationByOrganizationId(organizationId);
    }

    @Override
    public int changeStatus(BasOrganization basOrganization) {
        return basOrganizationMapper.updateBasOrganization(basOrganization);
    }
}
