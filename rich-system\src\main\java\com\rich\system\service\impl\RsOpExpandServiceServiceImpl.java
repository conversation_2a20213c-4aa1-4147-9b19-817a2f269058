package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpExpandService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsOpExpandServiceMapper;
import com.rich.system.service.RsOpExpandServiceService;

/**
 * 扩展服务服务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Service
public class RsOpExpandServiceServiceImpl implements RsOpExpandServiceService {
    @Autowired
    private RsOpExpandServiceMapper rsOpExpandServiceMapper;

    /**
     * 查询扩展服务服务
     *
     * @param expandServiceId 扩展服务服务主键
     * @return 扩展服务服务
     */
    @Override
    public RsOpExpandService selectRsOpExpandServiceByExpandServiceId(Long expandServiceId) {
        return rsOpExpandServiceMapper.selectRsOpExpandServiceByExpandServiceId(expandServiceId);
    }

    /**
     * 查询扩展服务服务列表
     *
     * @param rsOpExpandService 扩展服务服务
     * @return 扩展服务服务
     */
    @Override
    public List<RsOpExpandService> selectRsOpExpandServiceList(RsOpExpandService rsOpExpandService) {
        return rsOpExpandServiceMapper.selectRsOpExpandServiceList(rsOpExpandService);
    }

    /**
     * 新增扩展服务服务
     *
     * @param rsOpExpandService 扩展服务服务
     * @return 结果
     */
    @Override
    public int insertRsOpExpandService(RsOpExpandService rsOpExpandService) {
        return rsOpExpandServiceMapper.insertRsOpExpandService(rsOpExpandService);
    }

    /**
     * 修改扩展服务服务
     *
     * @param rsOpExpandService 扩展服务服务
     * @return 结果
     */
    @Override
    public int updateRsOpExpandService(RsOpExpandService rsOpExpandService) {
        return rsOpExpandServiceMapper.updateRsOpExpandService(rsOpExpandService);
    }

    /**
     * 修改扩展服务服务状态
     *
     * @param rsOpExpandService 扩展服务服务
     * @return 扩展服务服务
     */
    @Override
    public int changeStatus(RsOpExpandService rsOpExpandService) {
        return rsOpExpandServiceMapper.updateRsOpExpandService(rsOpExpandService);
    }

    /**
     * 批量删除扩展服务服务
     *
     * @param expandServiceIds 需要删除的扩展服务服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpExpandServiceByExpandServiceIds(Long[] expandServiceIds) {
        return rsOpExpandServiceMapper.deleteRsOpExpandServiceByExpandServiceIds(expandServiceIds);
    }

    /**
     * 删除扩展服务服务信息
     *
     * @param expandServiceId 扩展服务服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpExpandServiceByExpandServiceId(Long expandServiceId) {
        return rsOpExpandServiceMapper.deleteRsOpExpandServiceByExpandServiceId(expandServiceId);
    }
}
