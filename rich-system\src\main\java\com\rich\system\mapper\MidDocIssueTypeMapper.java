package com.rich.system.mapper;

import com.rich.system.domain.MidDocIssueType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@Mapper
public interface MidDocIssueTypeMapper {
    /**
     * 查询【请填写功能名称】
     *
     * @param docIssueTypeId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    MidDocIssueType selectMidDocIssueTypeByDocIssueTypeId(Long docIssueTypeId);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param midDocIssueType 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    List<MidDocIssueType> selectMidDocIssueTypeList(MidDocIssueType midDocIssueType);

    /**
     * 新增【请填写功能名称】
     *
     * @param midDocIssueType 【请填写功能名称】
     * @return 结果
     */
    int insertMidDocIssueType(MidDocIssueType midDocIssueType);

    /**
     * 修改【请填写功能名称】
     *
     * @param midDocIssueType 【请填写功能名称】
     * @return 结果
     */
    int updateMidDocIssueType(MidDocIssueType midDocIssueType);

    /**
     * 删除【请填写功能名称】
     *
     * @param docIssueTypeId 【请填写功能名称】主键
     * @return 结果
     */
    int deleteMidDocIssueTypeByDocIssueTypeId(Long docIssueTypeId);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param docIssueTypeIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteMidDocIssueTypeByDocIssueTypeIds(Long[] docIssueTypeIds);

    int batchDocIssueType(List<MidDocIssueType> list);
}
