package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.BasDistUnit;
import com.rich.system.mapper.BasDistUnitMapper;
import com.rich.system.service.BasDistUnitService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 单位Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-08-30
 */
@Service

public class BasDistUnitServiceImpl implements BasDistUnitService {
    @Autowired
    private BasDistUnitMapper basDistUnitMapper;

    /**
     * 查询单位
     *
     * @param unitId 单位主键
     * @return 单位
     */
    @Override
    public BasDistUnit selectBasChargeUnitByUnitId(Long unitId) {
        return basDistUnitMapper.selectBasUnitByUnitId(unitId);
    }

    /**
     * 查询单位列表
     *
     * @param basDistUnit 单位
     * @return 单位
     */
    @Override
    public List<BasDistUnit> selectBasChargeUnitList(BasDistUnit basDistUnit) {
        return basDistUnitMapper.selectBasUnitList(basDistUnit);
    }

    /**
     * 新增单位
     *
     * @param basDistUnit 单位
     * @return 结果
     */
    @Override
    public int insertBasChargeUnit(BasDistUnit basDistUnit) {
        return basDistUnitMapper.insertBasUnit(basDistUnit);
    }

    /**
     * 修改单位
     *
     * @param basDistUnit 单位
     * @return 结果
     */
    @Override
    public int updateBasChargeUnit(BasDistUnit basDistUnit) {
        return basDistUnitMapper.updateBasUnit(basDistUnit);
    }

    /**
     * 批量删除单位
     *
     * @param unitIds 需要删除的单位主键
     * @return 结果
     */
    @Override
    public int deleteBasChargeUnitByUnitIds(Long[] unitIds) {
        return basDistUnitMapper.deleteBasUnitByUnitIds(unitIds);
    }

    /**
     * 删除单位信息
     *
     * @param unitId 单位主键
     * @return 结果
     */
    @Override
    public int deleteBasChargeUnitByUnitId(Long unitId) {
        return basDistUnitMapper.deleteBasUnitByUnitId(unitId);
    }

    @Override
    public int changeStatus(BasDistUnit basDistUnit) {
        return basDistUnitMapper.updateBasUnit(basDistUnit);
    }
}
