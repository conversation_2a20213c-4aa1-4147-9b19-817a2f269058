package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.BasSendingStatus;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.system.mapper.BasSendingStatusMapper;
import com.rich.system.service.BasSendingStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 发送状态Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@Service
public class BasSendingStatusServiceImpl implements BasSendingStatusService {
    @Autowired
    private BasSendingStatusMapper basSendingStatusMapper;

    /**
     * 查询发送状态
     *
     * @param sendingStatusId 发送状态主键
     * @return 发送状态
     */
    @Override
    public BasSendingStatus selectBasSendingStatusBySendingStatusId(Long sendingStatusId) {
        return basSendingStatusMapper.selectBasSendingStatusBySendingStatusId(sendingStatusId);
    }

    /**
     * 查询发送状态列表
     *
     * @param basSendingStatus 发送状态
     * @return 发送状态
     */
    @Override
    public List<BasSendingStatus> selectBasSendingStatusList(BasSendingStatus basSendingStatus) {
        return basSendingStatusMapper.selectBasSendingStatusList(basSendingStatus);
    }

    /**
     * 新增发送状态
     *
     * @param basSendingStatus 发送状态
     * @return 结果
     */
    @Override
    public int insertBasSendingStatus(BasSendingStatus basSendingStatus) {
        basSendingStatus.setCreateTime(DateUtils.getNowDate());
        basSendingStatus.setCreateBy(SecurityUtils.getUserId());
        return basSendingStatusMapper.insertBasSendingStatus(basSendingStatus);
    }

    /**
     * 修改发送状态
     *
     * @param basSendingStatus 发送状态
     * @return 结果
     */
    @Override
    public int updateBasSendingStatus(BasSendingStatus basSendingStatus) {
        basSendingStatus.setUpdateTime(DateUtils.getNowDate());
        basSendingStatus.setUpdateBy(SecurityUtils.getUserId());
        return basSendingStatusMapper.updateBasSendingStatus(basSendingStatus);
    }

    /**
     * 修改发送状态状态
     *
     * @param basSendingStatus 发送状态
     * @return 发送状态
     */
    @Override
    public int changeStatus(BasSendingStatus basSendingStatus) {
        return basSendingStatusMapper.updateBasSendingStatus(basSendingStatus);
    }

    /**
     * 批量删除发送状态
     *
     * @param sendingStatusIds 需要删除的发送状态主键
     * @return 结果
     */
    @Override
    public int deleteBasSendingStatusBySendingStatusIds(Long[] sendingStatusIds) {
        return basSendingStatusMapper.deleteBasSendingStatusBySendingStatusIds(sendingStatusIds);
    }

    /**
     * 删除发送状态信息
     *
     * @param sendingStatusId 发送状态主键
     * @return 结果
     */
    @Override
    public int deleteBasSendingStatusBySendingStatusId(Long sendingStatusId) {
        return basSendingStatusMapper.deleteBasSendingStatusBySendingStatusId(sendingStatusId);
    }
}
