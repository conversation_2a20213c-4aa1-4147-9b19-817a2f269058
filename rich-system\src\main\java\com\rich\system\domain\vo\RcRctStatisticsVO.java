package com.rich.system.domain.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/10/13 9:37
 * @Version 1.0
 */
public class RcRctStatisticsVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long opId;

    private Long lastMonth;

    private Long currentMonth;

    private Long lastDay;

    private Long currentDay;

    private String opName;

    public Long getOpId() {
        return opId;
    }

    public void setOpId(Long opId) {
        this.opId = opId;
    }

    public Long getLastMonth() {
        return lastMonth;
    }

    public void setLastMonth(Long lastMonth) {
        this.lastMonth = lastMonth;
    }

    public Long getCurrentMonth() {
        return currentMonth;
    }

    public void setCurrentMonth(Long currentMonth) {
        this.currentMonth = currentMonth;
    }

    public Long getLastDay() {
        return lastDay;
    }

    public void setLastDay(Long lastDay) {
        this.lastDay = lastDay;
    }

    public Long getCurrentDay() {
        return currentDay;
    }

    public void setCurrentDay(Long currentDay) {
        this.currentDay = currentDay;
    }

    public String getOpName() {
        return opName;
    }

    public void setOpName(String opName) {
        this.opName = opName;
    }
}

