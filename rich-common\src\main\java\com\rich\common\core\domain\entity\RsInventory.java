package com.rich.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.NumberFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 库存对象 rs_inventory
 *
 * <AUTHOR>
 * @date 2024-11-08
 */
public class RsInventory extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @Excel(name = "库存编号", cellType = Excel.ColumnType.NUMERIC, width = 20, needMerge = true)
    private Long inventoryId;

    /**
     * 库存标志：在库/已出库/被打包
     */
    @Excel(name = "库存标志", readConverterExp = "0=在库,1=已出库,-1=被打包", needMerge = true)
    private String inventoryStatus;
    private String inboundSerialNoPre;
    private String inboundSerialNoSub;



    /**
     * 入仓流水号
     */
    @Excel(name = "入仓流水号", needMerge = true)
    private String inboundSerialNo;

    /**
     * 入仓流水号拆分
     */
//    @Excel(name = "入仓流水号拆分", needMerge = true)
    private String inboundSerialSplit;
    /**
     * 货代单号
     */
    @Excel(name = "货代单号", needMerge = true)
    private String forwarderNo;
    // 兼容日期格式
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "入仓日期", width = 30, dateFormat = "yyyy-MM-dd", needMerge = true)
    private Date actualInboundTime;
    /**
     * 入仓日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "录入日期")
    private Date inboundDate;


    /**
     * 客户代码
     */
    @Excel(name = "客户代码", needMerge = true)
    private String clientCode;
    /**
     * 分单号
     */
    @Excel(name = "分单号", needMerge = true)
    private String subOrderNo;

    @Excel(name = "收货人名称")
    private String consigneeName;
    @Excel(name = "收货人电话")
    private String consigneeTel;
    /**
     * sqd_唛头
     */
    @Excel(name = "唛头", needMerge = true)
    private String sqdShippingMark;
    /**
     * 总货名
     */
    @Excel(name = "总货名", needMerge = true)
    private String cargoName;

    /**
     * 出仓单号
     */
//    @Excel(name = "出仓单号", needMerge = true)
    private String outboundNo;

    /**
     * 总箱数
     */
    @Excel(name = "总箱数", needMerge = true, scale = 2)
    private Long totalBoxes;

    /**
     * 包装类型（纸箱/木箱/托盘/吨袋等）
     */
    @Excel(name = "包装类型", needMerge = true)
    private String packageType;

    /**
     * 总毛重
     */
    @Excel(name = "总毛重", needMerge = true, scale = 2)
    private BigDecimal totalGrossWeight;

    /**
     * 总体积
     */
    @Excel(name = "总体积", needMerge = true, scale = 2)
    private BigDecimal totalVolume;

    /**
     * 供货商（工厂代码/简称）
     */
    @Excel(name = "供货商", needMerge = true)
    private String supplier;

    /**
     * 送货司机
     */
    @Excel(name = "送货司机", needMerge = true)
    private String deliveryDriver;

    /**
     * 司机信息
     */
    @Excel(name = "司机信息", needMerge = true)
    private String driverInfo;


    /**
     * 破损标志
     */
    @Excel(name = "破损标志", needMerge = true)
    private String damageStatus;

    /**
     * 已收入仓费
     */
    @Excel(name = "已收入仓费", needMerge = true, scale = 2)
    private BigDecimal receivedStorageFee;

    /**
     * 未收卸货费
     */
    @Excel(name = "未收卸货费", needMerge = true, scale = 2)
    private BigDecimal unpaidUnloadingFee;

    /**
     * 物流代垫费
     */
    @Excel(name = "物流代垫费", needMerge = true, scale = 2)
    private BigDecimal logisticsAdvanceFee;

    @Excel(name = "未收打包费", needMerge = true, scale = 2)
    private BigDecimal unpaidPackingFee;
    @Excel(name = "实付打包费", needMerge = true, scale = 2)
    private BigDecimal receivedPackingFee;

    /**
     * 租金平衡费
     */
    @Excel(name = "租金平衡费", needMerge = true, scale = 2)
    private BigDecimal rentalBalanceFee;

    /**
     * 免堆期
     */
    @Excel(name = "免堆期", needMerge = true)
    private Long freeStackPeriod;

    /**
     * 仓租结算至
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "仓租结算至", width = 30, dateFormat = "yyyy-MM-dd", needMerge = true)
    private Date rentalSettlementDate;

    /**
     * 出仓日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
//    @Excel(name = "出仓日期", width = 30, dateFormat = "yyyy-MM-dd", needMerge = true)
    private Date outboundDate;


    /**
     * 存放位置1
     */
    private String storageLocation1;

    /**
     * 存放位置2
     */
    private String storageLocation2;

    /**
     * 存放位置3
     */
    private String storageLocation3;






    /**
     * 超期租金单价
     */
    @Excel(name = "超期租金单价", needMerge = true, scale = 2)
    private BigDecimal overdueRentalUnitPrice;

    /**
     * 超期租金
     */
    @Excel(name = "超期租金", needMerge = true, scale = 2)
    private BigDecimal overdueRentalFee;

    /**
     * 入仓备注
     */
    @Excel(name = "入仓备注", needMerge = true)
    private String inboundNotes;

    /**
     * 出仓备注
     */
    private String outboundNotes;
    private String warehouseCode;
    private String recordType;
    private String inboundType;
    @Excel(name = "已收供应商", needMerge = true, scale = 2)
    private BigDecimal receivedSupplier;
    private String cargoNature;
    @Excel(name = "已入仓费", needMerge = true, scale = 2)
    private BigDecimal unpaidPackagingFee;
    @Excel(name = "入仓费标准", needMerge = true, scale = 2)
    private BigDecimal inboundFee;
    @Excel(name = "实付卸货费", scale = 2)
    private BigDecimal receivedUnloadingFee;
    @Excel(name = "被打包至")
    private String packageIntoNo;

    @Excel(name = "长")
    private Long length;
    @Excel(name = "宽")
    private Long width;
    @Excel(name = "高")
    private Long height;
    @Excel(name = "单箱体积")
    private Long singlePieceVolume;
    @Excel(name = "单箱重量")
    private Long singlePieceWeight;
    @Excel(name = "货物明细")
    private List<RsCargoDetails> rsCargoDetailsList;
    private List<RsCargoDetails> outboundCargoDetailsList;
    private Date createdAt;
    private String preOutboundFlag;
    private String outboundRequestFlag;
    private String sqdPlannedOutboundDate;
    private String confirmRequestFlag;
    private String sqdInboundHandler;
    private String partialOutboundFlag;
    private Long outboundRecordId;
    private String confirmInboundRequestFlag;
    private String confirmOutboundRequestFlag;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualOutboundTime;
    private int cargoDetailRows;
    private BigDecimal unloadingFee;


    private String immediatePaymentFee;
    private String includesUnloadingFee;
    private String notes;
    private String outboundMethod;
    private String outboundType;
    private int rentalDays;

    private BigDecimal unpaidInboundFee;

    private int includesInboundFee;
    private int includesPackingFee;
    private String contractType;
    private MidOutboundSettlement midOutboundSettlement;
    private String clientName;
    private String packageRecord;
    private String repackingStatus;
    private String repackedInto;
    private Long packageTo;
    private String packageToNo;

    private String purchaseNo;
    private Long createdBy;
    private Long preOutboundRecordId;

    public Long getPreOutboundRecordId() {
        return preOutboundRecordId;
    }

    public void setPreOutboundRecordId(Long preOutboundRecordId) {
        this.preOutboundRecordId = preOutboundRecordId;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public String getPurchaseNo() {
        return purchaseNo;
    }

    public void setPurchaseNo(String purchaseNo) {
        this.purchaseNo = purchaseNo;
    }

    public Long getSinglePieceWeight() {
        return singlePieceWeight;
    }

    public void setSinglePieceWeight(Long singlePieceWeight) {
        this.singlePieceWeight = singlePieceWeight;
    }

    public Long getSinglePieceVolume() {
        return singlePieceVolume;
    }

    public void setSinglePieceVolume(Long singlePieceVolume) {
        this.singlePieceVolume = singlePieceVolume;
    }

    public Long getHeight() {
        return height;
    }

    public void setHeight(Long height) {
        this.height = height;
    }

    public Long getWidth() {
        return width;
    }

    public void setWidth(Long width) {
        this.width = width;
    }

    public Long getLength() {
        return length;
    }

    public void setLength(Long length) {
        this.length = length;
    }

    public String getPackageIntoNo() {
        return packageIntoNo;
    }

    public void setPackageIntoNo(String packageIntoNo) {
        this.packageIntoNo = packageIntoNo;
    }

    public String getPackageToNo() {
        return packageToNo;
    }

    public void setPackageToNo(String packageToNo) {
        this.packageToNo = packageToNo;
    }

    public Long getPackageTo() {
        return packageTo;
    }

    public void setPackageTo(Long packageTo) {
        this.packageTo = packageTo;
    }

    public String getRepackedInto() {
        return repackedInto;
    }

    public void setRepackedInto(String repackedInto) {
        this.repackedInto = repackedInto;
    }

    public String getRepackingStatus() {
        return repackingStatus;
    }

    public void setRepackingStatus(String repackingStatus) {
        this.repackingStatus = repackingStatus;
    }

    public String getPackageRecord() {
        return packageRecord;
    }

    public void setPackageRecord(String packageRecord) {
        this.packageRecord = packageRecord;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }


    public String getConsigneeTel() {
        return consigneeTel;
    }

    public void setConsigneeTel(String consigneeTel) {
        this.consigneeTel = consigneeTel;
    }

    public String getConsigneeName() {
        return consigneeName;
    }

    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName;
    }

    public MidOutboundSettlement getMidOutboundSettlement() {
        return midOutboundSettlement;
    }

    public void setMidOutboundSettlement(MidOutboundSettlement midOutboundSettlement) {
        this.midOutboundSettlement = midOutboundSettlement;
    }

    public BigDecimal getReceivedSupplier() {
        return receivedSupplier;
    }

    public void setReceivedSupplier(BigDecimal receivedSupplier) {
        this.receivedSupplier = receivedSupplier;
    }

    public String getInboundSerialNoPre() {
        return inboundSerialNoPre;
    }

    public void setInboundSerialNoPre(String inboundSerialNoPre) {
        this.inboundSerialNoPre = inboundSerialNoPre;
    }

    public String getInboundSerialNoSub() {
        return inboundSerialNoSub;
    }

    public void setInboundSerialNoSub(String inboundSerialNoSub) {
        this.inboundSerialNoSub = inboundSerialNoSub;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public int getIncludesPackingFee() {
        return includesPackingFee;
    }

    public void setIncludesPackingFee(int includesPackingFee) {
        this.includesPackingFee = includesPackingFee;
    }

    public int getIncludesInboundFee() {
        return includesInboundFee;
    }

    public void setIncludesInboundFee(int includesInboundFee) {
        this.includesInboundFee = includesInboundFee;
    }

    public BigDecimal getReceivedPackingFee() {
        return receivedPackingFee;
    }

    public void setReceivedPackingFee(BigDecimal receivedPackingFee) {
        this.receivedPackingFee = receivedPackingFee;
    }

    public BigDecimal getReceivedUnloadingFee() {
        return receivedUnloadingFee;
    }

    public void setReceivedUnloadingFee(BigDecimal receivedUnloadingFee) {
        this.receivedUnloadingFee = receivedUnloadingFee;
    }

    public BigDecimal getUnpaidInboundFee() {
        return unpaidInboundFee;
    }

    public void setUnpaidInboundFee(BigDecimal unpaidInboundFee) {
        this.unpaidInboundFee = unpaidInboundFee;
    }

    public BigDecimal getUnpaidPackagingFee() {
        return unpaidPackagingFee;
    }

    public void setUnpaidPackagingFee(BigDecimal unpaidPackagingFee) {
        this.unpaidPackagingFee = unpaidPackagingFee;
    }

    public int getRentalDays() {
        return rentalDays;
    }

    public void setRentalDays(int rentalDays) {
        this.rentalDays = rentalDays;
    }

    public String getOutboundType() {
        return outboundType;
    }

    public void setOutboundType(String outboundType) {
        this.outboundType = outboundType;
    }

    public String getOutboundMethod() {
        return outboundMethod;
    }

    public void setOutboundMethod(String outboundMethod) {
        this.outboundMethod = outboundMethod;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getIncludesUnloadingFee() {
        return includesUnloadingFee;
    }

    public void setIncludesUnloadingFee(String includesUnloadingFee) {
        this.includesUnloadingFee = includesUnloadingFee;
    }

    public String getImmediatePaymentFee() {
        return immediatePaymentFee;
    }

    public void setImmediatePaymentFee(String immediatePaymentFee) {
        this.immediatePaymentFee = immediatePaymentFee;
    }

    public BigDecimal getInboundFee() {
        return inboundFee;
    }

    public void setInboundFee(BigDecimal inboundFee) {
        this.inboundFee = inboundFee;
    }

    public BigDecimal getUnpaidPackingFee() {
        return unpaidPackingFee;
    }

    public void setUnpaidPackingFee(BigDecimal unpaidPackingFee) {
        this.unpaidPackingFee = unpaidPackingFee;
    }

    public BigDecimal getUnloadingFee() {
        return unloadingFee;
    }

    public void setUnloadingFee(BigDecimal unloadingFee) {
        this.unloadingFee = unloadingFee;
    }

    public int getCargoDetailRows() {
        return cargoDetailRows;
    }

    public void setCargoDetailRows(int cargoDetailRows) {
        this.cargoDetailRows = cargoDetailRows;
    }

    public Date getActualOutboundTime() {
        return actualOutboundTime;
    }

    public void setActualOutboundTime(Date actualOutboundTime) {
        this.actualOutboundTime = actualOutboundTime;
    }

    public Date getActualInboundTime() {
        return actualInboundTime;
    }

    public void setActualInboundTime(Date actualInboundTime) {
        this.actualInboundTime = actualInboundTime;
    }

    public String getConfirmOutboundRequestFlag() {
        return confirmOutboundRequestFlag;
    }

    public void setConfirmOutboundRequestFlag(String confirmOutboundRequestFlag) {
        this.confirmOutboundRequestFlag = confirmOutboundRequestFlag;
    }

    public String getConfirmInboundRequestFlag() {
        return confirmInboundRequestFlag;
    }

    public void setConfirmInboundRequestFlag(String confirmInboundRequestFlag) {
        this.confirmInboundRequestFlag = confirmInboundRequestFlag;
    }

    public List<RsCargoDetails> getOutboundCargoDetailsList() {
        return outboundCargoDetailsList;
    }

    public void setOutboundCargoDetailsList(List<RsCargoDetails> outboundCargoDetailsList) {
        this.outboundCargoDetailsList = outboundCargoDetailsList;
    }

    public Long getOutboundRecordId() {
        return outboundRecordId;
    }

    public void setOutboundRecordId(Long outboundRecordId) {
        this.outboundRecordId = outboundRecordId;
    }

    public String getPartialOutboundFlag() {
        return partialOutboundFlag;
    }

    public void setPartialOutboundFlag(String partialOutboundFlag) {
        this.partialOutboundFlag = partialOutboundFlag;
    }

    public String getSqdInboundHandler() {
        return sqdInboundHandler;
    }

    public void setSqdInboundHandler(String sqdInboundHandler) {
        this.sqdInboundHandler = sqdInboundHandler;
    }

    public String getConfirmRequestFlag() {
        return confirmRequestFlag;
    }

    public void setConfirmRequestFlag(String confirmRequestFlag) {
        this.confirmRequestFlag = confirmRequestFlag;
    }

    public String getSqdPlannedOutboundDate() {
        return sqdPlannedOutboundDate;
    }

    public void setSqdPlannedOutboundDate(String sqdPlannedOutboundDate) {
        this.sqdPlannedOutboundDate = sqdPlannedOutboundDate;
    }

    public String getOutboundRequestFlag() {
        return outboundRequestFlag;
    }

    public void setOutboundRequestFlag(String outboundRequestFlag) {
        this.outboundRequestFlag = outboundRequestFlag;
    }

    public String getPreOutboundFlag() {
        return preOutboundFlag;
    }

    public void setPreOutboundFlag(String preOutboundFlag) {
        this.preOutboundFlag = preOutboundFlag;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public List<RsCargoDetails> getRsCargoDetailsList() {
        return rsCargoDetailsList;
    }

    public void setRsCargoDetailsList(List<RsCargoDetails> rsCargoDetailsList) {
        this.rsCargoDetailsList = rsCargoDetailsList;
    }

    public String getCargoNature() {
        return cargoNature;
    }

    public void setCargoNature(String cargoNature) {
        this.cargoNature = cargoNature;
    }

    public String getInboundType() {
        return inboundType;
    }

    public void setInboundType(String inboundType) {
        this.inboundType = inboundType;
    }

    public String getRecordType() {
        return recordType;
    }

    public void setRecordType(String recordType) {
        this.recordType = recordType;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public Long getInventoryId() {
        return inventoryId;
    }

    public void setInventoryId(Long inventoryId) {
        this.inventoryId = inventoryId;
    }

    public String getInventoryStatus() {
        return inventoryStatus;
    }

    public void setInventoryStatus(String inventoryStatus) {
        this.inventoryStatus = inventoryStatus;
    }

    public String getInboundSerialNo() {
        return inboundSerialNo;
    }

    public void setInboundSerialNo(String inboundSerialNo) {
        this.inboundSerialNo = inboundSerialNo;
    }

    public String getInboundSerialSplit() {
        return inboundSerialSplit;
    }

    public void setInboundSerialSplit(String inboundSerialSplit) {
        this.inboundSerialSplit = inboundSerialSplit;
    }

    public Date getInboundDate() {
        return inboundDate;
    }

    public void setInboundDate(Date inboundDate) {
        this.inboundDate = inboundDate;
    }

    public String getOutboundNo() {
        return outboundNo;
    }

    public void setOutboundNo(String outboundNo) {
        this.outboundNo = outboundNo;
    }

    public String getForwarderNo() {
        return forwarderNo;
    }

    public void setForwarderNo(String forwarderNo) {
        this.forwarderNo = forwarderNo;
    }

    public Date getRentalSettlementDate() {
        return rentalSettlementDate;
    }

    public void setRentalSettlementDate(Date rentalSettlementDate) {
        this.rentalSettlementDate = rentalSettlementDate;
    }

    public Date getOutboundDate() {
        return outboundDate;
    }

    public void setOutboundDate(Date outboundDate) {
        this.outboundDate = outboundDate;
    }

    public String getClientCode() {
        return clientCode;
    }

    public void setClientCode(String clientCode) {
        this.clientCode = clientCode;
    }

    public String getSubOrderNo() {
        return subOrderNo;
    }

    public void setSubOrderNo(String subOrderNo) {
        this.subOrderNo = subOrderNo;
    }

    public String getSupplier() {
        return supplier;
    }

    public void setSupplier(String supplier) {
        this.supplier = supplier;
    }

    public String getDeliveryDriver() {
        return deliveryDriver;
    }

    public void setDeliveryDriver(String deliveryDriver) {
        this.deliveryDriver = deliveryDriver;
    }

    public String getDriverInfo() {
        return driverInfo;
    }

    public void setDriverInfo(String driverInfo) {
        this.driverInfo = driverInfo;
    }

    public String getSqdShippingMark() {
        return sqdShippingMark;
    }

    public void setSqdShippingMark(String sqdShippingMark) {
        this.sqdShippingMark = sqdShippingMark;
    }

    public String getCargoName() {
        return cargoName;
    }

    public void setCargoName(String cargoName) {
        this.cargoName = cargoName;
    }

    public Long getTotalBoxes() {
        return totalBoxes;
    }

    public void setTotalBoxes(Long totalBoxes) {
        this.totalBoxes = totalBoxes;
    }

    public String getPackageType() {
        return packageType;
    }

    public void setPackageType(String packageType) {
        this.packageType = packageType;
    }

    public BigDecimal getTotalGrossWeight() {
        return totalGrossWeight;
    }

    public void setTotalGrossWeight(BigDecimal totalGrossWeight) {
        this.totalGrossWeight = totalGrossWeight;
    }

    public BigDecimal getTotalVolume() {
        return totalVolume;
    }

    public void setTotalVolume(BigDecimal totalVolume) {
        this.totalVolume = totalVolume;
    }

    public String getDamageStatus() {
        return damageStatus;
    }

    public void setDamageStatus(String damageStatus) {
        this.damageStatus = damageStatus;
    }

    public String getStorageLocation1() {
        return storageLocation1;
    }

    public void setStorageLocation1(String storageLocation1) {
        this.storageLocation1 = storageLocation1;
    }

    public String getStorageLocation2() {
        return storageLocation2;
    }

    public void setStorageLocation2(String storageLocation2) {
        this.storageLocation2 = storageLocation2;
    }

    public String getStorageLocation3() {
        return storageLocation3;
    }

    public void setStorageLocation3(String storageLocation3) {
        this.storageLocation3 = storageLocation3;
    }

    public BigDecimal getReceivedStorageFee() {
        return receivedStorageFee;
    }

    public void setReceivedStorageFee(BigDecimal receivedStorageFee) {
        this.receivedStorageFee = receivedStorageFee;
    }

    public BigDecimal getUnpaidUnloadingFee() {
        return unpaidUnloadingFee;
    }

    public void setUnpaidUnloadingFee(BigDecimal unpaidUnloadingFee) {
        this.unpaidUnloadingFee = unpaidUnloadingFee;
    }

    public BigDecimal getLogisticsAdvanceFee() {
        return logisticsAdvanceFee;
    }

    public void setLogisticsAdvanceFee(BigDecimal logisticsAdvanceFee) {
        this.logisticsAdvanceFee = logisticsAdvanceFee;
    }

    public BigDecimal getRentalBalanceFee() {
        return rentalBalanceFee;
    }

    public void setRentalBalanceFee(BigDecimal rentalBalanceFee) {
        this.rentalBalanceFee = rentalBalanceFee;
    }

    public Long getFreeStackPeriod() {
        return freeStackPeriod;
    }

    public void setFreeStackPeriod(Long freeStackPeriod) {
        this.freeStackPeriod = freeStackPeriod;
    }

    public BigDecimal getOverdueRentalUnitPrice() {
        return overdueRentalUnitPrice;
    }

    public void setOverdueRentalUnitPrice(BigDecimal overdueRentalUnitPrice) {
        this.overdueRentalUnitPrice = overdueRentalUnitPrice;
    }

    public BigDecimal getOverdueRentalFee() {
        return overdueRentalFee;
    }

    public void setOverdueRentalFee(BigDecimal overdueRentalFee) {
        this.overdueRentalFee = overdueRentalFee;
    }

    public String getInboundNotes() {
        return inboundNotes;
    }

    public void setInboundNotes(String inboundNotes) {
        this.inboundNotes = inboundNotes;
    }

    public String getOutboundNotes() {
        return outboundNotes;
    }

    public void setOutboundNotes(String outboundNotes) {
        this.outboundNotes = outboundNotes;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("inventoryId", getInventoryId())
                .append("inventoryStatus", getInventoryStatus())
                .append("inboundSerialNo", getInboundSerialNo())
                .append("inboundSerialSplit", getInboundSerialSplit())
                .append("inboundDate", getInboundDate())
                .append("outboundNo", getOutboundNo())
                .append("forwarderNo", getForwarderNo())
                .append("rentalSettlementDate", getRentalSettlementDate())
                .append("outboundDate", getOutboundDate())
                .append("clientCode", getClientCode())
                .append("subOrderNo", getSubOrderNo())
                .append("supplier", getSupplier())
                .append("deliveryDriver", getDeliveryDriver())
                .append("driverInfo", getDriverInfo())
                .append("sqdShippingMark", getSqdShippingMark())
                .append("cargoName", getCargoName())
                .append("totalBoxes", getTotalBoxes())
                .append("packageType", getPackageType())
                .append("totalGrossWeight", getTotalGrossWeight())
                .append("totalVolume", getTotalVolume())
                .append("damageStatus", getDamageStatus())
                .append("storageLocation1", getStorageLocation1())
                .append("storageLocation2", getStorageLocation2())
                .append("storageLocation3", getStorageLocation3())
                .append("receivedStorageFee", getReceivedStorageFee())
                .append("unpaidUnloadingFee", getUnpaidUnloadingFee())
                .append("logisticsAdvanceFee", getLogisticsAdvanceFee())
                .append("rentalBalanceFee", getRentalBalanceFee())
                .append("freeStackPeriod", getFreeStackPeriod())
                .append("overdueRentalUnitPrice", getOverdueRentalUnitPrice())
                .append("overdueRentalFee", getOverdueRentalFee())
                .append("inboundNotes", getInboundNotes())
                .append("outboundNotes", getOutboundNotes())
                .toString();
    }
}
