package com.rich.common.utils;

import com.rich.common.core.domain.entity.SysRole;
import com.rich.common.exception.ServiceException;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/2/6 14:16
 * @Version 1.0
 */
public class SearchUtils {

    /**
     * [[1,2,3],[3],[2,3]]:只返回3
     *
     * 在数组列表中寻找各个数组中寻找交集
     * @param arrayLists
     * @return
     */
    public static List<Long> getLongs(@NotNull List<List<Long>> arrayLists) {
        List<Long> list = new LinkedList<>();
        Map<Long, Integer> a = new HashMap<>();
        for (List<Long> ls : arrayLists) {
            if (ls != null) {
                for (Long l : ls) {
                    // id没有出现过
                    if (!a.containsKey(l)) {
                        a.put(l, 1);
                        // 出现过，值加一
                    } else {
                        a.put(l, a.get(l) + 1);
                    }
                }
            }
        }
        for (Long key : a.keySet()) {
            // 某个元素的出现次数大于等于 arrayLists 列表的长度，则将该元素添加到 list 中
            if (a.get(key) >= arrayLists.size()) {
                list.add(key);
            }
        }
        return list;
    }

    public static boolean existSame(@NotNull Object[] a, @NotNull Object[] b) {
        boolean same = false;
        boolean bak = false;
        for (Object oa : a) {
            if (bak) {
                break;
            }
            for (Object ob : b) {
                if (oa.toString().equals(ob.toString())) {
                    same = true;
                    bak = true;
                    break;
                }
            }
        }
        return same;
    }

    public static String returnSame(@NotNull Object[] a, @NotNull Object[] b) {
        String same = null;
        boolean bak = false;
        for (Object oa : a) {
            if (bak) {
                break;
            }
            for (Object ob : b) {
                if (oa.toString().equals(ob.toString())) {
                    same = ob.toString();
                    bak = true;
                    break;
                }
            }
        }
        return same;
    }

    public static boolean isChief() {
        boolean chief = false;
        if (SecurityUtils.getLoginUser().getUser().getRoles().isEmpty()) {
            throw new ServiceException("该用户未分配角色");
        }
        for (SysRole r : SecurityUtils.getLoginUser().getUser().getRoles()) {
            if (r.getPosition() != null && r.getDept() != null) {
                // 是否是主管或组长
//                chief = (r.getPosition().getPositionSort() != null && r.getPosition().getPositionSort() == 7L || r.getPosition().getPositionSort() == 6L || r.getPosition().getPositionSort() == 5L) && r.getDept().getCreateListNum() != null && r.getDept().getCreateListNum() == 6L;
                chief = (r.getPosition() != null && r.getPosition().getPositionId() != null && r.getPosition().getPositionId() > 2L);
                if (chief) {
                    break;
                }
            }
        }
        return chief;
    }
}
