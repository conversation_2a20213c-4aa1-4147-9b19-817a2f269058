package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * <AUTHOR>
 * @Date 2024/1/23 17:44
 * @Version 1.0
 */
public class BasEmergencyLevel extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long emergencyLevelId;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String emergencyLevelName;

    public Long getEmergencyLevelId() {
        return emergencyLevelId;
    }

    public void setEmergencyLevelId(Long emergencyLevelId) {
        this.emergencyLevelId = emergencyLevelId;
    }

    public String getEmergencyLevelName() {
        return emergencyLevelName;
    }

    public void setEmergencyLevelName(String emergencyLevelName) {
        this.emergencyLevelName = emergencyLevelName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("emergencyLevelId", getEmergencyLevelId())
                .append("emergencyLevelName", getEmergencyLevelName())
                .append("remark", getRemark())
                .toString();
    }
}
