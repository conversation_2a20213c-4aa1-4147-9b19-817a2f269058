package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.RsInventory;
import com.rich.common.core.domain.entity.SysRole;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.DataAggregatorService;
import com.rich.system.service.RsInventoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 库存Controller
 *
 * <AUTHOR>
 * @date 2024-11-08
 */
@RestController
@RequestMapping("/system/inventory")
public class RsInventoryController extends BaseController {
    @Autowired
    private RsInventoryService rsInventoryService;

    @Autowired
    private DataAggregatorService dataAggregatorService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询库存列表
     */
    @PreAuthorize("@ss.hasPermi('system:inventory:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsInventory rsInventory) {
        rsInventory.setInventoryStatus("0");
        if (hasWarehouseAdminRole(SecurityUtils.getLoginUser().getUser().getRoles())
                || SecurityUtils.getDeptId().equals(109L)
                || SecurityUtils.getDeptId() == 102L
                || SecurityUtils.getLoginUser().getUser().isAdmin()) {
            rsInventory.setPermissionLevel(null);
        }
        if (rsInventory.getPackageTo() != null) {
            clearPage();
        } else {
            startPage();
        }
        List<RsInventory> list = rsInventoryService.selectRsInventoryList(rsInventory);
        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('system:inventory:list')")
    @GetMapping("/aggregator")
    public AjaxResult aggregator(RsInventory rsInventory) {
        try {
            rsInventory.setInventoryStatus("0");
            if (hasWarehouseAdminRole(SecurityUtils.getLoginUser().getUser().getRoles())
                    || SecurityUtils.getDeptId().equals(109L)
                    || SecurityUtils.getDeptId() == 102L
                    || SecurityUtils.getLoginUser().getUser().isAdmin()) {
                rsInventory.setPermissionLevel(null);
            }
            List<RsInventory> list = rsInventoryService.selectRsInventoryList(rsInventory);

            Map<String, Object> aggregatorConfig;
            try {
                aggregatorConfig = dataAggregatorService.extractAggregatorConfig(rsInventory);
            } catch (RuntimeException e) {
                logger.error("提取聚合配置失败", e);
                return AjaxResult.error(e.getMessage());
            }

            List<Map<String, Object>> aggregatedData = dataAggregatorService.aggregateData(list, aggregatorConfig);
            return AjaxResult.success(aggregatedData);
        } catch (Exception e) {
            logger.error("数据汇总处理失败", e);
            return AjaxResult.error("数据汇总处理失败: " + e.getMessage());
        }
    }

    @GetMapping("/lists")
    public TableDataInfo lists(RsInventory rsInventory) {
        rsInventory.setInventoryStatus("0");
        List<RsInventory> list = rsInventoryService.selectRsInventoryList(rsInventory);
        return getDataTable(list);
    }

    /**
     * 导出库存列表
     */
    @PreAuthorize("@ss.hasPermi('system:inventory:export')")
    @Log(title = "库存", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsInventory rsInventory) {
        if (hasWarehouseAdminRole(SecurityUtils.getLoginUser().getUser().getRoles())
                || SecurityUtils.getDeptId().equals(109L)
                || SecurityUtils.getDeptId() == 102L
                || SecurityUtils.getLoginUser().getUser().isAdmin()) {
            rsInventory.setPermissionLevel(null);
        }
        List<RsInventory> list = rsInventoryService.selectRsInventoryList(rsInventory);
        ExcelUtil<RsInventory> util = new ExcelUtil<RsInventory>(RsInventory.class);
        util.exportExcel(response, list, "库存数据", "库存数据");
    }

    /**
     * 获取库存详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:inventory:query')")
    @GetMapping(value = "/{inventoryId}")
    public AjaxResult getInfo(@PathVariable("inventoryId") Long inventoryId) {
        return AjaxResult.success(rsInventoryService.selectRsInventoryByInventoryId(inventoryId));
    }

    /**
     * 新增库存
     */
    @PreAuthorize("@ss.hasPermi('system:inventory:add')")
    @Log(title = "库存", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsInventory rsInventory) {
        return AjaxResult.success(rsInventoryService.insertRsInventory(rsInventory));
    }


    /**
     * 预出库
     */
    @Log(title = "预出库", businessType = BusinessType.UPDATE)
    @PutMapping("/preOutbound")
    public AjaxResult preOutbound(@RequestBody List<RsInventory> rsInventoryList) {
        int rows = 0;
        for (RsInventory rsInventory : rsInventoryList) {
            rows += rsInventoryService.preOutboundRsInventory(rsInventory);
        }
        return AjaxResult.success();
    }

    /**
     * 出库
     */
    @Log(title = "出库", businessType = BusinessType.UPDATE)
    @PutMapping("/outbound")
    public AjaxResult outbound(@RequestBody List<RsInventory> rsInventoryList) {
        int rows = 0;
        for (RsInventory rsInventory : rsInventoryList) {
            rows += rsInventoryService.outboundRsInventory(rsInventory);
        }
        return AjaxResult.success();
    }

    /**
     * 仓租结算
     */
    @Log(title = "出库", businessType = BusinessType.UPDATE)
    @PutMapping("/settlement")
    public AjaxResult settlement(@RequestBody List<RsInventory> rsInventoryList) {
        int rows = 0;
        rows += rsInventoryService.warehouseRentSettlement(rsInventoryList);
        return AjaxResult.success();
    }

    /**
     * 修改库存
     */
    @PreAuthorize("@ss.hasPermi('system:inventory:edit')")
    @Log(title = "库存", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsInventory rsInventory) {
        return toAjax(rsInventoryService.updateRsInventory(rsInventory));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:inventory:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsInventory rsInventory) {
        rsInventory.setUpdateBy(getUserId());
        return toAjax(rsInventoryService.changeStatus(rsInventory));
    }

    /**
     * 删除库存
     */
    @PreAuthorize("@ss.hasPermi('system:inventory:remove')")
    @Log(title = "库存", businessType = BusinessType.DELETE)
    @DeleteMapping("/{inventoryIds}")
    public AjaxResult remove(@PathVariable Long[] inventoryIds) {
        return toAjax(rsInventoryService.deleteRsInventoryByInventoryIds(inventoryIds));
    }

    /**
     * 判断用户是否拥有仓库管理员角色
     *
     * @param roles 用户角色列表
     * @return 是否拥有仓库管理员角色
     */
    private boolean hasWarehouseAdminRole(List<SysRole> roles) {
        if (roles == null || roles.isEmpty()) {
            return false;
        }

        // 检查用户角色列表中是否包含"Warehouse admin"角色
        return roles.stream()
                .anyMatch(role -> "Warehouse admin".equals(role.getRoleKey())
                        || "warehouse_admin".equals(role.getRoleKey()));
    }

    @GetMapping(value = "/package")
    public AjaxResult getPackages(RsInventory rsInventory) {
        return AjaxResult.success(rsInventoryService.getPackages(rsInventory));
    }

    @PutMapping("/packUp")
    public AjaxResult packUp(@RequestBody List<RsInventory> rsInventoryList) {
        int i = 0;
        for (RsInventory rsInventory : rsInventoryList) {
            i += rsInventoryService.updateRsInventory(rsInventory);
        }
        return toAjax(i);
    }

    @PutMapping("/cancelPkg")
    public AjaxResult cancelPkg(@RequestBody List<RsInventory> rsInventoryList) {
        int i = 0;
        for (RsInventory rsInventory : rsInventoryList) {
            rsInventory.setPackageTo(null);
            rsInventory.setPackageIntoNo(null);
            rsInventory.setPackageToNo(null);
            i += rsInventoryService.updateRsInventory(rsInventory);
        }
        return toAjax(i);
    }

    /**
     * 下载导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException {
        try {
            ExcelUtil<RsInventory> util = new ExcelUtil<>(RsInventory.class);
            util.importTemplateExcel(response, "库存数据");
        } catch (Exception ignore) {

        }
    }

    /**
     * 处理导入数据
     */
    @Log(title = "库存管理", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('system:inventory:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        // 移除缓存
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "inventory");
        ExcelUtil<RsInventory> util = new ExcelUtil<>(RsInventory.class);
        // 解析excel文件,获取数据
        List<RsInventory> inventoryList = util.importExcel(file.getInputStream());
        List<RsInventory> failList = rsInventoryService.importInventory(inventoryList, updateSupport);
        if (!failList.isEmpty()) {
            redisCache.setCacheObject("importFailList", failList);
            return AjaxResult.success("上传失败列表");
        } else {
            return AjaxResult.success("全部上传成功");
        }
    }

    /**
     * 导出导入失败的库存列表
     */
    @PreAuthorize("@ss.hasPermi('system:inventory:import')")
    @Log(title = "库存", businessType = BusinessType.EXPORT)
    @PostMapping("/failList")
    public void failList(HttpServletResponse response) {
        List<RsInventory> list = redisCache.getCacheObject("importFailList");
        ExcelUtil<RsInventory> util = new ExcelUtil<>(RsInventory.class);
        util.exportExcel(response, list, "上传失败列表");
        redisCache.deleteObject("importFailList");
    }
}
