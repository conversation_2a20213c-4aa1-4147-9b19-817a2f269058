package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasDistCargoType;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasDistCargoTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 货物特征Controller
 *
 * <AUTHOR>
 * @date 2022-08-29
 */
@RestController
@RequestMapping("/system/cargotype")
public class BasDistCargoTypeController extends BaseController {

    @Autowired
    private BasDistCargoTypeService basDistCargoTypeService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询货物特征列表
     */
    @PreAuthorize("@ss.hasPermi('system:cargotype:list')")
    @GetMapping("/list")
    public AjaxResult list(BasDistCargoType basDistCargoType) {
        return AjaxResult.success(basDistCargoTypeService.selectBasDistCargoTypeList(basDistCargoType));
    }

    @GetMapping("/selectList")
    public AjaxResult selectList(BasDistCargoType basDistCargoType) {
        List<BasDistCargoType> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        if (list == null) {
            basDistCargoType.setStatus("0");
            list = basDistCargoTypeService.selectBasDistCargoTypeList(basDistCargoType);
            redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
            redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType", list);
        }
        return AjaxResult.success(AjaxResult.DATA_TAG, list);
    }

    /**
     * 导出货物特征列表
     */
    @PreAuthorize("@ss.hasPermi('system:cargotype:export')")
    @Log(title = "货物特征", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasDistCargoType basDistCargoType) {
        List<BasDistCargoType> list = basDistCargoTypeService.selectBasDistCargoTypeList(basDistCargoType);
        ExcelUtil<BasDistCargoType> util = new ExcelUtil<BasDistCargoType>(BasDistCargoType.class);
        util.exportExcel(response, list, "货物特征数据");
    }

    /**
     * 获取货物特征详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:cargotype:edit')")
    @GetMapping(value = "/{cargoTypeId}")
    public AjaxResult getInfo(@PathVariable("cargoTypeId") Long cargoTypeId) {
        return AjaxResult.success(basDistCargoTypeService.selectBasDistCargoTypeByCargoTypeId(cargoTypeId));
    }

    /**
     * 新增货物特征
     */
    @PreAuthorize("@ss.hasPermi('system:cargotype:add')")
    @Log(title = "货物特征", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasDistCargoType basDistCargoType) {
        int out = basDistCargoTypeService.insertBasDistCargoType(basDistCargoType);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        return toAjax(out);
    }

    /**
     * 修改货物特征
     */
    @PreAuthorize("@ss.hasPermi('system:cargotype:edit')")
    @Log(title = "货物特征", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasDistCargoType basDistCargoType) {
        int out = basDistCargoTypeService.updateBasDistCargoType(basDistCargoType);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        return toAjax(out);
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('system:cargotype:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasDistCargoType basDistCargoType) {
        basDistCargoType.setUpdateBy(getUserId());
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        return toAjax(basDistCargoTypeService.change(basDistCargoType));
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('system:cargotype:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeLocked")
    public AjaxResult changeLocked(@RequestBody BasDistCargoType basDistCargoType) {
        basDistCargoType.setUpdateBy(getUserId());
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        return toAjax(basDistCargoTypeService.change(basDistCargoType));
    }

    /**
     * 删除货物特征
     */
    @PreAuthorize("@ss.hasPermi('system:cargotype:remove')")
    @Log(title = "货物特征", businessType = BusinessType.DELETE)
    @DeleteMapping("/{cargoTypeIds}")
    public AjaxResult remove(@PathVariable Long[] cargoTypeIds) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        return toAjax(basDistCargoTypeService.deleteBasDistCargoTypeByCargoTypeIds(cargoTypeIds));
    }
}
