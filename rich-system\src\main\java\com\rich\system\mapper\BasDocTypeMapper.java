package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.BasDocType;
import org.apache.ibatis.annotations.Mapper;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-08
 */
@Mapper
public interface BasDocTypeMapper {
    /**
     * 查询【请填写功能名称】
     *
     * @param docTypeCode 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    BasDocType selectBasDocTypeByDocTypeCode(String docTypeCode);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param basDocType 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    List<BasDocType> selectBasDocTypeList(BasDocType basDocType);

    /**
     * 新增【请填写功能名称】
     *
     * @param basDocType 【请填写功能名称】
     * @return 结果
     */
    int insertBasDocType(BasDocType basDocType);

    /**
     * 修改【请填写功能名称】
     *
     * @param basDocType 【请填写功能名称】
     * @return 结果
     */
    int updateBasDocType(BasDocType basDocType);

    /**
     * 删除【请填写功能名称】
     *
     * @param docTypeCode 【请填写功能名称】主键
     * @return 结果
     */
    int deleteBasDocTypeByDocTypeCode(String docTypeCode);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param docTypeCodes 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasDocTypeByDocTypeCodes(String[] docTypeCodes);
}
