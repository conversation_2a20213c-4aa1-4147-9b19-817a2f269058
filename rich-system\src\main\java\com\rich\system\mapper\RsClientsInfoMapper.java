package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsClientsInfo;
import org.apache.ibatis.annotations.Mapper;

/**
 * 用来记录客户常用的信息，避免重复劳动、错漏Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Mapper
public interface RsClientsInfoMapper {
    /**
     * 查询用来记录客户常用的信息，避免重复劳动、错漏
     *
     * @param clientsInfoId 用来记录客户常用的信息，避免重复劳动、错漏主键
     * @return 用来记录客户常用的信息，避免重复劳动、错漏
     */
    RsClientsInfo selectRsClientsInfoByClientsInfoId(Long clientsInfoId);

    /**
     * 查询用来记录客户常用的信息，避免重复劳动、错漏列表
     *
     * @param rsClientsInfo 用来记录客户常用的信息，避免重复劳动、错漏
     * @return 用来记录客户常用的信息，避免重复劳动、错漏集合
     */
    List<RsClientsInfo> selectRsClientsInfoList(RsClientsInfo rsClientsInfo);

    /**
     * 新增用来记录客户常用的信息，避免重复劳动、错漏
     *
     * @param rsClientsInfo 用来记录客户常用的信息，避免重复劳动、错漏
     * @return 结果
     */
    int insertRsClientsInfo(RsClientsInfo rsClientsInfo);

    /**
     * 修改用来记录客户常用的信息，避免重复劳动、错漏
     *
     * @param rsClientsInfo 用来记录客户常用的信息，避免重复劳动、错漏
     * @return 结果
     */
    int updateRsClientsInfo(RsClientsInfo rsClientsInfo);

    /**
     * 删除用来记录客户常用的信息，避免重复劳动、错漏
     *
     * @param clientsInfoId 用来记录客户常用的信息，避免重复劳动、错漏主键
     * @return 结果
     */
    int deleteRsClientsInfoByClientsInfoId(Long clientsInfoId);

    /**
     * 批量删除用来记录客户常用的信息，避免重复劳动、错漏
     *
     * @param clientsInfoIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsClientsInfoByClientsInfoIds(Long[] clientsInfoIds);
}
