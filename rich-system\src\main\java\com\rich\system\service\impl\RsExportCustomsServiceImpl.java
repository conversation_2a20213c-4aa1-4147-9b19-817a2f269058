package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsExportCustoms;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsExportCustomsMapper;
import com.rich.system.service.RsExportCustomsService;

/**
 * 出口报关Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@Service
public class RsExportCustomsServiceImpl implements RsExportCustomsService {
    @Autowired
    private RsExportCustomsMapper rsExportCustomsMapper;

    /**
     * 查询出口报关
     *
     * @param exportCustomsId 出口报关主键
     * @return 出口报关
     */
    @Override
    public RsExportCustoms selectRsExportCustomsByExportCustomsId(Long exportCustomsId) {
        return rsExportCustomsMapper.selectRsExportCustomsByExportCustomsId(exportCustomsId);
    }

    /**
     * 查询出口报关列表
     *
     * @param rsExportCustoms 出口报关
     * @return 出口报关
     */
    @Override
    public List<RsExportCustoms> selectRsExportCustomsList(RsExportCustoms rsExportCustoms) {
        return rsExportCustomsMapper.selectRsExportCustomsList(rsExportCustoms);
    }

    /**
     * 新增出口报关
     *
     * @param rsExportCustoms 出口报关
     * @return 结果
     */
    @Override
    public int insertRsExportCustoms(RsExportCustoms rsExportCustoms) {
        return rsExportCustomsMapper.insertRsExportCustoms(rsExportCustoms);
    }

    /**
     * 修改出口报关
     *
     * @param rsExportCustoms 出口报关
     * @return 结果
     */
    @Override
    public int updateRsExportCustoms(RsExportCustoms rsExportCustoms) {
        return rsExportCustomsMapper.updateRsExportCustoms(rsExportCustoms);
    }

    /**
     * 修改出口报关状态
     *
     * @param rsExportCustoms 出口报关
     * @return 出口报关
     */
    @Override
    public int changeStatus(RsExportCustoms rsExportCustoms) {
        return rsExportCustomsMapper.updateRsExportCustoms(rsExportCustoms);
    }

    /**
     * 批量删除出口报关
     *
     * @param exportCustomsIds 需要删除的出口报关主键
     * @return 结果
     */
    @Override
    public int deleteRsExportCustomsByExportCustomsIds(Long[] exportCustomsIds) {
        return rsExportCustomsMapper.deleteRsExportCustomsByExportCustomsIds(exportCustomsIds);
    }

    /**
     * 删除出口报关信息
     *
     * @param exportCustomsId 出口报关主键
     * @return 结果
     */
    @Override
    public int deleteRsExportCustomsByExportCustomsId(Long exportCustomsId) {
        return rsExportCustomsMapper.deleteRsExportCustomsByExportCustomsId(exportCustomsId);
    }
}
