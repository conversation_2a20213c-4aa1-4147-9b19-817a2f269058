package com.rich.system.service.impl;

import com.rich.common.constant.CacheConstants;
import com.rich.common.core.domain.entity.*;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.core.text.Convert;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SearchUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.StringUtils;
import com.rich.system.domain.MidCargoType;
import com.rich.system.domain.MidCarrier;
import com.rich.system.domain.MidServiceType;
import com.rich.system.domain.vo.RcRctStatisticsVO;
import com.rich.system.mapper.*;
import com.rich.system.service.RsRctOldService;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.rich.common.utils.PageUtils.startPage;

/**
 * 操作单列表Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-15
 */
@Service
public class RsRctOldServiceImpl implements RsRctOldService {
    @Autowired
    private RsRctOldMapper rsRctMapper;

    @Autowired
    private RsRctLogisticsTypeBasicInfoMapper rsRctLogisticsTypeBasicInfoMapper;

    @Autowired
    private RsRctLogisticsNoInfoMapper rsRctLogisticsNoInfoMapper;

    @Autowired
    private RsRctPreCarriageBasicInfoMapper rsRctPreCarriageBasicInfoMapper;

    @Autowired
    private RsRctPreCarriageNoInfoMapper rsRctPreCarriageNoInfoMapper;

    @Autowired
    private RsRctExportDeclarationBasicInfoMapper rsRctExportDeclarationBasicInfoMapper;

    @Autowired
    private RsRctImportClearanceBasicInfoMapper rsRctImportClearanceBasicInfoMapper;

    @Autowired
    private RsRctReceivablePayableMapper rsRctReceivablePayableMapper;

    @Autowired
    private RsOperationalProcessMapper rsOperationalProcessMapper;

    @Autowired
    private MidLocationDepartureMapper midLocationDepartureMapper;

    @Autowired
    private MidLocationDestinationMapper midLocationDestinationMapper;

    @Autowired
    private MidServiceTypeMapper midServiceTypeMapper;

    @Autowired
    private MidCargoTypeMapper midCargoTypeMapper;

    @Autowired
    private MidCarrierMapper midCarrierMapper;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisCacheImpl RedisCache;

    /**
     * 查询操作单列表
     *
     * @param rctId 操作单列表主键
     * @return 操作单列表
     */
    @Override
    public RsRctOld selectRsRctByRctId(Long rctId) {
        RsRctOld rsRct = rsRctMapper.selectRsRctByRctId(rctId);
        RsRctLogisticsTypeBasicInfo rsRctLogisticsTypeBasicInfo = rsRctLogisticsTypeBasicInfoMapper.selectRsRctLogisticsTypeBasicInfoByRctId(rctId);
        if (rsRctLogisticsTypeBasicInfo != null) {
            rsRctLogisticsTypeBasicInfo.setRsRctReceivablePayableList(rsRctReceivablePayableMapper.selectRsRctReceivablePayable(rsRct.getRctId(), rsRctLogisticsTypeBasicInfo.getTypeId(), rsRctLogisticsTypeBasicInfo.getLogisticsTypeInfoId()));
            rsRctLogisticsTypeBasicInfo.setRsOperationalProcessList(rsOperationalProcessMapper.selectRsOperationalProcess(rsRct.getRctId(), rsRctLogisticsTypeBasicInfo.getTypeId(), rsRctLogisticsTypeBasicInfo.getLogisticsTypeInfoId()));
        }
        RsRctPreCarriageBasicInfo rsRctPreCarriageBasicInfo = rsRctPreCarriageBasicInfoMapper.selectRsRctPreCarriageBasicInfoByRctId(rctId);
        if (rsRctPreCarriageBasicInfo != null) {
            rsRctPreCarriageBasicInfo.setRsRctReceivablePayableList(rsRctReceivablePayableMapper.selectRsRctReceivablePayable(rsRct.getRctId(), rsRctPreCarriageBasicInfo.getTypeId(), rsRctPreCarriageBasicInfo.getPreCarriageInfoId()));
            rsRctPreCarriageBasicInfo.setRsOperationalProcessList(rsOperationalProcessMapper.selectRsOperationalProcess(rsRct.getRctId(), rsRctPreCarriageBasicInfo.getTypeId(), rsRctPreCarriageBasicInfo.getPreCarriageInfoId()));
        }
        RsRctExportDeclarationBasicInfo rsRctExportDeclarationBasicInfo = rsRctExportDeclarationBasicInfoMapper.selectRsRctExportDeclarationBasicInfoByRctId(rctId);
        if (rsRctExportDeclarationBasicInfo != null) {
            rsRctExportDeclarationBasicInfo.setRsRctReceivablePayableList(rsRctReceivablePayableMapper.selectRsRctReceivablePayable(rsRct.getRctId(), rsRctExportDeclarationBasicInfo.getTypeId(), rsRctExportDeclarationBasicInfo.getExportDeclarationId()));
            rsRctExportDeclarationBasicInfo.setRsOperationalProcessList(rsOperationalProcessMapper.selectRsOperationalProcess(rsRct.getRctId(), rsRctExportDeclarationBasicInfo.getTypeId(), rsRctExportDeclarationBasicInfo.getExportDeclarationId()));
        }
        RsRctImportClearanceBasicInfo rsRctImportClearanceBasicInfo = rsRctImportClearanceBasicInfoMapper.selectRsRctImportClearanceBasicInfoByRctId(rctId);
        if (rsRctImportClearanceBasicInfo != null) {
            rsRctImportClearanceBasicInfo.setRsRctReceivablePayableList(rsRctReceivablePayableMapper.selectRsRctReceivablePayable(rsRct.getRctId(), rsRctImportClearanceBasicInfo.getTypeId(), rsRctImportClearanceBasicInfo.getImportClearanceId()));
            rsRctImportClearanceBasicInfo.setRsOperationalProcessList(rsOperationalProcessMapper.selectRsOperationalProcess(rsRct.getRctId(), rsRctImportClearanceBasicInfo.getTypeId(), rsRctImportClearanceBasicInfo.getImportClearanceId()));
        }
        rsRct.setRsRctLogisticsTypeBasicInfo(rsRctLogisticsTypeBasicInfo);
        rsRct.setRsRctPreCarriageBasicInfo(rsRctPreCarriageBasicInfo);
        rsRct.setRsRctExportDeclarationBasicInfo(rsRctExportDeclarationBasicInfo);
        rsRct.setRsRctImportClearanceBasicInfo(rsRctImportClearanceBasicInfo);
        return rsRct;
    }

    /**
     * 查询操作单列表列表
     *
     * @param rsRct 操作单列表
     * @return 操作单列表
     */
    @Override
    public List<RsRctOld> selectRsRctList(RsRctOld rsRct) {
        boolean search = rsRct.getPolIds() != null || rsRct.getDestinationPortIds() != null || rsRct.getLineIds() != null;
        if (search) {
            Map<String, List<?>> query = queryRctList(rsRct);
            List<List<Long>> queryList = (List<List<Long>>) query.get("list");
            List<Long> queryPolIds = (List<Long>) query.get("locationDeparture");
            List<Long> queryDestinationIds = (List<Long>) query.get("locationDestination");
            if (!queryList.isEmpty()) {
                rsRct.setRctIds(SearchUtils.getLongs(queryList));
            }
            if (!queryPolIds.isEmpty()) {
                rsRct.setPolIds(queryPolIds.toArray(new Long[0]));
            } else {
                rsRct.setPolIds(null);
            }
            if (!queryDestinationIds.isEmpty()) {
                rsRct.setDestinationPortIds(queryDestinationIds.toArray(new Long[0]));
            } else {
                rsRct.setDestinationPortIds(null);
            }
        }
        startPage();
        return rsRctMapper.selectRsRctList(rsRct);
    }

    private Map<String, List<?>> queryRctList(RsRctOld rsRct) {
        List<List<Long>> lists = new ArrayList<>();
        List<BasDistLocation> basDistLocations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        if (basDistLocations == null) {
            RedisCache.location();
            basDistLocations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        }
        List<BasDistLine> basDistLines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        if (basDistLines == null) {
            RedisCache.line();
            basDistLines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        }
        Set<Long> lineDestination = new HashSet<>();
        if (rsRct.getLineIds() != null) {
            for (BasDistLine line : basDistLines) {
                String[] lineAncestors = line.getAncestors().split(",");
                if (ArrayUtils.contains(rsRct.getLineIds(), line.getLineId())) {
                    for (String a : lineAncestors) {
                        lineDestination.add(Convert.toLong(a));
                    }
                }
                if (SearchUtils.existSame(lineAncestors, rsRct.getLineIds())) {
                    lineDestination.add(line.getLineId());
                }
            }
        }
        // 启运目的区域id集合
        Set<Long> locationDeparture = new HashSet<>();
        // 目的区域id集合
        Set<Long> locationDestination = new HashSet<>();
        //启运港或目的港
        if (rsRct.getPolIds() != null || rsRct.getDestinationPortIds() != null) {
            // 遍历所有的区域(3885)
            for (BasDistLocation location : basDistLocations) {
                // 当前地名的父级
                String[] ancestors = location.getAncestors().split(",");
                // 启运区域id没有包含-1(亚洲、欧洲、南美洲、北美洲、非洲、大洋洲)
                if (rsRct.getPolIds() != null && !ArrayUtils.contains(rsRct.getPolIds(), -1L)) {
                    // 当前区域的id在搜索的id集合中
                    if (ArrayUtils.contains(rsRct.getPolIds(), location.getLocationId())) {
                        locationDeparture.add(location.getLocationId());
                        for (String a : ancestors) {
                            locationDeparture.add(Convert.toLong(a));
                        }
                    }
                    // 是否存在重复
                    if (SearchUtils.existSame(ancestors, rsRct.getPolIds())) {
                        locationDeparture.add(location.getLocationId());
                    }
                }
                // 目的港不为空且不为顶级区域(亚洲、欧洲、南美洲、北美洲、非洲、大洋洲)
                if (rsRct.getDestinationPortIds() != null && !ArrayUtils.contains(rsRct.getDestinationPortIds(), -1L)) {
                    if (ArrayUtils.contains(rsRct.getDestinationPortIds(), location.getLocationId())) {
                        locationDestination.add(location.getLocationId());
                        for (String a : ancestors) {
                            locationDestination.add(Convert.toLong(a));
                        }
                    }
                    if (SearchUtils.existSame(ancestors, rsRct.getDestinationPortIds())) {
                        locationDestination.add(location.getLocationId());
                    }
                    if (lineDestination.contains(location.getLineId())) {
                        locationDestination.add(location.getLocationId());
                    }
                }
            }
        }
        HashMap<String, List<?>> out = new HashMap<>();
        out.put("list", lists);
        out.put("locationDeparture", Arrays.asList(locationDeparture.toArray()));
        out.put("locationDestination", Arrays.asList(locationDestination.toArray()));
        return out;
    }

    /**
     * 新增操作单列表
     *
     * @param rsRct 操作单列表
     * @return 结果
     */
    @Override
    public Long insertRsRct(RsRctOld rsRct) {
        rsRct.setCreateTime(DateUtils.getNowDate());
        rsRct.setCreateBy(SecurityUtils.getUserId());
        rsRctMapper.insertRsRct(rsRct);
        // 插入货物类型（普货、食品等）到中间表mid_cargo_type
        insertCargoType(rsRct);
        // 插入承运人（CMA、MSC等）到mid_carriers
        insertCarriers(rsRct);
        // 插入服务类型(海运、空运等)到mid_service_type
        insertServiceType(rsRct);

        // 通过websocket通知其他的用户刷新页面
        return rsRct.getRctId();
    }

    /**
     * 修改操作单列表
     *
     * @param rsRct 操作单列表
     * @return 结果
     */
    @Override
    public int updateRsRct(RsRctOld rsRct) {
        rsRct.setUpdateTime(DateUtils.getNowDate());
        rsRct.setUpdateBy(SecurityUtils.getUserId());
        midServiceTypeMapper.deleteMidServiceTypeById(rsRct.getRctId(), "rct");
        midCarrierMapper.deleteMidCarrierById(rsRct.getRctId(), "rct");
        midCargoTypeMapper.deleteMidCargoTypeById(rsRct.getRctId(), "rct");
        insertCargoType(rsRct);
        insertCarriers(rsRct);
        insertServiceType(rsRct);
        return rsRctMapper.updateRsRct(rsRct);
    }

    /**
     * 修改操作单列表状态
     *
     * @param rsRct 操作单列表
     * @return 操作单列表
     */
    @Override
    public int changeStatus(RsRctOld rsRct) {
        return rsRctMapper.updateRsRct(rsRct);
    }

    /**
     * 批量删除操作单列表
     *
     * @param rctIds 需要删除的操作单列表主键
     * @return 结果
     */
    @Override
    public int deleteRsRctByRctIds(Long[] rctIds) {
        midServiceTypeMapper.deleteMidServiceTypeByIds(rctIds, "rct");
        midCarrierMapper.deleteMidCarrierByIds(rctIds, "rct");
        midCargoTypeMapper.deleteMidCargoTypeByIds(rctIds, "rct");
        rsRctLogisticsTypeBasicInfoMapper.deleteRsRctLogisticsTypeBasicInfoByIds(rctIds);
        rsRctLogisticsNoInfoMapper.deleteRsRctLogisticsNoInfoByIds(rctIds);
        rsRctPreCarriageBasicInfoMapper.deleteRsRctPreCarriageBasicInfoByIds(rctIds);
        rsRctPreCarriageNoInfoMapper.deleteRsRctPreCarriageNoInfoByIds(rctIds);
        rsRctExportDeclarationBasicInfoMapper.deleteRsRctExportDeclarationBasicInfoByIds(rctIds);
        rsRctImportClearanceBasicInfoMapper.deleteRsRctImportClearanceBasicInfoByIds(rctIds);
        rsOperationalProcessMapper.deleteRsOperationalProcessByIds(rctIds);
        return rsRctMapper.deleteRsRctByRctIds(rctIds);
    }

    /**
     * 删除操作单列表信息
     *
     * @param rctId 操作单列表主键
     * @return 结果
     */
    @Override
    public int deleteRsRctByRctId(Long rctId) {
        midServiceTypeMapper.deleteMidServiceTypeById(rctId, "rct");
        midCarrierMapper.deleteMidCarrierById(rctId, "rct");
        midCargoTypeMapper.deleteMidCargoTypeById(rctId, "rct");
        rsRctLogisticsTypeBasicInfoMapper.deleteRsRctLogisticsTypeBasicInfoById(rctId);
        rsRctLogisticsNoInfoMapper.deleteRsRctLogisticsNoInfoByRctId(rctId);
        rsRctPreCarriageBasicInfoMapper.deleteRsRctPreCarriageBasicInfoById(rctId);
        rsRctPreCarriageNoInfoMapper.deleteRsRctPreCarriageNoInfoByRctId(rctId);
        rsRctExportDeclarationBasicInfoMapper.deleteRsRctExportDeclarationBasicInfoById(rctId);
        rsRctImportClearanceBasicInfoMapper.deleteRsRctImportClearanceBasicInfoById(rctId);
        rsOperationalProcessMapper.deleteRsOperationalProcessByRctId(rctId);
        return rsRctMapper.deleteRsRctByRctId(rctId);
    }

    @Override
    public int saveRctLogistics(RsRctOld rsRct) {
        int out;
        // 根据rctId获取对应的基础物流
        RsRctLogisticsTypeBasicInfo rsRctLogisticsTypeBasicInfo = rsRctLogisticsTypeBasicInfoMapper.selectRsRctLogisticsTypeBasicInfoByRctId(rsRct.getRctId());
        // 更新物流信息还是插入
        if (rsRctLogisticsTypeBasicInfo != null) {
            rsRct.getRsRctLogisticsTypeBasicInfo().setLogisticsTypeId(rsRctLogisticsTypeBasicInfo.getLogisticsTypeId());
            out = rsRctLogisticsTypeBasicInfoMapper.updateRsRctLogisticsTypeBasicInfo(rsRct.getRsRctLogisticsTypeBasicInfo());
        } else {
            rsRctLogisticsTypeBasicInfo = rsRct.getRsRctLogisticsTypeBasicInfo();
            out = rsRctLogisticsTypeBasicInfoMapper.insertRsRctLogisticsTypeBasicInfo(rsRctLogisticsTypeBasicInfo);
        }
        // 先删除跟物流信息相关的应收应付信息，再插入
        out += rsRctReceivablePayableMapper.deleteRsRctReceivablePayable(rsRct.getRctId(), rsRctLogisticsTypeBasicInfo.getTypeId(), rsRctLogisticsTypeBasicInfo.getLogisticsTypeInfoId());
        List<RsRctReceivablePayable> rsRctReceivablePayables = rsRct.getRsRctLogisticsTypeBasicInfo().getRsRctReceivablePayableList();
        if (rsRctReceivablePayables != null && !rsRctReceivablePayables.isEmpty()) {
            for (RsRctReceivablePayable rsRctReceivablePayable : rsRctReceivablePayables) {
                rsRctReceivablePayable.setBasicInfoId(rsRctLogisticsTypeBasicInfo.getLogisticsTypeInfoId());
                rsRctReceivablePayable.setRctId(rsRct.getRctId());
                rsRctReceivablePayable.setTypeId(rsRctLogisticsTypeBasicInfo.getTypeId());
                out += rsRctReceivablePayableMapper.insertRsRctReceivablePayable(rsRctReceivablePayable);
            }
        }
        // 编号信息先删除再插入
        out += rsRctLogisticsNoInfoMapper.deleteRsRctLogisticsNoInfoByRctId(rsRct.getRctId());
        List<RsRctLogisticsNoInfo> rsRctLogisticsNoInfos = rsRct.getRsRctLogisticsTypeBasicInfo().getRsRctLogisticsNoInfos();
        if (rsRctLogisticsNoInfos != null && !rsRctLogisticsNoInfos.isEmpty()) {
            for (RsRctLogisticsNoInfo rsRctLogisticsNoInfo : rsRctLogisticsNoInfos) {
                rsRctLogisticsNoInfo.setRctId(rsRct.getRctId());
                out += rsRctLogisticsNoInfoMapper.insertRsRctLogisticsNoInfo(rsRctLogisticsNoInfo);
            }
        }
        // 更新操作历史记录
        List<RsOperationalProcess> rsOperationalProcesses = rsRct.getRsRctLogisticsTypeBasicInfo().getRsOperationalProcessList();
        if (rsOperationalProcesses != null && !rsOperationalProcesses.isEmpty()) {
            for (RsOperationalProcess rsOperationalProcess : rsOperationalProcesses) {
                out += rsOperationalProcessMapper.updateRsOperationalProcess(rsOperationalProcess);
            }
        }
        return out;
    }

    @Override
    public int saveRctPreCarriage(RsRctOld rsRct) {
        int out;
        RsRctPreCarriageBasicInfo rsRctPreCarriageBasicInfo = rsRctPreCarriageBasicInfoMapper.selectRsRctPreCarriageBasicInfoByRctId(rsRct.getRctId());
        if (rsRctPreCarriageBasicInfo != null) {
            rsRct.getRsRctPreCarriageBasicInfo().setPreCarriageInfoId(rsRctPreCarriageBasicInfo.getPreCarriageInfoId());
            out = rsRctPreCarriageBasicInfoMapper.updateRsRctPreCarriageBasicInfo(rsRct.getRsRctPreCarriageBasicInfo());
        } else {
            rsRctPreCarriageBasicInfo = rsRct.getRsRctPreCarriageBasicInfo();
            out = rsRctPreCarriageBasicInfoMapper.insertRsRctPreCarriageBasicInfo(rsRctPreCarriageBasicInfo);
        }
        out += rsRctReceivablePayableMapper.deleteRsRctReceivablePayable(rsRct.getRctId(), rsRctPreCarriageBasicInfo.getTypeId(), rsRctPreCarriageBasicInfo.getPreCarriageInfoId());
        List<RsRctReceivablePayable> rsRctReceivablePayables = rsRct.getRsRctPreCarriageBasicInfo().getRsRctReceivablePayableList();
        if (rsRctReceivablePayables != null && rsRctReceivablePayables.size() > 0) {
            for (RsRctReceivablePayable rsRctReceivablePayable : rsRctReceivablePayables) {
                rsRctReceivablePayable.setBasicInfoId(rsRctPreCarriageBasicInfo.getPreCarriageInfoId());
                rsRctReceivablePayable.setRctId(rsRct.getRctId());
                rsRctReceivablePayable.setTypeId(rsRctPreCarriageBasicInfo.getTypeId());
                out += rsRctReceivablePayableMapper.insertRsRctReceivablePayable(rsRctReceivablePayable);
            }
        }
        out += rsRctPreCarriageNoInfoMapper.deleteRsRctPreCarriageNoInfoByRctId(rsRct.getRctId());
        List<RsRctPreCarriageNoInfo> rsRctPreCarriageNoInfos = rsRct.getRsRctPreCarriageBasicInfo().getRsRctPreCarriageNoInfos();
        if (rsRctPreCarriageNoInfos != null && rsRctPreCarriageNoInfos.size() > 0) {
            for (RsRctPreCarriageNoInfo rsRctPreCarriageNoInfo : rsRctPreCarriageNoInfos) {
                rsRctPreCarriageNoInfo.setRctId(rsRct.getRctId());
                out += rsRctPreCarriageNoInfoMapper.insertRsRctPreCarriageNoInfo(rsRctPreCarriageNoInfo);
            }
        }
        List<RsOperationalProcess> rsOperationalProcesses = rsRct.getRsRctPreCarriageBasicInfo().getRsOperationalProcessList();
        if (rsOperationalProcesses != null && rsOperationalProcesses.size() > 0) {
            for (RsOperationalProcess rsOperationalProcess : rsOperationalProcesses) {
                out += rsOperationalProcessMapper.updateRsOperationalProcess(rsOperationalProcess);
            }
        }
        return out;
    }

    @Override
    public int saveRctExportDeclaration(RsRctOld rsRct) {
        int out;
        RsRctExportDeclarationBasicInfo rsRctExportDeclarationBasicInfo = rsRctExportDeclarationBasicInfoMapper.selectRsRctExportDeclarationBasicInfoByRctId(rsRct.getRctId());
        if (rsRctExportDeclarationBasicInfo != null) {
            rsRct.getRsRctExportDeclarationBasicInfo().setExportDeclarationId(rsRctExportDeclarationBasicInfo.getExportDeclarationId());
            out = rsRctExportDeclarationBasicInfoMapper.updateRsRctExportDeclarationBasicInfo(rsRct.getRsRctExportDeclarationBasicInfo());
        } else {
            rsRctExportDeclarationBasicInfo = rsRct.getRsRctExportDeclarationBasicInfo();
            out = rsRctExportDeclarationBasicInfoMapper.insertRsRctExportDeclarationBasicInfo(rsRctExportDeclarationBasicInfo);
        }
        out += rsRctReceivablePayableMapper.deleteRsRctReceivablePayable(rsRct.getRctId(), rsRctExportDeclarationBasicInfo.getTypeId(), rsRctExportDeclarationBasicInfo.getExportDeclarationId());
        List<RsRctReceivablePayable> rsRctReceivablePayables = rsRct.getRsRctExportDeclarationBasicInfo().getRsRctReceivablePayableList();
        if (rsRctReceivablePayables != null && rsRctReceivablePayables.size() > 0) {
            for (RsRctReceivablePayable rsRctReceivablePayable : rsRctReceivablePayables) {
                rsRctReceivablePayable.setBasicInfoId(rsRctExportDeclarationBasicInfo.getExportDeclarationId());
                rsRctReceivablePayable.setRctId(rsRct.getRctId());
                rsRctReceivablePayable.setTypeId(rsRctExportDeclarationBasicInfo.getTypeId());
                out += rsRctReceivablePayableMapper.insertRsRctReceivablePayable(rsRctReceivablePayable);
            }
        }
        List<RsOperationalProcess> rsOperationalProcesses = rsRct.getRsRctExportDeclarationBasicInfo().getRsOperationalProcessList();
        if (rsOperationalProcesses != null && rsOperationalProcesses.size() > 0) {
            for (RsOperationalProcess rsOperationalProcess : rsOperationalProcesses) {
                out += rsOperationalProcessMapper.updateRsOperationalProcess(rsOperationalProcess);
            }
        }
        return out;
    }

    @Override
    public int saveRctImportClearance(RsRctOld rsRct) {
        int out;
        RsRctImportClearanceBasicInfo rsRctImportClearanceBasicInfo = rsRctImportClearanceBasicInfoMapper.selectRsRctImportClearanceBasicInfoByRctId(rsRct.getRctId());
        if (rsRctImportClearanceBasicInfo != null) {
            rsRct.getRsRctImportClearanceBasicInfo().setImportClearanceId(rsRctImportClearanceBasicInfo.getImportClearanceId());
            out = rsRctImportClearanceBasicInfoMapper.updateRsRctImportClearanceBasicInfo(rsRct.getRsRctImportClearanceBasicInfo());
        } else {
            rsRctImportClearanceBasicInfo = rsRct.getRsRctImportClearanceBasicInfo();
            out = rsRctImportClearanceBasicInfoMapper.insertRsRctImportClearanceBasicInfo(rsRctImportClearanceBasicInfo);
        }
        out += rsRctReceivablePayableMapper.deleteRsRctReceivablePayable(rsRct.getRctId(), rsRctImportClearanceBasicInfo.getTypeId(), rsRctImportClearanceBasicInfo.getImportClearanceId());
        List<RsRctReceivablePayable> rsRctReceivablePayables = rsRct.getRsRctImportClearanceBasicInfo().getRsRctReceivablePayableList();
        if (rsRctReceivablePayables != null && rsRctReceivablePayables.size() > 0) {
            for (RsRctReceivablePayable rsRctReceivablePayable : rsRctReceivablePayables) {
                rsRctReceivablePayable.setBasicInfoId(rsRctImportClearanceBasicInfo.getImportClearanceId());
                rsRctReceivablePayable.setRctId(rsRct.getRctId());
                rsRctReceivablePayable.setTypeId(rsRctImportClearanceBasicInfo.getTypeId());
                out += rsRctReceivablePayableMapper.insertRsRctReceivablePayable(rsRctReceivablePayable);
            }
        }
        List<RsOperationalProcess> rsOperationalProcesses = rsRct.getRsRctImportClearanceBasicInfo().getRsOperationalProcessList();
        if (rsOperationalProcesses != null && rsOperationalProcesses.size() > 0) {
            for (RsOperationalProcess rsOperationalProcess : rsOperationalProcesses) {
                out += rsOperationalProcessMapper.updateRsOperationalProcess(rsOperationalProcess);
            }
        }
        return out;
    }

    @Override
    public int getMon() {
        return rsRctMapper.getMon();
    }

    @Override
    public List<Long> getCarrierIds(Long rctId) {
        return midCarrierMapper.selectMidCarrierById(rctId, "rct");
    }

    @Override
    public List<Long> getCargoTypeIds(Long rctId) {
        return midCargoTypeMapper.selectMidCargoTypeById(rctId, "rct");
    }

    @Override
    public List<Long> getServiceTypeIds(Long rctId) {
        return midServiceTypeMapper.selectMidServiceTypeById(rctId, "rct");
    }

    @Override
    public List<RcRctStatisticsVO> getRctStatistics() {
        List<RcRctStatisticsVO> resultList = rsRctMapper.selectOpStatistics();

        return resultList;
    }

    private void insertCarriers(RsRctOld rsRct) {
        Long[] roles = rsRct.getCarrierIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidCarrier> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidCarrier midCarrier = new MidCarrier();
                midCarrier.setBelongId(rsRct.getRctId());
                midCarrier.setCarrierId(r);
                midCarrier.setBelongTo("rct");
                list.add(midCarrier);
            }
            midCarrierMapper.batchCarrier(list);
        }
        RedisCache.midCarrier("rct", "rctCarriers");
    }

    private void insertCargoType(RsRctOld rsRct) {
        Long[] roles = rsRct.getCargoTypeIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidCargoType> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidCargoType MidCargoType = new MidCargoType();
                MidCargoType.setBelongId(rsRct.getRctId());
                MidCargoType.setBelongTo("rct");
                MidCargoType.setCargoTypeId(r);
                list.add(MidCargoType);
            }
            midCargoTypeMapper.batchCargoType(list);
        }
        RedisCache.midCargoType("rct", "rctCargoType");
    }

    private void insertServiceType(RsRctOld rsRct) {
        Long[] roles = rsRct.getServiceTypeIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidServiceType> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidServiceType MidServiceType = new MidServiceType();
                MidServiceType.setBelongId(rsRct.getRctId());
                MidServiceType.setServiceTypeId(r);
                MidServiceType.setBelongTo("rct");
                list.add(MidServiceType);
            }
            midServiceTypeMapper.batchServiceType(list);
        }
        RedisCache.midServiceType("rct", "rctServiceType");
    }
}
