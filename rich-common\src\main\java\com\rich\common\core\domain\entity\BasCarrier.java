package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

/**
 * 船公司对象 bas_carrier
 *
 * <AUTHOR>
 * @date 2022-10-31
 */
public class BasCarrier extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主要承运人国际通用编码
     */
    private Long carrierId;

    /**
     * 主要承运人国际通用编码
     */
    @Excel(name = "主要承运人国际通用编码")
    private String carrierIntlCode;

    /**
     * 承运人简称（中文）
     */
    @Excel(name = "承运人简称", readConverterExp = "中=文")
    private String carrierShortName;

    private String carrierEnName;

    private String carrierLocalName;

    /**
     * 货物查询追踪网址
     */
    @Excel(name = "货物查询追踪网址")
    private String trackingWebsite;

    /**
     * 两位数国家/地区代码，查询location
     */
    @Excel(name = "两位数国家/地区代码，查询location")
    private String locationId;

    private Integer orderNum;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;

    private List<Long> serviceTypeIds;

    private String serviceType;

    private Long serviceTypeId;

    private String location;

    private String carrierQuery;
    private String serviceTypeIdString;
    private String typeIds;

    public String getTypeIds() {
        return typeIds;
    }

    public void setTypeIds(String typeIds) {
        this.typeIds = typeIds;
    }

    public String getServiceTypeIdString() {
        return serviceTypeIdString;
    }

    public void setServiceTypeIdString(String serviceTypeIdString) {
        this.serviceTypeIdString = serviceTypeIdString;
    }

    public String getCarrierQuery() {
        return carrierQuery;
    }

    public void setCarrierQuery(String carrierQuery) {
        this.carrierQuery = carrierQuery;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Long getServiceTypeId() {
        return serviceTypeId;
    }

    public void setServiceTypeId(Long serviceTypeId) {
        this.serviceTypeId = serviceTypeId;
    }

    public List<Long> getServiceTypeIds() {
        return serviceTypeIds;
    }

    public void setServiceTypeIds(List<Long> serviceTypeIds) {
        this.serviceTypeIds = serviceTypeIds;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    public Long getCarrierId() {
        return carrierId;
    }

    public void setCarrierIntlCode(String carrierIntlCode) {
        this.carrierIntlCode = carrierIntlCode;
    }

    public String getCarrierIntlCode() {
        return carrierIntlCode;
    }

    public void setCarrierShortName(String carrierShortName) {
        this.carrierShortName = carrierShortName;
    }

    public String getCarrierShortName() {
        return carrierShortName;
    }

    public void setCarrierEnName(String carrierEnName) {
        this.carrierEnName = carrierEnName;
    }

    public String getCarrierEnName() {
        return carrierEnName;
    }

    public void setCarrierLocalName(String carrierLocalName) {
        this.carrierLocalName = carrierLocalName;
    }

    public String getCarrierLocalName() {
        return carrierLocalName;
    }

    public void setTrackingWebsite(String trackingWebsite) {
        this.trackingWebsite = trackingWebsite;
    }

    public String getTrackingWebsite() {
        return trackingWebsite;
    }

    public void setLocationId(String locationId) {
        this.locationId = locationId;
    }

    public String getLocationId() {
        return locationId;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("carrierId", getCarrierId())
                .append("carrierIntlCode", getCarrierIntlCode())
                .append("carrierShortName", getCarrierShortName())
                .append("carrierEnName", getCarrierEnName())
                .append("carrierLocalName", getCarrierLocalName())
                .append("trackingWebsite", getTrackingWebsite())
                .append("locationId", getLocationId())
                .append("orderNum", getOrderNum())
                .append("status", getStatus())
                .append("remark", getRemark())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("deleteBy", getDeleteBy())
                .append("deleteTime", getDeleteTime())
                .append("deleteStatus", getDeleteStatus())
                .toString();
    }
}
