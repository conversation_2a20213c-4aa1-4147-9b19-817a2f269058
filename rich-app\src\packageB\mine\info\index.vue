<template>
  <view class="container">
    <uni-list>
      <uni-list-item :extraIcon="{type: 'person-filled'}" :rightText="user.wxNickName" showExtraIcon="true"
                     title="昵称"/>
      <uni-list-item :extraIcon="{type: 'phone-filled'}" :rightText="user.phonenumber" showExtraIcon="true"
                     title="手机号码"/>
      <uni-list-item :extraIcon="{type: 'email-filled'}" :rightText="user.email" showExtraIcon="true" title="邮箱"/>
      <uni-list-item :extraIcon="{type: 'auth-filled'}" :rightText="postGroup" showExtraIcon="true" title="岗位"/>
      <uni-list-item :extraIcon="{type: 'staff-filled'}" :rightText="roleGroup" showExtraIcon="true" title="角色"/>
      <uni-list-item :extraIcon="{type: 'calendar-filled'}" :rightText="user.createTime" showExtraIcon="true"
                     title="创建日期"/>
    </uni-list>
  </view>
</template>

<script>
import {getUserProfile} from "@/api/system/user"

export default {
  data() {
    return {
      user: {},
      roleGroup: "",
      postGroup: ""
    }
  },
  onLoad() {
    this.getUser()
  },
  methods: {
    getUser() {
      getUserProfile().then(response => {
        this.user = response.data
        this.roleGroup = response.roleGroup
        this.postGroup = response.postGroup
      })
    }
  }
}
</script>

<style lang="scss">
page {
  background-color: #ffffff;
}
</style>
