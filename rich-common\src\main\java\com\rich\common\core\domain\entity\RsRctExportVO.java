package com.rich.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;

import javax.xml.crypto.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/1/23 17:35
 * @Version 1.0
 */
public class RsRctExportVO {
    @Excel(name = "部门")
    private String deptName;
    @Excel(name = "流水号")
    private String rctNo;
    @Excel(name = "业务员")
    private String salesName;
    @Excel(name = "操作员")
    private String opName;
    @Excel(name = "主业务类型")
    private String logisticsTypeName;
    @Excel(name = "装箱类型")
    private String serviceType;
    @Excel(name = "箱型")
    private String revenueTon;
    @Excel(name = "装货港")
    private String pol;
    @Excel(name = "目的港")
    private String destinationPort;
    @Excel(name = "含税应收金额")
    private BigDecimal sqdDnRmbSumVat;
    @Excel(name = "含税应付金额")
    private BigDecimal sqdCnRmbSumVat;
    @Excel(name = "含税利润")
    private BigDecimal sqdProfitRmbSum;
    @Excel(name = "毛利率%")
    private String grossProfitMargin;
    @Excel(name = "操作日期")
//    @JsonFormat(pattern = "yyyy-MM-dd")
    private String rctCreateTime;
    @Excel(name = "ETD")
    private String etd;
    @Excel(name = "总柜数")
    private String totalContainers;
    @Excel(name = "Teu")
    private String Teu;

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getRctNo() {
        return rctNo;
    }

    public void setRctNo(String rctNo) {
        this.rctNo = rctNo;
    }

    public String getSalesName() {
        return salesName;
    }

    public void setSalesName(String salesName) {
        this.salesName = salesName;
    }

    public String getOpName() {
        return opName;
    }

    public void setOpName(String opName) {
        this.opName = opName;
    }

    public String getLogisticsTypeName() {
        return logisticsTypeName;
    }

    public void setLogisticsTypeName(String logisticsTypeName) {
        this.logisticsTypeName = logisticsTypeName;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public String getRevenueTon() {
        return revenueTon;
    }

    public void setRevenueTon(String revenueTon) {
        this.revenueTon = revenueTon;
    }

    public String getPol() {
        return pol;
    }

    public void setPol(String pol) {
        this.pol = pol;
    }

    public String getDestinationPort() {
        return destinationPort;
    }

    public void setDestinationPort(String destinationPort) {
        this.destinationPort = destinationPort;
    }

    public BigDecimal getSqdDnRmbSumVat() {
        return sqdDnRmbSumVat;
    }

    public void setSqdDnRmbSumVat(BigDecimal sqdDnRmbSumVat) {
        this.sqdDnRmbSumVat = sqdDnRmbSumVat;
    }

    public BigDecimal getSqdCnRmbSumVat() {
        return sqdCnRmbSumVat;
    }

    public void setSqdCnRmbSumVat(BigDecimal sqdCnRmbSumVat) {
        this.sqdCnRmbSumVat = sqdCnRmbSumVat;
    }

    public BigDecimal getSqdProfitRmbSum() {
        return sqdProfitRmbSum;
    }

    public void setSqdProfitRmbSum(BigDecimal sqdProfitRmbSum) {
        this.sqdProfitRmbSum = sqdProfitRmbSum;
    }

    public String getGrossProfitMargin() {
        return grossProfitMargin;
    }

    public void setGrossProfitMargin(String grossProfitMargin) {
        this.grossProfitMargin = grossProfitMargin;
    }

    public String getRctCreateTime() {
        return rctCreateTime;
    }

    public void setRctCreateTime(String rctCreateTime) {
        this.rctCreateTime = rctCreateTime;
    }

    public String getEtd() {
        return etd;
    }

    public void setEtd(String etd) {
        this.etd = etd;
    }

    public String getTotalContainers() {
        return totalContainers;
    }

    public void setTotalContainers(String totalContainers) {
        this.totalContainers = totalContainers;
    }

    public String getTeu() {
        return Teu;
    }

    public void setTeu(String teu) {
        Teu = teu;
    }
}
