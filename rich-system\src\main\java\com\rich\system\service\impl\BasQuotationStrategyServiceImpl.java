package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.BasQuotationStrategy;
import com.rich.system.mapper.BasQuotationStrategyMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.service.BasQuotationStrategyService;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-25
 */
@Service
public class BasQuotationStrategyServiceImpl implements BasQuotationStrategyService {
    @Autowired
    private BasQuotationStrategyMapper basQuotationStrategyMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param strategyCode 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public BasQuotationStrategy selectBasQuotationStrategyByStrategyCode(String strategyCode) {
        return basQuotationStrategyMapper.selectBasQuotationStrategyByStrategyCode(strategyCode);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param basQuotationStrategy 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<BasQuotationStrategy> selectBasQuotationStrategyList(BasQuotationStrategy basQuotationStrategy) {
        return basQuotationStrategyMapper.selectBasQuotationStrategyList(basQuotationStrategy);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param basQuotationStrategy 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertBasQuotationStrategy(BasQuotationStrategy basQuotationStrategy) {
        return basQuotationStrategyMapper.insertBasQuotationStrategy(basQuotationStrategy);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param basQuotationStrategy 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateBasQuotationStrategy(BasQuotationStrategy basQuotationStrategy) {
        return basQuotationStrategyMapper.updateBasQuotationStrategy(basQuotationStrategy);
    }

    /**
     * 修改【请填写功能名称】状态
     *
     * @param basQuotationStrategy 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public int changeStatus(BasQuotationStrategy basQuotationStrategy) {
        return basQuotationStrategyMapper.updateBasQuotationStrategy(basQuotationStrategy);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param strategyCodes 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteBasQuotationStrategyByStrategyCodes(String[] strategyCodes) {
        return basQuotationStrategyMapper.deleteBasQuotationStrategyByStrategyCodes(strategyCodes);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param strategyCode 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteBasQuotationStrategyByStrategyCode(String strategyCode) {
        return basQuotationStrategyMapper.deleteBasQuotationStrategyByStrategyCode(strategyCode);
    }
}
