package com.rich.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订舱单列表对象 rs_booking
 *
 * <AUTHOR>
 * @date 2023-08-15
 */
public class RsBooking extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 订舱申请单
     */
    private Long bookingId;

    /**
     * 操作员
     */
    @Excel(name = "操作员")
    private Long opId;

    private String opName;

    private String opAllocation;

    /**
     * 订舱单号
     */
    @Excel(name = "订舱单号")
    private String newBookingNo;

    /**
     * 订舱日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "订舱日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date newBookingTime;

    /**
     * 报价单号
     */
    @Excel(name = "报价单号")
    private String quotationNo;

    /**
     * 报价日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "报价日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date quotationDate;

    /**
     * 业务员
     */
    @Excel(name = "业务员")
    private Long salesId;

    private String salesName;

    /**
     * 业务助理
     */
    @Excel(name = "业务助理")
    private Long salesAssistantId;

    private String salesAssistantName;

    /**
     * 协助业务
     */
    @Excel(name = "协助业务")
    private Long salesObserverId;

    private String salesObserverName;

    private Integer isPsaVerified;
    @Excel(name = "商务审核")
    private Long verifyPsaId;

    private String psaName;

    /**
     * 商务审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "商务审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date psaVerifyTime;

    /**
     * 紧急程度
     */
    @Excel(name = "紧急程度")
    private String urgencyDegree;

    /**
     * 收付方式
     */
    @Excel(name = "收付方式")
    private Long paymentTypeId;

    private String paymentTypeName;

    /**
     * 放货方式
     */
    @Excel(name = "放货方式")
    private Long releaseTypeId;

    private String releaseTypeName;

    /**
     * 进度状态
     */
    @Excel(name = "进度状态")
    private Long processStatusId;

    private String processStatusName;
    /**
     * 委托单位
     */
    @Excel(name = "委托单位")
    private Long clientId;

    private String clientName;

    /**
     * 客户角色
     */
    @Excel(name = "客户角色")
    private Long clientRoleId;

    private String clientRoleName;

    /**
     * 联系人
     */
    @Excel(name = "联系人")
    private String clientContactor;

    /**
     * 联系人电话
     */
    @Excel(name = "联系人电话")
    private String clientContactorTel;

    /**
     * 邮箱
     */
    @Excel(name = "邮箱")
    private String clientContactorEmail;

    /**
     * 关联单位
     */
    @Excel(name = "关联单位")
    private String relationClientIds;

    /**
     * 进出口
     */
    @Excel(name = "进出口")
    private Integer impExpTypeId;

    /**
     * 收汇方式
     */
    @Excel(name = "收汇方式")
    private Long tradingPaymentChannelId;

    private String tradingPaymentChannelName;

    /**
     * 贸易条款
     */
    @Excel(name = "贸易条款")
    private Long tradingTermsId;

    private String tradingTermsName;

    /**
     * 运输条款
     */
    @Excel(name = "运输条款")
    private Long logisticsTermsId;

    private String logisticsTermsName;

    /**
     * 合同号
     */
    @Excel(name = "合同号")
    private String contractNo;

    /**
     * 发票号
     */
    @Excel(name = "发票号")
    private String invoiceNo;

    /**
     * 货名概要
     */
    @Excel(name = "货名概要")
    private String goodsNameSummary;

    /**
     * 件数
     */
    @Excel(name = "件数")
    private Integer packageQuantity;

    /**
     * 毛重
     */
    @Excel(name = "毛重")
    private BigDecimal grossWeight;

    /**
     * 重量单位
     */
    @Excel(name = "重量单位")
    private Long weightUnitId;

    private String weightUnitName;

    /**
     * 总体积
     */
    @Excel(name = "总体积")
    private BigDecimal volume;

    /**
     * 体积单位
     */
    @Excel(name = "体积单位")
    private Long volumeUnitId;

    private String volumeUnitName;

    /**
     * 货物特征
     */
    @Excel(name = "货物特征")
    private Long[] cargoTypeIds;

    /**
     * 总货值
     */
    @Excel(name = "总货值")
    private BigDecimal goodsValue;

    /**
     * 货值币种
     */
    @Excel(name = "货值币种")
    private Long goodsCurrencyId;

    private String goodsCurrencyName;

    /**
     * 货物限重
     */
    @Excel(name = "货物限重")
    private BigDecimal maxWeight;

    /**
     * 计费货量
     */
    @Excel(name = "计费货量")
    private String revenueTons;

    /**
     * 物流类型
     */
    @Excel(name = "物流类型")
    private Long logisticsTypeId;

    private String logisticsTypeName;

    /**
     * 启运港
     */
    @Excel(name = "启运港")
    private Long polId;

    private Long[] polIds;

    /**
     * 目的港
     */
    @Excel(name = "目的港")
    private Long destinationPortId;

    private Long[] destinationPortIds;

    /**
     * 承运人
     */
    @Excel(name = "承运人")
    private Long[] carrierIds;

    /**
     * 船期
     */
    @Excel(name = "船期")
    private String schedule;

    /**
     * 有效期
     */
    @Excel(name = "有效期")
    private String validDate;

    /**
     * 主提单
     */
    @Excel(name = "主提单")
    private Integer isMblNeeded;

    /**
     * 主提单号
     */
    @Excel(name = "主提单号")
    private String mblNo;

    /**
     * 套约
     */
    @Excel(name = "套约")
    private Integer isUnderAgreementMbl;

    /**
     * 清关中转
     */
    @Excel(name = "清关中转")
    private Integer isCustomsIntransitMbl;

    /**
     * 转单
     */
    @Excel(name = "转单")
    private Integer isSwitchMbl;

    /**
     * 拆单
     */
    @Excel(name = "拆单")
    private Integer isDividedMbl;

    /**
     * 出单方式
     */
    @Excel(name = "出单方式")
    private Long mblIssueTypeId;

    private String mblIssueTypeName;

    /**
     * 取单方式
     */
    @Excel(name = "取单方式")
    private Long mblGetWayId;

    private String mblGetWayName;

    /**
     * 交单方式
     */
    @Excel(name = "交单方式")
    private Long mblReleaseWayId;

    private String mblReleaseWayName;

    /**
     * 货代提单
     */
    @Excel(name = "货代提单")
    private Integer isHblNeeded;

    /**
     * 货代单号
     */
    @Excel(name = "货代单号")
    private String hblNoList;

    /**
     * 套约
     */
    @Excel(name = "套约")
    private Integer isUnderAgreementHbl;

    /**
     * 清关中转
     */
    @Excel(name = "清关中转")
    private Integer isCustomsIntransitHbl;

    /**
     * 转单
     */
    @Excel(name = "转单")
    private Integer isSwitchHbl;

    /**
     * 拆单
     */
    @Excel(name = "拆单")
    private Integer isDividedHbl;

    /**
     * 出单方式
     */
    @Excel(name = "出单方式")
    private Long hblIssueTypeId;

    private String hblIssueTypeName;
    /**
     * 取单方式
     */
    @Excel(name = "取单方式")
    private Long hblGetWayId;

    private String hblGetWayName;

    /**
     * 交单方式
     */
    @Excel(name = "交单方式")
    private Long hblReleaseWayId;

    private String hblReleaseWayName;
    /**
     * 服务类型
     */
    @Excel(name = "服务类型")
    private Long[] serviceTypeIds;

    /**
     * 业务报价综述
     */
    @Excel(name = "业务报价综述")
    private String quotationSummary;

    /**
     * 业务订舱备注
     */
    @Excel(name = "业务订舱备注")
    private String newBookingRemark;

    /**
     * 业务须知
     */
    @Excel(name = "业务须知")
    private String inquiryNotice;

    /**
     * 商务备注
     */
    @Excel(name = "商务备注")
    private String inquiryInnerRemark;

    /**
     * 操作主管备注
     */
    @Excel(name = "操作主管备注")
    private String opLeaderRemark;

    /**
     * 操作备注
     */
    @Excel(name = "操作备注")
    private String opInnerRemark;

    /**
     * 合约类型
     */
    @Excel(name = "合约类型")
    private Long agreementTypeId;

    /**
     * 合约号
     */
    @Excel(name = "合约号")
    private String agreementNo;

    private String readOnly;


    private String pol;

    private String destinationPort;

    private String cargoTypes;

    private String carriers;

    private String serviceTypes;

    private RsBookingLogisticsTypeBasicInfo rsBookingLogisticsTypeBasicInfo;

    private RsBookingPreCarriageBasicInfo rsBookingPreCarriageBasicInfo;

    private RsBookingExportDeclarationBasicInfo rsBookingExportDeclarationBasicInfo;

    private RsBookingImportClearanceBasicInfo rsBookingImportClearanceBasicInfo;

    private List<Long> bookingIds;


    private Date psaVerifyFrom;

    private Date psaVerifyTo;

    private Long[] lineIds;

    public Long[] getPolIds() {
        return polIds;
    }

    public void setPolIds(Long[] polIds) {
        this.polIds = polIds;
    }

    public Long[] getDestinationPortIds() {
        return destinationPortIds;
    }

    public void setDestinationPortIds(Long[] destinationPortIds) {
        this.destinationPortIds = destinationPortIds;
    }

    public Long getPolId() {
        return polId;
    }

    public void setPolId(Long polId) {
        this.polId = polId;
    }

    public Long getDestinationPortId() {
        return destinationPortId;
    }

    public void setDestinationPortId(Long destinationPortId) {
        this.destinationPortId = destinationPortId;
    }

    public String getPol() {
        return pol;
    }

    public void setPol(String pol) {
        this.pol = pol;
    }

    public String getDestinationPort() {
        return destinationPort;
    }

    public void setDestinationPort(String destinationPort) {
        this.destinationPort = destinationPort;
    }

    public Long getProcessStatusId() {
        return processStatusId;
    }

    public void setProcessStatusId(Long processStatusId) {
        this.processStatusId = processStatusId;
    }

    public String getProcessStatusName() {
        return processStatusName;
    }

    public void setProcessStatusName(String processStatusName) {
        this.processStatusName = processStatusName;
    }

    public String getCargoTypes() {
        return cargoTypes;
    }

    public void setCargoTypes(String cargoTypes) {
        this.cargoTypes = cargoTypes;
    }

    public String getCarriers() {
        return carriers;
    }

    public void setCarriers(String carriers) {
        this.carriers = carriers;
    }

    public String getServiceTypes() {
        return serviceTypes;
    }

    public void setServiceTypes(String serviceTypes) {
        this.serviceTypes = serviceTypes;
    }

    public String getSalesObserverName() {
        return salesObserverName;
    }

    public void setSalesObserverName(String salesObserverName) {
        this.salesObserverName = salesObserverName;
    }

    public String getPaymentTypeName() {
        return paymentTypeName;
    }

    public void setPaymentTypeName(String paymentTypeName) {
        this.paymentTypeName = paymentTypeName;
    }

    public String getReleaseTypeName() {
        return releaseTypeName;
    }

    public void setReleaseTypeName(String releaseTypeName) {
        this.releaseTypeName = releaseTypeName;
    }

    public String getClientRoleName() {
        return clientRoleName;
    }

    public void setClientRoleName(String clientRoleName) {
        this.clientRoleName = clientRoleName;
    }

    public String getTradingPaymentChannelName() {
        return tradingPaymentChannelName;
    }

    public void setTradingPaymentChannelName(String tradingPaymentChannelName) {
        this.tradingPaymentChannelName = tradingPaymentChannelName;
    }

    public String getTradingTermsName() {
        return tradingTermsName;
    }

    public void setTradingTermsName(String tradingTermsName) {
        this.tradingTermsName = tradingTermsName;
    }

    public String getLogisticsTermsName() {
        return logisticsTermsName;
    }

    public void setLogisticsTermsName(String logisticsTermsName) {
        this.logisticsTermsName = logisticsTermsName;
    }

    public String getWeightUnitName() {
        return weightUnitName;
    }

    public void setWeightUnitName(String weightUnitName) {
        this.weightUnitName = weightUnitName;
    }

    public String getVolumeUnitName() {
        return volumeUnitName;
    }

    public void setVolumeUnitName(String volumeUnitName) {
        this.volumeUnitName = volumeUnitName;
    }

    public String getGoodsCurrencyName() {
        return goodsCurrencyName;
    }

    public void setGoodsCurrencyName(String goodsCurrencyName) {
        this.goodsCurrencyName = goodsCurrencyName;
    }

    public String getLogisticsTypeName() {
        return logisticsTypeName;
    }

    public void setLogisticsTypeName(String logisticsTypeName) {
        this.logisticsTypeName = logisticsTypeName;
    }

    public String getMblIssueTypeName() {
        return mblIssueTypeName;
    }

    public void setMblIssueTypeName(String mblIssueTypeName) {
        this.mblIssueTypeName = mblIssueTypeName;
    }

    public String getMblGetWayName() {
        return mblGetWayName;
    }

    public void setMblGetWayName(String mblGetWayName) {
        this.mblGetWayName = mblGetWayName;
    }

    public String getMblReleaseWayName() {
        return mblReleaseWayName;
    }

    public void setMblReleaseWayName(String mblReleaseWayName) {
        this.mblReleaseWayName = mblReleaseWayName;
    }

    public String getHblIssueTypeName() {
        return hblIssueTypeName;
    }

    public void setHblIssueTypeName(String hblIssueTypeName) {
        this.hblIssueTypeName = hblIssueTypeName;
    }

    public String getHblGetWayName() {
        return hblGetWayName;
    }

    public void setHblGetWayName(String hblGetWayName) {
        this.hblGetWayName = hblGetWayName;
    }

    public String getHblReleaseWayName() {
        return hblReleaseWayName;
    }

    public void setHblReleaseWayName(String hblReleaseWayName) {
        this.hblReleaseWayName = hblReleaseWayName;
    }

    public String getOpName() {
        return opName;
    }

    public void setOpName(String opName) {
        this.opName = opName;
    }

    public String getSalesName() {
        return salesName;
    }

    public void setSalesName(String salesName) {
        this.salesName = salesName;
    }

    public String getSalesAssistantName() {
        return salesAssistantName;
    }

    public void setSalesAssistantName(String salesAssistantName) {
        this.salesAssistantName = salesAssistantName;
    }

    public String getPsaName() {
        return psaName;
    }

    public void setPsaName(String psaName) {
        this.psaName = psaName;
    }

    public Long[] getLineIds() {
        return lineIds;
    }

    public void setLineIds(Long[] lineIds) {
        this.lineIds = lineIds;
    }

    public Date getPsaVerifyFrom() {
        return psaVerifyFrom;
    }

    public void setPsaVerifyFrom(Date psaVerifyFrom) {
        this.psaVerifyFrom = psaVerifyFrom;
    }

    public Date getPsaVerifyTo() {
        return psaVerifyTo;
    }

    public void setPsaVerifyTo(Date psaVerifyTo) {
        this.psaVerifyTo = psaVerifyTo;
    }

    public String getOpAllocation() {
        return opAllocation;
    }

    public void setOpAllocation(String opAllocation) {
        this.opAllocation = opAllocation;
    }

    public Integer getIsPsaVerified() {
        return isPsaVerified;
    }

    public void setIsPsaVerified(Integer isPsaVerified) {
        this.isPsaVerified = isPsaVerified;
    }

    public Long getVerifyPsaId() {
        return verifyPsaId;
    }

    public void setVerifyPsaId(Long verifyPsaId) {
        this.verifyPsaId = verifyPsaId;
    }

    public Date getPsaVerifyTime() {
        return psaVerifyTime;
    }

    public void setPsaVerifyTime(Date psaVerifyTime) {
        this.psaVerifyTime = psaVerifyTime;
    }

    public List<Long> getBookingIds() {
        return bookingIds;
    }

    public void setBookingIds(List<Long> bookingIds) {
        this.bookingIds = bookingIds;
    }

    public Long[] getCargoTypeIds() {
        return cargoTypeIds;
    }

    public void setCargoTypeIds(Long[] cargoTypeIds) {
        this.cargoTypeIds = cargoTypeIds;
    }

    public Long[] getCarrierIds() {
        return carrierIds;
    }

    public void setCarrierIds(Long[] carrierIds) {
        this.carrierIds = carrierIds;
    }

    public Long[] getServiceTypeIds() {
        return serviceTypeIds;
    }

    public void setServiceTypeIds(Long[] serviceTypeIds) {
        this.serviceTypeIds = serviceTypeIds;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getReadOnly() {
        return readOnly;
    }

    public void setReadOnly(String readOnly) {
        this.readOnly = readOnly;
    }

    public RsBookingLogisticsTypeBasicInfo getRsBookingLogisticsTypeBasicInfo() {
        return rsBookingLogisticsTypeBasicInfo;
    }

    public void setRsBookingLogisticsTypeBasicInfo(RsBookingLogisticsTypeBasicInfo rsBookingLogisticsTypeBasicInfo) {
        this.rsBookingLogisticsTypeBasicInfo = rsBookingLogisticsTypeBasicInfo;
    }

    public RsBookingPreCarriageBasicInfo getRsBookingPreCarriageBasicInfo() {
        return rsBookingPreCarriageBasicInfo;
    }

    public void setRsBookingPreCarriageBasicInfo(RsBookingPreCarriageBasicInfo rsBookingPreCarriageBasicInfo) {
        this.rsBookingPreCarriageBasicInfo = rsBookingPreCarriageBasicInfo;
    }

    public RsBookingExportDeclarationBasicInfo getRsBookingExportDeclarationBasicInfo() {
        return rsBookingExportDeclarationBasicInfo;
    }

    public void setRsBookingExportDeclarationBasicInfo(RsBookingExportDeclarationBasicInfo rsBookingExportDeclarationBasicInfo) {
        this.rsBookingExportDeclarationBasicInfo = rsBookingExportDeclarationBasicInfo;
    }

    public RsBookingImportClearanceBasicInfo getRsBookingImportClearanceBasicInfo() {
        return rsBookingImportClearanceBasicInfo;
    }

    public void setRsBookingImportClearanceBasicInfo(RsBookingImportClearanceBasicInfo rsBookingImportClearanceBasicInfo) {
        this.rsBookingImportClearanceBasicInfo = rsBookingImportClearanceBasicInfo;
    }

    public void setBookingId(Long bookingId) {
        this.bookingId = bookingId;
    }

    public Long getBookingId() {
        return bookingId;
    }

    public void setOpId(Long opId) {
        this.opId = opId;
    }

    public Long getOpId() {
        return opId;
    }

    public void setNewBookingNo(String newBookingNo) {
        this.newBookingNo = newBookingNo;
    }

    public String getNewBookingNo() {
        return newBookingNo;
    }

    public void setNewBookingTime(Date newBookingTime) {
        this.newBookingTime = newBookingTime;
    }

    public Date getNewBookingTime() {
        return newBookingTime;
    }

    public void setQuotationNo(String quotationNo) {
        this.quotationNo = quotationNo;
    }

    public String getQuotationNo() {
        return quotationNo;
    }

    public void setQuotationDate(Date quotationDate) {
        this.quotationDate = quotationDate;
    }

    public Date getQuotationDate() {
        return quotationDate;
    }

    public void setSalesId(Long salesId) {
        this.salesId = salesId;
    }

    public Long getSalesId() {
        return salesId;
    }

    public void setSalesAssistantId(Long salesAssistantId) {
        this.salesAssistantId = salesAssistantId;
    }

    public Long getSalesAssistantId() {
        return salesAssistantId;
    }

    public void setSalesObserverId(Long salesObserverId) {
        this.salesObserverId = salesObserverId;
    }

    public Long getSalesObserverId() {
        return salesObserverId;
    }

    public void setUrgencyDegree(String urgencyDegree) {
        this.urgencyDegree = urgencyDegree;
    }

    public String getUrgencyDegree() {
        return urgencyDegree;
    }

    public void setPaymentTypeId(Long paymentTypeId) {
        this.paymentTypeId = paymentTypeId;
    }

    public Long getPaymentTypeId() {
        return paymentTypeId;
    }

    public void setReleaseTypeId(Long releaseTypeId) {
        this.releaseTypeId = releaseTypeId;
    }

    public Long getReleaseTypeId() {
        return releaseTypeId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientRoleId(Long clientRoleId) {
        this.clientRoleId = clientRoleId;
    }

    public Long getClientRoleId() {
        return clientRoleId;
    }

    public void setClientContactor(String clientContactor) {
        this.clientContactor = clientContactor;
    }

    public String getClientContactor() {
        return clientContactor;
    }

    public void setClientContactorTel(String clientContactorTel) {
        this.clientContactorTel = clientContactorTel;
    }

    public String getClientContactorTel() {
        return clientContactorTel;
    }

    public void setClientContactorEmail(String clientContactorEmail) {
        this.clientContactorEmail = clientContactorEmail;
    }

    public String getClientContactorEmail() {
        return clientContactorEmail;
    }

    public void setRelationClientIds(String relationClientIds) {
        this.relationClientIds = relationClientIds;
    }

    public String getRelationClientIds() {
        return relationClientIds;
    }

    public void setImpExpTypeId(Integer impExpTypeId) {
        this.impExpTypeId = impExpTypeId;
    }

    public Integer getImpExpTypeId() {
        return impExpTypeId;
    }

    public void setTradingPaymentChannelId(Long tradingPaymentChannelId) {
        this.tradingPaymentChannelId = tradingPaymentChannelId;
    }

    public Long getTradingPaymentChannelId() {
        return tradingPaymentChannelId;
    }

    public void setTradingTermsId(Long tradingTermsId) {
        this.tradingTermsId = tradingTermsId;
    }

    public Long getTradingTermsId() {
        return tradingTermsId;
    }

    public void setLogisticsTermsId(Long logisticsTermsId) {
        this.logisticsTermsId = logisticsTermsId;
    }

    public Long getLogisticsTermsId() {
        return logisticsTermsId;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setGoodsNameSummary(String goodsNameSummary) {
        this.goodsNameSummary = goodsNameSummary;
    }

    public String getGoodsNameSummary() {
        return goodsNameSummary;
    }

    public void setPackageQuantity(Integer packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public Integer getPackageQuantity() {
        return packageQuantity;
    }

    public void setGrossWeight(BigDecimal grossWeight) {
        this.grossWeight = grossWeight;
    }

    public BigDecimal getGrossWeight() {
        return grossWeight;
    }

    public void setWeightUnitId(Long weightUnitId) {
        this.weightUnitId = weightUnitId;
    }

    public Long getWeightUnitId() {
        return weightUnitId;
    }

    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    public BigDecimal getVolume() {
        return volume;
    }

    public void setVolumeUnitId(Long volumeUnitId) {
        this.volumeUnitId = volumeUnitId;
    }

    public Long getVolumeUnitId() {
        return volumeUnitId;
    }

    public void setGoodsValue(BigDecimal goodsValue) {
        this.goodsValue = goodsValue;
    }

    public BigDecimal getGoodsValue() {
        return goodsValue;
    }

    public void setGoodsCurrencyId(Long goodsCurrencyId) {
        this.goodsCurrencyId = goodsCurrencyId;
    }

    public Long getGoodsCurrencyId() {
        return goodsCurrencyId;
    }

    public void setMaxWeight(BigDecimal maxWeight) {
        this.maxWeight = maxWeight;
    }

    public BigDecimal getMaxWeight() {
        return maxWeight;
    }

    public void setRevenueTons(String revenueTons) {
        this.revenueTons = revenueTons;
    }

    public String getRevenueTons() {
        return revenueTons;
    }

    public void setLogisticsTypeId(Long logisticsTypeId) {
        this.logisticsTypeId = logisticsTypeId;
    }

    public Long getLogisticsTypeId() {
        return logisticsTypeId;
    }

    public void setSchedule(String schedule) {
        this.schedule = schedule;
    }

    public String getSchedule() {
        return schedule;
    }

    public void setValidDate(String validDate) {
        this.validDate = validDate;
    }

    public String getValidDate() {
        return validDate;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblIssueTypeId(Long mblIssueTypeId) {
        this.mblIssueTypeId = mblIssueTypeId;
    }

    public Long getMblIssueTypeId() {
        return mblIssueTypeId;
    }

    public void setMblGetWayId(Long mblGetWayId) {
        this.mblGetWayId = mblGetWayId;
    }

    public Long getMblGetWayId() {
        return mblGetWayId;
    }

    public void setMblReleaseWayId(Long mblReleaseWayId) {
        this.mblReleaseWayId = mblReleaseWayId;
    }

    public Long getMblReleaseWayId() {
        return mblReleaseWayId;
    }

    public void setHblNoList(String hblNoList) {
        this.hblNoList = hblNoList;
    }

    public String getHblNoList() {
        return hblNoList;
    }

    public void setHblIssueTypeId(Long hblIssueTypeId) {
        this.hblIssueTypeId = hblIssueTypeId;
    }

    public Long getHblIssueTypeId() {
        return hblIssueTypeId;
    }

    public void setHblGetWayId(Long hblGetWayId) {
        this.hblGetWayId = hblGetWayId;
    }

    public Long getHblGetWayId() {
        return hblGetWayId;
    }

    public void setHblReleaseWayId(Long hblReleaseWayId) {
        this.hblReleaseWayId = hblReleaseWayId;
    }

    public Long getHblReleaseWayId() {
        return hblReleaseWayId;
    }

    public void setQuotationSummary(String quotationSummary) {
        this.quotationSummary = quotationSummary;
    }

    public String getQuotationSummary() {
        return quotationSummary;
    }

    public void setNewBookingRemark(String newBookingRemark) {
        this.newBookingRemark = newBookingRemark;
    }

    public String getNewBookingRemark() {
        return newBookingRemark;
    }

    public void setInquiryNotice(String inquiryNotice) {
        this.inquiryNotice = inquiryNotice;
    }

    public String getInquiryNotice() {
        return inquiryNotice;
    }

    public void setInquiryInnerRemark(String inquiryInnerRemark) {
        this.inquiryInnerRemark = inquiryInnerRemark;
    }

    public String getInquiryInnerRemark() {
        return inquiryInnerRemark;
    }

    public void setOpLeaderRemark(String opLeaderRemark) {
        this.opLeaderRemark = opLeaderRemark;
    }

    public String getOpLeaderRemark() {
        return opLeaderRemark;
    }

    public void setOpInnerRemark(String opInnerRemark) {
        this.opInnerRemark = opInnerRemark;
    }

    public String getOpInnerRemark() {
        return opInnerRemark;
    }

    public void setAgreementTypeId(Long agreementTypeId) {
        this.agreementTypeId = agreementTypeId;
    }

    public Long getAgreementTypeId() {
        return agreementTypeId;
    }

    public void setAgreementNo(String agreementNo) {
        this.agreementNo = agreementNo;
    }

    public String getAgreementNo() {
        return agreementNo;
    }

    public Integer getIsMblNeeded() {
        return isMblNeeded;
    }

    public void setIsMblNeeded(Integer isMblNeeded) {
        this.isMblNeeded = isMblNeeded;
    }

    public Integer getIsUnderAgreementMbl() {
        return isUnderAgreementMbl;
    }

    public void setIsUnderAgreementMbl(Integer isUnderAgreementMbl) {
        this.isUnderAgreementMbl = isUnderAgreementMbl;
    }

    public Integer getIsCustomsIntransitMbl() {
        return isCustomsIntransitMbl;
    }

    public void setIsCustomsIntransitMbl(Integer isCustomsIntransitMbl) {
        this.isCustomsIntransitMbl = isCustomsIntransitMbl;
    }

    public Integer getIsSwitchMbl() {
        return isSwitchMbl;
    }

    public void setIsSwitchMbl(Integer isSwitchMbl) {
        this.isSwitchMbl = isSwitchMbl;
    }

    public Integer getIsDividedMbl() {
        return isDividedMbl;
    }

    public void setIsDividedMbl(Integer isDividedMbl) {
        this.isDividedMbl = isDividedMbl;
    }

    public Integer getIsHblNeeded() {
        return isHblNeeded;
    }

    public void setIsHblNeeded(Integer isHblNeeded) {
        this.isHblNeeded = isHblNeeded;
    }

    public Integer getIsUnderAgreementHbl() {
        return isUnderAgreementHbl;
    }

    public void setIsUnderAgreementHbl(Integer isUnderAgreementHbl) {
        this.isUnderAgreementHbl = isUnderAgreementHbl;
    }

    public Integer getIsCustomsIntransitHbl() {
        return isCustomsIntransitHbl;
    }

    public void setIsCustomsIntransitHbl(Integer isCustomsIntransitHbl) {
        this.isCustomsIntransitHbl = isCustomsIntransitHbl;
    }

    public Integer getIsSwitchHbl() {
        return isSwitchHbl;
    }

    public void setIsSwitchHbl(Integer isSwitchHbl) {
        this.isSwitchHbl = isSwitchHbl;
    }

    public Integer getIsDividedHbl() {
        return isDividedHbl;
    }

    public void setIsDividedHbl(Integer isDividedHbl) {
        this.isDividedHbl = isDividedHbl;
    }
}
