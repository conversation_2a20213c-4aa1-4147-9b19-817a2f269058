package com.rich.system.service.impl;

import com.rich.common.annotation.DataScope;
import com.rich.common.constant.CacheConstants;
import com.rich.common.constant.UserConstants;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.*;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.core.text.Convert;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SearchUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.StringUtils;
import com.rich.system.domain.*;
import com.rich.system.mapper.*;
import com.rich.system.service.ExtCompanyService;
import org.apache.commons.lang3.ArrayUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.util.*;
import java.util.stream.Collectors;

import static com.rich.common.utils.PageUtils.startPage;

/**
 * 公司Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-08-18
 */
@Service
public class ExtCompanyServiceImpl implements ExtCompanyService {

    @Autowired
    private ExtCompanyMapper extCompanyMapper;

    @Autowired
    private ExtStaffMapper extStaffMapper;

    @Autowired
    private MidRsStaffRoleMapper midRsStaffRoleMapper;

    @Autowired
    private MidCompanyRoleMapper midCompanyRoleMapper;

    @Autowired
    private MidCargoTypeMapper midCargoTypeMapper;

    @Autowired
    private MidServiceTypeMapper midServiceTypeMapper;

    @Autowired
    private MidLocationDepartureMapper midLocationDepartureMapper;

    @Autowired
    private MidLineDepartureMapper midLineDepartureMapper;

    @Autowired
    private MidLocationDestinationMapper midLocationDestinationMapper;

    @Autowired
    private MidLineDestinationMapper midLineDestinationMapper;

    @Autowired
    private MidOrganizationMapper midOrganizationMapper;

    @Autowired
    private MidCarrierMapper midCarrierMapper;

    @Autowired
    private BasDistLocationMapper basDistLocationMapper;

    @Autowired
    private BasDistLineMapper basDistLineMapper;

    @Autowired
    private BasCarrierMapper basCarrierMapper;

    @Autowired
    private BasCompanyRoleMapper basCompanyRoleMapper;

    @Autowired
    private BasDistServiceTypeMapper basDistServiceTypeMapper;

    @Autowired
    private BasDistCargoTypeMapper basDistCargoTypeMapper;

    @Autowired
    private BasAccountMapper basAccountMapper;

    @Autowired
    private RsCommunicationMapper rsCommunicationMapper;

    @Autowired
    private RsAgreementRecordMapper rsAgreementRecordMapper;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisCacheImpl RedisCache;


    /**
     * 查询公司
     *
     * @param companyId 公司主键
     * @return 公司
     */
    @Override
    @DataScope(deptAlias = "ud", subDeptAlias = "sd", userAlias = "u", subUserAlias = "s")
    public ExtCompany selectExtCompanyByCompanyId(Long companyId) {
        return extCompanyMapper.selectExtCompanyByCompanyId(companyId);
    }

    /**
     * 查询公司列表
     *
     * @param extCompany 公司
     * @return 公司
     */
    /*@Override
    @DataScope(deptAlias = "ud", subDeptAlias = "sd", userAlias = "u", subUserAlias = "s")
    public List<ExtCompany> selectExtCompanyList(ExtCompany extCompany) {
        //判断是否有搜索项
        boolean search = extCompany.getCargoTypeIds() != null
                || extCompany.getServiceTypeIds() != null
                || extCompany.getLocationDepartureIds() != null
                || extCompany.getLineDepartureIds() != null
                || extCompany.getLocationDestinationIds() != null
                || extCompany.getLineDestinationIds() != null
                || extCompany.getCarrierIds() != null
                || extCompany.getRoleIds() != null;
        List<Long> qc = null;
        // 根据条件获取搜索结果
        List<List<Long>> qcList = queryCompanys(extCompany);
        if (!qcList.isEmpty()) {
            //如果结果存在则获取筛选结果并赋值给qc
            qc = SearchUtils.getLongs(qcList);
        }
        //获取商务分配的公司列表
        List<List<Long>> tList = businessToCompany(extCompany);
        List<Long> b2c = new ArrayList<>();
        boolean chief = SearchUtils.isChief();
        // 如果是管理员或者部门管理
        if (SecurityUtils.isAdmin(SecurityUtils.getLoginUser().getUser().getRole()) || Boolean.TRUE.equals(chief)) {
            extCompany.setCompanyIds(getExtCompanies(qc));
        } else {
            if (extCompany.getRoleTypeId() != null && extCompany.getRoleTypeId() == 1L) {
                List<Long> list = SearchUtils.getLongs(tList);
                if (search) {
                    for (Long b : list) {
                        assert qc != null;
                        for (Long q : qc) {
                            if (b.equals(q)) {
                                b2c.add(q);
                            }
                        }
                    }
                } else {
                    b2c = list;
                }
                if (!tList.isEmpty()) {
                    extCompany.setCompanyIds(getExtCompanies(b2c));
                }
            } else {
                extCompany.setCompanyIds(getExtCompanies(qc));
            }
        }
        // 只查询权限范围内的客户
        extCompany.setUserId(SecurityUtils.getUserId());
        startPage();
        return extCompanyMapper.queryCompany(extCompany);
    }*/
    @Override
    @DataScope(deptAlias = "ud", subDeptAlias = "sd", userAlias = "u", subUserAlias = "s")
    public List<ExtCompany> selectExtCompanyList(ExtCompany extCompany) {
        //判断是否有搜索项
        boolean search = extCompany.getCargoTypeIds() != null
                || extCompany.getServiceTypeIds() != null
                || extCompany.getLocationDepartureIds() != null
                || extCompany.getLineDepartureIds() != null
                || extCompany.getLocationDestinationIds() != null
                || extCompany.getLineDestinationIds() != null
                || extCompany.getCarrierIds() != null
                || extCompany.getRoleIds() != null;
        List<Long> qc = null;
        //获取搜索结果
        List<List<Long>> qcList = queryCompanys(extCompany);
        if (qcList != null && !qcList.isEmpty()) {
            //如果结果存在则获取筛选结果并赋值给qc
            qc = SearchUtils.getLongs(qcList);
        }
        //获取商务分配的公司列表
        List<List<Long>> tList = businessToCompany(extCompany);
        List<Long> b2c = new ArrayList<>();
        boolean chief = SearchUtils.isChief();
        // 管理员
        if (SecurityUtils.isAdmin(SecurityUtils.getLoginUser().getUser().getRole()) || Boolean.TRUE.equals(chief) || SecurityUtils.getDeptId().equals(106L)) {
            extCompany.setCompanyIds(getExtCompanies(qc));
        } else {
            // (以前用roleTypeId,1是供应商)
            if (extCompany.getRoleSupplier() != null && extCompany.getRoleSupplier().equals("1")) {
                List<Long> list = SearchUtils.getLongs(tList);
                if (search) {
                    for (Long b : list) {
                        for (Long q : qc) {
                            if (b.equals(q)) {
                                b2c.add(q);
                            }
                        }
                    }
                } else {
                    b2c = list;
                }
                if (!tList.isEmpty()) {
                    extCompany.setCompanyIds(getExtCompanies(b2c));
                }
            } else {
                extCompany.setCompanyIds(getExtCompanies(qc));
            }
        }
        extCompany.setUserId(SecurityUtils.getUserId());
        startPage();
        return extCompanyMapper.queryCompany(extCompany);
    }


    @Override
    public List<ExtCompany> exportExtCompanyList(ExtCompany extCompany) {
        //判断是否有搜索项
        boolean search = extCompany.getCargoTypeIds() != null
                || extCompany.getServiceTypeIds() != null
                || extCompany.getLocationDepartureIds() != null
                || extCompany.getLineDepartureIds() != null
                || extCompany.getLocationDestinationIds() != null
                || extCompany.getLineDestinationIds() != null
                || extCompany.getCarrierIds() != null
                || extCompany.getRoleIds() != null;
        List<Long> qc = null;
        //获取搜索结果
        List<List<Long>> qcList = queryCompanys(extCompany);
        if (qcList != null && !qcList.isEmpty()) {
            //如果结果存在则获取筛选结果并赋值给qc
            qc = SearchUtils.getLongs(qcList);
        }
        //获取商务分配的公司列表
        List<List<Long>> tList = businessToCompany(extCompany);
        List<Long> b2c = new ArrayList<>();
        boolean chief = SearchUtils.isChief();
        // 管理员
        if (SecurityUtils.isAdmin(SecurityUtils.getLoginUser().getUser().getRole()) || Boolean.TRUE.equals(chief) || SecurityUtils.getDeptId().equals(106L)) {
            extCompany.setCompanyIds(getExtCompanies(qc));
        } else {
            // (以前用roleTypeId,1是供应商)
            if (extCompany.getRoleSupplier() != null && extCompany.getRoleSupplier().equals("1")) {
                List<Long> list = SearchUtils.getLongs(tList);
                if (search) {
                    for (Long b : list) {
                        for (Long q : qc) {
                            if (b.equals(q)) {
                                b2c.add(q);
                            }
                        }
                    }
                } else {
                    b2c = list;
                }
                if (!tList.isEmpty()) {
                    extCompany.setCompanyIds(getExtCompanies(b2c));
                }
            } else {
                extCompany.setCompanyIds(getExtCompanies(qc));
            }
        }
        extCompany.setUserId(SecurityUtils.getUserId());
        return extCompanyMapper.queryCompany(extCompany);
    }

    @Override
    public List<ExtCompany> selectList(ExtCompany extCompany) {
        extCompany.setDeleteStatus(0);
        extCompany.setUserId(SecurityUtils.getUserId());
        return extCompanyMapper.queryCompany(extCompany);
    }

    private Long[] getExtCompanies(List<Long> list) {
        Long[] l;
        if (list != null && !list.isEmpty()) {
            l = list.toArray(new Long[0]);
        } else {
            l = null;
        }
        return l;
    }

    public List<List<Long>> queryCompanys(ExtCompany extCompany) {
        List<List<Long>> lists = new ArrayList<>();
        // 目的地
        List<BasDistLocation> basDistLocations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        if (basDistLocations == null) {
            RedisCache.location();
            basDistLocations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        }
        // 目的航线
        List<BasDistLine> basDistLines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        if (basDistLines == null) {
            RedisCache.line();
            basDistLines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        }
        // 启运地
        List<MidLocationDeparture> midLocationDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyLocationDeparture");
        if (midLocationDepartures == null) {
            RedisCache.locationDeparture("company", "companyLocationDeparture");
            midLocationDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyLocationDeparture");
        }
        // 启运航线
        List<MidLineDeparture> midLineDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyLineDeparture");
        if (midLineDepartures == null) {
            RedisCache.lineDeparture("company", "companyLineDeparture");
            midLineDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyLineDeparture");
        }
        // 目的港
        List<MidLocationDestination> midLocationDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyLocationDestination");
        if (midLocationDestinations == null) {
            RedisCache.locationDestination("company", "companyLocationDestination");
            midLocationDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyLocationDestination");
        }
        // 公司目的航线
        List<MidLineDestination> midLineDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyLineDestination");
        if (midLineDestinations == null) {
            RedisCache.lineDestination("company", "companyLineDestination");
            midLineDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyLineDestination");
        }
        // 公司承运人
        List<MidCarrier> midCarriers = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyCarriers");
        if (midCarriers == null) {
            RedisCache.midCarrier("company", "companyCarriers");
            midCarriers = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyCarriers");
        }
        // 公司角色
        List<MidCompanyRole> midCompanyRoles = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyRole");
        if (midCompanyRoles == null) {
            RedisCache.companyRole("companyRole");
            midCompanyRoles = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyRole");
        }
        // 公司承运货物类型
        List<MidCargoType> midCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyCargoType");
        List<BasDistCargoType> basDistCargoTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        if (midCargoTypes == null) {
            RedisCache.midCargoType("company", "companyCargoType");
            midCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyCargoType");
        }
        if (basDistCargoTypes == null) {
            RedisCache.cargoType();
            basDistCargoTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        }
        List<MidServiceType> midServiceTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyServiceType");
        List<BasDistServiceType> basDistServiceTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
        if (midServiceTypes == null) {
            RedisCache.midServiceType("company", "companyServiceType");
            midServiceTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyServiceType");
        }
        if (basDistServiceTypes == null) {
            RedisCache.serviceType();
            basDistServiceTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
        }

        // 搜索的承运人id数组不为空
        if (extCompany.getCarrierIds() != null) {
            Set<Long> set = new HashSet<>();
            //获取选择的承运人
            // 从所有的承运人中选出用户选择的
            for (MidCarrier d : midCarriers) {
                if (ArrayUtils.contains(extCompany.getCarrierIds(), d.getCarrierId())) {
                    set.add(d.getBelongId());
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (!list.isEmpty()) {
                lists.add(list);
            } else {
                return null;
            }
        }
        // 搜索的公司角色id数组不为空
        if (extCompany.getRoleIds() != null) {
            Set<Long> set = new HashSet<>();
            //获取选择的公司角色
            for (MidCompanyRole d : midCompanyRoles) {
                if (ArrayUtils.contains(extCompany.getRoleIds(), d.getRoleId())) {
                    set.add(d.getCompanyId());
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (!list.isEmpty()) {
                lists.add(list);
            } else {
                return null;
            }
        }
        // 搜索的货物类型不为空而且
        if (extCompany.getCargoTypeIds() != null && !ArrayUtils.contains(extCompany.getCargoTypeIds(), -1L)) {
            Set<Long> set = new HashSet<>();
            Set<Long> c = new HashSet<>();
            //获取货物类型，以及其上级
            for (BasDistCargoType cargoType : basDistCargoTypes) {
                String[] ancestors = cargoType.getAncestors().split(",");
                if (SearchUtils.existSame(ancestors, extCompany.getCargoTypeIds())) {
                    c.add(cargoType.getCargoTypeId());
                }
                if (ArrayUtils.contains(extCompany.getCargoTypeIds(), cargoType.getCargoTypeId())) {
                    c.add(cargoType.getCargoTypeId());
                    for (String a : ancestors) {
                        c.add(Convert.toLong(a));
                    }
                }
            }
            //获取公司绑定的货物类型
            for (MidCargoType midCargoType : midCargoTypes) {
                if (c.contains(midCargoType.getCargoTypeId())) {
                    set.add(midCargoType.getBelongId());
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (!list.isEmpty()) {
                lists.add(list);
            } else {
                return null;
            }
        }
        // 获取公司的货运类型
        if (extCompany.getServiceTypeIds() != null && !ArrayUtils.contains(extCompany.getServiceTypeIds(), -1L)) {
            Set<Long> set = new HashSet<>();
            Set<Long> c = new HashSet<>();
            //获取服务类型，以及其上级
            for (BasDistServiceType serviceType : basDistServiceTypes) {
                if (ArrayUtils.contains(extCompany.getServiceTypeIds(), serviceType.getServiceTypeId())) {
                    c.add(serviceType.getServiceTypeId());
                }
            }
            //获取公司绑定的服务类型
            for (MidServiceType serviceType : midServiceTypes) {
                if (c.contains(serviceType.getServiceTypeId())) {
                    set.add(serviceType.getBelongId());
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (!list.isEmpty()) {
                lists.add(list);
            } else {
                return null;
            }
        }
        // 对应启运区域
        if (extCompany.getLocationDepartureIds() != null && !ArrayUtils.contains(extCompany.getLocationDepartureIds(), -1L)) {
            Set<Long> set = new HashSet<>();
            Set<Long> olines = new HashSet<>();
            Set<Long> olocations = new HashSet<>();
            //获取启运港，以及其上级
            for (BasDistLocation location : basDistLocations) {
                String[] ancestors = location.getAncestors().split(",");
                if (ArrayUtils.contains(extCompany.getLocationDepartureIds(), location.getLocationId())) {
                    olocations.add(location.getLocationId());
                    for (String a : ancestors) {
                        olocations.add(Convert.toLong(a));
                    }
                    //获取区域绑定的航线，以及其上级
                    for (BasDistLine line : basDistLines) {
                        String[] lineAncestors = line.getAncestors().split(",");
                        if (location.getLineId() != null && line.getLineId().equals(location.getLineId())) {
                            olines.add(location.getLineId());
                            for (String a : lineAncestors) {
                                olines.add(Convert.toLong(a));
                            }
                        }
                        if (location.getLineId() != null && ArrayUtils.contains(lineAncestors, location.getLineId().toString())) {
                            olines.add(line.getLineId());
                        }
                    }
                }
                if (SearchUtils.existSame(ancestors, extCompany.getLocationDepartureIds())) {
                    olocations.add(location.getLocationId());
                }
            }
            //获取公司绑定的启运港
            for (MidLocationDeparture locationDeparture : midLocationDepartures) {
                if (olocations.contains(locationDeparture.getLocationId())) {
                    set.add(locationDeparture.getBelongId());
                }
            }
            //获取公司绑定的启运航线
            for (MidLineDeparture lineDeparture : midLineDepartures) {
                if (olines.contains(lineDeparture.getLineId())) {
                    set.add(lineDeparture.getBelongId());
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (!list.isEmpty()) {
                lists.add(list);
            } else {
                return null;
            }
        }
        // 启运区域
        if (extCompany.getLineDepartureIds() != null && ArrayUtils.contains(extCompany.getLineDepartureIds(), -1L)) {
            Set<Long> set = new HashSet<>();
            Set<Long> olocations = new HashSet<>();
            Set<Long> olines = new HashSet<>();
            //获取启运航线极其上级
            for (BasDistLine line : basDistLines) {
                String[] ancestors = line.getAncestors().split(",");
                if (ArrayUtils.contains(extCompany.getLineDepartureIds(), line.getLineId())) {
                    olines.add(line.getLineId());
                    for (String a : ancestors) {
                        olines.add(Convert.toLong(a));
                    }
                }
                if (SearchUtils.existSame(ancestors, extCompany.getLineDepartureIds())) {
                    olines.add(line.getLineId());
                }
            }
            //获取启运航线绑定的区域
            for (BasDistLocation location : basDistLocations) {
                if (location.getLineId() != null && ArrayUtils.contains(olines.toArray(), location.getLineId())) {
                    String[] ancestors = location.getAncestors().split(",");
                    olocations.add(location.getLocationId());
                    for (String a : ancestors) {
                        olocations.add(Convert.toLong(a));
                    }
                }
            }
            //获取公司绑定的启运港
            for (MidLocationDeparture locationDeparture : midLocationDepartures) {
                if (olocations.contains(locationDeparture.getLocationId())) {
                    set.add(locationDeparture.getBelongId());
                }
            }
            //获取公司绑定的启运航线
            for (MidLineDeparture lineDeparture : midLineDepartures) {
                if (olines.contains(lineDeparture.getLineId())) {
                    set.add(lineDeparture.getBelongId());
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (!list.isEmpty()) {
                lists.add(list);
            } else {
                return null;
            }
        }
        // 目的区域 TODO 目的地与目的航线的交叉选择处理
        if (extCompany.getLocationDestinationIds() != null && !ArrayUtils.contains(extCompany.getLocationDestinationIds(), -1L)) {
            Set<Long> set = new HashSet<>();
            Set<Long> olines = new HashSet<>();
            Set<Long> olocations = new HashSet<>();

            for (BasDistLocation location : basDistLocations) {
                String[] ancestors = location.getAncestors().split(",");
                if (ArrayUtils.contains(extCompany.getLocationDestinationIds(), location.getLocationId())) {
                    olocations.add(location.getLocationId());
                    for (String a : ancestors) {
                        olocations.add(Convert.toLong(a));
                    }
                    for (BasDistLine line : basDistLines) {
                        String[] lineAncestors = line.getAncestors().split(",");
                        if (line.getLineId().equals(location.getLineId())) {
                            olines.add(location.getLineId());
                            for (String a : lineAncestors) {
                                olines.add(Convert.toLong(a));
                            }
                        }
                        if (location.getLineId() != null && ArrayUtils.contains(lineAncestors, location.getLineId().toString())) {
                            olines.add(line.getLineId());
                        }
                    }
                }
                if (SearchUtils.existSame(ancestors, extCompany.getLocationDestinationIds())) {
                    olocations.add(location.getLocationId());
                }
            }
            for (MidLocationDestination locationDestination : midLocationDestinations) {
                if (olocations.contains(locationDestination.getLocationId())) {
                    set.add(locationDestination.getBelongId());
                }
            }
            for (MidLineDestination lineDestination : midLineDestinations) {
                if (olines.contains(lineDestination.getLineId())) {
                    set.add(lineDestination.getBelongId());
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (list.size() > 0) {
                lists.add(list);
            } else {
                return null;
            }
        }
        // 目的航线
        if (extCompany.getLineDestinationIds() != null && !ArrayUtils.contains(extCompany.getLineDepartureIds(), -1L)) {
            Set<Long> set = new HashSet<>();
            Set<Long> olocations = new HashSet<>();
            Set<Long> olines = new HashSet<>();
            //获取目的航线极其上级
            for (BasDistLine line : basDistLines) {
                String[] ancestors = line.getAncestors().split(",");
                if (ArrayUtils.contains(extCompany.getLineDestinationIds(), line.getLineId())) {
                    olines.add(line.getLineId());
                    for (String a : ancestors) {
                        olines.add(Convert.toLong(a));
                    }
                }
                if (SearchUtils.existSame(ancestors, extCompany.getLineDestinationIds())) {
                    olines.add(line.getLineId());
                }
            }
            //获取目的航线绑定的区域
            for (BasDistLocation location : basDistLocations) {
                if (location.getLineId() != null && ArrayUtils.contains(olines.toArray(), location.getLineId())) {
                    String[] ancestors = location.getAncestors().split(",");
                    olocations.add(location.getLocationId());
                    for (String a : ancestors) {
                        olocations.add(Convert.toLong(a));
                    }
                }
            }
            //获取公司绑定的目的港
            for (MidLocationDestination locationDestination : midLocationDestinations) {
                if (olocations.contains(locationDestination.getLocationId())) {
                    set.add(locationDestination.getBelongId());
                }
            }
            //获取公司绑定的目的航线
            for (MidLineDestination lineDestination : midLineDestinations) {
                if (olines.contains(lineDestination.getLineId())) {
                    set.add(lineDestination.getBelongId());
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (!list.isEmpty()) {
                lists.add(list);
            } else {
                return null;
            }
        }

        //返回搜索集合
        return lists;
    }

    public List<List<Long>> businessToCompany(ExtCompany extCompany) {
        List<List<Long>> lists = new ArrayList<>();
        //获取数据缓存
        List<BasDistLocation> basDistLocations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        if (basDistLocations == null) {
            RedisCache.location();
            basDistLocations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        }
        List<BasDistLine> basDistLines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        if (basDistLines == null) {
            RedisCache.line();
            basDistLines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        }
        List<BasDistCargoType> basDistCargoTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        if (basDistCargoTypes == null) {
            RedisCache.cargoType();
            basDistCargoTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        }
        List<BasDistServiceType> basDistServiceTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
        if (basDistServiceTypes == null) {
            RedisCache.serviceType();
            basDistServiceTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
        }
        List<MidLocationDeparture> midCompanyLocationDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyLocationDeparture");
        if (midCompanyLocationDepartures == null) {
            RedisCache.locationDeparture("company", "companyLocationDeparture");
            midCompanyLocationDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyLocationDeparture");
        }
        List<MidLocationDeparture> midRoleLocationDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleLocationDeparture");
        if (midRoleLocationDepartures == null) {
            RedisCache.locationDeparture("role", "roleLocationDeparture");
            midRoleLocationDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleLocationDeparture");
        }
        List<MidLineDeparture> midCompanyLineDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyLineDeparture");
        if (midCompanyLineDepartures == null) {
            RedisCache.lineDeparture("company", "companyLineDeparture");
            midCompanyLineDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyLineDeparture");
        }
        List<MidLineDeparture> midRoleLineDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleLineDeparture");
        if (midRoleLineDepartures == null) {
            RedisCache.lineDeparture("role", "roleLineDeparture");
            midRoleLineDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleLineDeparture");
        }
        List<MidLocationDestination> midCompanyLocationDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyLocationDestination");
        if (midCompanyLocationDestinations == null) {
            RedisCache.locationDestination("company", "companyLocationDestination");
            midCompanyLocationDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyLocationDestination");
        }
        List<MidLocationDestination> midRoleLocationDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleLocationDestination");
        if (midRoleLocationDestinations == null) {
            RedisCache.locationDestination("role", "roleLocationDestination");
            midRoleLocationDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleLocationDestination");
        }
        List<MidLineDestination> midCompanyLineDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyLineDestination");
        if (midCompanyLineDestinations == null) {
            RedisCache.lineDestination("company", "companyLineDestination");
            midCompanyLineDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyLineDestination");
        }
        List<MidLineDestination> midRoleLineDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleLineDestination");
        if (midRoleLineDestinations == null) {
            RedisCache.lineDestination("role", "roleLineDestination");
            midRoleLineDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleLineDestination");
        }
        List<MidCargoType> midCompanyCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyCargoType");
        if (midCompanyCargoTypes == null) {
            RedisCache.midCargoType("company", "companyCargoType");
            midCompanyCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyCargoType");
        }
        List<MidCargoType> midRoleCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleCargoType");
        if (midRoleCargoTypes == null) {
            RedisCache.midCargoType("role", "roleCargoType");
            midRoleCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleCargoType");
        }
        List<MidServiceType> midCompanyServiceTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyServiceType");
        if (midCompanyServiceTypes == null) {
            RedisCache.midServiceType("company", "companyServiceType");
            midCompanyServiceTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyServiceType");
        }
        List<MidServiceType> midRoleServiceTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleServiceType");
        if (midRoleServiceTypes == null) {
            RedisCache.midServiceType("role", "roleServiceType");
            midRoleServiceTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleServiceType");
        }

        //初始化
        List<MidRsStaffRole> distribute;
        MidRsStaffRole midRsStaffRole = new MidRsStaffRole();
        Set<Long> locationDepartures = new HashSet<>();
        Set<Long> lineDepartures = new HashSet<>();
        Set<Long> locationDestinations = new HashSet<>();
        Set<Long> lineDestinations = new HashSet<>();
        Set<Long> cargoTypes = new HashSet<>();
        Set<Long> serviceTypes = new HashSet<>();
        //获取是否主管判断
        boolean chief = SearchUtils.isChief();
        //如果是主管同时搜索的员工不为空
        if (chief && extCompany.getQueryBStaffId() != null) {
            midRsStaffRole.setStaffId(extCompany.getQueryBStaffId());
        } else {
            midRsStaffRole.setStaffId(SecurityUtils.getUserId());
        }
        //获取用户绑定的角色
        distribute = midRsStaffRoleMapper.selectMidRsStaffRoleList(midRsStaffRole);
        //获取用户绑定的所有角色分配的区域，航线，服务类型，货物类型
        for (MidRsStaffRole r : distribute) {
            for (MidLocationDeparture m : midRoleLocationDepartures) {
                if (m.getBelongId().equals(r.getRoleId())) {
                    locationDepartures.add(m.getLocationId());
                }
            }
            for (MidLocationDestination m : midRoleLocationDestinations) {
                if (m.getBelongId().equals(r.getRoleId())) {
                    locationDestinations.add(m.getLocationId());
                }
            }
            for (MidLineDeparture m : midRoleLineDepartures) {
                if (m.getBelongId().equals(r.getRoleId())) {
                    lineDepartures.add(m.getLineId());
                }
            }
            for (MidLineDestination m : midRoleLineDestinations) {
                if (m.getBelongId().equals(r.getRoleId())) {
                    lineDestinations.add(m.getLineId());
                }
            }
            for (MidServiceType m : midRoleServiceTypes) {
                if (m.getBelongId().equals(r.getRoleId())) {
                    serviceTypes.add(m.getServiceTypeId());
                }
            }
            for (MidCargoType m : midRoleCargoTypes) {
                if (m.getBelongId().equals(r.getRoleId())) {
                    cargoTypes.add(m.getCargoTypeId());
                }
            }
        }
        //对角色绑定的启运区域进行搜索
        if (!locationDepartures.isEmpty() && !((extCompany.getLocationDepartureIds() != null && ArrayUtils.contains(extCompany.getLocationDepartureIds(), -1L)) || ArrayUtils.contains(locationDepartures.toArray(), -1L))) {
            Set<Long> set = new HashSet<>();
            Set<Long> olines = new HashSet<>();
            Set<Long> olocations = new HashSet<>();
            for (BasDistLocation location : basDistLocations) {
                String[] ancestors = location.getAncestors().split(",");
                if ((extCompany.getLocationDepartureIds() != null && ArrayUtils.contains(extCompany.getLocationDepartureIds(), location.getLocationId())) || ArrayUtils.contains(locationDepartures.toArray(), location.getLocationId())) {
                    olocations.add(location.getLocationId());
                    for (String a : ancestors) {
                        olocations.add(Convert.toLong(a));
                    }
                    for (BasDistLine line : basDistLines) {
                        String[] lineAncestors = line.getAncestors().split(",");
                        if (line.getLineId().equals(location.getLineId())) {
                            olines.add(location.getLineId());
                            for (String a : lineAncestors) {
                                olines.add(Convert.toLong(a));
                            }
                        }
                        if (ArrayUtils.contains(lineAncestors, location.getLineId().toString())) {
                            olines.add(line.getLineId());
                        }
                    }
                }
                if ((extCompany.getLocationDepartureIds() != null && SearchUtils.existSame(ancestors, extCompany.getLocationDepartureIds())) || SearchUtils.existSame(ancestors, locationDepartures.toArray())) {
                    olocations.add(location.getLocationId());
                }
            }
            for (MidLocationDeparture locationDeparture : midCompanyLocationDepartures) {
                if (olocations.contains(locationDeparture.getLocationId())) {
                    set.add(locationDeparture.getBelongId());
                }
            }
            for (MidLineDeparture lineDeparture : midCompanyLineDepartures) {
                if (olines.contains(lineDeparture.getLineId())) {
                    set.add(lineDeparture.getBelongId());
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (!list.isEmpty()) {
                lists.add(list);
            }
        }
        //对角色绑定的目的区域进行搜索
        if (!locationDestinations.isEmpty() && !((extCompany.getLocationDestinationIds() != null && ArrayUtils.contains(extCompany.getLocationDestinationIds(), -1L)) || ArrayUtils.contains(locationDestinations.toArray(), -1L))) {
            Set<Long> set = new HashSet<>();
            Set<Long> olines = new HashSet<>();
            Set<Long> olocations = new HashSet<>();
            for (BasDistLocation location : basDistLocations) {
                String[] ancestors = location.getAncestors().split(",");
                if ((extCompany.getLocationDestinationIds() != null && ArrayUtils.contains(extCompany.getLocationDestinationIds(), location.getLocationId())) || ArrayUtils.contains(locationDestinations.toArray(), location.getLocationId())) {
                    olocations.add(location.getLocationId());
                    for (String a : ancestors) {
                        olocations.add(Convert.toLong(a));
                    }
                    for (BasDistLine line : basDistLines) {
                        String[] lineAncestors = line.getAncestors().split(",");
                        if (line.getLineId().equals(location.getLineId())) {
                            olines.add(location.getLineId());
                            for (String a : lineAncestors) {
                                olines.add(Convert.toLong(a));
                            }
                        }
                        if (ArrayUtils.contains(lineAncestors, location.getLineId().toString())) {
                            olines.add(line.getLineId());
                        }
                    }
                }
                if ((extCompany.getLocationDestinationIds() != null && SearchUtils.existSame(ancestors, extCompany.getLocationDestinationIds())) || SearchUtils.existSame(ancestors, locationDestinations.toArray())) {
                    olocations.add(location.getLocationId());
                }
            }
            for (MidLocationDestination locationDestination : midCompanyLocationDestinations) {
                if (olocations.contains(locationDestination.getLocationId())) {
                    set.add(locationDestination.getBelongId());
                }
            }
            for (MidLineDestination lineDestination : midCompanyLineDestinations) {
                if (olines.contains(lineDestination.getLineId())) {
                    set.add(lineDestination.getBelongId());
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (!list.isEmpty()) {
                lists.add(list);
            }
        }
        //对角色绑定的目的航线进行搜索
        if (!lineDestinations.isEmpty() && !((extCompany.getLineDepartureIds() != null && ArrayUtils.contains(extCompany.getLineDepartureIds(), -1L)) || ArrayUtils.contains(lineDestinations.toArray(), -1L))) {
            Set<Long> set = new HashSet<>();
            Set<Long> olocations = new HashSet<>();
            Set<Long> olines = new HashSet<>();
            for (BasDistLine line : basDistLines) {
                String[] ancestors = line.getAncestors().split(",");
                if ((extCompany.getLineDepartureIds() != null && ArrayUtils.contains(extCompany.getLineDestinationIds(), line.getLineId())) || ArrayUtils.contains(lineDestinations.toArray(), line.getLineId())) {
                    olines.add(line.getLineId());
                    for (String a : ancestors) {
                        olines.add(Convert.toLong(a));
                    }
                }
                if ((extCompany.getLineDepartureIds() != null && SearchUtils.existSame(ancestors, extCompany.getLineDestinationIds())) || SearchUtils.existSame(ancestors, lineDestinations.toArray())) {
                    olines.add(line.getLineId());
                }
            }
            for (BasDistLocation location : basDistLocations) {
                if (location.getLineId() != null && ArrayUtils.contains(olines.toArray(), location.getLineId())) {
                    String[] ancestors = location.getAncestors().split(",");
                    olocations.add(location.getLocationId());
                    for (String a : ancestors) {
                        olocations.add(Convert.toLong(a));
                    }
                }
            }
            for (MidLocationDestination locationDestination : midCompanyLocationDestinations) {
                if (olocations.contains(locationDestination.getLocationId())) {
                    set.add(locationDestination.getBelongId());
                }
            }
            for (MidLineDestination lineDestination : midCompanyLineDestinations) {
                if (olines.contains(lineDestination.getLineId())) {
                    set.add(lineDestination.getBelongId());
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (list.size() > 0) {
                lists.add(list);
            }
        }
        //对角色绑定的服务类型进行搜索
        if (!serviceTypes.isEmpty() && !((extCompany.getServiceTypeIds() != null && ArrayUtils.contains(extCompany.getServiceTypeIds(), -1L)) || ArrayUtils.contains(serviceTypes.toArray(), -1L))) {
            Set<Long> set = new HashSet<>();
            Set<Long> c = new HashSet<>();
            for (BasDistServiceType serviceType : basDistServiceTypes) {
                String[] ancestors = serviceType.getAncestors() != null ? serviceType.getAncestors().split(",") : ArrayUtils.EMPTY_STRING_ARRAY;
                if ((extCompany.getServiceTypeIds() != null && SearchUtils.existSame(ancestors, extCompany.getServiceTypeIds())) || SearchUtils.existSame(ancestors, serviceTypes.toArray())) {
                    c.add(serviceType.getServiceTypeId());
                }
                if ((extCompany.getServiceTypeIds() != null && ArrayUtils.contains(extCompany.getServiceTypeIds(), serviceType.getServiceTypeId())) || ArrayUtils.contains(serviceTypes.toArray(), serviceType.getServiceTypeId())) {
                    c.add(serviceType.getServiceTypeId());
                    for (String a : ancestors) {
                        c.add(Convert.toLong(a));
                    }
                }
            }
            for (MidServiceType serviceType : midCompanyServiceTypes) {
                if (c.contains(serviceType.getServiceTypeId())) {
                    set.add(serviceType.getBelongId());
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (list.size() > 0) {
                lists.add(list);
            }
        }
        //对角色绑定的货物类型进行搜索
        if (!cargoTypes.isEmpty() && !((extCompany.getCargoTypeIds() != null && ArrayUtils.contains(extCompany.getCargoTypeIds(), -1L)) || ArrayUtils.contains(cargoTypes.toArray(), -1L))) {
            Set<Long> set = new HashSet<>();
            Set<Long> c = new HashSet<>();
            for (BasDistCargoType cargoType : basDistCargoTypes) {
                String[] ancestors = cargoType.getAncestors().split(",");
                if ((extCompany.getCargoTypeIds() != null && SearchUtils.existSame(ancestors, extCompany.getCargoTypeIds())) || SearchUtils.existSame(ancestors, cargoTypes.toArray())) {
                    c.add(cargoType.getCargoTypeId());
                }
                if ((extCompany.getCargoTypeIds() != null && ArrayUtils.contains(extCompany.getCargoTypeIds(), cargoType.getCargoTypeId())) || ArrayUtils.contains(cargoTypes.toArray(), cargoType.getCargoTypeId())) {
                    c.add(cargoType.getCargoTypeId());
                    for (String a : ancestors) {
                        c.add(Convert.toLong(a));
                    }
                }
            }
            for (MidCargoType midCargoType : midCompanyCargoTypes) {
                if (c.contains(midCargoType.getCargoTypeId())) {
                    set.add(midCargoType.getBelongId());
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (!list.isEmpty()) {
                lists.add(list);
            }
        }

        return lists;
    }


    /**
     * 新增公司
     *
     * @param extCompany 公司
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertExtCompany(@NotNull ExtCompany extCompany) {
        extCompany.setCreateBy(SecurityUtils.getUserId());
        extCompany.setCreateTime(DateUtils.getNowDate());
        extCompany.setCompanyTaxCode("RP" + isTaxCodeSame());
        int out = extCompanyMapper.insertExtCompany(extCompany);
        insertRole(extCompany);
        insertCargoType(extCompany);
        insertServiceType(extCompany);
        insertLineDeparture(extCompany);
        insertLocationDeparture(extCompany);
        insertLineDestination(extCompany);
        insertLocationDestination(extCompany);
        insertCarriers(extCompany);
        insertOrganizations(extCompany);

        // 插入主联系人信息
        ExtStaff extStaff = new ExtStaff();
        extStaff.setIsMain("Y");
        extStaff.setSqdCompanyId(extCompany.getCompanyId());
        extStaff.setStaffLocalName(extCompany.getMainStaffOfficialName());
        extStaff.setStaffWechat(extCompany.getStaffWechat());
        extStaff.setStaffQq(extCompany.getStaffQq());
        extStaff.setStaffWhatsapp(extCompany.getStaffWhatsapp());
        extStaff.setStaffOtherContact(extCompany.getStaffOtherContact());
        extStaff.setStaffPhoneNum(extCompany.getStaffMobile());
        extStaff.setStaffEmailEnterprise(extCompany.getStaffEmail());

        extStaffMapper.insertExtStaff(extStaff);

        return out;
    }

    private String isTaxCodeSame() {
        SecureRandom random = new SecureRandom();
        return String.format("%05d", random.nextInt(100000));

    }

    /**
     * 修改公司
     *
     * @param extCompany 公司
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateExtCompany(@NotNull ExtCompany extCompany) {
        midCompanyRoleMapper.deleteMidCompanyRoleByCompanyId(extCompany.getCompanyId());
        insertRole(extCompany);
        midCargoTypeMapper.deleteMidCargoTypeById(extCompany.getCompanyId(), "company");
        insertCargoType(extCompany);
        midServiceTypeMapper.deleteMidServiceTypeById(extCompany.getCompanyId(), "company");
        insertServiceType(extCompany);
        midLineDepartureMapper.deleteMidLineDepartureById(extCompany.getCompanyId(), "company");
        insertLineDeparture(extCompany);
        midLocationDepartureMapper.deleteMidLocationDepartureById(extCompany.getCompanyId(), "company");
        insertLocationDeparture(extCompany);
        midLineDestinationMapper.deleteMidLineDestinationById(extCompany.getCompanyId(), "company");
        insertLineDestination(extCompany);
        midLocationDestinationMapper.deleteMidLocationDestinationById(extCompany.getCompanyId(), "company");
        insertLocationDestination(extCompany);
        midCarrierMapper.deleteMidCarrierById(extCompany.getCompanyId(), "company");
        insertCarriers(extCompany);
        midOrganizationMapper.deleteMidOrganizationById(extCompany.getCompanyId(), "company");
        insertOrganizations(extCompany);
        extCompany.setUpdateBy(SecurityUtils.getUserId());
        extCompany.setUpdateTime(DateUtils.getNowDate());

        // 查看上一次的审核状态
        ExtCompany oldExtCompany = extCompanyMapper.selectExtCompanyByCompanyId(extCompany.getCompanyId());
        // 审核相关
        if (extCompany.getAccConfirmed() != null && extCompany.getAccConfirmed().equals("1") && !Optional.ofNullable(oldExtCompany.getAccConfirmedDate()).isPresent()) {
            extCompany.setAccConfirmedDate(DateUtils.getNowDate());
        }
        if (extCompany.getSalesConfirmed() != null && extCompany.getSalesConfirmed().equals("1") && !Optional.ofNullable(oldExtCompany.getSalesConfirmedDate()).isPresent()) {
            extCompany.setSalesConfirmedDate(DateUtils.getNowDate());
        }
        if (extCompany.getOpConfirmed() != null && extCompany.getOpConfirmed().equals("1") && !Optional.ofNullable(oldExtCompany.getOpConfirmedDate()).isPresent()) {
            extCompany.setOpConfirmedDate(DateUtils.getNowDate());
        }
        if (extCompany.getPsaConfirmed() != null && extCompany.getPsaConfirmed().equals("1") && !Optional.ofNullable(oldExtCompany.getPsaConfirmedDate()).isPresent()) {
            extCompany.setPsaConfirmedDate(DateUtils.getNowDate());
        }

        // 更新主联系人
        ExtStaff searchExtStaff = new ExtStaff();
        searchExtStaff.setIsMain("Y");
        searchExtStaff.setSqdCompanyId(extCompany.getCompanyId());
        List<ExtStaff> extStaffs = extStaffMapper.selectExtStaffList(searchExtStaff);

        ExtStaff extStaff = new ExtStaff();
        extStaff.setIsMain("Y");
        extStaff.setSqdCompanyId(extCompany.getCompanyId());
        extStaff.setStaffShortName(extCompany.getMainStaffOfficialName());
        extStaff.setStaffWechat(extCompany.getStaffWechat());
        extStaff.setStaffWhatsapp(extCompany.getStaffWhatsapp());
        extStaff.setStaffOtherContact(extCompany.getStaffOtherContact());
        extStaff.setStaffQq(extCompany.getStaffQq());
        extStaff.setStaffPhoneNum(extCompany.getStaffMobile());
        extStaff.setStaffEmailEnterprise(extCompany.getStaffEmail());
        if (extStaffs.isEmpty()) {
            extStaffMapper.insertExtStaff(extStaff);
        } else {
            extStaff.setStaffId(extStaffs.get(0).getStaffId());
            extStaffMapper.updateExtStaff(extStaff);
        }
        extCompany.setSqdMainAttn(extCompany.getMainStaffOfficialName());

        return extCompanyMapper.updateExtCompany(extCompany);
    }

    @Override
    public int justUpdate(ExtCompany extCompany) {
        return extCompanyMapper.updateExtCompany(extCompany);
    }


    /**
     * 批量删除公司
     *
     * @param companyIds 需要删除的公司主键
     * @return 结果
     */
    @Override
    public int deleteExtCompanyByCompanyIds(Long[] companyIds) {
//        midCompanyRoleMapper.deleteMidCompanyRoleByCompanyIds(companyIds);
//        RedisCache.companyRole("companyRole");
//
//        midCargoTypeMapper.deleteMidCargoTypeByIds(companyIds, "company");
//        RedisCache.midCargoType("company", "companyCargoType");
//
//        midServiceTypeMapper.deleteMidServiceTypeByIds(companyIds, "company");
//        RedisCache.midServiceType("company", "companyServiceType");
//
//        midLineDepartureMapper.deleteMidLineDepartureByIds(companyIds, "company");
//        RedisCache.lineDeparture("company", "companyLineDeparture");
//
//        midLocationDepartureMapper.deleteMidLocationDepartureByIds(companyIds, "company");
//        RedisCache.locationDeparture("company", "companyLocationDeparture");
//
//        midLineDestinationMapper.deleteMidLineDestinationByIds(companyIds, "company");
//        RedisCache.lineDestination("company", "companyLineDestination");
//
//        midLocationDestinationMapper.deleteMidLocationDestinationByIds(companyIds, "company");
//        RedisCache.locationDestination("company", "companyLocationDestination");
//
//        midCarrierMapper.deleteMidCarrierByIds(companyIds, "company");
//        RedisCache.midCarrier("company", "companyCarrier");
//
//        midOrganizationMapper.deleteMidOrganizationByIds(companyIds, "company");
//        RedisCache.organization("company", "companyOrganization");


        int out = extCompanyMapper.setDeleteExtCompanyByCompanyIds(companyIds);
        return out;
    }

    /**
     * 删除公司信息
     *
     * @param id 公司主键
     * @return 结果
     */
    @Override
    public int deleteExtCompanyByCompanyId(Long id) {
        midCompanyRoleMapper.deleteMidCompanyRoleByCompanyId(id);
        RedisCache.companyRole("companyRole");

        midCargoTypeMapper.deleteMidCargoTypeById(id, "company");
        RedisCache.midCargoType("company", "companyCargoType");

        midServiceTypeMapper.deleteMidServiceTypeById(id, "company");
        RedisCache.midServiceType("company", "companyServiceType");

        midLineDepartureMapper.deleteMidLineDepartureById(id, "company");
        RedisCache.lineDeparture("company", "companyLineDeparture");

        midLocationDepartureMapper.deleteMidLocationDepartureById(id, "company");
        RedisCache.locationDeparture("company", "companyLocationDeparture");

        midLineDestinationMapper.deleteMidLineDestinationById(id, "company");
        RedisCache.lineDestination("company", "companyLineDestination");

        midLocationDestinationMapper.deleteMidLocationDestinationById(id, "company");
        RedisCache.locationDestination("company", "companyLocationDestination");

        midCarrierMapper.deleteMidCarrierById(id, "company");
        RedisCache.midCarrier("company", "companyCarriers");

        midOrganizationMapper.deleteMidOrganizationById(id, "company");
        RedisCache.organization("company", "companyOrganization");

        return extCompanyMapper.deleteExtCompanyByCompanyId(id);
    }

    @Override
    public List<Long> selectCompanyRoles(Long companyId) {
        List<MidCompanyRole> roles = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyRole");
        if (roles == null) {
            RedisCache.companyRole("companyRole");
            roles = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyRole");
        }
        List<Long> r = new ArrayList<>();
        //获取公司绑定的角色类型
        for (MidCompanyRole companyRole : roles) {
            if (companyRole.getCompanyId().equals(companyId)) {
                r.add(companyRole.getRoleId());
            }
        }
        return r;
    }

    @Override
    public List<Long> selectCompanyServiceTypes(Long companyId) {
        List<MidServiceType> serviceTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyServiceType");
        if (serviceTypes == null) {
            RedisCache.midServiceType("company", "companyServiceType");
            serviceTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyServiceType");
        }
        List<Long> r = new ArrayList<>();
        //获取公司绑定的服务类型
        for (MidServiceType serviceType : serviceTypes) {
            if (serviceType.getBelongId().equals(companyId)) {
                r.add(serviceType.getServiceTypeId());
            }
        }
        return r;
    }

    @Override
    public List<Long> selectCompanyCargoTypes(Long companyId) {
        List<MidCargoType> cargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyCargoType");
        if (cargoTypes == null) {
            RedisCache.midCargoType("company", "companyCargoType");
            cargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyCargoType");
        }
        List<Long> r = new ArrayList<>();
        //获取公司绑定的货物类型
        for (MidCargoType cargoType : cargoTypes) {
            if (cargoType.getBelongId().equals(companyId)) {
                r.add(cargoType.getCargoTypeId());
            }
        }
        return r;
    }

    @Override
    public List<Long> selectCompanyLineDeparture(Long companyId) {
        List<MidLineDeparture> lineDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyLineDeparture");
        if (lineDepartures == null) {
            RedisCache.lineDeparture("company", "companyLineDeparture");
            lineDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyLineDeparture");
        }
        List<Long> r = new ArrayList<>();
        //获取公司绑定的启运航线
        for (MidLineDeparture lineDeparture : lineDepartures) {
            if (lineDeparture.getBelongId().equals(companyId)) {
                r.add(lineDeparture.getLineId());
            }
        }
        return r;
    }

    @Override
    public List<Long> selectCompanyLocationDeparture(Long companyId) {
        List<MidLocationDeparture> locationDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyLocationDeparture");
        if (locationDepartures == null) {
            RedisCache.locationDeparture("company", "companyLocationDeparture");
            locationDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyLocationDeparture");
        }
        List<Long> r = new ArrayList<>();
        //获取公司绑定的启运区域
        for (MidLocationDeparture locationDeparture : locationDepartures) {
            if (locationDeparture.getBelongId().equals(companyId)) {
                r.add(locationDeparture.getLocationId());
            }
        }
        return r;
    }

    @Override
    public List<Long> selectCompanyLineDestination(Long companyId) {
        List<MidLineDestination> lineDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyLineDestination");
        if (lineDestinations == null) {
            RedisCache.lineDestination("company", "companyLineDestination");
            lineDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyLineDestination");
        }
        List<Long> r = new ArrayList<>();
        //获取公司绑定的目的航线
        for (MidLineDestination lineDestination : lineDestinations) {
            if (lineDestination.getBelongId().equals(companyId)) {
                r.add(lineDestination.getLineId());
            }
        }
        return r;
    }

    @Override
    public List<Long> selectCompanyLocationDestination(Long companyId) {
        List<MidLocationDestination> locationDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyLocationDestination");
        if (locationDestinations == null) {
            RedisCache.locationDestination("company", "companyLocationDestination");
            locationDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyLocationDestination");
        }
        List<Long> r = new ArrayList<>();
        //获取公司绑定的目的区域
        for (MidLocationDestination locationDestination : locationDestinations) {
            if (locationDestination.getBelongId().equals(companyId)) {
                r.add(locationDestination.getLocationId());
            }
        }
        return r;
    }

    @Override
    public List<Long> selectCompanyOrganizations(Long companyId) {
        List<MidOrganization> organizations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyOrganization");
        if (organizations == null) {
            RedisCache.organization("company", "companyOrganization");
            organizations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyOrganization");
        }
        List<Long> r = new ArrayList<>();
        //获取公司绑定的组织
        for (MidOrganization organization : organizations) {
            if (organization.getBelongId().equals(companyId)) {
                r.add(organization.getOrganizationId());
            }
        }
        return r;
    }

    @Override
    public List<Long> selectCompanyCarriers(Long companyId) {
        List<MidCarrier> carriers = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyCarriers");
        if (carriers == null) {
            RedisCache.midCarrier("company", "companyCarriers");
            carriers = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "companyCarriers");
        }
        List<Long> r = new ArrayList<>();
        //获取公司绑定的承运人
        for (MidCarrier carrier : carriers) {
            if (carrier.getBelongId().equals(companyId)) {
                r.add(carrier.getCarrierId());
            }
        }
        return r;
    }

    /**
     * 查询公司名是否唯一
     *
     * @param companyShortName
     * @param companyEnShortName
     * @return
     */
    @Override
    public ExtCompany checkCompanyShortNameUnique(String companyShortName, String companyEnShortName) {
        return extCompanyMapper.checkCompanyShortNameUnique(companyShortName, companyEnShortName);
    }

    /**
     * 按照公司类型查询公司名是否唯一
     *
     * @param companyShortName
     * @param companyEnShortName
     * @param roleType
     * @return
     */
    @Override
    public ExtCompany checkCompanyShortNameUnique(String companyShortName, String companyEnShortName, String roleType) {
        if (roleType.equals("role_rich")) {
            return extCompanyMapper.checkCompanyShortNameUniqueByRoleRich(companyShortName, companyEnShortName);
        }
        if (roleType.equals("role_client")) {
            return extCompanyMapper.checkCompanyShortNameUniqueByRoleClient(companyShortName, companyEnShortName);
        }
        if (roleType.equals("role_supplier")) {
            return extCompanyMapper.checkCompanyShortNameUniqueByRoleSupplier(companyShortName, companyEnShortName);
        }
        if (roleType.equals("role_support")) {
            return extCompanyMapper.checkCompanyShortNameUniqueByRoleSupport(companyShortName, companyEnShortName);
        }
        return extCompanyMapper.checkCompanyShortNameUnique(companyShortName, companyEnShortName);
    }

    @Override
    public ExtCompany getDelCompany(Long companyId) {
        return extCompanyMapper.getDelCompany(companyId);
    }

    /*
     *
     公司中文名唯一性判断
     *
     * 不唯一返回true
     *
     */
    @Override
    public boolean checkCompanyLocalNameUnique(String companyLocalName, Long roleTypeId, Long companyId) {
        int count = extCompanyMapper.checkCompanyLocalNameUnique(companyLocalName, roleTypeId, companyId);
        if (count > 0) {
            return !UserConstants.NOT_UNIQUE;
        }
        return !UserConstants.UNIQUE;
    }

    @Override
    public boolean checkCompanyLocalNameUnique(String companyLocalName, Long roleTypeId, Long companyId, String roleType) {
        if (roleType.equals("role_rich")) {
            int count = extCompanyMapper.checkCompanyLocalNameUniqueByRoleRich(companyLocalName, roleTypeId, companyId);
            if (count > 0) {
                return !UserConstants.NOT_UNIQUE;
            }
        }
        if (roleType.equals("role_client")) {
            int count = extCompanyMapper.checkCompanyLocalNameUniqueByRoleClient(companyLocalName, roleTypeId, companyId);
            if (count > 0) {
                return !UserConstants.NOT_UNIQUE;
            }
        }
        if (roleType.equals("role_supplier")) {
            int count = extCompanyMapper.checkCompanyLocalNameUniqueByRoleSupplier(companyLocalName, roleTypeId, companyId);
            if (count > 0) {
                return !UserConstants.NOT_UNIQUE;
            }
        }
        if (roleType.equals("role_support")) {
            int count = extCompanyMapper.checkCompanyLocalNameUniqueByRoleSupport(companyLocalName, roleTypeId, companyId);
            if (count > 0) {
                return !UserConstants.NOT_UNIQUE;
            }
        }
        int count = extCompanyMapper.checkCompanyLocalNameUnique(companyLocalName, roleTypeId, companyId);
        if (count > 0) {
            return !UserConstants.NOT_UNIQUE;
        }
        return !UserConstants.UNIQUE;
    }

    /*
     *
     公司英文名唯一性判断
     *
     */
    @Override
    public boolean checkCompanyEnNameUnique(String companyEnName, Long roleTypeId, Long companyId) {
        int count = extCompanyMapper.checkCompanyEnNameUnique(companyEnName, roleTypeId, companyId);
        if (count > 0) {
            return !UserConstants.NOT_UNIQUE;
        }
        return !UserConstants.UNIQUE;
    }

    @Override
    public boolean checkCompanyEnNameUnique(String companyEnName, Long roleTypeId, Long companyId, String roleType) {
        if (roleType.equals("role_rich")) {
            int count = extCompanyMapper.checkCompanyEnNameUniqueByRoleRich(companyEnName, roleTypeId, companyId);
            if (count > 0) {
                return !UserConstants.NOT_UNIQUE;
            }
        }
        if (roleType.equals("role_client")) {
            int count = extCompanyMapper.checkCompanyEnNameUniqueByRoleClient(companyEnName, roleTypeId, companyId);
            if (count > 0) {
                return !UserConstants.NOT_UNIQUE;
            }
        }
        if (roleType.equals("role_supplier")) {
            int count = extCompanyMapper.checkCompanyEnNameUniqueByRoleSupplier(companyEnName, roleTypeId, companyId);
            if (count > 0) {
                return !UserConstants.NOT_UNIQUE;
            }
        }
        if (roleType.equals("role_support")) {
            int count = extCompanyMapper.checkCompanyEnNameUniqueByRoleSupport(companyEnName, roleTypeId, companyId);
            if (count > 0) {
                return !UserConstants.NOT_UNIQUE;
            }
        }
        int count = extCompanyMapper.checkCompanyEnNameUnique(companyEnName, roleTypeId, companyId);
        if (count > 0) {
            return !UserConstants.NOT_UNIQUE;
        }
        return !UserConstants.UNIQUE;
    }

    @Override
    public List<ExtStaff> selectExtStaffListByCompanyId(Long companyId) {
        ExtStaff extStaff = new ExtStaff();
        extStaff.setSqdCompanyId(companyId);
        return extStaffMapper.selectExtStaffList(extStaff);
    }

    /*
     *
     储存公司角色
     *
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertRole(@NotNull ExtCompany extCompany) {
        Long[] roles = extCompany.getRoleIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidCompanyRole> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidCompanyRole midCompanyRole = new MidCompanyRole();
                midCompanyRole.setCompanyId(extCompany.getCompanyId());
                midCompanyRole.setRoleId(r);
                list.add(midCompanyRole);
            }
            midCompanyRoleMapper.batchCompanyRole(list);
        }
        RedisCache.companyRole("companyRole");
    }

    /*
     *
     储存货物类型
     *
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertCargoType(@NotNull ExtCompany extCompany) {
        Long[] roles = extCompany.getCargoTypeIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidCargoType> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidCargoType MidCargoType = new MidCargoType();
                MidCargoType.setBelongId(extCompany.getCompanyId());
                MidCargoType.setBelongTo("company");
                MidCargoType.setCargoTypeId(r);
                list.add(MidCargoType);
            }
            midCargoTypeMapper.batchCargoType(list);
        }
        RedisCache.midCargoType("company", "companyCargoType");
    }

    /*
     *
     储存服务类型
     *
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertServiceType(@NotNull ExtCompany extCompany) {
        Long[] roles = extCompany.getServiceTypeIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidServiceType> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidServiceType MidServiceType = new MidServiceType();
                MidServiceType.setBelongId(extCompany.getCompanyId());
                MidServiceType.setServiceTypeId(r);
                MidServiceType.setBelongTo("company");
                list.add(MidServiceType);
            }
            midServiceTypeMapper.batchServiceType(list);
        }
        RedisCache.midServiceType("company", "companyServiceType");
    }

    /*
     *
     储存启运航线
     *
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertLineDeparture(@NotNull ExtCompany extCompany) {
        Long[] roles = extCompany.getLineDepartureIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidLineDeparture> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidLineDeparture MidLineDeparture = new MidLineDeparture();
                MidLineDeparture.setBelongId(extCompany.getCompanyId());
                MidLineDeparture.setLineId(r);
                MidLineDeparture.setBelongTo("company");
                list.add(MidLineDeparture);
            }
            midLineDepartureMapper.batchLD(list);
        }
        RedisCache.lineDeparture("company", "companyLineDeparture");
    }

    /*
         *
         储存启运区域
         *
         */
    @Transactional(rollbackFor = Exception.class)
    public void insertLocationDeparture(@NotNull ExtCompany extCompany) {
        Long[] roles = extCompany.getLocationDepartureIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidLocationDeparture> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidLocationDeparture MidLocationDeparture = new MidLocationDeparture();
                MidLocationDeparture.setBelongId(extCompany.getCompanyId());
                MidLocationDeparture.setLocationId(r);
                MidLocationDeparture.setBelongTo("company");
                list.add(MidLocationDeparture);
            }
            midLocationDepartureMapper.batchLD(list);
        }
        RedisCache.locationDeparture("company", "companyLocationDeparture");
    }

    /*
     *
     储存承运人
     *
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertCarriers(@NotNull ExtCompany extCompany) {
        Long[] roles = extCompany.getCarrierIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidCarrier> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidCarrier midCarrier = new MidCarrier();
                midCarrier.setBelongId(extCompany.getCompanyId());
                midCarrier.setCarrierId(r);
                midCarrier.setBelongTo("company");
                list.add(midCarrier);
            }
            midCarrierMapper.batchCarrier(list);
        }
        RedisCache.midCarrier("company", "companyCarriers");
    }

    /*
     *
     储存组织
     *
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertOrganizations(@NotNull ExtCompany extCompany) {
        Long[] roles = extCompany.getOrganizationIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidOrganization> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidOrganization midOrganization = new MidOrganization();
                midOrganization.setBelongId(extCompany.getCompanyId());
                midOrganization.setOrganizationId(r);
                midOrganization.setBelongTo("company");
                list.add(midOrganization);
            }
            midOrganizationMapper.batchOrganization(list);
        }
        RedisCache.organization("company", "companyOrganization");
    }

    /*
     *
     储存目的区域
     *
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertLocationDestination(@NotNull ExtCompany extCompany) {
        Long[] roles = extCompany.getLocationDestinationIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidLocationDestination> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidLocationDestination MidLocationDestination = new MidLocationDestination();
                MidLocationDestination.setBelongId(extCompany.getCompanyId());
                MidLocationDestination.setLocationId(r);
                MidLocationDestination.setBelongTo("company");
                list.add(MidLocationDestination);
            }
            midLocationDestinationMapper.batchLD(list);
        }
        RedisCache.locationDestination("company", "companyLocationDestination");
    }

    /*
     *
     储存目的航线
     *
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertLineDestination(@NotNull ExtCompany extCompany) {
        Long[] roles = extCompany.getLineDestinationIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidLineDestination> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidLineDestination MidLineDestination = new MidLineDestination();
                MidLineDestination.setBelongId(extCompany.getCompanyId());
                MidLineDestination.setLineId(r);
                MidLineDestination.setBelongTo("company");
                list.add(MidLineDestination);
            }
            midLineDestinationMapper.batchLD(list);
        }
        RedisCache.lineDestination("company", "companyLineDestination");
    }

    /*
     *
     上传公司列表
     *
     */
    @Override
    public void upload(List<UploadCompany> userList) {
        List<BasDistLocation> locations = basDistLocationMapper.selectBasDistLocationList(new BasDistLocation());
        List<BasDistLine> lines = basDistLineMapper.selectBasDistLineList(new BasDistLine());
        List<BasCarrier> carriers = basCarrierMapper.selectBasCarrierList(new BasCarrier());
        List<BasCompanyRole> roles = basCompanyRoleMapper.selectBasCompanyRoleList(new BasCompanyRole());
        List<BasDistServiceType> basDistServiceTypes = basDistServiceTypeMapper.selectBasDistServiceTypeList(new BasDistServiceType());
        List<BasDistCargoType> basDistCargoTypes = basDistCargoTypeMapper.selectBasDistCargoTypeList(new BasDistCargoType());
        for (UploadCompany a : userList) {
            ExtCompany extCompany = new ExtCompany();
            extCompany.setCompanyShortName(a.getShortName().replace(" ", ""));
            extCompany.setCompanyLocalName(a.getLocalName().replace(" ", ""));
            extCompany.setRemark(a.getRemark());
            extCompany.setRoleTypeId(1L);
            Long locationId = null;
            Set<Long> line = new HashSet<>();
            Set<Long> location = new HashSet<>();
            Set<Long> carrier = new HashSet<>();
            Set<Long> serviceType = new HashSet<>();
            Set<Long> role = new HashSet<>();
            Set<Long> cargo = new HashSet<>();
            String[] strings = a.getAdvantage().split("、");
            for (BasDistLocation l : locations) {
                if (l.getLocationLocalName().equals(a.getLocation())) {
                    locationId = l.getLocationId();
                }
                for (String s : strings) {
                    if (l.getLocationLocalName().equals(s) || l.getLocationEnName().equals(s)) {
                        location.add(l.getLocationId());
                    }
                }
            }
            if (locationId != null) {
                extCompany.setLocationId(locationId);
            }
            if (location.size() != 0) {
                Long[] inputLocation = location.toArray(new Long[0]);
                extCompany.setLocationDestinationIds(inputLocation);
            }
            for (BasDistLine l : lines) {
                if (l.getLineLocalName().equals(a.getLine())) {
                    line.add(l.getLineId());
                }
                for (String s : strings) {
                    if (l.getLineLocalName().equals(s) || l.getLineEnName().equals(s)) {
                        line.add(l.getLineId());
                    }
                }
            }
            if (line.size() != 0) {
                Long[] inputLine = line.toArray(new Long[0]);
                extCompany.setLineDestinationIds(inputLine);
            }
            String[] carriers1 = a.getCarrier().split("/");
            for (BasCarrier c : carriers) {
                for (String cc : carriers1) {
                    if (c.getCarrierId().toString().equals(cc)) {
                        carrier.add(c.getCarrierId());
                    }
                }
            }
            Long[] inputCarrier = carrier.toArray(new Long[0]);
            if (inputCarrier.length != 0) {
                extCompany.setCarrierIds(inputCarrier);
            }
            for (BasDistServiceType s : basDistServiceTypes) {
                if (s.getServiceLocalName().equals(a.getServiceType())) {
                    serviceType.add(s.getServiceTypeId());
                }
            }
            extCompany.setServiceTypeIds(serviceType.toArray(new Long[0]));
            for (BasCompanyRole r : roles) {
                if (r.getRoleLocalName().equals(a.getRole())) {
                    role.add(r.getRoleId());
                }
            }
            extCompany.setRoleIds(role.toArray(new Long[0]));
            for (BasDistCargoType c : basDistCargoTypes) {
                if (c.getCargoTypeLocalName().equals(a.getCargoType())) {
                    cargo.add(c.getCargoTypeId());
                }
            }
            extCompany.setCargoTypeIds(cargo.toArray(new Long[0]));
            ExtCompany company = extCompanyMapper.selectExtCompanyByNames(extCompany);
            if (company != null) {
                Set<Long> output = new HashSet<>();
                if (extCompany.getLocationDestinationIds() != null) {
                    output.addAll(Arrays.asList(extCompany.getLocationDestinationIds()));
                }
                if (company.getLocationDestinationIds() != null) {
                    output.addAll(Arrays.asList(company.getLocationDestinationIds()));
                }
                company.setLocationDestinationIds(output.toArray(new Long[0]));
                Set<Long> outputl = new HashSet<>();
                if (extCompany.getLineDestinationIds() != null) {
                    outputl.addAll(Arrays.asList(extCompany.getLineDestinationIds()));
                }
                if (company.getLineDestinationIds() != null) {
                    outputl.addAll(Arrays.asList(company.getLineDestinationIds()));
                }
                company.setLineDestinationIds(outputl.toArray(new Long[0]));
                Set<Long> outputc = new HashSet<>();
                if (extCompany.getCarrierIds() != null) {
                    outputc.addAll(Arrays.asList(extCompany.getCarrierIds()));
                }
                if (company.getCarrierIds() != null) {
                    outputc.addAll(Arrays.asList(company.getCarrierIds()));
                }
                company.setCarrierIds(outputc.toArray(new Long[0]));
                StringBuilder remark = new StringBuilder();
                if (company.getRemark() != null) {
                    remark.append(company.getRemark());
                }
                if (a.getRemark() != null) {
                    remark.append(a.getRemark());
                }
                company.setRemark(String.valueOf(remark));
                updateExtCompany(company);
                Long id = company.getCompanyId();
                if (!a.getContactor1().equals("")) {
                    ExtStaff extStaff1 = new ExtStaff();
                    extStaff1.setSqdCompanyId(id);
                    extStaff1.setIsMain("N");
                    extStaff1.setStaffLocalName(a.getLocalName1());
                    extStaff1.setStaffShortName(a.getShortName1());
                    extStaff1.setStaffEnName(a.getEnName1());
                    extStaff1.setStaffPhoneNum(a.getPhone1());
                    extStaff1.setStaffWechat(a.getWechat1());
                    extStaff1.setStaffQq(a.getQq1());
                    extStaff1.setStaffEmailEnterprise(a.getEmail1());
                    extStaff1.setLocationId(locationId);
                    extStaffMapper.insertExtStaff(extStaff1);
                }
                if (!a.getContactor2().equals("")) {
                    ExtStaff extStaff2 = new ExtStaff();
                    extStaff2.setSqdCompanyId(id);
                    extStaff2.setIsMain("N");
                    extStaff2.setStaffLocalName(a.getLocalName2());
                    extStaff2.setStaffShortName(a.getShortName2());
                    extStaff2.setStaffEnName(a.getEnName2());
                    extStaff2.setStaffPhoneNum(a.getPhone2());
                    extStaff2.setStaffWechat(a.getWechat2());
                    extStaff2.setStaffQq(a.getQq2());
                    extStaff2.setStaffEmailEnterprise(a.getEmail2());
                    extStaff2.setLocationId(locationId);
                    extStaffMapper.insertExtStaff(extStaff2);
                }
                if (!a.getContactor3().equals("")) {
                    ExtStaff extStaff3 = new ExtStaff();
                    extStaff3.setSqdCompanyId(id);
                    extStaff3.setIsMain("N");
                    extStaff3.setStaffLocalName(a.getLocalName3());
                    extStaff3.setStaffShortName(a.getShortName3());
                    extStaff3.setStaffEnName(a.getEnName3());
                    extStaff3.setStaffPhoneNum(a.getPhone3());
                    extStaff3.setStaffWechat(a.getWechat3());
                    extStaff3.setStaffQq(a.getQq3());
                    extStaff3.setStaffEmailEnterprise(a.getEmail3());
                    extStaff3.setLocationId(locationId);
                    extStaffMapper.insertExtStaff(extStaff3);
                }
            } else {
                insertExtCompany(extCompany);
                if (!a.getContactor1().equals("")) {
                    ExtStaff extStaff1 = new ExtStaff();
                    extStaff1.setSqdCompanyId(extCompany.getCompanyId());
                    extStaff1.setIsMain("Y");
                    extStaff1.setStaffLocalName(a.getLocalName1());
                    extStaff1.setStaffShortName(a.getShortName1());
                    extStaff1.setStaffEnName(a.getEnName1());
                    extStaff1.setStaffPhoneNum(a.getPhone1());
                    extStaff1.setStaffWechat(a.getWechat1());
                    extStaff1.setStaffQq(a.getQq1());
                    extStaff1.setStaffEmailEnterprise(a.getEmail1());
                    extStaff1.setLocationId(locationId);
                    extStaffMapper.insertExtStaff(extStaff1);
                }
                if (!a.getContactor2().equals("")) {
                    ExtStaff extStaff2 = new ExtStaff();
                    extStaff2.setSqdCompanyId(extCompany.getCompanyId());
                    extStaff2.setIsMain("N");
                    extStaff2.setStaffLocalName(a.getLocalName2());
                    extStaff2.setStaffShortName(a.getShortName2());
                    extStaff2.setStaffEnName(a.getEnName2());
                    extStaff2.setStaffPhoneNum(a.getPhone2());
                    extStaff2.setStaffWechat(a.getWechat2());
                    extStaff2.setStaffQq(a.getQq2());
                    extStaff2.setStaffEmailEnterprise(a.getEmail2());
                    extStaff2.setLocationId(locationId);
                    extStaffMapper.insertExtStaff(extStaff2);
                }
                if (!a.getContactor3().equals("")) {
                    ExtStaff extStaff3 = new ExtStaff();
                    extStaff3.setSqdCompanyId(extCompany.getCompanyId());
                    extStaff3.setIsMain("N");
                    extStaff3.setStaffLocalName(a.getLocalName3());
                    extStaff3.setStaffShortName(a.getShortName3());
                    extStaff3.setStaffEnName(a.getEnName3());
                    extStaff3.setStaffPhoneNum(a.getPhone3());
                    extStaff3.setStaffWechat(a.getWechat3());
                    extStaff3.setStaffQq(a.getQq3());
                    extStaff3.setStaffEmailEnterprise(a.getEmail3());
                    extStaff3.setLocationId(locationId);
                    extStaffMapper.insertExtStaff(extStaff3);
                }
            }
        }
    }

    /*
     *
     合并公司
     *
     */
    @Override
    public String mergeCompany(Long saveCompanyId, Long delCompanyId) {
        List<BasDistLocation> basDistLocations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        if (basDistLocations == null) {
            RedisCache.location();
            basDistLocations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        }
        List<BasDistLine> basDistLines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        if (basDistLines == null) {
            RedisCache.line();
            basDistLines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        }
        List<BasDistCargoType> basDistCargoTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        if (basDistCargoTypes == null) {
            RedisCache.cargoType();
            basDistCargoTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        }
        List<BasDistServiceType> basDistServiceTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
        if (basDistServiceTypes == null) {
            RedisCache.serviceType();
            basDistServiceTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
        }
        try {
            //创建company实例
            ExtCompany extCompany = new ExtCompany();
            extCompany.setCompanyId(saveCompanyId);
            //把删除的结合到选择留下的company
            Set<Long> saveCompanyRoles = new HashSet<>(selectCompanyRoles(saveCompanyId));
            saveCompanyRoles.addAll(selectCompanyRoles(delCompanyId));
            extCompany.setRoleIds(saveCompanyRoles.toArray(new Long[0]));

            Set<Long> saveServiceTypes = new HashSet<>(selectCompanyServiceTypes(saveCompanyId));
            saveServiceTypes.addAll(selectCompanyServiceTypes(delCompanyId));
            for (BasDistServiceType serviceType : basDistServiceTypes) {
                if (ArrayUtils.contains(saveServiceTypes.toArray(), serviceType.getServiceTypeId())) {
                    saveServiceTypes = saveServiceTypes.stream().filter(l -> !ArrayUtils.contains(serviceType.getAncestors().split(","), l)).collect(Collectors.toSet());
                }
            }
            extCompany.setServiceTypeIds(saveServiceTypes.toArray(new Long[0]));

            Set<Long> saveCarriers = new HashSet<>(selectCompanyCarriers(saveCompanyId));
            saveCarriers.addAll(selectCompanyCarriers(delCompanyId));
            extCompany.setCarrierIds(saveCarriers.toArray(new Long[0]));

            Set<Long> saveCargoTypes = new HashSet<>(selectCompanyCargoTypes(saveCompanyId));
            saveCargoTypes.addAll(selectCompanyCargoTypes(delCompanyId));
            for (BasDistCargoType cargoType : basDistCargoTypes) {
                if (ArrayUtils.contains(saveCargoTypes.toArray(), cargoType.getCargoTypeId())) {
                    saveCargoTypes = saveCargoTypes.stream().filter(l -> !ArrayUtils.contains(cargoType.getAncestors().split(","), l)).collect(Collectors.toSet());
                }
            }
            extCompany.setCargoTypeIds(saveCargoTypes.toArray(new Long[0]));

            Set<Long> saveOrganizations = new HashSet<>(selectCompanyOrganizations(saveCompanyId));
            saveOrganizations.addAll(selectCompanyOrganizations(delCompanyId));
            extCompany.setOrganizationIds(saveOrganizations.toArray(new Long[0]));

            Set<Long> saveLineDeparture = new HashSet<>(selectCompanyLineDeparture(saveCompanyId));
            saveLineDeparture.addAll(selectCompanyLineDeparture(delCompanyId));
            Set<Long> saveLineDestination = new HashSet<>(selectCompanyLineDestination(saveCompanyId));
            saveLineDestination.addAll(selectCompanyLineDestination(delCompanyId));
            for (BasDistLine line : basDistLines) {
                if (ArrayUtils.contains(saveLineDeparture.toArray(), line.getLineId())) {
                    saveLineDeparture = saveLineDeparture.stream().filter(l -> !ArrayUtils.contains(line.getAncestors().split(","), l)).collect(Collectors.toSet());
                }
                if (ArrayUtils.contains(saveLineDestination.toArray(), line.getLineId())) {
                    saveLineDestination = saveLineDestination.stream().filter(l -> !ArrayUtils.contains(line.getAncestors().split(","), l)).collect(Collectors.toSet());
                }
            }
            extCompany.setLineDepartureIds(saveLineDeparture.toArray(new Long[0]));
            extCompany.setLineDestinationIds(saveLineDestination.toArray(new Long[0]));

            Set<Long> saveLocationDeparture = new HashSet<>(selectCompanyLocationDeparture(saveCompanyId));
            saveLocationDeparture.addAll(selectCompanyLocationDeparture(delCompanyId));
            Set<Long> saveLocationDestination = new HashSet<>(selectCompanyLocationDestination(saveCompanyId));
            saveLocationDestination.addAll(selectCompanyLocationDestination(delCompanyId));
            for (BasDistLocation location : basDistLocations) {
                if (ArrayUtils.contains(saveLocationDeparture.toArray(), location.getLineId())) {
                    saveLocationDeparture = saveLocationDeparture.stream().filter(l -> !ArrayUtils.contains(location.getAncestors().split(","), l)).collect(Collectors.toSet());
                }
                if (ArrayUtils.contains(saveLocationDestination.toArray(), location.getLineId())) {
                    saveLocationDestination = saveLocationDestination.stream().filter(l -> !ArrayUtils.contains(location.getAncestors().split(","), l)).collect(Collectors.toSet());
                }
            }
            extCompany.setLocationDepartureIds(saveLocationDeparture.toArray(new Long[0]));
            extCompany.setLocationDestinationIds(saveLocationDestination.toArray(new Long[0]));
            //存入
            midCompanyRoleMapper.deleteMidCompanyRoleByCompanyId(extCompany.getCompanyId());
            insertRole(extCompany);
            midCargoTypeMapper.deleteMidCargoTypeById(extCompany.getCompanyId(), "company");
            insertCargoType(extCompany);
            midServiceTypeMapper.deleteMidServiceTypeById(extCompany.getCompanyId(), "company");
            insertServiceType(extCompany);
            midLineDepartureMapper.deleteMidLineDepartureById(extCompany.getCompanyId(), "company");
            insertLineDeparture(extCompany);
            midLocationDepartureMapper.deleteMidLocationDepartureById(extCompany.getCompanyId(), "company");
            insertLocationDeparture(extCompany);
            midLineDestinationMapper.deleteMidLineDestinationById(extCompany.getCompanyId(), "company");
            insertLineDestination(extCompany);
            midLocationDestinationMapper.deleteMidLocationDestinationById(extCompany.getCompanyId(), "company");
            insertLocationDestination(extCompany);
            midCarrierMapper.deleteMidCarrierById(extCompany.getCompanyId(), "company");
            insertCarriers(extCompany);
            midOrganizationMapper.deleteMidOrganizationById(extCompany.getCompanyId(), "company");
            insertOrganizations(extCompany);
            //员工修改成存留company
            List<ExtStaff> extStaffs = selectExtStaffListByCompanyId(delCompanyId);
            for (ExtStaff extStaff : extStaffs) {
                extStaff.setSqdCompanyId(saveCompanyId);
                extStaffMapper.updateExtStaff(extStaff);
            }
            //账户修改成存留company
            BasAccount basAccount = new BasAccount();
            basAccount.setBelongToCompany(delCompanyId);
            List<BasAccount> accounts = basAccountMapper.selectBasAccountList(basAccount);
            for (BasAccount account : accounts) {
                account.setBelongToCompany(saveCompanyId);
                basAccountMapper.updateBasAccount(account);
            }
            //协议修改成存留company
            RsAgreementRecord rsAgreementRecord = new RsAgreementRecord();
            rsAgreementRecord.setSqdCompanyId(delCompanyId);
            List<RsAgreementRecord> rsAgreementRecords = rsAgreementRecordMapper.selectRsAgreementRecordList(rsAgreementRecord);
            for (RsAgreementRecord agreementRecord : rsAgreementRecords) {
                agreementRecord.setSqdCompanyId(saveCompanyId);
                rsAgreementRecordMapper.updateRsAgreementRecord(agreementRecord);
            }
            //沟通修改成存留company
            RsCommunication rsCommunication = new RsCommunication();
            rsCommunication.setSqdCompanyId(delCompanyId);
            List<RsCommunication> communications = rsCommunicationMapper.selectRsCommunicationList(rsCommunication);
            for (RsCommunication communication : communications) {
                communication.setSqdCompanyId(saveCompanyId);
                rsCommunicationMapper.updateRsCommunication(communication);
            }
            deleteExtCompanyByCompanyId(delCompanyId);
            return "合并完成";
        } catch (Exception e) {
            return "出错了，联系管理员";
        }
    }

    @Override
    public List<ExtCompany> selectExtCompanyListByQuery(ExtCompany extCompany) {
        return extCompanyMapper.selectExtCompanyList(extCompany);
    }

    @Override
    public List<ExtCompany> selectExtCompanyByCompanyIds(Set<Long> set) {
        if (set.isEmpty()) {
            return Collections.emptyList();
        }
        return extCompanyMapper.selectExtCompanyByCompanyIds(set);
    }

    @Override
    public List<ExtCompany> selectExtCompanyListNoPage(ExtCompany extCompany) {
        if (SecurityUtils.getDeptId().equals(102L) || SecurityUtils.isAdmin(SecurityUtils.getLoginUser().getUser().getRole())) {
            extCompany.setPermissionLevel(null);
        }
        extCompany.setUserId(SecurityUtils.getUserId());
        return extCompanyMapper.queryCompany(extCompany);
    }

    @Override
    public ExtCompany checkCompanyNameExit(String companyShortName, String companyEnShortName) {
        return extCompanyMapper.selectExtCompanyByCompanyName(companyShortName, companyEnShortName);
    }

    @Override
    public int deleteExtCompany(ExtCompany extCompany) {
        int i = extCompanyMapper.deleteExtCompanyByRoleType(extCompany);
        ExtCompany company = extCompanyMapper.selectExtCompanyByCompanyId(extCompany.getCompanyId());
        if (company.getRoleRich().equals("0") && company.getRoleClient().equals("0") && company.getRoleSupplier().equals("0") && company.getRoleSupport().equals("0")) {
            // 删除
            extCompanyMapper.deleteExtCompanyByCompanyIds(new Long[]{extCompany.getCompanyId()});
        }
        return i;
    }

    @Override
    public AjaxResult querySame(ExtCompany extCompany) {
        return null;
    }

    @Override
    public List<ExtCompany> selectExtCompanys(ExtCompany extCompany) {
        return extCompanyMapper.selectExtCompanys(extCompany);
    }

    @Override
    public List<ExtCompany> selectExtCompanysByRct(ExtCompany extCompany) {
        return extCompanyMapper.selectExtCompanyByRct(extCompany);
    }
}
