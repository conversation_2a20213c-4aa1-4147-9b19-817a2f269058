package com.rich.system.mapper;

import com.rich.common.core.domain.entity.MidReimburseConfirm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-04-03
 */
@Mapper
public interface MidReimburseConfirmMapper {
    /**
     * 查询【请填写功能名称】
     *
     * @param reimburseId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    MidReimburseConfirm selectMidReimburseConfirmByReimburseId(Long reimburseId);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param midReimburseConfirm 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    List<MidReimburseConfirm> selectMidReimburseConfirmList(MidReimburseConfirm midReimburseConfirm);

    /**
     * 新增【请填写功能名称】
     *
     * @param midReimburseConfirm 【请填写功能名称】
     * @return 结果
     */
    int insertMidReimburseConfirm(MidReimburseConfirm midReimburseConfirm);

    /**
     * 修改【请填写功能名称】
     *
     * @param midReimburseConfirm 【请填写功能名称】
     * @return 结果
     */
    int updateMidReimburseConfirm(MidReimburseConfirm midReimburseConfirm);

    /**
     * 删除【请填写功能名称】
     *
     * @param reimburseId 【请填写功能名称】主键
     * @return 结果
     */
    int deleteMidReimburseConfirmByReimburseId(Long reimburseId);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param reimburseIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteMidReimburseConfirmByReimburseIds(Long[] reimburseIds);

    int deleteMidReimburseConfirmByReimburseType(@Param("reimburseId") Long reimburseId, @Param("type") int type);
}
