package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.BankTransactionRecordDTO;
import com.rich.common.core.domain.entity.RsBankRecord;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.RedisIdGeneratorService;
import com.rich.common.utils.SecurityUtils;
import com.rich.system.mapper.RsBankRecordMapper;
import com.rich.system.service.RsBankRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * 记录公司账户出入账明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-29
 */
@Service
public class RsBankRecordServiceImpl implements RsBankRecordService {
    @Autowired
    private RsBankRecordMapper rsBankRecordMapper;

    @Resource
    private RedisIdGeneratorService redisIdGeneratorService;

    @Autowired
    private RedisCacheImpl RedisCache;

    @Autowired
    private com.rich.common.core.redis.RedisCache redisCache;

    /**
     * 查询记录公司账户出入账明细
     *
     * @param bankRecordId 记录公司账户出入账明细主键
     * @return 记录公司账户出入账明细
     */
    @Override
    public RsBankRecord selectRsBankRecordByBankRecordId(Long bankRecordId) {
        return rsBankRecordMapper.selectRsBankRecordByBankRecordId(bankRecordId);
    }

    /**
     * 查询记录公司账户出入账明细列表
     *
     * @param rsBankRecord 记录公司账户出入账明细
     * @return 记录公司账户出入账明细
     */
    @Override
    public List<RsBankRecord> selectRsBankRecordList(RsBankRecord rsBankRecord) {
        return rsBankRecordMapper.selectRsBankRecordList(rsBankRecord);
    }

    /**
     * 新增记录公司账户出入账明细
     *
     * @param rsBankRecord 记录公司账户出入账明细
     * @return 结果
     */
    @Override
    public RsBankRecord insertRsBankRecord(RsBankRecord rsBankRecord) {
        String writeOff = redisIdGeneratorService.generateUniqueId("write_off");
        String date = DateUtils.dateTime();
        if (rsBankRecord.getIsRecievingOrPaying().equals("0")) {
            rsBankRecord.setBankRecordNo("AR" + date.substring(2) + writeOff);
        } else {
            rsBankRecord.setBankRecordNo("AP" + date.substring(2) + writeOff);
        }
        rsBankRecord.setBankRecordByStaffId(SecurityUtils.getUserId());
        rsBankRecord.setBankRecordUpdateTime(new Date());
        this.clearReceiveOrPay(rsBankRecord);
        int i = rsBankRecordMapper.insertRsBankRecord(rsBankRecord);
        return rsBankRecord;
    }

    /**
     * 修改记录公司账户出入账明细
     *
     * @param rsBankRecord 记录公司账户出入账明细
     * @return 结果
     */
    @Override
    public int updateRsBankRecord(RsBankRecord rsBankRecord) {
        rsBankRecord.setWriteoffStaffId(SecurityUtils.getUserId());
        // 修改了应收应付银行流水的前面两个字母也要更改
        if (rsBankRecord.getIsRecievingOrPaying().equals("0")) {
            rsBankRecord.setBankRecordNo("AR" + rsBankRecord.getBankRecordNo().substring(2));
        } else {
            rsBankRecord.setBankRecordNo("AP" + rsBankRecord.getBankRecordNo().substring(2));
        }
        this.clearReceiveOrPay(rsBankRecord);
        rsBankRecord.setBankRecordUpdateTime(new Date());
        return rsBankRecordMapper.updateRsBankRecord(rsBankRecord);
    }

    public void clearReceiveOrPay(RsBankRecord rsBankRecord) {
        if (rsBankRecord.getIsRecievingOrPaying().equals("1")) {
            // 如果是实付初始化应收
            rsBankRecord.setActualBankRecievedAmount(new BigDecimal(0));
            rsBankRecord.setBankRecievedHandlingFee(new BigDecimal(0));
            rsBankRecord.setSqdBillRecievedWriteoffBalance(new BigDecimal(0));
            rsBankRecord.setBankRecievedExchangeLost(new BigDecimal(0));
            rsBankRecord.setBillRecievedWriteoffAmount(new BigDecimal(0));
            rsBankRecord.setSqdBillRecievedWriteoffBalance(new BigDecimal(0));
        } else {
            rsBankRecord.setActualBankPaidAmount(new BigDecimal(0));
            rsBankRecord.setBankPaidHandlingFee(new BigDecimal(0));
            rsBankRecord.setSqdBillPaidAmount(new BigDecimal(0));
            rsBankRecord.setBillPaidWriteoffAmount(new BigDecimal(0));
            rsBankRecord.setBankPaidExchangeLost(new BigDecimal(0));
            rsBankRecord.setSqdBillPaidWriteoffBalance(new BigDecimal(0));
        }
    }

    /**
     * 修改记录公司账户出入账明细状态
     *
     * @param rsBankRecord 记录公司账户出入账明细
     * @return 记录公司账户出入账明细
     */
    @Override
    public int changeStatus(RsBankRecord rsBankRecord) {
        return rsBankRecordMapper.updateRsBankRecord(rsBankRecord);
    }

    @Override
    public List<RsBankRecord> getAccountFundStatistics(RsBankRecord rsBankRecord) {
        return rsBankRecordMapper.getAccountFundStatistics(rsBankRecord);
    }

    /**
     * 导入流水excel
     *
     * @param bankRecordList
     * @param chargeTypeId
     * @return
     */
    @Override
    public List<BankTransactionRecordDTO> importBankRecord(List<BankTransactionRecordDTO> bankRecordList, Long chargeTypeId) {
        Set<BankTransactionRecordDTO> failList = new HashSet<>();
        for (BankTransactionRecordDTO rsBankRecord : bankRecordList) {
            if (!failList.contains(rsBankRecord)) {
                String writeOff = redisIdGeneratorService.generateUniqueId("write_off");
                String date = DateUtils.dateTime();
                // 涉及到的交易客户如果系统中没有要新增
                RsBankRecord insertRsBankRecord = new RsBankRecord();
                insertRsBankRecord.setBankRecordByStaffId(SecurityUtils.getUserId());
                insertRsBankRecord.setBankRecordUpdateTime(new Date());
                if (rsBankRecord.getExpense() != null) {
                    //支出
                    insertRsBankRecord.setBankRecordNo("AP" + date.substring(2) + writeOff);
                    insertRsBankRecord.setIsRecievingOrPaying("1");
                    insertRsBankRecord.setBankPaidHandlingFee(rsBankRecord.getHandleFee());
                    insertRsBankRecord.setActualBankPaidAmount(rsBankRecord.getExpense());
                } else {
                    insertRsBankRecord.setBankRecordNo("AR" + date.substring(2) + writeOff);
                    insertRsBankRecord.setIsRecievingOrPaying("0");
                    insertRsBankRecord.setBankPaidHandlingFee(rsBankRecord.getHandleFee());
                    insertRsBankRecord.setActualBankRecievedAmount(rsBankRecord.getIncome());
                }
                insertRsBankRecord.setBankAccountCode(rsBankRecord.getBank());
                insertRsBankRecord.setBankCurrencyCode(rsBankRecord.getCurrency());
                insertRsBankRecord.setChargeTypeId(chargeTypeId);

                Long cId = RedisCache.getId("公司", rsBankRecord.getSettlementObject().replace(" ", ""));
                if (cId == null) {
                    rsBankRecord.setSettlementObject("系统无法匹配：" + rsBankRecord.getSettlementObject());
                    failList.add(rsBankRecord);
                } else {
                    insertRsBankRecord.setClearingCompanyId(cId);
                }

                if (!failList.contains(rsBankRecord)) {
                    rsBankRecordMapper.insertRsBankRecord(insertRsBankRecord);
                }
            }
        }
        return new ArrayList<>(failList);
    }

    /**
     * 批量删除记录公司账户出入账明细
     *
     * @param bankRecordIds 需要删除的记录公司账户出入账明细主键
     * @return 结果
     */
    @Override
    public int deleteRsBankRecordByBankRecordIds(Long[] bankRecordIds) {
        return rsBankRecordMapper.deleteRsBankRecordByBankRecordIds(bankRecordIds);
    }

    /**
     * 删除记录公司账户出入账明细信息
     *
     * @param bankRecordId 记录公司账户出入账明细主键
     * @return 结果
     */
    @Override
    public int deleteRsBankRecordByBankRecordId(Long bankRecordId) {
        return rsBankRecordMapper.deleteRsBankRecordByBankRecordId(bankRecordId);
    }
}
