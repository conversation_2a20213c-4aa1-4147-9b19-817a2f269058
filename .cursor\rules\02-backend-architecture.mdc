---
description:
globs:
alwaysApply: false
---
# 后端架构与开发规范

## 项目架构

瑞旗管理系统后端采用标准的分层架构:

```
Controller -> Service -> Mapper -> Entity/DTO
```

## 主要代码规范

### 包结构规范
- `com.rich`: 顶级包名
  - `.web.controller`: 控制器层，处理HTTP请求
  - `.service`: 业务逻辑层，实现业务处理
  - `.mapper`: 数据访问层，与数据库交互
  - `.domain`: 领域模型层，包含实体类与DTO
  - `.common`: 通用工具类与常量

### API接口规范
- 遵循RESTful设计规范
- 统一返回格式：`R<T>` 类，包含 `code`、`msg` 和 `data` 三个字段
- URL 命名采用 kebab-case (短横线)命名法
- 接口版本控制通过 URL 路径区分，例如 `/api/v1/users`

### 控制层规范
- 使用 `@RestController` 注解标识控制器
- 请求映射使用 `@GetMapping`、`@PostMapping` 等注解
- 接口参数校验使用 `@Valid` + Hibernate Validator
- 统一返回 R 对象，错误处理通过全局异常处理器

### 业务层规范
- 接口+实现类模式，如 `UserService` + `UserServiceImpl`
- 事务控制使用 `@Transactional` 注解
- 防止业务逻辑泄露到控制层或数据访问层

### 数据访问层规范
- 使用 MyBatis 注解或 XML 配置
- 简单查询使用注解，复杂查询使用 XML
- 分页查询使用 PageHelper

### 异常处理规范
- 使用 `@ControllerAdvice` + `@ExceptionHandler` 进行全局异常处理
- 自定义业务异常: `ServiceException`, `BusinessException`
- 避免直接抛出技术性异常

### 安全规范
- JWT 令牌认证
- 权限控制基于 RBAC 模型
- 密码加密存储 (BCrypt)
- 输入数据严格校验，防止 SQL 注入和 XSS 攻击

### 代码风格
- 类、方法、变量命名规范: 驼峰命名法
- 必须添加必要的注释 (类、方法、字段)
- 遵循阿里巴巴 Java 开发手册规范
