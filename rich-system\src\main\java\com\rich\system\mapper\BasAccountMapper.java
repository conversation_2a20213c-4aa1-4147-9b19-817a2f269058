package com.rich.system.mapper;


import com.rich.common.core.domain.entity.BasAccount;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2022-09-06
 */
@Mapper
public interface BasAccountMapper {
    /**
     * 查询【请填写功能名称】
     *
     * @param accountId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    BasAccount selectBasAccountByAccountId(Long accountId);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param basAccount 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    List<BasAccount> selectBasAccountList(BasAccount basAccount);

    /**
     * 新增【请填写功能名称】
     *
     * @param basAccount 【请填写功能名称】
     * @return 结果
     */
    int insertBasAccount(BasAccount basAccount);

    /**
     * 修改【请填写功能名称】
     *
     * @param basAccount 【请填写功能名称】
     * @return 结果
     */
    int updateBasAccount(BasAccount basAccount);

    /**
     * 删除【请填写功能名称】
     *
     * @param accountId 【请填写功能名称】主键
     * @return 结果
     */
    int deleteBasAccountByAccountId(Long accountId);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param accountIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasAccountByAccountIds(Long[] accountIds);

    int checkExistCodeUnique(String accountCode);

    List<BasAccount> selectCompanyAccount();

}
