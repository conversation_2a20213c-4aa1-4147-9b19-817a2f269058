package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.BasCreditLevel;
import org.apache.ibatis.annotations.Mapper;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-11-14
 */
@Mapper
public interface BasCreditLevelMapper {
    /**
     * 查询【请填写功能名称】
     *
     * @param creditLevel 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    BasCreditLevel selectBasCreditLevelByCreditLevel(String creditLevel);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param basCreditLevel 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    List<BasCreditLevel> selectBasCreditLevelList(BasCreditLevel basCreditLevel);

    /**
     * 新增【请填写功能名称】
     *
     * @param basCreditLevel 【请填写功能名称】
     * @return 结果
     */
    int insertBasCreditLevel(BasCreditLevel basCreditLevel);

    /**
     * 修改【请填写功能名称】
     *
     * @param basCreditLevel 【请填写功能名称】
     * @return 结果
     */
    int updateBasCreditLevel(BasCreditLevel basCreditLevel);

    /**
     * 删除【请填写功能名称】
     *
     * @param creditLevel 【请填写功能名称】主键
     * @return 结果
     */
    int deleteBasCreditLevelByCreditLevel(String creditLevel);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param creditLevels 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasCreditLevelByCreditLevels(String[] creditLevels);
}
