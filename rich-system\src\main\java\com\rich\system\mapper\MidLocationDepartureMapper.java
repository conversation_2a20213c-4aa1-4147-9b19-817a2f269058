package com.rich.system.mapper;

import com.rich.system.domain.MidLocationDeparture;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 启运港区域Mapper接口
 *
 * <AUTHOR>
 * @date 2022-09-16
 */
@Mapper
public interface MidLocationDepartureMapper {
    /**
     * 查询启运港区域
     *
     * @param belongId 启运港区域主键
     * @param belongTo 属于
     * @return 启运港区域
     */
    List<Long> selectMidLocationDepartureById(Long belongId, String belongTo);

    /**
     * 查询启运港区域列表
     *
     * @return 启运港区域集合
     */
    List<MidLocationDeparture> selectMidLocationDepartureByLocationIds(@Param("locationIds") Long[] locationIds, String belongTo);

    /**
     * 查询启运港区域列表
     *
     * @return 启运港区域集合
     */
    List<MidLocationDeparture> selectMidLocationDepartureList(MidLocationDeparture MidLocationDeparture);

    /**
     * 新增启运港区域
     *
     * @param MidLocationDeparture 启运港区域
     * @return 结果
     */
    int insertMidLocationDeparture(MidLocationDeparture MidLocationDeparture);


    /**
     * 删除启运港区域
     *
     * @param belongId 启运港区域主键
     * @param belongTo 属于
     * @return 结果
     */
    int deleteMidLocationDepartureById(Long belongId, String belongTo);

    /**
     * 批量删除启运港区域
     *
     * @param belongIds 需要删除的数据主键集合
     * @param belongTo  属于
     * @return 结果
     */
    int deleteMidLocationDepartureByIds(Long[] belongIds, String belongTo);


    /**
     * 批量新增${subTable.functionName}
     *
     * @param belongList ${subTable.functionName}列表
     * @return 结果
     */
    int batchLD(List<MidLocationDeparture> belongList);

}
