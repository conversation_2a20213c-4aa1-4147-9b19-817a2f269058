package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasCommonInfoType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 通用信息类型Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-07
 */
@Mapper
public interface BasCommonInfoTypeMapper {
    /**
     * 查询通用信息类型
     *
     * @param infoTypeId 通用信息类型主键
     * @return 通用信息类型
     */
    BasCommonInfoType selectBasCommonInfoTypeByInfoTypeId(Long infoTypeId);

    /**
     * 查询通用信息类型列表
     *
     * @param basCommonInfoType 通用信息类型
     * @return 通用信息类型集合
     */
    List<BasCommonInfoType> selectBasCommonInfoTypeList(BasCommonInfoType basCommonInfoType);

    List<BasCommonInfoType> selectBasCommonInfoList(BasCommonInfoType basCommonInfoType);

    /**
     * 新增通用信息类型
     *
     * @param basCommonInfoType 通用信息类型
     * @return 结果
     */
    int insertBasCommonInfoType(BasCommonInfoType basCommonInfoType);

    /**
     * 修改通用信息类型
     *
     * @param basCommonInfoType 通用信息类型
     * @return 结果
     */
    int updateBasCommonInfoType(BasCommonInfoType basCommonInfoType);

    /**
     * 删除通用信息类型
     *
     * @param infoTypeId 通用信息类型主键
     * @return 结果
     */
    int deleteBasCommonInfoTypeByInfoTypeId(Long infoTypeId);

    /**
     * 批量删除通用信息类型
     *
     * @param infoTypeIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasCommonInfoTypeByInfoTypeIds(Long[] infoTypeIds);
}
