package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsCargoDetails;
import org.apache.ibatis.annotations.Mapper;

/**
 * 客户在仓货物明细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-11
 */
@Mapper
public interface RsCargoDetailsMapper {
    /**
     * 查询客户在仓货物明细
     *
     * @param cargoDetailsId 客户在仓货物明细主键
     * @return 客户在仓货物明细
     */
    RsCargoDetails selectRsCargoDetailsByCargoDetailsId(Long cargoDetailsId);

    /**
     * 查询客户在仓货物明细列表
     *
     * @param rsCargoDetails 客户在仓货物明细
     * @return 客户在仓货物明细集合
     */
    List<RsCargoDetails> selectRsCargoDetailsList(RsCargoDetails rsCargoDetails);

    /**
     * 新增客户在仓货物明细
     *
     * @param rsCargoDetails 客户在仓货物明细
     * @return 结果
     */
    int insertRsCargoDetails(RsCargoDetails rsCargoDetails);

    /**
     * 修改客户在仓货物明细
     *
     * @param rsCargoDetails 客户在仓货物明细
     * @return 结果
     */
    int updateRsCargoDetails(RsCargoDetails rsCargoDetails);

    /**
     * 删除客户在仓货物明细
     *
     * @param cargoDetailsId 客户在仓货物明细主键
     * @return 结果
     */
    int deleteRsCargoDetailsByCargoDetailsId(Long cargoDetailsId);

    /**
     * 批量删除客户在仓货物明细
     *
     * @param cargoDetailsIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsCargoDetailsByCargoDetailsIds(Long[] cargoDetailsIds);

    void deleteRsCargoDetailsByInventoryId(Long inventoryId);

    /**
     * 批量插入货物明细
     *
     * @param cargoDetailsList 货物明细列表
     * @return 结果
     */
    int batchInsertCargoDetails(List<RsCargoDetails> cargoDetailsList);
}
