package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.SysPermsType;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.SysPermsTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 权限类型Controller
 *
 * <AUTHOR>
 * @date 2023-03-21
 */
@RestController
@RequestMapping("/system/permstype")

public class SysPermsTypeController extends BaseController {

    @Autowired
    private  SysPermsTypeService sysPermsTypeService;

    /**
     * 查询权限类型列表
     */
    @PreAuthorize("@ss.hasPermi('system:permstype:list')")
    @GetMapping("/list")
    public AjaxResult list(SysPermsType sysPermsType) {
        List<SysPermsType> list = sysPermsTypeService.selectSysPermsTypeList(sysPermsType);
        return AjaxResult.success(list);
    }

    /**
     * 导出权限类型列表
     */
    @PreAuthorize("@ss.hasPermi('system:permstype:export')")
    @Log(title = "权限类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysPermsType sysPermsType) {
        List<SysPermsType> list = sysPermsTypeService.selectSysPermsTypeList(sysPermsType);
        ExcelUtil<SysPermsType> util = new ExcelUtil<SysPermsType>(SysPermsType.class);
        util.exportExcel(response, list, "权限类型数据");
    }

    /**
     * 获取权限类型详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:permstype:edit')")
    @GetMapping(value = "/{permsId}")
    public AjaxResult getInfo(@PathVariable("permsId") Long permsId) {
        return AjaxResult.success(sysPermsTypeService.selectSysPermsTypeByPermsId(permsId));
    }

    /**
     * 新增权限类型
     */
    @PreAuthorize("@ss.hasPermi('system:permstype:add')")
    @Log(title = "权限类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysPermsType sysPermsType) {
        return toAjax(sysPermsTypeService.insertSysPermsType(sysPermsType));
    }

    /**
     * 修改权限类型
     */
    @PreAuthorize("@ss.hasPermi('system:permstype:edit')")
    @Log(title = "权限类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysPermsType sysPermsType) {
        return toAjax(sysPermsTypeService.updateSysPermsType(sysPermsType));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:permstype:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysPermsType sysPermsType) {
        sysPermsType.setUpdateBy(getUserId());
        return toAjax(sysPermsTypeService.changeStatus(sysPermsType));
    }

    /**
     * 删除权限类型
     */
    @PreAuthorize("@ss.hasPermi('system:permstype:remove')")
    @Log(title = "权限类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{permsIds}")
    public AjaxResult remove(@PathVariable Long[] permsIds) {
        return toAjax(sysPermsTypeService.deleteSysPermsTypeByPermsIds(permsIds));
    }
}
