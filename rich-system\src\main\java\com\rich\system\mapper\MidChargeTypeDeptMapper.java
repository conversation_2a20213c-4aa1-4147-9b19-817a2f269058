package com.rich.system.mapper;

import com.rich.system.domain.MidChargeTypeDept;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 费用类型部门Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-06
 */
@Mapper
public interface MidChargeTypeDeptMapper {
    /**
     * 查询费用类型部门
     *
     * @return 费用类型部门
     */
    List<Long> selectMidChargeTypeDeptById(Long belongId, String belongTo);

    /**
     * 查询费用类型部门列表
     *
     * @param midChargeTypeDept 费用类型部门
     * @return 费用类型部门集合
     */
    List<MidChargeTypeDept> selectMidChargeTypeDeptList(MidChargeTypeDept midChargeTypeDept);

    /**
     * 新增费用类型部门
     *
     * @param midChargeTypeDept 费用类型部门
     * @return 结果
     */
    int insertMidChargeTypeDept(MidChargeTypeDept midChargeTypeDept);

    /**
     * 修改费用类型部门
     *
     * @param midChargeTypeDept 费用类型部门
     * @return 结果
     */
    int updateMidChargeTypeDept(MidChargeTypeDept midChargeTypeDept);

    /**
     * 删除费用类型部门
     *
     * @return 结果
     */
    int deleteMidChargeTypeDeptById(Long chargeTypeId, String belongTo);

    /**
     * 批量删除费用类型部门
     *
     * @return 结果
     */
    int deleteMidChargeTypeDeptByIds(Long[] chargeTypeIds, String belongTo);

    int batchChargeTypeDept(List<MidChargeTypeDept> list);
}
