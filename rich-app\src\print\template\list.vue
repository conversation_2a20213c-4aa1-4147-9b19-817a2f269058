<template>
  <view class="template-list">
    <view class="header">
      <view class="title">标签模板列表</view>
      <button type="primary" @click="createTemplate">新建模板</button>
    </view>

    <view v-if="templates.length === 0" class="empty-tip">
      <text>暂无模板，请点击"新建模板"创建</text>
    </view>

    <view v-else class="template-items">
      <view v-for="(item, index) in templates" :key="index" class="template-item">
        <view class="template-info">
          <view class="template-name">{{ item.name }}</view>
          <view class="template-size">尺寸: {{ item.width }}mm × {{ item.height }}mm</view>
          <view class="template-elements">
            <text>文本: {{ item.elements.text.length }}个</text>
            <text>条码: {{ item.elements.barcode.length }}个</text>
            <text>二维码: {{ item.elements.qrcode.length }}个</text>
          </view>
        </view>
        <view class="template-actions">
          <button size="mini" type="primary" @click="editTemplate(item)">编辑</button>
          <button size="mini" type="warn" @click="deleteTemplate(index, item.id)">删除</button>
          <button size="mini" @click="useTemplate(item)">使用</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      templates: []
    }
  },

  onShow() {
    this.loadTemplates()
  },

  methods: {
    /**
     * 加载模板列表
     */
    loadTemplates() {
      try {
        const templates = uni.getStorageSync('label_templates') || '[]'
        this.templates = JSON.parse(templates)
      } catch (e) {
        this.showToast('加载模板失败')
        console.error(e)
      }
    },

    /**
     * 创建新模板
     */
    createTemplate() {
      uni.navigateTo({
        url: '/print/template/index'
      })
    },

    /**
     * 编辑模板
     */
    editTemplate(template) {
      uni.navigateTo({
        url: `/print/template/index?id=${template.id}`
      })
    },

    /**
     * 删除模板
     */
    deleteTemplate(index, id) {
      uni.showModal({
        title: '提示',
        content: '确定要删除该模板吗？',
        success: (res) => {
          if (res.confirm) {
            try {
              const templates = uni.getStorageSync('label_templates') || '[]'
              let templateList = JSON.parse(templates)

              // 查找并删除指定ID的模板
              const targetIndex = templateList.findIndex(item => item.id === id)
              if (targetIndex !== -1) {
                templateList.splice(targetIndex, 1)
                // 更新存储和显示
                uni.setStorageSync('label_templates', JSON.stringify(templateList))
                this.templates.splice(index, 1)
                this.showToast('删除成功')
              }
            } catch (e) {
              this.showToast('删除失败')
              console.error(e)
            }
          }
        }
      })
    },

    /**
     * 使用模板
     */
    useTemplate(template) {
      // 将选中的模板传递回上一页
      const pages = getCurrentPages()
      const prevPage = pages[pages.length - 2]

      // 如果是从打印页面进入的，则设置选中的模板并返回
      if (prevPage && prevPage.route === 'print/index/index') {
        // 在回到上一页时更新数据
        uni.$emit('template-selected', {template})
        uni.navigateBack()
      } else {
        // 直接跳转到打印页面
        uni.switchTab({
          url: '/print/index/index'
        })
        // 延迟发送事件，确保页面已加载
        setTimeout(() => {
          uni.$emit('template-selected', {template})
        }, 500)
      }
    },

    /**
     * 显示提示
     */
    showToast(title) {
      uni.showToast({
        title: title,
        icon: 'none',
        duration: 2000
      })
    }
  }
}
</script>

<style lang="scss">
.template-list {
  padding: 30rpx;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 30rpx;
    margin-bottom: 30rpx;
    border-bottom: 1px solid #eee;

    .title {
      font-size: 36rpx;
      font-weight: bold;
    }
  }

  .empty-tip {
    text-align: center;
    padding: 100rpx 0;
    color: #999;
    font-size: 28rpx;
  }

  .template-items {
    .template-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx;
      margin-bottom: 20rpx;
      border: 1px solid #eee;
      border-radius: 8rpx;

      .template-info {
        flex: 1;

        .template-name {
          font-size: 32rpx;
          font-weight: bold;
          margin-bottom: 10rpx;
        }

        .template-size {
          font-size: 24rpx;
          color: #666;
          margin-bottom: 10rpx;
        }

        .template-elements {
          font-size: 24rpx;
          color: #666;

          text {
            margin-right: 20rpx;
          }
        }
      }

      .template-actions {
        display: flex;
        flex-direction: column;

        button {
          margin-bottom: 10rpx;
        }
      }
    }
  }
}
</style> 