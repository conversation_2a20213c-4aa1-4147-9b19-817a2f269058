package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsBasicLogistics;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsBasicLogisticsService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 基础物流Controller
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@RestController
@RequestMapping("/system/basiclogistics")
public class RsBasicLogisticsController extends BaseController {
    @Autowired
    private RsBasicLogisticsService rsBasicLogisticsService;

    /**
     * 查询基础物流列表
     */
    @PreAuthorize("@ss.hasPermi('system:basiclogistics:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsBasicLogistics rsBasicLogistics) {
        startPage();
        List<RsBasicLogistics> list = rsBasicLogisticsService.selectRsBasicLogisticsList(rsBasicLogistics);
        return getDataTable(list);
    }

    /**
     * 导出基础物流列表
     */
    @PreAuthorize("@ss.hasPermi('system:basiclogistics:export')")
    @Log(title = "基础物流", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsBasicLogistics rsBasicLogistics) {
        List<RsBasicLogistics> list = rsBasicLogisticsService.selectRsBasicLogisticsList(rsBasicLogistics);
        ExcelUtil<RsBasicLogistics> util = new ExcelUtil<RsBasicLogistics>(RsBasicLogistics.class);
        util.exportExcel(response, list, "基础物流数据");
    }

    /**
     * 获取基础物流详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:basiclogistics:query')")
    @GetMapping(value = "/{basicLogisticsId}")
    public AjaxResult getInfo(@PathVariable("basicLogisticsId") Long basicLogisticsId) {
        return AjaxResult.success(rsBasicLogisticsService.selectRsBasicLogisticsByBasicLogisticsId(basicLogisticsId));
    }

    /**
     * 新增基础物流
     */
    @PreAuthorize("@ss.hasPermi('system:basiclogistics:add')")
    @Log(title = "基础物流", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsBasicLogistics rsBasicLogistics) {
        return toAjax(rsBasicLogisticsService.insertRsBasicLogistics(rsBasicLogistics));
    }

    /**
     * 修改基础物流
     */
    @PreAuthorize("@ss.hasPermi('system:basiclogistics:edit')")
    @Log(title = "基础物流", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsBasicLogistics rsBasicLogistics) {
        return toAjax(rsBasicLogisticsService.updateRsBasicLogistics(rsBasicLogistics));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:basiclogistics:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsBasicLogistics rsBasicLogistics) {
        rsBasicLogistics.setUpdateBy(getUserId());
        return toAjax(rsBasicLogisticsService.changeStatus(rsBasicLogistics));
    }

    /**
     * 删除基础物流
     */
    @PreAuthorize("@ss.hasPermi('system:basiclogistics:remove')")
    @Log(title = "基础物流", businessType = BusinessType.DELETE)
    @DeleteMapping("/{basicLogisticsIds}")
    public AjaxResult remove(@PathVariable Long[] basicLogisticsIds) {
        return toAjax(rsBasicLogisticsService.deleteRsBasicLogisticsByBasicLogisticsIds(basicLogisticsIds));
    }
}
