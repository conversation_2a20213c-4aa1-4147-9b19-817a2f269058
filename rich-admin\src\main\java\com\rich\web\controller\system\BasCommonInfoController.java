package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasCommonInfo;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasCommonInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 通用信息Controller
 *
 * <AUTHOR>
 * @date 2022-12-07
 */
@RestController
@RequestMapping("/system/commoninfo")
public class BasCommonInfoController extends BaseController {
    @Autowired
    private BasCommonInfoService basCommonInfoService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询通用信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:commoninfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasCommonInfo basCommonInfo) {
        startPage();
        List<BasCommonInfo> list = basCommonInfoService.selectBasCommonInfoList(basCommonInfo);
        return getDataTable(list);
    }

    @GetMapping("/selectList")
    public AjaxResult selectList(BasCommonInfo basCommonInfo) {
        List<BasCommonInfo> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "commonInfo");
        if (list == null) {
            basCommonInfo.setStatus("0");
            list = basCommonInfoService.selectBasCommonInfoList(basCommonInfo);
            redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "commonInfo");
            redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "commonInfo", list);
        }
        return AjaxResult.success(list);
    }

    /**
     * 导出通用信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:commoninfo:export')")
    @Log(title = "通用信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasCommonInfo basCommonInfo) {
        List<BasCommonInfo> list = basCommonInfoService.selectBasCommonInfoList(basCommonInfo);
        ExcelUtil<BasCommonInfo> util = new ExcelUtil<BasCommonInfo>(BasCommonInfo.class);
        util.exportExcel(response, list, "通用信息数据");
    }

    /**
     * 获取通用信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:commoninfo:edit')")
    @GetMapping(value = "/{infoId}")
    public AjaxResult getInfo(@PathVariable("infoId") Long infoId) {
        return AjaxResult.success(basCommonInfoService.selectBasCommonInfoByInfoId(infoId));
    }

    /**
     * 新增通用信息
     */
    @PreAuthorize("@ss.hasPermi('system:commoninfo:add')")
    @Log(title = "通用信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasCommonInfo basCommonInfo) {
        int out = basCommonInfoService.insertBasCommonInfo(basCommonInfo);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "commonInfo");
        return toAjax(out);
    }

    /**
     * 修改通用信息
     */
    @PreAuthorize("@ss.hasPermi('system:commoninfo:edit')")
    @Log(title = "通用信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasCommonInfo basCommonInfo) {
        int out = basCommonInfoService.updateBasCommonInfo(basCommonInfo);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "commonInfo");
        return toAjax(out);
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('system:commoninfo:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasCommonInfo basCommonInfo) {
        basCommonInfo.setUpdateBy(getUserId());
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "commonInfo");
        return toAjax(basCommonInfoService.changeStatus(basCommonInfo));
    }

    /**
     * 删除通用信息
     */
    @PreAuthorize("@ss.hasPermi('system:commoninfo:remove')")
    @Log(title = "通用信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{infoIds}")
    public AjaxResult remove(@PathVariable Long[] infoIds) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "commonInfo");
        return toAjax(basCommonInfoService.deleteBasCommonInfoByInfoIds(infoIds));
    }
}
