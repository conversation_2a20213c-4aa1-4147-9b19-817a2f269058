package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpInsurance;
import org.apache.ibatis.annotations.Mapper;

/**
 * 保险服务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Mapper
public interface RsOpInsuranceMapper {
    /**
     * 查询保险服务
     *
     * @param insuranceId 保险服务主键
     * @return 保险服务
     */
    RsOpInsurance selectRsOpInsuranceByInsuranceId(Long insuranceId);

    /**
     * 查询保险服务列表
     *
     * @param rsOpInsurance 保险服务
     * @return 保险服务集合
     */
    List<RsOpInsurance> selectRsOpInsuranceList(RsOpInsurance rsOpInsurance);

    /**
     * 新增保险服务
     *
     * @param rsOpInsurance 保险服务
     * @return 结果
     */
    int insertRsOpInsurance(RsOpInsurance rsOpInsurance);

    /**
     * 修改保险服务
     *
     * @param rsOpInsurance 保险服务
     * @return 结果
     */
    int updateRsOpInsurance(RsOpInsurance rsOpInsurance);

    /**
     * 删除保险服务
     *
     * @param insuranceId 保险服务主键
     * @return 结果
     */
    int deleteRsOpInsuranceByInsuranceId(Long insuranceId);

    /**
     * 批量删除保险服务
     *
     * @param insuranceIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsOpInsuranceByInsuranceIds(Long[] insuranceIds);

    RsOpInsurance selectRsOpInsuranceByRctId(Long rctId);
}
