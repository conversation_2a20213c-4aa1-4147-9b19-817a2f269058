package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpImportCustomsClearance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsOpImportCustomsClearanceMapper;
import com.rich.system.service.RsOpImportCustomsClearanceService;

/**
 * 进口清关服务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Service
public class RsOpImportCustomsClearanceServiceImpl implements RsOpImportCustomsClearanceService {
    @Autowired
    private RsOpImportCustomsClearanceMapper rsOpImportCustomsClearanceMapper;

    /**
     * 查询进口清关服务
     *
     * @param importCustomsClearanceId 进口清关服务主键
     * @return 进口清关服务
     */
    @Override
    public RsOpImportCustomsClearance selectRsOpImportCustomsClearanceByImportCustomsClearanceId(Long importCustomsClearanceId) {
        return rsOpImportCustomsClearanceMapper.selectRsOpImportCustomsClearanceByImportCustomsClearanceId(importCustomsClearanceId);
    }

    /**
     * 查询进口清关服务列表
     *
     * @param rsOpImportCustomsClearance 进口清关服务
     * @return 进口清关服务
     */
    @Override
    public List<RsOpImportCustomsClearance> selectRsOpImportCustomsClearanceList(RsOpImportCustomsClearance rsOpImportCustomsClearance) {
        return rsOpImportCustomsClearanceMapper.selectRsOpImportCustomsClearanceList(rsOpImportCustomsClearance);
    }

    /**
     * 新增进口清关服务
     *
     * @param rsOpImportCustomsClearance 进口清关服务
     * @return 结果
     */
    @Override
    public int insertRsOpImportCustomsClearance(RsOpImportCustomsClearance rsOpImportCustomsClearance) {
        return rsOpImportCustomsClearanceMapper.insertRsOpImportCustomsClearance(rsOpImportCustomsClearance);
    }

    /**
     * 修改进口清关服务
     *
     * @param rsOpImportCustomsClearance 进口清关服务
     * @return 结果
     */
    @Override
    public int updateRsOpImportCustomsClearance(RsOpImportCustomsClearance rsOpImportCustomsClearance) {
        return rsOpImportCustomsClearanceMapper.updateRsOpImportCustomsClearance(rsOpImportCustomsClearance);
    }

    /**
     * 修改进口清关服务状态
     *
     * @param rsOpImportCustomsClearance 进口清关服务
     * @return 进口清关服务
     */
    @Override
    public int changeStatus(RsOpImportCustomsClearance rsOpImportCustomsClearance) {
        return rsOpImportCustomsClearanceMapper.updateRsOpImportCustomsClearance(rsOpImportCustomsClearance);
    }

    /**
     * 批量删除进口清关服务
     *
     * @param importCustomsClearanceIds 需要删除的进口清关服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpImportCustomsClearanceByImportCustomsClearanceIds(Long[] importCustomsClearanceIds) {
        return rsOpImportCustomsClearanceMapper.deleteRsOpImportCustomsClearanceByImportCustomsClearanceIds(importCustomsClearanceIds);
    }

    /**
     * 删除进口清关服务信息
     *
     * @param importCustomsClearanceId 进口清关服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpImportCustomsClearanceByImportCustomsClearanceId(Long importCustomsClearanceId) {
        return rsOpImportCustomsClearanceMapper.deleteRsOpImportCustomsClearanceByImportCustomsClearanceId(importCustomsClearanceId);
    }
}
