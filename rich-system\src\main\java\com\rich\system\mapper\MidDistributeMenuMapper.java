package com.rich.system.mapper;

import com.rich.system.domain.MidDistributeMenu;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-03-20
 */
@Mapper
public interface MidDistributeMenuMapper {
    /**
     * 查询【请填写功能名称】
     *
     * @param distributeId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    List<Long> selectMidDistributeMenuByDistributeId(Long distributeId);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param midDistributeMenu 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    List<MidDistributeMenu> selectMidDistributeMenuList(MidDistributeMenu midDistributeMenu);

    /**
     * 新增【请填写功能名称】
     *
     * @param midDistributeMenu 【请填写功能名称】
     * @return 结果
     */
    int insertMidDistributeMenu(MidDistributeMenu midDistributeMenu);

    /**
     * 修改【请填写功能名称】
     *
     * @param midDistributeMenu 【请填写功能名称】
     * @return 结果
     */
    int updateMidDistributeMenu(MidDistributeMenu midDistributeMenu);

    /**
     * 删除【请填写功能名称】
     *
     * @param distributeId 【请填写功能名称】主键
     * @return 结果
     */
    int deleteMidDistributeMenuByDistributeId(Long distributeId);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param distributeIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteMidDistributeMenuByDistributeIds(Long[] distributeIds);

    int batchMenu(List<MidDistributeMenu> item);
}
