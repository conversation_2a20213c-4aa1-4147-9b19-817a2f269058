package com.rich.system.service.impl;

import com.rich.common.constant.CacheConstants;
import com.rich.common.core.domain.entity.BasPaymentType;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.system.mapper.BasPaymentTypeMapper;
import com.rich.system.service.BasPaymentTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 收付顺序Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@Service
public class BasPaymentTypeServiceImpl implements BasPaymentTypeService {
    @Autowired
    private BasPaymentTypeMapper basPaymentTypeMapper;

    /**
     * 查询收付顺序
     *
     * @param paymentTypeId 收付顺序主键
     * @return 收付顺序
     */
    @Override
    public BasPaymentType selectBasPaymentTypeByPaymentTypeId(Long paymentTypeId) {
        return basPaymentTypeMapper.selectBasPaymentTypeByPaymentTypeId(paymentTypeId);
    }

    /**
     * 查询收付顺序列表
     *
     * @param basPaymentType 收付顺序
     * @return 收付顺序
     */
    @Override
    public List<BasPaymentType> selectBasPaymentTypeList(BasPaymentType basPaymentType) {
        return basPaymentTypeMapper.selectBasPaymentTypeList(basPaymentType);
    }

    /**
     * 新增收付顺序
     *
     * @param basPaymentType 收付顺序
     * @return 结果
     */
    @Override
    public int insertBasPaymentType(BasPaymentType basPaymentType) {
        basPaymentType.setCreateTime(DateUtils.getNowDate());
        basPaymentType.setCreateBy(SecurityUtils.getUserId());
        return basPaymentTypeMapper.insertBasPaymentType(basPaymentType);
    }

    /**
     * 修改收付顺序
     *
     * @param basPaymentType 收付顺序
     * @return 结果
     */
    @Override
    public int updateBasPaymentType(BasPaymentType basPaymentType) {
        basPaymentType.setUpdateTime(DateUtils.getNowDate());
        basPaymentType.setUpdateBy(SecurityUtils.getUserId());
        return basPaymentTypeMapper.updateBasPaymentType(basPaymentType);
    }

    /**
     * 修改收付顺序状态
     *
     * @param basPaymentType 收付顺序
     * @return 收付顺序
     */
    @Override
    public int changeStatus(BasPaymentType basPaymentType) {
        return basPaymentTypeMapper.updateBasPaymentType(basPaymentType);
    }

    /**
     * 批量删除收付顺序
     *
     * @param paymentTypeIds 需要删除的收付顺序主键
     * @return 结果
     */
    @Override
    public int deleteBasPaymentTypeByPaymentTypeIds(Long[] paymentTypeIds) {
        return basPaymentTypeMapper.deleteBasPaymentTypeByPaymentTypeIds(paymentTypeIds);
    }

    /**
     * 删除收付顺序信息
     *
     * @param paymentTypeId 收付顺序主键
     * @return 结果
     */
    @Override
    public int deleteBasPaymentTypeByPaymentTypeId(Long paymentTypeId) {
        return basPaymentTypeMapper.deleteBasPaymentTypeByPaymentTypeId(paymentTypeId);
    }
}
