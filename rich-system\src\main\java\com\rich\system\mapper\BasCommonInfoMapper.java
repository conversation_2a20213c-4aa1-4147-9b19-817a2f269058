package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasCommonInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 通用信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-07
 */
@Mapper
public interface BasCommonInfoMapper {
    /**
     * 查询通用信息
     *
     * @param infoId 通用信息主键
     * @return 通用信息
     */
    BasCommonInfo selectBasCommonInfoByInfoId(Long infoId);

    /**
     * 查询通用信息列表
     *
     * @param basCommonInfo 通用信息
     * @return 通用信息集合
     */
    List<BasCommonInfo> selectBasCommonInfoList(BasCommonInfo basCommonInfo);

    /**
     * 新增通用信息
     *
     * @param basCommonInfo 通用信息
     * @return 结果
     */
    int insertBasCommonInfo(BasCommonInfo basCommonInfo);

    /**
     * 修改通用信息
     *
     * @param basCommonInfo 通用信息
     * @return 结果
     */
    int updateBasCommonInfo(BasCommonInfo basCommonInfo);

    /**
     * 删除通用信息
     *
     * @param infoId 通用信息主键
     * @return 结果
     */
    int deleteBasCommonInfoByInfoId(Long infoId);

    /**
     * 批量删除通用信息
     *
     * @param infoIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasCommonInfoByInfoIds(Long[] infoIds);
}
