package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsImportCustoms;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsImportCustomsService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 进口清关Controller
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@RestController
@RequestMapping("/system/importcustoms")
public class RsImportCustomsController extends BaseController {
    @Autowired
    private RsImportCustomsService rsImportCustomsService;

    /**
     * 查询进口清关列表
     */
    @PreAuthorize("@ss.hasPermi('system:importcustoms:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsImportCustoms rsImportCustoms) {
        startPage();
        List<RsImportCustoms> list = rsImportCustomsService.selectRsImportCustomsList(rsImportCustoms);
        return getDataTable(list);
    }

    /**
     * 导出进口清关列表
     */
    @PreAuthorize("@ss.hasPermi('system:importcustoms:export')")
    @Log(title = "进口清关", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsImportCustoms rsImportCustoms) {
        List<RsImportCustoms> list = rsImportCustomsService.selectRsImportCustomsList(rsImportCustoms);
        ExcelUtil<RsImportCustoms> util = new ExcelUtil<RsImportCustoms>(RsImportCustoms.class);
        util.exportExcel(response, list, "进口清关数据");
    }

    /**
     * 获取进口清关详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:importcustoms:query')")
    @GetMapping(value = "/{importCustomsId}")
    public AjaxResult getInfo(@PathVariable("importCustomsId") Long importCustomsId) {
        return AjaxResult.success(rsImportCustomsService.selectRsImportCustomsByImportCustomsId(importCustomsId));
    }

    /**
     * 新增进口清关
     */
    @PreAuthorize("@ss.hasPermi('system:importcustoms:add')")
    @Log(title = "进口清关", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsImportCustoms rsImportCustoms) {
        return toAjax(rsImportCustomsService.insertRsImportCustoms(rsImportCustoms));
    }

    /**
     * 修改进口清关
     */
    @PreAuthorize("@ss.hasPermi('system:importcustoms:edit')")
    @Log(title = "进口清关", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsImportCustoms rsImportCustoms) {
        return toAjax(rsImportCustomsService.updateRsImportCustoms(rsImportCustoms));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:importcustoms:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsImportCustoms rsImportCustoms) {
        rsImportCustoms.setUpdateBy(getUserId());
        return toAjax(rsImportCustomsService.changeStatus(rsImportCustoms));
    }

    /**
     * 删除进口清关
     */
    @PreAuthorize("@ss.hasPermi('system:importcustoms:remove')")
    @Log(title = "进口清关", businessType = BusinessType.DELETE)
    @DeleteMapping("/{importCustomsIds}")
    public AjaxResult remove(@PathVariable Long[] importCustomsIds) {
        return toAjax(rsImportCustomsService.deleteRsImportCustomsByImportCustomsIds(importCustomsIds));
    }
}
