package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

/**
 * 放货方式对象 bas_release_type
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
public class BasReleaseType extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 放货方式
     */
    private Long releaseTypeId;

    /**
     * 简称
     */
    @Excel(name = "简称")
    private String releaseTypeShortName;

    /**
     * 中文名
     */
    @Excel(name = "中文名")
    private String releaseTypeLocalName;

    /**
     * 英文名
     */
    @Excel(name = "英文名")
    private String releaseTypeEnName;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Integer orderNum;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;

    public void setReleaseTypeId(Long releaseTypeId) {
        this.releaseTypeId = releaseTypeId;
    }

    public Long getReleaseTypeId() {
        return releaseTypeId;
    }

    public void setReleaseTypeShortName(String releaseTypeShortName) {
        this.releaseTypeShortName = releaseTypeShortName;
    }

    public String getReleaseTypeShortName() {
        return releaseTypeShortName;
    }

    public void setReleaseTypeLocalName(String releaseTypeLocalName) {
        this.releaseTypeLocalName = releaseTypeLocalName;
    }

    public String getReleaseTypeLocalName() {
        return releaseTypeLocalName;
    }

    public void setReleaseTypeEnName(String releaseTypeEnName) {
        this.releaseTypeEnName = releaseTypeEnName;
    }

    public String getReleaseTypeEnName() {
        return releaseTypeEnName;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
}
