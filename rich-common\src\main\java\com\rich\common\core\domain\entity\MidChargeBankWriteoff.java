package com.rich.common.core.domain.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 记录银行销账的明细中间对象 mid_charge_bank_writeoff
 * 
 * <AUTHOR>
 * @date 2024-05-06
 */
public class MidChargeBankWriteoff extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 中间表流水ID
     */
    private Long midChargeBankId;

    /** 费用条目ID , */
    @Excel(name = "费用条目ID ,")
    private Long chargeId;

    /** 银行流水ID , */
    @Excel(name = "银行流水ID ,")
    private String bankStatement;

    /**
     * RCT号 ,
     */
    @Excel(name = "RCT号 ,")
    private String sqdRctNo;

    /**
     * 收支标志 ,应收应付标志
     */
    @Excel(name = "收支标志 ,应收应付标志")
    private String sqdIsRecievingOrPaying;

    /**
     * 结算公司 ,(应收/应付)
     */
    @Excel(name = "结算公司 ,(应收/应付)")
    private Long sqdClearingCompanyId;

    /** 费用名称ID , */
    @Excel(name = "费用名称ID ,")
    private Long sqdDnChargeNameId;

    /**
     * 费用名称简称 ,
     */
    @Excel(name = "费用名称简称 ,")
    private String sqdChargeShortname;

    /** 账单币种 , */
    @Excel(name = "账单币种 ,")
    private String sqdDnCurrencyCode;

    /** sqd_应收付额小计(金额小计) , */
    @Excel(name = "sqd_应收付额小计(金额小计) ,")
    private BigDecimal tempDnBalanceRemain;

    /**
     * 财务审核标记 ,财务在主表中审核，自动设置子表审核，取消审核，则须检查是否有销账/发票记录，无记录才可以取消审核。
     */
    @Excel(name = "财务审核标记 ,财务在主表中审核，自动设置子表审核，取消审核，则须检查是否有销账/发票记录，无记录才可以取消审核。")
    private String sqdIsAccountConfirmed;

    /**
     * 已销账金额 ,
     */
    @Excel(name = "已销账金额 ,")
    private BigDecimal sqdDnCurrencyPaid;

    /**
     * 未销账余额 ,
     */
    @Excel(name = "未销账余额 ,")
    private BigDecimal sqdDnCurrencyBalance;

    /**
     * 账单本次销账金额 ,
     */
    @Excel(name = "账单本次销账金额 ,")
    private BigDecimal writeoffFromDnBalance;

    /** 银行币种 , */
    @Excel(name = "银行币种 ,")
    private String sqdBankCurrencyCode;

    /**
     * 银行可销金额 ,
     */
    @Excel(name = "银行可销金额 ,")
    private BigDecimal tempBankBalanceRemain;

    /**
     * 账单汇率基准 ,在汇率表中查询出的原币种基数 或 本位币汇率
     */
    @Excel(name = "账单汇率基准 ,在汇率表中查询出的原币种基数 或 本位币汇率")
    private BigDecimal dnBasicRate;

    /**
     * 银行汇率基准 ,在汇率表中查询出的原币种基数 或 本位币汇率
     */
    @Excel(name = "银行汇率基准 ,在汇率表中查询出的原币种基数 或 本位币汇率")
    private BigDecimal bankBasicRate;

    /**
     * 汇率展示 ,文本，两个币种汇率相除，保留分子，如RMB:RMB 1, USD/RMB: 7.25, RMB/USD: 1/7.25
     */
    @Excel(name = "汇率展示 ,文本，两个币种汇率相除，保留分子，如RMB:RMB 1, USD/RMB: 7.25, RMB/USD: 1/7.25")
    private String exchangeRateShowing;

    /**
     * 折算记账金额 ,
     */
    @Excel(name = "折算记账金额 ,")
    private BigDecimal writeoffFromBankBalance;

    /**
     * 销账状态 ,√All,  =Part, -Null
     */
    @Excel(name = "销账状态 ,√All,  =Part, -Null")
    private String writeoffStatus;

    /**
     * 销账人 ,
     */
    @Excel(name = "销账人 ,")
    private Long writeoffStaffId;

    /**
     * 销账时间 ,
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "销账时间 ,", width = 30, dateFormat = "yyyy-MM-dd")
    private Date writeoffTime;
    private Long bankRecordId;

    public Long getBankRecordId() {
        return bankRecordId;
    }

    public void setBankRecordId(Long bankRecordId) {
        this.bankRecordId = bankRecordId;
    }

    public void setMidChargeBankId(Long midChargeBankId) {
        this.midChargeBankId = midChargeBankId;
    }

    public Long getMidChargeBankId() {
        return midChargeBankId;
    }

    public void setChargeId(Long chargeId)
    {
        this.chargeId = chargeId;
    }

    public Long getChargeId() {
        return chargeId;
    }

    public String getBankStatement() {
        return bankStatement;
    }

    public void setBankStatement(String bankStatement) {
        this.bankStatement = bankStatement;
    }

    public String getSqdRctNo() {
        return sqdRctNo;
    }

    public void setSqdRctNo(String sqdRctNo) {
        this.sqdRctNo = sqdRctNo;
    }

    public String getSqdIsRecievingOrPaying() {
        return sqdIsRecievingOrPaying;
    }

    public void setSqdIsRecievingOrPaying(String sqdIsRecievingOrPaying) {
        this.sqdIsRecievingOrPaying = sqdIsRecievingOrPaying;
    }

    public Long getSqdClearingCompanyId() {
        return sqdClearingCompanyId;
    }

    public void setSqdClearingCompanyId(Long sqdClearingCompanyId) {
        this.sqdClearingCompanyId = sqdClearingCompanyId;
    }
    public void setSqdDnChargeNameId(Long sqdDnChargeNameId) {
        this.sqdDnChargeNameId = sqdDnChargeNameId;
    }

    public Long getSqdDnChargeNameId() {
        return sqdDnChargeNameId;
    }

    public String getSqdChargeShortname() {
        return sqdChargeShortname;
    }

    public void setSqdChargeShortname(String sqdChargeShortname) {
        this.sqdChargeShortname = sqdChargeShortname;
    }

    public void setSqdDnCurrencyCode(String sqdDnCurrencyCode) {
        this.sqdDnCurrencyCode = sqdDnCurrencyCode;
    }

    public String getSqdDnCurrencyCode() {
        return sqdDnCurrencyCode;
    }

    public void setTempDnBalanceRemain(BigDecimal tempDnBalanceRemain) {
        this.tempDnBalanceRemain = tempDnBalanceRemain;
    }

    public BigDecimal getTempDnBalanceRemain() {
        return tempDnBalanceRemain;
    }

    public String getSqdIsAccountConfirmed() {
        return sqdIsAccountConfirmed;
    }

    public void setSqdIsAccountConfirmed(String sqdIsAccountConfirmed) {
        this.sqdIsAccountConfirmed = sqdIsAccountConfirmed;
    }

    public BigDecimal getSqdDnCurrencyPaid() {
        return sqdDnCurrencyPaid;
    }

    public void setSqdDnCurrencyPaid(BigDecimal sqdDnCurrencyPaid) {
        this.sqdDnCurrencyPaid = sqdDnCurrencyPaid;
    }

    public BigDecimal getSqdDnCurrencyBalance() {
        return sqdDnCurrencyBalance;
    }

    public void setSqdDnCurrencyBalance(BigDecimal sqdDnCurrencyBalance) {
        this.sqdDnCurrencyBalance = sqdDnCurrencyBalance;
    }

    public void setWriteoffFromDnBalance(BigDecimal writeoffFromDnBalance) {
        this.writeoffFromDnBalance = writeoffFromDnBalance;
    }

    public BigDecimal getWriteoffFromDnBalance() {
        return writeoffFromDnBalance;
    }

    public void setSqdBankCurrencyCode(String sqdBankCurrencyCode) {
        this.sqdBankCurrencyCode = sqdBankCurrencyCode;
    }

    public String getSqdBankCurrencyCode() {
        return sqdBankCurrencyCode;
    }

    public void setTempBankBalanceRemain(BigDecimal tempBankBalanceRemain) {
        this.tempBankBalanceRemain = tempBankBalanceRemain;
    }

    public BigDecimal getTempBankBalanceRemain() {
        return tempBankBalanceRemain;
    }

    public BigDecimal getDnBasicRate() {
        return dnBasicRate;
    }

    public void setDnBasicRate(BigDecimal dnBasicRate) {
        this.dnBasicRate = dnBasicRate;
    }

    public BigDecimal getBankBasicRate() {
        return bankBasicRate;
    }

    public void setBankBasicRate(BigDecimal bankBasicRate) {
        this.bankBasicRate = bankBasicRate;
    }

    public String getExchangeRateShowing() {
        return exchangeRateShowing;
    }

    public void setExchangeRateShowing(String exchangeRateShowing) {
        this.exchangeRateShowing = exchangeRateShowing;
    }

    public void setWriteoffFromBankBalance(BigDecimal writeoffFromBankBalance) {
        this.writeoffFromBankBalance = writeoffFromBankBalance;
    }

    public BigDecimal getWriteoffFromBankBalance() {
        return writeoffFromBankBalance;
    }

    public String getWriteoffStatus() {
        return writeoffStatus;
    }

    public void setWriteoffStatus(String writeoffStatus) {
        this.writeoffStatus = writeoffStatus;
    }

    public Long getWriteoffStaffId() {
        return writeoffStaffId;
    }

    public void setWriteoffStaffId(Long writeoffStaffId) {
        this.writeoffStaffId = writeoffStaffId;
    }

    public Date getWriteoffTime() {
        return writeoffTime;
    }

    public void setWriteoffTime(Date writeoffTime)
    {
        this.writeoffTime = writeoffTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("midChargeBankId", getMidChargeBankId())
                .append("chargeId", getChargeId())
                .append("bankStatement", getBankStatement())
                .append("sqdRctNo", getSqdRctNo())
                .append("sqdIsRecievingOrPaying", getSqdIsRecievingOrPaying())
                .append("sqdClearingCompanyId", getSqdClearingCompanyId())
                .append("sqdDnChargeNameId", getSqdDnChargeNameId())
                .append("sqdChargeShortname", getSqdChargeShortname())
                .append("sqdDnCurrencyCode", getSqdDnCurrencyCode())
                .append("tempDnBalanceRemain", getTempDnBalanceRemain())
                .append("sqdIsAccountConfirmed", getSqdIsAccountConfirmed())
                .append("sqdDnCurrencyPaid", getSqdDnCurrencyPaid())
                .append("sqdDnCurrencyBalance", getSqdDnCurrencyBalance())
                .append("writeoffFromDnBalance", getWriteoffFromDnBalance())
                .append("sqdBankCurrencyCode", getSqdBankCurrencyCode())
                .append("tempBankBalanceRemain", getTempBankBalanceRemain())
                .append("dnBasicRate", getDnBasicRate())
                .append("bankBasicRate", getBankBasicRate())
                .append("exchangeRateShowing", getExchangeRateShowing())
                .append("writeoffFromBankBalance", getWriteoffFromBankBalance())
            .append("writeoffStatus", getWriteoffStatus())
            .append("writeoffStaffId", getWriteoffStaffId())
            .append("writeoffTime", getWriteoffTime())
            .toString();
    }
}
