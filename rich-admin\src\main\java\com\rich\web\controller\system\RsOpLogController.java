package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsOpLog;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsOpLogService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 操作单操作记录Controller
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@RestController
@RequestMapping("/system/oplog")
public class RsOpLogController extends BaseController {
    @Autowired
    private RsOpLogService rsOpLogService;

    /**
     * 查询操作单操作记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:oplog:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsOpLog rsOpLog) {
        startPage();
        List<RsOpLog> list = rsOpLogService.selectRsOpLogList(rsOpLog);
        return getDataTable(list);
    }

    /**
     * 导出操作单操作记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:oplog:export')")
    @Log(title = "操作单操作记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsOpLog rsOpLog) {
        List<RsOpLog> list = rsOpLogService.selectRsOpLogList(rsOpLog);
        ExcelUtil<RsOpLog> util = new ExcelUtil<RsOpLog>(RsOpLog.class);
        util.exportExcel(response, list, "操作单操作记录数据");
    }

    /**
     * 获取操作单操作记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:oplog:query')")
    @GetMapping(value = "/{opLogId}")
    public AjaxResult getInfo(@PathVariable("opLogId") Long opLogId) {
        return AjaxResult.success(rsOpLogService.selectRsOpLogByOpLogId(opLogId));
    }

    /**
     * 新增操作单操作记录
     */
    @PreAuthorize("@ss.hasPermi('system:oplog:add')")
    @Log(title = "操作单操作记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsOpLog rsOpLog) {
        return toAjax(rsOpLogService.insertRsOpLog(rsOpLog));
    }

    /**
     * 修改操作单操作记录
     */
    @PreAuthorize("@ss.hasPermi('system:oplog:edit')")
    @Log(title = "操作单操作记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsOpLog rsOpLog) {
        return toAjax(rsOpLogService.updateRsOpLog(rsOpLog));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:oplog:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsOpLog rsOpLog) {
        rsOpLog.setUpdateBy(getUserId());
        return toAjax(rsOpLogService.changeStatus(rsOpLog));
    }

    /**
     * 删除操作单操作记录
     */
    @PreAuthorize("@ss.hasPermi('system:oplog:remove')")
    @Log(title = "操作单操作记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{opLogIds}")
    public AjaxResult remove(@PathVariable Long[] opLogIds) {
        return toAjax(rsOpLogService.deleteRsOpLogByOpLogIds(opLogIds));
    }
}
