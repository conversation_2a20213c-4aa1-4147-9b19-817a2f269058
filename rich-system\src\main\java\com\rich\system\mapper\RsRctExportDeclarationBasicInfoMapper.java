package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsRctExportDeclarationBasicInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 操作单出口报关基础信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-15
 */
@Mapper
public interface RsRctExportDeclarationBasicInfoMapper {
    /**
     * 查询操作单出口报关基础信息
     *
     * @param exportDeclarationId 操作单出口报关基础信息主键
     * @return 操作单出口报关基础信息
     */
    RsRctExportDeclarationBasicInfo selectRsRctExportDeclarationBasicInfoByRctId(Long rctId);

    /**
     * 查询操作单出口报关基础信息列表
     *
     * @param rsRctExportDeclarationBasicInfo 操作单出口报关基础信息
     * @return 操作单出口报关基础信息集合
     */
    List<RsRctExportDeclarationBasicInfo> selectRsRctExportDeclarationBasicInfoList(RsRctExportDeclarationBasicInfo rsRctExportDeclarationBasicInfo);

    /**
     * 新增操作单出口报关基础信息
     *
     * @param rsRctExportDeclarationBasicInfo 操作单出口报关基础信息
     * @return 结果
     */
    int insertRsRctExportDeclarationBasicInfo(RsRctExportDeclarationBasicInfo rsRctExportDeclarationBasicInfo);

    /**
     * 修改操作单出口报关基础信息
     *
     * @param rsRctExportDeclarationBasicInfo 操作单出口报关基础信息
     * @return 结果
     */
    int updateRsRctExportDeclarationBasicInfo(RsRctExportDeclarationBasicInfo rsRctExportDeclarationBasicInfo);

    /**
     * 删除操作单出口报关基础信息
     *
     * @return 结果
     */
    int deleteRsRctExportDeclarationBasicInfoById(Long rctId);

    /**
     * 批量删除操作单出口报关基础信息
     *
     * @return 结果
     */
    int deleteRsRctExportDeclarationBasicInfoByIds(Long[] rctIds);
}
