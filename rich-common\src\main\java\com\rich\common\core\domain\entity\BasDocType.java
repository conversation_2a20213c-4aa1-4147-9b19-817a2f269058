package com.rich.common.core.domain.entity;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 【请填写功能名称】对象 bas_doc_type
 *
 * <AUTHOR>
 * @date 2024-04-08
 */
public class BasDocType extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 文件类型
     */
    private String docTypeCode;

    /**
     * 中文名
     */
    @Excel(name = "中文名")
    private String docTypeLocalName;

    /**
     * 英文名
     */
    @Excel(name = "英文名")
    private String docTypeEnName;

    /**
     * 所属服务
     */
    @Excel(name = "所属服务")
    private String belongsServiceId;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;

    /**
     * 删除时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "删除时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date deleteTime;

    /**
     * 数据状态（-1:删除或不可用，0：正常）
     */
    @Excel(name = "数据状态", readConverterExp = "-=1:删除或不可用，0：正常")
    private String deleteStatus;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long deleteBy;
    private int orderNum;
    private String serviceName;

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public int getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(int orderNum) {
        this.orderNum = orderNum;
    }

    public String getDocTypeCode() {
        return docTypeCode;
    }

    public void setDocTypeCode(String docTypeCode) {
        this.docTypeCode = docTypeCode;
    }

    public String getDocTypeLocalName() {
        return docTypeLocalName;
    }

    public void setDocTypeLocalName(String docTypeLocalName) {
        this.docTypeLocalName = docTypeLocalName;
    }

    public String getDocTypeEnName() {
        return docTypeEnName;
    }

    public void setDocTypeEnName(String docTypeEnName) {
        this.docTypeEnName = docTypeEnName;
    }

    public String getBelongsServiceId() {
        return belongsServiceId;
    }

    public void setBelongsServiceId(String belongsServiceId) {
        this.belongsServiceId = belongsServiceId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Long getDeleteBy() {
        return deleteBy;
    }

    public void setDeleteBy(Long deleteBy) {
        this.deleteBy = deleteBy;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("docTypeCode", getDocTypeCode())
                .append("docTypeLocalName", getDocTypeLocalName())
                .append("docTypeEnName", getDocTypeEnName())
                .append("belongsServiceId", getBelongsServiceId())
                .append("status", getStatus())
                .append("remark", getRemark())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("deleteTime", getDeleteTime())
                .append("deleteStatus", getDeleteStatus())
                .append("deleteBy", getDeleteBy())
                .append("updateBy", getUpdateBy())
                .append("createBy", getCreateBy())
                .toString();
    }
}
