package com.rich.common.core.domain.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 服务实例，记录着每一个订舱中的各种服务对象 rs_service_instances
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
public class RsServiceInstances extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 服务实例id ,
     */
    private Long serviceId;

    /**
     * rctId
     */
    private Long rctId;

    /**
     * 服务类型id ,
     */
    @Excel(name = "服务类型id ,")
    private Long serviceTypeId;

    /**
     * 所属操作单号 ,
     */
    @Excel(name = "所属操作单号 ,")
    private String rctNo;

    /**
     * 供应商id ,直接从询价单中复制
     */
    @Excel(name = "供应商id ,直接从询价单中复制")
    private Long supplierId;

    /**
     * 供应商综述 ,直接从询价单中复制
     */
    @Excel(name = "供应商综述 ,直接从询价单中复制")
    private String supplierSummary;

    /**
     * 供应商联系人 ,直接从询价单中复制
     */
    @Excel(name = "供应商联系人 ,直接从询价单中复制")
    private String supplierContact;

    /**
     * 供应商联系方式 ,直接从询价单中复制
     */
    @Excel(name = "供应商联系方式 ,直接从询价单中复制")
    private String supplierTel;

    /**
     * 询价单号 ,商务询价单号(用于应付)
     */
    @Excel(name = "询价单号 ,商务询价单号(用于应付)")
    private String inquiryNo;

    /**
     * 合约类型 ,直接从询价单中复制
     */
    @Excel(name = "合约类型 ,直接从询价单中复制")
    private String agreementTypeCode;

    /**
     * 合约号 ,直接从询价单中复制
     */
    @Excel(name = "合约号 ,直接从询价单中复制")
    private String agreementNo;

    /**
     * 运输限重 ,直接从询价单中复制
     */
    @Excel(name = "运输限重 ,直接从询价单中复制")
    private BigDecimal maxWeight;

    /**
     * 询价日期 ,直接从询价单中复制
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "询价日期 ,直接从询价单中复制", width = 30, dateFormat = "yyyy-MM-dd")
    private Date inquiryLeatestUpdatedTime;

    /**
     * 这个服务的实例是什么（去哪张表里查询）
     */
    @Excel(name = "这个服务的实例是什么", readConverterExp = "去=哪张表里查询")
    private String serviceBelongTo;

    private String paymentTitleCode;

    private String inquiryNotice;

    private String inquiryInnerRemark;

    private Long inquiryPsaId;

    private String logisticsPaymentTermsCode;


    /**
     * 业务确认 ,按条目确认
     */
    @Excel(name = "业务确认 ,按条目确认")
    private Long isDnSalesConfirmed;

    /**
     * 客户确认 ,按条目确认
     */
    @Excel(name = "客户确认 ,按条目确认")
    private Long isDnClientConfirmed;

    /**
     * 操作确认 ,按条目确认
     */
    @Excel(name = "操作确认 ,按条目确认")
    private Long isDnOpConfirmed;

    /**
     * 商务确认 ,
     */
    @Excel(name = "商务确认 ,")
    private Long isDnPsaConfirmed;

    /**
     * 供应商确认 ,
     */
    @Excel(name = "供应商确认 ,")
    private Long isDnSupplierConfirmed;

    /**
     * 财务审核 ,
     */
    @Excel(name = "财务审核 ,")
    private Long isAccountConfirmed;

    /**
     * 财务审核人 ,
     */
    @Excel(name = "财务审核人 ,")
    private Long confirmAccountId;

    /**
     * 财务审核时间 ,
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "财务审核时间 ,", width = 30, dateFormat = "yyyy-MM-dd")
    private Date accountConfirmTime;

    /** 业务确认时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "业务确认时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date salesConfirmedTime;

    /** 客户确认时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "客户确认时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date clientConfirmedTime;

    /** 操作确认时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "操作确认时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date opConfirmedTime;

    /** 商务确认时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "商务确认时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date psaConfirmedTime;

    /** 供应商确认时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "供应商确认时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date supplierConfirmedTime;
    private String supplierName;
    private String serviceFold;
    private String serviceDetailsCode;

    public String getServiceDetailsCode() {
        return serviceDetailsCode;
    }

    public void setServiceDetailsCode(String serviceDetailsCode) {
        this.serviceDetailsCode = serviceDetailsCode;
    }

    public String getServiceFold() {
        return serviceFold;
    }

    public void setServiceFold(String serviceFold) {
        this.serviceFold = serviceFold;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public Date getOpConfirmedTime() {
        return opConfirmedTime;
    }

    public void setOpConfirmedTime(Date opConfirmedTime) {
        this.opConfirmedTime = opConfirmedTime;
    }

    public Date getSalesConfirmedTime() {
        return salesConfirmedTime;
    }

    public void setSalesConfirmedTime(Date salesConfirmedTime) {
        this.salesConfirmedTime = salesConfirmedTime;
    }

    public Date getClientConfirmedTime() {
        return clientConfirmedTime;
    }

    public void setClientConfirmedTime(Date clientConfirmedTime) {
        this.clientConfirmedTime = clientConfirmedTime;
    }

    public Date getPsaConfirmedTime() {
        return psaConfirmedTime;
    }

    public void setPsaConfirmedTime(Date psaConfirmedTime) {
        this.psaConfirmedTime = psaConfirmedTime;
    }

    public Date getSupplierConfirmedTime() {
        return supplierConfirmedTime;
    }

    public void setSupplierConfirmedTime(Date supplierConfirmedTime) {
        this.supplierConfirmedTime = supplierConfirmedTime;
    }

    public Long getIsDnSalesConfirmed() {
        return isDnSalesConfirmed;
    }

    public void setIsDnSalesConfirmed(Long isDnSalesConfirmed) {
        this.isDnSalesConfirmed = isDnSalesConfirmed;
    }

    public Long getIsDnClientConfirmed() {
        return isDnClientConfirmed;
    }

    public void setIsDnClientConfirmed(Long isDnClientConfirmed) {
        this.isDnClientConfirmed = isDnClientConfirmed;
    }

    public Long getIsDnOpConfirmed() {
        return isDnOpConfirmed;
    }

    public void setIsDnOpConfirmed(Long isDnOpConfirmed) {
        this.isDnOpConfirmed = isDnOpConfirmed;
    }

    public Long getIsDnPsaConfirmed() {
        return isDnPsaConfirmed;
    }

    public void setIsDnPsaConfirmed(Long isDnPsaConfirmed) {
        this.isDnPsaConfirmed = isDnPsaConfirmed;
    }

    public Long getIsDnSupplierConfirmed() {
        return isDnSupplierConfirmed;
    }

    public void setIsDnSupplierConfirmed(Long isDnSupplierConfirmed) {
        this.isDnSupplierConfirmed = isDnSupplierConfirmed;
    }

    public Long getIsAccountConfirmed() {
        return isAccountConfirmed;
    }

    public void setIsAccountConfirmed(Long isAccountConfirmed) {
        this.isAccountConfirmed = isAccountConfirmed;
    }

    public Long getConfirmAccountId() {
        return confirmAccountId;
    }

    public void setConfirmAccountId(Long confirmAccountId) {
        this.confirmAccountId = confirmAccountId;
    }

    public Date getAccountConfirmTime() {
        return accountConfirmTime;
    }

    public void setAccountConfirmTime(Date accountConfirmTime) {
        this.accountConfirmTime = accountConfirmTime;
    }

    public Long getRctId() {
        return rctId;
    }

    public void setRctId(Long rctId) {
        this.rctId = rctId;
    }

    public String getLogisticsPaymentTermsCode() {
        return logisticsPaymentTermsCode;
    }

    public void setLogisticsPaymentTermsCode(String logisticsPaymentTermsCode) {
        this.logisticsPaymentTermsCode = logisticsPaymentTermsCode;
    }

    public String getPaymentTitleCode() {
        return paymentTitleCode;
    }

    public void setPaymentTitleCode(String paymentTitleCode) {
        this.paymentTitleCode = paymentTitleCode;
    }

    public String getInquiryNotice() {
        return inquiryNotice;
    }

    public void setInquiryNotice(String inquiryNotice) {
        this.inquiryNotice = inquiryNotice;
    }

    public String getInquiryInnerRemark() {
        return inquiryInnerRemark;
    }

    public void setInquiryInnerRemark(String inquiryInnerRemark) {
        this.inquiryInnerRemark = inquiryInnerRemark;
    }

    public Long getInquiryPsaId() {
        return inquiryPsaId;
    }

    public void setInquiryPsaId(Long inquiryPsaId) {
        this.inquiryPsaId = inquiryPsaId;
    }

    public Long getServiceId() {
        return serviceId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    public Long getServiceTypeId() {
        return serviceTypeId;
    }

    public void setServiceTypeId(Long serviceTypeId) {
        this.serviceTypeId = serviceTypeId;
    }

    public String getRctNo() {
        return rctNo;
    }

    public void setRctNo(String rctNo) {
        this.rctNo = rctNo;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierSummary() {
        return supplierSummary;
    }

    public void setSupplierSummary(String supplierSummary) {
        this.supplierSummary = supplierSummary;
    }

    public String getSupplierContact() {
        return supplierContact;
    }

    public void setSupplierContact(String supplierContact) {
        this.supplierContact = supplierContact;
    }

    public String getSupplierTel() {
        return supplierTel;
    }

    public void setSupplierTel(String supplierTel) {
        this.supplierTel = supplierTel;
    }

    public String getInquiryNo() {
        return inquiryNo;
    }

    public void setInquiryNo(String inquiryNo) {
        this.inquiryNo = inquiryNo;
    }

    public String getAgreementTypeCode() {
        return agreementTypeCode;
    }

    public void setAgreementTypeCode(String agreementTypeCode) {
        this.agreementTypeCode = agreementTypeCode;
    }

    public String getAgreementNo() {
        return agreementNo;
    }

    public void setAgreementNo(String agreementNo) {
        this.agreementNo = agreementNo;
    }

    public BigDecimal getMaxWeight() {
        return maxWeight;
    }

    public void setMaxWeight(BigDecimal maxWeight) {
        this.maxWeight = maxWeight;
    }

    public Date getInquiryLeatestUpdatedTime() {
        return inquiryLeatestUpdatedTime;
    }

    public void setInquiryLeatestUpdatedTime(Date inquiryLeatestUpdatedTime) {
        this.inquiryLeatestUpdatedTime = inquiryLeatestUpdatedTime;
    }

    public String getServiceBelongTo() {
        return serviceBelongTo;
    }

    public void setServiceBelongTo(String serviceBelongTo) {
        this.serviceBelongTo = serviceBelongTo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("serviceId", getServiceId())
                .append("serviceTypeId", getServiceTypeId())
                .append("rctNo", getRctNo())
                .append("supplierId", getSupplierId())
                .append("supplierSummary", getSupplierSummary())
                .append("supplierContact", getSupplierContact())
                .append("supplierTel", getSupplierTel())
                .append("inquiryNo", getInquiryNo())
                .append("agreementTypeCode", getAgreementTypeCode())
                .append("agreementNo", getAgreementNo())
                .append("maxWeight", getMaxWeight())
                .append("inquiryLeatestUpdatedTime", getInquiryLeatestUpdatedTime())
                .append("serviceBelongTo", getServiceBelongTo())
                .toString();
    }
}
