package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

/**
 * 
 * @TableName bas_dept_position
 */
public class BasPosition extends BaseEntity {
    /**
     * 职位ID
     */
    @Excel(name = "岗位序号", cellType = Excel.ColumnType.NUMERIC)
    private Long positionId;


    /**
     * 职位全程
     */
    @Excel(name = "岗位全称")
    private String positionLocalName;

    /**
     * 职位英语名称
     */
    @Excel(name = "岗位英文名")
    private String positionEnName;

    /**
     * 职位排序
     */
    private Integer positionSort;

    private String status;

    private String positionQuery;

    private Integer level;

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    private static final long serialVersionUID = 1L;

    public String getPositionQuery() {
        return positionQuery;
    }

    public void setPositionQuery(String positionQuery) {
        this.positionQuery = positionQuery;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 职位ID
     */
    public Long getPositionId() {
        return positionId;
    }

    /**
     * 职位ID
     */
    public void setPositionId(Long positionId) {
        this.positionId = positionId;
    }


    /**
     * 职位全程
     */
    public String getPositionLocalName() {
        return positionLocalName;
    }

    /**
     * 职位全程
     */
    public void setPositionLocalName(String positionLocalName) {
        this.positionLocalName = positionLocalName;
    }

    /**
     * 职位英语名称
     */
    public String getPositionEnName() {
        return positionEnName;
    }

    /**
     * 职位英语名称
     */
    public void setPositionEnName(String positionEnName) {
        this.positionEnName = positionEnName;
    }

    /**
     * 职位排序
     */
    public Integer getPositionSort() {
        return positionSort;
    }

    /**
     * 职位排序
     */
    public void setPositionSort(Integer positionSort) {
        this.positionSort = positionSort;
    }

}