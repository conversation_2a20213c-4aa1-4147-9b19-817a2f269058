---
description:
globs:
alwaysApply: false
---
# 前端架构与开发规范 (rich-ui)

前端基于 Vue2 + Element-UI 构建，采用 Vue CLI 脚手架，使用 ES6+ 语法标准。

## 项目结构

```
rich-ui/
├── src/                # 源代码主目录
│   ├── api/            # API接口请求模块
│   ├── assets/         # 静态资源文件
│   ├── components/     # 全局通用组件
│   ├── directive/      # 自定义指令
│   ├── layout/         # 布局组件
│   ├── router/         # 路由配置
│   ├── store/          # Vuex状态管理
│   ├── utils/          # 工具函数库
│   ├── views/          # 页面视图组件
│   ├── App.vue         # 根组件
│   ├── main.js         # 入口文件
│   ├── permission.js   # 权限控制
│   └── settings.js     # 全局配置
├── public/             # 静态资源目录
└── package.json        # 项目依赖配置
```

## 主要开发规范

### 组件开发规范
- 组件文件名：采用 kebab-case (短横线)命名法
- 基础组件使用 `Base` 前缀
- 单一职责原则：每个组件只做一件事
- Props 定义需要类型和默认值
- 样式作用域使用 scoped 或 CSS Module

### 代码风格规范
- 使用 Options API 风格开发组件
- 组件选项顺序: data, props, computed, watch, methods, lifecycle hooks
- 方法命名采用小驼峰 (camelCase)
- 事件命名使用 kebab-case (如: this.$emit('item-click'))
- 组件模板中使用连字符命名 (kebab-case)

### API 调用规范
- 所有 API 请求集中在 api 目录下按模块组织
- 使用封装的 axios 实例处理请求
- 统一处理请求/响应拦截
- 错误处理统一管理

### 路由规范
- 路由配置按功能模块组织
- 使用路由元信息 (meta) 控制路由权限
- 懒加载优化性能

### 状态管理规范
- Vuex Store 按模块拆分
- Mutations 遵循常量命名方式
- 异步操作放在 Actions 中处理
- Getters 用于派生计算属性

### 样式规范
- 使用 SCSS 预处理器
- 全局样式变量集中管理
- 布局优先使用 flex 或 grid
- 组件样式隔离使用 scoped

### 性能优化规范
- 路由懒加载
- 组件按需引入
- 列表数据分页
- 大数据量表格虚拟滚动
- 避免不必要的计算和渲染

### 文件引用规范
- 使用 @ 别名引用 src 目录下的文件
- 相对路径不超过两级，否则使用别名

### Element-UI 使用规范
- 表单校验统一使用 Element 表单验证
- 消息提示统一使用 Element Message 组件
- 弹窗确认使用 MessageBox 组件
- 表格分页、搜索、排序遵循统一交互模式
