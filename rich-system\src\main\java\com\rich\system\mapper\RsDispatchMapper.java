package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsDispatch;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 尾程运输Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@Mapper
public interface RsDispatchMapper {
    /**
     * 查询尾程运输
     *
     * @param dispatchId 尾程运输主键
     * @return 尾程运输
     */
    RsDispatch selectRsDispatchByDispatchId(Long dispatchId);

    /**
     * 查询尾程运输列表
     *
     * @param rsDispatch 尾程运输
     * @return 尾程运输集合
     */
    List<RsDispatch> selectRsDispatchList(RsDispatch rsDispatch);

    /**
     * 新增尾程运输
     *
     * @param rsDispatch 尾程运输
     * @return 结果
     */
    int insertRsDispatch(RsDispatch rsDispatch);

    /**
     * 修改尾程运输
     *
     * @param rsDispatch 尾程运输
     * @return 结果
     */
    int updateRsDispatch(RsDispatch rsDispatch);

    /**
     * 删除尾程运输
     *
     * @param dispatchId 尾程运输主键
     * @return 结果
     */
    int deleteRsDispatchByDispatchId(Long dispatchId);

    /**
     * 批量删除尾程运输
     *
     * @param dispatchIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsDispatchByDispatchIds(Long[] dispatchIds);
}
