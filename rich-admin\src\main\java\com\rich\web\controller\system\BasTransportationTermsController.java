package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasTransportationTerms;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasTransportationTermsService;
import com.rich.system.service.impl.RedisCacheImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 运输条款Controller
 *
 * <AUTHOR>
 * @date 2023-05-05
 */
@RestController
@RequestMapping("/system/transportationterms")
public class BasTransportationTermsController extends BaseController {
    @Autowired
    private  BasTransportationTermsService basTransportationTermsService;

    @Autowired
    private  RedisCache redisCache;

    @Autowired
    private RedisCacheImpl RedisCache;

    /**
     * 查询运输条款列表
     */
    @PreAuthorize("@ss.hasPermi('system:transportationterms:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasTransportationTerms basTransportationTerms) {
        startPage();
        List<BasTransportationTerms> list = basTransportationTermsService.selectBasTransportationTermsList(basTransportationTerms);
        return getDataTable(list);
    }

    @GetMapping("/selectList")
    public AjaxResult selectList() {
        List<BasTransportationTerms> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "transportationTerms");
        if (list == null) {
            RedisCache.transportationTerms();
            list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "transportationTerms");
        }
        return AjaxResult.success(list);
    }

    /**
     * 导出运输条款列表
     */
    @PreAuthorize("@ss.hasPermi('system:transportationterms:export')")
    @Log(title = "运输条款", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasTransportationTerms basTransportationTerms) {
        List<BasTransportationTerms> list = basTransportationTermsService.selectBasTransportationTermsList(basTransportationTerms);
        ExcelUtil<BasTransportationTerms> util = new ExcelUtil<BasTransportationTerms>(BasTransportationTerms.class);
        util.exportExcel(response, list, "运输条款数据");
    }

    /**
     * 获取运输条款详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:transportationterms:query')")
    @GetMapping(value = "/{transportationTermsId}")
    public AjaxResult getInfo(@PathVariable("transportationTermsId") Long transportationTermsId) {
        return AjaxResult.success(basTransportationTermsService.selectBasTransportationTermsByTransportationTermsId(transportationTermsId));
    }

    /**
     * 新增运输条款
     */
    @PreAuthorize("@ss.hasPermi('system:transportationterms:add')")
    @Log(title = "运输条款", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasTransportationTerms basTransportationTerms) {
        return toAjax(basTransportationTermsService.insertBasTransportationTerms(basTransportationTerms));
    }

    /**
     * 修改运输条款
     */
    @PreAuthorize("@ss.hasPermi('system:transportationterms:edit')")
    @Log(title = "运输条款", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasTransportationTerms basTransportationTerms) {
        return toAjax(basTransportationTermsService.updateBasTransportationTerms(basTransportationTerms));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:transportationterms:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasTransportationTerms basTransportationTerms) {
        basTransportationTerms.setUpdateBy(getUserId());
        return toAjax(basTransportationTermsService.changeStatus(basTransportationTerms));
    }

    /**
     * 删除运输条款
     */
    @PreAuthorize("@ss.hasPermi('system:transportationterms:remove')")
    @Log(title = "运输条款", businessType = BusinessType.DELETE)
    @DeleteMapping("/{transportationTermsIds}")
    public AjaxResult remove(@PathVariable Long[] transportationTermsIds) {
        return toAjax(basTransportationTermsService.deleteBasTransportationTermsByTransportationTermsIds(transportationTermsIds));
    }
}
