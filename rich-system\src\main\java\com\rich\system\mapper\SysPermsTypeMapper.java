package com.rich.system.mapper;

import com.rich.common.core.domain.entity.SysPermsType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 权限类型Mapper接口
 *
 * <AUTHOR>
 * @date 2023-03-21
 */
@Mapper
public interface SysPermsTypeMapper {
    /**
     * 查询权限类型
     *
     * @param permsId 权限类型主键
     * @return 权限类型
     */
    SysPermsType selectSysPermsTypeByPermsId(Long permsId);

    /**
     * 查询权限类型列表
     *
     * @param sysPermsType 权限类型
     * @return 权限类型集合
     */
    List<SysPermsType> selectSysPermsTypeList(SysPermsType sysPermsType);

    /**
     * 新增权限类型
     *
     * @param sysPermsType 权限类型
     * @return 结果
     */
    int insertSysPermsType(SysPermsType sysPermsType);

    /**
     * 修改权限类型
     *
     * @param sysPermsType 权限类型
     * @return 结果
     */
    int updateSysPermsType(SysPermsType sysPermsType);

    /**
     * 删除权限类型
     *
     * @param permsId 权限类型主键
     * @return 结果
     */
    int deleteSysPermsTypeByPermsId(Long permsId);

    /**
     * 批量删除权限类型
     *
     * @param permsIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteSysPermsTypeByPermsIds(Long[] permsIds);
}
