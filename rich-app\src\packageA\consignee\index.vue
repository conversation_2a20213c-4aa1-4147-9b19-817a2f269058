<template>
  <view class="container">
    <!-- 客户信息 -->
    <view v-if="mpWarehouseClient" class="client-info-container">
      <view class="client-header">
        <view class="header-left">
          <view class="client-avatar">{{ mpWarehouseClient.clientCode.charAt(0) }}</view>
          <view class="client-title">
            <text class="client-code">{{ mpWarehouseClient.clientCode }}</text>
            <text class="client-region">{{ mpWarehouseClient.clientRegion }}</text>
          </view>
        </view>
      </view>

      <view class="client-details">
        <view class="detail-item">
          <view class="detail-icon">
            <uni-icons color="#3c9cff" size="18" type="shop"></uni-icons>
          </view>
          <view class="detail-content">
            <text class="detail-label">公司名称</text>
            <text class="detail-value">{{ mpWarehouseClient.destinationCompanyName }}</text>
          </view>
        </view>

        <view class="detail-item">
          <view class="detail-icon">
            <uni-icons color="#3c9cff" size="18" type="location"></uni-icons>
          </view>
          <view class="detail-content">
            <text class="detail-label">仓库地址</text>
            <text class="detail-value">{{ mpWarehouseClient.destinationWarehouseAddress }}</text>
          </view>
        </view>

        <view class="detail-item">
          <view class="detail-icon">
            <uni-icons color="#3c9cff" size="18" type="phone"></uni-icons>
          </view>
          <view class="detail-content">
            <text class="detail-label">联系电话</text>
            <text class="detail-value">{{ mpWarehouseClient.destinationWarehouseContact }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 搜索区域 -->
    <view class="search-box">
      <uni-search-bar v-model="searchValue" placeholder="请输入收货人代码或名称" @confirm="search"/>
    </view>

    <!-- 操作按钮区域 -->
    <view class="action-buttons">
      <button size="mini" type="primary" @click="handleAdd">新增</button>
      <button size="mini" type="default" @click="refreshList">刷新</button>
    </view>

    <!-- 列表区域 -->
    <view class="consignee-list">
      <uni-list v-if="consigneeList.length > 0">
        <uni-list-item v-for="(item, index) in consigneeList" :key="index"
                       :note="item.consigneeTel + '  ' + item.remark"
                       :rightText="item.clientRegion"
                       :title="item.clientCode + '-' + item.consigneeCode + '-' + item.consigneeName" link
                       @click="handleDetail(item)"/>
      </uni-list>
      <view v-else class="empty-tip">
        <uni-icons size="30" type="info"></uni-icons>
        <text>暂无收货人数据</text>
      </view>
    </view>

    <!-- 分页或加载更多 -->
    <uni-load-more :status="loadMoreStatus"/>
  </view>
</template>

<script>
import uniSearchBar from '../components/uni-search-bar/uni-search-bar.vue'
import uniList from '../components/uni-list/uni-list.vue'
import uniListItem from '../components/uni-list-item/uni-list-item.vue'
import uniLoadMore from '../components/uni-load-more/uni-load-more.vue'
import {getConsigneeList} from '@/api/system/consignee.js'
import {checkRole} from '@/utils/permission'

export default {
  components: {
    uniSearchBar,
    uniList,
    uniListItem,
    uniLoadMore
  },
  data() {
    return {
      mpWarehouseClient: this.$store.state.user.mpWarehouseClient,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        code: '',
        name: ''
      },
      // 搜索值
      searchValue: '',
      // 收货人列表
      consigneeList: [],
      // 总记录数
      total: 0,
      // 加载状态
      loadMoreStatus: 'more',
      // 是否还有更多数据
      hasMoreData: true
    }
  },
  onLoad() {
    this.getList()
  },
  onPullDownRefresh() {
    this.refreshList()
    setTimeout(() => {
      uni.stopPullDownRefresh()
    }, 1000)
  },
  onReachBottom() {
    if (this.hasMoreData) {
      this.queryParams.pageNum++
      this.loadMoreStatus = 'loading'
      this.getList()
    }
  },
  methods: {
    checkRole,
    // 获取列表数据
    getList() {
      this.loadMoreStatus = 'loading'
      if (checkRole(['client'])) {
        this.queryParams.clientCode = this.$store.state.user.mpWarehouseClient.clientCode
      }
      if (checkRole(['warehouse'])) {
        this.queryParams.clientCode = null
      }
      getConsigneeList(this.queryParams).then(res => {
        const data = res.rows || []
        if (this.queryParams.pageNum === 1) {
          this.consigneeList = data
        } else {
          this.consigneeList = [...this.consigneeList, ...data]
        }
        this.total = res.total
        this.hasMoreData = this.consigneeList.length < this.total
        this.loadMoreStatus = this.hasMoreData ? 'more' : 'noMore'
      }).catch(() => {
        this.loadMoreStatus = 'more'
      })
    },
    // 搜索
    search() {
      this.queryParams.pageNum = 1
      this.queryParams.code = this.searchValue
      this.queryParams.name = this.searchValue
      this.getList()
    },
    // 刷新列表
    refreshList() {
      this.queryParams.pageNum = 1
      this.searchValue = ''
      this.queryParams.code = ''
      this.queryParams.name = ''
      this.getList()
    },
    // 新增收货人
    handleAdd() {
      uni.navigateTo({
        url: '/packageA/consignee/add'
      })
    },
    // 查看详情
    handleDetail(item) {
      uni.navigateTo({
        url: '/packageA/consignee/add?consigneeId=' + item.consigneeId
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;

  .search-box {
    margin-bottom: 20rpx;
  }

  .action-buttons {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20rpx;

    button {
      margin: 0 10rpx;
    }
  }

  .consignee-list {
    margin-bottom: 20rpx;
  }

  .empty-tip {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;
    color: #999;

    text {
      margin-top: 20rpx;
    }
  }

  .client-info-container {
    background-color: #ffffff;
    border-radius: 12rpx;
    box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.08);
    margin: 20rpx;
    overflow: hidden;
  }

  .client-header {
    background: linear-gradient(to right, #3c9cff, #5dabff);
    color: #ffffff;
    padding: 30rpx 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-left {
      display: flex;
      align-items: center;

      .client-avatar {
        width: 80rpx;
        height: 80rpx;
        background-color: #ffffff;
        color: #3c9cff;
        font-size: 36rpx;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        margin-right: 20rpx;
      }

      .client-title {
        display: flex;
        flex-direction: column;

        .client-code {
          font-size: 32rpx;
          font-weight: bold;
          margin-bottom: 8rpx;
        }

        .client-region {
          font-size: 24rpx;
          opacity: 0.8;
        }
      }
    }
  }

  .client-details {
    padding: 20rpx;

    .detail-item {
      display: flex;
      padding: 16rpx 10rpx;
      border-bottom: 1rpx solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .detail-icon {
        margin-right: 20rpx;
        display: flex;
        align-items: center;
      }

      .detail-content {
        flex: 1;
        display: flex;
        flex-direction: column;

        .detail-label {
          font-size: 24rpx;
          color: #999;
          margin-bottom: 6rpx;
        }

        .detail-value {
          font-size: 28rpx;
          color: #333;
          word-break: break-all;
        }
      }
    }
  }
}
</style>
