package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsWarehouse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsWarehouseService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 仓储服务Controller
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@RestController
@RequestMapping("/system/warehouse")
public class RsWarehouseController extends BaseController {
    @Autowired
    private RsWarehouseService rsWarehouseService;

    /**
     * 查询仓储服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:warehouse:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsWarehouse rsWarehouse) {
        startPage();
        List<RsWarehouse> list = rsWarehouseService.selectRsWarehouseList(rsWarehouse);
        return getDataTable(list);
    }

    /**
     * 导出仓储服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:warehouse:export')")
    @Log(title = "仓储服务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsWarehouse rsWarehouse) {
        List<RsWarehouse> list = rsWarehouseService.selectRsWarehouseList(rsWarehouse);
        ExcelUtil<RsWarehouse> util = new ExcelUtil<RsWarehouse>(RsWarehouse.class);
        util.exportExcel(response, list, "仓储服务数据");
    }

    /**
     * 获取仓储服务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:warehouse:query')")
    @GetMapping(value = "/{warehouseId}")
    public AjaxResult getInfo(@PathVariable("warehouseId") Long warehouseId) {
        return AjaxResult.success(rsWarehouseService.selectRsWarehouseByWarehouseId(warehouseId));
    }

    /**
     * 新增仓储服务
     */
    @PreAuthorize("@ss.hasPermi('system:warehouse:add')")
    @Log(title = "仓储服务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsWarehouse rsWarehouse) {
        return toAjax(rsWarehouseService.insertRsWarehouse(rsWarehouse));
    }

    /**
     * 修改仓储服务
     */
    @PreAuthorize("@ss.hasPermi('system:warehouse:edit')")
    @Log(title = "仓储服务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsWarehouse rsWarehouse) {
        return toAjax(rsWarehouseService.updateRsWarehouse(rsWarehouse));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:warehouse:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsWarehouse rsWarehouse) {
        rsWarehouse.setUpdateBy(getUserId());
        return toAjax(rsWarehouseService.changeStatus(rsWarehouse));
    }

    /**
     * 删除仓储服务
     */
    @PreAuthorize("@ss.hasPermi('system:warehouse:remove')")
    @Log(title = "仓储服务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{warehouseIds}")
    public AjaxResult remove(@PathVariable Long[] warehouseIds) {
        return toAjax(rsWarehouseService.deleteRsWarehouseByWarehouseIds(warehouseIds));
    }
}
