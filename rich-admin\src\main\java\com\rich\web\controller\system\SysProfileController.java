package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.config.RichConfig;
import com.rich.common.constant.UserConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.RsStaff;
import com.rich.common.core.domain.model.LoginUser;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.StringUtils;
import com.rich.common.utils.file.FileUploadUtils;
import com.rich.common.utils.file.MimeTypeUtils;
import com.rich.framework.web.service.TokenService;
import com.rich.system.service.RsStaffService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 个人信息 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/user/profile")

public class SysProfileController extends BaseController {

    @Autowired
    private  RsStaffService userService;

    @Autowired
    private  TokenService tokenService;

    /**
     * 个人信息
     */
    @GetMapping
    public AjaxResult profile() {
        LoginUser loginUser = getLoginUser();
        RsStaff user = loginUser.getUser();
        return AjaxResult.success(user);
    }

    /**
     * 修改用户
     */
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult updateProfile(@RequestBody RsStaff user) {
        LoginUser loginUser = getLoginUser();
        RsStaff sysUser = loginUser.getUser();
        user.setStaffUsername(sysUser.getStaffUsername());
        if (StringUtils.isNotEmpty(user.getStaffPhoneNum())
                && !userService.checkPhoneUnique(user)) {
            return AjaxResult.error("修改用户'" + user.getStaffUsername() + "'失败，手机号码已存在");
        }
        if (StringUtils.isNotEmpty(user.getStaffEmailEnterprise())
                && !userService.checkEmailUnique(user)) {
            return AjaxResult.error("修改用户'" + user.getStaffUsername() + "'失败，邮箱账号已存在");
        }
        user.setStaffId(sysUser.getStaffId());
        user.setStaffPassword(null);
        user.setStaffAvatar(null);
        user.setSqdDeptId(null);
        if (userService.updateUserProfile(user) > 0) {
            // 更新缓存用户信息
            sysUser.setStaffShortName(user.getStaffShortName());
            sysUser.setStaffPhoneNum(user.getStaffPhoneNum());
            sysUser.setStaffEmailEnterprise(user.getStaffEmailEnterprise());
            sysUser.setStaffGender(user.getStaffGender());
            tokenService.setLoginUser(loginUser);
            return AjaxResult.success();
        }
        return AjaxResult.error("修改个人信息异常，请联系管理员");
    }

    /**
     * 重置密码
     */
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping("/updatePwd")
    public AjaxResult updatePwd(String oldPassword, String newPassword) {
        LoginUser loginUser = getLoginUser();
        String userName = loginUser.getUsername();
        String password = loginUser.getPassword();
        if (!SecurityUtils.matchesPassword(oldPassword, password)) {
            return AjaxResult.error("修改密码失败，旧密码错误");
        }
        if (SecurityUtils.matchesPassword(newPassword, password)) {
            return AjaxResult.error("新密码不能与旧密码相同");
        }
        if (userService.resetUserPwd(userName, SecurityUtils.encryptPassword(newPassword)) > 0) {
            // 更新缓存用户密码
            loginUser.getUser().setStaffPassword(SecurityUtils.encryptPassword(newPassword));
            tokenService.setLoginUser(loginUser);
            return AjaxResult.success();
        }
        return AjaxResult.error("修改密码异常，请联系管理员");
    }

    /**
     * 头像上传
     */
    @Log(title = "用户头像", businessType = BusinessType.UPDATE)
    @PostMapping("/avatar")
    public AjaxResult avatar(@RequestParam("avatarfile") MultipartFile file) throws Exception {
        if (!file.isEmpty()) {
            LoginUser loginUser = getLoginUser();
            String avatar = FileUploadUtils.upload(RichConfig.getAvatarPath(), file, MimeTypeUtils.IMAGE_EXTENSION);
            if (userService.updateUserAvatar(loginUser.getUsername(), avatar)) {
                AjaxResult ajax = AjaxResult.success();
                ajax.put("imgUrl", avatar);
                // 更新缓存用户头像
                loginUser.getUser().setStaffAvatar(avatar);
                tokenService.setLoginUser(loginUser);
                return ajax;
            }
        }
        return AjaxResult.error("上传图片异常，请联系管理员");
    }
}
