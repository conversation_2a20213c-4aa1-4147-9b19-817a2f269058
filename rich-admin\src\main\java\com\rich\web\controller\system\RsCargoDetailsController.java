package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsCargoDetails;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsCargoDetailsService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 客户在仓货物明细Controller
 *
 * <AUTHOR>
 * @date 2024-11-11
 */
@RestController
@RequestMapping("/system/cargodetails")
public class RsCargoDetailsController extends BaseController {
    @Autowired
    private RsCargoDetailsService rsCargoDetailsService;

    /**
     * 查询客户在仓货物明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:cargodetails:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsCargoDetails rsCargoDetails) {
        startPage();
        List<RsCargoDetails> list = rsCargoDetailsService.selectRsCargoDetailsList(rsCargoDetails);
        return getDataTable(list);
    }

    /**
     * 导出客户在仓货物明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:cargodetails:export')")
    @Log(title = "客户在仓货物明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsCargoDetails rsCargoDetails) {
        List<RsCargoDetails> list = rsCargoDetailsService.selectRsCargoDetailsList(rsCargoDetails);
        ExcelUtil<RsCargoDetails> util = new ExcelUtil<RsCargoDetails>(RsCargoDetails.class);
        util.exportExcel(response, list, "客户在仓货物明细数据");
    }

    /**
     * 获取客户在仓货物明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:cargodetails:query')")
    @GetMapping(value = "/{cargoDetailsId}")
    public AjaxResult getInfo(@PathVariable("cargoDetailsId") Long cargoDetailsId) {
        return AjaxResult.success(rsCargoDetailsService.selectRsCargoDetailsByCargoDetailsId(cargoDetailsId));
    }

    /**
     * 新增客户在仓货物明细
     */
    @PreAuthorize("@ss.hasPermi('system:cargodetails:add')")
    @Log(title = "客户在仓货物明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsCargoDetails rsCargoDetails) {
        return toAjax(rsCargoDetailsService.insertRsCargoDetails(rsCargoDetails));
    }

    /**
     * 修改客户在仓货物明细
     */
    @PreAuthorize("@ss.hasPermi('system:cargodetails:edit')")
    @Log(title = "客户在仓货物明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsCargoDetails rsCargoDetails) {
        return toAjax(rsCargoDetailsService.updateRsCargoDetails(rsCargoDetails));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:cargodetails:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsCargoDetails rsCargoDetails) {
        rsCargoDetails.setUpdateBy(getUserId());
        return toAjax(rsCargoDetailsService.changeStatus(rsCargoDetails));
    }

    /**
     * 删除客户在仓货物明细
     */
    @PreAuthorize("@ss.hasPermi('system:cargodetails:remove')")
    @Log(title = "客户在仓货物明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{cargoDetailsIds}")
    public AjaxResult remove(@PathVariable Long[] cargoDetailsIds) {
        return toAjax(rsCargoDetailsService.deleteRsCargoDetailsByCargoDetailsIds(cargoDetailsIds));
    }
}
