package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasProcess;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 进程名称Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-05
 */
@Mapper
public interface BasProcessMapper {
    /**
     * 查询进程名称
     *
     * @param processId 进程名称主键
     * @return 进程名称
     */
    BasProcess selectBasProcessByProcessId(Long processId);

    /**
     * 查询进程名称列表
     *
     * @param basProcess 进程名称
     * @return 进程名称集合
     */
    List<BasProcess> selectBasProcessList(BasProcess basProcess);

    /**
     * 新增进程名称
     *
     * @param basProcess 进程名称
     * @return 结果
     */
    int insertBasProcess(BasProcess basProcess);

    /**
     * 修改进程名称
     *
     * @param basProcess 进程名称
     * @return 结果
     */
    int updateBasProcess(BasProcess basProcess);

    /**
     * 删除进程名称
     *
     * @param processId 进程名称主键
     * @return 结果
     */
    int deleteBasProcessByProcessId(Long processId);

    /**
     * 批量删除进程名称
     *
     * @param processIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasProcessByProcessIds(Long[] processIds);

    List<BasProcess> selectBasProcessByServiceTypeId(Long serviceTypeId);
}
