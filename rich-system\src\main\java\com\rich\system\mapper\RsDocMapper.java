package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsDoc;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 文件信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@Mapper
public interface RsDocMapper {
    /**
     * 查询文件信息
     *
     * @param docId 文件信息主键
     * @return 文件信息
     */
    RsDoc selectRsDocByDocId(Long docId);

    /**
     * 查询文件信息列表
     *
     * @param rsDoc 文件信息
     * @return 文件信息集合
     */
    List<RsDoc> selectRsDocList(RsDoc rsDoc);

    /**
     * 新增文件信息
     *
     * @param rsDoc 文件信息
     * @return 结果
     */
    int insertRsDoc(RsDoc rsDoc);

    /**
     * 修改文件信息
     *
     * @param rsDoc 文件信息
     * @return 结果
     */
    int updateRsDoc(RsDoc rsDoc);

    /**
     * 删除文件信息
     *
     * @param docId 文件信息主键
     * @return 结果
     */
    int deleteRsDocByDocId(Long docId);

    /**
     * 批量删除文件信息
     *
     * @param docIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsDocByDocIds(Long[] docIds);

    int deleteRsDoc(Long serviceInstanceId);
}
