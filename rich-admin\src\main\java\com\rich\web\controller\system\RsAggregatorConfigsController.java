package com.rich.web.controller.system;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.dto.AggregatorConfigDTO;
import com.rich.common.core.domain.entity.RsAggregatorConfigs;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.RsAggregatorConfigsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 数据汇总配置Controller
 *
 * <AUTHOR>
 * @date 2025-02-18
 */
@RestController
@RequestMapping("/system/aggregatorconfigs")
public class RsAggregatorConfigsController extends BaseController {
    @Autowired
    private RsAggregatorConfigsService rsAggregatorConfigsService;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 查询数据汇总配置列表
     */
    @GetMapping("/list")
    public TableDataInfo list(RsAggregatorConfigs rsAggregatorConfigs) {
        rsAggregatorConfigs.setUserId(SecurityUtils.getUserId());
        startPage();
        List<RsAggregatorConfigs> list = rsAggregatorConfigsService.selectRsAggregatorConfigsList(rsAggregatorConfigs);
        return getDataTable(list);
    }

    /**
     * 导出数据汇总配置列表
     */
    @Log(title = "数据汇总配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsAggregatorConfigs rsAggregatorConfigs) {
        List<RsAggregatorConfigs> list = rsAggregatorConfigsService.selectRsAggregatorConfigsList(rsAggregatorConfigs);
        ExcelUtil<RsAggregatorConfigs> util = new ExcelUtil<RsAggregatorConfigs>(RsAggregatorConfigs.class);
        util.exportExcel(response, list, "数据汇总配置数据");
    }

    /**
     * 获取数据汇总配置详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(rsAggregatorConfigsService.selectRsAggregatorConfigsById(id));
    }

    /**
     * 新增数据汇总配置
     */
    @Log(title = "数据汇总配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AggregatorConfigDTO aggregatorConfigDTO) {
        try {
            RsAggregatorConfigs rsAggregatorConfigs = new RsAggregatorConfigs();
            rsAggregatorConfigs.setName(aggregatorConfigDTO.getName());
            rsAggregatorConfigs.setUserId(SecurityUtils.getUserId());
            rsAggregatorConfigs.setConfig(objectMapper.writeValueAsString(aggregatorConfigDTO.getConfig()));
            rsAggregatorConfigs.setConfigType(aggregatorConfigDTO.getType());
            return toAjax(rsAggregatorConfigsService.insertRsAggregatorConfigs(rsAggregatorConfigs));
        } catch (JsonProcessingException e) {
            throw new RuntimeException("保存配置失败");
        }
    }

    /**
     * 修改数据汇总配置
     */
    @Log(title = "数据汇总配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsAggregatorConfigs rsAggregatorConfigs) {
        return toAjax(rsAggregatorConfigsService.updateRsAggregatorConfigs(rsAggregatorConfigs));
    }

    /**
     * 状态状态
     */
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsAggregatorConfigs rsAggregatorConfigs) {
        rsAggregatorConfigs.setUpdateBy(getUserId());
        return toAjax(rsAggregatorConfigsService.changeStatus(rsAggregatorConfigs));
    }

    /**
     * 删除数据汇总配置
     */
    @Log(title = "数据汇总配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(rsAggregatorConfigsService.deleteRsAggregatorConfigsByIds(ids));
    }
}
