package com.rich.system.domain;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 【请填写功能名称】对象 mid_company_role
 *
 * <AUTHOR>
 * @date 2022-09-07
 */
public class MidCompanyRole extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 公司ID
     */
    @Excel(name = "公司ID")
    private Long companyId;

    /**
     * 公司角色
     */
    @Excel(name = "公司角色")
    private Long roleId;

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("companyId", getCompanyId())
                .append("roleId", getRoleId())
                .toString();
    }
}
