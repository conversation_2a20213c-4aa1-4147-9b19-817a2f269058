package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsBookingExportDeclarationBasicInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 订舱单出口报关基础信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-15
 */
@Mapper
public interface RsBookingExportDeclarationBasicInfoMapper {
    /**
     * 查询订舱单出口报关基础信息
     *
     * @return 订舱单出口报关基础信息
     */
    RsBookingExportDeclarationBasicInfo selectRsBookingExportDeclarationBasicInfoByBookingId(Long bookingId);

    /**
     * 查询订舱单出口报关基础信息列表
     *
     * @param rsBookingExportDeclarationBasicInfo 订舱单出口报关基础信息
     * @return 订舱单出口报关基础信息集合
     */
    List<RsBookingExportDeclarationBasicInfo> selectRsBookingExportDeclarationBasicInfoList(RsBookingExportDeclarationBasicInfo rsBookingExportDeclarationBasicInfo);

    /**
     * 新增订舱单出口报关基础信息
     *
     * @param rsBookingExportDeclarationBasicInfo 订舱单出口报关基础信息
     * @return 结果
     */
    int insertRsBookingExportDeclarationBasicInfo(RsBookingExportDeclarationBasicInfo rsBookingExportDeclarationBasicInfo);

    /**
     * 修改订舱单出口报关基础信息
     *
     * @param rsBookingExportDeclarationBasicInfo 订舱单出口报关基础信息
     * @return 结果
     */
    int updateRsBookingExportDeclarationBasicInfo(RsBookingExportDeclarationBasicInfo rsBookingExportDeclarationBasicInfo);

    /**
     * 删除订舱单出口报关基础信息
     *
     * @return 结果
     */
    int deleteRsBookingExportDeclarationBasicInfoById(Long bookingId);

    /**
     * 批量删除订舱单出口报关基础信息
     *
     * @return 结果
     */
    int deleteRsBookingExportDeclarationBasicInfoByIds(Long[] bookingIds);
}
