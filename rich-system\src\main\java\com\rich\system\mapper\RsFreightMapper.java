package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsFreight;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 基础运费Mapper接口
 *
 * <AUTHOR>
 * @date 2023-01-04
 */
@Mapper
public interface RsFreightMapper {
    /**
     * 查询基础运费
     *
     * @param freightId 基础运费主键
     * @return 基础运费
     */
    RsFreight selectRsFreightByFreightId(Long freightId);

    List<RsFreight> selectRsFreightByFreightIds(List<Long> freightId);

    /**
     * 查询基础运费列表
     *
     * @param rsFreight 基础运费
     * @return 基础运费集合
     */
    List<RsFreight> selectRsFreightList(RsFreight rsFreight);

    List<RsFreight> selectFreightList();

    /**
     * 新增基础运费
     *
     * @param rsFreight 基础运费
     * @return 结果
     */
    int insertRsFreight(RsFreight rsFreight);

    /**
     * 修改基础运费
     *
     * @param rsFreight 基础运费
     * @return 结果
     */
    int updateRsFreight(RsFreight rsFreight);
    int updateImportFreight(RsFreight rsFreight);

    /**
     * 删除基础运费
     *
     * @param freightId 基础运费主键
     * @return 结果
     */
    int deleteRsFreightByFreightId(Long freightId);

    /**
     * 批量删除基础运费
     *
     * @param freightIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsFreightByFreightIds(Long[] freightIds);

    int bathRsFreight(List<RsFreight> insertList);
}
