package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasPaymentType;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasPaymentTypeService;
import com.rich.system.service.impl.RedisCacheImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 收付顺序Controller
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@RestController
@RequestMapping("/system/paymenttype")
public class BasPaymentTypeController extends BaseController {
    @Autowired
    private BasPaymentTypeService basPaymentTypeService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisCacheImpl RedisCache;

    /**
     * 查询收付顺序列表
     */
    @PreAuthorize("@ss.hasPermi('system:paymenttype:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasPaymentType basPaymentType) {
        startPage();
        List<BasPaymentType> list = basPaymentTypeService.selectBasPaymentTypeList(basPaymentType);
        return getDataTable(list);
    }

    @GetMapping("/selectList")
    public AjaxResult selectList() {
        List<BasPaymentType> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "paymentType");
        if (list == null) {
            RedisCache.paymentType();
            list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "paymentType");
        }
        return AjaxResult.success(list);
    }

    /**
     * 导出收付顺序列表
     */
    @PreAuthorize("@ss.hasPermi('system:paymenttype:export')")
    @Log(title = "收付顺序", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasPaymentType basPaymentType) {
        List<BasPaymentType> list = basPaymentTypeService.selectBasPaymentTypeList(basPaymentType);
        ExcelUtil<BasPaymentType> util = new ExcelUtil<BasPaymentType>(BasPaymentType.class);
        util.exportExcel(response, list, "收付顺序数据");
    }

    /**
     * 获取收付顺序详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:paymenttype:query')")
    @GetMapping(value = "/{paymentTypeId}")
    public AjaxResult getInfo(@PathVariable("paymentTypeId") Long paymentTypeId) {
        return AjaxResult.success(basPaymentTypeService.selectBasPaymentTypeByPaymentTypeId(paymentTypeId));
    }

    /**
     * 新增收付顺序
     */
    @PreAuthorize("@ss.hasPermi('system:paymenttype:add')")
    @Log(title = "收付顺序", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasPaymentType basPaymentType) {
        return toAjax(basPaymentTypeService.insertBasPaymentType(basPaymentType));
    }

    /**
     * 修改收付顺序
     */
    @PreAuthorize("@ss.hasPermi('system:paymenttype:edit')")
    @Log(title = "收付顺序", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasPaymentType basPaymentType) {
        return toAjax(basPaymentTypeService.updateBasPaymentType(basPaymentType));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:paymenttype:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasPaymentType basPaymentType) {
        basPaymentType.setUpdateBy(getUserId());
        return toAjax(basPaymentTypeService.changeStatus(basPaymentType));
    }

    /**
     * 删除收付顺序
     */
    @PreAuthorize("@ss.hasPermi('system:paymenttype:remove')")
    @Log(title = "收付顺序", businessType = BusinessType.DELETE)
    @DeleteMapping("/{paymentTypeIds}")
    public AjaxResult remove(@PathVariable Long[] paymentTypeIds) {
        return toAjax(basPaymentTypeService.deleteBasPaymentTypeByPaymentTypeIds(paymentTypeIds));
    }
}
