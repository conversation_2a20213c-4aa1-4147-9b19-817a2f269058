<template>
  <view class="agreement-container">
    <view class="agreement-title">隐私政策</view>
    <view class="agreement-content">
      <view class="section">
        <view class="section-title">1. 引言</view>
        <view class="section-content">
          <view class="paragraph">
            欢迎使用瑞旗仓储服务。我们非常重视您的隐私，并致力于保护您的个人信息。本隐私政策旨在向您说明我们如何收集、使用、存储和保护您的个人信息。
          </view>
        </view>
      </view>

      <view class="section">
        <view class="section-title">2. 信息收集</view>
        <view class="section-content">
          <view class="paragraph">2.1 我们可能收集的个人信息包括但不限于：</view>
          <view class="paragraph">- 注册信息：姓名、联系方式、公司信息等</view>
          <view class="paragraph">- 设备信息：设备标识、IP地址、操作系统等</view>
          <view class="paragraph">- 使用数据：访问时间、使用功能、日志信息等</view>
          <view class="paragraph">2.2 我们通过以下方式收集信息：</view>
          <view class="paragraph">- 您直接提供的信息</view>
          <view class="paragraph">- 自动收集的技术信息</view>
          <view class="paragraph">- 第三方合作伙伴共享的信息</view>
        </view>
      </view>

      <view class="section">
        <view class="section-title">3. 信息使用</view>
        <view class="section-content">
          <view class="paragraph">3.1 我们使用收集的信息用于：</view>
          <view class="paragraph">- 提供、维护和改进我们的服务</view>
          <view class="paragraph">- 处理和完成您的交易</view>
          <view class="paragraph">- 与您沟通并回应您的请求</view>
          <view class="paragraph">- 个性化您的体验</view>
          <view class="paragraph">- 保障服务安全和防止欺诈</view>
        </view>
      </view>

      <view class="section">
        <view class="section-title">4. 信息共享</view>
        <view class="section-content">
          <view class="paragraph">4.1 我们不会出售、交易或出租您的个人信息。</view>
          <view class="paragraph">4.2 在以下情况下，我们可能会共享您的信息：</view>
          <view class="paragraph">- 经您同意的共享</view>
          <view class="paragraph">- 与我们的服务提供商共享</view>
          <view class="paragraph">- 法律要求或为保护权利</view>
          <view class="paragraph">- 业务转让相关的共享</view>
        </view>
      </view>

      <view class="section">
        <view class="section-title">5. 信息存储与安全</view>
        <view class="section-content">
          <view class="paragraph">5.1 我们采取合理的技术和组织措施来保护您的个人信息。</view>
          <view class="paragraph">5.2 我们会在法律要求的期限内保留您的个人信息。</view>
        </view>
      </view>

      <view class="section">
        <view class="section-title">6. 您的权利</view>
        <view class="section-content">
          <view class="paragraph">根据适用法律，您可能有权：</view>
          <view class="paragraph">- 访问您的个人信息</view>
          <view class="paragraph">- 更正不准确的信息</view>
          <view class="paragraph">- 删除您的个人信息</view>
          <view class="paragraph">- 限制或反对处理</view>
          <view class="paragraph">- 数据可携带性</view>
        </view>
      </view>

      <view class="section">
        <view class="section-title">7. Cookie和类似技术</view>
        <view class="section-content">
          <view class="paragraph">
            我们使用Cookie和类似技术来收集和存储信息，以提供更好的用户体验，分析使用情况并支持我们的营销工作。
          </view>
        </view>
      </view>

      <view class="section">
        <view class="section-title">8. 儿童隐私</view>
        <view class="section-content">
          <view class="paragraph">
            我们的服务不面向16岁以下的儿童。如果您是父母或监护人，发现您的孩子向我们提供了个人信息，请联系我们。
          </view>
        </view>
      </view>

      <view class="section">
        <view class="section-title">9. 隐私政策更新</view>
        <view class="section-content">
          <view class="paragraph">我们可能会定期更新本隐私政策。更新后的政策将在本应用中发布，并在更新日期生效。</view>
        </view>
      </view>

      <view class="section">
        <view class="section-title">10. 联系我们</view>
        <view class="section-content">
          <view class="paragraph">如果您对本隐私政策有任何疑问或顾虑，请联系我们的数据保护团队。</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {}
  },
  onLoad() {
    uni.setNavigationBarTitle({
      title: '隐私政策'
    })
  }
}
</script>

<style lang="scss" scoped>
.agreement-container {
  padding: 30rpx;

  .agreement-title {
    font-size: 40rpx;
    font-weight: bold;
    text-align: center;
    margin-bottom: 40rpx;
  }

  .agreement-content {
    .section {
      margin-bottom: 30rpx;

      .section-title {
        font-size: 32rpx;
        font-weight: bold;
        margin-bottom: 20rpx;
      }

      .section-content {
        .paragraph {
          font-size: 28rpx;
          line-height: 1.6;
          margin-bottom: 15rpx;
          text-align: justify;
        }
      }
    }
  }
}
</style> 