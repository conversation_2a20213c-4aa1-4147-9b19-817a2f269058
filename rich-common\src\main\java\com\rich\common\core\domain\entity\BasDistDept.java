package com.rich.common.core.domain.entity;

import com.rich.common.core.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
 * @TableName bas_dept
 */
public class BasDistDept extends BaseEntity {
    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 部门父ID
     */
    private Long parentId;

    /**
     * 祖籍列表
     */
    private String ancestors;

    /**
     * 部门状态（0正常 1停用）
     */
    private String status;

    /**
     * 显示顺序
     */
    private Integer orderNum;

    /**
     * 负责人
     */
    private String leaderName;

    /**
     * 部门简称
     */
    private String deptShortName;

    /**
     * 部门全程
     */
    private String deptLocalName;

    /**
     * 部门英语名称
     */
    private String deptEnName;

    private Integer createListNum;

    /**
     * 父部门名称
     */
    private String parentName;

    private String deptQuery;
    /**
     * 子部门
     */
    private List<BasDistDept> children = new ArrayList<>();

    public Integer getCreateListNum() {
        return createListNum;
    }

    public void setCreateListNum(Integer createListNum) {
        this.createListNum = createListNum;
    }

    private static final long serialVersionUID = 1L;

    public BasDistDept() {
    }

    public String getDeptQuery() {
        return deptQuery;
    }

    public void setDeptQuery(String deptQuery) {
        this.deptQuery = deptQuery;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public List<BasDistDept> getChildren() {
        return children;
    }

    public void setChildren(List<BasDistDept> children) {
        this.children = children;
    }

    /**
     * 部门ID
     */
    public Long getDeptId() {
        return deptId;
    }

    /**
     * 部门ID
     */
    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    /**
     * 部门父ID
     */
    public Long getParentId() {
        return parentId;
    }

    /**
     * 部门父ID
     */
    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    /**
     * 祖籍列表
     */
    public String getAncestors() {
        return ancestors;
    }

    /**
     * 祖籍列表
     */
    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }

    /**
     * 部门状态（0正常 1停用）
     */
    public String getStatus() {
        return status;
    }

    /**
     * 部门状态（0正常 1停用）
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 显示顺序
     */
    @NotNull(message = "显示顺序不能为空")
    public Integer getOrderNum() {
        return orderNum;
    }

    /**
     * 显示顺序
     */
    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    /**
     * 负责人
     */
    public String getLeaderName() {
        return leaderName;
    }

    /**
     * 负责人
     */
    public void setLeaderName(String leaderName) {
        this.leaderName = leaderName;
    }

    /**
     * 部门简称
     */
    public String getDeptShortName() {
        return deptShortName;
    }

    /**
     * 部门简称
     */
    public void setDeptShortName(String deptShortName) {
        this.deptShortName = deptShortName;
    }

    /**
     * 部门全程
     */
    @NotBlank(message = "部门名称不能为空")
    @Size(min = 0, max = 30, message = "部门名称长度不能超过30个字符")
    public String getDeptLocalName() {
        return deptLocalName;
    }

    /**
     * 部门全程
     */
    public void setDeptLocalName(String deptLocalName) {
        this.deptLocalName = deptLocalName;
    }

    /**
     * 部门英语名称
     */
    public String getDeptEnName() {
        return deptEnName;
    }

    /**
     * 部门英语名称
     */
    public void setDeptEnName(String deptEnName) {
        this.deptEnName = deptEnName;
    }

}