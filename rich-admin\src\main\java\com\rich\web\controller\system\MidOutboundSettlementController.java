package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.MidOutboundSettlement;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.MidOutboundSettlementService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 仓租结算中间Controller
 *
 * <AUTHOR>
 * @date 2025-02-11
 */
@RestController
@RequestMapping("/system/outboundsettlement")
public class MidOutboundSettlementController extends BaseController {
    @Autowired
    private MidOutboundSettlementService midOutboundSettlementService;

    /**
     * 查询仓租结算中间列表
     */
    @PreAuthorize("@ss.hasPermi('system:outboundsettlement:list')")
    @GetMapping("/list")
    public TableDataInfo list(MidOutboundSettlement midOutboundSettlement) {
        startPage();
        List<MidOutboundSettlement> list = midOutboundSettlementService.selectMidOutboundSettlementList(midOutboundSettlement);
        return getDataTable(list);
    }

    /**
     * 导出仓租结算中间列表
     */
    @PreAuthorize("@ss.hasPermi('system:outboundsettlement:export')")
    @Log(title = "仓租结算中间", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MidOutboundSettlement midOutboundSettlement) {
        List<MidOutboundSettlement> list = midOutboundSettlementService.selectMidOutboundSettlementList(midOutboundSettlement);
        ExcelUtil<MidOutboundSettlement> util = new ExcelUtil<MidOutboundSettlement>(MidOutboundSettlement.class);
        util.exportExcel(response, list, "仓租结算中间数据");
    }

    /**
     * 获取仓租结算中间详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:outboundsettlement:query')")
    @GetMapping(value = "/{outboundRecordId}")
    public AjaxResult getInfo(@PathVariable("outboundRecordId") Long outboundRecordId) {
        return AjaxResult.success(midOutboundSettlementService.selectMidOutboundSettlementByOutboundRecordId(outboundRecordId));
    }

    /**
     * 新增仓租结算中间
     */
    @PreAuthorize("@ss.hasPermi('system:outboundsettlement:add')")
    @Log(title = "仓租结算中间", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MidOutboundSettlement midOutboundSettlement) {
        return toAjax(midOutboundSettlementService.insertMidOutboundSettlement(midOutboundSettlement));
    }

    /**
     * 修改仓租结算中间
     */
    @PreAuthorize("@ss.hasPermi('system:outboundsettlement:edit')")
    @Log(title = "仓租结算中间", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MidOutboundSettlement midOutboundSettlement) {
        return toAjax(midOutboundSettlementService.updateMidOutboundSettlement(midOutboundSettlement));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:outboundsettlement:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody MidOutboundSettlement midOutboundSettlement) {
        midOutboundSettlement.setUpdateBy(getUserId());
        return toAjax(midOutboundSettlementService.changeStatus(midOutboundSettlement));
    }

    /**
     * 删除仓租结算中间
     */
    @PreAuthorize("@ss.hasPermi('system:outboundsettlement:remove')")
    @Log(title = "仓租结算中间", businessType = BusinessType.DELETE)
    @DeleteMapping("/{outboundRecordIds}")
    public AjaxResult remove(@PathVariable Long[] outboundRecordIds) {
        return toAjax(midOutboundSettlementService.deleteMidOutboundSettlementByOutboundRecordIds(outboundRecordIds));
    }
}
