package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpSeaLcl;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 拼柜海运服务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Mapper
public interface RsOpSeaLclMapper {
    /**
     * 查询拼柜海运服务
     *
     * @param seaLclId 拼柜海运服务主键
     * @return 拼柜海运服务
     */
    RsOpSeaLcl selectRsOpSeaLclBySeaLclId(Long seaLclId);

    /**
     * 查询拼柜海运服务列表
     *
     * @param rsOpSeaLcl 拼柜海运服务
     * @return 拼柜海运服务集合
     */
    List<RsOpSeaLcl> selectRsOpSeaLclList(RsOpSeaLcl rsOpSeaLcl);

    /**
     * 新增拼柜海运服务
     *
     * @param rsOpSeaLcl 拼柜海运服务
     * @return 结果
     */
    int insertRsOpSeaLcl(RsOpSeaLcl rsOpSeaLcl);

    /**
     * 修改拼柜海运服务
     *
     * @param rsOpSeaLcl 拼柜海运服务
     * @return 结果
     */
    int updateRsOpSeaLcl(RsOpSeaLcl rsOpSeaLcl);

    /**
     * 删除拼柜海运服务
     *
     * @param seaLclId 拼柜海运服务主键
     * @return 结果
     */
    int deleteRsOpSeaLclBySeaLclId(Long seaLclId);

    /**
     * 批量删除拼柜海运服务
     *
     * @param seaLclIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsOpSeaLclBySeaLclIds(Long[] seaLclIds);

    List<RsOpSeaLcl> selectRsOpSeaLclByRctId(@Param("rctId") Long rctId, @Param("sqdServiceTypeId") Long sqdServiceTypeId);

    void deleteRsOpSeaLclByRctIdAndServiceTypeId(@Param("rctId") Long rctId, @Param("sqdServiceTypeId") long l);

    void upsertRsOpSeaLcl(RsOpSeaLcl rsOpSeaLcl);
}
