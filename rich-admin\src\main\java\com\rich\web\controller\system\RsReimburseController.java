package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.RsReimburse;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.RsReimburseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 报销记录Controller
 *
 * <AUTHOR>
 * @date 2023-03-30
 */
@RestController
@RequestMapping("/system/reimburse")
public class RsReimburseController extends BaseController {
    @Autowired
    private RsReimburseService rsReimburseService;

    /**
     * 查询报销记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:reimburse:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsReimburse rsReimburse) {
        if (SecurityUtils.getDeptId().equals(102L) || SecurityUtils.getDeptId().equals(104L) || SecurityUtils.getDeptId().equals(103L) || SecurityUtils.isAdmin(SecurityUtils.getLoginUser().getUser().getRole())) {
            rsReimburse.setPermissionLevel(null);
        }
        startPage();
        List<RsReimburse> list = rsReimburseService.selectRsReimburseList(rsReimburse);
        return getDataTable(list);
    }

    /**
     * 查询待报销记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:reimburse:list')")
    @GetMapping("/listReimburse")
    public TableDataInfo listReimburse(RsReimburse rsReimburse) {
        List<RsReimburse> list = rsReimburseService.selectRsReimburseWriteOffList(rsReimburse);
        return getDataTable(list);
    }

    /**
     * 导出报销记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:reimburse:export')")
    @Log(title = "报销记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsReimburse rsReimburse) {
        if (SecurityUtils.getDeptId().equals(102L) || SecurityUtils.getDeptId().equals(104L) || SecurityUtils.getDeptId().equals(103L) || SecurityUtils.isAdmin(SecurityUtils.getLoginUser().getUser().getRole())) {
            rsReimburse.setPermissionLevel(null);
        }
        List<RsReimburse> list = rsReimburseService.selectRsReimburseList(rsReimburse);
        ExcelUtil<RsReimburse> util = new ExcelUtil<RsReimburse>(RsReimburse.class);
        util.exportExcel(response, list, "报销记录数据");
    }

    /**
     * 获取报销记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:reimburse:edit')")
    @GetMapping(value = "/{reimburseId}")
    public AjaxResult getInfo(@PathVariable("reimburseId") Long reimburseId) {
        return AjaxResult.success(rsReimburseService.selectRsReimburseByReimburseId(reimburseId));
    }

    /**
     * 新增报销记录
     */
    @PreAuthorize("@ss.hasPermi('system:reimburse:add')")
    @Log(title = "报销记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsReimburse rsReimburse) {
        return toAjax(rsReimburseService.insertRsReimburse(rsReimburse));
    }

    /**
     * 修改报销记录
     */
    @PreAuthorize("@ss.hasPermi('system:reimburse:edit')")
    @Log(title = "报销记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsReimburse rsReimburse) {
        return toAjax(rsReimburseService.updateRsReimburse(rsReimburse));
    }

    /**
     * 修改报销记录
     *
     * @param rsReimburse 勾选的报销审核
     */
    @PreAuthorize("@ss.hasAnyPermi('system:reimburse:deptapproval,system:reimburse:hrapproval,system:reimburse:ceoapproval,system:reimburse:financeconfirm')")
    @Log(title = "报销记录", businessType = BusinessType.UPDATE)
    @PutMapping("/approval")
    public AjaxResult approval(@RequestBody List<RsReimburse> rsReimburse) {
        return toAjax(rsReimburseService.rsReimburseApproval(rsReimburse));
    }

    @PreAuthorize("@ss.hasAnyPermi('system:reimburse:deptapproval,system:reimburse:hrapproval,system:reimburse:ceoapproval,system:reimburse:financeconfirm')")
    @Log(title = "报销记录", businessType = BusinessType.UPDATE)
    @PutMapping("/cancel")
    public AjaxResult approvalCancel(@RequestBody RsReimburse rsReimburse) {
        return toAjax(rsReimburseService.rsReimburseApprovalCancel(rsReimburse));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:reimburse:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsReimburse rsReimburse) {
        rsReimburse.setUpdateBy(getUserId());
        return toAjax(rsReimburseService.changeStatus(rsReimburse));
    }

    /**
     * 删除报销记录
     */
    @PreAuthorize("@ss.hasPermi('system:reimburse:remove')")
    @Log(title = "报销记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{reimburseIds}")
    public AjaxResult remove(@PathVariable Long[] reimburseIds) {
        return toAjax(rsReimburseService.deleteRsReimburseByReimburseIds(reimburseIds));
    }

    /**
     * 销账报销记录
     *
     * @param rsReimburse 勾选的报销审核
     */
    @PreAuthorize("@ss.hasAnyPermi('system:reimburse:deptapproval,system:reimburse:hrapproval,system:reimburse:ceoapproval,system:reimburse:financeconfirm')")
    @Log(title = "报销记录", businessType = BusinessType.UPDATE)
    @PutMapping("/writeoff")
    public AjaxResult writeoff(@RequestBody List<RsReimburse> rsReimburse) {
        return toAjax(rsReimburseService.rsReimburseWriteOff(rsReimburse));
    }
}
