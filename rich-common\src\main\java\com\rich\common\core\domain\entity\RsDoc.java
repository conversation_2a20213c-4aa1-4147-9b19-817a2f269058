package com.rich.common.core.domain.entity;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 文件信息对象 rs_doc
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
public class RsDoc extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long docId;

    /**
     * 所属服务实例id ,
     */
    @Excel(name = "所属服务实例id ,")
    private Long serviceId;

    /**
     * 所属服务类型id ,
     */
    @Excel(name = "所属服务类型id ,")
    private Long sqdServiceTypeId;

    /**
     * 所属操作单号 ,
     */
    @Excel(name = "所属操作单号 ,")
    private String sqdRctNo;

    /**
     * 文件来源公司id ,
     */
    @Excel(name = "文件来源公司id ,")
    private Long docSourceId;

    /**
     * 文件类型 ,HBL,MBL,AWBL,SWBL,CO,BSC,COC
     */
    @Excel(name = "文件类型 ,HBL,MBL,AWBL,SWBL,CO,BSC,COC")
    private Long docTypeId;

    /**
     * 文件编号 ,提单号等
     */
    @Excel(name = "文件编号 ,提单号等")
    private String docTrackingNo;

    /**
     * 发货人 ,
     */
    @Excel(name = "发货人 ,")
    private String shipper;

    /**
     * 收货人 ,
     */
    @Excel(name = "收货人 ,")
    private String consignee;

    /**
     * 通知人 ,
     */
    @Excel(name = "通知人 ,")
    private String notifyParty;

    /**
     * 唛头 ,
     */
    @Excel(name = "唛头 ,")
    private String shippingMark;

    /**
     * 货描 ,
     */
    @Excel(name = "货描 ,")
    private String goodsDescription;

    /**
     * 柜号和封条和分柜资料(list) ,
     */
    @Excel(name = "柜号和封条和分柜资料(list) ,")
    private String containersSealsList;

    /**
     * 签单日期 ,
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "签单日期 ,", width = 30, dateFormat = "yyyy-MM-dd")
    private Date blIssueDate;

    /**
     * 签单地点 ,
     */
    @Excel(name = "签单地点 ,")
    private String blIssueLocation;

    /**
     * 是否转单 ,
     */
    @Excel(name = "是否转单 ,")
    private String isSwitchBl;

    /**
     * 是否拆单 ,
     */
    @Excel(name = "是否拆单 ,")
    private String isDividedBl;

    /**
     * 显示套约 ,
     */
    @Excel(name = "显示套约 ,")
    private String isAgreementShowed;

    /**
     * 清关中转 ,
     */
    @Excel(name = "清关中转 ,")
    private String isCustomsIntransit;

    /**
     * 出单方式 ,
     */
    @Excel(name = "出单方式 ,")
    private String docIssueType;

    /**
     * 取单方式 ,
     */
    @Excel(name = "取单方式 ,")
    private String docGettingWay;

    /**
     * 交单方式 ,
     */
    @Excel(name = "交单方式 ,")
    private String docDeliveryWay;

    public Long getDocId() {
        return docId;
    }

    public void setDocId(Long docId) {
        this.docId = docId;
    }

    public Long getServiceId() {
        return serviceId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    public Long getSqdServiceTypeId() {
        return sqdServiceTypeId;
    }

    public void setSqdServiceTypeId(Long sqdServiceTypeId) {
        this.sqdServiceTypeId = sqdServiceTypeId;
    }

    public String getSqdRctNo() {
        return sqdRctNo;
    }

    public void setSqdRctNo(String sqdRctNo) {
        this.sqdRctNo = sqdRctNo;
    }

    public Long getDocSourceId() {
        return docSourceId;
    }

    public void setDocSourceId(Long docSourceId) {
        this.docSourceId = docSourceId;
    }

    public Long getDocTypeId() {
        return docTypeId;
    }

    public void setDocTypeId(Long docTypeId) {
        this.docTypeId = docTypeId;
    }

    public String getDocTrackingNo() {
        return docTrackingNo;
    }

    public void setDocTrackingNo(String docTrackingNo) {
        this.docTrackingNo = docTrackingNo;
    }

    public String getShipper() {
        return shipper;
    }

    public void setShipper(String shipper) {
        this.shipper = shipper;
    }

    public String getConsignee() {
        return consignee;
    }

    public void setConsignee(String consignee) {
        this.consignee = consignee;
    }

    public String getNotifyParty() {
        return notifyParty;
    }

    public void setNotifyParty(String notifyParty) {
        this.notifyParty = notifyParty;
    }

    public String getShippingMark() {
        return shippingMark;
    }

    public void setShippingMark(String shippingMark) {
        this.shippingMark = shippingMark;
    }

    public String getGoodsDescription() {
        return goodsDescription;
    }

    public void setGoodsDescription(String goodsDescription) {
        this.goodsDescription = goodsDescription;
    }

    public String getContainersSealsList() {
        return containersSealsList;
    }

    public void setContainersSealsList(String containersSealsList) {
        this.containersSealsList = containersSealsList;
    }

    public Date getBlIssueDate() {
        return blIssueDate;
    }

    public void setBlIssueDate(Date blIssueDate) {
        this.blIssueDate = blIssueDate;
    }

    public String getBlIssueLocation() {
        return blIssueLocation;
    }

    public void setBlIssueLocation(String blIssueLocation) {
        this.blIssueLocation = blIssueLocation;
    }

    public String getIsSwitchBl() {
        return isSwitchBl;
    }

    public void setIsSwitchBl(String isSwitchBl) {
        this.isSwitchBl = isSwitchBl;
    }

    public String getIsDividedBl() {
        return isDividedBl;
    }

    public void setIsDividedBl(String isDividedBl) {
        this.isDividedBl = isDividedBl;
    }

    public String getIsAgreementShowed() {
        return isAgreementShowed;
    }

    public void setIsAgreementShowed(String isAgreementShowed) {
        this.isAgreementShowed = isAgreementShowed;
    }

    public String getIsCustomsIntransit() {
        return isCustomsIntransit;
    }

    public void setIsCustomsIntransit(String isCustomsIntransit) {
        this.isCustomsIntransit = isCustomsIntransit;
    }

    public String getDocIssueType() {
        return docIssueType;
    }

    public void setDocIssueType(String docIssueType) {
        this.docIssueType = docIssueType;
    }

    public String getDocGettingWay() {
        return docGettingWay;
    }

    public void setDocGettingWay(String docGettingWay) {
        this.docGettingWay = docGettingWay;
    }

    public String getDocDeliveryWay() {
        return docDeliveryWay;
    }

    public void setDocDeliveryWay(String docDeliveryWay) {
        this.docDeliveryWay = docDeliveryWay;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("docId", getDocId())
                .append("serviceId", getServiceId())
                .append("sqdServiceTypeId", getSqdServiceTypeId())
                .append("sqdRctNo", getSqdRctNo())
                .append("docSourceId", getDocSourceId())
                .append("docTypeId", getDocTypeId())
                .append("docTrackingNo", getDocTrackingNo())
                .append("shipper", getShipper())
                .append("consignee", getConsignee())
                .append("notifyParty", getNotifyParty())
                .append("shippingMark", getShippingMark())
                .append("goodsDescription", getGoodsDescription())
                .append("containersSealsList", getContainersSealsList())
                .append("blIssueDate", getBlIssueDate())
                .append("blIssueLocation", getBlIssueLocation())
                .append("isSwitchBl", getIsSwitchBl())
                .append("isDividedBl", getIsDividedBl())
                .append("isAgreementShowed", getIsAgreementShowed())
                .append("isCustomsIntransit", getIsCustomsIntransit())
                .append("docIssueType", getDocIssueType())
                .append("docGettingWay", getDocGettingWay())
                .append("docDeliveryWay", getDocDeliveryWay())
                .toString();
    }
}
