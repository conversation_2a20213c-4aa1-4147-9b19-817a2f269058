package com.rich.system.domain;

import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 【请填写功能名称】对象 mid_currency
 *
 * <AUTHOR>
 * @date 2022-12-05
 */
public class MidCurrency extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long currencyId;

    private Long belongId;

    private String belongTo;

    public Long getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(Long currencyId) {
        this.currencyId = currencyId;
    }

    public Long getBelongId() {
        return belongId;
    }

    public void setBelongId(Long belongId) {
        this.belongId = belongId;
    }

    public String getBelongTo() {
        return belongTo;
    }

    public void setBelongTo(String belongTo) {
        this.belongTo = belongTo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("currencyId", getCurrencyId())
                .append("belongId", getBelongId())
                .append("belongTo", getBelongTo())
                .toString();
    }
}
