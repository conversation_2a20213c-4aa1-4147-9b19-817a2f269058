package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 贸易条款对象 bas_trading_terms
 *
 * <AUTHOR>
 * @date 2023-06-30
 */
public class BasTradingTerms extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 贸易条款
     */
    private Long tradingTermsId;

    /**
     * 简称
     */
    @Excel(name = "简称")
    private String tradingTermsShortName;

    /**
     * 中文名
     */
    @Excel(name = "中文名")
    private String tradingTermsLocalName;

    /**
     * 英文名
     */
    @Excel(name = "英文名")
    private String tradingTermsEnName;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Integer orderNum;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;


    public void setTradingTermsId(Long tradingTermsId) {
        this.tradingTermsId = tradingTermsId;
    }

    public Long getTradingTermsId() {
        return tradingTermsId;
    }

    public void setTradingTermsShortName(String tradingTermsShortName) {
        this.tradingTermsShortName = tradingTermsShortName;
    }

    public String getTradingTermsShortName() {
        return tradingTermsShortName;
    }

    public void setTradingTermsLocalName(String tradingTermsLocalName) {
        this.tradingTermsLocalName = tradingTermsLocalName;
    }

    public String getTradingTermsLocalName() {
        return tradingTermsLocalName;
    }

    public void setTradingTermsEnName(String tradingTermsEnName) {
        this.tradingTermsEnName = tradingTermsEnName;
    }

    public String getTradingTermsEnName() {
        return tradingTermsEnName;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
}
