package com.rich.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 【请填写功能名称】对象 bas_dist_line
 *
 * <AUTHOR>
 * @date 2022-08-22
 */
public class BasDistLine extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 航线
     */
    private Long lineId;

    /**
     * 父ID
     */
    @Excel(name = "父ID")
    private Long parentId;

    /**
     * 祖籍列表
     */
    @Excel(name = "祖籍列表")
    private String ancestors;

    /**
     * 航线简称
     */
    @Excel(name = "航线简称")
    private String lineShortName;

    /**
     * 航线中文名
     */
    @Excel(name = "航线中文名")
    private String lineLocalName;

    /**
     * 航线英文名
     */
    @Excel(name = "航线英文名")
    private String lineEnName;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Long orderNum;

    private String status;

    private String lineQuery;

    public String getLineQuery() {
        return lineQuery;
    }

    public void setLineQuery(String lineQuery) {
        this.lineQuery = lineQuery;
    }

    public String getLineShortName() {
        return lineShortName;
    }

    public void setLineShortName(String lineShortName) {
        this.lineShortName = lineShortName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Long orderNum) {
        this.orderNum = orderNum;
    }

    public void setLineId(Long lineId) {
        this.lineId = lineId;
    }

    public Long getLineId() {
        return lineId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }

    public String getAncestors() {
        return ancestors;
    }

    public void setLineLocalName(String lineLocalName) {
        this.lineLocalName = lineLocalName;
    }

    public String getLineLocalName() {
        return lineLocalName;
    }

    public void setLineEnName(String lineEnName) {
        this.lineEnName = lineEnName;
    }

    public String getLineEnName() {
        return lineEnName;
    }


    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("lineId", getLineId())
                .append("parentId", getParentId())
                .append("ancestors", getAncestors())
                .append("lineLocalName", getLineLocalName())
                .append("lineEnName", getLineEnName())
                .append("remark", getRemark())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("deleteBy", getDeleteBy())
                .append("deleteTime", getDeleteTime())
                .append("deleteStatus", getDeleteStatus())
                .toString();
    }
}
