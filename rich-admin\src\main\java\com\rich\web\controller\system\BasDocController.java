package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasDoc;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasDistLocationService;
import com.rich.system.service.BasDocService;
import com.rich.system.service.impl.RedisCacheImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 文件名称Controller
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@RestController
@RequestMapping("/system/doc")
public class BasDocController extends BaseController {
    @Autowired
    private BasDocService basDocService;

    @Autowired
    private BasDistLocationService basDistLocationService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisCacheImpl RedisCache;

    /**
     * 查询文件名称列表
     */
    @PreAuthorize("@ss.hasPermi('system:doc:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasDoc basDoc) {
        startPage();
        List<BasDoc> list = basDocService.selectBasDocList(basDoc);
        return getDataTable(list);
    }

    @GetMapping("/selectList")
    public AjaxResult selectList() {
        List<BasDoc> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "doc");
        if (list == null) {
            RedisCache.doc();
            list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "doc");
        }
        return AjaxResult.success(list);
    }

    /**
     * 导出文件名称列表
     */
    @PreAuthorize("@ss.hasPermi('system:doc:export')")
    @Log(title = "文件名称", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasDoc basDoc) {
        List<BasDoc> list = basDocService.selectBasDocList(basDoc);
        ExcelUtil<BasDoc> util = new ExcelUtil<BasDoc>(BasDoc.class);
        util.exportExcel(response, list, "文件名称数据");
    }

    /**
     * 获取文件名称详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:doc:query')")
    @GetMapping(value = "/{docId}")
    public AjaxResult getInfo(@PathVariable("docId") Long docId) {
        AjaxResult ajaxResult = AjaxResult.success();
        BasDoc basDoc = basDocService.selectBasDocByDocId(docId);
        List<Long> locationDepartureIds = basDocService.selectLocationDeparture(docId);
        List<Long> locationDestinationIds = basDocService.selectLocationDestination(docId);
        Set<Long> set = new HashSet<>();
        if (!locationDepartureIds.isEmpty()) {
            set.addAll(locationDepartureIds);
        }
        if (!locationDestinationIds.isEmpty()) {
            set.addAll(locationDestinationIds);
        }
        basDoc.setServiceTypeIds(basDocService.selectServiceTypes(docId).toArray(new Long[0]));
        basDoc.setCargoTypeIds(basDocService.selectCargoTypes(docId).toArray(new Long[0]));
        basDoc.setLocationDepartureIds(locationDepartureIds.toArray(new Long[0]));
        basDoc.setLocationDestinationIds(locationDestinationIds.toArray(new Long[0]));
        ajaxResult.put("locationOptions", set.size() > 0 ? basDistLocationService.selectBasDistLocationByIds(set) : null);
        basDoc.setLineDepartureIds(basDocService.selectLineDeparture(docId).toArray(new Long[0]));
        basDoc.setLineDestinationIds(basDocService.selectLineDestination(docId).toArray(new Long[0]));
        basDoc.setCarrierIds(basDocService.selectCarriers(docId).toArray(new Long[0]));
        basDoc.setDocFlowDirectionIds(basDocService.selectDocFlowDirection(docId).toArray(new Long[0]));
        basDoc.setDocIssueTypeIds(basDocService.selectDocIssueType(docId).toArray(new Long[0]));
        ajaxResult.put(AjaxResult.DATA_TAG, basDoc);
        return ajaxResult;
    }

    /**
     * 新增文件名称
     */
    @PreAuthorize("@ss.hasPermi('system:doc:add')")
    @Log(title = "文件名称", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasDoc basDoc) {
        return toAjax(basDocService.insertBasDoc(basDoc));
    }

    /**
     * 修改文件名称
     */
    @PreAuthorize("@ss.hasPermi('system:doc:edit')")
    @Log(title = "文件名称", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasDoc basDoc) {
        return toAjax(basDocService.updateBasDoc(basDoc));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:doc:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasDoc basDoc) {
        basDoc.setUpdateBy(getUserId());
        return toAjax(basDocService.changeStatus(basDoc));
    }

    /**
     * 删除文件名称
     */
    @PreAuthorize("@ss.hasPermi('system:doc:remove')")
    @Log(title = "文件名称", businessType = BusinessType.DELETE)
    @DeleteMapping("/{docIds}")
    public AjaxResult remove(@PathVariable Long[] docIds) {
        return toAjax(basDocService.deleteBasDocByDocIds(docIds));
    }
}
