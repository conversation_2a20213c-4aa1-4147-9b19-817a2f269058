package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.BasLoadingWay;
import org.apache.ibatis.annotations.Mapper;

/**
 * 装柜方式Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-25
 */
@Mapper
public interface BasLoadingWayMapper {
    /**
     * 查询装柜方式
     *
     * @param loadingWayCode 装柜方式主键
     * @return 装柜方式
     */
    BasLoadingWay selectBasLoadingWayByLoadingWayCode(String loadingWayCode);

    /**
     * 查询装柜方式列表
     *
     * @param basLoadingWay 装柜方式
     * @return 装柜方式集合
     */
    List<BasLoadingWay> selectBasLoadingWayList(BasLoadingWay basLoadingWay);

    /**
     * 新增装柜方式
     *
     * @param basLoadingWay 装柜方式
     * @return 结果
     */
    int insertBasLoadingWay(BasLoadingWay basLoadingWay);

    /**
     * 修改装柜方式
     *
     * @param basLoadingWay 装柜方式
     * @return 结果
     */
    int updateBasLoadingWay(BasLoadingWay basLoadingWay);

    /**
     * 删除装柜方式
     *
     * @param loadingWayCode 装柜方式主键
     * @return 结果
     */
    int deleteBasLoadingWayByLoadingWayCode(String loadingWayCode);

    /**
     * 批量删除装柜方式
     *
     * @param loadingWayCodes 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasLoadingWayByLoadingWayCodes(String[] loadingWayCodes);
}
