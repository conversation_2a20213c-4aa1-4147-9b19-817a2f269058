package com.rich.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 协议记录对象 rs_agreement_record
 *
 * <AUTHOR>
 * @date 2022-10-28
 */
public class RsAgreementRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 合作协议ID
     */
    private Long agreementId;

    /**
     * 合作公司编码
     */
    @Excel(name = "合作公司编码")
    private Long sqdCompanyId;

    /**
     * 与合作公司签订的协议号
     */
    @Excel(name = "与合作公司签订的协议号")
    private String agreementNumber;

    /**
     * 协议类型
     */
    @Excel(name = "协议类型")
    private Long agreementTypeId;

    /**
     * 合作金额
     */
    @Excel(name = "合作金额")
    private BigDecimal agreementPrice;

    /**
     * 负责员工
     */
    @Excel(name = "负责员工")
    private Long staffId;

    private String staffName;

    /**
     * 协议有效期从
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "协议有效期从", width = 30, dateFormat = "yyyy-MM-dd")
    private Date agreementStartDate;

    /**
     * 协议有效期至
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "协议有效期至", width = 30, dateFormat = "yyyy-MM-dd")
    private Date agreementEndDate;

    /**
     * 协议是否有效
     */
    @Excel(name = "协议是否有效")
    private String isAvailable;

    /**
     * 信用额度，默认0
     */
    @Excel(name = "信用额度，默认0")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal creditLimit;

    /**
     * 协议币种和信用额度币种
     */
    private Long currencyId;

    @Excel(name = "协议币种和信用额度币种")
    private String currencyCode;

    /**
     * 支付时间节点凭据
     */
    @Excel(name = "支付时间节点凭据")
    private Long paymentDateNodeId;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结款日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date settlementDate;

    /**
     * 信用天数，默认0，表示没有信用额度，票结。
     * 负数表示需要提前结算的天数。
     */
    @Excel(name = "信用天数")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal creditDays;

    /**
     * 信用天数是否为工作日，默认否
     */
    @Excel(name = "信用周期")
    private String isWorkingDay;

    /**
     * 信用评级，待定
     */
    @Excel(name = "信用评级，待定")
    private String creditLevel;

    /**
     * 业务录入完成后锁定
     */
    @Excel(name = "业务录入完成后锁定")
    private String isLocked;

    /**
     * 经理确认
     */
    @Excel(name = "部门确认")
    private String deptConfirmed;

    private Long deptConfirmedId;

    /**
     * 经理确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "部门确认时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date deptConfirmedDate;

    /**
     * 财务确认
     */
    @Excel(name = "财务确认")
    private String financeConfirmed;

    private Long financeConfirmedId;

    /**
     * 财务确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "财务确认时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date financeConfirmedDate;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private String orderNum;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;

    public Long getDeptConfirmedId() {
        return deptConfirmedId;
    }

    public void setDeptConfirmedId(Long deptConfirmedId) {
        this.deptConfirmedId = deptConfirmedId;
    }

    public Long getFinanceConfirmedId() {
        return financeConfirmedId;
    }

    public void setFinanceConfirmedId(Long financeConfirmedId) {
        this.financeConfirmedId = financeConfirmedId;
    }

    public String getDeptConfirmed() {
        return deptConfirmed;
    }

    public void setDeptConfirmed(String deptConfirmed) {
        this.deptConfirmed = deptConfirmed;
    }

    public Date getDeptConfirmedDate() {
        return deptConfirmedDate;
    }

    public void setDeptConfirmedDate(Date deptConfirmedDate) {
        this.deptConfirmedDate = deptConfirmedDate;
    }

    public String getFinanceConfirmed() {
        return financeConfirmed;
    }

    public void setFinanceConfirmed(String financeConfirmed) {
        this.financeConfirmed = financeConfirmed;
    }

    public Date getFinanceConfirmedDate() {
        return financeConfirmedDate;
    }

    public void setFinanceConfirmedDate(Date financeConfirmedDate) {
        this.financeConfirmedDate = financeConfirmedDate;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public Date getSettlementDate() {
        return settlementDate;
    }

    public void setSettlementDate(Date settlementDate) {
        this.settlementDate = settlementDate;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public void setAgreementId(Long agreementId) {
        this.agreementId = agreementId;
    }

    public Long getAgreementId() {
        return agreementId;
    }

    public void setSqdCompanyId(Long sqdCompanyId) {
        this.sqdCompanyId = sqdCompanyId;
    }

    public Long getSqdCompanyId() {
        return sqdCompanyId;
    }

    public void setAgreementNumber(String agreementNumber) {
        this.agreementNumber = agreementNumber;
    }

    public String getAgreementNumber() {
        return agreementNumber;
    }

    public void setAgreementTypeId(Long agreementTypeId) {
        this.agreementTypeId = agreementTypeId;
    }

    public Long getAgreementTypeId() {
        return agreementTypeId;
    }

    public void setAgreementPrice(BigDecimal agreementPrice) {
        this.agreementPrice = agreementPrice;
    }

    public BigDecimal getAgreementPrice() {
        return agreementPrice;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setAgreementStartDate(Date agreementStartDate) {
        this.agreementStartDate = agreementStartDate;
    }

    public Date getAgreementStartDate() {
        return agreementStartDate;
    }

    public void setAgreementEndDate(Date agreementEndDate) {
        this.agreementEndDate = agreementEndDate;
    }

    public Date getAgreementEndDate() {
        return agreementEndDate;
    }


    public void setCurrencyId(Long currencyId) {
        this.currencyId = currencyId;
    }

    public Long getCurrencyId() {
        return currencyId;
    }

    public void setPaymentDateNodeId(Long paymentDateNodeId) {
        this.paymentDateNodeId = paymentDateNodeId;
    }

    public Long getPaymentDateNodeId() {
        return paymentDateNodeId;
    }

    public void setCreditLevel(String creditLevel) {
        this.creditLevel = creditLevel;
    }

    public String getCreditLevel() {
        return creditLevel;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public String getIsAvailable() {
        return isAvailable;
    }

    public void setIsAvailable(String isAvailable) {
        this.isAvailable = isAvailable;
    }

    public String getIsWorkingDay() {
        return isWorkingDay;
    }

    public void setIsWorkingDay(String isWorkingDay) {
        this.isWorkingDay = isWorkingDay;
    }

    public String getIsLocked() {
        return isLocked;
    }

    public void setIsLocked(String isLocked) {
        this.isLocked = isLocked;
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    public BigDecimal getCreditLimit() {
        return creditLimit;
    }

    public void setCreditLimit(BigDecimal creditLimit) {
        this.creditLimit = creditLimit;
    }

    public BigDecimal getCreditDays() {
        return creditDays;
    }

    public void setCreditDays(BigDecimal creditDays) {
        this.creditDays = creditDays;
    }
}
