package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/12/16 14:45
 * @Version 1.0
 */

public class BankTransactionRecordDTO {

    @Excel(name = "日期")
    private Date transactionDate; // 日期
    @Excel(name = "所属期")
    private String period;                 // 所属期
    @Excel(name = "银行")
    private String bank;                   // 银行
    @Excel(name = "币种")
    private String currency;               // 币种
    @Excel(name = "收入")
    private BigDecimal income;             // 收入
    @Excel(name = "支出")
    private BigDecimal expense;            // 支出

    @Excel(name = "结算对象")
    private String settlementObject;       // 结算对象
    @Excel(name = "款项归属")
    private String fundAttribution;        // 款项归属
    @Excel(name = "手续费")
    private BigDecimal handleFee;

    public BigDecimal getHandleFee() {
        return handleFee;
    }

    public void setHandleFee(BigDecimal handleFee) {
        this.handleFee = handleFee;
    }

    // 构造方法
    public BankTransactionRecordDTO() {
    }

    public BankTransactionRecordDTO(Date transactionDate, String period, String bank, String currency,
                                    BigDecimal income, BigDecimal expense, String settlementObject, String fundAttribution) {
        this.transactionDate = transactionDate;
        this.period = period;
        this.bank = bank;
        this.currency = currency;
        this.income = income;
        this.expense = expense;
        this.settlementObject = settlementObject;
        this.fundAttribution = fundAttribution;
    }

    // Getter 和 Setter 方法
    public Date getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(Date transactionDate) {
        this.transactionDate = transactionDate;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getBank() {
        return bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getIncome() {
        return income;
    }

    public void setIncome(BigDecimal income) {
        this.income = income;
    }

    public BigDecimal getExpense() {
        return expense;
    }

    public void setExpense(BigDecimal expense) {
        this.expense = expense;
    }

    public String getSettlementObject() {
        return settlementObject;
    }

    public void setSettlementObject(String settlementObject) {
        this.settlementObject = settlementObject;
    }

    public String getFundAttribution() {
        return fundAttribution;
    }

    public void setFundAttribution(String fundAttribution) {
        this.fundAttribution = fundAttribution;
    }

    @Override
    public String toString() {
        return "BankTransactionRecord{" +
                "transactionDate=" + transactionDate +
                ", period='" + period + '\'' +
                ", bank='" + bank + '\'' +
                ", currency='" + currency + '\'' +
                ", income=" + income +
                ", expense=" + expense +
                ", settlementObject='" + settlementObject + '\'' +
                ", fundAttribution='" + fundAttribution + '\'' +
                '}';
    }
}

