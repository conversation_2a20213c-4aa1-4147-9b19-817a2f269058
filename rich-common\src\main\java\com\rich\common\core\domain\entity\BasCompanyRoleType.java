package com.rich.common.core.domain.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 【请填写功能名称】对象 bas_company_role_type
 * 
 * <AUTHOR>
 * @date 2022-08-29
 */
public class BasCompanyRoleType extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 角色类型 */
    private Long roleTypeId;

    private String roleTypeShortName;

    /** 角色类型中文名 */
    @Excel(name = "角色类型中文名")
    private String roleTypeLocalName;

    /** 角色类型英文名 */
    @Excel(name = "角色类型英文名")
    private String roleTypeEnName;

    private String status;

    private String roleTypeQuery;

    private String orderNum;

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    public String getRoleTypeQuery() {
        return roleTypeQuery;
    }

    public void setRoleTypeQuery(String roleTypeQuery) {
        this.roleTypeQuery = roleTypeQuery;
    }

    public void setRoleTypeId(Long roleTypeId)
    {
        this.roleTypeId = roleTypeId;
    }

    public Long getRoleTypeId() 
    {
        return roleTypeId;
    }
    public void setRoleTypeLocalName(String roleTypeLocalName) 
    {
        this.roleTypeLocalName = roleTypeLocalName;
    }

    public String getRoleTypeLocalName() 
    {
        return roleTypeLocalName;
    }
    public void setRoleTypeEnName(String roleTypeEnName) 
    {
        this.roleTypeEnName = roleTypeEnName;
    }

    public String getRoleTypeEnName() 
    {
        return roleTypeEnName;
    }

    public String getRoleTypeShortName() {
        return roleTypeShortName;
    }

    public void setRoleTypeShortName(String roleTypeShortName) {
        this.roleTypeShortName = roleTypeShortName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("roleTypeId", getRoleTypeId())
            .append("roleTypeLocalName", getRoleTypeLocalName())
            .append("roleTypeEnName", getRoleTypeEnName())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("deleteBy", getDeleteBy())
            .append("deleteTime", getDeleteTime())
            .append("deleteStatus", getDeleteStatus())
            .toString();
    }
}
