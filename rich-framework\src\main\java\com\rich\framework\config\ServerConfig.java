package com.rich.framework.config;

import com.rich.common.utils.ServletUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import javax.servlet.http.HttpServletRequest;

/**
 * 服务相关配置
 *
 * <AUTHOR>
 */
@Component
public class ServerConfig {
    public static String getDomain(HttpServletRequest request) {
        /*StringBuffer url = request.getRequestURL();
        String contextPath = request.getServletContext().getContextPath();

        // 检查X-Forwarded-Proto头以确定原始请求协议
        String forwardedProto = request.getHeader("X-Forwarded-Proto");
        if (forwardedProto != null && forwardedProto.equalsIgnoreCase("https")) {
            // 将url中的协议部分替换为https
            int protocolEndIndex = url.indexOf(":");
            url.replace(0, protocolEndIndex, "https");
        }*/

        // 删除请求URI部分并追加上下文路径
        return ServletUriComponentsBuilder.fromCurrentContextPath().build().toUriString();
    }

    /**
     * 获取完整的请求路径，包括：域名，端口，上下文访问路径
     *
     * @return 服务地址
     */
    public String getUrl() {
        HttpServletRequest request = ServletUtils.getRequest();
        return getDomain(request);
    }
}
