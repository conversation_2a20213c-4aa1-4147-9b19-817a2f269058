package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasExchangeRate;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 汇率Mapper接口
 *
 * <AUTHOR>
 * @date 2023-05-30
 */
@Mapper
public interface BasExchangeRateMapper {
    /**
     * 查询汇率
     *
     * @param exchangeRateId 汇率主键
     * @return 汇率
     */
    BasExchangeRate selectBasExchangeRateByExchangeRateId(Long exchangeRateId);

    /**
     * 查询汇率列表
     *
     * @param basExchangeRate 汇率
     * @return 汇率集合
     */
    List<BasExchangeRate> selectBasExchangeRateList(BasExchangeRate basExchangeRate);

    /**
     * 新增汇率
     *
     * @param basExchangeRate 汇率
     * @return 结果
     */
    int insertBasExchangeRate(BasExchangeRate basExchangeRate);

    /**
     * 修改汇率
     *
     * @param basExchangeRate 汇率
     * @return 结果
     */
    int updateBasExchangeRate(BasExchangeRate basExchangeRate);

    /**
     * 删除汇率
     *
     * @param exchangeRateId 汇率主键
     * @return 结果
     */
    int deleteBasExchangeRateByExchangeRateId(Long exchangeRateId);

    /**
     * 批量删除汇率
     *
     * @param exchangeRateIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasExchangeRateByExchangeRateIds(Long[] exchangeRateIds);

    List<BasExchangeRate> selectList();
}
