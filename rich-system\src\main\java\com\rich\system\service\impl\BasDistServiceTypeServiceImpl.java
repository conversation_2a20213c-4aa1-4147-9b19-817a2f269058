package com.rich.system.service.impl;

import com.rich.common.constant.CacheConstants;
import com.rich.common.constant.UserConstants;
import com.rich.common.core.domain.entity.BasDistServiceType;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.core.text.Convert;
import com.rich.common.exception.ServiceException;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.StringUtils;
import com.rich.system.domain.MidCarrier;
import com.rich.system.mapper.BasDistServiceTypeMapper;
import com.rich.system.mapper.MidCarrierMapper;
import com.rich.system.service.BasDistServiceTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.swing.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 服务类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-08-29
 */
@Service

public class BasDistServiceTypeServiceImpl implements BasDistServiceTypeService {
    @Autowired
    private BasDistServiceTypeMapper basDistServiceTypeMapper;
    @Autowired
    private MidCarrierMapper midCarrierMapper;
    @Autowired
    private RedisCacheImpl RedisCache;
    @Autowired
    private RedisCache redisCache;

    /**
     * 查询服务类型
     *
     * @param serviceTypeId 服务类型主键
     * @return 服务类型
     */
    @Override
    public BasDistServiceType selectBasDistServiceTypeByServiceTypeId(Long serviceTypeId) {
        return basDistServiceTypeMapper.selectBasDistServiceTypeByServiceTypeId(serviceTypeId);
    }

    /**
     * 查询服务类型列表
     *
     * @param basDistServiceType 服务类型
     * @return 服务类型
     */
    @Override
    public List<BasDistServiceType> selectBasDistServiceTypeList(BasDistServiceType basDistServiceType) {
        return basDistServiceTypeMapper.selectBasDistServiceTypeList(basDistServiceType);
    }

    /**
     * 新增服务类型
     *
     * @param basDistServiceType 服务类型
     * @return 结果
     */
    @Override
    public int insertBasDistServiceType(BasDistServiceType basDistServiceType) {
        basDistServiceType.setCreateTime(DateUtils.getNowDate());
        basDistServiceType.setCreateBy(SecurityUtils.getUserId());
        BasDistServiceType info = basDistServiceTypeMapper.selectBasDistServiceTypeByServiceTypeId(basDistServiceType.getParentId());
        // 如果父节点不为正常状态,则不允许新增子节点
        /*if (!UserConstants.DEPT_NORMAL.equals(info.getStatus())) {
            throw new ServiceException("区域停用，不允许新增");
        }
        basDistServiceType.setAncestors(info.getAncestors() + "," + info.getServiceTypeId());*/
        insertCarrierIds(basDistServiceType);
        return basDistServiceTypeMapper.insertBasDistServiceType(basDistServiceType);
    }

    /**
     * 修改服务类型
     *
     * @param basDistServiceType 服务类型
     * @return 结果
     */
    @Override
    public int updateBasDistServiceType(BasDistServiceType basDistServiceType) {
        basDistServiceType.setUpdateTime(DateUtils.getNowDate());
        basDistServiceType.setUpdateBy(SecurityUtils.getUserId());
        BasDistServiceType newParentServiceType = basDistServiceTypeMapper.selectBasDistServiceTypeByServiceTypeId(basDistServiceType.getParentId());
        BasDistServiceType oldServiceType = basDistServiceTypeMapper.selectBasDistServiceTypeByServiceTypeId(basDistServiceType.getServiceTypeId());
        if (StringUtils.isNotNull(newParentServiceType) && StringUtils.isNotNull(oldServiceType)) {
            String newAncestors = newParentServiceType.getAncestors() + "," + newParentServiceType.getServiceTypeId();
            String oldAncestors = oldServiceType.getAncestors();
            basDistServiceType.setAncestors(newAncestors);
            updateServiceTypeChildren(basDistServiceType.getServiceTypeId(), newAncestors, oldAncestors);
        }
        if (basDistServiceType.getTypeId() != null) {
            List<BasDistServiceType> basDistServiceTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
            if (basDistServiceTypes == null) {
                RedisCache.serviceType();
                basDistServiceTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
            }
            for (BasDistServiceType serviceType: basDistServiceTypes) {
                if(serviceType.getTypeId()!=null&&serviceType.getTypeId().equals(basDistServiceType.getTypeId())){
                    serviceType.setTypeId(null);
                    basDistServiceTypeMapper.updateBasDistServiceType(serviceType);
                }
            }
        }
        if (UserConstants.DEPT_NORMAL.equals(basDistServiceType.getStatus()) && StringUtils.isNotEmpty(basDistServiceType.getAncestors())
                && !StringUtils.equals("0", basDistServiceType.getAncestors())) {
            // 如果该部门是启用状态，则启用该部门的所有上级部门
            updateParentServiceTypeStatusNormal(basDistServiceType);
        }
        midCarrierMapper.deleteMidCarrierById(basDistServiceType.getServiceTypeId(), "serviceType");
        insertCarrierIds(basDistServiceType);
        return basDistServiceTypeMapper.updateBasDistServiceType(basDistServiceType);
    }


    /**
     * 批量删除服务类型
     *
     * @param serviceTypeIds 需要删除的服务类型主键
     * @return 结果
     */
    @Override
    public int deleteBasDistServiceTypeByServiceTypeIds(Long[] serviceTypeIds) {
        for (Long l : serviceTypeIds) {
            midCarrierMapper.deleteMidCarrierById(l, "serviceType");
        }
        return basDistServiceTypeMapper.deleteBasDistServiceTypeByServiceTypeIds(serviceTypeIds);
    }

    /**
     * 删除服务类型信息
     *
     * @param serviceTypeId 服务类型主键
     * @return 结果
     */
    @Override
    public int deleteBasDistServiceTypeByServiceTypeId(Long serviceTypeId) {
        midCarrierMapper.deleteMidCarrierById(serviceTypeId, "serviceType");
        return basDistServiceTypeMapper.deleteBasDistServiceTypeByServiceTypeId(serviceTypeId);
    }

    @Override
    public List<Long> getServiceTypeCarrier(Long serviceTypeId) {
        return midCarrierMapper.selectMidCarrierById(serviceTypeId, "serviceType");
    }

    @Override
    public int changeBasDistServiceTypeStatus(BasDistServiceType basDistServiceType) {
        return basDistServiceTypeMapper.updateBasDistServiceType(basDistServiceType);
    }

    public void insertCarrierIds(BasDistServiceType basDistServiceType) {
        List<MidCarrier> insert = new ArrayList<>();
        if (basDistServiceType.getCarrierIds() != null && basDistServiceType.getCarrierIds().length > 0) {
            for (Long s : basDistServiceType.getCarrierIds()) {
                MidCarrier midCarrier = new MidCarrier();
                midCarrier.setBelongTo("serviceType");
                midCarrier.setCarrierId(s);
                midCarrier.setBelongId(basDistServiceType.getServiceTypeId());
                insert.add(midCarrier);
            }
            midCarrierMapper.batchCarrier(insert);
            RedisCache.midCarrier("serviceType", "carrierServiceType");
        }
    }

    /**
     * 修改子元素关系
     *
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateServiceTypeChildren(Long serviceTypeId, String newAncestors, String oldAncestors) {
        List<BasDistServiceType> children = basDistServiceTypeMapper.selectChildrenServiceTypeById(serviceTypeId);
        for (BasDistServiceType child : children) {
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
        }
        if (children.size() > 0) {
            basDistServiceTypeMapper.updateServiceTypeChildren(children);
        }
    }

    /**
     * 修改该部门的父级部门状态
     */
    private void updateParentServiceTypeStatusNormal(BasDistServiceType basDistServiceType) {
        String ancestors = basDistServiceType.getAncestors();
        Long[] serviceTypeIds = Convert.toLongArray(ancestors);
        basDistServiceTypeMapper.updateServiceTypeStatusNormal(serviceTypeIds);
    }
}
