package com.rich.system.domain;

import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 【请填写功能名称】对象 mid_charge_type_dept
 *
 * <AUTHOR>
 * @date 2022-12-06
 */
public class MidChargeTypeDept extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long chargeTypeId;

    private Long deptId;

    private String belongTo;

    public Long getChargeTypeId() {
        return chargeTypeId;
    }

    public void setChargeTypeId(Long chargeTypeId) {
        this.chargeTypeId = chargeTypeId;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getBelongTo() {
        return belongTo;
    }

    public void setBelongTo(String belongTo) {
        this.belongTo = belongTo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("chargeTypeId", getChargeTypeId())
                .append("deptId", getDeptId())
                .append("belongTo", getBelongTo())
                .toString();
    }
}
