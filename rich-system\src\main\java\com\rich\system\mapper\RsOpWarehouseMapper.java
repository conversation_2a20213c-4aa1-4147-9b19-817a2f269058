package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpWHS;
import com.rich.common.core.domain.entity.RsOpWarehouse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 仓储服务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Mapper
public interface RsOpWarehouseMapper {
    /**
     * 查询仓储服务
     *
     * @param warehouseId 仓储服务主键
     * @return 仓储服务
     */
    RsOpWarehouse selectRsOpWarehouseByWarehouseId(Long warehouseId);

    /**
     * 查询仓储服务列表
     *
     * @param rsOpWarehouse 仓储服务
     * @return 仓储服务集合
     */
    List<RsOpWarehouse> selectRsOpWarehouseList(RsOpWarehouse rsOpWarehouse);

    /**
     * 新增仓储服务
     *
     * @param rsOpWarehouse 仓储服务
     * @return 结果
     */
    int insertRsOpWarehouse(RsOpWarehouse rsOpWarehouse);

    int insertRsOpWarehouse(RsOpWHS rsOpWHS);

    /**
     * 修改仓储服务
     *
     * @param rsOpWarehouse 仓储服务
     * @return 结果
     */
    int updateRsOpWarehouse(RsOpWarehouse rsOpWarehouse);

    int updateRsOpWarehouse(RsOpWHS rsOpWHS);

    /**
     * 删除仓储服务
     *
     * @param warehouseId 仓储服务主键
     * @return 结果
     */
    int deleteRsOpWarehouseByWarehouseId(Long warehouseId);

    /**
     * 批量删除仓储服务
     *
     * @param warehouseIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsOpWarehouseByWarehouseIds(Long[] warehouseIds);

    RsOpWarehouse selectRsOpWarehouseByRctId(Long rctId);

    RsOpWHS selectRsOpWHSByRctId(@Param("rctId") Long rctId, @Param("sqdServiceTypeId") Long sqdServiceTypeId);
}
