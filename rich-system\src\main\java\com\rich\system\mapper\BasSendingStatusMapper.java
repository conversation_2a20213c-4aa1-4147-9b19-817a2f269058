package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasSendingStatus;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 发送状态Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@Mapper
public interface BasSendingStatusMapper {
    /**
     * 查询发送状态
     *
     * @param sendingStatusId 发送状态主键
     * @return 发送状态
     */
    BasSendingStatus selectBasSendingStatusBySendingStatusId(Long sendingStatusId);

    /**
     * 查询发送状态列表
     *
     * @param basSendingStatus 发送状态
     * @return 发送状态集合
     */
    List<BasSendingStatus> selectBasSendingStatusList(BasSendingStatus basSendingStatus);

    /**
     * 新增发送状态
     *
     * @param basSendingStatus 发送状态
     * @return 结果
     */
    int insertBasSendingStatus(BasSendingStatus basSendingStatus);

    /**
     * 修改发送状态
     *
     * @param basSendingStatus 发送状态
     * @return 结果
     */
    int updateBasSendingStatus(BasSendingStatus basSendingStatus);

    /**
     * 删除发送状态
     *
     * @param sendingStatusId 发送状态主键
     * @return 结果
     */
    int deleteBasSendingStatusBySendingStatusId(Long sendingStatusId);

    /**
     * 批量删除发送状态
     *
     * @param sendingStatusIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasSendingStatusBySendingStatusIds(Long[] sendingStatusIds);
}
