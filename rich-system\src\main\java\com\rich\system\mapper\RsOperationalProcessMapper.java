package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsOperationalProcess;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 操作进度Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@Mapper
public interface RsOperationalProcessMapper {
    /**
     * 查询操作进度
     *
     * @param operationalProcessId 操作进度主键
     * @return 操作进度
     */
    RsOperationalProcess selectRsOperationalProcessByOperationalProcessId(Long operationalProcessId);

    /**
     * 查询操作进度列表
     *
     * @param rsOperationalProcess 操作进度
     * @return 操作进度集合
     */
    List<RsOperationalProcess> selectRsOperationalProcessList(RsOperationalProcess rsOperationalProcess);

    List<RsOperationalProcess> selectRsOperationalProcess(Long rctId, Long typeId, Long basicInfoId);

    /**
     * 新增操作进度
     *
     * @param rsOperationalProcess 操作进度
     * @return 结果
     */
    int insertRsOperationalProcess(RsOperationalProcess rsOperationalProcess);

    /**
     * 修改操作进度
     *
     * @param rsOperationalProcess 操作进度
     * @return 结果
     */
    int updateRsOperationalProcess(RsOperationalProcess rsOperationalProcess);

    /**
     * 删除操作进度
     *
     * @param operationalProcessId 操作进度主键
     * @return 结果
     */
    int deleteRsOperationalProcessByOperationalProcessId(Long operationalProcessId);

    /**
     * 删除操作进度
     *
     * @return 结果
     */
    int deleteRsOperationalProcessByRctId(Long rctId);

    /**
     * 批量删除操作进度
     *
     * @return 结果
     */
    int deleteRsOperationalProcessByIds(Long[] rctIds);

    int deleteRsOperationalProcess(Long rctId, Long typeId, Long basicInfoId);
}
