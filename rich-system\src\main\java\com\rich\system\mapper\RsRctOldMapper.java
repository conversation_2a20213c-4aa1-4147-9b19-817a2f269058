package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsRctOld;
import com.rich.system.domain.vo.RcRctStatisticsVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 操作单列表Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-15
 */
@Mapper
public interface RsRctOldMapper {
    /**
     * 查询操作单列表
     *
     * @param rctId 操作单列表主键
     * @return 操作单列表
     */
    RsRctOld selectRsRctByRctId(Long rctId);

    /**
     * 查询操作单列表列表
     *
     * @param rsRct 操作单列表
     * @return 操作单列表集合
     */
    List<RsRctOld> selectRsRctList(RsRctOld rsRct);

    /**
     * 新增操作单列表
     *
     * @param rsRct 操作单列表
     * @return 结果
     */
    int insertRsRct(RsRctOld rsRct);

    /**
     * 修改操作单列表
     *
     * @param rsRct 操作单列表
     * @return 结果
     */
    int updateRsRct(RsRctOld rsRct);

    /**
     * 删除操作单列表
     *
     * @param rctId 操作单列表主键
     * @return 结果
     */
    int deleteRsRctByRctId(Long rctId);

    /**
     * 批量删除操作单列表
     *
     * @param rctIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsRctByRctIds(Long[] rctIds);

    int getMon();

    List<RcRctStatisticsVO> selectOpStatistics();

}
