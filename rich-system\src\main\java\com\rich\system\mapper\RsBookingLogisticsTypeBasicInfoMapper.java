package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsBookingLogisticsTypeBasicInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 订舱单基础物流信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-15
 */
@Mapper
public interface RsBookingLogisticsTypeBasicInfoMapper {
    /**
     * 查询订舱单基础物流信息
     *
     * @return 订舱单基础物流信息
     */
    RsBookingLogisticsTypeBasicInfo selectRsBookingLogisticsTypeBasicInfoByBookingId(Long bookingId);

    /**
     * 查询订舱单基础物流信息列表
     *
     * @param rsBookingLogisticsTypeBasicInfo 订舱单基础物流信息
     * @return 订舱单基础物流信息集合
     */
    List<RsBookingLogisticsTypeBasicInfo> selectRsBookingLogisticsTypeBasicInfoList(RsBookingLogisticsTypeBasicInfo rsBookingLogisticsTypeBasicInfo);

    /**
     * 新增订舱单基础物流信息
     *
     * @param rsBookingLogisticsTypeBasicInfo 订舱单基础物流信息
     * @return 结果
     */
    int insertRsBookingLogisticsTypeBasicInfo(RsBookingLogisticsTypeBasicInfo rsBookingLogisticsTypeBasicInfo);

    /**
     * 修改订舱单基础物流信息
     *
     * @param rsBookingLogisticsTypeBasicInfo 订舱单基础物流信息
     * @return 结果
     */
    int updateRsBookingLogisticsTypeBasicInfo(RsBookingLogisticsTypeBasicInfo rsBookingLogisticsTypeBasicInfo);

    /**
     * 删除订舱单基础物流信息
     *
     * @return 结果
     */
    int deleteRsBookingLogisticsTypeBasicInfoById(Long bookingId);

    /**
     * 批量删除订舱单基础物流信息
     *
     * @return 结果
     */
    int deleteRsBookingLogisticsTypeBasicInfoByIds(Long[] bookingIds);
}
