package com.rich.system.domain;

/**
 * @TableName mid_role_dept
 */
public class MidRoleDept {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    private Long roleId;
    /**
     *
     */
    private Long deptId;

    /**
     *
     */
    public Long getRoleId() {
        return roleId;
    }

    /**
     *
     */
    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    /**
     *
     */
    public Long getDeptId() {
        return deptId;
    }

    /**
     *
     */
    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MidRoleDept other = (MidRoleDept) that;
        return (this.getRoleId() == null ? other.getRoleId() == null : this.getRoleId().equals(other.getRoleId()))
                && (this.getDeptId() == null ? other.getDeptId() == null : this.getDeptId().equals(other.getDeptId()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getRoleId() == null) ? 0 : getRoleId().hashCode());
        result = prime * result + ((getDeptId() == null) ? 0 : getDeptId().hashCode());
        return result;
    }

    @Override
    public String toString() {
        String sb = getClass().getSimpleName() +
                " [" +
                "Hash = " + hashCode() +
                ", roleId=" + roleId +
                ", deptId=" + deptId +
                ", serialVersionUID=" + serialVersionUID +
                "]";
        return sb;
    }
}