import {getToken} from "@/utils/auth";

// 登录页面
const loginPage = "/pages/login";

// 页面白名单
const whiteList = [
    "/pages/login",
    "/packageB/register/register",
    "/packageB/common/webview/index",
    "/pages/common/agreements/user-agreement",
    "/pages/common/agreements/privacy-policy",
];

// 检查地址白名单
function checkWhite(url) {
    const path = url.split("?")[0];
    return whiteList.indexOf(path) !== -1;
}

// 页面跳转验证拦截器
let list = ["navigateTo", "redirectTo", "reLaunch", "switchTab"];
list.forEach((item) => {
    uni.addInterceptor(item, {
        invoke(to) {
            if (getToken()) {
                if (to.url === loginPage) {
                    uni.reLaunch({url: "/"});
                }
                return true;
            } else {
                if (checkWhite(to.url)) {
                    return true;
                }
                // 携带当前页面路径作为redirect参数
                const redirectUrl = encodeURIComponent(to.url);
                uni.reLaunch({url: `${loginPage}?redirect=${redirectUrl}`});
                return false;
            }
        },
        fail(err) {
            console.log(err);
        },
    });
});
