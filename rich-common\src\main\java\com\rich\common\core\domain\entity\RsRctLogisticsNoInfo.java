package com.rich.common.core.domain.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

/**
 * 操作单基础物流编号信息对象 rs_rct_logistics_no_info
 * 
 * <AUTHOR>
 * @date 2023-08-15
 */
public class RsRctLogisticsNoInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 基础物流编号信息 */
    private Long logisticsNoInfoId;

    /** 操作单 */
    @Excel(name = "操作单")
    private Long rctId;

    /** so号码 */
    @Excel(name = "so号码")
    private String soNo;

    /** 主提单号 */
    @Excel(name = "主提单号")
    private String mblNo;

    /** 货代单号 */
    @Excel(name = "货代单号")
    private String hblNo;

    /** 柜号信息 */
    @Excel(name = "柜号信息")
    private String containersInfo;

    /** 发货人 */
    @Excel(name = "发货人")
    private String shipper;

    /** 收货人 */
    @Excel(name = "收货人")
    private String consignee;

    /** 通知人 */
    @Excel(name = "通知人")
    private String notifyParty;

    /** 启运港放舱代理 */
    @Excel(name = "启运港放舱代理")
    private String polBookingAgent;

    /** 目的港换单代理 */
    @Excel(name = "目的港换单代理")
    private String podHandleAgent;

    /** 唛头 */
    @Excel(name = "唛头")
    private String shippingMark;

    /** 货描 */
    @Excel(name = "货描")
    private String goodsDescription;

    /** 签单日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "签单日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date blIssueDate;

    /** 签单地点 */
    @Excel(name = "签单地点")
    private String blIssueLocation;

    /** 删除时间 */
    private Date deleteTime;

    /** 数据状态（-1:删除或不可用，0：正常） */
    private String deleteStatus;

    /** $column.columnComment */
    private Long deleteBy;

    public void setLogisticsNoInfoId(Long logisticsNoInfoId) 
    {
        this.logisticsNoInfoId = logisticsNoInfoId;
    }

    public Long getLogisticsNoInfoId() 
    {
        return logisticsNoInfoId;
    }
    public void setRctId(Long rctId) 
    {
        this.rctId = rctId;
    }

    public Long getRctId() 
    {
        return rctId;
    }
    public void setSoNo(String soNo) 
    {
        this.soNo = soNo;
    }

    public String getSoNo() 
    {
        return soNo;
    }
    public void setMblNo(String mblNo) 
    {
        this.mblNo = mblNo;
    }

    public String getMblNo() 
    {
        return mblNo;
    }
    public void setHblNo(String hblNo) 
    {
        this.hblNo = hblNo;
    }

    public String getHblNo() 
    {
        return hblNo;
    }
    public void setContainersInfo(String containersInfo) 
    {
        this.containersInfo = containersInfo;
    }

    public String getContainersInfo() 
    {
        return containersInfo;
    }
    public void setShipper(String shipper) 
    {
        this.shipper = shipper;
    }

    public String getShipper() 
    {
        return shipper;
    }
    public void setConsignee(String consignee) 
    {
        this.consignee = consignee;
    }

    public String getConsignee() 
    {
        return consignee;
    }
    public void setNotifyParty(String notifyParty) 
    {
        this.notifyParty = notifyParty;
    }

    public String getNotifyParty() 
    {
        return notifyParty;
    }
    public void setPolBookingAgent(String polBookingAgent) 
    {
        this.polBookingAgent = polBookingAgent;
    }

    public String getPolBookingAgent() 
    {
        return polBookingAgent;
    }
    public void setPodHandleAgent(String podHandleAgent) 
    {
        this.podHandleAgent = podHandleAgent;
    }

    public String getPodHandleAgent() 
    {
        return podHandleAgent;
    }
    public void setShippingMark(String shippingMark) 
    {
        this.shippingMark = shippingMark;
    }

    public String getShippingMark() 
    {
        return shippingMark;
    }
    public void setGoodsDescription(String goodsDescription) 
    {
        this.goodsDescription = goodsDescription;
    }

    public String getGoodsDescription() 
    {
        return goodsDescription;
    }
    public void setBlIssueDate(Date blIssueDate) 
    {
        this.blIssueDate = blIssueDate;
    }

    public Date getBlIssueDate() 
    {
        return blIssueDate;
    }
    public void setBlIssueLocation(String blIssueLocation) 
    {
        this.blIssueLocation = blIssueLocation;
    }

    public String getBlIssueLocation() 
    {
        return blIssueLocation;
    }

}
