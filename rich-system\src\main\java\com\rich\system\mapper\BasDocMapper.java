package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasDoc;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 文件名称Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@Mapper
public interface BasDocMapper {
    /**
     * 查询文件名称
     *
     * @param docId 文件名称主键
     * @return 文件名称
     */
    BasDoc selectBasDocByDocId(Long docId);

    /**
     * 查询文件名称列表
     *
     * @param basDoc 文件名称
     * @return 文件名称集合
     */
    List<BasDoc> selectBasDocList(BasDoc basDoc);

    /**
     * 新增文件名称
     *
     * @param basDoc 文件名称
     * @return 结果
     */
    int insertBasDoc(BasDoc basDoc);

    /**
     * 修改文件名称
     *
     * @param basDoc 文件名称
     * @return 结果
     */
    int updateBasDoc(BasDoc basDoc);

    /**
     * 删除文件名称
     *
     * @param docId 文件名称主键
     * @return 结果
     */
    int deleteBasDocByDocId(Long docId);

    /**
     * 批量删除文件名称
     *
     * @param docIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasDocByDocIds(Long[] docIds);
}
