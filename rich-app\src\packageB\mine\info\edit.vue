<template>
  <view class="container">
    <view class="example">
      <view class="avatar-section">
        <open-data class="avatar" type="userAvatarUrl"></open-data>
        <!-- <image :src="user.avatar || '/static/images/default-avatar.png'" class="avatar"></image> -->
      </view>

      <form @submit="submit">
        <view class="form-title">个人资料</view>

        <view class="form-item">
          <view class="form-label">
            <text class="label-text">用户昵称</text>
          </view>
          <view class="form-content">
            <input v-model="user.wxNickName" class="input" name="wxNickName" placeholder="请输入昵称"
                   placeholder-class="input-placeholder"/>
          </view>
        </view>

        <view class="form-item">
          <view class="form-label">
            <text class="label-text">手机号码</text>
          </view>
          <view class="form-content">
            <input v-model="user.staffPhoneNum" class="input" name="staffPhoneNum" placeholder="请输入手机号码"
                   placeholder-class="input-placeholder" type="number"/>
            <text class="input-icon">📱</text>
          </view>
        </view>

        <!-- <view class="form-item">
          <view class="form-label">
            <text class="label-text">邮箱</text>
          </view>
          <view class="form-content">
            <input class="input" v-model="user.email" placeholder="请输入邮箱" name="email" placeholder-class="input-placeholder"/>
            <text class="input-icon">✉️</text>
          </view>
        </view> -->

        <button form-type="submit" type="primary">保存修改</button>
      </form>
    </view>
  </view>
</template>

<script>
import {getUserProfile} from "@/api/system/user"
import {updateUserProfileByWx} from "@/api/system/user"

export default {
  data() {
    return {
      user: {
        nickName: "",
        phonenumber: "",
        email: "",
        sex: "",
        avatar: ""
      },
      sexs: [{
        text: '男',
        value: "0"
      }, {
        text: '女',
        value: "1"
      }]
    }
  },
  onShow() {
    this.getUser()
  },
  methods: {
    getUser() {
      getUserProfile().then(response => {
        console.log(response)
        this.user = response.data
      })
    },
    sexChange(e) {
      this.user.sex = e.detail.value
    },
    submit() {
      // 表单验证
      if (!this.user.wxNickName) {
        uni.showToast({
          title: '用户昵称不能为空',
          icon: 'none'
        })
        return
      }

      if (!this.user.staffPhoneNum) {
        uni.showToast({
          title: '手机号码不能为空',
          icon: 'none'
        })
        return
      }

      const phoneReg = /^1[3|4|5|6|7|8|9][0-9]\d{8}$/
      if (!phoneReg.test(this.user.staffPhoneNum)) {
        uni.showToast({
          title: '请输入正确的手机号码',
          icon: 'none'
        })
        return
      }

      // if (!this.user.email) {
      //   uni.showToast({
      //     title: '邮箱地址不能为空',
      //     icon: 'none'
      //   })
      //   return
      // }

      // const emailReg = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(\.[a-zA-Z0-9_-])+/
      // if (!emailReg.test(this.user.email)) {
      //   uni.showToast({
      //     title: '请输入正确的邮箱地址',
      //     icon: 'none'
      //   })
      //   return
      // }

      const data = {
        wxNickName: this.user.wxNickName,
        PhoneNum: this.user.staffPhoneNum,
        // email: this.user.email,
        // sex: this.user.sex
      }

      updateUserProfileByWx(data).then(response => {
        console.log(response)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
page {
  background-color: #f8f8f8;
}

.container {
  padding-bottom: 30rpx;
}

.example {
  margin: 20rpx;
  border-radius: 16rpx;
  background-color: #fff;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 50rpx 0;
  background: linear-gradient(135deg, #44ADFB, #5C7CFA);
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    bottom: -30rpx;
    left: 0;
    right: 0;
    height: 60rpx;
    background-color: #fff;
    border-radius: 50% 50% 0 0;
  }

  .avatar {
    width: 180rpx;
    height: 180rpx;
    border-radius: 50%;
    border: 6rpx solid rgba(255, 255, 255, 0.8);
    overflow: hidden;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  }
}

form {
  padding: 0 40rpx 40rpx;
}

.form-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin: 30rpx 0;
  position: relative;
  padding-left: 24rpx;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 8rpx;
    height: 32rpx;
    background: linear-gradient(to bottom, #44ADFB, #5C7CFA);
    border-radius: 4rpx;
  }
}

.form-item {
  padding: 28rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-label {
  margin-bottom: 18rpx;

  .label-text {
    font-size: 28rpx;
    color: #666;
    font-weight: 500;
    position: relative;
    padding-left: 16rpx;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 6rpx;
      height: 20rpx;
      background-color: #5C7CFA;
      border-radius: 3rpx;
    }
  }
}

.form-content {
  position: relative;
}

.input {
  height: 88rpx;
  font-size: 30rpx;
  color: #333;
  width: 100%;
  padding: 0 80rpx 0 20rpx;
  background-color: #f9f9f9;
  border-radius: 44rpx;
}

.input-placeholder {
  color: #bbb;
}

.input-icon {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #999;
}

.radio-group {
  display: flex;
  flex-wrap: wrap;
  padding: 10rpx 0;
}

.radio-item {
  display: flex;
  align-items: center;
  margin-right: 60rpx;
  font-size: 30rpx;
  color: #333;
  padding: 10rpx 0;
}

.radio-item text {
  margin-left: 8rpx;
}

.wx-nickname {
  height: 88rpx;
  line-height: 88rpx;
  font-size: 30rpx;
  color: #333;
  background-color: #f9f9f9;
  border-radius: 44rpx;
  padding: 0 20rpx;
  margin-bottom: 16rpx;
}

button[type="primary"] {
  margin-top: 60rpx;
  height: 90rpx;
  line-height: 90rpx;
  font-size: 32rpx;
  border-radius: 45rpx;
  background: linear-gradient(to right, #44ADFB, #5C7CFA);
  box-shadow: 0 8rpx 20rpx rgba(92, 124, 250, 0.3);
  border: none;
  letter-spacing: 2rpx;
  position: relative;
  overflow: hidden;

  &::after {
    border: none;
  }

  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 10rpx rgba(92, 124, 250, 0.3);
  }
}
</style>
