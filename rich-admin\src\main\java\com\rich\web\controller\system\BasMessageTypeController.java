package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasMessageType;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasMessageTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 【请填写功能名称】Controller
 *
 * <AUTHOR>
 * @date 2023-02-21
 */
@RestController
@RequestMapping("/system/messagetype")

public class BasMessageTypeController extends BaseController {

    @Autowired
    private BasMessageTypeService basMessageTypeService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:messagetype:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasMessageType basMessageType) {
        startPage();
        List<BasMessageType> list = basMessageTypeService.selectBasMessageTypeList(basMessageType);
        return getDataTable(list);
    }

    @GetMapping("/selectList")
    public AjaxResult selectList(BasMessageType basMessageType) {
        List<BasMessageType> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "messageType");
        if (list == null) {
            basMessageType.setStatus("0");
            list = basMessageTypeService.selectBasMessageTypeList(basMessageType);
            redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "messageType");
            redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "messageType", list);
        }
        return AjaxResult.success(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:messagetype:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasMessageType basMessageType) {
        List<BasMessageType> list = basMessageTypeService.selectBasMessageTypeList(basMessageType);
        ExcelUtil<BasMessageType> util = new ExcelUtil<BasMessageType>(BasMessageType.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:messagetype:edit')")
    @GetMapping(value = "/{messageTypeId}")
    public AjaxResult getInfo(@PathVariable("messageTypeId") Long messageTypeId) {
        return AjaxResult.success(basMessageTypeService.selectBasMessageTypeByMessageTypeId(messageTypeId));
    }

    /**
     * 新增【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:messagetype:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasMessageType basMessageType) {
        return toAjax(basMessageTypeService.insertBasMessageType(basMessageType));
    }

    /**
     * 修改【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:messagetype:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasMessageType basMessageType) {
        return toAjax(basMessageTypeService.updateBasMessageType(basMessageType));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:messagetype:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasMessageType basMessageType) {
        basMessageType.setUpdateBy(getUserId());
        return toAjax(basMessageTypeService.changeStatus(basMessageType));
    }

    /**
     * 删除【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:messagetype:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{messageTypeIds}")
    public AjaxResult remove(@PathVariable Long[] messageTypeIds) {
        return toAjax(basMessageTypeService.deleteBasMessageTypeByMessageTypeIds(messageTypeIds));
    }
}
