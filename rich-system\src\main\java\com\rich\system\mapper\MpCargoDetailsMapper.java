package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.MpCargoDetails;
import org.apache.ibatis.annotations.Mapper;

/**
 * 客户在仓货物明细Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Mapper
public interface MpCargoDetailsMapper {
    /**
     * 查询客户在仓货物明细
     *
     * @param cargoDetailsId 客户在仓货物明细主键
     * @return 客户在仓货物明细
     */
    MpCargoDetails selectMpCargoDetailsByCargoDetailsId(Long cargoDetailsId);

    /**
     * 查询客户在仓货物明细列表
     *
     * @param mpCargoDetails 客户在仓货物明细
     * @return 客户在仓货物明细集合
     */
    List<MpCargoDetails> selectMpCargoDetailsList(MpCargoDetails mpCargoDetails);

    /**
     * 新增客户在仓货物明细
     *
     * @param mpCargoDetails 客户在仓货物明细
     * @return 结果
     */
    int insertMpCargoDetails(MpCargoDetails mpCargoDetails);

    /**
     * 修改客户在仓货物明细
     *
     * @param mpCargoDetails 客户在仓货物明细
     * @return 结果
     */
    int updateMpCargoDetails(MpCargoDetails mpCargoDetails);

    /**
     * 删除客户在仓货物明细
     *
     * @param cargoDetailsId 客户在仓货物明细主键
     * @return 结果
     */
    int deleteMpCargoDetailsByCargoDetailsId(Long cargoDetailsId);

    /**
     * 批量删除客户在仓货物明细
     *
     * @param cargoDetailsIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteMpCargoDetailsByCargoDetailsIds(Long[] cargoDetailsIds);
}
