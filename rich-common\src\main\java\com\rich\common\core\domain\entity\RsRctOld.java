package com.rich.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import com.rich.common.utils.SecurityUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 操作单列表对象 rs_rct
 *
 * <AUTHOR>
 * @date 2023-08-15
 */
public class RsRctOld extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 订单
     */
    private Long rctId;

    /**
     * 操作单号
     */
    @Excel(name = "操作单号")
    private String rctNo;

    /**
     * 操作日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "操作日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rctOpDate;

    /**
     * 操作员
     */
    @Excel(name = "操作员")
    private Long opId;

    private String opName;

    /**
     * 操作分配标记
     */
    private Integer isOpAllotted;

    /**
     * 订舱员
     */
    @Excel(name = "订舱员")
    private Long bookingOpId;

    private String bookingOpName;

    /**
     * 单证员
     */
    @Excel(name = "单证员")
    private Long docOpId;

    private String docOpName;

    /**
     * 协助操作
     */
    @Excel(name = "协助操作")
    private Long opObserverId;

    private String opObserverName;

    /**
     * 订舱单号
     */
    @Excel(name = "订舱单号")
    private String newBookingNo;

    /**
     * 订舱日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "订舱日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date newBookingTime;

    /**
     * 报价单号
     */
    @Excel(name = "报价单号")
    private String quotationNo;

    /**
     * 报价日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "报价日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date quotationDate;

    /**
     * 业务员
     */
    @Excel(name = "业务员")
    private Long salesId;

    private String salesName;

    /**
     * 业务助理
     */
    @Excel(name = "业务助理")
    private Long salesAssistantId;

    private String salesAssistantName;

    /**
     * 协助业务
     */
    @Excel(name = "协助业务")
    private Long salesObserverId;

    private Integer isPsaVerified;
    @Excel(name = "商务审核")
    private Long verifyPsaId;

    private String psaName;

    /**
     * 商务审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "商务审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date psaVerifyTime;

    /**
     * 紧急程度
     */
    @Excel(name = "紧急程度")
    private String urgencyDegree;

    /**
     * 收付方式
     */
    @Excel(name = "收付方式")
    private Long paymentTypeId;

    private String paymentTypeName;

    /**
     * 放货方式
     */
    @Excel(name = "放货方式")
    private Long releaseTypeId;

    private String releaseTypeName;

    /**
     * 进度状态
     */
    @Excel(name = "进度状态")
    private Long processStatusId;

    private String processStatusName;

    /**
     * 委托单位
     */
    @Excel(name = "委托单位")
    private Long clientId;

    private String clientName;

    /**
     * 客户角色
     */
    @Excel(name = "客户角色")
    private Long clientRoleId;

    private String clientRoleName;

    /**
     * 联系人
     */
    @Excel(name = "联系人")
    private String clientContactor;

    /**
     * 联系人电话
     */
    @Excel(name = "联系人电话")
    private String clientContactorTel;

    /**
     * 邮箱
     */
    @Excel(name = "邮箱")
    private String clientContactorEmail;

    /**
     * 关联单位
     */
    @Excel(name = "关联单位")
    private String relationClientIds;

    /**
     * 进出口
     */
    @Excel(name = "进出口")
    private Integer impExpTypeId;

    /**
     * 收汇方式
     */
    @Excel(name = "收汇方式")
    private Long tradingPaymentChannelId;

    private String tradingPaymentChannelName;

    /**
     * 贸易条款
     */
    @Excel(name = "贸易条款")
    private Long tradingTermsId;

    private String tradingTermsName;

    /**
     * 运输条款
     */
    @Excel(name = "运输条款")
    private Long logisticsTermsId;

    private String logisticsTermsName;

    /**
     * 合同号
     */
    @Excel(name = "合同号")
    private String clientContractNo;

    /**
     * 发票号
     */
    @Excel(name = "发票号")
    private String clientInvoiceNo;

    /**
     * 货名概要
     */
    @Excel(name = "货名概要")
    private String goodsNameSummary;

    /**
     * 件数
     */
    @Excel(name = "件数")
    private Integer packageQuantity;

    /**
     * 毛重
     */
    @Excel(name = "毛重")
    private BigDecimal grossWeight;

    /**
     * 重量单位
     */
    @Excel(name = "重量单位")
    private Long weightUnitId;

    private String weightUnitName;

    /**
     * 总体积
     */
    @Excel(name = "总体积")
    private BigDecimal volume;

    /**
     * 体积单位
     */
    @Excel(name = "体积单位")
    private Long volumeUnitId;

    private String volumeUnitName;

    /**
     * 货物特征
     */
    @Excel(name = "货物特征")
    private Long[] cargoTypeIds;

    /**
     * 总货值
     */
    @Excel(name = "总货值")
    private BigDecimal goodsValue;

    /**
     * 货值币种
     */
    @Excel(name = "货值币种")
    private Long goodsCurrencyId;

    private String goodsCurrencyName;

    /**
     * 货物限重
     */
    @Excel(name = "货物限重")
    private BigDecimal maxWeight;

    /**
     * 计费货量
     */
    @Excel(name = "计费货量")
    private String revenueTons;

    /**
     * 物流类型
     */
    @Excel(name = "物流类型")
    private Long logisticsTypeId;

    private String logisticsTypeName;

    /**
     * 启运港
     */
    @Excel(name = "启运港")
    private Long polId;

    private Long[] polIds;

    /**
     * 目的港
     */
    @Excel(name = "目的港")
    private Long destinationPortId;

    private Long[] destinationPortIds;

    /**
     * 承运人
     */
    @Excel(name = "承运人")
    private Long[] carrierIds;

    /**
     * 船期
     */
    @Excel(name = "船期")
    private String schedule;

    /**
     * 有效期
     */
    @Excel(name = "有效期")
    private Date validTimeFrom;

    /**
     * 有效期
     */
    @Excel(name = "有效期")
    private Date validDateTo;

    private Long localBasicPortId;

    private Long transitPortId;

    private Long podId;

    private Date destinationPortEta;

    /**
     * 主提单
     */
    @Excel(name = "主提单")
    private String isMblNeeded;

    /**
     * 主提单号
     */
    @Excel(name = "主提单号")
    private String mblNo;

    /**
     * 套约
     */
    @Excel(name = "套约")
    private String isUnderAgreementMbl;

    /**
     * 清关中转
     */
    @Excel(name = "清关中转")
    private String isCustomsIntransitMbl;

    /**
     * 转单
     */
    @Excel(name = "转单")
    private String isSwitchMbl;

    /**
     * 拆单
     */
    @Excel(name = "拆单")
    private String isDividedMbl;

    /**
     * 出单方式
     */
    @Excel(name = "出单方式")
    private Long mblIssueTypeId;

    private String mblIssueTypeName;

    /**
     * 取单方式
     */
    @Excel(name = "取单方式")
    private Long mblGetWayId;

    private String mblGetWayName;

    /**
     * 交单方式
     */
    @Excel(name = "交单方式")
    private Long mblReleaseWayId;

    private String mblReleaseWayName;

    /**
     * 货代提单
     */
    @Excel(name = "货代提单")
    private String isHblNeeded;

    /**
     * 货代单号
     */
    @Excel(name = "货代单号")
    private String hblNoList;

    /**
     * 套约
     */
    @Excel(name = "套约")
    private String isUnderAgreementHbl;

    /**
     * 清关中转
     */
    @Excel(name = "清关中转")
    private String isCustomsIntransitHbl;

    /**
     * 转单
     */
    @Excel(name = "转单")
    private String isSwitchHbl;

    /**
     * 拆单
     */
    @Excel(name = "拆单")
    private String isDividedHbl;

    /**
     * 出单方式
     */
    @Excel(name = "出单方式")
    private Long hblIssueTypeId;

    private String hblIssueTypeName;

    /**
     * 取单方式
     */
    @Excel(name = "取单方式")
    private Long hblGetWayId;

    private String hblGetWayName;

    /**
     * 交单方式
     */
    @Excel(name = "交单方式")
    private Long hblReleaseWayId;

    private String hblReleaseWayName;

    /**
     * 服务类型
     */
    @Excel(name = "服务类型")
    private Long[] serviceTypeIds;

    /**
     * 业务报价综述
     */
    @Excel(name = "业务报价综述")
    private String quotationSummary;

    /**
     * 业务订舱备注
     */
    @Excel(name = "业务订舱备注")
    private String newBookingRemark;

    /**
     * 业务须知
     */
    @Excel(name = "业务须知")
    private String inquiryNotice;

    /**
     * 商务备注
     */
    @Excel(name = "商务备注")
    private String inquiryInnerRemark;

    /**
     * 操作主管备注
     */
    @Excel(name = "操作主管备注")
    private String opLeaderRemark;

    /**
     * 操作备注
     */
    @Excel(name = "操作备注")
    private String opInnerRemark;

    /**
     * 合约类型
     */
    @Excel(name = "合约类型")
    private Long agreementTypeId;

    /**
     * 合约号
     */
    @Excel(name = "合约号")
    private String agreementNo;

    /**
     * so号
     */
    @Excel(name = "so号")
    private String soNo;

    /**
     * 柜号
     */
    @Excel(name = "柜号")
    private String containerNo;

    /**
     * 封号
     */
    @Excel(name = "封号")
    private String sealNo;

    /**
     * 订舱概述
     */
    @Excel(name = "订舱概述")
    private String bookingDetail;

    /**
     * 装柜时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "装柜时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date precarriageTime;

    /**
     * 截关时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "截关时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date cvClosingTime;

    /**
     * 报关
     */
    @Excel(name = "报关")
    private String cvDeclaringTime;

    /**
     * 截VGM
     */
    @Excel(name = "截VGM")
    private String vgmClosingTime;

    /**
     * 截SI
     */
    @Excel(name = "截SI")
    private String siClosingTime;

    /**
     * 拖车
     */
    @Excel(name = "拖车")
    private String trailer;

    /**
     * 船次
     */
    @Excel(name = "船次")
    private String firstVoyage;

    /**
     * 到港时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "到港时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date podETA;

    /**
     * 电放方式
     */
    @Excel(name = "电放方式")
    private String telexReleaseType;

    /**
     * 是否放货
     */
    @Excel(name = "是否放货")
    private String isReleasable;

    /**
     * 发资料给代理
     */
    @Excel(name = "发资料给代理")
    private String sendToAgent;

    /**
     * 船名
     */
    @Excel(name = "船名")
    private String firstVessel;

    /**
     * 大船时间
     */
    @Excel(name = "大船时间", width = 30)
    private String shipTime;

    /**
     * 操作备注
     */
    @Excel(name = "备注")
    private String remark;

    /**
     * 订单难度
     */
    @Excel(name = "订单难度")
    private String orderDifficulty;

    /**
     * 订舱口
     */
    @Excel(name = "订舱口")
    private Long bookingAgent;

    /**
     * 承运人
     */
    @Excel(name = "承运人Id")
    private Long carrier;

    /**
     * 承运人
     */
    @Excel(name = "承运人")
    private String carrierEnName;

    /**
     * 订舱口名
     */
    @Excel(name = "订舱口名")
    private String bookingAgentName;

    /**
     * 物流类型英文简称
     */
    @Excel(name = "物流类型英文简称")
    private String logisticsTypeEnName;
    private String readOnly;
    private Date psaVerifyFrom;
    private Date psaVerifyTo;
    private Date rctOperationFrom;
    private Date rctOperationTo;
    private String pol;
    private String destinationPort;
    private String cargoTypes;
    private String carriers;
    private String serviceTypes;
    private Long[] lineIds;
    private Long[] permissionLevel;
    private RsRctLogisticsTypeBasicInfo rsRctLogisticsTypeBasicInfo;
    private RsRctPreCarriageBasicInfo rsRctPreCarriageBasicInfo;
    private RsRctExportDeclarationBasicInfo rsRctExportDeclarationBasicInfo;
    private RsRctImportClearanceBasicInfo rsRctImportClearanceBasicInfo;
    private List<Long> rctIds;

    public Date getValidDateTo() {
        return validDateTo;
    }

    public void setValidDateTo(Date validDateTo) {
        this.validDateTo = validDateTo;
    }

    public Long getLocalBasicPortId() {
        return localBasicPortId;
    }

    public void setLocalBasicPortId(Long localBasicPortId) {
        this.localBasicPortId = localBasicPortId;
    }

    public Long getTransitPortId() {
        return transitPortId;
    }

    public void setTransitPortId(Long transitPortId) {
        this.transitPortId = transitPortId;
    }

    public Long getPodId() {
        return podId;
    }

    public void setPodId(Long podId) {
        this.podId = podId;
    }

    public Date getDestinationPortEta() {
        return destinationPortEta;
    }

    public void setDestinationPortEta(Date destinationPortEta) {
        this.destinationPortEta = destinationPortEta;
    }

    public String getLogisticsTypeEnName() {
        return logisticsTypeEnName;
    }

    public void setLogisticsTypeEnName(String logisticsTypeEnName) {
        this.logisticsTypeEnName = logisticsTypeEnName;
    }

    public String getCarrierEnName() {
        return carrierEnName;
    }

    public void setCarrierEnName(String carrierEnName) {
        this.carrierEnName = carrierEnName;
    }

    public String getBookingAgentName() {
        return bookingAgentName;
    }

    public void setBookingAgentName(String bookingAgentName) {
        this.bookingAgentName = bookingAgentName;
    }

    public Long[] getPermissionLevel() {
        return permissionLevel;
    }

    public void setPermissionLevel(Long[] permissionLevel) {
        this.permissionLevel = permissionLevel;
    }

    public Long getCarrier() {
        return carrier;
    }

    public void setCarrier(Long carrier) {
        this.carrier = carrier;
    }

    public Long getBookingAgent() {
        return bookingAgent;
    }

    public void setBookingAgent(Long bookingAgent) {
        this.bookingAgent = bookingAgent;
    }

    public String getOrderDifficulty() {
        return orderDifficulty;
    }

    public void setOrderDifficulty(String orderDifficulty) {
        this.orderDifficulty = orderDifficulty;
    }

    public String getSoNo() {
        return soNo;
    }

    public void setSoNo(String soNo) {
        this.soNo = soNo;
    }

    public String getContainerNo() {
        return containerNo;
    }

    public void setContainerNo(String containerNo) {
        this.containerNo = containerNo;
    }

    public String getSealNo() {
        return sealNo;
    }

    public void setSealNo(String sealNo) {
        this.sealNo = sealNo;
    }

    public String getBookingDetail() {
        return bookingDetail;
    }

    public void setBookingDetail(String bookingDetail) {
        this.bookingDetail = bookingDetail;
    }

    public Date getPrecarriageTime() {
        return precarriageTime;
    }

    public void setPrecarriageTime(Date precarriageTime) {
        this.precarriageTime = precarriageTime;
    }

    public Date getCvClosingTime() {
        return cvClosingTime;
    }

    public void setCvClosingTime(Date cvClosingTime) {
        this.cvClosingTime = cvClosingTime;
    }

    public String getCvDeclaringTime() {
        return cvDeclaringTime;
    }

    public void setCvDeclaringTime(String cvDeclaringTime) {
        this.cvDeclaringTime = cvDeclaringTime;
    }

    public String getVgmClosingTime() {
        return vgmClosingTime;
    }

    public void setVgmClosingTime(String vgmClosingTime) {
        this.vgmClosingTime = vgmClosingTime;
    }

    public String getSiClosingTime() {
        return siClosingTime;
    }

    public void setSiClosingTime(String siClosingTime) {
        this.siClosingTime = siClosingTime;
    }

    public String getTrailer() {
        return trailer;
    }

    public void setTrailer(String trailer) {
        this.trailer = trailer;
    }

    public String getFirstVoyage() {
        return firstVoyage;
    }

    public void setFirstVoyage(String firstVoyage) {
        this.firstVoyage = firstVoyage;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getPodETA() {
        return podETA;
    }

    public void setPodETA(Date podETA) {
        this.podETA = podETA;
    }

    public String getTelexReleaseType() {
        return telexReleaseType;
    }

    public void setTelexReleaseType(String telexReleaseType) {
        this.telexReleaseType = telexReleaseType;
    }

    public String getIsReleasable() {
        return isReleasable;
    }

    public void setIsReleasable(String isReleasable) {
        this.isReleasable = isReleasable;
    }

    public String getSendToAgent() {
        return sendToAgent;
    }

    public void setSendToAgent(String sendToAgent) {
        this.sendToAgent = sendToAgent;
    }

    public String getFirstVessel() {
        return firstVessel;
    }

    public void setFirstVessel(String firstVessel) {
        this.firstVessel = firstVessel;
    }

    public String getShipTime() {
        return shipTime;
    }

    public void setShipTime(String shipTime) {
        this.shipTime = shipTime;
    }

    public Long[] getLineIds() {
        return lineIds;
    }

    public void setLineIds(Long[] lineIds) {
        this.lineIds = lineIds;
    }

    public Long[] getPolIds() {
        return polIds;
    }

    public void setPolIds(Long[] polIds) {
        this.polIds = polIds;
    }

    public Long[] getDestinationPortIds() {
        return destinationPortIds;
    }

    public void setDestinationPortIds(Long[] destinationPortIds) {
        this.destinationPortIds = destinationPortIds;
    }

    public Long getPolId() {
        return polId;
    }

    public void setPolId(Long polId) {
        this.polId = polId;
    }

    public Long getDestinationPortId() {
        return destinationPortId;
    }

    public void setDestinationPortId(Long destinationPortId) {
        this.destinationPortId = destinationPortId;
    }

    public String getPol() {
        return pol;
    }

    public void setPol(String pol) {
        this.pol = pol;
    }

    public String getDestinationPort() {
        return destinationPort;
    }

    public void setDestinationPort(String destinationPort) {
        this.destinationPort = destinationPort;
    }

    public String getCargoTypes() {
        return cargoTypes;
    }

    public void setCargoTypes(String cargoTypes) {
        this.cargoTypes = cargoTypes;
    }

    public String getCarriers() {
        return carriers;
    }

    public void setCarriers(String carriers) {
        this.carriers = carriers;
    }

    public String getServiceTypes() {
        return serviceTypes;
    }

    public void setServiceTypes(String serviceTypes) {
        this.serviceTypes = serviceTypes;
    }

    public String getBookingOpName() {
        return bookingOpName;
    }

    public void setBookingOpName(String bookingOpName) {
        this.bookingOpName = bookingOpName;
    }

    public String getDocOpName() {
        return docOpName;
    }

    public void setDocOpName(String docOpName) {
        this.docOpName = docOpName;
    }

    public String getOpObserverName() {
        return opObserverName;
    }

    public void setOpObserverName(String opObserverName) {
        this.opObserverName = opObserverName;
    }

    public String getPaymentTypeName() {
        return paymentTypeName;
    }

    public void setPaymentTypeName(String paymentTypeName) {
        this.paymentTypeName = paymentTypeName;
    }

    public String getReleaseTypeName() {
        return releaseTypeName;
    }

    public void setReleaseTypeName(String releaseTypeName) {
        this.releaseTypeName = releaseTypeName;
    }

    public String getProcessStatusName() {
        return processStatusName;
    }

    public void setProcessStatusName(String processStatusName) {
        this.processStatusName = processStatusName;
    }

    public String getClientRoleName() {
        return clientRoleName;
    }

    public void setClientRoleName(String clientRoleName) {
        this.clientRoleName = clientRoleName;
    }

    public String getTradingPaymentChannelName() {
        return tradingPaymentChannelName;
    }

    public void setTradingPaymentChannelName(String tradingPaymentChannelName) {
        this.tradingPaymentChannelName = tradingPaymentChannelName;
    }

    public String getTradingTermsName() {
        return tradingTermsName;
    }

    public void setTradingTermsName(String tradingTermsName) {
        this.tradingTermsName = tradingTermsName;
    }

    public String getLogisticsTermsName() {
        return logisticsTermsName;
    }

    public void setLogisticsTermsName(String logisticsTermsName) {
        this.logisticsTermsName = logisticsTermsName;
    }

    public String getWeightUnitName() {
        return weightUnitName;
    }

    public void setWeightUnitName(String weightUnitName) {
        this.weightUnitName = weightUnitName;
    }

    public String getVolumeUnitName() {
        return volumeUnitName;
    }

    public void setVolumeUnitName(String volumeUnitName) {
        this.volumeUnitName = volumeUnitName;
    }

    public String getGoodsCurrencyName() {
        return goodsCurrencyName;
    }

    public void setGoodsCurrencyName(String goodsCurrencyName) {
        this.goodsCurrencyName = goodsCurrencyName;
    }

    public String getLogisticsTypeName() {
        return logisticsTypeName;
    }

    public void setLogisticsTypeName(String logisticsTypeName) {
        this.logisticsTypeName = logisticsTypeName;
    }

    public String getMblIssueTypeName() {
        return mblIssueTypeName;
    }

    public void setMblIssueTypeName(String mblIssueTypeName) {
        this.mblIssueTypeName = mblIssueTypeName;
    }

    public String getMblGetWayName() {
        return mblGetWayName;
    }

    public void setMblGetWayName(String mblGetWayName) {
        this.mblGetWayName = mblGetWayName;
    }

    public String getMblReleaseWayName() {
        return mblReleaseWayName;
    }

    public void setMblReleaseWayName(String mblReleaseWayName) {
        this.mblReleaseWayName = mblReleaseWayName;
    }

    public String getHblIssueTypeName() {
        return hblIssueTypeName;
    }

    public void setHblIssueTypeName(String hblIssueTypeName) {
        this.hblIssueTypeName = hblIssueTypeName;
    }

    public String getHblGetWayName() {
        return hblGetWayName;
    }

    public void setHblGetWayName(String hblGetWayName) {
        this.hblGetWayName = hblGetWayName;
    }

    public String getHblReleaseWayName() {
        return hblReleaseWayName;
    }

    public void setHblReleaseWayName(String hblReleaseWayName) {
        this.hblReleaseWayName = hblReleaseWayName;
    }

    public String getOpName() {
        return opName;
    }

    public void setOpName(String opName) {
        this.opName = opName;
    }

    public String getSalesName() {
        return salesName;
    }

    public void setSalesName(String salesName) {
        this.salesName = salesName;
    }

    public String getSalesAssistantName() {
        return salesAssistantName;
    }

    public void setSalesAssistantName(String salesAssistantName) {
        this.salesAssistantName = salesAssistantName;
    }

    public String getPsaName() {
        return psaName;
    }

    public void setPsaName(String psaName) {
        this.psaName = psaName;
    }

    public Date getRctOperationFrom() {
        return rctOperationFrom;
    }

    public void setRctOperationFrom(Date rctOperationFrom) {
        this.rctOperationFrom = rctOperationFrom;
    }

    public Date getRctOperationTo() {
        return rctOperationTo;
    }

    public void setRctOperationTo(Date rctOperationTo) {
        this.rctOperationTo = rctOperationTo;
    }

    public Date getPsaVerifyFrom() {
        return psaVerifyFrom;
    }

    public void setPsaVerifyFrom(Date psaVerifyFrom) {
        this.psaVerifyFrom = psaVerifyFrom;
    }

    public Date getPsaVerifyTo() {
        return psaVerifyTo;
    }

    public void setPsaVerifyTo(Date psaVerifyTo) {
        this.psaVerifyTo = psaVerifyTo;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public Integer getIsOpAllotted() {
        return isOpAllotted;
    }

    public void setIsOpAllotted(Integer isOpAllotted) {
        this.isOpAllotted = isOpAllotted;
    }

    public Integer getIsPsaVerified() {
        return isPsaVerified;
    }

    public void setIsPsaVerified(Integer isPsaVerified) {
        this.isPsaVerified = isPsaVerified;
    }

    public Long[] getCargoTypeIds() {
        return cargoTypeIds;
    }

    public void setCargoTypeIds(Long[] cargoTypeIds) {
        this.cargoTypeIds = cargoTypeIds;
    }

    public Long[] getCarrierIds() {
        return carrierIds;
    }

    public void setCarrierIds(Long[] carrierIds) {
        this.carrierIds = carrierIds;
    }

    public Long[] getServiceTypeIds() {
        return serviceTypeIds;
    }

    public void setServiceTypeIds(Long[] serviceTypeIds) {
        this.serviceTypeIds = serviceTypeIds;
    }

    public List<Long> getRctIds() {
        return rctIds;
    }

    public void setRctIds(List<Long> rctIds) {
        this.rctIds = rctIds;
    }

    public String getReadOnly() {
        return readOnly;
    }

    public void setReadOnly(String readOnly) {
        this.readOnly = readOnly;
    }

    public RsRctLogisticsTypeBasicInfo getRsRctLogisticsTypeBasicInfo() {
        return rsRctLogisticsTypeBasicInfo;
    }

    public void setRsRctLogisticsTypeBasicInfo(RsRctLogisticsTypeBasicInfo rsRctLogisticsTypeBasicInfo) {
        this.rsRctLogisticsTypeBasicInfo = rsRctLogisticsTypeBasicInfo;
    }

    public RsRctPreCarriageBasicInfo getRsRctPreCarriageBasicInfo() {
        return rsRctPreCarriageBasicInfo;
    }

    public void setRsRctPreCarriageBasicInfo(RsRctPreCarriageBasicInfo rsRctPreCarriageBasicInfo) {
        this.rsRctPreCarriageBasicInfo = rsRctPreCarriageBasicInfo;
    }


    public RsRctExportDeclarationBasicInfo getRsRctExportDeclarationBasicInfo() {
        return rsRctExportDeclarationBasicInfo;
    }

    public void setRsRctExportDeclarationBasicInfo(RsRctExportDeclarationBasicInfo rsRctExportDeclarationBasicInfo) {
        this.rsRctExportDeclarationBasicInfo = rsRctExportDeclarationBasicInfo;
    }

    public RsRctImportClearanceBasicInfo getRsRctImportClearanceBasicInfo() {
        return rsRctImportClearanceBasicInfo;
    }

    public void setRsRctImportClearanceBasicInfo(RsRctImportClearanceBasicInfo rsRctImportClearanceBasicInfo) {
        this.rsRctImportClearanceBasicInfo = rsRctImportClearanceBasicInfo;
    }

    public Long getRctId() {
        return rctId;
    }

    public void setRctId(Long rctId) {
        this.rctId = rctId;
    }

    public String getRctNo() {
        return rctNo;
    }

    public void setRctNo(String rctNo) {
        this.rctNo = rctNo;
    }

    public Date getRctOpDate() {
        return rctOpDate;
    }

    public void setRctOpDate(Date rctOpDate) {
        this.rctOpDate = rctOpDate;
    }

    public Long getOpId() {
        return opId;
    }

    public void setOpId(Long opId) {
        this.opId = opId;
    }

    public Long getBookingOpId() {
        return bookingOpId;
    }

    public void setBookingOpId(Long bookingOpId) {
        this.bookingOpId = bookingOpId;
    }

    public Long getDocOpId() {
        return docOpId;
    }

    public void setDocOpId(Long docOpId) {
        this.docOpId = docOpId;
    }

    public Long getOpObserverId() {
        return opObserverId;
    }

    public void setOpObserverId(Long opObserverId) {
        this.opObserverId = opObserverId;
    }

    public String getNewBookingNo() {
        return newBookingNo;
    }

    public void setNewBookingNo(String newBookingNo) {
        this.newBookingNo = newBookingNo;
    }

    public Date getNewBookingTime() {
        return newBookingTime;
    }

    public void setNewBookingTime(Date newBookingTime) {
        this.newBookingTime = newBookingTime;
    }

    public String getQuotationNo() {
        return quotationNo;
    }

    public void setQuotationNo(String quotationNo) {
        this.quotationNo = quotationNo;
    }

    public Date getQuotationDate() {
        return quotationDate;
    }

    public void setQuotationDate(Date quotationDate) {
        this.quotationDate = quotationDate;
    }

    public Long getSalesId() {
        return salesId;
    }

    public void setSalesId(Long salesId) {
        this.salesId = salesId;
    }

    public Long getSalesAssistantId() {
        return salesAssistantId;
    }

    public void setSalesAssistantId(Long salesAssistantId) {
        this.salesAssistantId = salesAssistantId;
    }

    public Long getSalesObserverId() {
        return salesObserverId;
    }

    public void setSalesObserverId(Long salesObserverId) {
        this.salesObserverId = salesObserverId;
    }

    public Long getVerifyPsaId() {
        return verifyPsaId;
    }

    public void setVerifyPsaId(Long verifyPsaId) {
        this.verifyPsaId = verifyPsaId;
    }

    public Date getPsaVerifyTime() {
        return psaVerifyTime;
    }

    public void setPsaVerifyTime(Date psaVerifyTime) {
        this.psaVerifyTime = psaVerifyTime;
    }

    public String getUrgencyDegree() {
        return urgencyDegree;
    }

    public void setUrgencyDegree(String urgencyDegree) {
        this.urgencyDegree = urgencyDegree;
    }

    public Long getPaymentTypeId() {
        return paymentTypeId;
    }

    public void setPaymentTypeId(Long paymentTypeId) {
        this.paymentTypeId = paymentTypeId;
    }

    public Long getReleaseTypeId() {
        return releaseTypeId;
    }

    public void setReleaseTypeId(Long releaseTypeId) {
        this.releaseTypeId = releaseTypeId;
    }

    public Long getProcessStatusId() {
        return processStatusId;
    }

    public void setProcessStatusId(Long processStatusId) {
        this.processStatusId = processStatusId;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public Long getClientRoleId() {
        return clientRoleId;
    }

    public void setClientRoleId(Long clientRoleId) {
        this.clientRoleId = clientRoleId;
    }

    public String getClientContactor() {
        return clientContactor;
    }

    public void setClientContactor(String clientContactor) {
        this.clientContactor = clientContactor;
    }

    public String getClientContactorTel() {
        return clientContactorTel;
    }

    public void setClientContactorTel(String clientContactorTel) {
        this.clientContactorTel = clientContactorTel;
    }

    public String getClientContactorEmail() {
        return clientContactorEmail;
    }

    public void setClientContactorEmail(String clientContactorEmail) {
        this.clientContactorEmail = clientContactorEmail;
    }

    public String getRelationClientIds() {
        return relationClientIds;
    }

    public void setRelationClientIds(String relationClientIds) {
        this.relationClientIds = relationClientIds;
    }

    public Integer getImpExpTypeId() {
        return impExpTypeId;
    }

    public void setImpExpTypeId(Integer impExpTypeId) {
        this.impExpTypeId = impExpTypeId;
    }

    public Long getTradingPaymentChannelId() {
        return tradingPaymentChannelId;
    }

    public void setTradingPaymentChannelId(Long tradingPaymentChannelId) {
        this.tradingPaymentChannelId = tradingPaymentChannelId;
    }

    public Long getTradingTermsId() {
        return tradingTermsId;
    }

    public void setTradingTermsId(Long tradingTermsId) {
        this.tradingTermsId = tradingTermsId;
    }

    public Long getLogisticsTermsId() {
        return logisticsTermsId;
    }

    public void setLogisticsTermsId(Long logisticsTermsId) {
        this.logisticsTermsId = logisticsTermsId;
    }

    public String getClientContractNo() {
        return clientContractNo;
    }

    public void setClientContractNo(String clientContractNo) {
        this.clientContractNo = clientContractNo;
    }

    public String getClientInvoiceNo() {
        return clientInvoiceNo;
    }

    public void setClientInvoiceNo(String clientInvoiceNo) {
        this.clientInvoiceNo = clientInvoiceNo;
    }

    public String getGoodsNameSummary() {
        return goodsNameSummary;
    }

    public void setGoodsNameSummary(String goodsNameSummary) {
        this.goodsNameSummary = goodsNameSummary;
    }

    public Integer getPackageQuantity() {
        return packageQuantity;
    }

    public void setPackageQuantity(Integer packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public BigDecimal getGrossWeight() {
        return grossWeight;
    }

    public void setGrossWeight(BigDecimal grossWeight) {
        this.grossWeight = grossWeight;
    }

    public Long getWeightUnitId() {
        return weightUnitId;
    }

    public void setWeightUnitId(Long weightUnitId) {
        this.weightUnitId = weightUnitId;
    }

    public BigDecimal getVolume() {
        return volume;
    }

    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    public Long getVolumeUnitId() {
        return volumeUnitId;
    }

    public void setVolumeUnitId(Long volumeUnitId) {
        this.volumeUnitId = volumeUnitId;
    }

    public BigDecimal getGoodsValue() {
        return goodsValue;
    }

    public void setGoodsValue(BigDecimal goodsValue) {
        this.goodsValue = goodsValue;
    }

    public Long getGoodsCurrencyId() {
        return goodsCurrencyId;
    }

    public void setGoodsCurrencyId(Long goodsCurrencyId) {
        this.goodsCurrencyId = goodsCurrencyId;
    }

    public BigDecimal getMaxWeight() {
        return maxWeight;
    }

    public void setMaxWeight(BigDecimal maxWeight) {
        this.maxWeight = maxWeight;
    }

    public String getRevenueTons() {
        return revenueTons;
    }

    public void setRevenueTons(String revenueTons) {
        this.revenueTons = revenueTons;
    }

    public Long getLogisticsTypeId() {
        return logisticsTypeId;
    }

    public void setLogisticsTypeId(Long logisticsTypeId) {
        this.logisticsTypeId = logisticsTypeId;
    }

    public String getSchedule() {
        return schedule;
    }

    public void setSchedule(String schedule) {
        this.schedule = schedule;
    }

    public Date getValidTimeFrom() {
        return validTimeFrom;
    }

    public void setValidTimeFrom(Date validTimeFrom) {
        this.validTimeFrom = validTimeFrom;
    }

    public String getIsMblNeeded() {
        return isMblNeeded;
    }

    public void setIsMblNeeded(String isMblNeeded) {
        this.isMblNeeded = isMblNeeded;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getIsUnderAgreementMbl() {
        return isUnderAgreementMbl;
    }

    public void setIsUnderAgreementMbl(String isUnderAgreementMbl) {
        this.isUnderAgreementMbl = isUnderAgreementMbl;
    }

    public String getIsCustomsIntransitMbl() {
        return isCustomsIntransitMbl;
    }

    public void setIsCustomsIntransitMbl(String isCustomsIntransitMbl) {
        this.isCustomsIntransitMbl = isCustomsIntransitMbl;
    }

    public String getIsSwitchMbl() {
        return isSwitchMbl;
    }

    public void setIsSwitchMbl(String isSwitchMbl) {
        this.isSwitchMbl = isSwitchMbl;
    }

    public String getIsDividedMbl() {
        return isDividedMbl;
    }

    public void setIsDividedMbl(String isDividedMbl) {
        this.isDividedMbl = isDividedMbl;
    }

    public Long getMblIssueTypeId() {
        return mblIssueTypeId;
    }

    public void setMblIssueTypeId(Long mblIssueTypeId) {
        this.mblIssueTypeId = mblIssueTypeId;
    }

    public Long getMblGetWayId() {
        return mblGetWayId;
    }

    public void setMblGetWayId(Long mblGetWayId) {
        this.mblGetWayId = mblGetWayId;
    }

    public Long getMblReleaseWayId() {
        return mblReleaseWayId;
    }

    public void setMblReleaseWayId(Long mblReleaseWayId) {
        this.mblReleaseWayId = mblReleaseWayId;
    }

    public String getIsHblNeeded() {
        return isHblNeeded;
    }

    public void setIsHblNeeded(String isHblNeeded) {
        this.isHblNeeded = isHblNeeded;
    }

    public String getHblNoList() {
        return hblNoList;
    }

    public void setHblNoList(String hblNoList) {
        this.hblNoList = hblNoList;
    }

    public String getIsUnderAgreementHbl() {
        return isUnderAgreementHbl;
    }

    public void setIsUnderAgreementHbl(String isUnderAgreementHbl) {
        this.isUnderAgreementHbl = isUnderAgreementHbl;
    }

    public String getIsCustomsIntransitHbl() {
        return isCustomsIntransitHbl;
    }

    public void setIsCustomsIntransitHbl(String isCustomsIntransitHbl) {
        this.isCustomsIntransitHbl = isCustomsIntransitHbl;
    }

    public String getIsSwitchHbl() {
        return isSwitchHbl;
    }

    public void setIsSwitchHbl(String isSwitchHbl) {
        this.isSwitchHbl = isSwitchHbl;
    }

    public String getIsDividedHbl() {
        return isDividedHbl;
    }

    public void setIsDividedHbl(String isDividedHbl) {
        this.isDividedHbl = isDividedHbl;
    }

    public Long getHblIssueTypeId() {
        return hblIssueTypeId;
    }

    public void setHblIssueTypeId(Long hblIssueTypeId) {
        this.hblIssueTypeId = hblIssueTypeId;
    }

    public Long getHblGetWayId() {
        return hblGetWayId;
    }

    public void setHblGetWayId(Long hblGetWayId) {
        this.hblGetWayId = hblGetWayId;
    }

    public Long getHblReleaseWayId() {
        return hblReleaseWayId;
    }

    public void setHblReleaseWayId(Long hblReleaseWayId) {
        this.hblReleaseWayId = hblReleaseWayId;
    }

    public String getQuotationSummary() {
        return quotationSummary;
    }

    public void setQuotationSummary(String quotationSummary) {
        this.quotationSummary = quotationSummary;
    }

    public String getNewBookingRemark() {
        return newBookingRemark;
    }

    public void setNewBookingRemark(String newBookingRemark) {
        this.newBookingRemark = newBookingRemark;
    }

    public String getInquiryNotice() {
        return inquiryNotice;
    }

    public void setInquiryNotice(String inquiryNotice) {
        this.inquiryNotice = inquiryNotice;
    }

    public String getInquiryInnerRemark() {
        return inquiryInnerRemark;
    }

    public void setInquiryInnerRemark(String inquiryInnerRemark) {
        this.inquiryInnerRemark = inquiryInnerRemark;
    }

    public String getOpLeaderRemark() {
        return opLeaderRemark;
    }

    public void setOpLeaderRemark(String opLeaderRemark) {
        this.opLeaderRemark = opLeaderRemark;
    }

    public String getOpInnerRemark() {
        return opInnerRemark;
    }

    public void setOpInnerRemark(String opInnerRemark) {
        this.opInnerRemark = opInnerRemark;
    }

    public Long getAgreementTypeId() {
        return agreementTypeId;
    }

    public void setAgreementTypeId(Long agreementTypeId) {
        this.agreementTypeId = agreementTypeId;
    }

    public String getAgreementNo() {
        return agreementNo;
    }

    public void setAgreementNo(String agreementNo) {
        this.agreementNo = agreementNo;
    }
}
