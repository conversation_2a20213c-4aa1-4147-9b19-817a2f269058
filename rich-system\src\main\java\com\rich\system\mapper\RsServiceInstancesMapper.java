package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsServiceInstances;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 服务实例，记录着每一个订舱中的各种服务Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@Mapper
public interface RsServiceInstancesMapper {
    /**
     * 查询服务实例，记录着每一个订舱中的各种服务
     *
     * @param serviceId 服务实例，记录着每一个订舱中的各种服务主键
     * @return 服务实例，记录着每一个订舱中的各种服务
     */
    RsServiceInstances selectRsServiceInstancesByServiceId(Long serviceId);

    /**
     * 查询服务实例，记录着每一个订舱中的各种服务列表
     *
     * @param rsServiceInstances 服务实例，记录着每一个订舱中的各种服务
     * @return 服务实例，记录着每一个订舱中的各种服务集合
     */
    List<RsServiceInstances> selectRsServiceInstancesList(RsServiceInstances rsServiceInstances);

    /**
     * 新增服务实例，记录着每一个订舱中的各种服务
     *
     * @param rsServiceInstances 服务实例，记录着每一个订舱中的各种服务
     * @return 结果
     */
    int insertRsServiceInstances(RsServiceInstances rsServiceInstances);

    /**
     * 修改服务实例，记录着每一个订舱中的各种服务
     *
     * @param rsServiceInstances 服务实例，记录着每一个订舱中的各种服务
     * @return 结果
     */
    int updateRsServiceInstances(RsServiceInstances rsServiceInstances);

    /**
     * 删除服务实例，记录着每一个订舱中的各种服务
     *
     * @param serviceId 服务实例，记录着每一个订舱中的各种服务主键
     * @return 结果
     */
    int deleteRsServiceInstancesByServiceId(Long serviceId);

    /**
     * 批量删除服务实例，记录着每一个订舱中的各种服务
     *
     * @param serviceIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsServiceInstancesByServiceIds(Long[] serviceIds);

    int deleteRsServiceInstancesByRctId(@Param("rctId") Long rctId, @Param("serviceBelongTo") String serviceBelongTo);

    RsServiceInstances selectRsServiceInstances(@Param("rctId") Long rctId, @Param("serviceBelongTo") String logistics);

    List<RsServiceInstances> selectRsServiceInstancesByRctId(Long rctId);

    void upsertRsServiceInstances(RsServiceInstances rsServiceInstance);
}
