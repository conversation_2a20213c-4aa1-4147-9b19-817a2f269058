package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasProcess;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasProcessService;
import com.rich.system.service.impl.RedisCacheImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 进程名称Controller
 *
 * <AUTHOR>
 * @date 2023-06-05
 */
@RestController
@RequestMapping("/system/process")
public class BasProcessController extends BaseController {
    @Autowired
    private BasProcessService basProcessService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisCacheImpl RedisCache;

    /**
     * 查询进程名称列表
     */
    @PreAuthorize("@ss.hasPermi('system:process:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasProcess basProcess) {
        startPage();
        List<BasProcess> list = basProcessService.selectBasProcessList(basProcess);
        return getDataTable(list);
    }

    @GetMapping("/selectList")
    public AjaxResult selectList() {
        List<BasProcess> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "process");
        if (list == null) {
            RedisCache.process();
            list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "process");
        }
        return AjaxResult.success(list);
    }

    /**
     * 导出进程名称列表
     */
    @PreAuthorize("@ss.hasPermi('system:process:export')")
    @Log(title = "进程名称", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasProcess basProcess) {
        List<BasProcess> list = basProcessService.selectBasProcessList(basProcess);
        ExcelUtil<BasProcess> util = new ExcelUtil<BasProcess>(BasProcess.class);
        util.exportExcel(response, list, "进程名称数据");
    }

    /**
     * 获取进程名称详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:process:query')")
    @GetMapping(value = "/{processId}")
    public AjaxResult getInfo(@PathVariable("processId") Long processId) {
        return AjaxResult.success(basProcessService.selectBasProcessByProcessId(processId));
    }

    /**
     * 新增进程名称
     */
    @PreAuthorize("@ss.hasPermi('system:process:add')")
    @Log(title = "进程名称", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasProcess basProcess) {
        return toAjax(basProcessService.insertBasProcess(basProcess));
    }

    /**
     * 修改进程名称
     */
    @PreAuthorize("@ss.hasPermi('system:process:edit')")
    @Log(title = "进程名称", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasProcess basProcess) {
        return toAjax(basProcessService.updateBasProcess(basProcess));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:process:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasProcess basProcess) {
        basProcess.setUpdateBy(getUserId());
        return toAjax(basProcessService.changeStatus(basProcess));
    }

    /**
     * 删除进程名称
     */
    @PreAuthorize("@ss.hasPermi('system:process:remove')")
    @Log(title = "进程名称", businessType = BusinessType.DELETE)
    @DeleteMapping("/{processIds}")
    public AjaxResult remove(@PathVariable Long[] processIds) {
        return toAjax(basProcessService.deleteBasProcessByProcessIds(processIds));
    }

    @GetMapping("/getByServiceType")
    public List<BasProcess> getByServiceType(Long serviceTypeId) {
        return basProcessService.selectBasProcessByServiceTypeId(serviceTypeId);
    }
}
