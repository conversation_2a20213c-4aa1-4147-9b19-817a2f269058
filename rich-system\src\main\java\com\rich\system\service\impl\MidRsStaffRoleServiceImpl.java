package com.rich.system.service.impl;

import com.rich.common.core.domain.TreeSelect;
import com.rich.common.core.domain.entity.MidRsStaffRole;
import com.rich.common.core.domain.entity.RsStaff;
import com.rich.common.core.domain.entity.SysMenu;
import com.rich.common.utils.SecurityUtils;
import com.rich.system.mapper.BasDistDeptMapper;
import com.rich.system.mapper.MidRsStaffRoleMapper;
import com.rich.system.mapper.SysMenuMapper;
import com.rich.system.mapper.SysRoleMapper;
import com.rich.system.service.MidRsStaffRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 权限分配Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-02
 */
@Service

public class MidRsStaffRoleServiceImpl implements MidRsStaffRoleService {
    @Autowired
    private MidRsStaffRoleMapper midRsStaffRoleMapper;
    @Autowired
    private SysMenuMapper menuMapper;
    @Autowired
    private SysMenuServiceImpl service;

    @Autowired
    private SysRoleMapper sysRoleMapper;

    @Autowired
    private BasDistDeptMapper basDistDeptMapper;

    /**
     * 查询权限分配
     *
     * @param staffRoleDeptId 权限分配主键
     * @return 权限分配
     */
    @Override
    public MidRsStaffRole selectMidRsStaffRoleByStaffRoleDeptId(Long staffRoleDeptId) {
        return midRsStaffRoleMapper.selectMidRsStaffRoleByStaffRoleDeptId(staffRoleDeptId);
    }

    /**
     * 查询权限分配列表
     *
     * @param midRsStaffRole 权限分配
     * @return 权限分配
     */
    @Override
    public List<MidRsStaffRole> selectMidRsStaffRoleList(MidRsStaffRole midRsStaffRole) {
        // 不是总经办部门且非管理员,根据id查询管理部门下的员工角色
        if (!(SecurityUtils.getDeptId().equals(102L) || SecurityUtils.getUserId().equals(1L))) {
            midRsStaffRole.setUserId(SecurityUtils.getUserId());
        }
        return midRsStaffRoleMapper.queryMidRsStaffRoleList(midRsStaffRole);
    }

    @Override
    public List<TreeSelect> selectMidRsStaffRoleMenu(MidRsStaffRole midRsStaffRole) {
        RsStaff rsStaff = new RsStaff();
        rsStaff.setStaffId(midRsStaffRole.getStaffId());
        List<SysMenu> menus = service.selectMenuList(rsStaff);
        return service.buildMenuTreeSelect(menus);
    }

    /**
     * 新增权限分配
     *
     * @param midRsStaffRole 权限分配
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public int insertMidRsStaffRole(MidRsStaffRole midRsStaffRole) {
        return midRsStaffRoleMapper.insertMidRsStaffRole(midRsStaffRole);
    }

    /**
     * 修改权限分配
     *
     * @param midRsStaffRole 权限分配
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public int updateMidRsStaffRole(MidRsStaffRole midRsStaffRole) {
        return midRsStaffRoleMapper.updateMidRsStaffRole(midRsStaffRole);
    }


    /**
     * 批量删除权限分配
     *
     * @param staffRoleDeptIds 需要删除的权限分配主键
     * @return 结果
     */
    @Override
    public int deleteMidRsStaffRoleByStaffRoleDeptIds(Long[] staffRoleDeptIds) {
        return midRsStaffRoleMapper.deleteUserRole(staffRoleDeptIds);
    }

    /**
     * 删除权限分配信息
     *
     * @param staffRoleDeptId 权限分配主键
     * @return 结果
     */
    @Override
    public int deleteMidRsStaffRoleByStaffRoleDeptId(Long staffRoleDeptId) {
        return midRsStaffRoleMapper.deleteUserRoleByUserId(staffRoleDeptId);
    }

}
