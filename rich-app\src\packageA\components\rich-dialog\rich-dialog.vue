<template>
  <view v-if="value" :style="{
		'background-color': shadeBackground,
		'z-index': zIndex,
	}" class="rich-dialog" <!-- #ifndef MP-WEIXIN -->
  @click.self="close"
  <!-- #endif -->
  >
  <view :style="{
			width,
			height: tcMaxHeightTag ? box_h : 'auto',
			position: tcMaxHeightTag ? 'absolute' : 'relative',
			top: tcMaxHeightTag ? '50%' : '0',
			transform: tcMaxHeightTag ? 'translate(-50%, -50%)' : 'translate(-50%, 0%)',
			'background': background,
			'border-radius': borderRadius + 'rpx',
		}" class="rich-dialog-box">
    <view v-if="headerShow" :style="{
				height: titleHeight + 'rpx',
				'line-height': titleHeight + 'rpx',
			}" class="rich-dialog-title">
      {{ title }}
      <image v-if="closeButShow" :src="closeImg" :style="{
					top: ((titleHeight - 90) / 2) + 'rpx',
				}" class="rich-dialog-close" @click.stop="close"></image>
    </view>
    <view ref="content" :style="{
				height: headerShow ? 'calc(100% - ' + titleHeight + 'rpx)' : '100%',
			}" class="rich-dialog-content">
      <slot></slot>
    </view>
  </view>
  </view>
</template>

<script>
import index from './index.js';

export default index;
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>
