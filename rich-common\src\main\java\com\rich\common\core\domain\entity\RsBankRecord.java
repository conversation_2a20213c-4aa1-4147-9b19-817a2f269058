package com.rich.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import com.rich.common.utils.ChargeTypeExcelHandler;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 记录公司账户出入账明细对象 rs_bank_record
 *
 * <AUTHOR>
 * @date 2024-04-29
 */
public class RsBankRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 银行流水ID
     */
    private Long bankRecordId;

    /**
     * 收支标志 ,
     */
    @Excel(name = "收支标志")
    private String isRecievingOrPaying;

    /**
     * 所属公司 ,属于瑞旗的哪个子公司
     */
    @Excel(name = "所属公司")
    private String sqdPaymentTitleCode;

    /**
     * 银行账户 ,瑞旗公司的账户
     */
    @Excel(name = "银行账户")
    private String bankAccountCode;

    /**
     * 结算公司ID ,客户/供应商ID
     */
    @Excel(name = "结算公司ID")
    private Long clearingCompanyId;

    /**
     * 结算公司简称 ,
     */
    @Excel(name = "结算公司简称")
    private String sqdClearingCompanyShortname;

    /**
     * 费用类型 ,(基础表：费用类型)
     */
    @Excel(name = "费用类型", handler = ChargeTypeExcelHandler.class)
    private Long chargeTypeId;

    /**
     * 费用描述 ,
     */
    @Excel(name = "费用描述")
    private String chargeDescription;

    /**
     * 银行币种 ,
     */
    @Excel(name = "银行币种")
    private String bankCurrencyCode;

    /**
     * 实收金额 ,实际入账金额
     */
    @Excel(name = "实收金额", scale = 2)
    private BigDecimal actualBankRecievedAmount;

    /**
     * 实付金额 ,实际出账金额（含我司已支付手续费+汇率损益）
     */
    @Excel(name = "实付金额", scale = 2)
    private BigDecimal actualBankPaidAmount;

    /**
     * 收款手续费 ,收款银行手续费
     */
    @Excel(name = "收款手续费", scale = 2)
    private BigDecimal bankRecievedHandlingFee;

    /**
     * 付款手续费 ,付款银行手续费
     */
    @Excel(name = "付款手续费", scale = 2)
    private BigDecimal bankPaidHandlingFee;

    /**
     * 收款汇损 ,收款时因币种折算产生的汇率损益，录入银行费用时不可用。
     * 销账时手动调整。
     */
    @Excel(name = "收款汇损", scale = 2)
    private BigDecimal bankRecievedExchangeLost;

    /**
     * 付款汇损 ,同上
     */
    @Excel(name = "付款汇损", scale = 2)
    private BigDecimal bankPaidExchangeLost;

    /**
     * 收款记账 ,实收金额 + 收款手续费 + 收款汇损 = 收款记账
     */
    @Excel(name = "收款记账", scale = 2)
    private BigDecimal sqdBillRecievedAmount;

    /**
     * 付款记账 ,实付金额 - 付款手续费 - 付款汇损 = 付款记账
     */
    @Excel(name = "付款记账", scale = 2)
    private BigDecimal sqdBillPaidAmount;

    /**
     * 收账已销 ,
     */
    @Excel(name = "收账已销", scale = 2)
    private BigDecimal billRecievedWriteoffAmount;

    /**
     * 付账已销 ,
     */
    @Excel(name = "付账已销", scale = 2)
    private BigDecimal billPaidWriteoffAmount;

    /**
     * 收账未销 ,收款记账 - 收账已销 = 收账未销
     */
    @Excel(name = "收账未销", scale = 2)
    private BigDecimal sqdBillRecievedWriteoffBalance;

    /**
     * 付账未销 ,付款记账 - 付账已销 = 付账未销
     */
    @Excel(name = "付账未销", scale = 2)
    private BigDecimal sqdBillPaidWriteoffBalance;

    /**
     * 销账状态 ,√All,  =Part, -Null
     */
    @Excel(name = "销账状态")
    private String writeoffStatus;

    /**
     * 银行时间 ,银行流水发生时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "银行时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date bankRecordTime;

    /**
     * 结算方式 ,
     */
    @Excel(name = "结算方式")
    private String paymentTypeCode;

    /**
     * 凭证号 ,
     */
    @Excel(name = "凭证号")
    private String voucherNo;

    /**
     * 银行流水备注 ,
     */
    @Excel(name = "银行流水备注")
    private String bankRecordRemark;

    /**
     * 银行流水录入人 ,
     */
    @Excel(name = "银行流水录入人")
    private Long bankRecordByStaffId;

    /**
     * 银行流水录入时间 ,
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "银行流水录入时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date bankRecordUpdateTime;

    /**
     * 录入锁定状态 ,录入锁定后，银行录入部分不可更改
     */
    @Excel(name = "录入锁定状态")
    private String isBankRecordLocked;

    /**
     * 销账锁定状态 ,销账锁定后，此条记录（包括中间表、费用明细表）都不可以更改（发票信息除外）。
     */
    @Excel(name = "销账锁定状态", readConverterExp = "包=括中间表、费用明细表")
    private String isWriteoffLocked;

    /**
     * 费用条目ID_list ,
     */
    @Excel(name = "费用条目ID_list")
    private String sqdChargeIdList;

    /**
     * 相关操作单号_list ,(RCT_list)
     */
    @Excel(name = "相关操作单号_list")
    private String sqdRaletiveRctList;

    /**
     * 相关发票号码_list ,
     */
    @Excel(name = "相关发票号码_list")
    private String sqdRaletiveInvoiceList;

    /**
     * 所属员工ID ,
     */
    @Excel(name = "所属员工ID")
    private Long sqdRsStaffId;

    /**
     * 销账备注 ,
     */
    @Excel(name = "销账备注")
    private String writeoffRemark;

    /**
     * 销账人 ,
     */
    @Excel(name = "销账人")
    private Long writeoffStaffId;

    /**
     * 销账时间 ,
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "销账时间 ,", width = 30, dateFormat = "yyyy-MM-dd")
    private Date writeoffTime;
    private String bankRecordNo;
    private String invoiceNo;
    private String chargeType;
    private BigDecimal slipAmount;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date slipDate;
    private String slipFile;
    private String slipConfirmed;
    private Long verifyId;
    private Date verifyTime;
    private String chargeName;
    private Long[] chargeTypeIds;
    private String rctNo;

    private Long[] companyIds;
    private Long sqdReimburseId;
    private BigDecimal totalRecieved;
    private BigDecimal totalPaid;
    private String companyName;
    private String bankAccSummary;
    private String sqdInvoiceIdList;
    private String invoiceStatus;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date[] bankRecordTimeDate;

    public Date[] getBankRecordTimeDate() {
        return bankRecordTimeDate;
    }

    public void setBankRecordTimeDate(Date[] bankRecordTimeDate) {
        this.bankRecordTimeDate = bankRecordTimeDate;
    }

    public String getInvoiceStatus() {
        return invoiceStatus;
    }

    public void setInvoiceStatus(String invoiceStatus) {
        this.invoiceStatus = invoiceStatus;
    }

    public String getSqdInvoiceIdList() {
        return sqdInvoiceIdList;
    }

    public void setSqdInvoiceIdList(String sqdInvoiceIdList) {
        this.sqdInvoiceIdList = sqdInvoiceIdList;
    }

    public String getBankAccSummary() {
        return bankAccSummary;
    }

    public void setBankAccSummary(String bankAccSummary) {
        this.bankAccSummary = bankAccSummary;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public BigDecimal getTotalPaid() {
        return totalPaid;
    }

    public void setTotalPaid(BigDecimal totalPaid) {
        this.totalPaid = totalPaid;
    }

    public BigDecimal getTotalRecieved() {
        return totalRecieved;
    }

    public void setTotalRecieved(BigDecimal totalRecieved) {
        this.totalRecieved = totalRecieved;
    }

    public Long getSqdReimburseId() {
        return sqdReimburseId;
    }

    public void setSqdReimburseId(Long sqdReimburseId) {
        this.sqdReimburseId = sqdReimburseId;
    }

    public Long[] getCompanyIds() {
        return companyIds;
    }

    public void setCompanyIds(Long[] companyIds) {
        this.companyIds = companyIds;
    }

    public String getRctNo() {
        return rctNo;
    }

    public void setRctNo(String rctNo) {
        this.rctNo = rctNo;
    }

    public Long[] getChargeTypeIds() {
        return chargeTypeIds;
    }

    public void setChargeTypeIds(Long[] chargeTypeIds) {
        this.chargeTypeIds = chargeTypeIds;
    }

    public String getChargeName() {
        return chargeName;
    }

    public void setChargeName(String chargeName) {
        this.chargeName = chargeName;
    }

    public Date getVerifyTime() {
        return verifyTime;
    }

    public void setVerifyTime(Date verifyTime) {
        this.verifyTime = verifyTime;
    }

    public Long getVerifyId() {
        return verifyId;
    }

    public void setVerifyId(Long verifyId) {
        this.verifyId = verifyId;
    }

    public String getSlipConfirmed() {
        return slipConfirmed;
    }

    public void setSlipConfirmed(String slipConfirmed) {
        this.slipConfirmed = slipConfirmed;
    }

    public String getSlipFile() {
        return slipFile;
    }

    public void setSlipFile(String slipFile) {
        this.slipFile = slipFile;
    }

    public Date getSlipDate() {
        return slipDate;
    }

    public void setSlipDate(Date slipDate) {
        this.slipDate = slipDate;
    }

    public BigDecimal getSlipAmount() {
        return slipAmount;
    }

    public void setSlipAmount(BigDecimal slipAmount) {
        this.slipAmount = slipAmount;
    }

    public String getChargeType() {
        return chargeType;
    }

    public void setChargeType(String chargeType) {
        this.chargeType = chargeType;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public String getBankRecordNo() {
        return bankRecordNo;
    }

    public void setBankRecordNo(String bankRecordNo) {
        this.bankRecordNo = bankRecordNo;
    }

    public void setBankRecordId(Long bankRecordId) {
        this.bankRecordId = bankRecordId;
    }

    public Long getBankRecordId() {
        return bankRecordId;
    }

    public void setIsRecievingOrPaying(String isRecievingOrPaying) {
        this.isRecievingOrPaying = isRecievingOrPaying;
    }

    public String getIsRecievingOrPaying() {
        return isRecievingOrPaying;
    }

    public void setSqdPaymentTitleCode(String sqdPaymentTitleCode) {
        this.sqdPaymentTitleCode = sqdPaymentTitleCode;
    }

    public String getSqdPaymentTitleCode() {
        return sqdPaymentTitleCode;
    }

    public void setBankAccountCode(String bankAccountCode) {
        this.bankAccountCode = bankAccountCode;
    }

    public String getBankAccountCode() {
        return bankAccountCode;
    }

    public void setClearingCompanyId(Long clearingCompanyId) {
        this.clearingCompanyId = clearingCompanyId;
    }

    public Long getClearingCompanyId() {
        return clearingCompanyId;
    }

    public void setSqdClearingCompanyShortname(String sqdClearingCompanyShortname) {
        this.sqdClearingCompanyShortname = sqdClearingCompanyShortname;
    }

    public String getSqdClearingCompanyShortname() {
        return sqdClearingCompanyShortname;
    }

    public void setChargeTypeId(Long chargeTypeId) {
        this.chargeTypeId = chargeTypeId;
    }

    public Long getChargeTypeId() {
        return chargeTypeId;
    }

    public void setChargeDescription(String chargeDescription) {
        this.chargeDescription = chargeDescription;
    }

    public String getChargeDescription() {
        return chargeDescription;
    }

    public void setBankCurrencyCode(String bankCurrencyCode) {
        this.bankCurrencyCode = bankCurrencyCode;
    }

    public String getBankCurrencyCode() {
        return bankCurrencyCode;
    }

    public BigDecimal getActualBankRecievedAmount() {
        return actualBankRecievedAmount;
    }

    public void setActualBankRecievedAmount(BigDecimal actualBankRecievedAmount) {
        this.actualBankRecievedAmount = actualBankRecievedAmount;
    }

    public BigDecimal getActualBankPaidAmount() {
        return actualBankPaidAmount;
    }

    public void setActualBankPaidAmount(BigDecimal actualBankPaidAmount) {
        this.actualBankPaidAmount = actualBankPaidAmount;
    }

    public BigDecimal getBankRecievedHandlingFee() {
        return bankRecievedHandlingFee;
    }

    public void setBankRecievedHandlingFee(BigDecimal bankRecievedHandlingFee) {
        this.bankRecievedHandlingFee = bankRecievedHandlingFee;
    }

    public BigDecimal getBankPaidHandlingFee() {
        return bankPaidHandlingFee;
    }

    public void setBankPaidHandlingFee(BigDecimal bankPaidHandlingFee) {
        this.bankPaidHandlingFee = bankPaidHandlingFee;
    }

    public BigDecimal getBankRecievedExchangeLost() {
        return bankRecievedExchangeLost;
    }

    public void setBankRecievedExchangeLost(BigDecimal bankRecievedExchangeLost) {
        this.bankRecievedExchangeLost = bankRecievedExchangeLost;
    }

    public BigDecimal getBankPaidExchangeLost() {
        return bankPaidExchangeLost;
    }

    public void setBankPaidExchangeLost(BigDecimal bankPaidExchangeLost) {
        this.bankPaidExchangeLost = bankPaidExchangeLost;
    }

    public BigDecimal getSqdBillRecievedAmount() {
        return sqdBillRecievedAmount;
    }

    public void setSqdBillRecievedAmount(BigDecimal sqdBillRecievedAmount) {
        this.sqdBillRecievedAmount = sqdBillRecievedAmount;
    }

    public BigDecimal getSqdBillPaidAmount() {
        return sqdBillPaidAmount;
    }

    public void setSqdBillPaidAmount(BigDecimal sqdBillPaidAmount) {
        this.sqdBillPaidAmount = sqdBillPaidAmount;
    }

    public BigDecimal getBillRecievedWriteoffAmount() {
        return billRecievedWriteoffAmount;
    }

    public void setBillRecievedWriteoffAmount(BigDecimal billRecievedWriteoffAmount) {
        this.billRecievedWriteoffAmount = billRecievedWriteoffAmount;
    }

    public BigDecimal getBillPaidWriteoffAmount() {
        return billPaidWriteoffAmount;
    }

    public void setBillPaidWriteoffAmount(BigDecimal billPaidWriteoffAmount) {
        this.billPaidWriteoffAmount = billPaidWriteoffAmount;
    }

    public BigDecimal getSqdBillRecievedWriteoffBalance() {
        return sqdBillRecievedWriteoffBalance;
    }

    public void setSqdBillRecievedWriteoffBalance(BigDecimal sqdBillRecievedWriteoffBalance) {
        this.sqdBillRecievedWriteoffBalance = sqdBillRecievedWriteoffBalance;
    }

    public BigDecimal getSqdBillPaidWriteoffBalance() {
        return sqdBillPaidWriteoffBalance;
    }

    public void setSqdBillPaidWriteoffBalance(BigDecimal sqdBillPaidWriteoffBalance) {
        this.sqdBillPaidWriteoffBalance = sqdBillPaidWriteoffBalance;
    }

    public String getWriteoffStatus() {
        return writeoffStatus;
    }

    public void setWriteoffStatus(String writeoffStatus) {
        this.writeoffStatus = writeoffStatus;
    }

    public void setBankRecordTime(Date bankRecordTime) {
        this.bankRecordTime = bankRecordTime;
    }

    public Date getBankRecordTime() {
        return bankRecordTime;
    }

    public void setPaymentTypeCode(String paymentTypeCode) {
        this.paymentTypeCode = paymentTypeCode;
    }

    public String getPaymentTypeCode() {
        return paymentTypeCode;
    }

    public void setVoucherNo(String voucherNo) {
        this.voucherNo = voucherNo;
    }

    public String getVoucherNo() {
        return voucherNo;
    }

    public String getBankRecordRemark() {
        return bankRecordRemark;
    }

    public void setBankRecordRemark(String bankRecordRemark) {
        this.bankRecordRemark = bankRecordRemark;
    }

    public Long getBankRecordByStaffId() {
        return bankRecordByStaffId;
    }

    public void setBankRecordByStaffId(Long bankRecordByStaffId) {
        this.bankRecordByStaffId = bankRecordByStaffId;
    }

    public Date getBankRecordUpdateTime() {
        return bankRecordUpdateTime;
    }

    public void setBankRecordUpdateTime(Date bankRecordUpdateTime) {
        this.bankRecordUpdateTime = bankRecordUpdateTime;
    }

    public String getIsBankRecordLocked() {
        return isBankRecordLocked;
    }

    public void setIsBankRecordLocked(String isBankRecordLocked) {
        this.isBankRecordLocked = isBankRecordLocked;
    }

    public String getIsWriteoffLocked() {
        return isWriteoffLocked;
    }

    public void setIsWriteoffLocked(String isWriteoffLocked) {
        this.isWriteoffLocked = isWriteoffLocked;
    }

    public void setSqdChargeIdList(String sqdChargeIdList) {
        this.sqdChargeIdList = sqdChargeIdList;
    }

    public String getSqdChargeIdList() {
        return sqdChargeIdList;
    }

    public void setSqdRaletiveRctList(String sqdRaletiveRctList) {
        this.sqdRaletiveRctList = sqdRaletiveRctList;
    }

    public String getSqdRaletiveRctList() {
        return sqdRaletiveRctList;
    }

    public String getSqdRaletiveInvoiceList() {
        return sqdRaletiveInvoiceList;
    }

    public void setSqdRaletiveInvoiceList(String sqdRaletiveInvoiceList) {
        this.sqdRaletiveInvoiceList = sqdRaletiveInvoiceList;
    }

    public Long getSqdRsStaffId() {
        return sqdRsStaffId;
    }

    public void setSqdRsStaffId(Long sqdRsStaffId) {
        this.sqdRsStaffId = sqdRsStaffId;
    }

    public String getWriteoffRemark() {
        return writeoffRemark;
    }

    public void setWriteoffRemark(String writeoffRemark) {
        this.writeoffRemark = writeoffRemark;
    }

    public Long getWriteoffStaffId() {
        return writeoffStaffId;
    }

    public void setWriteoffStaffId(Long writeoffStaffId) {
        this.writeoffStaffId = writeoffStaffId;
    }

    public Date getWriteoffTime() {
        return writeoffTime;
    }

    public void setWriteoffTime(Date writeoffTime) {
        this.writeoffTime = writeoffTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("bankRecordId", getBankRecordId())
                .append("isRecievingOrPaying", getIsRecievingOrPaying())
                .append("sqdPaymentTitleCode", getSqdPaymentTitleCode())
                .append("bankAccountCode", getBankAccountCode())
                .append("clearingCompanyId", getClearingCompanyId())
                .append("sqdClearingCompanyShortname", getSqdClearingCompanyShortname())
                .append("chargeTypeId", getChargeTypeId())
                .append("chargeDescription", getChargeDescription())
                .append("bankCurrencyCode", getBankCurrencyCode())
                .append("actualBankRecievedAmount", getActualBankRecievedAmount())
                .append("actualBankPaidAmount", getActualBankPaidAmount())
                .append("bankRecievedHandlingFee", getBankRecievedHandlingFee())
                .append("bankPaidHandlingFee", getBankPaidHandlingFee())
                .append("bankRecievedExchangeLost", getBankRecievedExchangeLost())
                .append("bankPaidExchangeLost", getBankPaidExchangeLost())
                .append("sqdBillRecievedAmount", getSqdBillRecievedAmount())
                .append("sqdBillPaidAmount", getSqdBillPaidAmount())
                .append("billRecievedWriteoffAmount", getBillRecievedWriteoffAmount())
                .append("billPaidWriteoffAmount", getBillPaidWriteoffAmount())
                .append("sqdBillRecievedWriteoffBalance", getSqdBillRecievedWriteoffBalance())
                .append("sqdBillPaidWriteoffBalance", getSqdBillPaidWriteoffBalance())
                .append("writeoffStatus", getWriteoffStatus())
                .append("bankRecordTime", getBankRecordTime())
                .append("paymentTypeCode", getPaymentTypeCode())
                .append("voucherNo", getVoucherNo())
                .append("bankRecordRemark", getBankRecordRemark())
                .append("bankRecordByStaffId", getBankRecordByStaffId())
                .append("bankRecordUpdateTime", getBankRecordUpdateTime())
                .append("isBankRecordLocked", getIsBankRecordLocked())
                .append("isWriteoffLocked", getIsWriteoffLocked())
                .append("sqdChargeIdList", getSqdChargeIdList())
                .append("sqdRaletiveRctList", getSqdRaletiveRctList())
                .append("sqdRaletiveInvoiceList", getSqdRaletiveInvoiceList())
                .append("sqdRsStaffId", getSqdRsStaffId())
                .append("writeoffRemark", getWriteoffRemark())
                .append("writeoffStaffId", getWriteoffStaffId())
                .append("writeoffTime", getWriteoffTime())
                .toString();
    }
}
