package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasDistUnit;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasDistUnitService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 费用单位Controller
 *
 * <AUTHOR>
 * @date 2022-08-30
 */
@RestController
@RequestMapping("/system/unit")
public class BasDistUnitController extends BaseController {

    @Autowired
    private BasDistUnitService basDistUnitService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询费用单位列表
     */
    @PreAuthorize("@ss.hasPermi('system:unit:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasDistUnit basDistUnit) {
        startPage();
        List<BasDistUnit> list = basDistUnitService.selectBasChargeUnitList(basDistUnit);
        return getDataTable(list);
    }

    @GetMapping("/selectList")
    public AjaxResult selectList(BasDistUnit basDistUnit) {
        List<BasDistUnit> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "unit");
        if (list == null) {
            basDistUnit.setStatus("0");
            list = basDistUnitService.selectBasChargeUnitList(basDistUnit);
            redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "unit");
            redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "unit", list);
        }
        return AjaxResult.success(list);
    }

    /**
     * 导出费用单位列表
     */
    @PreAuthorize("@ss.hasPermi('system:unit:export')")
    @Log(title = "费用单位", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasDistUnit basDistUnit) {
        List<BasDistUnit> list = basDistUnitService.selectBasChargeUnitList(basDistUnit);
        ExcelUtil<BasDistUnit> util = new ExcelUtil<BasDistUnit>(BasDistUnit.class);
        util.exportExcel(response, list, "费用单位数据");
    }

    /**
     * 获取费用单位详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:unit:edit')")
    @GetMapping(value = "/{unitId}")
    public AjaxResult getInfo(@PathVariable("unitId") Long unitId) {
        AjaxResult ajaxResult = AjaxResult.success();
        ajaxResult.put(AjaxResult.DATA_TAG,basDistUnitService.selectBasChargeUnitByUnitId(unitId));
        return ajaxResult;
    }

    /**
     * 新增费用单位
     */
    @PreAuthorize("@ss.hasPermi('system:unit:add')")
    @Log(title = "费用单位", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasDistUnit basDistUnit) {
        int out = basDistUnitService.insertBasChargeUnit(basDistUnit);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "unit");
        return toAjax(out);
    }

    /**
     * 修改费用单位
     */
    @PreAuthorize("@ss.hasPermi('system:unit:edit')")
    @Log(title = "费用单位", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasDistUnit basDistUnit) {
        int out = basDistUnitService.updateBasChargeUnit(basDistUnit);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "unit");
        return toAjax(out);
    }

    /**
     * 修改费用单位
     */
    @PreAuthorize("@ss.hasPermi('system:unit:edit')")
    @Log(title = "费用单位", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasDistUnit basDistUnit) {
        int out = basDistUnitService.changeStatus(basDistUnit);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "unit");
        return toAjax(out);
    }

    /**
     * 删除费用单位
     */
    @PreAuthorize("@ss.hasPermi('system:unit:remove')")
    @Log(title = "费用单位", businessType = BusinessType.DELETE)
    @DeleteMapping("/{unitIds}")
    public AjaxResult remove(@PathVariable Long[] unitIds) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "unit");
        return toAjax(basDistUnitService.deleteBasChargeUnitByUnitIds(unitIds));
    }
}
