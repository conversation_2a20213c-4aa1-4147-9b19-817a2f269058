<script>
import config from './config'
import {getToken} from '@/utils/auth'

export default {
  onLaunch: function () {
    this.initApp()
  },

  onHide: function () {
    // 小程序进入后台时，断开蓝牙连接
    // this.$store.dispatch('bluetooth/disconnectBluetooth')
  },
  
  methods: {
    // 初始化应用
    initApp() {
      // 初始化应用配置
      this.initConfig()
      // 检查用户登录状态
      this.checkLogin()
    },
    initConfig() {
      this.globalData.config = config
    },
    checkLogin() {
      if (!getToken()) {
        // 所有平台环境都跳转到登录页，不再自动登录
        this.$tab.reLaunch('/pages/login')
      }
    }
  }
}
</script>

<style lang="scss">
@import '@/static/scss/index.scss'
</style>
