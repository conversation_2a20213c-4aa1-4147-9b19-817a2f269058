package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsRctLogisticsNoInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 操作单基础物流编号信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-15
 */
@Mapper
public interface RsRctLogisticsNoInfoMapper {
    /**
     * 查询操作单基础物流编号信息
     *
     * @param logisticsNoInfoId 操作单基础物流编号信息主键
     * @return 操作单基础物流编号信息
     */
    RsRctLogisticsNoInfo selectRsRctLogisticsNoInfoByLogisticsNoInfoId(Long logisticsNoInfoId);

    /**
     * 查询操作单基础物流编号信息列表
     *
     * @param rsRctLogisticsNoInfo 操作单基础物流编号信息
     * @return 操作单基础物流编号信息集合
     */
    List<RsRctLogisticsNoInfo> selectRsRctLogisticsNoInfoList(RsRctLogisticsNoInfo rsRctLogisticsNoInfo);

    /**
     * 新增操作单基础物流编号信息
     *
     * @param rsRctLogisticsNoInfo 操作单基础物流编号信息
     * @return 结果
     */
    int insertRsRctLogisticsNoInfo(RsRctLogisticsNoInfo rsRctLogisticsNoInfo);

    /**
     * 修改操作单基础物流编号信息
     *
     * @param rsRctLogisticsNoInfo 操作单基础物流编号信息
     * @return 结果
     */
    int updateRsRctLogisticsNoInfo(RsRctLogisticsNoInfo rsRctLogisticsNoInfo);

    /**
     * 删除操作单基础物流编号信息
     *
     * @return 结果
     */
    int deleteRsRctLogisticsNoInfoByRctId(Long rctId);

    /**
     * 批量删除操作单基础物流编号信息
     *
     * @return 结果
     */
    int deleteRsRctLogisticsNoInfoByIds(Long[] rctIds);

}
