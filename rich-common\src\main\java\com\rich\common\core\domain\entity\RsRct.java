package com.rich.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import com.rich.common.utils.RsRctCustomExcelHandler;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 操作单对象 rs_rct
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
public class RsRct extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private Long rctId;
    private Long userId;

    /**
     * 操作单号 ,（RCT）
     */
    @Excel(name = "操作单号")
    private String rctNo;
    /**
     * 操作日期 ,
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "操作日期", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date rctCreateTime;
    /**
     * 紧急程度 ,
     */
    @Excel(name = "紧急程度", readConverterExp = "0=预定,1=当天,2=常规, 3=紧急, 4=立即")
    private String emergencyLevel;
    /**
     * 订单难度 ,
     */
    @Excel(name = "订单难度", readConverterExp = "0=简易,1=标准,4=高级, 8=特别")
    private String difficultyLevel;

    /**
     * 委托单位概要 ,包含客户编号/简称/全称
     */
    @Excel(name = "委托单位概要", handler = RsRctCustomExcelHandler.class, args = {"client"})
    private String clientSummary;
    @Excel(name = "所属公司")
    private String orderBelongsTo;
    /**
     * 放货方式 ,
     */
    @Excel(name = "放货方式", readConverterExp = "1=月结,2=押放,3=票结,4=签放,5=订金,6=预收,7=扣货,9=居间")
    private String releaseType;
    @Excel(name = "物流类型")
    private String logisticsTypeEnName;
    /**
     * 服务类型List ,服务类型List集合字符串（逗号分隔）
     */
    @Excel(name = "服务类型", handler = RsRctCustomExcelHandler.class, args = {"service"})
    private String serviceTypeIdList;
    /**
     * 进出口类型 ,
     */
    @Excel(name = "进出口类型", readConverterExp = "1=出口,2=进口")
    private String impExpType;
    @Excel(name = "启运港")
    private String pol;
    @Excel(name = "目的港")
    private String destinationPort;
    /**
     * 计费货量 ,
     */
    @Excel(name = "计费货量")
    private String revenueTon;
    /**
     * 货名概要 ,
     */
    @Excel(name = "货名概要")
    private String goodsNameSummary;
    @Excel(name = "提单类型")
    private String blTypeCode;
    @Excel(name = "出单方式")
    /**
     * 出单方式 ,作为冗余字段
     */
    private String sqdIssueType;
    /**
     * 交单方式 ,作为冗余字段
     */
    @Excel(name = "交单方式", readConverterExp = "1=境外快递,2=境内快递,3=跑腿,4=业务送达,5=客户自取,6=QQ,7=微信,8=电邮,9=公众号,10=承运人系统,11=订舱口系统,12=第三方系统")
    private String sqdDocDeliveryWay;
    @Excel(name = "承运人")
    private String carrierEnName;
    @Excel(name = "")
    private String agreementTypeCode;
    @Excel(name = "")
    private String supplierName;
    @Excel(name = "入仓号")
    /**
     * 入仓号 ,
     */
    private String warehousingNo;
    /**
     * 放舱单号汇总 ,作为冗余字段
     */
    @Excel(name = "SO号")
    private String soNo;
    @Excel(name = "提单号")
    /**
     * 提单号汇总 ,作为冗余字段
     */
    private String blNo;
    @Excel(name = "柜号")
    /**
     * 柜号和封条汇总 ,作为冗余字段
     */
    private String sqdContainersSealsSum;
    @Excel(name = "订单状态", readConverterExp = "1=通过,2=进行,4=异常,6=确认,7=完成,8=取消,9=驳回")
    private Long processStatusId;
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "ETD", dateFormat = "yyyy-MM-dd")
    private Date etd;
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "ETA", dateFormat = "yyyy-MM-dd")
    private Date eta;
    /**
     * 卸货港到达 ,（时间）
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "ATD", dateFormat = "yyyy-MM-dd")
    private String podEta;


    /**
     * 目的港到达 ,（时间）
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "ATA", dateFormat = "yyyy-MM-dd")
    private String destinationPortEta;

    /**
     * 业务员 ,
     */
    @Excel(name = "业务", handler = RsRctCustomExcelHandler.class, args = {"staff"})
    private Long salesId;
    /**
     * 审核商务 ,审核订单的商务
     */
    @Excel(name = "商务", handler = RsRctCustomExcelHandler.class, args = {"staff"})
    private Long verifyPsaId;
    /**
     * 商务审核时间 ,
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "商务审核时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date psaVerifyTime;
    @Excel(name = "审核状态", readConverterExp = "1=通过,2=进行,4=异常,6=确认,7=完成,8=取消,9=驳回")
    private Long psaVerifyStatusId;
    /**
     * 操作员 ,
     */
    @Excel(name = "操作", handler = RsRctCustomExcelHandler.class, args = {"staff"})
    private Long opId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最新操作时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date statusUpdateTime;


    /**
     * 订舱日期 ,
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date newBookingTime;

    private Date firstAtd;
    private Date destinationPortAta;


    /**
     * 委托单位 ,
     */
    private Long clientId;


    /**
     * 客户角色 ,
     */
    private Long clientRoleId;

    /**
     * 联系人称谓 ,直接拷贝，不关联客户表
     */
    private String clientContact;

    /**
     * 联系人电话 ,直接拷贝，不关联客户表
     */
    private String clientContactTel;

    /**
     * 联系人邮箱 ,直接拷贝，不关联客户表
     */
    private String clientContactEmail;

    /**
     * 关联单位List ,简称
     */
    private String relationClientIdList;


    /**
     * 收款抬头 ,瑞旗收款公司(账号的抬头)
     */
    @Excel(name = "收款抬头")
    private String paymentTitleCode;


    /**
     * 贸易条款 ,
     */
    @Excel(name = "贸易条款")
    private String tradingTerms;

    /**
     * 运输条款 ,
     */
    @Excel(name = "运输条款")
    private String logisticsTerms;

    /**
     * 收汇方式 ,客户与他的客户/供应商之间的收付方式
     */
    @Excel(name = "收汇方式")
    private String tradingPaymentChannel;

    /**
     * 合同号 ,(委托单位)合同号
     */
    private String clientContractNo;

    /**
     * 发票号 ,(委托单位)发票号
     */
    private String clientInvoiceNo;

    /**
     * 货物特征汇总 ,货物id集合，逗号分割
     */
    private String cargoTypeIdSum;


    /**
     * 件数 ,
     */
    private Long packageQuantity;

    /**
     * 体积 ,
     */
    private BigDecimal goodsVolume;

    /**
     * 毛重 ,
     */
    private BigDecimal grossWeight;

    /**
     * 重量单位 ,原：货物单位cargo_unit_id
     */
    private String weightUnitCode;

    /**
     * 货值币种 ,
     */
    private String goodsCurrencyCode;

    /**
     * 货值 ,原：cargo_price
     */
    private BigDecimal goodsValue;

    /**
     * 物流类型 ,在此，与主服务类型合用一个字段
     */
    private Long logisticsTypeId;


    /**
     * 启运港 ,原：departure_id，询价/订舱起点
     */
    private Long polId;

    /**
     * 境内基港 ,启运地境内大港，for bl&tracking
     */
    private Long localBasicPortId;

    /**
     * 中转港 ,起运地境外中转港，for tracking
     */
    private Long transitPortId;

    /**
     * 卸货港 ,境外卸货大港，for bl&tracking
     */
    private Long podId;

    /**
     * 目的港 ,询价/订舱终点，for bl&tracking
     */
    private Long destinationPortId;

    /**
     * 截关时间 ,（启运港截关时间）
     */
    private String cvClosingTime;

    /**
     * 截补料 ,（时间）
     */
    private String siClosingTime;

    /**
     * 头程船名 ,
     */
    private String firstVessel;

    /**
     * 头程航次 ,
     */
    private String firstVoyage;

    /**
     * 头程开舱 ,（时间）
     */
    private String firstCyOpenTime;

    /**
     * 头程截重 ,（时间）
     */
    private String firstCyClosingTime;

    /**
     * 头程装船 ,（时间）
     */
    private Date firstEtd;

    /**
     * 基港船名 ,
     */
    private String basicVessel;

    /**
     * 基港航次 ,
     */
    private String basicVoyage;

    /**
     * 基港截重 ,（时间）
     */
    private Date basicFinalGateinTime;

    /**
     * 基港装船 ,（时间）
     */
    private Date basicEtd;


    /**
     * 承运人 ,
     */
    private Long carrierId;

    /**
     * 航班时效综述 ,商务提供的船期综述
     */
    private String inquiryScheduleSummary;

    /**
     * 启运港放舱代理 ,订舱口（主服务的供应商）
     */
    private Long polBookingAgent;

    /**
     * 目的港换单代理 ,
     */
    private Long podHandleAgent;


    /**
     * 报关方式 ,作为冗余字段
     */
    private String sqdExportCustomsType;

    /**
     * 拖车方式 ,作为冗余字段
     */
    private String sqdTrailerType;

    /**
     * 订单总体进度 ,
     */
    private String rctProcessStatusSummary;

    /**
     * 物流总体进度 ,
     */
    private String transportStatusSummary;

    /**
     * 文件总体进度 ,
     */
    private String docStatusSummary;

    /**
     * 收款总体进度 ,
     */
    private String paymentReceivingStatusSummary;

    /**
     * 付款总体进度 ,
     */
    private String paymentPayingStatusSummary;

    /**
     * 物流进度 ,
     */
    private String transportStatus;
    private String transportStatusA;
    private String transportStatusB;

    /**
     * 文件进度 ,
     */
    private String docStatus;
    private String docStatusA;
    private String docStatusB;

    /**
     * 付款进度 ,
     */
    private String paymentPayingStatus;

    /**
     * 订单进度流水号 ,
     */
    private Long rctProcessId;

    /**
     * 进度名称 ,
     */
    private Long processId;

    /**
     * 进度状态 ,
     */
    private Long porcessStatusId;

    /**
     * 进度发生时间 ,
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date processStatusTime;

    /**
     * 进度备注 ,
     */
    private String processRemark;

    /**
     * 报价单号 ,（业务 QTT）
     */
    private String qoutationNo;

    /**
     * 业务报价综述 ,（给客户）
     */
    private String qoutationSketch;


    /**
     * 报价日期 ,
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date qoutationTime;

    /**
     * 订舱单号 ,（NBN）
     */
    private String newBookingNo;

    /**
     * 业务订舱备注 ,（给操作）
     */
    private String newBookingRemark;

    /**
     * 业务助理 ,
     */
    @Excel(name = "业务助理", handler = RsRctCustomExcelHandler.class, args = {"staff"})
    private Long salesAssistantId;

    /**
     * 协助业务员 ,
     */
    private Long salesObserverId;


    /**
     * 业务须知汇总 ,具体物流服务（商务给业务）
     */
    private String inquiryNoticeSum;

    /**
     * 商务备注汇总 ,具体物流服务（商务给操作）
     */
    private String inquiryInnerRemarkSum;


    /**
     * 操作主管批示 ,（给操作）
     */
    private String opLeaderNotice;

    /**
     * 操作备注 ,操作过程中的备注
     */
    private String opInnerRemark;


    /**
     * 订舱员 ,
     */
    private Long bookingOpId;

    /**
     * 单证员 ,
     */
    private Long docOpId;

    /**
     * 协助操作员 ,
     */
    private Long opObserverId;


    /**
     * 删除人 ,
     */
    private Long deleteBy;

    /**
     * 删除时间 ,
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date deleteTime;


    /**
     * 装运区域 ,
     */
    private Long precarriageRegionId;

    /**
     * 装运详址 ,
     */
    private String precarriageAddress;

    /**
     * 装运时间 ,
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date precarriageTime;

    /**
     * 装运联系人 ,
     */
    private String precarriageContact;

    /**
     * 装运电话 ,
     */
    private String precarriageTel;

    /**
     * 装运备注 ,
     */
    private String precarriageRemark;

    /**
     * 派送区域 ,
     */
    private Long dispatchRegionId;

    /**
     * 派送详址 ,
     */
    private String dispatchAddress;

    /**
     * 派送时间 ,
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dispatchTime;

    /**
     * 派送联系人 ,
     */
    private String dispatchContact;

    /**
     * 派送电话 ,
     */
    private String dispatchTel;

    /**
     * 派送备注 ,
     */
    private String dispatchRemark;

    /**
     * 收款进度 ,
     */
    private String paymentReceivingStatus;

    /**
     * 入仓 ,作为临时冗余字段
     */
    private String sqdWarehousingStatus;

    /**
     * 订舱 ,作为临时冗余字段
     */
    private String sqdShippingBookingStatus;

    /**
     * 约车 ,作为临时冗余字段
     */
    private String sqdTrailerBookingStatus;

    /**
     * 约柜 ,作为临时冗余字段
     */
    private String sqdContainerBookingStatus;

    /**
     * 装柜 ,作为临时冗余字段
     */
    private String sqdContainerLoadingStatus;

    /**
     * 配船 ,作为临时冗余字段
     */
    private String sqdVesselArrangeStatus;

    /**
     * VGM ,作为临时冗余字段
     */
    private String sqdVgmStatus;

    /**
     * 单证 ,作为临时冗余字段
     */
    private String sqdCustomDocsStatus;

    /**
     * 报关授权 ,作为临时冗余字段
     */
    private String sqdCustomAuthorizedStatus;

    /**
     * 查验 ,作为临时冗余字段
     */
    private String sqdCustomExamineStatus;

    /**
     * 放行 ,作为临时冗余字段
     */
    private String sqdCustomReleaseStatus;

    /**
     * 对单 ,作为临时冗余字段
     */
    private String sqdSiVerifyStatus;

    /**
     * 补料 ,作为临时冗余字段
     */
    private String sqdSiPostStatus;

    /**
     * AMS/ENS ,作为临时冗余字段
     */
    private String sqdAmsEnsPostStatus;

    /**
     * ISF/EMNF ,作为临时冗余字段
     */
    private String sqdIsfEmnfPostStatus;

    /**
     * 主服务付款状态 ,作为临时冗余字段
     */
    private String sqdMainServicePayingStatus;

    /**
     * 提单从订舱口赎回 ,作为临时冗余字段
     */
    private String blGettingStatus;

    /**
     * 提单交付给客户 ,作为临时冗余字段
     */
    private String blReleasingStatus;

    /**
     * 柜号汇总 ,作为冗余字段
     */
    private String sqdContainerNoSum;

    /**
     * 启运港订舱代理 ,订舱口（主服务的供应商）(##)
     */
    private Long sqdPolBookingAgent;

    /**
     * 目的港换单代理 ,(##)
     */
    private Long sqdPodHandleAgent;

    /**
     * 承运人 ,业务预设，主服务变动时自动更新，(##)下同
     */
    private Long sqdCarrierId;

    /**
     * 结款方式 ,（原：放货方式release_type）
     */
    private String logisticsPaymentTermsCode;

    /**
     * 客户单号 ,(委托单位)工作号
     */
    private String clientJobNo;

    /**
     * 发货人 ,客户填入的第一信息，仅供参考
     */
    private String bookingShipper;

    /**
     * 收货人 ,客户填入的第一信息，仅供参考
     */
    private String bookingConsignee;

    /**
     * 通知人 ,客户填入的第一信息，仅供参考
     */
    private String bookingNotifyParty;

    /**
     * 投保方式,作为冗余字段
     */
    private String sqdInsuranceType;

    /**
     * 操作确认 ,客户信息中的操作确认
     */
    private String isOpConfirmed;

    /**
     * 操作确认Id ,客户信息中的操作确认人id
     */
    private Long opConfirmedId;

    /**
     * 操作确认人 ,客户信息中的操作确认人名称
     */
    private String opConfirmedName;

    /**
     * 操作确认时间 ,客户信息中的操作确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date opConfirmedDate;

    /**
     * 业务确认 ,客户信息中的业务确认
     */
    private String isSalesConfirmed;

    /**
     * 业务确认Id ,客户信息中的业务确认人id
     */
    private Long salesConfirmedId;

    /**
     * 业务确认人 ,客户信息中的业务确认人名称
     */
    private String salesConfirmedName;

    /**
     * 业务确认时间 ,客户信息中的业务确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date salesConfirmedDate;

    /**
     * 客户确认 ,客户信息中的客户确认
     */
    private String isClientConfirmed;

    /**
     * 客户确认Id ,客户信息中的客户确认人id
     */
    private Long clientConfirmedId;
    private String blFormCode;

    /**
     * 客户确认人 ,客户信息中的客户确认人名称
     */
    private String clientConfirmedName;

    /**
     * 客户确认时间 ,客户信息中的客户确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date clientConfirmedDate;

    /**
     * 财务确认 ,客户信息中的财务确认
     */
    private String isAccountConfirmed;

    /**
     * 财务确认Id ,客户信息中的财务确认人id
     */
    private Long accountConfirmedId;

    /**
     * 财务确认人 ,客户信息中的财务确认人名称
     */
    private String accountConfirmedName;

    /**
     * 财务确认时间 ,客户信息中的财务确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date accountConfirmedDate;

    private String isDirectLine;

    private String isBatches;


    /**
     * 操作主管
     */
    private Long verifyOpLeaderId;

    /**
     * 操作主管批示时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date opLeaderVerifyTime;

    /**
     * 折合已收
     */
    private BigDecimal sqdReceivedRmbSum;

    /**
     * 折合已付
     */
    private BigDecimal sqdPaidRmbSum;

    /**
     * 操作单信息是否显示
     */
    private String messageDisplay;

    /**
     * 操作单服务信息是否折叠
     */
    private String serviceMessageFold;


    // 客户信息
    private RsClientMessage rsClientMessage;

    // 子服务
    private RsBasicLogistics rsBasicLogistics;

    private RsPrecarriage rsPrecarriage;

    private RsExportCustoms rsExportCustoms;

    private RsImportCustoms rsImportCustoms;

    private Long[] serviceTypeIds;

    private Long[] cargoTypeIds;

    private Long[] carrierIds;

    // search
    private Long[] destinationPortIds;

    private Long[] polIds;

    private Long[] lineIds;

    private List<Long> rctIds;

    private Long[] permissionLevel;

    private RsOpSeaFcl rsOpSeaFcl;
    private List<RsOpSeaFcl> rsOpSeaFclList;

    private RsOpSeaLcl rsOpSeaLcl;
    private List<RsOpSeaLcl> rsOpSeaLclList;

    private RsOpAir rsOpAir;
    private List<RsOpAir> rsOpAirList;

    private RsOpRail rsOpRail;

    private RsOpExpress rsOpExpress;

    private RsOpPortService rsOpPortService;

    private RsOpTruck rsOpTruck;
    private List<RsOpTruck> rsOpTruckList;

    private RsOpExportCustomsClearance rsOpExportCustomsClearance;

    private RsOpImportCustomsClearance rsOpImportCustomsClearance;

    private RsOpImportDispatchTruck rsOpImportDispatchTruck;

    private RsOpWarehouse rsOpWarehouse;

    private RsOpInspectionAndCertificate rsOpInspectionAndCertificate;

    private RsOpLand rsOpLand;

    private RsOpInsurance rsOpInsurance;

    private RsOpExpandService rsOpExpandService;

    private RsOpRailFCL rsOpRailFCL;

    private RsOpRailLCL rsOpRailLCL;

    private RsOpCtnrTruck rsOpCtnrTruck;
    private List<RsOpCtnrTruck> rsOpCtnrTruckList;
    private RsOpBulkTruck rsOpBulkTruck;
    private List<RsOpBulkTruck> rsOpBulkTruckList;
    private RsOpDocDeclare rsOpDocDeclare;
    private List<RsOpDocDeclare> rsOpDocDeclareList;
    private RsOpFreeDeclare rsOpFreeDeclare;
    private List<RsOpFreeDeclare> rsOpFreeDeclareList;
    private RsOpDOAgent rsOpDOAgent;
    private RsOpClearAgent rsOpClearAgent;
    private RsOpWHS rsOpWHS;
    private RsOpOther rsOpOther;
    private RsOp3rdCert rsOp3rdCert;
    private RsOpINS rsOpINS;
    private RsOpTrading rsOpTrading;
    private RsOpFumigation rsOpFumigation;
    private RsOpCO rsOpCO;

    private Long opLeaderVerifyStatusId;
    private String bookingAgent;
    private String precarriageSupplierNo;

    private String noTransferAllowed;
    private String noDividedAllowed;
    private String noAgreementShowed;
    private String opAccept;
    private String psaVerify;
    private String sqdCarrier;

    private Set<Long> supplierIds;

    private List<RsBookingMessage> bookingMessagesList;
    private String bookingAgentRemark;
    private String shippingMark;
    private String polName;
    private String podName;
    private String freightPaidWayCode;
    private String bookingChargeRemark;
    private String ctnrTypeCode;
    private String cargoTypeCodeSum;


    private String sqdDnReceiveSlipStatus;
    private String sqdDnPaySlipStatus;

    private String paymentNode;
    private Long rsPsaId;
    private String sqdPsaNo;
    private Long psaRctId;
    private String opAskingBlGetTime;
    private String opAskingBlReleaseTime;
    private String accPromissBlGetTime;
    private String actualBlGotTime;
    private String accPromissBlReleaseTime;
    private String actualBlReleaseTime;
    private String agentNoticeTime;
    private Date bookingStatus;
    private String secondVessel;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date[] rctOpDate;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date[] ATDDate;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date[] ETDDate;

    /**
     * 折合rmb报价
     */
    @Excel(name = "折合rmb报价", scale = 2)
    private BigDecimal quotationInRmb;

    /**
     * 折合rmb询价
     */
    @Excel(name = "折合rmb询价", scale = 2)
    private BigDecimal inquiryInRmb;

    /**
     * 折合rmb预期含税利润
     */
    @Excel(name = "折合rmb预期含税利润", scale = 2)
    private BigDecimal estimatedProfitInRmb;

    /**
     * usd含税应收
     */
    @Excel(name = "usd含税应收", scale = 2)
    private BigDecimal dnUsd;

    /**
     * rmb含税应收
     */
    @Excel(name = "rmb含税应收", scale = 2)
    private BigDecimal dnRmb;

    /**
     * usd含税未收
     */
    @Excel(name = "usd含税未收", scale = 2)
    private BigDecimal dnUsdBalance;

    /**
     * rmb含税未收
     */
    @Excel(name = "rmb含税未收", scale = 2)
    private BigDecimal dnRmbBalance;

    /**
     * 折合rmb含税应收
     */
    @Excel(name = "折合rmb含税应收", scale = 2)
    private BigDecimal dnInRmb;

    /**
     * 折合rmb未收
     */
    @Excel(name = "折合rmb未收", scale = 2)
    private BigDecimal dnInRmbBalance;

    /**
     * usd含税应付
     */
    @Excel(name = "usd含税应付", scale = 2)
    private BigDecimal cnUsd;

    /**
     * rmb含税应付
     */
    @Excel(name = "rmb含税应付", scale = 2)
    private BigDecimal cnRmb;

    /**
     * usd含税未付
     */
    @Excel(name = "usd含税未付", scale = 2)
    private BigDecimal cnUsdBalance;

    /**
     * rmb含税未付
     */
    @Excel(name = "rmb含税未付", scale = 2)
    private BigDecimal cnRmbBalance;

    /**
     * 折合rmb含税应付
     */
    @Excel(name = "折合rmb含税应付", scale = 2)
    private BigDecimal cnInRmb;

    /**
     * 折合rmb未付
     */
    @Excel(name = "折合rmb未付", scale = 2)
    private BigDecimal cnInRmbBalance;

    /**
     * 美元部分含税利润
     */
    @Excel(name = "美元部分含税利润", scale = 2)
    private BigDecimal profitUsd;

    /**
     * 人民币部分含税利润
     */
    @Excel(name = "人民币部分含税利润", scale = 2)
    private BigDecimal profitRmb;

    /**
     * 折合人民币利润
     */
    @Excel(name = "折合人民币利润", scale = 2)
    private BigDecimal profitInRmb;

    /**
     * （报价和实际账单的）预期差折合人民币
     */
    @Excel(name = "", readConverterExp = "利润差额", scale = 2)
    private BigDecimal differenceInRmb;

    private String isCustomsIntransitShowed;

    @Excel(name = "毛利率")
    private BigDecimal profitRate;
    private String statisticsSalesId;

    public String getStatisticsSalesId() {
        return statisticsSalesId;
    }

    public void setStatisticsSalesId(String statisticsSalesId) {
        this.statisticsSalesId = statisticsSalesId;
    }

    public BigDecimal getProfitRate() {
        return profitRate;
    }

    public void setProfitRate(BigDecimal profitRate) {
        this.profitRate = profitRate;
    }

    public Long getRctId() {
        return rctId;
    }

    public void setRctId(Long rctId) {
        this.rctId = rctId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getRctNo() {
        return rctNo;
    }

    public void setRctNo(String rctNo) {
        this.rctNo = rctNo;
    }

    public Date getRctCreateTime() {
        return rctCreateTime;
    }

    public void setRctCreateTime(Date rctCreateTime) {
        this.rctCreateTime = rctCreateTime;
    }

    public String getEmergencyLevel() {
        return emergencyLevel;
    }

    public void setEmergencyLevel(String emergencyLevel) {
        this.emergencyLevel = emergencyLevel;
    }

    public String getDifficultyLevel() {
        return difficultyLevel;
    }

    public void setDifficultyLevel(String difficultyLevel) {
        this.difficultyLevel = difficultyLevel;
    }

    public String getClientSummary() {
        return clientSummary;
    }

    public void setClientSummary(String clientSummary) {
        this.clientSummary = clientSummary;
    }

    public String getOrderBelongsTo() {
        return orderBelongsTo;
    }

    public void setOrderBelongsTo(String orderBelongsTo) {
        this.orderBelongsTo = orderBelongsTo;
    }

    public String getReleaseType() {
        return releaseType;
    }

    public void setReleaseType(String releaseType) {
        this.releaseType = releaseType;
    }

    public String getLogisticsTypeEnName() {
        return logisticsTypeEnName;
    }

    public void setLogisticsTypeEnName(String logisticsTypeEnName) {
        this.logisticsTypeEnName = logisticsTypeEnName;
    }

    public String getServiceTypeIdList() {
        return serviceTypeIdList;
    }

    public void setServiceTypeIdList(String serviceTypeIdList) {
        this.serviceTypeIdList = serviceTypeIdList;
    }

    public String getImpExpType() {
        return impExpType;
    }

    public void setImpExpType(String impExpType) {
        this.impExpType = impExpType;
    }

    public String getPol() {
        return pol;
    }

    public void setPol(String pol) {
        this.pol = pol;
    }

    public String getDestinationPort() {
        return destinationPort;
    }

    public void setDestinationPort(String destinationPort) {
        this.destinationPort = destinationPort;
    }

    public String getRevenueTon() {
        return revenueTon;
    }

    public void setRevenueTon(String revenueTon) {
        this.revenueTon = revenueTon;
    }

    public String getGoodsNameSummary() {
        return goodsNameSummary;
    }

    public void setGoodsNameSummary(String goodsNameSummary) {
        this.goodsNameSummary = goodsNameSummary;
    }

    public String getBlTypeCode() {
        return blTypeCode;
    }

    public void setBlTypeCode(String blTypeCode) {
        this.blTypeCode = blTypeCode;
    }

    public String getSqdIssueType() {
        return sqdIssueType;
    }

    public void setSqdIssueType(String sqdIssueType) {
        this.sqdIssueType = sqdIssueType;
    }

    public String getSqdDocDeliveryWay() {
        return sqdDocDeliveryWay;
    }

    public void setSqdDocDeliveryWay(String sqdDocDeliveryWay) {
        this.sqdDocDeliveryWay = sqdDocDeliveryWay;
    }

    public String getCarrierEnName() {
        return carrierEnName;
    }

    public void setCarrierEnName(String carrierEnName) {
        this.carrierEnName = carrierEnName;
    }

    public String getAgreementTypeCode() {
        return agreementTypeCode;
    }

    public void setAgreementTypeCode(String agreementTypeCode) {
        this.agreementTypeCode = agreementTypeCode;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getWarehousingNo() {
        return warehousingNo;
    }

    public void setWarehousingNo(String warehousingNo) {
        this.warehousingNo = warehousingNo;
    }

    public String getSoNo() {
        return soNo;
    }

    public void setSoNo(String soNo) {
        this.soNo = soNo;
    }

    public String getBlNo() {
        return blNo;
    }

    public void setBlNo(String blNo) {
        this.blNo = blNo;
    }

    public String getSqdContainersSealsSum() {
        return sqdContainersSealsSum;
    }

    public void setSqdContainersSealsSum(String sqdContainersSealsSum) {
        this.sqdContainersSealsSum = sqdContainersSealsSum;
    }

    public Long getProcessStatusId() {
        return processStatusId;
    }

    public void setProcessStatusId(Long processStatusId) {
        this.processStatusId = processStatusId;
    }

    public Date getEtd() {
        return etd;
    }

    public void setEtd(Date etd) {
        this.etd = etd;
    }

    public Date getEta() {
        return eta;
    }

    public void setEta(Date eta) {
        this.eta = eta;
    }

    public String getPodEta() {
        return podEta;
    }

    public void setPodEta(String podEta) {
        this.podEta = podEta;
    }

    public String getDestinationPortEta() {
        return destinationPortEta;
    }

    public void setDestinationPortEta(String destinationPortEta) {
        this.destinationPortEta = destinationPortEta;
    }

    public Long getSalesId() {
        return salesId;
    }

    public void setSalesId(Long salesId) {
        this.salesId = salesId;
    }

    public Long getVerifyPsaId() {
        return verifyPsaId;
    }

    public void setVerifyPsaId(Long verifyPsaId) {
        this.verifyPsaId = verifyPsaId;
    }

    public Date getPsaVerifyTime() {
        return psaVerifyTime;
    }

    public void setPsaVerifyTime(Date psaVerifyTime) {
        this.psaVerifyTime = psaVerifyTime;
    }

    public Long getPsaVerifyStatusId() {
        return psaVerifyStatusId;
    }

    public void setPsaVerifyStatusId(Long psaVerifyStatusId) {
        this.psaVerifyStatusId = psaVerifyStatusId;
    }

    public Long getOpId() {
        return opId;
    }

    public void setOpId(Long opId) {
        this.opId = opId;
    }

    public Date getStatusUpdateTime() {
        return statusUpdateTime;
    }

    public void setStatusUpdateTime(Date statusUpdateTime) {
        this.statusUpdateTime = statusUpdateTime;
    }

    public Date getNewBookingTime() {
        return newBookingTime;
    }

    public void setNewBookingTime(Date newBookingTime) {
        this.newBookingTime = newBookingTime;
    }

    public Date getFirstAtd() {
        return firstAtd;
    }

    public void setFirstAtd(Date firstAtd) {
        this.firstAtd = firstAtd;
    }

    public Date getDestinationPortAta() {
        return destinationPortAta;
    }

    public void setDestinationPortAta(Date destinationPortAta) {
        this.destinationPortAta = destinationPortAta;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public Long getClientRoleId() {
        return clientRoleId;
    }

    public void setClientRoleId(Long clientRoleId) {
        this.clientRoleId = clientRoleId;
    }

    public String getClientContact() {
        return clientContact;
    }

    public void setClientContact(String clientContact) {
        this.clientContact = clientContact;
    }

    public String getClientContactTel() {
        return clientContactTel;
    }

    public void setClientContactTel(String clientContactTel) {
        this.clientContactTel = clientContactTel;
    }

    public String getClientContactEmail() {
        return clientContactEmail;
    }

    public void setClientContactEmail(String clientContactEmail) {
        this.clientContactEmail = clientContactEmail;
    }

    public String getRelationClientIdList() {
        return relationClientIdList;
    }

    public void setRelationClientIdList(String relationClientIdList) {
        this.relationClientIdList = relationClientIdList;
    }

    public String getPaymentTitleCode() {
        return paymentTitleCode;
    }

    public void setPaymentTitleCode(String paymentTitleCode) {
        this.paymentTitleCode = paymentTitleCode;
    }

    public String getTradingTerms() {
        return tradingTerms;
    }

    public void setTradingTerms(String tradingTerms) {
        this.tradingTerms = tradingTerms;
    }

    public String getLogisticsTerms() {
        return logisticsTerms;
    }

    public void setLogisticsTerms(String logisticsTerms) {
        this.logisticsTerms = logisticsTerms;
    }

    public String getTradingPaymentChannel() {
        return tradingPaymentChannel;
    }

    public void setTradingPaymentChannel(String tradingPaymentChannel) {
        this.tradingPaymentChannel = tradingPaymentChannel;
    }

    public String getClientContractNo() {
        return clientContractNo;
    }

    public void setClientContractNo(String clientContractNo) {
        this.clientContractNo = clientContractNo;
    }

    public String getClientInvoiceNo() {
        return clientInvoiceNo;
    }

    public void setClientInvoiceNo(String clientInvoiceNo) {
        this.clientInvoiceNo = clientInvoiceNo;
    }

    public String getCargoTypeIdSum() {
        return cargoTypeIdSum;
    }

    public void setCargoTypeIdSum(String cargoTypeIdSum) {
        this.cargoTypeIdSum = cargoTypeIdSum;
    }

    public Long getPackageQuantity() {
        return packageQuantity;
    }

    public void setPackageQuantity(Long packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public BigDecimal getGoodsVolume() {
        return goodsVolume;
    }

    public void setGoodsVolume(BigDecimal goodsVolume) {
        this.goodsVolume = goodsVolume;
    }

    public BigDecimal getGrossWeight() {
        return grossWeight;
    }

    public void setGrossWeight(BigDecimal grossWeight) {
        this.grossWeight = grossWeight;
    }

    public String getWeightUnitCode() {
        return weightUnitCode;
    }

    public void setWeightUnitCode(String weightUnitCode) {
        this.weightUnitCode = weightUnitCode;
    }

    public String getGoodsCurrencyCode() {
        return goodsCurrencyCode;
    }

    public void setGoodsCurrencyCode(String goodsCurrencyCode) {
        this.goodsCurrencyCode = goodsCurrencyCode;
    }

    public BigDecimal getGoodsValue() {
        return goodsValue;
    }

    public void setGoodsValue(BigDecimal goodsValue) {
        this.goodsValue = goodsValue;
    }

    public Long getLogisticsTypeId() {
        return logisticsTypeId;
    }

    public void setLogisticsTypeId(Long logisticsTypeId) {
        this.logisticsTypeId = logisticsTypeId;
    }

    public Long getPolId() {
        return polId;
    }

    public void setPolId(Long polId) {
        this.polId = polId;
    }

    public Long getLocalBasicPortId() {
        return localBasicPortId;
    }

    public void setLocalBasicPortId(Long localBasicPortId) {
        this.localBasicPortId = localBasicPortId;
    }

    public Long getTransitPortId() {
        return transitPortId;
    }

    public void setTransitPortId(Long transitPortId) {
        this.transitPortId = transitPortId;
    }

    public Long getPodId() {
        return podId;
    }

    public void setPodId(Long podId) {
        this.podId = podId;
    }

    public Long getDestinationPortId() {
        return destinationPortId;
    }

    public void setDestinationPortId(Long destinationPortId) {
        this.destinationPortId = destinationPortId;
    }

    public String getCvClosingTime() {
        return cvClosingTime;
    }

    public void setCvClosingTime(String cvClosingTime) {
        this.cvClosingTime = cvClosingTime;
    }

    public String getSiClosingTime() {
        return siClosingTime;
    }

    public void setSiClosingTime(String siClosingTime) {
        this.siClosingTime = siClosingTime;
    }

    public String getFirstVessel() {
        return firstVessel;
    }

    public void setFirstVessel(String firstVessel) {
        this.firstVessel = firstVessel;
    }

    public String getFirstVoyage() {
        return firstVoyage;
    }

    public void setFirstVoyage(String firstVoyage) {
        this.firstVoyage = firstVoyage;
    }

    public String getFirstCyOpenTime() {
        return firstCyOpenTime;
    }

    public void setFirstCyOpenTime(String firstCyOpenTime) {
        this.firstCyOpenTime = firstCyOpenTime;
    }

    public String getFirstCyClosingTime() {
        return firstCyClosingTime;
    }

    public void setFirstCyClosingTime(String firstCyClosingTime) {
        this.firstCyClosingTime = firstCyClosingTime;
    }

    public Date getFirstEtd() {
        return firstEtd;
    }

    public void setFirstEtd(Date firstEtd) {
        this.firstEtd = firstEtd;
    }

    public String getBasicVessel() {
        return basicVessel;
    }

    public void setBasicVessel(String basicVessel) {
        this.basicVessel = basicVessel;
    }

    public String getBasicVoyage() {
        return basicVoyage;
    }

    public void setBasicVoyage(String basicVoyage) {
        this.basicVoyage = basicVoyage;
    }

    public Date getBasicFinalGateinTime() {
        return basicFinalGateinTime;
    }

    public void setBasicFinalGateinTime(Date basicFinalGateinTime) {
        this.basicFinalGateinTime = basicFinalGateinTime;
    }

    public Date getBasicEtd() {
        return basicEtd;
    }

    public void setBasicEtd(Date basicEtd) {
        this.basicEtd = basicEtd;
    }

    public Long getCarrierId() {
        return carrierId;
    }

    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    public String getInquiryScheduleSummary() {
        return inquiryScheduleSummary;
    }

    public void setInquiryScheduleSummary(String inquiryScheduleSummary) {
        this.inquiryScheduleSummary = inquiryScheduleSummary;
    }

    public Long getPolBookingAgent() {
        return polBookingAgent;
    }

    public void setPolBookingAgent(Long polBookingAgent) {
        this.polBookingAgent = polBookingAgent;
    }

    public Long getPodHandleAgent() {
        return podHandleAgent;
    }

    public void setPodHandleAgent(Long podHandleAgent) {
        this.podHandleAgent = podHandleAgent;
    }

    public String getSqdExportCustomsType() {
        return sqdExportCustomsType;
    }

    public void setSqdExportCustomsType(String sqdExportCustomsType) {
        this.sqdExportCustomsType = sqdExportCustomsType;
    }

    public String getSqdTrailerType() {
        return sqdTrailerType;
    }

    public void setSqdTrailerType(String sqdTrailerType) {
        this.sqdTrailerType = sqdTrailerType;
    }

    public String getRctProcessStatusSummary() {
        return rctProcessStatusSummary;
    }

    public void setRctProcessStatusSummary(String rctProcessStatusSummary) {
        this.rctProcessStatusSummary = rctProcessStatusSummary;
    }

    public String getTransportStatusSummary() {
        return transportStatusSummary;
    }

    public void setTransportStatusSummary(String transportStatusSummary) {
        this.transportStatusSummary = transportStatusSummary;
    }

    public String getDocStatusSummary() {
        return docStatusSummary;
    }

    public void setDocStatusSummary(String docStatusSummary) {
        this.docStatusSummary = docStatusSummary;
    }

    public String getPaymentReceivingStatusSummary() {
        return paymentReceivingStatusSummary;
    }

    public void setPaymentReceivingStatusSummary(String paymentReceivingStatusSummary) {
        this.paymentReceivingStatusSummary = paymentReceivingStatusSummary;
    }

    public String getPaymentPayingStatusSummary() {
        return paymentPayingStatusSummary;
    }

    public void setPaymentPayingStatusSummary(String paymentPayingStatusSummary) {
        this.paymentPayingStatusSummary = paymentPayingStatusSummary;
    }

    public String getTransportStatus() {
        return transportStatus;
    }

    public void setTransportStatus(String transportStatus) {
        this.transportStatus = transportStatus;
    }

    public String getTransportStatusA() {
        return transportStatusA;
    }

    public void setTransportStatusA(String transportStatusA) {
        this.transportStatusA = transportStatusA;
    }

    public String getTransportStatusB() {
        return transportStatusB;
    }

    public void setTransportStatusB(String transportStatusB) {
        this.transportStatusB = transportStatusB;
    }

    public String getDocStatus() {
        return docStatus;
    }

    public void setDocStatus(String docStatus) {
        this.docStatus = docStatus;
    }

    public String getDocStatusA() {
        return docStatusA;
    }

    public void setDocStatusA(String docStatusA) {
        this.docStatusA = docStatusA;
    }

    public String getDocStatusB() {
        return docStatusB;
    }

    public void setDocStatusB(String docStatusB) {
        this.docStatusB = docStatusB;
    }

    public String getPaymentPayingStatus() {
        return paymentPayingStatus;
    }

    public void setPaymentPayingStatus(String paymentPayingStatus) {
        this.paymentPayingStatus = paymentPayingStatus;
    }

    public Long getRctProcessId() {
        return rctProcessId;
    }

    public void setRctProcessId(Long rctProcessId) {
        this.rctProcessId = rctProcessId;
    }

    public Long getProcessId() {
        return processId;
    }

    public void setProcessId(Long processId) {
        this.processId = processId;
    }

    public Long getPorcessStatusId() {
        return porcessStatusId;
    }

    public void setPorcessStatusId(Long porcessStatusId) {
        this.porcessStatusId = porcessStatusId;
    }

    public Date getProcessStatusTime() {
        return processStatusTime;
    }

    public void setProcessStatusTime(Date processStatusTime) {
        this.processStatusTime = processStatusTime;
    }

    public String getProcessRemark() {
        return processRemark;
    }

    public void setProcessRemark(String processRemark) {
        this.processRemark = processRemark;
    }

    public String getQoutationNo() {
        return qoutationNo;
    }

    public void setQoutationNo(String qoutationNo) {
        this.qoutationNo = qoutationNo;
    }

    public String getQoutationSketch() {
        return qoutationSketch;
    }

    public void setQoutationSketch(String qoutationSketch) {
        this.qoutationSketch = qoutationSketch;
    }

    public Date getQoutationTime() {
        return qoutationTime;
    }

    public void setQoutationTime(Date qoutationTime) {
        this.qoutationTime = qoutationTime;
    }

    public String getNewBookingNo() {
        return newBookingNo;
    }

    public void setNewBookingNo(String newBookingNo) {
        this.newBookingNo = newBookingNo;
    }

    public String getNewBookingRemark() {
        return newBookingRemark;
    }

    public void setNewBookingRemark(String newBookingRemark) {
        this.newBookingRemark = newBookingRemark;
    }

    public Long getSalesAssistantId() {
        return salesAssistantId;
    }

    public void setSalesAssistantId(Long salesAssistantId) {
        this.salesAssistantId = salesAssistantId;
    }

    public Long getSalesObserverId() {
        return salesObserverId;
    }

    public void setSalesObserverId(Long salesObserverId) {
        this.salesObserverId = salesObserverId;
    }

    public String getInquiryNoticeSum() {
        return inquiryNoticeSum;
    }

    public void setInquiryNoticeSum(String inquiryNoticeSum) {
        this.inquiryNoticeSum = inquiryNoticeSum;
    }

    public String getInquiryInnerRemarkSum() {
        return inquiryInnerRemarkSum;
    }

    public void setInquiryInnerRemarkSum(String inquiryInnerRemarkSum) {
        this.inquiryInnerRemarkSum = inquiryInnerRemarkSum;
    }

    public String getOpLeaderNotice() {
        return opLeaderNotice;
    }

    public void setOpLeaderNotice(String opLeaderNotice) {
        this.opLeaderNotice = opLeaderNotice;
    }

    public String getOpInnerRemark() {
        return opInnerRemark;
    }

    public void setOpInnerRemark(String opInnerRemark) {
        this.opInnerRemark = opInnerRemark;
    }

    public Long getBookingOpId() {
        return bookingOpId;
    }

    public void setBookingOpId(Long bookingOpId) {
        this.bookingOpId = bookingOpId;
    }

    public Long getDocOpId() {
        return docOpId;
    }

    public void setDocOpId(Long docOpId) {
        this.docOpId = docOpId;
    }

    public Long getOpObserverId() {
        return opObserverId;
    }

    public void setOpObserverId(Long opObserverId) {
        this.opObserverId = opObserverId;
    }

    @Override
    public Long getDeleteBy() {
        return deleteBy;
    }

    @Override
    public void setDeleteBy(Long deleteBy) {
        this.deleteBy = deleteBy;
    }

    @Override
    public Date getDeleteTime() {
        return deleteTime;
    }

    @Override
    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Long getPrecarriageRegionId() {
        return precarriageRegionId;
    }

    public void setPrecarriageRegionId(Long precarriageRegionId) {
        this.precarriageRegionId = precarriageRegionId;
    }

    public String getPrecarriageAddress() {
        return precarriageAddress;
    }

    public void setPrecarriageAddress(String precarriageAddress) {
        this.precarriageAddress = precarriageAddress;
    }

    public Date getPrecarriageTime() {
        return precarriageTime;
    }

    public void setPrecarriageTime(Date precarriageTime) {
        this.precarriageTime = precarriageTime;
    }

    public String getPrecarriageContact() {
        return precarriageContact;
    }

    public void setPrecarriageContact(String precarriageContact) {
        this.precarriageContact = precarriageContact;
    }

    public String getPrecarriageTel() {
        return precarriageTel;
    }

    public void setPrecarriageTel(String precarriageTel) {
        this.precarriageTel = precarriageTel;
    }

    public String getPrecarriageRemark() {
        return precarriageRemark;
    }

    public void setPrecarriageRemark(String precarriageRemark) {
        this.precarriageRemark = precarriageRemark;
    }

    public Long getDispatchRegionId() {
        return dispatchRegionId;
    }

    public void setDispatchRegionId(Long dispatchRegionId) {
        this.dispatchRegionId = dispatchRegionId;
    }

    public String getDispatchAddress() {
        return dispatchAddress;
    }

    public void setDispatchAddress(String dispatchAddress) {
        this.dispatchAddress = dispatchAddress;
    }

    public Date getDispatchTime() {
        return dispatchTime;
    }

    public void setDispatchTime(Date dispatchTime) {
        this.dispatchTime = dispatchTime;
    }

    public String getDispatchContact() {
        return dispatchContact;
    }

    public void setDispatchContact(String dispatchContact) {
        this.dispatchContact = dispatchContact;
    }

    public String getDispatchTel() {
        return dispatchTel;
    }

    public void setDispatchTel(String dispatchTel) {
        this.dispatchTel = dispatchTel;
    }

    public String getDispatchRemark() {
        return dispatchRemark;
    }

    public void setDispatchRemark(String dispatchRemark) {
        this.dispatchRemark = dispatchRemark;
    }

    public String getPaymentReceivingStatus() {
        return paymentReceivingStatus;
    }

    public void setPaymentReceivingStatus(String paymentReceivingStatus) {
        this.paymentReceivingStatus = paymentReceivingStatus;
    }

    public String getSqdWarehousingStatus() {
        return sqdWarehousingStatus;
    }

    public void setSqdWarehousingStatus(String sqdWarehousingStatus) {
        this.sqdWarehousingStatus = sqdWarehousingStatus;
    }

    public String getSqdShippingBookingStatus() {
        return sqdShippingBookingStatus;
    }

    public void setSqdShippingBookingStatus(String sqdShippingBookingStatus) {
        this.sqdShippingBookingStatus = sqdShippingBookingStatus;
    }

    public String getSqdTrailerBookingStatus() {
        return sqdTrailerBookingStatus;
    }

    public void setSqdTrailerBookingStatus(String sqdTrailerBookingStatus) {
        this.sqdTrailerBookingStatus = sqdTrailerBookingStatus;
    }

    public String getSqdContainerBookingStatus() {
        return sqdContainerBookingStatus;
    }

    public void setSqdContainerBookingStatus(String sqdContainerBookingStatus) {
        this.sqdContainerBookingStatus = sqdContainerBookingStatus;
    }

    public String getSqdContainerLoadingStatus() {
        return sqdContainerLoadingStatus;
    }

    public void setSqdContainerLoadingStatus(String sqdContainerLoadingStatus) {
        this.sqdContainerLoadingStatus = sqdContainerLoadingStatus;
    }

    public String getSqdVesselArrangeStatus() {
        return sqdVesselArrangeStatus;
    }

    public void setSqdVesselArrangeStatus(String sqdVesselArrangeStatus) {
        this.sqdVesselArrangeStatus = sqdVesselArrangeStatus;
    }

    public String getSqdVgmStatus() {
        return sqdVgmStatus;
    }

    public void setSqdVgmStatus(String sqdVgmStatus) {
        this.sqdVgmStatus = sqdVgmStatus;
    }

    public String getSqdCustomDocsStatus() {
        return sqdCustomDocsStatus;
    }

    public void setSqdCustomDocsStatus(String sqdCustomDocsStatus) {
        this.sqdCustomDocsStatus = sqdCustomDocsStatus;
    }

    public String getSqdCustomAuthorizedStatus() {
        return sqdCustomAuthorizedStatus;
    }

    public void setSqdCustomAuthorizedStatus(String sqdCustomAuthorizedStatus) {
        this.sqdCustomAuthorizedStatus = sqdCustomAuthorizedStatus;
    }

    public String getSqdCustomExamineStatus() {
        return sqdCustomExamineStatus;
    }

    public void setSqdCustomExamineStatus(String sqdCustomExamineStatus) {
        this.sqdCustomExamineStatus = sqdCustomExamineStatus;
    }

    public String getSqdCustomReleaseStatus() {
        return sqdCustomReleaseStatus;
    }

    public void setSqdCustomReleaseStatus(String sqdCustomReleaseStatus) {
        this.sqdCustomReleaseStatus = sqdCustomReleaseStatus;
    }

    public String getSqdSiVerifyStatus() {
        return sqdSiVerifyStatus;
    }

    public void setSqdSiVerifyStatus(String sqdSiVerifyStatus) {
        this.sqdSiVerifyStatus = sqdSiVerifyStatus;
    }

    public String getSqdSiPostStatus() {
        return sqdSiPostStatus;
    }

    public void setSqdSiPostStatus(String sqdSiPostStatus) {
        this.sqdSiPostStatus = sqdSiPostStatus;
    }

    public String getSqdAmsEnsPostStatus() {
        return sqdAmsEnsPostStatus;
    }

    public void setSqdAmsEnsPostStatus(String sqdAmsEnsPostStatus) {
        this.sqdAmsEnsPostStatus = sqdAmsEnsPostStatus;
    }

    public String getSqdIsfEmnfPostStatus() {
        return sqdIsfEmnfPostStatus;
    }

    public void setSqdIsfEmnfPostStatus(String sqdIsfEmnfPostStatus) {
        this.sqdIsfEmnfPostStatus = sqdIsfEmnfPostStatus;
    }

    public String getSqdMainServicePayingStatus() {
        return sqdMainServicePayingStatus;
    }

    public void setSqdMainServicePayingStatus(String sqdMainServicePayingStatus) {
        this.sqdMainServicePayingStatus = sqdMainServicePayingStatus;
    }

    public String getBlGettingStatus() {
        return blGettingStatus;
    }

    public void setBlGettingStatus(String blGettingStatus) {
        this.blGettingStatus = blGettingStatus;
    }

    public String getBlReleasingStatus() {
        return blReleasingStatus;
    }

    public void setBlReleasingStatus(String blReleasingStatus) {
        this.blReleasingStatus = blReleasingStatus;
    }

    public String getSqdContainerNoSum() {
        return sqdContainerNoSum;
    }

    public void setSqdContainerNoSum(String sqdContainerNoSum) {
        this.sqdContainerNoSum = sqdContainerNoSum;
    }

    public Long getSqdPolBookingAgent() {
        return sqdPolBookingAgent;
    }

    public void setSqdPolBookingAgent(Long sqdPolBookingAgent) {
        this.sqdPolBookingAgent = sqdPolBookingAgent;
    }

    public Long getSqdPodHandleAgent() {
        return sqdPodHandleAgent;
    }

    public void setSqdPodHandleAgent(Long sqdPodHandleAgent) {
        this.sqdPodHandleAgent = sqdPodHandleAgent;
    }

    public Long getSqdCarrierId() {
        return sqdCarrierId;
    }

    public void setSqdCarrierId(Long sqdCarrierId) {
        this.sqdCarrierId = sqdCarrierId;
    }

    public String getLogisticsPaymentTermsCode() {
        return logisticsPaymentTermsCode;
    }

    public void setLogisticsPaymentTermsCode(String logisticsPaymentTermsCode) {
        this.logisticsPaymentTermsCode = logisticsPaymentTermsCode;
    }

    public String getClientJobNo() {
        return clientJobNo;
    }

    public void setClientJobNo(String clientJobNo) {
        this.clientJobNo = clientJobNo;
    }

    public String getBookingShipper() {
        return bookingShipper;
    }

    public void setBookingShipper(String bookingShipper) {
        this.bookingShipper = bookingShipper;
    }

    public String getBookingConsignee() {
        return bookingConsignee;
    }

    public void setBookingConsignee(String bookingConsignee) {
        this.bookingConsignee = bookingConsignee;
    }

    public String getBookingNotifyParty() {
        return bookingNotifyParty;
    }

    public void setBookingNotifyParty(String bookingNotifyParty) {
        this.bookingNotifyParty = bookingNotifyParty;
    }

    public String getSqdInsuranceType() {
        return sqdInsuranceType;
    }

    public void setSqdInsuranceType(String sqdInsuranceType) {
        this.sqdInsuranceType = sqdInsuranceType;
    }

    public String getIsOpConfirmed() {
        return isOpConfirmed;
    }

    public void setIsOpConfirmed(String isOpConfirmed) {
        this.isOpConfirmed = isOpConfirmed;
    }

    public Long getOpConfirmedId() {
        return opConfirmedId;
    }

    public void setOpConfirmedId(Long opConfirmedId) {
        this.opConfirmedId = opConfirmedId;
    }

    public String getOpConfirmedName() {
        return opConfirmedName;
    }

    public void setOpConfirmedName(String opConfirmedName) {
        this.opConfirmedName = opConfirmedName;
    }

    public Date getOpConfirmedDate() {
        return opConfirmedDate;
    }

    public void setOpConfirmedDate(Date opConfirmedDate) {
        this.opConfirmedDate = opConfirmedDate;
    }

    public String getIsSalesConfirmed() {
        return isSalesConfirmed;
    }

    public void setIsSalesConfirmed(String isSalesConfirmed) {
        this.isSalesConfirmed = isSalesConfirmed;
    }

    public Long getSalesConfirmedId() {
        return salesConfirmedId;
    }

    public void setSalesConfirmedId(Long salesConfirmedId) {
        this.salesConfirmedId = salesConfirmedId;
    }

    public String getSalesConfirmedName() {
        return salesConfirmedName;
    }

    public void setSalesConfirmedName(String salesConfirmedName) {
        this.salesConfirmedName = salesConfirmedName;
    }

    public Date getSalesConfirmedDate() {
        return salesConfirmedDate;
    }

    public void setSalesConfirmedDate(Date salesConfirmedDate) {
        this.salesConfirmedDate = salesConfirmedDate;
    }

    public String getIsClientConfirmed() {
        return isClientConfirmed;
    }

    public void setIsClientConfirmed(String isClientConfirmed) {
        this.isClientConfirmed = isClientConfirmed;
    }

    public Long getClientConfirmedId() {
        return clientConfirmedId;
    }

    public void setClientConfirmedId(Long clientConfirmedId) {
        this.clientConfirmedId = clientConfirmedId;
    }

    public String getBlFormCode() {
        return blFormCode;
    }

    public void setBlFormCode(String blFormCode) {
        this.blFormCode = blFormCode;
    }

    public String getClientConfirmedName() {
        return clientConfirmedName;
    }

    public void setClientConfirmedName(String clientConfirmedName) {
        this.clientConfirmedName = clientConfirmedName;
    }

    public Date getClientConfirmedDate() {
        return clientConfirmedDate;
    }

    public void setClientConfirmedDate(Date clientConfirmedDate) {
        this.clientConfirmedDate = clientConfirmedDate;
    }

    public String getIsAccountConfirmed() {
        return isAccountConfirmed;
    }

    public void setIsAccountConfirmed(String isAccountConfirmed) {
        this.isAccountConfirmed = isAccountConfirmed;
    }

    public Long getAccountConfirmedId() {
        return accountConfirmedId;
    }

    public void setAccountConfirmedId(Long accountConfirmedId) {
        this.accountConfirmedId = accountConfirmedId;
    }

    public String getAccountConfirmedName() {
        return accountConfirmedName;
    }

    public void setAccountConfirmedName(String accountConfirmedName) {
        this.accountConfirmedName = accountConfirmedName;
    }

    public Date getAccountConfirmedDate() {
        return accountConfirmedDate;
    }

    public void setAccountConfirmedDate(Date accountConfirmedDate) {
        this.accountConfirmedDate = accountConfirmedDate;
    }

    public String getIsDirectLine() {
        return isDirectLine;
    }

    public void setIsDirectLine(String isDirectLine) {
        this.isDirectLine = isDirectLine;
    }

    public String getIsBatches() {
        return isBatches;
    }

    public void setIsBatches(String isBatches) {
        this.isBatches = isBatches;
    }

    public Long getVerifyOpLeaderId() {
        return verifyOpLeaderId;
    }

    public void setVerifyOpLeaderId(Long verifyOpLeaderId) {
        this.verifyOpLeaderId = verifyOpLeaderId;
    }

    public Date getOpLeaderVerifyTime() {
        return opLeaderVerifyTime;
    }

    public void setOpLeaderVerifyTime(Date opLeaderVerifyTime) {
        this.opLeaderVerifyTime = opLeaderVerifyTime;
    }

    public BigDecimal getSqdReceivedRmbSum() {
        return sqdReceivedRmbSum;
    }

    public void setSqdReceivedRmbSum(BigDecimal sqdReceivedRmbSum) {
        this.sqdReceivedRmbSum = sqdReceivedRmbSum;
    }

    public BigDecimal getSqdPaidRmbSum() {
        return sqdPaidRmbSum;
    }

    public void setSqdPaidRmbSum(BigDecimal sqdPaidRmbSum) {
        this.sqdPaidRmbSum = sqdPaidRmbSum;
    }

    public String getMessageDisplay() {
        return messageDisplay;
    }

    public void setMessageDisplay(String messageDisplay) {
        this.messageDisplay = messageDisplay;
    }

    public String getServiceMessageFold() {
        return serviceMessageFold;
    }

    public void setServiceMessageFold(String serviceMessageFold) {
        this.serviceMessageFold = serviceMessageFold;
    }

    public RsClientMessage getRsClientMessage() {
        return rsClientMessage;
    }

    public void setRsClientMessage(RsClientMessage rsClientMessage) {
        this.rsClientMessage = rsClientMessage;
    }

    public RsBasicLogistics getRsBasicLogistics() {
        return rsBasicLogistics;
    }

    public void setRsBasicLogistics(RsBasicLogistics rsBasicLogistics) {
        this.rsBasicLogistics = rsBasicLogistics;
    }

    public RsPrecarriage getRsPrecarriage() {
        return rsPrecarriage;
    }

    public void setRsPrecarriage(RsPrecarriage rsPrecarriage) {
        this.rsPrecarriage = rsPrecarriage;
    }

    public RsExportCustoms getRsExportCustoms() {
        return rsExportCustoms;
    }

    public void setRsExportCustoms(RsExportCustoms rsExportCustoms) {
        this.rsExportCustoms = rsExportCustoms;
    }

    public RsImportCustoms getRsImportCustoms() {
        return rsImportCustoms;
    }

    public void setRsImportCustoms(RsImportCustoms rsImportCustoms) {
        this.rsImportCustoms = rsImportCustoms;
    }

    public Long[] getServiceTypeIds() {
        return serviceTypeIds;
    }

    public void setServiceTypeIds(Long[] serviceTypeIds) {
        this.serviceTypeIds = serviceTypeIds;
    }

    public Long[] getCargoTypeIds() {
        return cargoTypeIds;
    }

    public void setCargoTypeIds(Long[] cargoTypeIds) {
        this.cargoTypeIds = cargoTypeIds;
    }

    public Long[] getCarrierIds() {
        return carrierIds;
    }

    public void setCarrierIds(Long[] carrierIds) {
        this.carrierIds = carrierIds;
    }

    public Long[] getDestinationPortIds() {
        return destinationPortIds;
    }

    public void setDestinationPortIds(Long[] destinationPortIds) {
        this.destinationPortIds = destinationPortIds;
    }

    public Long[] getPolIds() {
        return polIds;
    }

    public void setPolIds(Long[] polIds) {
        this.polIds = polIds;
    }

    public Long[] getLineIds() {
        return lineIds;
    }

    public void setLineIds(Long[] lineIds) {
        this.lineIds = lineIds;
    }

    public List<Long> getRctIds() {
        return rctIds;
    }

    public void setRctIds(List<Long> rctIds) {
        this.rctIds = rctIds;
    }

    @Override
    public Long[] getPermissionLevel() {
        return permissionLevel;
    }

    @Override
    public void setPermissionLevel(Long[] permissionLevel) {
        this.permissionLevel = permissionLevel;
    }

    public RsOpSeaFcl getRsOpSeaFcl() {
        return rsOpSeaFcl;
    }

    public void setRsOpSeaFcl(RsOpSeaFcl rsOpSeaFcl) {
        this.rsOpSeaFcl = rsOpSeaFcl;
    }

    public List<RsOpSeaFcl> getRsOpSeaFclList() {
        return rsOpSeaFclList;
    }

    public void setRsOpSeaFclList(List<RsOpSeaFcl> rsOpSeaFclList) {
        this.rsOpSeaFclList = rsOpSeaFclList;
    }

    public RsOpSeaLcl getRsOpSeaLcl() {
        return rsOpSeaLcl;
    }

    public void setRsOpSeaLcl(RsOpSeaLcl rsOpSeaLcl) {
        this.rsOpSeaLcl = rsOpSeaLcl;
    }

    public List<RsOpSeaLcl> getRsOpSeaLclList() {
        return rsOpSeaLclList;
    }

    public void setRsOpSeaLclList(List<RsOpSeaLcl> rsOpSeaLclList) {
        this.rsOpSeaLclList = rsOpSeaLclList;
    }

    public RsOpAir getRsOpAir() {
        return rsOpAir;
    }

    public void setRsOpAir(RsOpAir rsOpAir) {
        this.rsOpAir = rsOpAir;
    }

    public List<RsOpAir> getRsOpAirList() {
        return rsOpAirList;
    }

    public void setRsOpAirList(List<RsOpAir> rsOpAirList) {
        this.rsOpAirList = rsOpAirList;
    }

    public RsOpRail getRsOpRail() {
        return rsOpRail;
    }

    public void setRsOpRail(RsOpRail rsOpRail) {
        this.rsOpRail = rsOpRail;
    }

    public RsOpExpress getRsOpExpress() {
        return rsOpExpress;
    }

    public void setRsOpExpress(RsOpExpress rsOpExpress) {
        this.rsOpExpress = rsOpExpress;
    }

    public RsOpPortService getRsOpPortService() {
        return rsOpPortService;
    }

    public void setRsOpPortService(RsOpPortService rsOpPortService) {
        this.rsOpPortService = rsOpPortService;
    }

    public RsOpTruck getRsOpTruck() {
        return rsOpTruck;
    }

    public void setRsOpTruck(RsOpTruck rsOpTruck) {
        this.rsOpTruck = rsOpTruck;
    }

    public List<RsOpTruck> getRsOpTruckList() {
        return rsOpTruckList;
    }

    public void setRsOpTruckList(List<RsOpTruck> rsOpTruckList) {
        this.rsOpTruckList = rsOpTruckList;
    }

    public RsOpExportCustomsClearance getRsOpExportCustomsClearance() {
        return rsOpExportCustomsClearance;
    }

    public void setRsOpExportCustomsClearance(RsOpExportCustomsClearance rsOpExportCustomsClearance) {
        this.rsOpExportCustomsClearance = rsOpExportCustomsClearance;
    }

    public RsOpImportCustomsClearance getRsOpImportCustomsClearance() {
        return rsOpImportCustomsClearance;
    }

    public void setRsOpImportCustomsClearance(RsOpImportCustomsClearance rsOpImportCustomsClearance) {
        this.rsOpImportCustomsClearance = rsOpImportCustomsClearance;
    }

    public RsOpImportDispatchTruck getRsOpImportDispatchTruck() {
        return rsOpImportDispatchTruck;
    }

    public void setRsOpImportDispatchTruck(RsOpImportDispatchTruck rsOpImportDispatchTruck) {
        this.rsOpImportDispatchTruck = rsOpImportDispatchTruck;
    }

    public RsOpWarehouse getRsOpWarehouse() {
        return rsOpWarehouse;
    }

    public void setRsOpWarehouse(RsOpWarehouse rsOpWarehouse) {
        this.rsOpWarehouse = rsOpWarehouse;
    }

    public RsOpInspectionAndCertificate getRsOpInspectionAndCertificate() {
        return rsOpInspectionAndCertificate;
    }

    public void setRsOpInspectionAndCertificate(RsOpInspectionAndCertificate rsOpInspectionAndCertificate) {
        this.rsOpInspectionAndCertificate = rsOpInspectionAndCertificate;
    }

    public RsOpLand getRsOpLand() {
        return rsOpLand;
    }

    public void setRsOpLand(RsOpLand rsOpLand) {
        this.rsOpLand = rsOpLand;
    }

    public RsOpInsurance getRsOpInsurance() {
        return rsOpInsurance;
    }

    public void setRsOpInsurance(RsOpInsurance rsOpInsurance) {
        this.rsOpInsurance = rsOpInsurance;
    }

    public RsOpExpandService getRsOpExpandService() {
        return rsOpExpandService;
    }

    public void setRsOpExpandService(RsOpExpandService rsOpExpandService) {
        this.rsOpExpandService = rsOpExpandService;
    }

    public RsOpRailFCL getRsOpRailFCL() {
        return rsOpRailFCL;
    }

    public void setRsOpRailFCL(RsOpRailFCL rsOpRailFCL) {
        this.rsOpRailFCL = rsOpRailFCL;
    }

    public RsOpRailLCL getRsOpRailLCL() {
        return rsOpRailLCL;
    }

    public void setRsOpRailLCL(RsOpRailLCL rsOpRailLCL) {
        this.rsOpRailLCL = rsOpRailLCL;
    }

    public RsOpCtnrTruck getRsOpCtnrTruck() {
        return rsOpCtnrTruck;
    }

    public void setRsOpCtnrTruck(RsOpCtnrTruck rsOpCtnrTruck) {
        this.rsOpCtnrTruck = rsOpCtnrTruck;
    }

    public List<RsOpCtnrTruck> getRsOpCtnrTruckList() {
        return rsOpCtnrTruckList;
    }

    public void setRsOpCtnrTruckList(List<RsOpCtnrTruck> rsOpCtnrTruckList) {
        this.rsOpCtnrTruckList = rsOpCtnrTruckList;
    }

    public RsOpBulkTruck getRsOpBulkTruck() {
        return rsOpBulkTruck;
    }

    public void setRsOpBulkTruck(RsOpBulkTruck rsOpBulkTruck) {
        this.rsOpBulkTruck = rsOpBulkTruck;
    }

    public List<RsOpBulkTruck> getRsOpBulkTruckList() {
        return rsOpBulkTruckList;
    }

    public void setRsOpBulkTruckList(List<RsOpBulkTruck> rsOpBulkTruckList) {
        this.rsOpBulkTruckList = rsOpBulkTruckList;
    }

    public RsOpDocDeclare getRsOpDocDeclare() {
        return rsOpDocDeclare;
    }

    public void setRsOpDocDeclare(RsOpDocDeclare rsOpDocDeclare) {
        this.rsOpDocDeclare = rsOpDocDeclare;
    }

    public List<RsOpDocDeclare> getRsOpDocDeclareList() {
        return rsOpDocDeclareList;
    }

    public void setRsOpDocDeclareList(List<RsOpDocDeclare> rsOpDocDeclareList) {
        this.rsOpDocDeclareList = rsOpDocDeclareList;
    }

    public RsOpFreeDeclare getRsOpFreeDeclare() {
        return rsOpFreeDeclare;
    }

    public void setRsOpFreeDeclare(RsOpFreeDeclare rsOpFreeDeclare) {
        this.rsOpFreeDeclare = rsOpFreeDeclare;
    }

    public List<RsOpFreeDeclare> getRsOpFreeDeclareList() {
        return rsOpFreeDeclareList;
    }

    public void setRsOpFreeDeclareList(List<RsOpFreeDeclare> rsOpFreeDeclareList) {
        this.rsOpFreeDeclareList = rsOpFreeDeclareList;
    }

    public RsOpDOAgent getRsOpDOAgent() {
        return rsOpDOAgent;
    }

    public void setRsOpDOAgent(RsOpDOAgent rsOpDOAgent) {
        this.rsOpDOAgent = rsOpDOAgent;
    }

    public RsOpClearAgent getRsOpClearAgent() {
        return rsOpClearAgent;
    }

    public void setRsOpClearAgent(RsOpClearAgent rsOpClearAgent) {
        this.rsOpClearAgent = rsOpClearAgent;
    }

    public RsOpWHS getRsOpWHS() {
        return rsOpWHS;
    }

    public void setRsOpWHS(RsOpWHS rsOpWHS) {
        this.rsOpWHS = rsOpWHS;
    }

    public RsOpOther getRsOpOther() {
        return rsOpOther;
    }

    public void setRsOpOther(RsOpOther rsOpOther) {
        this.rsOpOther = rsOpOther;
    }

    public RsOp3rdCert getRsOp3rdCert() {
        return rsOp3rdCert;
    }

    public void setRsOp3rdCert(RsOp3rdCert rsOp3rdCert) {
        this.rsOp3rdCert = rsOp3rdCert;
    }

    public RsOpINS getRsOpINS() {
        return rsOpINS;
    }

    public void setRsOpINS(RsOpINS rsOpINS) {
        this.rsOpINS = rsOpINS;
    }

    public RsOpTrading getRsOpTrading() {
        return rsOpTrading;
    }

    public void setRsOpTrading(RsOpTrading rsOpTrading) {
        this.rsOpTrading = rsOpTrading;
    }

    public RsOpFumigation getRsOpFumigation() {
        return rsOpFumigation;
    }

    public void setRsOpFumigation(RsOpFumigation rsOpFumigation) {
        this.rsOpFumigation = rsOpFumigation;
    }

    public RsOpCO getRsOpCO() {
        return rsOpCO;
    }

    public void setRsOpCO(RsOpCO rsOpCO) {
        this.rsOpCO = rsOpCO;
    }

    public Long getOpLeaderVerifyStatusId() {
        return opLeaderVerifyStatusId;
    }

    public void setOpLeaderVerifyStatusId(Long opLeaderVerifyStatusId) {
        this.opLeaderVerifyStatusId = opLeaderVerifyStatusId;
    }

    public String getBookingAgent() {
        return bookingAgent;
    }

    public void setBookingAgent(String bookingAgent) {
        this.bookingAgent = bookingAgent;
    }

    public String getPrecarriageSupplierNo() {
        return precarriageSupplierNo;
    }

    public void setPrecarriageSupplierNo(String precarriageSupplierNo) {
        this.precarriageSupplierNo = precarriageSupplierNo;
    }

    public String getNoTransferAllowed() {
        return noTransferAllowed;
    }

    public void setNoTransferAllowed(String noTransferAllowed) {
        this.noTransferAllowed = noTransferAllowed;
    }

    public String getNoDividedAllowed() {
        return noDividedAllowed;
    }

    public void setNoDividedAllowed(String noDividedAllowed) {
        this.noDividedAllowed = noDividedAllowed;
    }

    public String getNoAgreementShowed() {
        return noAgreementShowed;
    }

    public void setNoAgreementShowed(String noAgreementShowed) {
        this.noAgreementShowed = noAgreementShowed;
    }

    public String getOpAccept() {
        return opAccept;
    }

    public void setOpAccept(String opAccept) {
        this.opAccept = opAccept;
    }

    public String getPsaVerify() {
        return psaVerify;
    }

    public void setPsaVerify(String psaVerify) {
        this.psaVerify = psaVerify;
    }

    public String getSqdCarrier() {
        return sqdCarrier;
    }

    public void setSqdCarrier(String sqdCarrier) {
        this.sqdCarrier = sqdCarrier;
    }

    public Set<Long> getSupplierIds() {
        return supplierIds;
    }

    public void setSupplierIds(Set<Long> supplierIds) {
        this.supplierIds = supplierIds;
    }

    public List<RsBookingMessage> getBookingMessagesList() {
        return bookingMessagesList;
    }

    public void setBookingMessagesList(List<RsBookingMessage> bookingMessagesList) {
        this.bookingMessagesList = bookingMessagesList;
    }

    public String getBookingAgentRemark() {
        return bookingAgentRemark;
    }

    public void setBookingAgentRemark(String bookingAgentRemark) {
        this.bookingAgentRemark = bookingAgentRemark;
    }

    public String getShippingMark() {
        return shippingMark;
    }

    public void setShippingMark(String shippingMark) {
        this.shippingMark = shippingMark;
    }

    public String getPolName() {
        return polName;
    }

    public void setPolName(String polName) {
        this.polName = polName;
    }

    public String getPodName() {
        return podName;
    }

    public void setPodName(String podName) {
        this.podName = podName;
    }

    public String getFreightPaidWayCode() {
        return freightPaidWayCode;
    }

    public void setFreightPaidWayCode(String freightPaidWayCode) {
        this.freightPaidWayCode = freightPaidWayCode;
    }

    public String getBookingChargeRemark() {
        return bookingChargeRemark;
    }

    public void setBookingChargeRemark(String bookingChargeRemark) {
        this.bookingChargeRemark = bookingChargeRemark;
    }

    public String getCtnrTypeCode() {
        return ctnrTypeCode;
    }

    public void setCtnrTypeCode(String ctnrTypeCode) {
        this.ctnrTypeCode = ctnrTypeCode;
    }

    public String getCargoTypeCodeSum() {
        return cargoTypeCodeSum;
    }

    public void setCargoTypeCodeSum(String cargoTypeCodeSum) {
        this.cargoTypeCodeSum = cargoTypeCodeSum;
    }

    public String getSqdDnReceiveSlipStatus() {
        return sqdDnReceiveSlipStatus;
    }

    public void setSqdDnReceiveSlipStatus(String sqdDnReceiveSlipStatus) {
        this.sqdDnReceiveSlipStatus = sqdDnReceiveSlipStatus;
    }

    public String getSqdDnPaySlipStatus() {
        return sqdDnPaySlipStatus;
    }

    public void setSqdDnPaySlipStatus(String sqdDnPaySlipStatus) {
        this.sqdDnPaySlipStatus = sqdDnPaySlipStatus;
    }

    public String getPaymentNode() {
        return paymentNode;
    }

    public void setPaymentNode(String paymentNode) {
        this.paymentNode = paymentNode;
    }

    public Long getRsPsaId() {
        return rsPsaId;
    }

    public void setRsPsaId(Long rsPsaId) {
        this.rsPsaId = rsPsaId;
    }

    public String getSqdPsaNo() {
        return sqdPsaNo;
    }

    public void setSqdPsaNo(String sqdPsaNo) {
        this.sqdPsaNo = sqdPsaNo;
    }

    public Long getPsaRctId() {
        return psaRctId;
    }

    public void setPsaRctId(Long psaRctId) {
        this.psaRctId = psaRctId;
    }

    public String getOpAskingBlGetTime() {
        return opAskingBlGetTime;
    }

    public void setOpAskingBlGetTime(String opAskingBlGetTime) {
        this.opAskingBlGetTime = opAskingBlGetTime;
    }

    public String getOpAskingBlReleaseTime() {
        return opAskingBlReleaseTime;
    }

    public void setOpAskingBlReleaseTime(String opAskingBlReleaseTime) {
        this.opAskingBlReleaseTime = opAskingBlReleaseTime;
    }

    public String getAccPromissBlGetTime() {
        return accPromissBlGetTime;
    }

    public void setAccPromissBlGetTime(String accPromissBlGetTime) {
        this.accPromissBlGetTime = accPromissBlGetTime;
    }

    public String getActualBlGotTime() {
        return actualBlGotTime;
    }

    public void setActualBlGotTime(String actualBlGotTime) {
        this.actualBlGotTime = actualBlGotTime;
    }

    public String getAccPromissBlReleaseTime() {
        return accPromissBlReleaseTime;
    }

    public void setAccPromissBlReleaseTime(String accPromissBlReleaseTime) {
        this.accPromissBlReleaseTime = accPromissBlReleaseTime;
    }

    public String getActualBlReleaseTime() {
        return actualBlReleaseTime;
    }

    public void setActualBlReleaseTime(String actualBlReleaseTime) {
        this.actualBlReleaseTime = actualBlReleaseTime;
    }

    public String getAgentNoticeTime() {
        return agentNoticeTime;
    }

    public void setAgentNoticeTime(String agentNoticeTime) {
        this.agentNoticeTime = agentNoticeTime;
    }

    public Date getBookingStatus() {
        return bookingStatus;
    }

    public void setBookingStatus(Date bookingStatus) {
        this.bookingStatus = bookingStatus;
    }

    public String getSecondVessel() {
        return secondVessel;
    }

    public void setSecondVessel(String secondVessel) {
        this.secondVessel = secondVessel;
    }

    public Date[] getRctOpDate() {
        return rctOpDate;
    }

    public void setRctOpDate(Date[] rctOpDate) {
        this.rctOpDate = rctOpDate;
    }

    public Date[] getETDDate() {
        return ETDDate;
    }

    public void setETDDate(Date[] ETDDate) {
        this.ETDDate = ETDDate;
    }

    public Date[] getATDDate() {
        return ATDDate;
    }

    public void setATDDate(Date[] ATDDate) {
        this.ATDDate = ATDDate;
    }

    public BigDecimal getQuotationInRmb() {
        return quotationInRmb;
    }

    public void setQuotationInRmb(BigDecimal quotationInRmb) {
        this.quotationInRmb = quotationInRmb;
    }

    public BigDecimal getInquiryInRmb() {
        return inquiryInRmb;
    }

    public void setInquiryInRmb(BigDecimal inquiryInRmb) {
        this.inquiryInRmb = inquiryInRmb;
    }

    public BigDecimal getEstimatedProfitInRmb() {
        return estimatedProfitInRmb;
    }

    public void setEstimatedProfitInRmb(BigDecimal estimatedProfitInRmb) {
        this.estimatedProfitInRmb = estimatedProfitInRmb;
    }

    public BigDecimal getDnUsd() {
        return dnUsd;
    }

    public void setDnUsd(BigDecimal dnUsd) {
        this.dnUsd = dnUsd;
    }

    public BigDecimal getDnRmb() {
        return dnRmb;
    }

    public void setDnRmb(BigDecimal dnRmb) {
        this.dnRmb = dnRmb;
    }

    public BigDecimal getDnUsdBalance() {
        return dnUsdBalance;
    }

    public void setDnUsdBalance(BigDecimal dnUsdBalance) {
        this.dnUsdBalance = dnUsdBalance;
    }

    public BigDecimal getDnRmbBalance() {
        return dnRmbBalance;
    }

    public void setDnRmbBalance(BigDecimal dnRmbBalance) {
        this.dnRmbBalance = dnRmbBalance;
    }

    public BigDecimal getDnInRmb() {
        return dnInRmb;
    }

    public void setDnInRmb(BigDecimal dnInRmb) {
        this.dnInRmb = dnInRmb;
    }

    public BigDecimal getDnInRmbBalance() {
        return dnInRmbBalance;
    }

    public void setDnInRmbBalance(BigDecimal dnInRmbBalance) {
        this.dnInRmbBalance = dnInRmbBalance;
    }

    public BigDecimal getCnUsd() {
        return cnUsd;
    }

    public void setCnUsd(BigDecimal cnUsd) {
        this.cnUsd = cnUsd;
    }

    public BigDecimal getCnRmb() {
        return cnRmb;
    }

    public void setCnRmb(BigDecimal cnRmb) {
        this.cnRmb = cnRmb;
    }

    public BigDecimal getCnUsdBalance() {
        return cnUsdBalance;
    }

    public void setCnUsdBalance(BigDecimal cnUsdBalance) {
        this.cnUsdBalance = cnUsdBalance;
    }

    public BigDecimal getCnRmbBalance() {
        return cnRmbBalance;
    }

    public void setCnRmbBalance(BigDecimal cnRmbBalance) {
        this.cnRmbBalance = cnRmbBalance;
    }

    public BigDecimal getCnInRmb() {
        return cnInRmb;
    }

    public void setCnInRmb(BigDecimal cnInRmb) {
        this.cnInRmb = cnInRmb;
    }

    public BigDecimal getCnInRmbBalance() {
        return cnInRmbBalance;
    }

    public void setCnInRmbBalance(BigDecimal cnInRmbBalance) {
        this.cnInRmbBalance = cnInRmbBalance;
    }

    public BigDecimal getProfitUsd() {
        return profitUsd;
    }

    public void setProfitUsd(BigDecimal profitUsd) {
        this.profitUsd = profitUsd;
    }

    public BigDecimal getProfitRmb() {
        return profitRmb;
    }

    public void setProfitRmb(BigDecimal profitRmb) {
        this.profitRmb = profitRmb;
    }

    public BigDecimal getProfitInRmb() {
        return profitInRmb;
    }

    public void setProfitInRmb(BigDecimal profitInRmb) {
        this.profitInRmb = profitInRmb;
    }

    public BigDecimal getDifferenceInRmb() {
        return differenceInRmb;
    }

    public void setDifferenceInRmb(BigDecimal differenceInRmb) {
        this.differenceInRmb = differenceInRmb;
    }

    public String getIsCustomsIntransitShowed() {
        return isCustomsIntransitShowed;
    }

    public void setIsCustomsIntransitShowed(String isCustomsIntransitShowed) {
        this.isCustomsIntransitShowed = isCustomsIntransitShowed;
    }
}
