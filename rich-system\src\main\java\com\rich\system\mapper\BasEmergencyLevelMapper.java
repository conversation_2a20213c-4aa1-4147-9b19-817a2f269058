package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasEmergencyLevel;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/23 17:46
 * @Version 1.0
 */
@Mapper
public interface BasEmergencyLevelMapper {
    /**
     * 查询紧急程度
     *
     * @param emergencyLevelId 紧急程度主键
     * @return 紧急程度
     */
    BasEmergencyLevel selectBasEmergencyLevelByEmergencyLevelId(Long emergencyLevelId);

    /**
     * 查询紧急程度列表
     *
     * @param basEmergencyLevel 紧急程度
     * @return 紧急程度集合
     */
    List<BasEmergencyLevel> selectBasEmergencyLevelList(BasEmergencyLevel basEmergencyLevel);

    /**
     * 新增紧急程度
     *
     * @param basEmergencyLevel 紧急程度
     * @return 结果
     */
    int insertBasEmergencyLevel(BasEmergencyLevel basEmergencyLevel);

    /**
     * 修改紧急程度
     *
     * @param basEmergencyLevel 紧急程度
     * @return 结果
     */
    int updateBasEmergencyLevel(BasEmergencyLevel basEmergencyLevel);

    /**
     * 删除紧急程度
     *
     * @param emergencyLevelId 紧急程度主键
     * @return 结果
     */
    int deleteBasEmergencyLevelByEmergencyLevelId(Long emergencyLevelId);

    /**
     * 批量删除紧急程度
     *
     * @param emergencyLevelIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasEmergencyLevelByEmergencyLevelIds(Long[] emergencyLevelIds);
}
