package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsCargoDetails;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsCargoDetailsMapper;
import com.rich.system.service.RsCargoDetailsService;

/**
 * 客户在仓货物明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-11
 */
@Service
public class RsCargoDetailsServiceImpl implements RsCargoDetailsService {
    @Autowired
    private RsCargoDetailsMapper rsCargoDetailsMapper;

    /**
     * 查询客户在仓货物明细
     *
     * @param cargoDetailsId 客户在仓货物明细主键
     * @return 客户在仓货物明细
     */
    @Override
    public RsCargoDetails selectRsCargoDetailsByCargoDetailsId(Long cargoDetailsId) {
        return rsCargoDetailsMapper.selectRsCargoDetailsByCargoDetailsId(cargoDetailsId);
    }

    /**
     * 查询客户在仓货物明细列表
     *
     * @param rsCargoDetails 客户在仓货物明细
     * @return 客户在仓货物明细
     */
    @Override
    public List<RsCargoDetails> selectRsCargoDetailsList(RsCargoDetails rsCargoDetails) {
        return rsCargoDetailsMapper.selectRsCargoDetailsList(rsCargoDetails);
    }

    /**
     * 新增客户在仓货物明细
     *
     * @param rsCargoDetails 客户在仓货物明细
     * @return 结果
     */
    @Override
    public int insertRsCargoDetails(RsCargoDetails rsCargoDetails) {
        return rsCargoDetailsMapper.insertRsCargoDetails(rsCargoDetails);
    }

    /**
     * 修改客户在仓货物明细
     *
     * @param rsCargoDetails 客户在仓货物明细
     * @return 结果
     */
    @Override
    public int updateRsCargoDetails(RsCargoDetails rsCargoDetails) {
        return rsCargoDetailsMapper.updateRsCargoDetails(rsCargoDetails);
    }

    /**
     * 修改客户在仓货物明细状态
     *
     * @param rsCargoDetails 客户在仓货物明细
     * @return 客户在仓货物明细
     */
    @Override
    public int changeStatus(RsCargoDetails rsCargoDetails) {
        return rsCargoDetailsMapper.updateRsCargoDetails(rsCargoDetails);
    }

    /**
     * 批量删除客户在仓货物明细
     *
     * @param cargoDetailsIds 需要删除的客户在仓货物明细主键
     * @return 结果
     */
    @Override
    public int deleteRsCargoDetailsByCargoDetailsIds(Long[] cargoDetailsIds) {
        return rsCargoDetailsMapper.deleteRsCargoDetailsByCargoDetailsIds(cargoDetailsIds);
    }

    /**
     * 删除客户在仓货物明细信息
     *
     * @param cargoDetailsId 客户在仓货物明细主键
     * @return 结果
     */
    @Override
    public int deleteRsCargoDetailsByCargoDetailsId(Long cargoDetailsId) {
        return rsCargoDetailsMapper.deleteRsCargoDetailsByCargoDetailsId(cargoDetailsId);
    }
}
