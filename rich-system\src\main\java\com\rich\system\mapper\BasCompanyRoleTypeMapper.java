package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasCompanyRoleType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 公司类型Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-07
 */
@Mapper
public interface BasCompanyRoleTypeMapper {
    /**
     * 查询公司类型
     *
     * @param roleTypeId 公司类型主键
     * @return 公司类型
     */
    BasCompanyRoleType selectBasCompanyRoleTypeByRoleTypeId(Long roleTypeId);

    /**
     * 查询公司类型列表
     *
     * @param basCompanyRoleType 公司类型
     * @return 公司类型集合
     */
    List<BasCompanyRoleType> selectBasCompanyRoleTypeList(BasCompanyRoleType basCompanyRoleType);

    /**
     * 新增公司类型
     *
     * @param basCompanyRoleType 公司类型
     * @return 结果
     */
    int insertBasCompanyRoleType(BasCompanyRoleType basCompanyRoleType);

    /**
     * 修改公司类型
     *
     * @param basCompanyRoleType 公司类型
     * @return 结果
     */
    int updateBasCompanyRoleType(BasCompanyRoleType basCompanyRoleType);

    /**
     * 删除公司类型
     *
     * @param roleTypeId 公司类型主键
     * @return 结果
     */
    int deleteBasCompanyRoleTypeByRoleTypeId(Long roleTypeId);

    /**
     * 批量删除公司类型
     *
     * @param roleTypeIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasCompanyRoleTypeByRoleTypeIds(Long[] roleTypeIds);
}
