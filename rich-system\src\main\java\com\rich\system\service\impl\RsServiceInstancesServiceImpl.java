package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsServiceInstances;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsServiceInstancesMapper;
import com.rich.system.service.RsServiceInstancesService;

/**
 * 服务实例，记录着每一个订舱中的各种服务Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@Service
public class RsServiceInstancesServiceImpl implements RsServiceInstancesService {
    @Autowired
    private RsServiceInstancesMapper rsServiceInstancesMapper;

    /**
     * 查询服务实例，记录着每一个订舱中的各种服务
     *
     * @param serviceId 服务实例，记录着每一个订舱中的各种服务主键
     * @return 服务实例，记录着每一个订舱中的各种服务
     */
    @Override
    public RsServiceInstances selectRsServiceInstancesByServiceId(Long serviceId) {
        return rsServiceInstancesMapper.selectRsServiceInstancesByServiceId(serviceId);
    }

    /**
     * 查询服务实例，记录着每一个订舱中的各种服务列表
     *
     * @param rsServiceInstances 服务实例，记录着每一个订舱中的各种服务
     * @return 服务实例，记录着每一个订舱中的各种服务
     */
    @Override
    public List<RsServiceInstances> selectRsServiceInstancesList(RsServiceInstances rsServiceInstances) {
        return rsServiceInstancesMapper.selectRsServiceInstancesList(rsServiceInstances);
    }

    /**
     * 新增服务实例，记录着每一个订舱中的各种服务
     *
     * @param rsServiceInstances 服务实例，记录着每一个订舱中的各种服务
     * @return 结果
     */
    @Override
    public int insertRsServiceInstances(RsServiceInstances rsServiceInstances) {
        return rsServiceInstancesMapper.insertRsServiceInstances(rsServiceInstances);
    }

    /**
     * 修改服务实例，记录着每一个订舱中的各种服务
     *
     * @param rsServiceInstances 服务实例，记录着每一个订舱中的各种服务
     * @return 结果
     */
    @Override
    public int updateRsServiceInstances(RsServiceInstances rsServiceInstances) {
        return rsServiceInstancesMapper.updateRsServiceInstances(rsServiceInstances);
    }

    /**
     * 修改服务实例，记录着每一个订舱中的各种服务状态
     *
     * @param rsServiceInstances 服务实例，记录着每一个订舱中的各种服务
     * @return 服务实例，记录着每一个订舱中的各种服务
     */
    @Override
    public int changeStatus(RsServiceInstances rsServiceInstances) {
        return rsServiceInstancesMapper.updateRsServiceInstances(rsServiceInstances);
    }

    /**
     * 批量删除服务实例，记录着每一个订舱中的各种服务
     *
     * @param serviceIds 需要删除的服务实例，记录着每一个订舱中的各种服务主键
     * @return 结果
     */
    @Override
    public int deleteRsServiceInstancesByServiceIds(Long[] serviceIds) {
        return rsServiceInstancesMapper.deleteRsServiceInstancesByServiceIds(serviceIds);
    }

    /**
     * 删除服务实例，记录着每一个订舱中的各种服务信息
     *
     * @param serviceId 服务实例，记录着每一个订舱中的各种服务主键
     * @return 结果
     */
    @Override
    public int deleteRsServiceInstancesByServiceId(Long serviceId) {
        return rsServiceInstancesMapper.deleteRsServiceInstancesByServiceId(serviceId);
    }
}
