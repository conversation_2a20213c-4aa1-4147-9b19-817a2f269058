package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasDocReleaseWay;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasDocReleaseWayService;
import com.rich.system.service.impl.RedisCacheImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 交单方式Controller
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@RestController
@RequestMapping("/system/docreleaseway")
public class BasDocReleaseWayController extends BaseController {
    @Autowired
    private BasDocReleaseWayService basDocReleaseWayService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisCacheImpl RedisCache;

    /**
     * 查询交单方式列表
     */
    @PreAuthorize("@ss.hasPermi('system:docreleaseway:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasDocReleaseWay basDocReleaseWay) {
        startPage();
        List<BasDocReleaseWay> list = basDocReleaseWayService.selectBasDocReleaseWayList(basDocReleaseWay);
        return getDataTable(list);
    }

    @GetMapping("/selectList")
    public AjaxResult selectList() {
        List<BasDocReleaseWay> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "docReleaseWay");
        if (list == null) {
            RedisCache.docReleaseWay();
            list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "docReleaseWay");
        }
        return AjaxResult.success(list);
    }

    /**
     * 导出交单方式列表
     */
    @PreAuthorize("@ss.hasPermi('system:docreleaseway:export')")
    @Log(title = "交单方式", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasDocReleaseWay basDocReleaseWay) {
        List<BasDocReleaseWay> list = basDocReleaseWayService.selectBasDocReleaseWayList(basDocReleaseWay);
        ExcelUtil<BasDocReleaseWay> util = new ExcelUtil<BasDocReleaseWay>(BasDocReleaseWay.class);
        util.exportExcel(response, list, "交单方式数据");
    }

    /**
     * 获取交单方式详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:docreleaseway:query')")
    @GetMapping(value = "/{releaseWayId}")
    public AjaxResult getInfo(@PathVariable("releaseWayId") Long releaseWayId) {
        return AjaxResult.success(basDocReleaseWayService.selectBasDocReleaseWayByReleaseWayId(releaseWayId));
    }

    /**
     * 新增交单方式
     */
    @PreAuthorize("@ss.hasPermi('system:docreleaseway:add')")
    @Log(title = "交单方式", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasDocReleaseWay basDocReleaseWay) {
        return toAjax(basDocReleaseWayService.insertBasDocReleaseWay(basDocReleaseWay));
    }

    /**
     * 修改交单方式
     */
    @PreAuthorize("@ss.hasPermi('system:docreleaseway:edit')")
    @Log(title = "交单方式", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasDocReleaseWay basDocReleaseWay) {
        return toAjax(basDocReleaseWayService.updateBasDocReleaseWay(basDocReleaseWay));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:docreleaseway:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasDocReleaseWay basDocReleaseWay) {
        basDocReleaseWay.setUpdateBy(getUserId());
        return toAjax(basDocReleaseWayService.changeStatus(basDocReleaseWay));
    }

    /**
     * 删除交单方式
     */
    @PreAuthorize("@ss.hasPermi('system:docreleaseway:remove')")
    @Log(title = "交单方式", businessType = BusinessType.DELETE)
    @DeleteMapping("/{releaseWayIds}")
    public AjaxResult remove(@PathVariable Long[] releaseWayIds) {
        return toAjax(basDocReleaseWayService.deleteBasDocReleaseWayByReleaseWayIds(releaseWayIds));
    }
}
