package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasCompanyRole;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasCompanyRoleService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 公司角色Controller
 *
 * <AUTHOR>
 * @date 2022-09-06
 */
@RestController
@RequestMapping("/system/companyrole")
public class BasCompanyRoleController extends BaseController {

    @Autowired
    private BasCompanyRoleService basCompanyRoleService;


    @Autowired
    private RedisCache redisCache;

    /**
     * 查询公司角色列表
     */
    @PreAuthorize("@ss.hasPermi('system:companyrole:list')")
    @GetMapping("/list")
    public AjaxResult list(BasCompanyRole basCompanyRole) {
        List<BasCompanyRole> list = basCompanyRoleService.selectBasCompanyRoleList(basCompanyRole);
        return AjaxResult.success(list);
    }

    @GetMapping("/selectList")
    public AjaxResult selectList(BasCompanyRole basCompanyRole) {
        List<BasCompanyRole> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "companyRole");
        if (list == null) {
            basCompanyRole.setStatus("0");
            list = basCompanyRoleService.selectBasCompanyRoleList(basCompanyRole);
            redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "companyRole");
            redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "companyRole", list);
        }
        return AjaxResult.success(list);
    }

    /**
     * 导出公司角色列表
     */
    @PreAuthorize("@ss.hasPermi('system:companyrole:export')")
    @Log(title = "公司角色", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasCompanyRole basCompanyRole) {
        List<BasCompanyRole> list = basCompanyRoleService.selectBasCompanyRoleList(basCompanyRole);
        ExcelUtil<BasCompanyRole> util = new ExcelUtil<BasCompanyRole>(BasCompanyRole.class);
        util.exportExcel(response, list, "公司角色数据");
    }

    /**
     * 获取公司角色详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:companyrole:edit')")
    @GetMapping(value = "/{roleId}")
    public AjaxResult getInfo(@PathVariable("roleId") Long roleId) {
        return AjaxResult.success(basCompanyRoleService.selectBasCompanyRoleByRoleId(roleId));
    }

    /**
     * 新增公司角色
     */
    @PreAuthorize("@ss.hasPermi('system:companyrole:add')")
    @Log(title = "公司角色", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasCompanyRole basCompanyRole) {
        int out = basCompanyRoleService.insertBasCompanyRole(basCompanyRole);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "companyRole");
        return toAjax(out);
    }

    /**
     * 修改公司角色
     */
    @PreAuthorize("@ss.hasPermi('system:companyrole:edit')")
    @Log(title = "公司角色", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasCompanyRole basCompanyRole) {
        int out = basCompanyRoleService.updateBasCompanyRole(basCompanyRole);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "companyRole");
        return toAjax(out);
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('system:companyrole:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasCompanyRole basCompanyRole) {
        basCompanyRole.setUpdateBy(getUserId());
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "companyRole");
        return toAjax(basCompanyRoleService.updateBasCompanyRole(basCompanyRole));
    }

    /**
     * 删除公司角色
     */
    @PreAuthorize("@ss.hasPermi('system:companyrole:remove')")
    @Log(title = "公司角色", businessType = BusinessType.DELETE)
    @DeleteMapping("/{roleIds}")
    public AjaxResult remove(@PathVariable Long[] roleIds) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "companyRole");
        return toAjax(basCompanyRoleService.deleteBasCompanyRoleByRoleIds(roleIds));
    }
}
