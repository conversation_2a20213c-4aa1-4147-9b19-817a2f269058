---
description:
globs:
alwaysApply: false
---
# 常见编码模式与实现规范

本文档整理项目中常见的设计模式和实现规范，以便在开发中保持一致性。

## 后端设计模式与实现

### 实体类规范
```java
@Data
@NoArgsConstructor
@TableName("sys_user")
public class SysUser extends BaseEntity {
    /** 用户ID */
    @TableId
    private Long userId;
    
    /** 用户名 */
    private String userName;
    
    /** 密码 */
    private String password;
    
    // 其他字段...
}
```

### 控制器实现模式
```java
@RestController
@RequestMapping("/system/user")
public class SysUserController {
    @Autowired
    private SysUserService userService;
    
    /**
     * 获取用户列表
     */
    @GetMapping("/list")
    public R<PageInfo<SysUser>> list(SysUser user, PageQuery pageQuery) {
        startPage(pageQuery);
        List<SysUser> list = userService.selectUserList(user);
        return R.success(getPageInfo(list));
    }
    
    /**
     * 新增用户
     */
    @PostMapping
    public R<Void> add(@RequestBody @Validated SysUser user) {
        userService.insertUser(user);
        return R.success();
    }
    
    // 其他方法...
}
```

### 服务层实现模式
```java
@Service
public class SysUserServiceImpl implements SysUserService {
    @Autowired
    private SysUserMapper userMapper;
    
    /**
     * 查询用户列表
     */
    @Override
    public List<SysUser> selectUserList(SysUser user) {
        return userMapper.selectUserList(user);
    }
    
    /**
     * 新增用户
     */
    @Override
    @Transactional
    public void insertUser(SysUser user) {
        // 业务逻辑处理
        // ...
        userMapper.insert(user);
    }
    
    // 其他方法...
}
```

### Mapper实现模式
```java
@Mapper
public interface SysUserMapper {
    /**
     * 查询用户列表
     */
    @Select("<script>SELECT * FROM sys_user WHERE del_flag = '0'" +
            "<if test=\"userName != null and userName != ''\"> AND user_name like concat('%', #{userName}, '%')</if>" +
            "</script>")
    List<SysUser> selectUserList(SysUser user);
    
    /**
     * 新增用户
     */
    int insert(SysUser user);
}
```

## 前端实现模式

### 页面组件结构
```vue
<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <!-- 表单项 -->
    </el-form>
    
    <!-- 操作区域 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" @click="handleAdd">新增</el-button>
      </el-col>
    </el-row>
    
    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="dataList">
      <!-- 表格列 -->
    </el-table>
    
    <!-- 分页组件 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    
    <!-- 弹窗组件 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <!-- 表单内容 -->
    </el-dialog>
  </div>
</template>

<script>
import { listUsers, getUser, addUser } from "@/api/system/user";

export default {
  name: "User",
  data() {
    return {
      // 加载状态
      loading: false,
      // 总条数
      total: 0,
      // 数据列表
      dataList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: undefined
      },
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 获取数据列表
    getList() {
      this.loading = true;
      listUsers(this.queryParams).then(response => {
        this.dataList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 新增按钮操作
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加用户";
    },
    // 重置表单
    reset() {
      this.form = {};
    }
    // 其他方法...
  }
};
</script>
```

### API请求模式
```js
import request from '@/utils/request'

// 查询用户列表
export function listUsers(query) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: query
  })
}

// 新增用户
export function addUser(data) {
  return request({
    url: '/system/user',
    method: 'post',
    data: data
  })
}
```

## 移动端实现模式

### 页面结构
```vue
<template>
  <view class="content">
    <!-- 页面内容 -->
    <view class="search-box">
      <uni-search-bar @confirm="search" v-model="keyword" />
    </view>
    
    <view class="list-box">
      <uni-list>
        <uni-list-item 
          v-for="(item, index) in dataList" 
          :key="index" 
          :title="item.title"
          @click="handleClick(item)"
        />
      </uni-list>
    </view>
    
    <!-- 加载更多 -->
    <uni-load-more :status="loadMoreStatus" />
  </view>
</template>

<script>
import { getList } from '@/api/module'

export default {
  data() {
    return {
      keyword: '',
      pageNum: 1,
      pageSize: 10,
      dataList: [],
      total: 0,
      loadMoreStatus: 'more'
    }
  },
  onLoad() {
    this.getList()
  },
  // 上拉加载
  onReachBottom() {
    if (this.dataList.length < this.total) {
      this.pageNum++
      this.loadMoreStatus = 'loading'
      this.getList()
    } else {
      this.loadMoreStatus = 'noMore'
    }
  },
  // 下拉刷新
  onPullDownRefresh() {
    this.pageNum = 1
    this.dataList = []
    this.getList()
  },
  methods: {
    getList() {
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        keyword: this.keyword
      }
      
      getList(params).then(res => {
        if (this.pageNum === 1) {
          this.dataList = res.data.list
        } else {
          this.dataList = [...this.dataList, ...res.data.list]
        }
        this.total = res.data.total
        
        this.loadMoreStatus = this.dataList.length >= this.total ? 'noMore' : 'more'
        uni.stopPullDownRefresh()
      })
    },
    search() {
      this.pageNum = 1
      this.dataList = []
      this.getList()
    },
    handleClick(item) {
      uni.navigateTo({
        url: `/pages/detail/detail?id=${item.id}`
      })
    }
  }
}
</script>

<style lang="scss">
.content {
  padding: 20rpx;
}
</style>
```

### 请求封装
```js
// utils/request.js
import { getToken } from './auth'
import { toast } from './common'

const baseURL = process.env.BASE_URL || 'http://localhost:8080'

const request = (options = {}) => {
  return new Promise((resolve, reject) => {
    // 请求拦截
    options.url = baseURL + options.url
    options.header = {
      'Authorization': getToken() || '',
      'Content-Type': 'application/json'
    }
    
    // 发起请求
    uni.request({
      ...options,
      success: (res) => {
        if (res.statusCode === 200) {
          if (res.data.code === 200) {
            resolve(res.data)
          } else {
            toast(res.data.msg || '请求失败')
            reject(res.data)
          }
        } else if (res.statusCode === 401) {
          // 未授权处理
          uni.navigateTo({
            url: '/pages/login/login'
          })
          reject(res.data)
        } else {
          toast('服务器异常')
          reject(res.data)
        }
      },
      fail: (err) => {
        toast('网络异常')
        reject(err)
      }
    })
  })
}

export default request
