package com.rich.common.config;

import com.rich.common.utils.CanalUtil;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2023/5/29 17:01
 * @Version 1.0
 */
//@Configuration
public class InitCanalConfig implements CommandLineRunner {

    @Resource
    private CanalUtil canalUtil;


    @Override
    public void run(String... args) {
        canalUtil.run();
    }
}
