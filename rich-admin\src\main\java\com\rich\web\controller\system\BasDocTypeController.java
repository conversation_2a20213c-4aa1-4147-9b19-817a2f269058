package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.BasDocType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.BasDocTypeService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 *
 * <AUTHOR>
 * @date 2024-04-08
 */
@RestController
@RequestMapping("/system/doctype")
public class BasDocTypeController extends BaseController {
    @Autowired
    private BasDocTypeService basDocTypeService;

    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:doctype:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasDocType basDocType) {
        startPage();
        List<BasDocType> list = basDocTypeService.selectBasDocTypeList(basDocType);
        return getDataTable(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:doctype:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasDocType basDocType) {
        List<BasDocType> list = basDocTypeService.selectBasDocTypeList(basDocType);
        ExcelUtil<BasDocType> util = new ExcelUtil<BasDocType>(BasDocType.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:doctype:query')")
    @GetMapping(value = "/{docTypeCode}")
    public AjaxResult getInfo(@PathVariable("docTypeCode") String docTypeCode) {
        return AjaxResult.success(basDocTypeService.selectBasDocTypeByDocTypeCode(docTypeCode));
    }

    /**
     * 新增【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:doctype:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasDocType basDocType) {
        return toAjax(basDocTypeService.insertBasDocType(basDocType));
    }

    /**
     * 修改【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:doctype:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasDocType basDocType) {
        return toAjax(basDocTypeService.updateBasDocType(basDocType));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:doctype:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasDocType basDocType) {
        basDocType.setUpdateBy(getUserId());
        return toAjax(basDocTypeService.changeStatus(basDocType));
    }

    /**
     * 删除【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:doctype:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{docTypeCodes}")
    public AjaxResult remove(@PathVariable String[] docTypeCodes) {
        return toAjax(basDocTypeService.deleteBasDocTypeByDocTypeCodes(docTypeCodes));
    }
}
