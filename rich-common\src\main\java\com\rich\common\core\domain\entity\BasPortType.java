package com.rich.common.core.domain.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 【请填写功能名称】对象 bas_port_type
 * 
 * <AUTHOR>
 * @date 2022-08-29
 */
public class BasPortType extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 港口类型 */
    private Long portTypeId;

    /** 港口类名缩写 */
    @Excel(name = "港口类名缩写")
    private String portTypeShortName;

    /** 港口类型中文名 */
    @Excel(name = "港口类型中文名")
    private String portTypeLocalName;

    /** 港口类型英文名 */
    @Excel(name = "港口类型英文名")
    private String portTypeEnName;

    private String status;

    private String portTypeQuery;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPortTypeQuery() {
        return portTypeQuery;
    }

    public void setPortTypeQuery(String portTypeQuery) {
        this.portTypeQuery = portTypeQuery;
    }

    public void setPortTypeId(Long portTypeId)
    {
        this.portTypeId = portTypeId;
    }

    public Long getPortTypeId() 
    {
        return portTypeId;
    }
    public void setPortTypeShortName(String portTypeShortName) 
    {
        this.portTypeShortName = portTypeShortName;
    }

    public String getPortTypeShortName() 
    {
        return portTypeShortName;
    }
    public void setPortTypeLocalName(String portTypeLocalName) 
    {
        this.portTypeLocalName = portTypeLocalName;
    }

    public String getPortTypeLocalName() 
    {
        return portTypeLocalName;
    }
    public void setPortTypeEnName(String portTypeEnName) 
    {
        this.portTypeEnName = portTypeEnName;
    }

    public String getPortTypeEnName() 
    {
        return portTypeEnName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("portTypeId", getPortTypeId())
            .append("portTypeShortName", getPortTypeShortName())
            .append("portTypeLocalName", getPortTypeLocalName())
            .append("portTypeEnName", getPortTypeEnName())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("deleteBy", getDeleteBy())
            .append("deleteTime", getDeleteTime())
            .append("deleteStatus", getDeleteStatus())
            .toString();
    }
}
