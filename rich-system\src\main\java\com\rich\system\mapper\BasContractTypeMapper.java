package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasContractType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 合约类别Mapper接口
 *
 * <AUTHOR>
 * @date 2023-01-13
 */
@Mapper
public interface BasContractTypeMapper {
    /**
     * 查询合约类别
     *
     * @param contractTypeId 合约类别主键
     * @return 合约类别
     */
    BasContractType selectBasContractTypeByContractTypeId(Long contractTypeId);

    /**
     * 查询合约类别列表
     *
     * @param basContractType 合约类别
     * @return 合约类别集合
     */
    List<BasContractType> selectBasContractTypeList(BasContractType basContractType);

    /**
     * 新增合约类别
     *
     * @param basContractType 合约类别
     * @return 结果
     */
    int insertBasContractType(BasContractType basContractType);

    /**
     * 修改合约类别
     *
     * @param basContractType 合约类别
     * @return 结果
     */
    int updateBasContractType(BasContractType basContractType);

    /**
     * 删除合约类别
     *
     * @param contractTypeId 合约类别主键
     * @return 结果
     */
    int deleteBasContractTypeByContractTypeId(Long contractTypeId);

    /**
     * 批量删除合约类别
     *
     * @param contractTypeIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasContractTypeByContractTypeIds(Long[] contractTypeIds);
}
