package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsOpSeaFcl;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 整柜海运服务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Mapper
public interface RsOpSeaFclMapper {
    /**
     * 查询整柜海运服务
     *
     * @param seaFclId 整柜海运服务主键
     * @return 整柜海运服务
     */
    RsOpSeaFcl selectRsOpSeaFclBySeaFclId(Long seaFclId);

    /**
     * 查询整柜海运服务列表
     *
     * @param rsOpSeaFcl 整柜海运服务
     * @return 整柜海运服务集合
     */
    List<RsOpSeaFcl> selectRsOpSeaFclList(RsOpSeaFcl rsOpSeaFcl);

    /**
     * 新增整柜海运服务
     *
     * @param rsOpSeaFcl 整柜海运服务
     * @return 结果
     */
    int insertRsOpSeaFcl(RsOpSeaFcl rsOpSeaFcl);

    /**
     * 修改整柜海运服务
     *
     * @param rsOpSeaFcl 整柜海运服务
     * @return 结果
     */
    int updateRsOpSeaFcl(RsOpSeaFcl rsOpSeaFcl);

    /**
     * 删除整柜海运服务
     *
     * @param seaFclId 整柜海运服务主键
     * @return 结果
     */
    int deleteRsOpSeaFclBySeaFclId(Long seaFclId);

    /**
     * 批量删除整柜海运服务
     *
     * @param seaFclIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsOpSeaFclBySeaFclIds(Long[] seaFclIds);

    List<RsOpSeaFcl> selectRsOpSeaFclByRctId(@Param("rctId") Long rctId, @Param("sqdServiceTypeId") Long sqdServiceTypeId);

    void deleteRsOpSeaFclByRctIdAndServiceTypeId(@Param("rctId") Long rctId, @Param("sqdServiceTypeId") long l);

    List<RsOpSeaFcl> selectRsPsaRctList(RsOpSeaFcl rsOpSeaFcl);

    RsOpSeaFcl selectRsPsaRctBySeaId(Long psaRctId);

    void upsertRsOpSeaFcl(RsOpSeaFcl rsOpSeaFcl);

    int updateRsOpSeaFclByRctId(RsOpSeaFcl rsOpSeaFcl);
}
