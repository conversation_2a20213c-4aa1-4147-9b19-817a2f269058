package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.RsOpSeaFcl;
import com.rich.common.core.domain.entity.RsPsaRct;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasDistLocationService;
import com.rich.system.service.ExtCompanyService;
import com.rich.system.service.RsOpSeaFclService;
import com.rich.system.service.RsPsaRctService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 商务订舱Controller
 *
 * <AUTHOR>
 * @date 2024-07-01
 */
@RestController
@RequestMapping("/system/psarct")
public class RsPsaRctController extends BaseController {
    @Autowired
    private RsPsaRctService rsPsaRctService;

    @Resource
    private RsOpSeaFclService rsOpSeaFclService;

    @Autowired
    private BasDistLocationService basDistLocationService;

    @Autowired
    private ExtCompanyService extCompanyService;

    /**
     * 查询商务订舱列表
     */
    @PreAuthorize("@ss.hasPermi('system:psarct:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsOpSeaFcl rsOpSeaFcl) {
        startPage();
        List<RsOpSeaFcl> list = rsOpSeaFclService.selectRsPsaRctList(rsOpSeaFcl);
        return getDataTable(list);
    }

    /**
     * 导出商务订舱列表 hasAnyPermi('system:account:list,system:user:list')
     */
    @PreAuthorize("@ss.hasPermi('system:psarct:export')")
    @Log(title = "商务订舱", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsPsaRct rsPsaRct) {
        List<RsPsaRct> list = rsPsaRctService.selectRsPsaRctList(rsPsaRct);
        ExcelUtil<RsPsaRct> util = new ExcelUtil<RsPsaRct>(RsPsaRct.class);
        util.exportExcel(response, list, "商务订舱数据");
    }

    /**
     * 获取商务订舱详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:psarct:query')")
    @GetMapping(value = "/{seaId}")
    public AjaxResult getInfo(@PathVariable("seaId") Long seaId) {
        RsOpSeaFcl rsOpSeaFcl = rsOpSeaFclService.selectRsPsaRctBySeaId(seaId);
        Set<Long> set = new HashSet<>();
        if (rsOpSeaFcl.getPolId() != null) {
            set.add(rsOpSeaFcl.getPolId());
        }
        if (rsOpSeaFcl.getDestinationPortId() != null) {
            set.add(rsOpSeaFcl.getDestinationPortId());
        }
        if (rsOpSeaFcl.getPolId() != null) {
            set.add(rsOpSeaFcl.getPolId());
        }
        if (rsOpSeaFcl.getTransitPortId() != null) {
            set.add(rsOpSeaFcl.getTransitPortId());
        }
        if (rsOpSeaFcl.getDestinationPortId() != null) {
            set.add(rsOpSeaFcl.getDestinationPortId());
        }
        AjaxResult ajaxResult = AjaxResult.success();
        ajaxResult.put(AjaxResult.DATA_TAG, rsOpSeaFcl);
        ajaxResult.put("locationOptions", !set.isEmpty() ? basDistLocationService.selectBasDistLocationByIds(set) : null);
        ajaxResult.put("supplier", rsOpSeaFcl.getSupplierId() != null ? extCompanyService.selectExtCompanyByCompanyId(rsOpSeaFcl.getSupplierId()) : null);
        return ajaxResult;
    }

    /**
     * 新增商务订舱
     */
    @PreAuthorize("@ss.hasAnyPermi('system:psarct:add,system:rct:add')")
    @Log(title = "商务订舱", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsOpSeaFcl rsOpSeaFcl) {
        return AjaxResult.success(rsOpSeaFclService.insertRsOpSeaFcl(rsOpSeaFcl));
    }

    /**
     * 修改商务订舱
     */
    @PreAuthorize("@ss.hasAnyPermi('system:psarct:edit,system:rct:edit')")
    @Log(title = "商务订舱", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsOpSeaFcl rsOpSeaFcl) {
        return AjaxResult.success(rsOpSeaFclService.updateRsOpSeaFclByRctId(rsOpSeaFcl));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:psarct:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsPsaRct rsPsaRct) {
        rsPsaRct.setUpdateBy(getUserId());
        return toAjax(rsPsaRctService.changeStatus(rsPsaRct));
    }

    /**
     * 删除商务订舱
     */
    @PreAuthorize("@ss.hasPermi('system:psarct:remove')")
    @Log(title = "商务订舱", businessType = BusinessType.DELETE)
    @DeleteMapping("/{psaRctIds}")
    public AjaxResult remove(@PathVariable Long[] psaRctIds) {
        return toAjax(rsPsaRctService.deleteRsPsaRctByPsaRctIds(psaRctIds));
    }
}
