package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasDocReleaseWay;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 交单方式Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@Mapper
public interface BasDocReleaseWayMapper {
    /**
     * 查询交单方式
     *
     * @param releaseWayId 交单方式主键
     * @return 交单方式
     */
    BasDocReleaseWay selectBasDocReleaseWayByReleaseWayId(Long releaseWayId);

    /**
     * 查询交单方式列表
     *
     * @param basDocReleaseWay 交单方式
     * @return 交单方式集合
     */
    List<BasDocReleaseWay> selectBasDocReleaseWayList(BasDocReleaseWay basDocReleaseWay);

    /**
     * 新增交单方式
     *
     * @param basDocReleaseWay 交单方式
     * @return 结果
     */
    int insertBasDocReleaseWay(BasDocReleaseWay basDocReleaseWay);

    /**
     * 修改交单方式
     *
     * @param basDocReleaseWay 交单方式
     * @return 结果
     */
    int updateBasDocReleaseWay(BasDocReleaseWay basDocReleaseWay);

    /**
     * 删除交单方式
     *
     * @param releaseWayId 交单方式主键
     * @return 结果
     */
    int deleteBasDocReleaseWayByReleaseWayId(Long releaseWayId);

    /**
     * 批量删除交单方式
     *
     * @param releaseWayIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasDocReleaseWayByReleaseWayIds(Long[] releaseWayIds);
}
