package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.BasFreightPaidWay;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.BasFreightPaidWayService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 付款方式Controller
 *
 * <AUTHOR>
 * @date 2024-03-25
 */
@RestController
@RequestMapping("/system/freightpaidway")
public class BasFreightPaidWayController extends BaseController {
    @Autowired
    private BasFreightPaidWayService basFreightPaidWayService;

    /**
     * 查询付款方式列表
     */
    @GetMapping("/list")
    public TableDataInfo list(BasFreightPaidWay basFreightPaidWay) {
        startPage();
        List<BasFreightPaidWay> list = basFreightPaidWayService.selectBasFreightPaidWayList(basFreightPaidWay);
        return getDataTable(list);
    }

    /**
     * 导出付款方式列表
     */
    @PreAuthorize("@ss.hasPermi('system:freightpaidway:export')")
    @Log(title = "付款方式", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasFreightPaidWay basFreightPaidWay) {
        List<BasFreightPaidWay> list = basFreightPaidWayService.selectBasFreightPaidWayList(basFreightPaidWay);
        ExcelUtil<BasFreightPaidWay> util = new ExcelUtil<BasFreightPaidWay>(BasFreightPaidWay.class);
        util.exportExcel(response, list, "付款方式数据");
    }

    /**
     * 获取付款方式详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:freightpaidway:query')")
    @GetMapping(value = "/{freightPaidWayCode}")
    public AjaxResult getInfo(@PathVariable("freightPaidWayCode") String freightPaidWayCode) {
        return AjaxResult.success(basFreightPaidWayService.selectBasFreightPaidWayByFreightPaidWayCode(freightPaidWayCode));
    }

    /**
     * 新增付款方式
     */
    @PreAuthorize("@ss.hasPermi('system:freightpaidway:add')")
    @Log(title = "付款方式", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasFreightPaidWay basFreightPaidWay) {
        return toAjax(basFreightPaidWayService.insertBasFreightPaidWay(basFreightPaidWay));
    }

    /**
     * 修改付款方式
     */
    @PreAuthorize("@ss.hasPermi('system:freightpaidway:edit')")
    @Log(title = "付款方式", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasFreightPaidWay basFreightPaidWay) {
        return toAjax(basFreightPaidWayService.updateBasFreightPaidWay(basFreightPaidWay));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:freightpaidway:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasFreightPaidWay basFreightPaidWay) {
        basFreightPaidWay.setUpdateBy(getUserId());
        return toAjax(basFreightPaidWayService.changeStatus(basFreightPaidWay));
    }

    /**
     * 删除付款方式
     */
    @PreAuthorize("@ss.hasPermi('system:freightpaidway:remove')")
    @Log(title = "付款方式", businessType = BusinessType.DELETE)
    @DeleteMapping("/{freightPaidWayCodes}")
    public AjaxResult remove(@PathVariable String[] freightPaidWayCodes) {
        return toAjax(basFreightPaidWayService.deleteBasFreightPaidWayByFreightPaidWayCodes(freightPaidWayCodes));
    }
}
