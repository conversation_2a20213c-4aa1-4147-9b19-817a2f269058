/**
 * 显示消息提示框
 * @param content 提示的标题
 */
export function toast(content) {
    uni.showToast({
        icon: "none",
        title: content,
    });
}

/**
 * 显示模态弹窗
 * @param content 提示的标题
 */
export function showConfirm(content) {
    return new Promise((resolve, reject) => {
        uni.showModal({
            title: "提示",
            content: content,
            cancelText: "取消",
            confirmText: "确定",
            success: function (res) {
                resolve(res);
            },
        });
    });
}

/**
 * 参数处理
 * @param params 参数
 */
export function tansParams(params) {
    let result = "";
    for (const propName of Object.keys(params)) {
        const value = params[propName];
        var part = encodeURIComponent(propName) + "=";
        if (value !== null && value !== "" && typeof value !== "undefined") {
            if (typeof value === "object") {
                for (const key of Object.keys(value)) {
                    if (
                        value[key] !== null &&
                        value[key] !== "" &&
                        typeof value[key] !== "undefined"
                    ) {
                        let params = propName + "[" + key + "]";
                        var subPart = encodeURIComponent(params) + "=";
                        result += subPart + encodeURIComponent(value[key]) + "&";
                    }
                }
            } else {
                result += part + encodeURIComponent(value) + "&";
            }
        }
    }
    return result;
}

/**
 * 字符串加密
 */
export function encrypt(str) {
    if (!str) return "";

    try {
        // 对字符串进行编码
        const encodedStr = encodeURIComponent(str);
        // 在微信小程序中使用wx.arrayBufferToBase64替代btoa
        if (typeof wx !== "undefined") {
            // 将字符串转换为ArrayBuffer
            const buffer = new Uint8Array(encodedStr.length);
            for (let i = 0; i < encodedStr.length; i++) {
                buffer[i] = encodedStr.charCodeAt(i);
            }
            return wx.arrayBufferToBase64(buffer);
        } else if (typeof uni !== "undefined" && uni.arrayBufferToBase64) {
            // 如果是uni-app环境，使用uni.arrayBufferToBase64
            const buffer = new Uint8Array(encodedStr.length);
            for (let i = 0; i < encodedStr.length; i++) {
                buffer[i] = encodedStr.charCodeAt(i);
            }
            return uni.arrayBufferToBase64(buffer);
        } else if (typeof btoa !== "undefined") {
            // 浏览器环境
            return btoa(encodedStr);
        } else {
            // 简单替代方案（不安全，仅用作备选）
            return encodedStr
                .split("")
                .map((char) => char.charCodeAt(0).toString(16))
                .join("");
        }
    } catch (e) {
        console.error("加密失败:", e);
        return "";
    }
}

/**
 * 字符串解密
 */
export function decrypt(str) {
    if (!str) return "";

    try {
        let decodedStr;
        if (typeof wx !== "undefined") {
            // 微信小程序环境
            const arrayBuffer = wx.base64ToArrayBuffer(str);
            const uint8Array = new Uint8Array(arrayBuffer);
            let tempStr = "";
            for (let i = 0; i < uint8Array.length; i++) {
                tempStr += String.fromCharCode(uint8Array[i]);
            }
            decodedStr = tempStr;
        } else if (typeof uni !== "undefined" && uni.base64ToArrayBuffer) {
            // uni-app环境
            const arrayBuffer = uni.base64ToArrayBuffer(str);
            const uint8Array = new Uint8Array(arrayBuffer);
            let tempStr = "";
            for (let i = 0; i < uint8Array.length; i++) {
                tempStr += String.fromCharCode(uint8Array[i]);
            }
            decodedStr = tempStr;
        } else if (typeof atob !== "undefined") {
            // 浏览器环境
            decodedStr = atob(str);
        } else {
            // 简单替代方案（不安全，仅用作备选）
            decodedStr = str
                .match(/.{1,2}/g)
                .map((char) => String.fromCharCode(parseInt(char, 16)))
                .join("");
        }
        return decodeURIComponent(decodedStr);
    } catch (e) {
        console.error("解密失败:", e);
        return "";
    }
}
