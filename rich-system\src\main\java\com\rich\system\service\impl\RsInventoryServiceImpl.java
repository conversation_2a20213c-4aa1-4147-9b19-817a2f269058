package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.MidOutboundSettlement;
import com.rich.common.core.domain.entity.RsCargoDetails;
import com.rich.common.core.domain.entity.RsInventory;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.RedisIdGeneratorService;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.StringUtils;
import com.rich.common.utils.bean.BeanUtils;
import com.rich.system.mapper.MidOutboundSettlementMapper;
import com.rich.system.mapper.RsCargoDetailsMapper;
import com.rich.system.mapper.RsInventoryMapper;
import com.rich.system.service.RsInventoryService;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 库存Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-08
 */
@Service
public class RsInventoryServiceImpl implements RsInventoryService {
    @Autowired
    private RsInventoryMapper rsInventoryMapper;

    @Resource
    private RsCargoDetailsMapper rsCargoDetailsMapper;

    @Resource
    private RedisIdGeneratorService redisIdGeneratorService;

    @Resource
    private MidOutboundSettlementMapper midOutboundSettlementMapper;

    /**
     * 查询库存
     *
     * @param inventoryId 库存主键
     * @return 库存
     */
    @Override
    public RsInventory selectRsInventoryByInventoryId(Long inventoryId) {
        return rsInventoryMapper.selectRsInventoryByInventoryId(inventoryId);
    }

    /**
     * 查询库存列表
     *
     * @param rsInventory 库存
     * @return 库存
     */
    @Override
    public List<RsInventory> selectRsInventoryList(RsInventory rsInventory) {
        return rsInventoryMapper.selectRsInventoryList(rsInventory);
    }

    /**
     * 新增库存
     *
     * @param rsInventory 库存
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RsInventory insertRsInventory(RsInventory rsInventory) {
        generateInboundSerialNo(rsInventory);
        if (rsInventory.getRepackingStatus() == null) {
            rsInventory.setRepackingStatus(rsInventory.getPackageTo() != null ? "被打包" : "-");
        }
        rsInventory.setCreateBy(SecurityUtils.getUserId());
        int i = rsInventoryMapper.insertRsInventory(rsInventory);
        for (RsCargoDetails rsCargoDetails : rsInventory.getRsCargoDetailsList()) {
            rsCargoDetails.setInventoryId(rsInventory.getInventoryId());
            rsCargoDetails.setInboundSerialNo(rsInventory.getInboundSerialNo());
            rsCargoDetailsMapper.insertRsCargoDetails(rsCargoDetails);
        }
        return rsInventory;
    }

    private void generateInboundSerialNo(RsInventory rsInventory) {
        // 生成入仓流水号
        String prefix = "RS.";
        int no = rsInventoryMapper.getInboundNo();
        String dateStr = DateUtils.dateTimeNow("yyMM");
        String padlNo = StringUtils.padl(no + 1, 4);
        rsInventory.setInboundSerialNo(prefix + dateStr + padlNo);
    }

    // 根据上一次的流水号生成下一次部分出库的流水号
    public static String getNextSerialNumber(String lastSerialNumber) {
        if (!StringUtils.isNotBlank(lastSerialNumber)) {
            return "";
        }
        // 查找最后的"-"字符位置，判断是否有后缀
        int dashIndex = lastSerialNumber.lastIndexOf('-');
        if (dashIndex == -1) {
            // 如果没有后缀，直接加上 "-A"
            return lastSerialNumber + "-A";
        } else {
            // 提取现有的字母后缀并计算下一个字母
            char currentSuffix = lastSerialNumber.charAt(dashIndex + 1);
            char nextSuffix = (char) (currentSuffix + 1); // 生成下一个字母

            // 生成新的流水号
            return lastSerialNumber.substring(0, dashIndex + 1) + nextSuffix;
        }
    }

    /**
     * 修改库存状态
     *
     * @param rsInventory 库存
     * @return 库存
     */
    @Override
    public int changeStatus(RsInventory rsInventory) {
        return rsInventoryMapper.updateRsInventory(rsInventory);
    }


    /**
     * 批量删除库存
     *
     * @param inventoryIds 需要删除的库存主键
     * @return 结果
     */
    @Override
    public int deleteRsInventoryByInventoryIds(Long[] inventoryIds) {
        return rsInventoryMapper.deleteRsInventoryByInventoryIds(inventoryIds);
    }

    /**
     * 删除库存信息
     *
     * @param inventoryId 库存主键
     * @return 结果
     */
    @Override
    public int deleteRsInventoryByInventoryId(Long inventoryId) {
        rsCargoDetailsMapper.deleteRsCargoDetailsByInventoryId(inventoryId);
        return rsInventoryMapper.deleteRsInventoryByInventoryId(inventoryId);
    }

    /**
     * 修改库存
     *
     * @param rsInventory 库存
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateRsInventory(RsInventory rsInventory) {
        rsCargoDetailsMapper.deleteRsCargoDetailsByInventoryId(rsInventory.getInventoryId());
        for (RsCargoDetails rsCargoDetails : rsInventory.getRsCargoDetailsList()) {
            rsCargoDetails.setInventoryId(rsInventory.getInventoryId());
            rsCargoDetailsMapper.insertRsCargoDetails(rsCargoDetails);
        }
        return rsInventoryMapper.updateRsInventory(rsInventory);
    }

    /**
     * 出仓
     *
     * @param rsInventory 库存信息
     * @return 处理结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int outboundRsInventory(RsInventory rsInventory) {
        // 参数校验
        if (rsInventory == null || rsInventory.getInventoryId() == null) {
            throw new IllegalArgumentException("库存信息不能为空");
        }

        // 使用统一的时间戳
        Date currentTime = new Date();

        // 根据出库方式处理
        if ("0".equals(rsInventory.getPartialOutboundFlag())) {
            // 全部出库
            return handleFullOutbound(rsInventory, currentTime);
        } else {
            // 部分出库
            return handlePartialOutbound(rsInventory, currentTime);
        }
    }

    /**
     * 处理全部出库
     */
    private int handleFullOutbound(RsInventory rsInventory, Date currentTime) {
        // 更新库存状态为已出库
        rsInventory.setInventoryStatus("1");
        rsInventory.setActualOutboundTime(currentTime);
        rsInventoryMapper.updateRsInventory(rsInventory);
        // 如果是打包箱,将打包箱中的所有快递也出库
        if (rsInventory.getPackageRecord().equals("1")) {
            RsInventory queryParam = new RsInventory();
            queryParam.setPackageTo(rsInventory.getInventoryId());
            rsInventoryMapper.updateRsInventoryByPackageTo(queryParam);
        }

        // 更新所有货物明细状态为已出库
        if (rsInventory.getRsCargoDetailsList() != null) {
            for (RsCargoDetails rsCargoDetails : rsInventory.getRsCargoDetailsList()) {
                rsCargoDetails.setInventoryStatus("1");
                rsCargoDetailsMapper.updateRsCargoDetails(rsCargoDetails);
            }
        }

        return 1;
    }

    /**
     * 处理部分出库
     */
    private int handlePartialOutbound(RsInventory rsInventory, Date currentTime) {
        // 1. 设置原库存记录为已出库
        rsInventory.setInventoryStatus("1");
        rsInventory.setActualOutboundTime(currentTime);

        // 2. 获取原始货物明细列表
        RsCargoDetails searchCondition = new RsCargoDetails();
        searchCondition.setInventoryId(rsInventory.getInventoryId());
        List<RsCargoDetails> originalList = rsCargoDetailsMapper.selectRsCargoDetailsList(searchCondition);

        // 3. 计算出库货物的数量和保留货物的明细
        OutboundCalculationResult result = calculateOutboundDetails(rsInventory, originalList);

        // 4. 更新原库存记录的汇总数据（出库部分）
        rsInventory.setTotalBoxes(result.totalBoxCountOutbound);
        rsInventory.setTotalVolume(result.totalVolumeOutbound);
        rsInventory.setTotalGrossWeight(result.totalGrossWeightOutbound);
        rsInventoryMapper.updateRsInventory(rsInventory);

        // 如果是打包箱,将打包箱中的所有快递也出库
        if (rsInventory.getPackageRecord().equals("1")) {
            RsInventory queryParam = new RsInventory();
            queryParam.setPackageTo(rsInventory.getInventoryId());
            rsInventoryMapper.updateRsInventoryByPackageTo(queryParam);
        }

        // 5. 创建新的库存记录（保留部分）
        RsInventory newInventory = createNewInventoryRecord(rsInventory, result, currentTime);
        // 部分出仓,新的库存中费用默认已结清
        newInventory.setReceivedSupplier(BigDecimal.ZERO);
        newInventory.setInboundFee(BigDecimal.ZERO);
        newInventory.setReceivedStorageFee(BigDecimal.ZERO);
        newInventory.setUnpaidUnloadingFee(BigDecimal.ZERO);
        newInventory.setReceivedUnloadingFee(BigDecimal.ZERO);
        newInventory.setUnpaidPackagingFee(BigDecimal.ZERO);
        newInventory.setReceivedPackingFee(BigDecimal.ZERO);
        newInventory.setLogisticsAdvanceFee(BigDecimal.ZERO);

        rsInventory.setRepackingStatus(rsInventory.getPackageTo() != null ? "被打包" : "-");
        rsInventory.setCreateBy(SecurityUtils.getUserId());
        rsInventoryMapper.insertRsInventory(newInventory);

        // 6. 更新保留的货物明细，关联到新库存记录
        for (RsCargoDetails cargoDetail : result.remainingCargoDetails) {
            cargoDetail.setInventoryId(newInventory.getInventoryId());
            cargoDetail.setOutboundRecordId(null);
            rsCargoDetailsMapper.updateRsCargoDetails(cargoDetail);
        }

        return 1;
    }

    /**
     * 计算出库明细和保留明细
     */
    private OutboundCalculationResult calculateOutboundDetails(RsInventory rsInventory, List<RsCargoDetails> originalList) {
        OutboundCalculationResult result = new OutboundCalculationResult();

        // 如果没有指定出库明细，则返回空结果
        if (rsInventory.getOutboundCargoDetailsList() == null || rsInventory.getOutboundCargoDetailsList().isEmpty()) {
            return result;
        }

        // 获取要出库的货物ID集合
        Set<Long> outboundIds = rsInventory.getOutboundCargoDetailsList().stream()
                .map(RsCargoDetails::getCargoDetailsId)
                .collect(Collectors.toSet());

        // 找出不需要出库的货物明细
        for (RsCargoDetails cargoDetails : originalList) {
            if (!outboundIds.contains(cargoDetails.getCargoDetailsId())) {
                result.remainingCargoDetails.add(cargoDetails);
            }
        }

        // 处理需要出库的货物明细
        for (RsCargoDetails originalCargo : originalList) {
            for (RsCargoDetails outboundCargo : rsInventory.getOutboundCargoDetailsList()) {
                if (!originalCargo.getCargoDetailsId().equals(outboundCargo.getCargoDetailsId())) {
                    continue;
                }

                // 获取出库箱数
                Long boxCount = outboundCargo.getBoxCount();
                if (boxCount == null || boxCount <= 0) {
                    continue;
                }

                // 累计出库数量
                result.totalBoxCountOutbound += boxCount;

                // 计算体积和重量
                BigDecimal volume = calculateVolume(originalCargo, outboundCargo, boxCount);
                BigDecimal weight = calculateWeight(originalCargo, outboundCargo, boxCount);

                result.totalVolumeOutbound = result.totalVolumeOutbound.add(volume);
                result.totalGrossWeightOutbound = result.totalGrossWeightOutbound.add(weight);

                // 如果箱数不同，需要拆分
                if (!originalCargo.getBoxCount().equals(outboundCargo.getBoxCount())) {
                    // 创建保留部分的货物明细
                    RsCargoDetails remainingCargo = createRemainingCargoDetail(originalCargo, outboundCargo, boxCount);
                    result.remainingCargoDetails.add(remainingCargo);

                    // 创建出库部分的货物明细
                    RsCargoDetails outboundCargoDetail = createOutboundCargoDetail(originalCargo, outboundCargo, boxCount);
                    outboundCargoDetail.setInventoryStatus("1");
                    outboundCargoDetail.setOutboundRecordId(rsInventory.getOutboundRecordId());
                    rsCargoDetailsMapper.insertRsCargoDetails(outboundCargoDetail);
                }
            }
        }

        return result;
    }

    /**
     * 计算货物体积
     */
    private BigDecimal calculateVolume(RsCargoDetails originalCargo, RsCargoDetails outboundCargo, Long boxCount) {
        // 检查参数是否为空
        if (originalCargo == null || outboundCargo == null || boxCount == null || boxCount <= 0) {
            return BigDecimal.ZERO;
        }
        
        if (outboundCargo.getSinglePieceVolume() != null &&
                !outboundCargo.getSinglePieceVolume().equals(BigDecimal.ZERO)) {
            return outboundCargo.getSinglePieceVolume().multiply(new BigDecimal(boxCount));
        } else {
            // 检查原始货物的箱数和体积
            Long originalBoxCount = originalCargo.getBoxCount();
            BigDecimal unitVolume = originalCargo.getUnitVolume();

            // 防止除零错误和空值
            if (originalBoxCount == null || originalBoxCount <= 0 || unitVolume == null) {
                return BigDecimal.ZERO;
            }

            return unitVolume
                    .divide(new BigDecimal(originalBoxCount), 2, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(boxCount));
        }
    }

    /**
     * 计算货物重量
     */
    private BigDecimal calculateWeight(RsCargoDetails originalCargo, RsCargoDetails outboundCargo, Long boxCount) {
        // 检查参数是否为空
        if (originalCargo == null || outboundCargo == null || boxCount == null || boxCount <= 0) {
            return BigDecimal.ZERO;
        }
        
        if (outboundCargo.getSinglePieceWeight() != null &&
                !outboundCargo.getSinglePieceWeight().equals(BigDecimal.ZERO)) {
            return outboundCargo.getSinglePieceWeight().multiply(new BigDecimal(boxCount));
        } else {
            // 检查原始货物的箱数和重量
            Long originalBoxCount = originalCargo.getBoxCount();
            BigDecimal unitGrossWeight = originalCargo.getUnitGrossWeight();

            // 防止除零错误和空值
            if (originalBoxCount == null || originalBoxCount <= 0 || unitGrossWeight == null) {
                return BigDecimal.ZERO;
            }

            return unitGrossWeight
                    .divide(new BigDecimal(originalBoxCount), 2, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(boxCount));
        }
    }

    /**
     * 创建保留部分的货物明细
     */
    private RsCargoDetails createRemainingCargoDetail(RsCargoDetails originalCargo, RsCargoDetails outboundCargo, Long boxCount) {
        RsCargoDetails remainingCargo = new RsCargoDetails();
        BeanUtils.copyProperties(outboundCargo, remainingCargo);

        // 计算保留的箱数
        Long remainingBoxCount = originalCargo.getBoxCount() - boxCount;
        remainingCargo.setBoxCount(remainingBoxCount);

        // 计算保留的体积
        if (remainingCargo.getSinglePieceVolume() != null &&
                !remainingCargo.getSinglePieceVolume().equals(BigDecimal.ZERO)) {
            remainingCargo.setUnitVolume(new BigDecimal(remainingBoxCount).multiply(remainingCargo.getSinglePieceVolume()));
        } else {
            // 检查 originalCargo 和 unitVolume 是否为空
            if (originalCargo == null) {
                remainingCargo.setUnitVolume(BigDecimal.ZERO);
            } else {
                BigDecimal unitVolume = originalCargo.getUnitVolume();
                Long originalBoxCount = originalCargo.getBoxCount();

                // 检查 unitVolume 是否为空
                if (unitVolume == null) {
                    remainingCargo.setUnitVolume(BigDecimal.ZERO);
                }
                // 检查除数是否为零或空
                else if (originalBoxCount == null || originalBoxCount <= 0) {
                    remainingCargo.setUnitVolume(BigDecimal.ZERO);
                } else {
                    remainingCargo.setUnitVolume(unitVolume
                            .divide(new BigDecimal(originalBoxCount), 2, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal(remainingBoxCount)));
                }
            }
        }

        // 计算保留的重量
        if (remainingCargo.getSinglePieceWeight() != null &&
                !remainingCargo.getSinglePieceWeight().equals(BigDecimal.ZERO)) {
            remainingCargo.setUnitGrossWeight(new BigDecimal(remainingBoxCount).multiply(remainingCargo.getSinglePieceWeight()));
        } else {
            // 检查 originalCargo 和 unitGrossWeight 是否为空
            if (originalCargo == null) {
                remainingCargo.setUnitGrossWeight(BigDecimal.ZERO);
            } else {
                BigDecimal unitGrossWeight = originalCargo.getUnitGrossWeight();
                Long originalBoxCount = originalCargo.getBoxCount();

                // 检查 unitGrossWeight 是否为空
                if (unitGrossWeight == null) {
                    remainingCargo.setUnitGrossWeight(BigDecimal.ZERO);
                }
                // 检查除数是否为零或空
                else if (originalBoxCount == null || originalBoxCount <= 0) {
                    remainingCargo.setUnitGrossWeight(BigDecimal.ZERO);
                } else {
                    remainingCargo.setUnitGrossWeight(unitGrossWeight
                            .divide(new BigDecimal(originalBoxCount), 2, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal(remainingBoxCount)));
                }
            }
        }

        remainingCargo.setOutboundRecordId(null);
        return remainingCargo;
    }

    /**
     * 创建出库部分的货物明细
     */
    private RsCargoDetails createOutboundCargoDetail(RsCargoDetails originalCargo, RsCargoDetails outboundCargo, Long boxCount) {
        RsCargoDetails outboundCargoDetail = new RsCargoDetails();
        BeanUtils.copyProperties(outboundCargo, outboundCargoDetail);

        // 计算出库的体积
        if (outboundCargoDetail.getSinglePieceVolume() != null &&
                !outboundCargoDetail.getSinglePieceVolume().equals(BigDecimal.ZERO)) {
            outboundCargoDetail.setUnitVolume(new BigDecimal(boxCount).multiply(outboundCargoDetail.getSinglePieceVolume()));
        } else {
            // 检查 originalCargo 是否为空
            if (originalCargo == null) {
                outboundCargoDetail.setUnitVolume(BigDecimal.ZERO);
            } else {
                BigDecimal unitVolume = originalCargo.getUnitVolume();
                Long originalBoxCount = originalCargo.getBoxCount();

                // 检查 unitVolume 是否为空
                if (unitVolume == null) {
                    outboundCargoDetail.setUnitVolume(BigDecimal.ZERO);
                }
                // 检查除数是否为零或空
                else if (originalBoxCount == null || originalBoxCount <= 0) {
                    outboundCargoDetail.setUnitVolume(BigDecimal.ZERO);
                } else {
                    outboundCargoDetail.setUnitVolume(unitVolume
                            .divide(new BigDecimal(originalBoxCount), 2, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal(boxCount)));
                }
            }
        }

        // 计算出库的重量
        if (outboundCargoDetail.getSinglePieceWeight() != null &&
                !outboundCargoDetail.getSinglePieceWeight().equals(BigDecimal.ZERO)) {
            outboundCargoDetail.setUnitGrossWeight(new BigDecimal(boxCount).multiply(outboundCargoDetail.getSinglePieceWeight()));
        } else {
            // 检查 originalCargo 是否为空
            if (originalCargo == null) {
                outboundCargoDetail.setUnitGrossWeight(BigDecimal.ZERO);
            } else {
                BigDecimal unitGrossWeight = originalCargo.getUnitGrossWeight();
                Long originalBoxCount = originalCargo.getBoxCount();

                // 检查 unitGrossWeight 是否为空
                if (unitGrossWeight == null) {
                    outboundCargoDetail.setUnitGrossWeight(BigDecimal.ZERO);
                }
                // 检查除数是否为零或空
                else if (originalBoxCount == null || originalBoxCount <= 0) {
                    outboundCargoDetail.setUnitGrossWeight(BigDecimal.ZERO);
                } else {
                    outboundCargoDetail.setUnitGrossWeight(unitGrossWeight
                            .divide(new BigDecimal(originalBoxCount), 2, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal(boxCount)));
                }
            }
        }

        outboundCargoDetail.setCargoDetailsId(null);
        return outboundCargoDetail;
    }

    /**
     * 创建新的库存记录
     */
    private RsInventory createNewInventoryRecord(RsInventory originalInventory, OutboundCalculationResult result, Date currentTime) {
        RsInventory newInventory = new RsInventory();
        BeanUtils.copyProperties(originalInventory, newInventory);

        // 设置新库存记录的属性
        newInventory.setInventoryId(null);
        newInventory.setInboundSerialSplit(originalInventory.getInboundSerialNo());
        newInventory.setInboundSerialNo(getNextSerialNumber(originalInventory.getInboundSerialNo()));
        newInventory.setInventoryStatus("0");  // 设置为在库状态
        newInventory.setCreatedAt(null);       // 让数据库自动生成创建时间
        newInventory.setRentalSettlementDate(currentTime);
        newInventory.setInboundDate(currentTime);
        newInventory.setOutboundRecordId(null);

        // 设置新库存记录的数量信息
        newInventory.setTotalBoxes(result.totalBoxCountRemaining);
        newInventory.setTotalVolume(result.totalVolumeRemaining);
        newInventory.setTotalGrossWeight(result.totalGrossWeightRemaining);
        newInventory.setPartialOutboundFlag("0");  // 重置部分出库标志

        return newInventory;
    }

    /**
     * 出库计算结果类
     */
    private static class OutboundCalculationResult {
        // 出库部分
        long totalBoxCountOutbound = 0L;
        BigDecimal totalVolumeOutbound = BigDecimal.ZERO;
        BigDecimal totalGrossWeightOutbound = BigDecimal.ZERO;

        // 保留部分
        long totalBoxCountRemaining = 0L;
        BigDecimal totalVolumeRemaining = BigDecimal.ZERO;
        BigDecimal totalGrossWeightRemaining = BigDecimal.ZERO;

        // 保留的货物明细
        List<RsCargoDetails> remainingCargoDetails = new ArrayList<>();

        // 计算保留部分的数量
        public void calculateRemaining(RsInventory originalInventory) {
            totalBoxCountRemaining = originalInventory.getTotalBoxes() - totalBoxCountOutbound;
            totalVolumeRemaining = originalInventory.getTotalVolume().subtract(totalVolumeOutbound);
            totalGrossWeightRemaining = originalInventory.getTotalGrossWeight().subtract(totalGrossWeightOutbound);

            // 确保数值不为负
            totalBoxCountRemaining = Math.max(0, totalBoxCountRemaining);
            totalVolumeRemaining = totalVolumeRemaining.max(BigDecimal.ZERO);
            totalGrossWeightRemaining = totalGrossWeightRemaining.max(BigDecimal.ZERO);
        }
    }

    @Override
    public int preOutboundRsInventory(RsInventory rsInventory) {
        rsInventoryMapper.updateRsInventory(rsInventory);
        if (rsInventory.getPartialOutboundFlag().equals("1")) {
            for (RsCargoDetails rsCargoDetails : rsInventory.getOutboundCargoDetailsList()) {
                RsCargoDetails rsCargoDetailsNew = new RsCargoDetails();
                rsCargoDetailsNew.setPreOutboundFlag("1");
                rsCargoDetailsNew.setCargoDetailsId(rsCargoDetails.getCargoDetailsId());
                rsCargoDetailsMapper.updateRsCargoDetails(rsCargoDetailsNew);
            }
        } else {
            for (RsCargoDetails rsCargoDetails : rsInventory.getRsCargoDetailsList()) {
                RsCargoDetails rsCargoDetailsNew = new RsCargoDetails();
                rsCargoDetailsNew.setPreOutboundFlag("1");
                rsCargoDetailsNew.setCargoDetailsId(rsCargoDetails.getCargoDetailsId());
                rsCargoDetailsMapper.updateRsCargoDetails(rsCargoDetailsNew);
            }
        }
        return 0;
    }

    @NotNull
    private static MidOutboundSettlement getMidOutboundSettlement(RsInventory inventory) {
        MidOutboundSettlement midOutboundSettlement = new MidOutboundSettlement();
        midOutboundSettlement.setSettlementDate(inventory.getRentalSettlementDate());
        midOutboundSettlement.setOutboundRecordId(inventory.getOutboundRecordId());
        midOutboundSettlement.setInventoryId(inventory.getInventoryId());
        midOutboundSettlement.setSettlementRate(inventory.getOverdueRentalFee());
        midOutboundSettlement.setSettlementInboundDate(inventory.getActualInboundTime());
        return midOutboundSettlement;
    }

    /**
     * 计算仓租
     *
     * @param rsInventory
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int warehouseRentSettlement(List<RsInventory> rsInventory) {
        BigDecimal overdueRentalFee = BigDecimal.ZERO;
        List<MidOutboundSettlement> midOutboundSettlements = new ArrayList<>();
        for (RsInventory inventory : rsInventory) {
            overdueRentalFee = overdueRentalFee.add(inventory.getOverdueRentalFee());

            MidOutboundSettlement midOutboundSettlement = getMidOutboundSettlement(inventory);
            midOutboundSettlements.add(midOutboundSettlement);

            RsInventory updateObject = new RsInventory();
            updateObject.setInventoryId(inventory.getInventoryId());
            // 方法2：使用LocalDate (推荐)
            if (inventory.getRentalSettlementDate() != null) {
                Date originalDate = inventory.getRentalSettlementDate();
                LocalDate localDate = originalDate.toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDate()
                        .plusDays(1);
                Date newDate = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                updateObject.setRentalSettlementDate(newDate);
            } else {
                updateObject.setRentalSettlementDate(null);
            }
            rsInventoryMapper.updateRsInventory(updateObject);
        }
        midOutboundSettlementMapper.batchInsert(midOutboundSettlements);
        return 0;
    }

    @Override
    public List<RsInventory> getPackages(RsInventory rsInventory) {
        return rsInventoryMapper.getPackages(rsInventory);
    }

    /**
     * 导入库存数据
     *
     * @param inventoryList 库存数据列表
     * @param updateSupport 是否支持更新
     * @return 导入失败的库存数据列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<RsInventory> importInventory(List<RsInventory> inventoryList, boolean updateSupport) {
        Set<RsInventory> failList = new HashSet<>();
        String operName = SecurityUtils.getUsername();
        Date now = DateUtils.getNowDate();

        // 批量处理的集合
        List<RsInventory> batchInventoryList = new ArrayList<>();
        List<RsCargoDetails> batchCargoDetailsList = new ArrayList<>();

        for (RsInventory inventory : inventoryList) {
            try {
                // 设置创建人和创建时间
                inventory.setCreateBy(SecurityUtils.getUserId());
                inventory.setCreateTime(now);
                inventory.setUpdateBy(SecurityUtils.getUserId());
                inventory.setUpdateTime(now);

                // 设置默认状态为在库
                inventory.setInventoryStatus("0");

                // 生成流水号
                generateInboundSerialNo(inventory);

                // 是否放到打包箱中
                if (inventory.getPackageIntoNo() != null) {
                    RsInventory queryParams = new RsInventory();
                    queryParams.setSubOrderNo(inventory.getPackageIntoNo());
                    List<RsInventory> rsInventories = rsInventoryMapper.selectRsInventoryList(queryParams);
                    if (!rsInventories.isEmpty()) {
                        inventory.setPackageTo(rsInventories.get(0).getInventoryId());
                        inventory.setPackageToNo(rsInventories.get(0).getInboundSerialNo());
                        inventory.setRepackingStatus("被打包");
                    }
                }

                // 计算总体积和总重量，注意处理可能的空值情况
                if (inventory.getTotalBoxes() != null) {
                    // 总体积计算
                    if (inventory.getSinglePieceVolume() != null) {
                        inventory.setTotalVolume(BigDecimal.valueOf(inventory.getTotalBoxes() * inventory.getSinglePieceVolume()));
                    } else {
                        // 如果单件体积为空，设置默认值
                        inventory.setSinglePieceVolume(0L);
                        inventory.setTotalVolume(BigDecimal.ZERO);
                    }

                    // 总重量计算
                    if (inventory.getSinglePieceWeight() != null) {
                        inventory.setTotalGrossWeight(BigDecimal.valueOf(inventory.getTotalBoxes() * inventory.getSinglePieceWeight()));
                    } else {
                        // 如果单件重量为空，设置默认值
                        inventory.setSinglePieceWeight(0L);
                        inventory.setTotalGrossWeight(BigDecimal.ZERO);
                    }
                } else {
                    // 如果箱数为空，设置默认值
                    inventory.setTotalBoxes(0L);
                    inventory.setTotalVolume(BigDecimal.ZERO);
                    inventory.setTotalGrossWeight(BigDecimal.ZERO);
                }

                // 添加到批量插入列表
                batchInventoryList.add(inventory);

            } catch (Exception e) {
                // 记录导入失败的记录
                inventory.setRemark("导入失败：" + e.getMessage());
                failList.add(inventory);
            }
        }

        // 如果有记录需要插入
        if (!batchInventoryList.isEmpty()) {
            try {
                // 批量插入库存记录
                rsInventoryMapper.batchInsertInventory(batchInventoryList);

                // 为每个库存创建货物明细并添加到批量集合
                for (RsInventory inventory : batchInventoryList) {
                    RsCargoDetails rsCargoDetails = new RsCargoDetails();
                    rsCargoDetails.setInventoryId(inventory.getInventoryId());
                    rsCargoDetails.setItemName(inventory.getCargoName());
                    rsCargoDetails.setBoxCount(inventory.getTotalBoxes());

                    // 设置尺寸，需要处理可能的空值
                    rsCargoDetails.setUnitWidth(inventory.getWidth() != null ?
                            BigDecimal.valueOf(inventory.getWidth()) : BigDecimal.ZERO);
                    rsCargoDetails.setUnitHeight(inventory.getLength() != null ?
                            BigDecimal.valueOf(inventory.getLength()) : BigDecimal.ZERO);
                    rsCargoDetails.setUnitLength(inventory.getHeight() != null ?
                            BigDecimal.valueOf(inventory.getHeight()) : BigDecimal.ZERO);

                    // 设置单件重量和体积，需要处理可能的空值
                    rsCargoDetails.setSinglePieceWeight(inventory.getSinglePieceWeight() != null ?
                            BigDecimal.valueOf(inventory.getSinglePieceWeight()) : BigDecimal.ZERO);
                    rsCargoDetails.setSinglePieceVolume(inventory.getSinglePieceVolume() != null ?
                            BigDecimal.valueOf(inventory.getSinglePieceVolume()) : BigDecimal.ZERO);

                    // 计算单位体积和重量
                    if (inventory.getTotalBoxes() != null && inventory.getTotalBoxes() > 0) {
                        // 体积和重量值已经计算过，直接使用
                        rsCargoDetails.setUnitVolume(inventory.getTotalVolume());
                        rsCargoDetails.setUnitGrossWeight(inventory.getTotalGrossWeight());
                    } else {
                        // 如果箱数为0，设置默认值
                        rsCargoDetails.setUnitVolume(BigDecimal.ZERO);
                        rsCargoDetails.setUnitGrossWeight(BigDecimal.ZERO);
                    }

                    batchCargoDetailsList.add(rsCargoDetails);
                }

                // 批量插入货物明细
                if (!batchCargoDetailsList.isEmpty()) {
                    rsCargoDetailsMapper.batchInsertCargoDetails(batchCargoDetailsList);
                }
            } catch (Exception e) {
                // 批量插入失败，将所有记录标记为失败
                for (RsInventory inventory : batchInventoryList) {
                    inventory.setRemark("批量导入失败：" + e.getMessage());
                    failList.add(inventory);
                }
            }
        }

        return new ArrayList<>(failList);
    }

    /**
     * 根据快递单号查询库存信息
     *
     * @param expressNo 快递单号
     * @return 库存信息
     */
    @Override
    public RsInventory selectRsInventoryByExpressNo(String expressNo) {
        if (StringUtils.isEmpty(expressNo)) {
            return null;
        }

        RsInventory queryParam = new RsInventory();
        queryParam.setForwarderNo(expressNo); // 假设快递单号存储在forwarderNo字段

        List<RsInventory> inventoryList = rsInventoryMapper.selectRsInventoryList(queryParam);
        return inventoryList.isEmpty() ? null : inventoryList.get(0);
    }

    /**
     * 获取库存状态统计
     *
     * @param clientCode 客户代码
     * @return 状态统计信息
     */
    @Override
    public Map<String, Integer> getInventoryStatusCount(String clientCode) {
        Map<String, Integer> statusCount = new HashMap<>();

        // 构建查询条件
        RsInventory queryParam = new RsInventory();
        if (StringUtils.isNotEmpty(clientCode)) {
            queryParam.setClientCode(clientCode);
        }

        // 查询所有相关库存
        List<RsInventory> inventoryList = rsInventoryMapper.selectRsInventoryList(queryParam);

        // 初始化计数器
        statusCount.put("preEntryCount", 0);      // 预入库
        statusCount.put("inTransitCount", 0);     // 在途
        statusCount.put("uncompletedCount", 0);   // 信息未完善
        statusCount.put("unconfirmedCount", 0);   // 未确认
        statusCount.put("confirmedCount", 0);     // 已确认
        statusCount.put("unknownCount", 0);       // 未知归属

        // 统计各状态数量
        for (RsInventory inventory : inventoryList) {
            String status = inventory.getInventoryStatus();
            String clientCodeValue = inventory.getClientCode();

            if ("0".equals(status)) { // 在库状态
                if (StringUtils.isEmpty(clientCodeValue) || "unknown".equals(clientCodeValue)) {
                    statusCount.put("unknownCount", statusCount.get("unknownCount") + 1);
                } else if (!isInventoryInfoComplete(inventory)) {
                    statusCount.put("uncompletedCount", statusCount.get("uncompletedCount") + 1);
                } else if ("0".equals(inventory.getConfirmInboundRequestFlag())) {
                    statusCount.put("unconfirmedCount", statusCount.get("unconfirmedCount") + 1);
                } else {
                    statusCount.put("confirmedCount", statusCount.get("confirmedCount") + 1);
                }
            } else if ("1".equals(status)) { // 已出库
                // 已出库的不计入统计
            } else if ("-1".equals(status)) { // 被打包
                statusCount.put("inTransitCount", statusCount.get("inTransitCount") + 1);
            }
        }

        return statusCount;
    }

    /**
     * 检查库存信息是否完整
     *
     * @param inventory 库存信息
     * @return 是否完整
     */
    @Override
    public boolean isInventoryInfoComplete(RsInventory inventory) {
        if (inventory == null) {
            return false;
        }

        // 检查必要字段是否完整
        return StringUtils.isNotEmpty(inventory.getClientName()) &&
               StringUtils.isNotEmpty(inventory.getCargoName()) &&
               inventory.getTotalBoxes() != null &&
               inventory.getTotalBoxes() > 0 &&
               inventory.getTotalGrossWeight() != null &&
               inventory.getTotalGrossWeight().compareTo(BigDecimal.ZERO) > 0;
    }
}
