package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.RsOpSeaFcl;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.RsOpSeaFclService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 整柜海运服务Controller
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@RestController
@RequestMapping("/system/opseafcl")
public class RsOpSeaFclController extends BaseController {
    @Autowired
    private RsOpSeaFclService rsOpSeaFclService;

    /**
     * 查询整柜海运服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opseafcl:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsOpSeaFcl rsOpSeaFcl) {
        startPage();
        List<RsOpSeaFcl> list = rsOpSeaFclService.selectRsOpSeaFclList(rsOpSeaFcl);
        return getDataTable(list);
    }

    /**
     * 导出整柜海运服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opseafcl:export')")
    @Log(title = "整柜海运服务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsOpSeaFcl rsOpSeaFcl) {
        List<RsOpSeaFcl> list = rsOpSeaFclService.selectRsOpSeaFclList(rsOpSeaFcl);
        ExcelUtil<RsOpSeaFcl> util = new ExcelUtil<RsOpSeaFcl>(RsOpSeaFcl.class);
        util.exportExcel(response, list, "整柜海运服务数据");
    }

    /**
     * 获取整柜海运服务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:opseafcl:query')")
    @GetMapping(value = "/{seaFclId}")
    public AjaxResult getInfo(@PathVariable("seaFclId") Long seaFclId) {
        return AjaxResult.success(rsOpSeaFclService.selectRsOpSeaFclBySeaFclId(seaFclId));
    }

    /**
     * 新增整柜海运服务
     */
    @PreAuthorize("@ss.hasPermi('system:opseafcl:add')")
    @Log(title = "整柜海运服务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsOpSeaFcl rsOpSeaFcl) {
        return AjaxResult.success(rsOpSeaFclService.insertRsOpSeaFcl(rsOpSeaFcl));
    }

    /**
     * 修改整柜海运服务
     */
    @PreAuthorize("@ss.hasPermi('system:opseafcl:edit')")
    @Log(title = "整柜海运服务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsOpSeaFcl rsOpSeaFcl) {
        return toAjax(rsOpSeaFclService.updateRsOpSeaFcl(rsOpSeaFcl));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:opseafcl:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsOpSeaFcl rsOpSeaFcl) {
        rsOpSeaFcl.setUpdateBy(getUserId());
        return toAjax(rsOpSeaFclService.changeStatus(rsOpSeaFcl));
    }

    /**
     * 删除整柜海运服务
     */
    @PreAuthorize("@ss.hasPermi('system:opseafcl:remove')")
    @Log(title = "整柜海运服务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{seaFclIds}")
    public AjaxResult remove(@PathVariable Long[] seaFclIds) {
        return toAjax(rsOpSeaFclService.deleteRsOpSeaFclBySeaFclIds(seaFclIds));
    }
}
