package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasDistLine;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 航线Mapper接口
 *
 * <AUTHOR>
 * @date 2022-08-22
 */
@Mapper
public interface BasDistLineMapper {
    /**
     * 查询航线
     *
     * @param lineId 航线主键
     * @return 航线
     */
    BasDistLine selectBasDistLineByLineId(Long lineId);

    /**
     * 查询航线列表
     *
     * @param basDistLine 航线
     * @return 航线集合
     */
    List<BasDistLine> selectBasDistLineList(BasDistLine basDistLine);

    BasDistLine selectLine(String name);

    /**
     * 新增航线
     *
     * @param basDistLine 航线
     * @return 结果
     */
    int insertBasDistLine(BasDistLine basDistLine);

    /**
     * 修改航线
     *
     * @param basDistLine 航线
     * @return 结果
     */
    int updateBasDistLine(BasDistLine basDistLine);

    /**
     * 删除航线
     *
     * @param lineId 航线主键
     * @return 结果
     */
    int deleteBasDistLineByLineId(Long lineId);

    /**
     * 批量删除航线
     *
     * @param lineIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasDistLineByLineIds(Long[] lineIds);

    List<BasDistLine> selectChildrenLineById(Long lineId);

    void updateLineChildren(@Param("lines") List<BasDistLine> lines);

    void updateLineStatusNormal(Long[] lineIds);

    List<Long> queryParentSonByLineId(Long lineId);

}
