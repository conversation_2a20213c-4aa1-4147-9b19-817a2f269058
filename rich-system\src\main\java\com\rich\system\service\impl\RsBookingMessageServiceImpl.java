package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsBookingMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsBookingMessageMapper;
import com.rich.system.service.RsBookingMessageService;

/**
 * 记录提单信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@Service
public class RsBookingMessageServiceImpl implements RsBookingMessageService {
    @Autowired
    private RsBookingMessageMapper rsBookingMessageMapper;

    /**
     * 查询记录提单信息
     *
     * @param rsBookingMessageId 记录提单信息主键
     * @return 记录提单信息
     */
    @Override
    public RsBookingMessage selectRsBookingMessageByRsBookingMessageId(Long rsBookingMessageId) {
        return rsBookingMessageMapper.selectRsBookingMessageByRsBookingMessageId(rsBookingMessageId);
    }

    /**
     * 查询记录提单信息列表
     *
     * @param rsBookingMessage 记录提单信息
     * @return 记录提单信息
     */
    @Override
    public List<RsBookingMessage> selectRsBookingMessageList(RsBookingMessage rsBookingMessage) {
        return rsBookingMessageMapper.selectRsBookingMessageList(rsBookingMessage);
    }

    /**
     * 新增记录提单信息
     *
     * @param rsBookingMessage 记录提单信息
     * @return 结果
     */
    @Override
    public int insertRsBookingMessage(RsBookingMessage rsBookingMessage) {
        return rsBookingMessageMapper.insertRsBookingMessage(rsBookingMessage);
    }

    /**
     * 修改记录提单信息
     *
     * @param rsBookingMessage 记录提单信息
     * @return 结果
     */
    @Override
    public int updateRsBookingMessage(RsBookingMessage rsBookingMessage) {
        return rsBookingMessageMapper.updateRsBookingMessage(rsBookingMessage);
    }

    /**
     * 修改记录提单信息状态
     *
     * @param rsBookingMessage 记录提单信息
     * @return 记录提单信息
     */
    @Override
    public int changeStatus(RsBookingMessage rsBookingMessage) {
        return rsBookingMessageMapper.updateRsBookingMessage(rsBookingMessage);
    }

    /**
     * 批量删除记录提单信息
     *
     * @param rsBookingMessageIds 需要删除的记录提单信息主键
     * @return 结果
     */
    @Override
    public int deleteRsBookingMessageByRsBookingMessageIds(Long[] rsBookingMessageIds) {
        return rsBookingMessageMapper.deleteRsBookingMessageByRsBookingMessageIds(rsBookingMessageIds);
    }

    /**
     * 删除记录提单信息信息
     *
     * @param rsBookingMessageId 记录提单信息主键
     * @return 结果
     */
    @Override
    public int deleteRsBookingMessageByRsBookingMessageId(Long rsBookingMessageId) {
        return rsBookingMessageMapper.deleteRsBookingMessageByRsBookingMessageId(rsBookingMessageId);
    }
}
