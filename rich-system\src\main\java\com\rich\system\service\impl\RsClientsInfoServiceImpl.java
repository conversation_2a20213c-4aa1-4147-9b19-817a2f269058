package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsClientsInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsClientsInfoMapper;
import com.rich.system.service.RsClientsInfoService;

/**
 * 用来记录客户常用的信息，避免重复劳动、错漏Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Service
public class RsClientsInfoServiceImpl implements RsClientsInfoService {
    @Autowired
    private RsClientsInfoMapper rsClientsInfoMapper;

    /**
     * 查询用来记录客户常用的信息，避免重复劳动、错漏
     *
     * @param clientsInfoId 用来记录客户常用的信息，避免重复劳动、错漏主键
     * @return 用来记录客户常用的信息，避免重复劳动、错漏
     */
    @Override
    public RsClientsInfo selectRsClientsInfoByClientsInfoId(Long clientsInfoId) {
        return rsClientsInfoMapper.selectRsClientsInfoByClientsInfoId(clientsInfoId);
    }

    /**
     * 查询用来记录客户常用的信息，避免重复劳动、错漏列表
     *
     * @param rsClientsInfo 用来记录客户常用的信息，避免重复劳动、错漏
     * @return 用来记录客户常用的信息，避免重复劳动、错漏
     */
    @Override
    public List<RsClientsInfo> selectRsClientsInfoList(RsClientsInfo rsClientsInfo) {
        return rsClientsInfoMapper.selectRsClientsInfoList(rsClientsInfo);
    }

    /**
     * 新增用来记录客户常用的信息，避免重复劳动、错漏
     *
     * @param rsClientsInfo 用来记录客户常用的信息，避免重复劳动、错漏
     * @return 结果
     */
    @Override
    public int insertRsClientsInfo(RsClientsInfo rsClientsInfo) {
        return rsClientsInfoMapper.insertRsClientsInfo(rsClientsInfo);
    }

    /**
     * 修改用来记录客户常用的信息，避免重复劳动、错漏
     *
     * @param rsClientsInfo 用来记录客户常用的信息，避免重复劳动、错漏
     * @return 结果
     */
    @Override
    public int updateRsClientsInfo(RsClientsInfo rsClientsInfo) {
        return rsClientsInfoMapper.updateRsClientsInfo(rsClientsInfo);
    }

    /**
     * 修改用来记录客户常用的信息，避免重复劳动、错漏状态
     *
     * @param rsClientsInfo 用来记录客户常用的信息，避免重复劳动、错漏
     * @return 用来记录客户常用的信息，避免重复劳动、错漏
     */
    @Override
    public int changeStatus(RsClientsInfo rsClientsInfo) {
        return rsClientsInfoMapper.updateRsClientsInfo(rsClientsInfo);
    }

    /**
     * 批量删除用来记录客户常用的信息，避免重复劳动、错漏
     *
     * @param clientsInfoIds 需要删除的用来记录客户常用的信息，避免重复劳动、错漏主键
     * @return 结果
     */
    @Override
    public int deleteRsClientsInfoByClientsInfoIds(Long[] clientsInfoIds) {
        return rsClientsInfoMapper.deleteRsClientsInfoByClientsInfoIds(clientsInfoIds);
    }

    /**
     * 删除用来记录客户常用的信息，避免重复劳动、错漏信息
     *
     * @param clientsInfoId 用来记录客户常用的信息，避免重复劳动、错漏主键
     * @return 结果
     */
    @Override
    public int deleteRsClientsInfoByClientsInfoId(Long clientsInfoId) {
        return rsClientsInfoMapper.deleteRsClientsInfoByClientsInfoId(clientsInfoId);
    }
}
