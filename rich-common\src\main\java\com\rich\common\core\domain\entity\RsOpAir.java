package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.List;

/**
 * 空运服务对象 rs_op_air
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
public class RsOpAir extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long airId;

    /**
     * 所属服务实例id ,
     */
    @Excel(name = "所属服务实例id ,")
    private Long serviceId;

    /**
     * 所属服务类型id ,
     */
    @Excel(name = "所属服务类型id ,")
    private Long sqdServiceTypeId;

    /**
     * 所属操作单号 ,
     */
    @Excel(name = "所属操作单号 ,")
    private String sqdRctNo;

    private RsServiceInstances rsServiceInstances;

    private List<RsCharge> rsChargeList;

    private List<RsDoc> rsDocList;

    private List<RsOpLog> rsOpLogList;
    private String sqdServiceDetailsCode;
    private String sqdPsaNo;
    private String soNo;
    private String blNo;
    private Long carrierId;
    private String firstVessel;
    private String basicVessel;
    private String inquiryScheduleSummary;
    private String cvClosingTime;
    private String etd;
    private String basicFinalGateinTime;
    private String eta;
    private String siClosingTime;
    private String sqdAmsEnsPostStatus;
    private String podEta;
    private String basicEtd;
    private String destinationPortEta;
    private String payable;
    private BigDecimal payableRMB;
    private BigDecimal payableUSD;
    private BigDecimal payableUSDTax;
    private BigDecimal payableRMBTax;

    public BigDecimal getPayableRMBTax() {
        return payableRMBTax;
    }

    public void setPayableRMBTax(BigDecimal payableRMBTax) {
        this.payableRMBTax = payableRMBTax;
    }

    public BigDecimal getPayableUSDTax() {
        return payableUSDTax;
    }

    public void setPayableUSDTax(BigDecimal payableUSDTax) {
        this.payableUSDTax = payableUSDTax;
    }

    public BigDecimal getPayableUSD() {
        return payableUSD;
    }

    public void setPayableUSD(BigDecimal payableUSD) {
        this.payableUSD = payableUSD;
    }

    public BigDecimal getPayableRMB() {
        return payableRMB;
    }

    public void setPayableRMB(BigDecimal payableRMB) {
        this.payableRMB = payableRMB;
    }

    public String getPayable() {
        return payable;
    }

    public void setPayable(String payable) {
        this.payable = payable;
    }

    public String getDestinationPortEta() {
        return destinationPortEta;
    }

    public void setDestinationPortEta(String destinationPortEta) {
        this.destinationPortEta = destinationPortEta;
    }

    public String getBasicEtd() {
        return basicEtd;
    }

    public void setBasicEtd(String basicEtd) {
        this.basicEtd = basicEtd;
    }

    public String getPodEta() {
        return podEta;
    }

    public void setPodEta(String podEta) {
        this.podEta = podEta;
    }

    public String getSqdAmsEnsPostStatus() {
        return sqdAmsEnsPostStatus;
    }

    public void setSqdAmsEnsPostStatus(String sqdAmsEnsPostStatus) {
        this.sqdAmsEnsPostStatus = sqdAmsEnsPostStatus;
    }

    public String getSiClosingTime() {
        return siClosingTime;
    }

    public void setSiClosingTime(String siClosingTime) {
        this.siClosingTime = siClosingTime;
    }

    public String getEta() {
        return eta;
    }

    public void setEta(String eta) {
        this.eta = eta;
    }

    public String getBasicFinalGateinTime() {
        return basicFinalGateinTime;
    }

    public void setBasicFinalGateinTime(String basicFinalGateinTime) {
        this.basicFinalGateinTime = basicFinalGateinTime;
    }

    public String getEtd() {
        return etd;
    }

    public void setEtd(String etd) {
        this.etd = etd;
    }

    public String getCvClosingTime() {
        return cvClosingTime;
    }

    public void setCvClosingTime(String cvClosingTime) {
        this.cvClosingTime = cvClosingTime;
    }

    public String getInquiryScheduleSummary() {
        return inquiryScheduleSummary;
    }

    public void setInquiryScheduleSummary(String inquiryScheduleSummary) {
        this.inquiryScheduleSummary = inquiryScheduleSummary;
    }

    public String getBasicVessel() {
        return basicVessel;
    }

    public void setBasicVessel(String basicVessel) {
        this.basicVessel = basicVessel;
    }

    public String getFirstVessel() {
        return firstVessel;
    }

    public void setFirstVessel(String firstVessel) {
        this.firstVessel = firstVessel;
    }

    public Long getCarrierId() {
        return carrierId;
    }

    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    public String getBlNo() {
        return blNo;
    }

    public void setBlNo(String blNo) {
        this.blNo = blNo;
    }

    public String getSoNo() {
        return soNo;
    }

    public void setSoNo(String soNo) {
        this.soNo = soNo;
    }

    public String getSqdPsaNo() {
        return sqdPsaNo;
    }

    public void setSqdPsaNo(String sqdPsaNo) {
        this.sqdPsaNo = sqdPsaNo;
    }

    public String getSqdServiceDetailsCode() {
        return sqdServiceDetailsCode;
    }

    public void setSqdServiceDetailsCode(String sqdServiceDetailsCode) {
        this.sqdServiceDetailsCode = sqdServiceDetailsCode;
    }

    public List<RsOpLog> getRsOpLogList() {
        return rsOpLogList;
    }

    public void setRsOpLogList(List<RsOpLog> rsOpLogList) {
        this.rsOpLogList = rsOpLogList;
    }

    public RsServiceInstances getRsServiceInstances() {
        return rsServiceInstances;
    }

    public void setRsServiceInstances(RsServiceInstances rsServiceInstances) {
        this.rsServiceInstances = rsServiceInstances;
    }

    public List<RsCharge> getRsChargeList() {
        return rsChargeList;
    }

    public void setRsChargeList(List<RsCharge> rsChargeList) {
        this.rsChargeList = rsChargeList;
    }

    public List<RsDoc> getRsDocList() {
        return rsDocList;
    }

    public void setRsDocList(List<RsDoc> rsDocList) {
        this.rsDocList = rsDocList;
    }

    public Long getAirId() {
        return airId;
    }

    public void setAirId(Long airId) {
        this.airId = airId;
    }

    public Long getServiceId() {
        return serviceId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    public Long getSqdServiceTypeId() {
        return sqdServiceTypeId;
    }

    public void setSqdServiceTypeId(Long sqdServiceTypeId) {
        this.sqdServiceTypeId = sqdServiceTypeId;
    }

    public String getSqdRctNo() {
        return sqdRctNo;
    }

    public void setSqdRctNo(String sqdRctNo) {
        this.sqdRctNo = sqdRctNo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("airId", getAirId())
                .append("serviceId", getServiceId())
                .append("sqdServiceTypeId", getSqdServiceTypeId())
                .append("sqdRctNo", getSqdRctNo())
                .toString();
    }
}
