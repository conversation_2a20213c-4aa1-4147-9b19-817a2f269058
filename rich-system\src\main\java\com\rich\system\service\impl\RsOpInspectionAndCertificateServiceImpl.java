package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpInspectionAndCertificate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsOpInspectionAndCertificateMapper;
import com.rich.system.service.RsOpInspectionAndCertificateService;

/**
 * 检验与证书服务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Service
public class RsOpInspectionAndCertificateServiceImpl implements RsOpInspectionAndCertificateService {
    @Autowired
    private RsOpInspectionAndCertificateMapper rsOpInspectionAndCertificateMapper;

    /**
     * 查询检验与证书服务
     *
     * @param inspectionAndCertificateId 检验与证书服务主键
     * @return 检验与证书服务
     */
    @Override
    public RsOpInspectionAndCertificate selectRsOpInspectionAndCertificateByInspectionAndCertificateId(Long inspectionAndCertificateId) {
        return rsOpInspectionAndCertificateMapper.selectRsOpInspectionAndCertificateByInspectionAndCertificateId(inspectionAndCertificateId);
    }

    /**
     * 查询检验与证书服务列表
     *
     * @param rsOpInspectionAndCertificate 检验与证书服务
     * @return 检验与证书服务
     */
    @Override
    public List<RsOpInspectionAndCertificate> selectRsOpInspectionAndCertificateList(RsOpInspectionAndCertificate rsOpInspectionAndCertificate) {
        return rsOpInspectionAndCertificateMapper.selectRsOpInspectionAndCertificateList(rsOpInspectionAndCertificate);
    }

    /**
     * 新增检验与证书服务
     *
     * @param rsOpInspectionAndCertificate 检验与证书服务
     * @return 结果
     */
    @Override
    public int insertRsOpInspectionAndCertificate(RsOpInspectionAndCertificate rsOpInspectionAndCertificate) {
        return rsOpInspectionAndCertificateMapper.insertRsOpInspectionAndCertificate(rsOpInspectionAndCertificate);
    }

    /**
     * 修改检验与证书服务
     *
     * @param rsOpInspectionAndCertificate 检验与证书服务
     * @return 结果
     */
    @Override
    public int updateRsOpInspectionAndCertificate(RsOpInspectionAndCertificate rsOpInspectionAndCertificate) {
        return rsOpInspectionAndCertificateMapper.updateRsOpInspectionAndCertificate(rsOpInspectionAndCertificate);
    }

    /**
     * 修改检验与证书服务状态
     *
     * @param rsOpInspectionAndCertificate 检验与证书服务
     * @return 检验与证书服务
     */
    @Override
    public int changeStatus(RsOpInspectionAndCertificate rsOpInspectionAndCertificate) {
        return rsOpInspectionAndCertificateMapper.updateRsOpInspectionAndCertificate(rsOpInspectionAndCertificate);
    }

    /**
     * 批量删除检验与证书服务
     *
     * @param inspectionAndCertificateIds 需要删除的检验与证书服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpInspectionAndCertificateByInspectionAndCertificateIds(Long[] inspectionAndCertificateIds) {
        return rsOpInspectionAndCertificateMapper.deleteRsOpInspectionAndCertificateByInspectionAndCertificateIds(inspectionAndCertificateIds);
    }

    /**
     * 删除检验与证书服务信息
     *
     * @param inspectionAndCertificateId 检验与证书服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpInspectionAndCertificateByInspectionAndCertificateId(Long inspectionAndCertificateId) {
        return rsOpInspectionAndCertificateMapper.deleteRsOpInspectionAndCertificateByInspectionAndCertificateId(inspectionAndCertificateId);
    }
}
