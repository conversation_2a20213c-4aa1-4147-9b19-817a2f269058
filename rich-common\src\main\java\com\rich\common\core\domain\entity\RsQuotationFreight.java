package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 报价详细对象 rs_quotation_freight
 *
 * <AUTHOR>
 * @date 2023-05-12
 */
public class RsQuotationFreight extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 报价ID
     */
    @Excel(name = "报价ID")
    private Long quotationId;

    /**
     * 类型
     */
    @Excel(name = "类型")
    private Integer typeId;

    private Long serviceTypeId;

    /**
     * 具体费用ID
     */
    @Excel(name = "具体费用ID")
    private Long chargeId;

    private String charge;
    private String chargeEn;

    /**
     * 费用ID
     */
    @Excel(name = "费用ID")
    private Long freightId;

    private String company;
    private Long companyId;

    private String richNo;

    private Integer chargeOrderNum;
    private Integer chargeTypeOrderNum;

    /**
     * 附加费ID
     */
    @Excel(name = "附加费ID")
    private Long localId;


    /**
     * 策略
     */
    @Excel(name = "策略")
    private String strategyId;

    /**
     * 报价
     */
    @Excel(name = "报价")
    private BigDecimal quotationRate;

    /**
     * 币种
     */

    private String QuotationCurrencyCode;
    @Excel(name = "币种")
    private String quotationCurrency;

    /**
     * 数量
     */
    @Excel(name = "数量")
    private Integer quotationAmount;

    private BigDecimal quotationTotal;

    /**
     * 成本
     */
    @Excel(name = "成本")
    private BigDecimal inquiryRate;

    /**
     * 成本币种
     */

    private Long inquiryCurrencyCode;
    @Excel(name = "成本币种")
    private String costCurrency;

    /**
     * 成本数量
     */
    @Excel(name = "成本数量")
    private Integer inquiryAmount;

    private BigDecimal costTotal;

    /**
     * 单位
     */
    @Excel(name = "单位")
    private String unitCode;


    private String unit;
    /**
     * 本位币汇率
     */
    @Excel(name = "本位币汇率")
    private BigDecimal baseCurrencyRate;

    /**
     * 汇率
     */
    @Excel(name = "汇率")
    private BigDecimal exchangeRate;

    /**
     * 是否含税
     */
    @Excel(name = "是否含税")
    private Integer isTaxIncluded;

    /**
     * 税率
     */
    @Excel(name = "税率")
    private BigDecimal taxRate;

    /**
     * 利润
     */
    @Excel(name = "利润")
    private BigDecimal profit;

    private String location;
    private String locationEn;
    private String loading;
    private String loadingEn;

    public Long getServiceTypeId() {
        return serviceTypeId;
    }

    public void setServiceTypeId(Long serviceTypeId) {
        this.serviceTypeId = serviceTypeId;
    }

    public BigDecimal getQuotationTotal() {
        return quotationTotal;
    }

    public void setQuotationTotal(BigDecimal quotationTotal) {
        this.quotationTotal = quotationTotal;
    }

    public BigDecimal getCostTotal() {
        return costTotal;
    }

    public void setCostTotal(BigDecimal costTotal) {
        this.costTotal = costTotal;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getInquiryCurrencyCode() {
        return inquiryCurrencyCode;
    }

    public void setInquiryCurrencyCode(Long inquiryCurrencyCode) {
        this.inquiryCurrencyCode = inquiryCurrencyCode;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getRichNo() {
        return richNo;
    }

    public void setRichNo(String richNo) {
        this.richNo = richNo;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getLocationEn() {
        return locationEn;
    }

    public void setLocationEn(String locationEn) {
        this.locationEn = locationEn;
    }

    public String getLoading() {
        return loading;
    }

    public void setLoading(String loading) {
        this.loading = loading;
    }

    public String getLoadingEn() {
        return loadingEn;
    }

    public void setLoadingEn(String loadingEn) {
        this.loadingEn = loadingEn;
    }

    public String getChargeEn() {
        return chargeEn;
    }

    public void setChargeEn(String chargeEn) {
        this.chargeEn = chargeEn;
    }

    public Integer getChargeTypeOrderNum() {
        return chargeTypeOrderNum;
    }

    public void setChargeTypeOrderNum(Integer chargeTypeOrderNum) {
        this.chargeTypeOrderNum = chargeTypeOrderNum;
    }

    public Integer getChargeOrderNum() {
        return chargeOrderNum;
    }

    public void setChargeOrderNum(Integer chargeOrderNum) {
        this.chargeOrderNum = chargeOrderNum;
    }

    public String getCharge() {
        return charge;
    }

    public void setCharge(String charge) {
        this.charge = charge;
    }

    public String getQuotationCurrency() {
        return quotationCurrency;
    }

    public void setQuotationCurrency(String quotationCurrency) {
        this.quotationCurrency = quotationCurrency;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Long getChargeId() {
        return chargeId;
    }

    public void setChargeId(Long chargeId) {
        this.chargeId = chargeId;
    }

    public Long getLocalId() {
        return localId;
    }

    public void setLocalId(Long localId) {
        this.localId = localId;
    }

    public void setQuotationId(Long quotationId) {
        this.quotationId = quotationId;
    }

    public Long getQuotationId() {
        return quotationId;
    }

    public void setTypeId(Integer typeId) {
        this.typeId = typeId;
    }

    public Integer getTypeId() {
        return typeId;
    }

    public void setFreightId(Long freightId) {
        this.freightId = freightId;
    }

    public Long getFreightId() {
        return freightId;
    }

    public void setStrategyId(String strategyId) {
        this.strategyId = strategyId;
    }

    public String getStrategyId() {
        return strategyId;
    }

    public BigDecimal getQuotationRate() {
        return quotationRate;
    }

    public void setQuotationRate(BigDecimal quotationRate) {
        this.quotationRate = quotationRate;
    }

    public String getQuotationCurrencyCode() {
        return QuotationCurrencyCode;
    }

    public void setQuotationCurrencyCode(String QuotationCurrencyCode) {
        this.QuotationCurrencyCode = QuotationCurrencyCode;
    }

    public void setQuotationAmount(Integer quotationAmount) {
        this.quotationAmount = quotationAmount;
    }

    public Integer getQuotationAmount() {
        return quotationAmount;
    }

    public BigDecimal getInquiryRate() {
        return inquiryRate;
    }

    public void setInquiryRate(BigDecimal inquiryRate) {
        this.inquiryRate = inquiryRate;
    }

    public void setCostCurrency(String costCurrency) {
        this.costCurrency = costCurrency;
    }

    public String getCostCurrency() {
        return costCurrency;
    }

    public Integer getInquiryAmount() {
        return inquiryAmount;
    }

    public void setInquiryAmount(Integer inquiryAmount) {
        this.inquiryAmount = inquiryAmount;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public BigDecimal getBaseCurrencyRate() {
        return baseCurrencyRate;
    }

    public void setBaseCurrencyRate(BigDecimal baseCurrencyRate) {
        this.baseCurrencyRate = baseCurrencyRate;
    }

    public void setExchangeRate(BigDecimal exchangeRate) {
        this.exchangeRate = exchangeRate;
    }

    public BigDecimal getExchangeRate() {
        return exchangeRate;
    }

    public Integer getIsTaxIncluded() {
        return isTaxIncluded;
    }

    public void setIsTaxIncluded(Integer isTaxIncluded) {
        this.isTaxIncluded = isTaxIncluded;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setProfit(BigDecimal profit) {
        this.profit = profit;
    }

    public BigDecimal getProfit() {
        return profit;
    }

}
