package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsCommonUsed;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsCommonUsedService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 常用信息Controller
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
@RestController
@RequestMapping("/system/commonused")
public class RsCommonUsedController extends BaseController
{
    @Autowired
    private RsCommonUsedService rsCommonUsedService;

    /**
     * 查询常用信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:commonused:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsCommonUsed rsCommonUsed)
    {
        startPage();
        List<RsCommonUsed> list = rsCommonUsedService.selectRsCommonUsedList(rsCommonUsed);
        return getDataTable(list);
    }

    /**
     * 导出常用信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:commonused:export')")
    @Log(title = "常用信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsCommonUsed rsCommonUsed)
    {
        List<RsCommonUsed> list = rsCommonUsedService.selectRsCommonUsedList(rsCommonUsed);
        ExcelUtil<RsCommonUsed> util = new ExcelUtil<RsCommonUsed>(RsCommonUsed.class);
        util.exportExcel(response, list, "常用信息数据");
    }

    /**
     * 获取常用信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:commonused:query')")
    @GetMapping(value = "/{commonUsedId}")
    public AjaxResult getInfo(@PathVariable("commonUsedId") Long commonUsedId)
    {
        return AjaxResult.success(rsCommonUsedService.selectRsCommonUsedByCommonUsedId(commonUsedId));
    }

    /**
     * 新增常用信息
     */
    @PreAuthorize("@ss.hasPermi('system:commonused:add')")
    @Log(title = "常用信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsCommonUsed rsCommonUsed)
    {
        return toAjax(rsCommonUsedService.insertRsCommonUsed(rsCommonUsed));
    }

    /**
     * 修改常用信息
     */
    @PreAuthorize("@ss.hasPermi('system:commonused:edit')")
    @Log(title = "常用信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsCommonUsed rsCommonUsed)
    {
        return toAjax(rsCommonUsedService.updateRsCommonUsed(rsCommonUsed));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:commonused:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsCommonUsed rsCommonUsed) {
        rsCommonUsed.setUpdateBy(getUserId());
        return toAjax(rsCommonUsedService.changeStatus(rsCommonUsed));
    }

    /**
     * 删除常用信息
     */
    @PreAuthorize("@ss.hasPermi('system:commonused:remove')")
    @Log(title = "常用信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{commonUsedIds}")
    public AjaxResult remove(@PathVariable Long[] commonUsedIds)
    {
        return toAjax(rsCommonUsedService.deleteRsCommonUsedByCommonUsedIds(commonUsedIds));
    }
}
