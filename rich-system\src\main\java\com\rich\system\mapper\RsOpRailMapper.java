package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpAir;
import com.rich.common.core.domain.entity.RsOpRail;
import com.rich.common.core.domain.entity.RsOpRailFCL;
import com.rich.common.core.domain.entity.RsOpRailLCL;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 铁路服务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Mapper
public interface RsOpRailMapper {
    /**
     * 查询铁路服务
     *
     * @param railId 铁路服务主键
     * @return 铁路服务
     */
    RsOpRail selectRsOpRailByRailId(Long railId);

    /**
     * 查询铁路服务列表
     *
     * @param rsOpRail 铁路服务
     * @return 铁路服务集合
     */
    List<RsOpRail> selectRsOpRailList(RsOpRail rsOpRail);

    /**
     * 新增铁路服务
     *
     * @param rsOpRail 铁路服务
     * @return 结果
     */
    int insertRsOpRail(RsOpRail rsOpRail);

    int insertRsOpRail(RsOpRailFCL rsOpRailFCL);

    int insertRsOpRail(RsOpRailLCL rsOpRailLCL);

    /**
     * 修改铁路服务
     *
     * @param rsOpRail 铁路服务
     * @return 结果
     */
    int updateRsOpRail(RsOpRail rsOpRail);

    int updateRsOpRail(RsOpRailFCL rsOpRailFCL);

    int updateRsOpRail(RsOpRailLCL rsOpRailLCL);

    /**
     * 删除铁路服务
     *
     * @param railId 铁路服务主键
     * @return 结果
     */
    int deleteRsOpRailByRailId(Long railId);

    /**
     * 批量删除铁路服务
     *
     * @param railIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsOpRailByRailIds(Long[] railIds);

    //    RsOpRail selectRsOpRailByRctId(Long rctId);
    RsOpRailFCL selectRsOpRailFclByRctId(@Param("rctId") Long rctId, @Param("sqdServiceTypeId") Long sqdServiceTypeId);

    RsOpRailLCL selectRsOpRailLclByRctId(@Param("rctId") Long rctId, @Param("sqdServiceTypeId") Long sqdServiceTypeId);
}
