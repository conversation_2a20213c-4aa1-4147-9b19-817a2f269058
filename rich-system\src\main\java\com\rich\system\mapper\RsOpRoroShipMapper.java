package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpRoroShip;
import org.apache.ibatis.annotations.Mapper;

/**
 * 滚装船服务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Mapper
public interface RsOpRoroShipMapper {
    /**
     * 查询滚装船服务
     *
     * @param roroShipId 滚装船服务主键
     * @return 滚装船服务
     */
    RsOpRoroShip selectRsOpRoroShipByRoroShipId(Long roroShipId);

    /**
     * 查询滚装船服务列表
     *
     * @param rsOpRoroShip 滚装船服务
     * @return 滚装船服务集合
     */
    List<RsOpRoroShip> selectRsOpRoroShipList(RsOpRoroShip rsOpRoroShip);

    /**
     * 新增滚装船服务
     *
     * @param rsOpRoroShip 滚装船服务
     * @return 结果
     */
    int insertRsOpRoroShip(RsOpRoroShip rsOpRoroShip);

    /**
     * 修改滚装船服务
     *
     * @param rsOpRoroShip 滚装船服务
     * @return 结果
     */
    int updateRsOpRoroShip(RsOpRoroShip rsOpRoroShip);

    /**
     * 删除滚装船服务
     *
     * @param roroShipId 滚装船服务主键
     * @return 结果
     */
    int deleteRsOpRoroShipByRoroShipId(Long roroShipId);

    /**
     * 批量删除滚装船服务
     *
     * @param roroShipIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsOpRoroShipByRoroShipIds(Long[] roroShipIds);

    RsOpRoroShip selectRsOpRoroByRctId(Long rctId);
}
