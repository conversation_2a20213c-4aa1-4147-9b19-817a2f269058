package com.rich.system.domain;

import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 【请填写功能名称】对象 mid_unit
 *
 * <AUTHOR>
 * @date 2022-12-05
 */
public class MidUnit extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long unitId;

    private Long belongId;

    private String belongTo;

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public Long getBelongId() {
        return belongId;
    }

    public void setBelongId(Long belongId) {
        this.belongId = belongId;
    }

    public String getBelongTo() {
        return belongTo;
    }

    public void setBelongTo(String belongTo) {
        this.belongTo = belongTo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("unitId", getUnitId())
                .append("belongId", getBelongId())
                .append("belongTo", getBelongTo())
                .toString();
    }
}
