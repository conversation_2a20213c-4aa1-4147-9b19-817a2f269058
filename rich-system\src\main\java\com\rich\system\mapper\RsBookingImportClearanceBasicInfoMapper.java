package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsBookingImportClearanceBasicInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 订舱单进口清关基础信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-15
 */
@Mapper
public interface RsBookingImportClearanceBasicInfoMapper {
    /**
     * 查询订舱单进口清关基础信息
     *
     * @return 订舱单进口清关基础信息
     */
    RsBookingImportClearanceBasicInfo selectRsBookingImportClearanceBasicInfoByBookingId(Long bookingId);

    /**
     * 查询订舱单进口清关基础信息列表
     *
     * @param rsBookingImportClearanceBasicInfo 订舱单进口清关基础信息
     * @return 订舱单进口清关基础信息集合
     */
    List<RsBookingImportClearanceBasicInfo> selectRsBookingImportClearanceBasicInfoList(RsBookingImportClearanceBasicInfo rsBookingImportClearanceBasicInfo);

    /**
     * 新增订舱单进口清关基础信息
     *
     * @param rsBookingImportClearanceBasicInfo 订舱单进口清关基础信息
     * @return 结果
     */
    int insertRsBookingImportClearanceBasicInfo(RsBookingImportClearanceBasicInfo rsBookingImportClearanceBasicInfo);

    /**
     * 修改订舱单进口清关基础信息
     *
     * @param rsBookingImportClearanceBasicInfo 订舱单进口清关基础信息
     * @return 结果
     */
    int updateRsBookingImportClearanceBasicInfo(RsBookingImportClearanceBasicInfo rsBookingImportClearanceBasicInfo);

    /**
     * 删除订舱单进口清关基础信息
     *
     * @return 结果
     */
    int deleteRsBookingImportClearanceBasicInfoById(Long bookingId);

    /**
     * 批量删除订舱单进口清关基础信息
     *
     * @param importClearanceIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsBookingImportClearanceBasicInfoByIds(Long[] bookingIds);
}
