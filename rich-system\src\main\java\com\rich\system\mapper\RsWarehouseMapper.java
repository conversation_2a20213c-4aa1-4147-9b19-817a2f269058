package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsWarehouse;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 仓储服务Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@Mapper
public interface RsWarehouseMapper {
    /**
     * 查询仓储服务
     *
     * @param warehouseId 仓储服务主键
     * @return 仓储服务
     */
    RsWarehouse selectRsWarehouseByWarehouseId(Long warehouseId);

    /**
     * 查询仓储服务列表
     *
     * @param rsWarehouse 仓储服务
     * @return 仓储服务集合
     */
    List<RsWarehouse> selectRsWarehouseList(RsWarehouse rsWarehouse);

    /**
     * 新增仓储服务
     *
     * @param rsWarehouse 仓储服务
     * @return 结果
     */
    int insertRsWarehouse(RsWarehouse rsWarehouse);

    /**
     * 修改仓储服务
     *
     * @param rsWarehouse 仓储服务
     * @return 结果
     */
    int updateRsWarehouse(RsWarehouse rsWarehouse);

    /**
     * 删除仓储服务
     *
     * @param warehouseId 仓储服务主键
     * @return 结果
     */
    int deleteRsWarehouseByWarehouseId(Long warehouseId);

    /**
     * 批量删除仓储服务
     *
     * @param warehouseIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsWarehouseByWarehouseIds(Long[] warehouseIds);
}
