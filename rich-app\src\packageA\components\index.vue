<template>
  <view class="content">
    <text class="title">组件分包入口页面</text>
    <text class="desc">此页面仅用于满足分包要求</text>
  </view>
</template>

<script>
export default {
  data() {
    return {};
  },
  onLoad() {

  }
}
</script>

<style>
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
}

.title {
  font-size: 18px;
  color: #333;
  margin-bottom: 20px;
}

.desc {
  font-size: 14px;
  color: #666;
  text-align: center;
}
</style>