package com.rich.common.core.domain.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

/**
 * 发送状态对象 bas_sending_status
 * 
 * <AUTHOR>
 * @date 2023-06-16
 */
public class BasSendingStatus extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 发送状态 */
    private Long sendingStatusId;

    /** 简称 */
    @Excel(name = "简称")
    private String sendingStatusShortName;

    /** 中文名 */
    @Excel(name = "中文名")
    private String sendingStatusLocalName;

    /** 英文名 */
    @Excel(name = "英文名")
    private String sendingStatusEnName;

    /** 排序 */
    @Excel(name = "排序")
    private Integer orderNum;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 删除人 */
    private String deleteBy;

    /** 删除时间 */
    private Date deleteTime;

    /** 数据状态（-1:删除或不可用，0：正常） */
    private String deleteStatus;

    public void setSendingStatusId(Long sendingStatusId) 
    {
        this.sendingStatusId = sendingStatusId;
    }

    public Long getSendingStatusId() 
    {
        return sendingStatusId;
    }
    public void setSendingStatusShortName(String sendingStatusShortName) 
    {
        this.sendingStatusShortName = sendingStatusShortName;
    }

    public String getSendingStatusShortName() 
    {
        return sendingStatusShortName;
    }
    public void setSendingStatusLocalName(String sendingStatusLocalName) 
    {
        this.sendingStatusLocalName = sendingStatusLocalName;
    }

    public String getSendingStatusLocalName() 
    {
        return sendingStatusLocalName;
    }
    public void setSendingStatusEnName(String sendingStatusEnName) 
    {
        this.sendingStatusEnName = sendingStatusEnName;
    }

    public String getSendingStatusEnName() 
    {
        return sendingStatusEnName;
    }
    public void setOrderNum(Integer orderNum) 
    {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() 
    {
        return orderNum;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
}
