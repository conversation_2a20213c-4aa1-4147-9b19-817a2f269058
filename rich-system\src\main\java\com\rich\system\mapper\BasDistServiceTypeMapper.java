package com.rich.system.mapper;


import com.rich.common.core.domain.entity.BasDistServiceType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 服务类型Mapper接口
 *
 * <AUTHOR>
 * @date 2022-08-29
 */
@Mapper
public interface BasDistServiceTypeMapper {
    /**
     * 查询服务类型
     *
     * @param serviceTypeId 服务类型主键
     * @return 服务类型
     */
    BasDistServiceType selectBasDistServiceTypeByServiceTypeId(Long serviceTypeId);

    /**
     * 查询服务类型列表
     *
     * @param basDistServiceType 服务类型
     * @return 服务类型集合
     */
    List<BasDistServiceType> selectBasDistServiceTypeList(BasDistServiceType basDistServiceType);

    /**
     * 新增服务类型
     *
     * @param basDistServiceType 服务类型
     * @return 结果
     */
    int insertBasDistServiceType(BasDistServiceType basDistServiceType);

    /**
     * 修改服务类型
     *
     * @param basDistServiceType 服务类型
     * @return 结果
     */
    int updateBasDistServiceType(BasDistServiceType basDistServiceType);

    /**
     * 删除服务类型
     *
     * @param serviceTypeId 服务类型主键
     * @return 结果
     */
    int deleteBasDistServiceTypeByServiceTypeId(Long serviceTypeId);

    /**
     * 批量删除服务类型
     *
     * @param serviceTypeIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasDistServiceTypeByServiceTypeIds(Long[] serviceTypeIds);

    List<BasDistServiceType> selectChildrenServiceTypeById(Long serviceTypeId);

    void updateServiceTypeChildren(@Param("serviceTypes") List<BasDistServiceType> children);

    void updateServiceTypeStatusNormal(Long[] serviceTypeIds);

}
