package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsOpWarehouse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsOpWarehouseService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 仓储服务Controller
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@RestController
@RequestMapping("/system/opwarehouse")
public class RsOpWarehouseController extends BaseController {
    @Autowired
    private RsOpWarehouseService rsOpWarehouseService;

    /**
     * 查询仓储服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opwarehouse:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsOpWarehouse rsOpWarehouse) {
        startPage();
        List<RsOpWarehouse> list = rsOpWarehouseService.selectRsOpWarehouseList(rsOpWarehouse);
        return getDataTable(list);
    }

    /**
     * 导出仓储服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opwarehouse:export')")
    @Log(title = "仓储服务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsOpWarehouse rsOpWarehouse) {
        List<RsOpWarehouse> list = rsOpWarehouseService.selectRsOpWarehouseList(rsOpWarehouse);
        ExcelUtil<RsOpWarehouse> util = new ExcelUtil<RsOpWarehouse>(RsOpWarehouse.class);
        util.exportExcel(response, list, "仓储服务数据");
    }

    /**
     * 获取仓储服务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:opwarehouse:query')")
    @GetMapping(value = "/{warehouseId}")
    public AjaxResult getInfo(@PathVariable("warehouseId") Long warehouseId) {
        return AjaxResult.success(rsOpWarehouseService.selectRsOpWarehouseByWarehouseId(warehouseId));
    }

    /**
     * 新增仓储服务
     */
    @PreAuthorize("@ss.hasPermi('system:opwarehouse:add')")
    @Log(title = "仓储服务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsOpWarehouse rsOpWarehouse) {
        return toAjax(rsOpWarehouseService.insertRsOpWarehouse(rsOpWarehouse));
    }

    /**
     * 修改仓储服务
     */
    @PreAuthorize("@ss.hasPermi('system:opwarehouse:edit')")
    @Log(title = "仓储服务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsOpWarehouse rsOpWarehouse) {
        return toAjax(rsOpWarehouseService.updateRsOpWarehouse(rsOpWarehouse));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:opwarehouse:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsOpWarehouse rsOpWarehouse) {
        rsOpWarehouse.setUpdateBy(getUserId());
        return toAjax(rsOpWarehouseService.changeStatus(rsOpWarehouse));
    }

    /**
     * 删除仓储服务
     */
    @PreAuthorize("@ss.hasPermi('system:opwarehouse:remove')")
    @Log(title = "仓储服务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{warehouseIds}")
    public AjaxResult remove(@PathVariable Long[] warehouseIds) {
        return toAjax(rsOpWarehouseService.deleteRsOpWarehouseByWarehouseIds(warehouseIds));
    }
}
