package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.ExtStaff;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.ExtStaffService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 外部员工Controller
 *
 * <AUTHOR>
 * @date 2022-09-27
 */
@RestController
@RequestMapping("/system/extStaff")
public class ExtStaffController extends BaseController {

    @Autowired
    private ExtStaffService extStaffService;

    /**
     * 查询外部员工列表
     */
    @GetMapping("/list")
    public AjaxResult list(ExtStaff extStaff) {
        return AjaxResult.success(extStaffService.selectExtStaffList(extStaff));
    }

    /**
     * 导出外部员工列表
     */
    @PreAuthorize("@ss.hasPermi('system:extstaff:export')")
    @Log(title = "外部员工", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExtStaff extStaff) {
        List<ExtStaff> list = extStaffService.selectExtStaffList(extStaff);
        ExcelUtil<ExtStaff> util = new ExcelUtil<ExtStaff>(ExtStaff.class);
        util.exportExcel(response, list, "外部员工数据");
    }

    /**
     * 获取外部员工详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:extstaff:edit')")
    @GetMapping(value = "/{staffId}")
    public AjaxResult getInfo(@PathVariable("staffId") Long staffId) {
        return AjaxResult.success(extStaffService.selectExtStaffByStaffId(staffId));
    }

    /**
     * 新增外部员工
     */
    @PreAuthorize("@ss.hasPermi('system:extstaff:add')")
    @Log(title = "外部员工", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ExtStaff extStaff) {
        return toAjax(extStaffService.insertExtStaff(extStaff));
    }

    /**
     * 修改外部员工
     */
    @PreAuthorize("@ss.hasPermi('system:extstaff:edit')")
    @Log(title = "外部员工", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExtStaff extStaff) {
        return toAjax(extStaffService.updateExtStaff(extStaff));
    }

    /**
     * 删除外部员工
     */
    @PreAuthorize("@ss.hasPermi('system:extstaff:remove')")
    @Log(title = "外部员工", businessType = BusinessType.DELETE)
    @DeleteMapping("/{staffIds}")
    public AjaxResult remove(@PathVariable Long[] staffIds) {
        return toAjax(extStaffService.deleteExtStaffByStaffIds(staffIds));
    }
}
