package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasCarrier;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasCarrierService;
import com.rich.system.service.impl.RedisCacheImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 船公司Controller
 *
 * <AUTHOR>
 * @date 2022-10-31
 */
@RestController
@RequestMapping("/system/carrier")
public class BasCarrierController extends BaseController {

    @Autowired
    private BasCarrierService basCarrierService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisCacheImpl RedisCache;

    /**
     * 查询船公司列表
     */
    @PreAuthorize("@ss.hasPermi('system:carrier:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasCarrier basCarrier) {
        startPage();
        List<BasCarrier> list = basCarrierService.selectBasCarrierList(basCarrier);
        return getDataTable(list);
    }

    /**
     * 查询船公司选择框列表
     */
    @GetMapping("/selectList")
    public AjaxResult listAll(BasCarrier basCarrier) {
        List<BasCarrier> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "carrier");
        if (list == null) {
            RedisCache.carrier();
            list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "carrier");
        }
        return AjaxResult.success(AjaxResult.DATA_TAG, list);
    }

    /**
     * 查询船公司列表
     */
    @GetMapping("/listCs")
    public AjaxResult listCs() {
        List list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "serviceTypeCarriers");
        if (list == null) {
            list = basCarrierService.selectServiceTypeCarriers();
            redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "serviceTypeCarriers");
            redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "serviceTypeCarriers", list);
        }
        return AjaxResult.success(list);
    }

    /**
     * 导出船公司列表
     */
    @PreAuthorize("@ss.hasPermi('system:carrier:export')")
    @Log(title = "船公司", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasCarrier basCarrier) {
        List<BasCarrier> list = basCarrierService.selectBasCarrierList(basCarrier);
        ExcelUtil<BasCarrier> util = new ExcelUtil<BasCarrier>(BasCarrier.class);
        util.exportExcel(response, list, "船公司数据");
    }

    /**
     * 获取船公司详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:carrier:edit')")
    @GetMapping(value = "/{carrierId}")
    public AjaxResult getInfo(@PathVariable("carrierId") Long carrierId) {
        AjaxResult ajaxResult = AjaxResult.success();
        ajaxResult.put(AjaxResult.DATA_TAG, basCarrierService.selectBasCarrierByCarrierId(carrierId));
        ajaxResult.put("serviceTypeIds", basCarrierService.selectServiceTypeCarrierIds(carrierId));
        return ajaxResult;
    }

    /**
     * 新增船公司
     */
    @PreAuthorize("@ss.hasPermi('system:carrier:add')")
    @Log(title = "船公司", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasCarrier basCarrier) {
        int out = basCarrierService.insertBasCarrier(basCarrier);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "carrier");
        return toAjax(out);
    }

    /**
     * 修改船公司
     */
    @PreAuthorize("@ss.hasPermi('system:carrier:edit')")
    @Log(title = "船公司", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasCarrier basCarrier) {
        int out = basCarrierService.updateBasCarrier(basCarrier);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "carrier");
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "serviceTypeCarriers");
        return toAjax(out);
    }

    /**
     * 修改船公司
     */
    @PreAuthorize("@ss.hasPermi('system:carrier:edit')")
    @Log(title = "船公司", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasCarrier basCarrier) {
        int out = basCarrierService.changeStatus(basCarrier);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "carrier");
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "serviceTypeCarriers");
        return toAjax(out);
    }

    /**
     * 删除船公司
     */
    @PreAuthorize("@ss.hasPermi('system:carrier:remove')")
    @Log(title = "船公司", businessType = BusinessType.DELETE)
    @DeleteMapping("/{carrierIds}")
    public AjaxResult remove(@PathVariable Long[] carrierIds) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "carrier");
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "serviceTypeCarriers");
        return toAjax(basCarrierService.deleteBasCarrierByCarrierIds(carrierIds));
    }
}
