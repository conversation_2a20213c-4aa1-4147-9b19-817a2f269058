package com.rich.system.service.impl;

import com.rich.common.annotation.DataScope;
import com.rich.common.constant.CacheConstants;
import com.rich.common.constant.UserConstants;
import com.rich.common.core.domain.entity.BasDistDept;
import com.rich.common.core.domain.entity.MidRsStaffRole;
import com.rich.common.core.domain.entity.RsStaff;
import com.rich.common.core.domain.entity.SysRole;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.core.text.Convert;
import com.rich.common.exception.ServiceException;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.StringUtils;
import com.rich.common.utils.spring.SpringUtils;
import com.rich.system.domain.*;
import com.rich.system.mapper.*;
import com.rich.system.service.SysRoleService;
import org.apache.commons.lang.ArrayUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 角色 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysRoleServiceImpl implements SysRoleService {
    @Autowired
    private SysRoleMapper roleMapper;
    @Autowired
    private MidRoleMenuMapper roleMenuMapper;
    @Autowired
    private MidRoleDeptMapper roleDeptMapper;
    @Autowired
    private MidRsStaffRoleMapper midRsStaffRoleMapper;
    @Autowired
    private MidLocationDepartureMapper midLocationDepartureMapper;
    @Autowired
    private MidLineDepartureMapper midLineDepartureMapper;
    @Autowired
    private MidLocationDestinationMapper midLocationDestinationMapper;
    @Autowired
    private MidLineDestinationMapper midLineDestinationMapper;
    @Autowired
    private MidServiceTypeMapper midServiceTypeMapper;
    @Autowired
    private MidCargoTypeMapper midCargoTypeMapper;
    @Autowired
    private SysDistributeServiceImpl sysDistributeService;
    @Autowired
    private MidRoleMenuMapper midRoleMenuMapper;
    @Autowired
    private RedisCache redisCache;

    /**
     * 根据条件分页查询角色数据
     *
     * @param role 角色信息
     * @return 角色数据集合信息
     */
    @Override
    @DataScope(deptAlias = "d", subDeptAlias = "d", userAlias = "u", subUserAlias = "u")
    public List<SysRole> selectRoleList(SysRole role) {
        if (role.getServiceTypeId() != null) {
            Set<Long> roleIds = new HashSet<>();
            MidServiceType midServiceType = new MidServiceType();
            midServiceType.setBelongTo("role");
            midServiceType.setServiceTypeId(role.getServiceTypeId());
            List<MidServiceType> midServiceTypes = midServiceTypeMapper.selectMidServiceTypeList(midServiceType);
            for (MidServiceType s : midServiceTypes) {
                roleIds.add(s.getBelongId());
            }
            role.setServiceTypeRoles(roleIds.toArray(new Long[0]));
        }
        role.setUserId(SecurityUtils.getUserId());
        if (SecurityUtils.getDeptId() == 102 || SecurityUtils.getUserId() == 1) {
            return roleMapper.selectRoleListAll(role);
        }
        return roleMapper.selectRoleList(role);
    }

    @Override
    public List<SysRole> getList() {
        SysRole role = new SysRole();
        if (SecurityUtils.getDeptId().equals(102L) || SecurityUtils.getUserId().equals(1L)) {
            role.setUserId(null);
        } else {
            role.setUserId(SecurityUtils.getUserId());
        }
        return roleMapper.selectRoleList(role);
    }

    /**
     * 根据用户ID查询角色
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    @Override
    public List<MidRsStaffRole> selectRolesByUserId(Long userId) {
        MidRsStaffRole midRsStaffRole = new MidRsStaffRole();
        midRsStaffRole.setStaffId(userId);
        return midRsStaffRoleMapper.selectMidRsStaffRoleList(midRsStaffRole);
    }

    /**
     * 根据用户ID查询权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    @Override
    public Set<String> selectRolePermissionByUserId(Long userId) {
        List<SysRole> perms = roleMapper.selectRolePermissionByUserId(userId);
        Set<String> permsSet = new HashSet<>();
        for (SysRole perm : perms) {
            if (StringUtils.isNotNull(perm)) {
                permsSet.addAll(Arrays.asList(perm.getRoleKey().trim().split(",")));
            }
        }
        return permsSet;
    }

    /**
     * 查询所有角色
     *
     * @return 角色列表
     */
    @Override
    public List<SysRole> selectRoleAll() {
        return SpringUtils.getAopProxy(this).selectRoleList(new SysRole());
    }


    /**
     * 通过角色ID查询角色
     *
     * @param roleId 角色ID
     * @return 角色对象信息
     */
    @Override
    public SysRole selectRoleById(Long roleId) {
        return roleMapper.selectRoleById(roleId);
    }

    /**
     * 校验角色名称是否唯一
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    public boolean checkroleLocalNameUnique(SysRole role) {
        Long roleId = StringUtils.isNull(role.getRoleId()) ? -1L : role.getRoleId();
        SysRole info = roleMapper.checkroleLocalNameUnique(role.getRoleLocalName());
        if (StringUtils.isNotNull(info) && info.getRoleId().longValue() != roleId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验角色权限是否唯一
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    public boolean checkRoleKeyUnique(SysRole role) {
        Long roleId = StringUtils.isNull(role.getRoleId()) ? -1L : role.getRoleId();
        SysRole info = roleMapper.checkRoleKeyUnique(role.getRoleKey());
        if (StringUtils.isNotNull(info) && info.getRoleId().longValue() != roleId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验角色是否允许操作
     *
     * @param role 角色信息
     */
    @Override
    public void checkRoleAllowed(SysRole role) {
        if (StringUtils.isNotNull(role.getRoleId()) && role.isAdmin()) {
            throw new ServiceException("不允许操作超级管理员角色");
        }
    }

    /**
     * 校验角色是否有数据权限
     *
     * @param roleId 角色id
     */
    @Override
    public void checkRoleDataScope(Long roleId) {
        // 当前角色不是管理员
        if (!RsStaff.isAdmin(SecurityUtils.getLoginUser().getUser().getRole())) {
            SysRole role = new SysRole();
            role.setRoleId(roleId);
            // 根据角色id查询角色列表
            List<SysRole> roles = SpringUtils.getAopProxy(this).selectRoleList(role);
            if (StringUtils.isEmpty(roles)) {
                throw new ServiceException("没有权限访问角色数据！");
            }
        }
    }

    /**
     * 通过角色ID查询角色使用数量
     *
     * @param roleId 角色ID
     * @return 结果
     */
    @Override
    public int countUserRoleByRoleId(Long roleId) {
        return midRsStaffRoleMapper.countUserRoleByRoleId(roleId);
    }

    /**
     * 新增保存角色信息
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertRole(@NotNull SysRole role) {
        SysRole info = roleMapper.selectRoleById(role.getParentId());
        // 如果父节点不为正常状态,则不允许新增子节点
        if (!UserConstants.NORMAL.equals(info.getStatus())) {
            throw new ServiceException("角色停用，不允许新增");
        }
        role.setAncestors(info.getAncestors() + "," + role.getParentId());
        // 新增角色信息
        int out = roleMapper.insertRole(role);
        insertRoleMenu(role);
        insertServiceTypes(role);
        insertCargoTypes(role);
        insertLocationDeparture(role);
        insertLineDeparture(role);
        insertLocationDestination(role);
        insertLineDestination(role);
        return out;
    }

    private void insertLineDestination(SysRole role) {
        Long[] lineDestinationIds = role.getLineDestinationIds();
        if (StringUtils.isNotEmpty(lineDestinationIds)) {
            List<MidLineDestination> list = new ArrayList<>(lineDestinationIds.length);
            for (Long l : lineDestinationIds) {
                MidLineDestination MidLineDestination = new MidLineDestination();
                MidLineDestination.setBelongId(role.getRoleId());
                MidLineDestination.setLineId(l);
                MidLineDestination.setBelongTo("role");
                list.add(MidLineDestination);
            }
            midLineDestinationMapper.batchLD(list);
        }
    }

    private void insertLocationDestination(SysRole role) {
        Long[] locationDestinationIds = role.getLocationDestinationIds();
        if (StringUtils.isNotEmpty(locationDestinationIds)) {
            List<MidLocationDestination> list = new ArrayList<>(locationDestinationIds.length);
            for (Long l : locationDestinationIds) {
                MidLocationDestination MidLocationDestination = new MidLocationDestination();
                MidLocationDestination.setBelongId(role.getRoleId());
                MidLocationDestination.setLocationId(l);
                MidLocationDestination.setBelongTo("role");
                list.add(MidLocationDestination);
            }
            midLocationDestinationMapper.batchLD(list);
        }
    }

    private void insertLineDeparture(@NotNull SysRole role) {
        Long[] lineDepartureIds = role.getLineDepartureIds();
        if (StringUtils.isNotEmpty(lineDepartureIds)) {
            List<MidLineDeparture> list = new ArrayList<>(lineDepartureIds.length);
            for (Long l : lineDepartureIds) {
                MidLineDeparture MidLineDeparture = new MidLineDeparture();
                MidLineDeparture.setBelongId(role.getRoleId());
                MidLineDeparture.setLineId(l);
                MidLineDeparture.setBelongTo("role");
                list.add(MidLineDeparture);
            }
            midLineDepartureMapper.batchLD(list);
        }
    }

    private void insertLocationDeparture(@NotNull SysRole role) {
        Long[] locationDepartureIds = role.getLocationDepartureIds();
        if (StringUtils.isNotEmpty(locationDepartureIds)) {
            List<MidLocationDeparture> list = new ArrayList<>(locationDepartureIds.length);
            for (Long l : locationDepartureIds) {
                MidLocationDeparture MidLocationDeparture = new MidLocationDeparture();
                MidLocationDeparture.setBelongId(role.getRoleId());
                MidLocationDeparture.setLocationId(l);
                MidLocationDeparture.setBelongTo("role");
                list.add(MidLocationDeparture);
            }
            midLocationDepartureMapper.batchLD(list);
        }
    }

    private void insertCargoTypes(@NotNull SysRole role) {
        Long[] cargoTypeIds = role.getCargoTypeIds();
        if (StringUtils.isNotEmpty(cargoTypeIds)) {
            List<MidCargoType> list = new ArrayList<>(cargoTypeIds.length);
            for (Long c : cargoTypeIds) {
                MidCargoType MidCargoType = new MidCargoType();
                MidCargoType.setBelongId(role.getRoleId());
                MidCargoType.setBelongTo("role");
                MidCargoType.setCargoTypeId(c);
                list.add(MidCargoType);
            }
            midCargoTypeMapper.batchCargoType(list);
        }
    }

    private void insertServiceTypes(@NotNull SysRole role) {
        Long[] serviceTypeIds = role.getServiceTypeIds();
        if (StringUtils.isNotEmpty(serviceTypeIds)) {
            List<MidServiceType> list = new ArrayList<>(serviceTypeIds.length);
            for (Long s : serviceTypeIds) {
                MidServiceType MidServiceType = new MidServiceType();
                MidServiceType.setBelongId(role.getRoleId());
                MidServiceType.setServiceTypeId(s);
                MidServiceType.setBelongTo("role");
                list.add(MidServiceType);
            }
            midServiceTypeMapper.batchServiceType(list);
        }
    }

    /**
     * 修改保存角色信息
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public int updateRole(@NotNull SysRole role) {
        role.setUpdateBy(SecurityUtils.getUserId());
        role.setUpdateTime(DateUtils.getNowDate());
        // 查询当前角色的父级
        SysRole newParentRole = roleMapper.selectRoleById(role.getParentId());
        // 查询没有更新前的角色
        SysRole oldRole = roleMapper.selectRoleById(role.getRoleId());
        // 更新父级
        if (StringUtils.isNotNull(newParentRole) && StringUtils.isNotNull(oldRole)) {
            String newAncestors = newParentRole.getAncestors() + "," + newParentRole.getRoleId();
            String oldAncestors = oldRole.getAncestors();
            role.setAncestors(newAncestors);
            // 更新角色子级
            updateRoleChildren(role.getRoleId(), newAncestors, oldAncestors);
        }
        if (UserConstants.DEPT_NORMAL.equals(role.getStatus()) && StringUtils.isNotEmpty(role.getAncestors())
                && !StringUtils.equals("0", role.getAncestors())) {
            // 如果该部门是启用状态，则启用该部门的所有上级部门
            updateParentRoleStatusNormal(role);
        }
        // 删除之前先把旧的菜单查出来用于对比是否减少权限
        // TODO 更改
        Long[] oldMenuIds = roleMapper.selectRoleMenuById(role.getRoleId());
        role.getParams().put("oldMenuIds", oldMenuIds);
        // 删除角色与菜单关联
        roleMenuMapper.deleteRoleMenuByRoleId(role.getRoleId());
        // 删除角色与部门关联
        roleDeptMapper.deleteRoleDeptByRoleId(role.getRoleId());
        // 新增角色和部门信息（数据权限）
        insertRoleDept(role);
        // 新增角色和菜单信息
        insertRoleMenu(role);
        deleteRoleMids(role.getRoleId());
        insertServiceTypes(role);
        insertCargoTypes(role);
        insertLocationDestination(role);
        insertLineDestination(role);
        insertLocationDeparture(role);
        insertLineDeparture(role);
        // 更新角色
        return roleMapper.updateRole(role);
    }

    private void updateParentRoleStatusNormal(@NotNull SysRole role) {
        String ancestors = role.getAncestors();
        Long[] roleIds = Convert.toLongArray(ancestors);
        roleMapper.updateRoleStatusNormal(roleIds);
    }

    private void updateRoleChildren(Long roleId, String newAncestors, String oldAncestors) {
        // 根据角色id查询子级菜单
        List<SysRole> children = roleMapper.selectChildrenRoleById(roleId);
        for (SysRole child : children) {
            // 更改这些子级菜单的父级
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
        }
        // 更新
        if (!children.isEmpty()) {
            roleMapper.updateRoleChildren(children);
        }
    }

    /**
     * 修改角色状态
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    public int updateRoleStatus(SysRole role) {
        return roleMapper.updateRole(role);
    }

    /**
     * 新增角色菜单信息
     *
     * @param role 角色对象
     */
    @Transactional(rollbackFor = {Exception.class})
    public int insertRoleMenu(@NotNull SysRole role) {
        // 查询要新增的角色
        SysRole sysRole = roleMapper.selectRoleById(role.getRoleId());
        // 除最大权限外的角色id列表
        List<Long> unlessMaxRoleIds = roleMenuMapper.selectUnlessMaxRoleIds(role.getDeptId());
        List<Long> unlessMaxAndBasicRoleIds = roleMenuMapper.unlessMaxAndRoleIds(role.getDeptId());
        // 如果是部门最高权限，对比菜单是否减少(权限收回),对与收回的权限整个部门的权限都要收回(删除整个部门所有角色的对应菜单)
        // 如果是部门基础权限，对比菜单是否减少，减少的菜单除了部门最高权限角色外其他角色都要收回
        if (role.getRoleKey().contains("Max") || role.getRoleKey().contains("Basic")) {
            // 将该部门高级角色下的部门所有角色都去除掉对应的菜单(收回权限)
            if (role.getRoleKey().contains("Max")) {
                roleMenuMapper.takeBackMenuByDeptId(sysRole.getDeptId(), role.getMenuIds());
            } else if (role.getRoleKey().contains("Basic")) {
                Long[] newMenuIds = role.getMenuIds();
                Set<Long> takeBackMenuIds = new HashSet<Long>();
                for (Long oldMenuId : (Long[]) role.getParams().get("oldMenuIds")) {
                    if (!ArrayUtils.contains(newMenuIds, oldMenuId)) {
                        takeBackMenuIds.add(oldMenuId);
                    }
                }
                if (!takeBackMenuIds.isEmpty()) {
                    for (Long unlessMaxRoleId : unlessMaxRoleIds) {
                        roleMenuMapper.deleteRoleMenuByRoleIds(unlessMaxRoleId, takeBackMenuIds);
                    }
                }
            }
        }

        // 除总经办之外的角色 部门中其他角色在添加权限时只能添加部门最高角色拥有的权限
        if (!role.getDeptId().equals(102L)) {
            if (!role.getRoleKey().contains("Max")) {
                Long[] deptMaxRoleMenuIds = roleMapper.selectDeptMaxRoleMenuIds(role.getDeptId());
                Long[] newMenuIds = role.getMenuIds();
                for (Long newMenuId : newMenuIds) {
                    // 如果最高权限中不包含要添加的权限，直接抛出错误
                    if (!ArrayUtils.contains(deptMaxRoleMenuIds, newMenuId)) {
                        throw new RuntimeException("添加的权限超过部门最高权限范围！！！");
                    }
                }
            }
        }


        int rows = 1;
        // 新增用户与角色管理
        List<MidRoleMenu> list = new ArrayList<MidRoleMenu>();
        for (Long menuId : role.getMenuIds()) {
            MidRoleMenu rm = new MidRoleMenu();
            rm.setRoleId(role.getRoleId());
            rm.setMenuId(menuId);
            list.add(rm);
        }
        if (!list.isEmpty()) {
            // 在基础角色添加权限时还需要给该基础角色的部门内除最高权限角色和基础角色外的所有角色添加
            if (role.getRoleKey().contains("Basic")) {
                // 拿到除最高权限外的每一个角色
                ArrayList<MidRoleMenu> basicRoleMenus = new ArrayList<>();
                for (Long unlessMaxAndBasicRoleId : unlessMaxAndBasicRoleIds) {
                    Arrays.stream(role.getMenuIds()).forEach(menuId -> {
                        MidRoleMenu midRoleMenu = new MidRoleMenu();
                        midRoleMenu.setMenuId(menuId);
                        midRoleMenu.setRoleId(unlessMaxAndBasicRoleId);
                        basicRoleMenus.add(midRoleMenu);
                    });
                }
                if (!basicRoleMenus.isEmpty()) {
                    roleMenuMapper.batchRoleMenuByMenuIds(basicRoleMenus);
                }
            }

            // 先删除菜单缓存
            redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "basicRoleMenu");
            rows = roleMenuMapper.batchRoleMenuByMenuIds(list);
        }
        return rows;
    }

    /**
     * 新增角色部门信息(数据权限)
     *
     * @param role 角色对象
     */
    public int insertRoleDept(@NotNull SysRole role) {
        int rows = 1;
        // 新增角色与部门（数据权限）管理
        List<MidRoleDept> list = new ArrayList<>();
        for (Long deptId : role.getDeptIds()) {
            MidRoleDept rd = new MidRoleDept();
            rd.setRoleId(role.getRoleId());
            rd.setDeptId(deptId);
            list.add(rd);
        }
        if (!list.isEmpty()) {
            rows = roleDeptMapper.batchRoleDept(list);
        }
        return rows;
    }

    /**
     * 通过角色ID删除角色
     *
     * @param roleId 角色ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteRoleById(Long roleId) {
        checkRoleAllowed(new SysRole(roleId));
        checkRoleDataScope(roleId);
        SysRole role = selectRoleById(roleId);
        if (countUserRoleByRoleId(roleId) > 0) {
            throw new ServiceException(String.format("%1$s已分配,不能删除", role.getRoleLocalName()));
        }
        // 删除角色与菜单关联
        roleMenuMapper.deleteRoleMenuByRoleId(roleId);
        // 删除角色与部门关联
        roleDeptMapper.deleteRoleDeptByRoleId(roleId);
        deleteRoleMids(roleId);
        return roleMapper.deleteRoleById(roleId);
    }

    private void deleteRoleMids(Long roleId) {
        midCargoTypeMapper.deleteMidCargoTypeById(roleId, "role");
        midServiceTypeMapper.deleteMidServiceTypeById(roleId, "role");
        midLineDepartureMapper.deleteMidLineDepartureById(roleId, "role");
        midLocationDepartureMapper.deleteMidLocationDepartureById(roleId, "role");
        midLineDestinationMapper.deleteMidLineDestinationById(roleId, "role");
        midLocationDestinationMapper.deleteMidLocationDestinationById(roleId, "role");
    }

    /**
     * 批量删除角色信息
     *
     * @param roleIds 需要删除的角色ID
     * @return 结果
     */
    @Transactional
    public int deleteRoleByIds(Long @NotNull [] roleIds) {
        for (Long roleId : roleIds) {
            checkRoleAllowed(new SysRole(roleId));
            checkRoleDataScope(roleId);
            SysRole role = selectRoleById(roleId);
            if (countUserRoleByRoleId(roleId) > 0) {
                throw new ServiceException(String.format("%1$s已分配,不能删除", role.getRoleLocalName()));
            }
        }
        // 删除角色与菜单关联
        roleMenuMapper.deleteRoleMenu(roleIds);
        // 删除角色与部门关联
        roleDeptMapper.deleteRoleDept(roleIds);

        return roleMapper.deleteRoleByIds(roleIds);
    }

    @Override
    @DataScope(deptAlias = "d", subDeptAlias = "d", userAlias = "u", subUserAlias = "u")
    public List<MidRsStaffRole> selectSales(BasDistDept dept) {
        return midRsStaffRoleMapper.selectSales(dept);
    }

    @Override
    public List<MidRsStaffRole> selectBusinesses(BasDistDept dept) {
        List<MidRsStaffRole> roles = midRsStaffRoleMapper.selectBusinesses(dept);
        List<MidRsStaffRole> out = new ArrayList<>();
        // 去重
        for (MidRsStaffRole role : roles) {
            if (out.contains(role)) {
                continue;
            }
            if (role.getStaffId() != null) {
                List<MidRsStaffRole> d = new ArrayList<>();
                try {
                    d = roles.stream().filter(MidRsStaffRole -> MidRsStaffRole.getParentId().equals(role.getParentId()) && MidRsStaffRole.getStaffId().equals(role.getStaffId()) && !MidRsStaffRole.getDeptId().equals(role.getDeptId())).collect(Collectors.toList());
                } catch (Exception ignore) {
                }
                if (d.size() > 0) {
                    out.addAll(d);
                }
            }
        }
        roles = roles.stream().filter(MidRsStaffRole -> !out.contains(MidRsStaffRole)).collect(Collectors.toList());
        return roles;
    }

    @Override
    public List<Long> selectLocationDepartureIds(Long roleId) {
        return midLocationDepartureMapper.selectMidLocationDepartureById(roleId, "role");
    }

    @Override
    public List<Long> selectLineDepartureIds(Long roleId) {
        return midLineDepartureMapper.selectMidLineDepartureById(roleId, "role");
    }

    @Override
    public List<Long> selectLocationDestinationIds(Long roleId) {
        return midLocationDestinationMapper.selectMidLocationDestinationById(roleId, "role");
    }

    @Override
    public List<Long> selectLineDestinationIds(Long roleId) {
        return midLineDestinationMapper.selectMidLineDestinationById(roleId, "role");
    }

    @Override
    public List<Long> selectServiceTypeIds(Long roleId) {
        return midServiceTypeMapper.selectMidServiceTypeById(roleId, "role");
    }

    @Override
    public List<Long> selectCargoTypeIds(Long roleId) {
        return midCargoTypeMapper.selectMidCargoTypeById(roleId, "role");
    }

    @Override
    public Long[] getUnderManagerDeptIds(Long userId) {

        return midRsStaffRoleMapper.getUnderManagerDeptIds(userId);
    }

    public static boolean hasIntersection(String[] strArray, Long[] longArray) {
        // 将 Long 数组转换为 Set 集合
        Set<Long> longSet = Arrays.stream(longArray).collect(Collectors.toSet());

        // 检查字符串数组中的元素是否存在于 Set 集合中
        return Arrays.stream(strArray)
                .map(str -> {
                    try {
                        return Long.parseLong(str);
                    } catch (NumberFormatException e) {
                        return null; // 无法转换时返回 null
                    }
                })
                .anyMatch(num -> num != null && longSet.contains(num));
    }

    @NotNull
    public ArrayList<MidRsStaffRole> filterBusinessesListByRole(List<MidRsStaffRole> list, String level) {

        ArrayList<MidRsStaffRole> result = new ArrayList<>();

        // 管理员返回全部信息
        for (SysRole role : SecurityUtils.getLoginUser().getUser().getRoles()) {
            if (role.getDeptId().equals(102L) || role.getRoleKey().equals("admin")) {
                return (ArrayList<MidRsStaffRole>) list;
            }
        }


        if (level.equals("C")) {
            // 根据当前用户的角色返回对应的业务人员
            Long userId = SecurityUtils.getUserId();
            Long deptId = SecurityUtils.getDeptId();
            // 是否为管理并返回所管理部门的id
            Long[] underManagerDeptIds = midRsStaffRoleMapper.getUnderManagerDeptIds(userId);
            // 先加载自己部门的全部人员
            List<MidRsStaffRole> collect = list.stream().peek(o -> {
                // 如果是同部门或者是自己管理的部门
                if (Objects.equals(o.getDeptId(), deptId) || ArrayUtils.contains(underManagerDeptIds, o.getDeptId()) || ArrayUtils.contains(underManagerDeptIds, o.getParentId())) {
                    result.add(o);
                }
                if (hasIntersection(o.getAncestors().split(","), underManagerDeptIds)) {
                    result.add(o);
                }
                if (o.getStaffId() != null && o.getStaffId().equals(userId)) {
                    result.add(o);
                }
            }).collect(Collectors.toList());
        } else {
            result.addAll(list);
        }

        return result;
    }
}
