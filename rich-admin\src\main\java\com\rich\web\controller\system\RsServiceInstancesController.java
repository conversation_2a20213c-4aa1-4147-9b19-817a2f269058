package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsServiceInstances;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsServiceInstancesService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 服务实例，记录着每一个订舱中的各种服务Controller
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@RestController
@RequestMapping("/system/serviceinstances")
public class RsServiceInstancesController extends BaseController {
    @Autowired
    private RsServiceInstancesService rsServiceInstancesService;

    /**
     * 查询服务实例，记录着每一个订舱中的各种服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:serviceinstances:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsServiceInstances rsServiceInstances) {
        startPage();
        List<RsServiceInstances> list = rsServiceInstancesService.selectRsServiceInstancesList(rsServiceInstances);
        return getDataTable(list);
    }

    /**
     * 导出服务实例，记录着每一个订舱中的各种服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:serviceinstances:export')")
    @Log(title = "服务实例，记录着每一个订舱中的各种服务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsServiceInstances rsServiceInstances) {
        List<RsServiceInstances> list = rsServiceInstancesService.selectRsServiceInstancesList(rsServiceInstances);
        ExcelUtil<RsServiceInstances> util = new ExcelUtil<RsServiceInstances>(RsServiceInstances.class);
        util.exportExcel(response, list, "服务实例，记录着每一个订舱中的各种服务数据");
    }

    /**
     * 获取服务实例，记录着每一个订舱中的各种服务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:serviceinstances:query')")
    @GetMapping(value = "/{serviceId}")
    public AjaxResult getInfo(@PathVariable("serviceId") Long serviceId) {
        return AjaxResult.success(rsServiceInstancesService.selectRsServiceInstancesByServiceId(serviceId));
    }

    /**
     * 新增服务实例，记录着每一个订舱中的各种服务
     */
    @PreAuthorize("@ss.hasPermi('system:serviceinstances:add')")
    @Log(title = "服务实例，记录着每一个订舱中的各种服务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsServiceInstances rsServiceInstances) {
        return toAjax(rsServiceInstancesService.insertRsServiceInstances(rsServiceInstances));
    }

    /**
     * 修改服务实例，记录着每一个订舱中的各种服务
     */
    @PreAuthorize("@ss.hasPermi('system:serviceinstances:edit')")
    @Log(title = "服务实例，记录着每一个订舱中的各种服务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsServiceInstances rsServiceInstances) {
        return toAjax(rsServiceInstancesService.updateRsServiceInstances(rsServiceInstances));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:serviceinstances:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsServiceInstances rsServiceInstances) {
        rsServiceInstances.setUpdateBy(getUserId());
        return toAjax(rsServiceInstancesService.changeStatus(rsServiceInstances));
    }

    /**
     * 删除服务实例，记录着每一个订舱中的各种服务
     */
    @PreAuthorize("@ss.hasPermi('system:serviceinstances:remove')")
    @Log(title = "服务实例，记录着每一个订舱中的各种服务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{serviceIds}")
    public AjaxResult remove(@PathVariable Long[] serviceIds) {
        return toAjax(rsServiceInstancesService.deleteRsServiceInstancesByServiceIds(serviceIds));
    }
}
