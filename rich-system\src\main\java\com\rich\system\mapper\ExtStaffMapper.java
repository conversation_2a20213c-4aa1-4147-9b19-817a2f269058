package com.rich.system.mapper;

import com.rich.common.core.domain.entity.ExtStaff;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 外部员工Mapper接口
 *
 * <AUTHOR>
 * @date 2022-09-27
 */
@Mapper
public interface ExtStaffMapper {
    /**
     * 查询外部员工
     *
     * @param staffId 外部员工主键
     * @return 外部员工
     */
    ExtStaff selectExtStaffByStaffId(Long staffId);

    /**
     * 查询外部员工列表
     *
     * @param extStaff 外部员工
     * @return 外部员工集合
     */
    List<ExtStaff> selectExtStaffList(ExtStaff extStaff);

    /**
     * 新增外部员工
     *
     * @param extStaff 外部员工
     * @return 结果
     */
    int insertExtStaff(ExtStaff extStaff);

    /**
     * 修改外部员工
     *
     * @param extStaff 外部员工
     * @return 结果
     */
    int updateExtStaff(ExtStaff extStaff);

    /**
     * 删除外部员工
     *
     * @param staffId 外部员工主键
     * @return 结果
     */
    int deleteExtStaffByStaffId(Long staffId);

    /**
     * 批量删除外部员工
     *
     * @param staffIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteExtStaffByStaffIds(Long[] staffIds);
}
