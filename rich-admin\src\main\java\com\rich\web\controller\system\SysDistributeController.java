package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasDistDept;
import com.rich.common.core.domain.entity.SysDistribute;
import com.rich.common.core.domain.entity.SysMenu;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasDistDeptService;
import com.rich.system.service.SysDistributeService;
import com.rich.system.service.SysMenuService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 权限分配Controller
 *
 * <AUTHOR>
 * @date 2023-02-13
 */
@RestController
@RequestMapping("/system/distribute")

public class SysDistributeController extends BaseController {

    @Autowired
    private  SysDistributeService sysDistributeService;

    /**
     * 查询权限分配列表
     */
    @PreAuthorize("@ss.hasPermi('system:distribute:list')")
    @GetMapping("/list")
    public AjaxResult list(SysDistribute sysDistribute) {
        List<SysDistribute> list = sysDistributeService.selectSysDistributeList(sysDistribute);
        return AjaxResult.success(list);
    }

    /**
     * 导出权限分配列表
     */
    @PreAuthorize("@ss.hasPermi('system:distribute:export')")
    @Log(title = "权限分配", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysDistribute sysDistribute) {
        List<SysDistribute> list = sysDistributeService.selectSysDistributeList(sysDistribute);
        ExcelUtil<SysDistribute> util = new ExcelUtil<SysDistribute>(SysDistribute.class);
        util.exportExcel(response, list, "权限分配数据");
    }

    /**
     * 获取权限分配详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:distribute:edit')")
    @GetMapping(value = "/{distributeId}")
    public AjaxResult getInfo(@PathVariable("distributeId") Long distributeId) {
        return AjaxResult.success(sysDistributeService.selectSysDistributeByDistributeId(distributeId));
    }

    /**
     * 新增权限分配
     */
    @PreAuthorize("@ss.hasPermi('system:distribute:add')")
    @Log(title = "权限分配", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysDistribute sysDistribute) {
        return toAjax(sysDistributeService.insertSysDistribute(sysDistribute));
    }

    /**
     * 修改权限分配
     */
    @PreAuthorize("@ss.hasPermi('system:distribute:edit')")
    @Log(title = "权限分配", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysDistribute sysDistribute) {
        return toAjax(sysDistributeService.updateSysDistribute(sysDistribute));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:distribute:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysDistribute sysDistribute) {
        sysDistribute.setUpdateBy(getUserId());
        return toAjax(sysDistributeService.changeStatus(sysDistribute));
    }

    /**
     * 删除权限分配
     */
    @PreAuthorize("@ss.hasPermi('system:distribute:remove')")
    @Log(title = "权限分配", businessType = BusinessType.DELETE)
    @DeleteMapping("/{distributeIds}")
    public AjaxResult remove(@PathVariable Long[] distributeIds) {
        return toAjax(sysDistributeService.deleteSysDistributeByDistributeIds(distributeIds));
    }

    /**
     * 新增权限分配
     */
    @PreAuthorize("@ss.hasPermi('system:distribute:flash')")
    @Log(title = "刷新权限", businessType = BusinessType.INSERT)
    @PostMapping("/flash")
    public AjaxResult flash() {
        sysDistributeService.flashSysDistribute();
        return AjaxResult.success();
    }
}
