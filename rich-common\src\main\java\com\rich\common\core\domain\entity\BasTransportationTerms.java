package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

/**
 * 运输条款对象 bas_transportation_terms
 *
 * <AUTHOR>
 * @date 2023-05-05
 */
public class BasTransportationTerms extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 运输条款
     */
    private Long transportationTermsId;

    /**
     * 简称
     */
    @Excel(name = "简称")
    private String transportationShortName;

    /**
     * 中文名
     */
    @Excel(name = "中文名")
    private String transportationLocalName;

    /**
     * 英文名
     */
    @Excel(name = "英文名")
    private String transportationEnName;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Integer orderNum;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;


    public void setTransportationTermsId(Long transportationTermsId) {
        this.transportationTermsId = transportationTermsId;
    }

    public Long getTransportationTermsId() {
        return transportationTermsId;
    }

    public void setTransportationShortName(String transportationShortName) {
        this.transportationShortName = transportationShortName;
    }

    public String getTransportationShortName() {
        return transportationShortName;
    }

    public void setTransportationLocalName(String transportationLocalName) {
        this.transportationLocalName = transportationLocalName;
    }

    public String getTransportationLocalName() {
        return transportationLocalName;
    }

    public void setTransportationEnName(String transportationEnName) {
        this.transportationEnName = transportationEnName;
    }

    public String getTransportationEnName() {
        return transportationEnName;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
}
