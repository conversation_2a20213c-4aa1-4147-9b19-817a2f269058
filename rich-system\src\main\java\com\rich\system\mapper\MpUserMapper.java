package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.MpUser;
import org.apache.ibatis.annotations.Mapper;

/**
 * 用户信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Mapper
public interface MpUserMapper {
    /**
     * 查询用户信息
     *
     * @param userId 用户信息主键
     * @return 用户信息
     */
    MpUser selectMpUserByUserId(Long userId);

    /**
     * 查询用户信息列表
     *
     * @param mpUser 用户信息
     * @return 用户信息集合
     */
    List<MpUser> selectMpUserList(MpUser mpUser);

    /**
     * 新增用户信息
     *
     * @param mpUser 用户信息
     * @return 结果
     */
    int insertMpUser(MpUser mpUser);

    /**
     * 修改用户信息
     *
     * @param mpUser 用户信息
     * @return 结果
     */
    int updateMpUser(MpUser mpUser);

    /**
     * 删除用户信息
     *
     * @param userId 用户信息主键
     * @return 结果
     */
    int deleteMpUserByUserId(Long userId);

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteMpUserByUserIds(Long[] userIds);
}
