package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpExpress;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 快递服务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Mapper
public interface RsOpExpressMapper {
    /**
     * 查询快递服务
     *
     * @param expressId 快递服务主键
     * @return 快递服务
     */
    RsOpExpress selectRsOpExpressByExpressId(Long expressId);

    /**
     * 查询快递服务列表
     *
     * @param rsOpExpress 快递服务
     * @return 快递服务集合
     */
    List<RsOpExpress> selectRsOpExpressList(RsOpExpress rsOpExpress);

    /**
     * 新增快递服务
     *
     * @param rsOpExpress 快递服务
     * @return 结果
     */
    int insertRsOpExpress(RsOpExpress rsOpExpress);

    /**
     * 修改快递服务
     *
     * @param rsOpExpress 快递服务
     * @return 结果
     */
    int updateRsOpExpress(RsOpExpress rsOpExpress);

    /**
     * 删除快递服务
     *
     * @param expressId 快递服务主键
     * @return 结果
     */
    int deleteRsOpExpressByExpressId(Long expressId);

    /**
     * 批量删除快递服务
     *
     * @param expressIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsOpExpressByExpressIds(Long[] expressIds);

    RsOpExpress selectRsOpExpressByRctId(@Param("rctId") Long rctId, @Param("sqdServiceTypeId") Long sqdServiceTypeId);
}
