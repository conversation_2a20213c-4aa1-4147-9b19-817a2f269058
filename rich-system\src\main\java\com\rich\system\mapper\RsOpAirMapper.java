package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpAir;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 空运服务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Mapper
public interface RsOpAirMapper {
    /**
     * 查询空运服务
     *
     * @param airId 空运服务主键
     * @return 空运服务
     */
    RsOpAir selectRsOpAirByAirId(Long airId);

    /**
     * 查询空运服务列表
     *
     * @param rsOpAir 空运服务
     * @return 空运服务集合
     */
    List<RsOpAir> selectRsOpAirList(RsOpAir rsOpAir);

    /**
     * 新增空运服务
     *
     * @param rsOpAir 空运服务
     * @return 结果
     */
    int insertRsOpAir(RsOpAir rsOpAir);

    /**
     * 修改空运服务
     *
     * @param rsOpAir 空运服务
     * @return 结果
     */
    int updateRsOpAir(RsOpAir rsOpAir);

    /**
     * 删除空运服务
     *
     * @param airId 空运服务主键
     * @return 结果
     */
    int deleteRsOpAirByAirId(Long airId);

    /**
     * 批量删除空运服务
     *
     * @param airIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsOpAirByAirIds(Long[] airIds);

    List<RsOpAir> selectRsOpAirByRctId(@Param("rctId") Long rctId, @Param("sqdServiceTypeId") Long sqdServiceTypeId);

    void deleteRsOpAirByRctIdAndServiceTypeId(@Param("rctId") Long rctId, @Param("sqdServiceTypeId") Long sqdServiceTypeId);

    void upsertRsOpAir(RsOpAir rsOpAir);
}
