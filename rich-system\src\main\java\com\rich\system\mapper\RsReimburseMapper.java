package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsReimburse;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 报销记录Mapper接口
 *
 * <AUTHOR>
 * @date 2023-03-30
 */
@Mapper
public interface RsReimburseMapper {
    /**
     * 查询报销记录
     *
     * @param reimburseId 报销记录主键
     * @return 报销记录
     */
    RsReimburse selectRsReimburseByReimburseId(Long reimburseId);

    /**
     * 查询报销记录列表
     *
     * @param rsReimburse 报销记录
     * @return 报销记录集合
     */
    List<RsReimburse> selectRsReimburseList(RsReimburse rsReimburse);

    /**
     * 新增报销记录
     *
     * @param rsReimburse 报销记录
     * @return 结果
     */
    int insertRsReimburse(RsReimburse rsReimburse);

    /**
     * 修改报销记录
     *
     * @param rsReimburse 报销记录
     * @return 结果
     */
    int updateRsReimburse(RsReimburse rsReimburse);

    /**
     * 审批报销记录
     *
     * @param rsReimburse 报销记录
     * @return 结果
     */
    int rsReimburseApproval(RsReimburse rsReimburse);

    /**
     * 删除报销记录
     *
     * @param reimburseId 报销记录主键
     * @return 结果
     */
    int deleteRsReimburseByReimburseId(Long reimburseId);

    /**
     * 批量删除报销记录
     *
     * @param reimburseIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsReimburseByReimburseIds(Long[] reimburseIds);


    List<RsReimburse> selectRsReimburseWritOffList(RsReimburse rsReimburse);
}
