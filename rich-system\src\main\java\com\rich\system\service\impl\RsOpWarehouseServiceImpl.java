package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpWarehouse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsOpWarehouseMapper;
import com.rich.system.service.RsOpWarehouseService;

/**
 * 仓储服务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Service
public class RsOpWarehouseServiceImpl implements RsOpWarehouseService {
    @Autowired
    private RsOpWarehouseMapper rsOpWarehouseMapper;

    /**
     * 查询仓储服务
     *
     * @param warehouseId 仓储服务主键
     * @return 仓储服务
     */
    @Override
    public RsOpWarehouse selectRsOpWarehouseByWarehouseId(Long warehouseId) {
        return rsOpWarehouseMapper.selectRsOpWarehouseByWarehouseId(warehouseId);
    }

    /**
     * 查询仓储服务列表
     *
     * @param rsOpWarehouse 仓储服务
     * @return 仓储服务
     */
    @Override
    public List<RsOpWarehouse> selectRsOpWarehouseList(RsOpWarehouse rsOpWarehouse) {
        return rsOpWarehouseMapper.selectRsOpWarehouseList(rsOpWarehouse);
    }

    /**
     * 新增仓储服务
     *
     * @param rsOpWarehouse 仓储服务
     * @return 结果
     */
    @Override
    public int insertRsOpWarehouse(RsOpWarehouse rsOpWarehouse) {
        return rsOpWarehouseMapper.insertRsOpWarehouse(rsOpWarehouse);
    }

    /**
     * 修改仓储服务
     *
     * @param rsOpWarehouse 仓储服务
     * @return 结果
     */
    @Override
    public int updateRsOpWarehouse(RsOpWarehouse rsOpWarehouse) {
        return rsOpWarehouseMapper.updateRsOpWarehouse(rsOpWarehouse);
    }

    /**
     * 修改仓储服务状态
     *
     * @param rsOpWarehouse 仓储服务
     * @return 仓储服务
     */
    @Override
    public int changeStatus(RsOpWarehouse rsOpWarehouse) {
        return rsOpWarehouseMapper.updateRsOpWarehouse(rsOpWarehouse);
    }

    /**
     * 批量删除仓储服务
     *
     * @param warehouseIds 需要删除的仓储服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpWarehouseByWarehouseIds(Long[] warehouseIds) {
        return rsOpWarehouseMapper.deleteRsOpWarehouseByWarehouseIds(warehouseIds);
    }

    /**
     * 删除仓储服务信息
     *
     * @param warehouseId 仓储服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpWarehouseByWarehouseId(Long warehouseId) {
        return rsOpWarehouseMapper.deleteRsOpWarehouseByWarehouseId(warehouseId);
    }
}
