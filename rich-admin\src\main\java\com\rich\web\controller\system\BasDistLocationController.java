package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasDistLine;
import com.rich.common.core.domain.entity.BasDistLocation;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.StringUtils;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasDistLineService;
import com.rich.system.service.BasDistLocationService;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 地点Controller
 *
 * <AUTHOR>
 * @date 2022-08-15
 */
@RestController
@RequestMapping("/system/location")
public class BasDistLocationController extends BaseController {

    @Autowired
    private BasDistLocationService basDistLocationService;


    @Autowired
    private BasDistLineService basDistLineService;


    @Autowired
    private RedisCache redisCache;

    /**
     * 查询地点列表
     */
    @PreAuthorize("@ss.hasPermi('system:location:list')")
    @GetMapping("/queryList")
    public AjaxResult queryList(BasDistLocation basDistLocation) {
        List<BasDistLocation> list = basDistLocationService.selectBasDistLocationList(basDistLocation);
        return AjaxResult.success(list);
    }

    @PreAuthorize("@ss.hasPermi('system:location:list')")
    @GetMapping("/getList")
    public AjaxResult getList(BasDistLocation basDistLocation) {
        List<BasDistLocation> list = basDistLocationService.selectLoadLocationList(basDistLocation);
        return AjaxResult.success(list);
    }

    @GetMapping("/selectList")
    public AjaxResult selectList(BasDistLocation basDistLocation) {
        List<BasDistLocation> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        if (list == null) {
            basDistLocation.setStatus("0");
            list = basDistLocationService.selectBasDistLocationList(basDistLocation);
            redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "location");
            redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "location", list);
        }
        return AjaxResult.success(list);
    }

    /**
     * 查询地点列表（排除节点）
     */

    @GetMapping("/list/exclude/{locationId}")
    public AjaxResult excludeChild(@PathVariable(value = "locationId", required = false) Long locationId) {
        List<BasDistLocation> locations = basDistLocationService.selectBasDistLocationList(new BasDistLocation());
        locations.removeIf(d -> d.getLocationId().intValue() == locationId
                || ArrayUtils.contains(StringUtils.split(d.getAncestors(), ","), locationId.toString()));
        return AjaxResult.success(locations);
    }

    /**
     * 导出地点列表
     */
    @PreAuthorize("@ss.hasPermi('system:location:export')")
    @Log(title = "地址输出", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasDistLocation basDistLocation) {
        List<BasDistLocation> list = basDistLocationService.selectBasDistLocationList(basDistLocation);
        List<BasDistLine> lines = basDistLineService.selectBasDistLineList(new BasDistLine());
        for (BasDistLocation l : list) {
            for (BasDistLine line : lines) {
                if ((l.getLineId() != null && line.getLineId() != null) && l.getLineId().equals(line.getLineId())) {
                    l.setLineName(line.getLineLocalName());
                }
            }
        }
        ExcelUtil<BasDistLocation> util = new ExcelUtil<BasDistLocation>(BasDistLocation.class);
        util.exportExcel(response, list, "地点数据");
    }

    /**
     * 获取地点详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:location:edit')")
    @GetMapping(value = "/{locationId}")
    public AjaxResult getInfo(@PathVariable("locationId") Long locationId) {
        AjaxResult ajaxResult = AjaxResult.success();
        Set<Long> set = new HashSet<>();
        BasDistLocation location = basDistLocationService.selectBasDistLocationByLocationId(locationId);
        ajaxResult.put(AjaxResult.DATA_TAG, location);
        if (location.getParentId() != null) {
            set.add(location.getParentId());
        }
        ajaxResult.put("locationOptions", set.size() > 0 ? basDistLocationService.selectBasDistLocationByIds(set) : null);
        return ajaxResult;
    }

    /**
     * 新增地点
     */
    @PreAuthorize("@ss.hasPermi('system:location:add')")
    @Log(title = "新增地点", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasDistLocation basDistLocation) {
        int out = basDistLocationService.insertBasDistLocation(basDistLocation);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "location");
        return toAjax(out);
    }

    /**
     * 修改地点
     */
    @PreAuthorize("@ss.hasPermi('system:location:edit')")
    @Log(title = "修改地点", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasDistLocation basDistLocation) {
        int out = basDistLocationService.updateBasDistLocation(basDistLocation);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "location");
        return toAjax(out);
    }

    /**
     * 修改地点
     */
    @PreAuthorize("@ss.hasPermi('system:location:edit')")
    @Log(title = "修改地点", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasDistLocation basDistLocation) {
        int out = basDistLocationService.changeStatus(basDistLocation);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "location");
        return toAjax(out);
    }

    /**
     * 删除地点
     */
    @PreAuthorize("@ss.hasPermi('system:location:remove')")
    @Log(title = "删除地点", businessType = BusinessType.DELETE)
    @DeleteMapping("/{locationIds}")
    public AjaxResult remove(@PathVariable Long[] locationIds) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "location");
        return toAjax(basDistLocationService.deleteBasDistLocationByLocationIds(locationIds));
    }

    @GetMapping("/locationOptions")
    public AjaxResult locationOptions(BasDistLocation basDistLocation) {
        return AjaxResult.success(basDistLocationService.selectBasDistLocationByIds(new HashSet<>(Arrays.asList(basDistLocation.getLocationSelectList()))));
    }
}
