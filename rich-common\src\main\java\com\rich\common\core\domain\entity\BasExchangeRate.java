package com.rich.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 汇率对象 bas_exchange_rate
 *
 * <AUTHOR>
 * @date 2023-06-05
 */
public class BasExchangeRate extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 汇率ID
     */
    private Long exchangeRateId;


    private String currencyCode;
    private String currency;

    /**
     * 本位币
     */
    @Excel(name = "本位币")
    private String localCurrency;
    private String basicCurrency;

    /**
     * 基数
     */
    @Excel(name = "基数")
    private BigDecimal base;
    private BigDecimal baseShow;
    private BigDecimal buyRateShow;
    private BigDecimal sellRateShow;
    private BigDecimal settleRateShow;
    private BigDecimal exchangeRateShow;

    /**
     * 汇率
     */
    @Excel(name = "汇率")
    private BigDecimal exchangeRate;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date validFrom;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date validTo;
    private BigDecimal buyRate;
    private BigDecimal settleRate;
    private BigDecimal sellRate;
    @Excel(name = "外汇币")
    private String overseaCurrency;

    public BigDecimal getSellRateShow() {
        return sellRateShow;
    }

    public void setSellRateShow(BigDecimal sellRateShow) {
        this.sellRateShow = sellRateShow;
    }

    public BigDecimal getBuyRateShow() {
        return buyRateShow;
    }

    public void setBuyRateShow(BigDecimal buyRateShow) {
        this.buyRateShow = buyRateShow;
    }

    public BigDecimal getSettleRateShow() {
        return settleRateShow;
    }

    public void setSettleRateShow(BigDecimal settleRateShow) {
        this.settleRateShow = settleRateShow;
    }

    public String getLocalCurrency() {
        return localCurrency;
    }

    public void setLocalCurrency(String localCurrency) {
        this.localCurrency = localCurrency;
    }

    public String getOverseaCurrency() {
        return overseaCurrency;
    }

    public void setOverseaCurrency(String overseaCurrency) {
        this.overseaCurrency = overseaCurrency;
    }

    public BigDecimal getSellRate() {
        return sellRate;
    }

    public void setSellRate(BigDecimal sellRate) {
        this.sellRate = sellRate;
    }

    public BigDecimal getSettleRate() {
        return settleRate;
    }

    public void setSettleRate(BigDecimal settleRate) {
        this.settleRate = settleRate;
    }

    public BigDecimal getBuyRate() {
        return buyRate;
    }

    public void setBuyRate(BigDecimal buyRate) {
        this.buyRate = buyRate;
    }

    public BigDecimal getBaseShow() {
        return baseShow;
    }

    public void setBaseShow(BigDecimal baseShow) {
        this.baseShow = baseShow;
    }

    public BigDecimal getExchangeRateShow() {
        return exchangeRateShow;
    }

    public void setExchangeRateShow(BigDecimal exchangeRateShow) {
        this.exchangeRateShow = exchangeRateShow;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public Date getValidFrom() {
        return validFrom;
    }

    public void setValidFrom(Date validFrom) {
        this.validFrom = validFrom;
    }

    public Date getValidTo() {
        return validTo;
    }

    public void setValidTo(Date validTo) {
        this.validTo = validTo;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getBasicCurrency() {
        return basicCurrency;
    }

    public void setBasicCurrency(String basicCurrency) {
        this.basicCurrency = basicCurrency;
    }

    public void setExchangeRateId(Long exchangeRateId) {
        this.exchangeRateId = exchangeRateId;
    }

    public Long getExchangeRateId() {
        return exchangeRateId;
    }

    public void setBase(BigDecimal base) {
        this.base = base;
    }

    public BigDecimal getBase() {
        return base;
    }

    public void setExchangeRate(BigDecimal exchangeRate) {
        this.exchangeRate = exchangeRate;
    }

    public BigDecimal getExchangeRate() {
        return exchangeRate;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
}
