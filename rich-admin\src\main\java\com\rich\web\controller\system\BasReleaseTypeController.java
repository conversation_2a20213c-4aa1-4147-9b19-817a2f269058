package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasReleaseType;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasReleaseTypeService;
import com.rich.system.service.impl.RedisCacheImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 放货方式Controller
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@RestController
@RequestMapping("/system/releasetype")
public class BasReleaseTypeController extends BaseController {
    @Autowired
    private BasReleaseTypeService basReleaseTypeService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisCacheImpl RedisCache;

    /**
     * 查询放货方式列表
     */
    @PreAuthorize("@ss.hasPermi('system:releasetype:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasReleaseType basReleaseType) {
        startPage();
        List<BasReleaseType> list = basReleaseTypeService.selectBasReleaseTypeList(basReleaseType);
        return getDataTable(list);
    }

    @GetMapping("/selectList")
    public AjaxResult selectList() {
        List<BasReleaseType> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "releaseType");
        if (list == null) {
            RedisCache.releaseType();
            list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "releaseType");
        }
        return AjaxResult.success(list);
    }

    /**
     * 导出放货方式列表
     */
    @PreAuthorize("@ss.hasPermi('system:releasetype:export')")
    @Log(title = "放货方式", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasReleaseType basReleaseType) {
        List<BasReleaseType> list = basReleaseTypeService.selectBasReleaseTypeList(basReleaseType);
        ExcelUtil<BasReleaseType> util = new ExcelUtil<BasReleaseType>(BasReleaseType.class);
        util.exportExcel(response, list, "放货方式数据");
    }

    /**
     * 获取放货方式详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:releasetype:query')")
    @GetMapping(value = "/{releaseTypeId}")
    public AjaxResult getInfo(@PathVariable("releaseTypeId") Long releaseTypeId) {
        return AjaxResult.success(basReleaseTypeService.selectBasReleaseTypeByReleaseTypeId(releaseTypeId));
    }

    /**
     * 新增放货方式
     */
    @PreAuthorize("@ss.hasPermi('system:releasetype:add')")
    @Log(title = "放货方式", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasReleaseType basReleaseType) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "releaseType");
        return toAjax(basReleaseTypeService.insertBasReleaseType(basReleaseType));
    }

    /**
     * 修改放货方式
     */
    @PreAuthorize("@ss.hasPermi('system:releasetype:edit')")
    @Log(title = "放货方式", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasReleaseType basReleaseType) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "releaseType");
        return toAjax(basReleaseTypeService.updateBasReleaseType(basReleaseType));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:releasetype:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasReleaseType basReleaseType) {
        basReleaseType.setUpdateBy(getUserId());
        return toAjax(basReleaseTypeService.changeStatus(basReleaseType));
    }

    /**
     * 删除放货方式
     */
    @PreAuthorize("@ss.hasPermi('system:releasetype:remove')")
    @Log(title = "放货方式", businessType = BusinessType.DELETE)
    @DeleteMapping("/{releaseTypeIds}")
    public AjaxResult remove(@PathVariable Long[] releaseTypeIds) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "releaseType");
        return toAjax(basReleaseTypeService.deleteBasReleaseTypeByReleaseTypeIds(releaseTypeIds));
    }
}
