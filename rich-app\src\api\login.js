import request from '@/utils/request'

// 登录方法
export function login(username, password, code, uuid) {
    const data = {
        username,
        password,
        code,
        uuid
    }
    return request({
        'url': '/login',
        headers: {
            isToken: false
        },
        'method': 'post',
        'data': data
    })
}

// 微信授权登录方法
export function wechatLogin(code) {
    return request({
        'url': '/mp/user/login',
        headers: {
            isToken: false
        },
        'method': 'post',
        'data': {
            code,
            autoRegister: true // 开启自动注册功能
        }
    })
}

// 注册方法
export function register(data) {
    return request({
        url: '/register',
        headers: {
            isToken: false
        },
        method: 'post',
        data: data
    })
}

// 获取用户详细信息
export function getInfo() {
    return request({
        'url': '/mp/user/info',
        'method': 'get'
    })
}

// 退出方法
export function logout() {
    return request({
        'url': '/logout',
        'method': 'post'
    })
}

// 获取验证码
export function getCodeImg() {
    return request({
        'url': '/captchaImage',
        headers: {
            isToken: false
        },
        method: 'get',
        timeout: 20000
    })
}

// 获取手机验证码
export function getVerifyCode(mobile) {
    return request({
        'url': '/getVerifyCode',
        headers: {
            isToken: false
        },
        'method': 'post',
        'data': {mobile}
    })
}

// 绑定微信用户
export function bindWechatUser(data) {
    return request({
        'url': '/wechat/bindUser',
        headers: {
            isToken: false
        },
        'method': 'post',
        'data': data
    })
}
