package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasChargeType;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasChargeTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 费用类型Controller
 *
 * <AUTHOR>
 * @date 2022-08-30
 */
@RestController
@RequestMapping("/system/chargetype")
public class BasChargeTypeController extends BaseController {

    @Autowired
    private BasChargeTypeService basChargeTypeService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询费用类型列表
     */
    @PreAuthorize("@ss.hasPermi('system:chargetype:list')")
    @GetMapping("/list")
    public AjaxResult list(BasChargeType basChargeType) {
        return AjaxResult.success(basChargeTypeService.selectBasChargeTypeList(basChargeType));
    }

    @GetMapping("/selectList")
    public AjaxResult selectList(BasChargeType basChargeType) {
        List<BasChargeType> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "chargeType");
        if (list == null) {
            basChargeType.setStatus("0");
            list = basChargeTypeService.selectBasChargeTypeList(basChargeType);
            redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "chargeType");
            redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "chargeType", list);
        }
        return AjaxResult.success(AjaxResult.DATA_TAG, list);
    }

    @GetMapping("/chargeList")
    public AjaxResult chargeList() {
        List list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "chargeList");
        if (list == null) {
            list = basChargeTypeService.selectBasChargeList();
            redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "chargeList");
            redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "chargeList", list);
        }
        return AjaxResult.success(list);
    }

    /**
     * 导出费用类型列表
     */
    @PreAuthorize("@ss.hasPermi('system:chargetype:export')")
    @Log(title = "费用类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasChargeType basChargeType) {
        List<BasChargeType> list = basChargeTypeService.selectBasChargeTypeList(basChargeType);
        ExcelUtil<BasChargeType> util = new ExcelUtil<BasChargeType>(BasChargeType.class);
        util.exportExcel(response, list, "费用类型数据");
    }

    /**
     * 获取费用类型详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:chargetype:edit')")
    @GetMapping(value = "/{chargeTypeId}")
    public AjaxResult getInfo(@PathVariable("chargeTypeId") Long chargeTypeId) {
        AjaxResult ajaxResult = AjaxResult.success();
        ajaxResult.put(AjaxResult.DATA_TAG, basChargeTypeService.selectBasChargeTypeByChargeTypeId(chargeTypeId));
        ajaxResult.put("check", basChargeTypeService.getCheckChargeTypeDept(chargeTypeId));
        ajaxResult.put("enter", basChargeTypeService.getEnterChargeTypeDept(chargeTypeId));
        return ajaxResult;
    }

    /**
     * 新增费用类型
     */
    @PreAuthorize("@ss.hasPermi('system:chargetype:add')")
    @Log(title = "费用类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasChargeType basChargeType) {
        int out = basChargeTypeService.insertBasChargeType(basChargeType);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "chargeType");
        return toAjax(out);
    }

    /**
     * 修改费用类型
     */
    @PreAuthorize("@ss.hasPermi('system:chargetype:edit')")
    @Log(title = "费用类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasChargeType basChargeType) {
        int out = basChargeTypeService.updateBasChargeType(basChargeType);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "chargeType");
        return toAjax(out);
    }

    /**
     * 修改费用类型
     */
    @PreAuthorize("@ss.hasPermi('system:chargetype:edit')")
    @Log(title = "费用类型", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasChargeType basChargeType) {
        int out = basChargeTypeService.changeStatus(basChargeType);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "chargeType");
        return toAjax(out);
    }

    /**
     * 删除费用类型
     */
    @PreAuthorize("@ss.hasPermi('system:chargetype:remove')")
    @Log(title = "费用类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{chargeTypeIds}")
    public AjaxResult remove(@PathVariable Long[] chargeTypeIds) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "chargeType");
        return toAjax(basChargeTypeService.deleteBasChargeTypeByChargeTypeIds(chargeTypeIds));
    }
}
