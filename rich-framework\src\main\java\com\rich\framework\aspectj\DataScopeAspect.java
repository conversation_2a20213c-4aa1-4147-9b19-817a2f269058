package com.rich.framework.aspectj;

import com.rich.common.annotation.DataScope;
import com.rich.common.core.domain.BaseEntity;
import com.rich.common.core.domain.entity.RsStaff;
import com.rich.common.core.domain.entity.SysRole;
import com.rich.common.core.domain.model.LoginUser;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

/**
 * 数据过滤处理
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class DataScopeAspect {
    /**
     * 全部数据权限
     */
    public static final String DATA_SCOPE_ALL = "1";

    /**
     * 自定数据权限
     */
    public static final String DATA_SCOPE_CUSTOM = "2";

    /**
     * 部门数据权限
     */
    public static final String DATA_SCOPE_DEPT = "3";

    /**
     * 部门及以下数据权限
     */
    public static final String DATA_SCOPE_DEPT_AND_CHILD = "4";

    /**
     * 仅本人数据权限
     */
    public static final String DATA_SCOPE_SELF = "5";

    /**
     * 数据权限过滤关键字
     */
    public static final String DATA_SCOPE = "dataScope";

    @Before("@annotation(controllerDataScope)")
    public void doBefore(JoinPoint point, DataScope controllerDataScope) throws Throwable {
        clearDataScope(point);
        handleDataScope(point, controllerDataScope);
    }

    /**
     * 数据范围过滤
     *
     * @param joinPoint 切点
     * @param user      用户
     * @param deptAlias 部门表别名
     * @param userAlias 用户表别名
     */
    public static void dataScopeFilter(JoinPoint joinPoint, RsStaff user, String deptAlias, String subDeptAlias, String userAlias, String subUserAlias) {
        StringBuilder sqlString = new StringBuilder();

        // 循环遍历角色(有多少个部门就会添加拼接几次sql),将用户权限限制到他的部门内(多个部门就会有多个角色,拼接多次)
        for (SysRole role : user.getRoles()) {
            String dataScope = role.getDataScope();
            // 全部数据权限
            if (DATA_SCOPE_ALL.equals(dataScope)) {
                sqlString = new StringBuilder();
                break;
                // 自定义数据权限
            } else if (DATA_SCOPE_CUSTOM.equals(dataScope)) {
                sqlString.append(StringUtils.format(" OR {}.dept_id IN ( SELECT dept_id FROM mid_role_dept WHERE role_id = {} )", deptAlias, role.getRoleId()));
                sqlString.append(StringUtils.format(" OR {}.dept_id IN ( SELECT dept_id FROM mid_role_dept WHERE role_id = {} )", subDeptAlias, role.getRoleId()));
                // 部门数据权限
            } else if (DATA_SCOPE_DEPT.equals(dataScope)) {
                sqlString.append(StringUtils.format(" OR {}.dept_id = {} ", deptAlias, role.getDeptId()));
                sqlString.append(StringUtils.format("  OR {}.dept_id = {}", subDeptAlias, role.getDeptId()));
                // 部门及以下数据权限
            } else if (DATA_SCOPE_DEPT_AND_CHILD.equals(dataScope)) {

                sqlString.append(StringUtils.format(
                        " OR {}.staff_id in (SELECT a.staff_id FROM(SELECT DISTINCT ur.staff_id FROM mid_rs_staff_role ur LEFT JOIN sys_role r ON ur.role_id = r.role_id WHERE ur.dept_id IN ( SELECT dept_id FROM bas_dist_dept WHERE dept_id = {} OR find_in_set( {}, ancestors ) )AND r.position_id < 14)a) "
                        , userAlias, role.getDeptId(), role.getDeptId()));
                sqlString.append(StringUtils.format(
                        " OR {}.staff_id in (SELECT a.staff_id FROM(SELECT DISTINCT ur.staff_id FROM mid_rs_staff_role ur LEFT JOIN sys_role r ON ur.role_id = r.role_id WHERE ur.dept_id IN ( SELECT dept_id FROM bas_dist_dept WHERE dept_id = {} OR find_in_set( {}, ancestors ) )AND r.position_id < 14)a) "
                        , subUserAlias, role.getDeptId(), role.getDeptId()));
                // 仅本人数据权限
            } else if (DATA_SCOPE_SELF.equals(dataScope)) {
                if (StringUtils.isNotBlank(userAlias)) {
                    sqlString.append(StringUtils.format("  OR {}.staff_id = {}", userAlias, user.getStaffId()));
                    sqlString.append(StringUtils.format("  OR {}.staff_id = {}", subUserAlias, user.getStaffId()));
                } else {
                    // 数据权限为仅本人且没有userAlias别名不查询任何数据
                    sqlString.append(StringUtils.format(" OR {}.dept_id = 0 ", deptAlias));
                    sqlString.append(StringUtils.format(" OR {}.dept_id = 0", subDeptAlias));
                }
            }
        }
        if (StringUtils.isNotBlank(sqlString.toString())) {
            Object params = joinPoint.getArgs()[0];
            if (StringUtils.isNotNull(params) && params instanceof BaseEntity) {
                BaseEntity baseEntity = (BaseEntity) params;
                baseEntity.getParams().put(DATA_SCOPE, " AND (" + sqlString.substring(4) + ")");
            }
        }
    }

    protected void handleDataScope(final JoinPoint joinPoint, DataScope controllerDataScope) {
        // 获取当前的用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (StringUtils.isNotNull(loginUser)) {
            RsStaff currentUser = loginUser.getUser();
            // 如果是超级管理员，则不过滤数据
            if (StringUtils.isNotNull(currentUser) && !currentUser.isAdmin()) {
//                 String permission = StringUtils.defaultIfEmpty(controllerDataScope.permission(), PermissionContextHolder.getContext());
                // 在原本只有部门别名和用户别名下添加了子部门和子用户
                dataScopeFilter(joinPoint, currentUser, controllerDataScope.deptAlias(), controllerDataScope.subDeptAlias(),
                        controllerDataScope.userAlias(), controllerDataScope.subUserAlias());
            }
        }
    }


    /**
     * 拼接权限sql前先清空params.dataScope参数防止注入
     */
    private void clearDataScope(final JoinPoint joinPoint) {
        Object params = joinPoint.getArgs()[0];
        if (StringUtils.isNotNull(params) && params instanceof BaseEntity) {
            BaseEntity baseEntity = (BaseEntity) params;
            baseEntity.getParams().put(DATA_SCOPE, "");
        }
    }
}
