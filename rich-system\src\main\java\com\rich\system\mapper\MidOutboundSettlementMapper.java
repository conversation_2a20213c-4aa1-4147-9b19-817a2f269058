package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.MidOutboundSettlement;
import org.apache.ibatis.annotations.Mapper;

/**
 * 仓租结算中间Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-11
 */
@Mapper
public interface MidOutboundSettlementMapper {
    /**
     * 查询仓租结算中间
     *
     * @param outboundRecordId 仓租结算中间主键
     * @return 仓租结算中间
     */
    MidOutboundSettlement selectMidOutboundSettlementByOutboundRecordId(Long outboundRecordId);

    /**
     * 查询仓租结算中间列表
     *
     * @param midOutboundSettlement 仓租结算中间
     * @return 仓租结算中间集合
     */
    List<MidOutboundSettlement> selectMidOutboundSettlementList(MidOutboundSettlement midOutboundSettlement);

    /**
     * 新增仓租结算中间
     *
     * @param midOutboundSettlement 仓租结算中间
     * @return 结果
     */
    int insertMidOutboundSettlement(MidOutboundSettlement midOutboundSettlement);

    /**
     * 修改仓租结算中间
     *
     * @param midOutboundSettlement 仓租结算中间
     * @return 结果
     */
    int updateMidOutboundSettlement(MidOutboundSettlement midOutboundSettlement);

    /**
     * 删除仓租结算中间
     *
     * @param outboundRecordId 仓租结算中间主键
     * @return 结果
     */
    int deleteMidOutboundSettlementByOutboundRecordId(Long outboundRecordId);

    /**
     * 批量删除仓租结算中间
     *
     * @param outboundRecordIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteMidOutboundSettlementByOutboundRecordIds(Long[] outboundRecordIds);

    void batchInsert(List<MidOutboundSettlement> list);
}
