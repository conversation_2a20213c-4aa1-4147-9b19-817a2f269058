package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.RsStaff;
import com.rich.common.core.domain.entity.SysRole;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.StringUtils;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasPositionService;
import com.rich.system.service.RsStaffService;
import com.rich.system.service.SysRoleService;
import com.rich.system.service.impl.RedisCacheImpl;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/user")
public class RsStaffController extends BaseController {

    @Autowired
    private RsStaffService userService;
    @Autowired
    private SysRoleService roleService;
    @Autowired
    private BasPositionService postService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private RedisCacheImpl Redis;

    /**
     * 获取用户列表
     */
    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping(value = "/list")
    public TableDataInfo list(RsStaff user) {
        startPage();
        List<RsStaff> list = userService.selectUserBasicList(user);
        return getDataTable(list);
    }

    /**
     * 获取用户列表
     */
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping(value = "/detailList")
    public TableDataInfo detailList(RsStaff user) {
        startPage();
        List<RsStaff> list = userService.selectUserList(user);
        return getDataTable(list);
    }

    /**
     * 获取用户列表
     */
    @GetMapping(value = "/selectList")
    public AjaxResult selectList(RsStaff user) {
        user.setStaffJobStatus("0");
        return AjaxResult.success(userService.selectList(user));
    }

    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:user:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsStaff user) {
        List<RsStaff> list = userService.selectUserList(user);
        ExcelUtil<RsStaff> util = new ExcelUtil<RsStaff>(RsStaff.class);
        util.exportExcel(response, list, "用户数据");
    }

    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('system:user:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<RsStaff> util = new ExcelUtil<RsStaff>(RsStaff.class);
        List<RsStaff> userList = util.importExcel(file.getInputStream());
        String message = userService.importUser(userList, updateSupport);
        return AjaxResult.success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<RsStaff> util = new ExcelUtil<RsStaff>(RsStaff.class);
        util.importTemplateExcel(response, "用户数据");
    }

    /**
     * 根据用户编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @GetMapping(value = {"/", "/{userId}"})
    public AjaxResult getInfo(@PathVariable(value = "userId", required = false) Long userId) {
        userService.checkUserDataScope(userId);
        AjaxResult ajax = AjaxResult.success();
        List<SysRole> roles = roleService.selectRoleAll();
        ajax.put("roles", RsStaff.isAdmin(userService.selectUserById(userId).getRole()) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        ajax.put("posts", postService.selectPostAll());
        if (StringUtils.isNotNull(userId)) {
            RsStaff sysUser = userService.selectUserById(userId);
            ajax.put(AjaxResult.DATA_TAG, sysUser);
            ajax.put("postIds", postService.selectPostListByUserId(userId));
            ajax.put("roleIds", sysUser.getRoles().stream().map(SysRole::getRoleId).collect(Collectors.toList()));
        }
        return ajax;
    }

    /**
     * 新增用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:add')")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody RsStaff user) {
        if (!userService.checkUserNameUnique(user)) {
            return AjaxResult.error("新增用户'" + user.getStaffUsername() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(user.getStaffPhoneNum()) && !userService.checkPhoneUnique(user)) {
            return AjaxResult.error("新增用户'" + user.getStaffUsername() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getStaffEmailEnterprise()) && !userService.checkEmailUnique(user)) {
            return AjaxResult.error("新增用户'" + user.getStaffUsername() + "'失败，邮箱账号已存在");
        }
        for (String staffByRoleDept : redisCache.keys(CacheConstants.DATA_CACHE_KEY + "staffByRoleDept" + "*")) {
            redisCache.deleteObject(staffByRoleDept);
        }
        for (String roleStaff : redisCache.keys(CacheConstants.DATA_CACHE_KEY + "roleStaff" + "*")) {
            redisCache.deleteObject(roleStaff);
        }
        user.setCreateBy(getUserId());
        user.setStaffPassword(SecurityUtils.encryptPassword(user.getStaffPassword()));
        return toAjax(userService.insertUser(user));
    }

    /**
     * 修改用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody RsStaff user) {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getStaffId());
        if (StringUtils.isNotEmpty(user.getStaffPhoneNum()) && !userService.checkPhoneUnique(user)) {
            return AjaxResult.error("修改用户'" + user.getStaffUsername() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getStaffEmailEnterprise()) && !userService.checkEmailUnique(user)) {
            return AjaxResult.error("修改用户'" + user.getStaffUsername() + "'失败，邮箱账号已存在");
        }
        for (String staffByRoleDept : redisCache.keys(CacheConstants.DATA_CACHE_KEY + "staffByRoleDept" + "*")) {
            redisCache.deleteObject(staffByRoleDept);
        }
        for (String roleStaff : redisCache.keys(CacheConstants.DATA_CACHE_KEY + "roleStaff" + "*")) {
            redisCache.deleteObject(roleStaff);
        }
        user.setUpdateBy(getUserId());
        return toAjax(userService.updateUser(user));
    }

    /**
     * 删除用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:remove')")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable Long[] userIds) {
        if (ArrayUtils.contains(userIds, getUserId())) {
            return error("当前用户不能删除");
        }
        for (String staffByRoleDept : redisCache.keys(CacheConstants.DATA_CACHE_KEY + "staffByRoleDept" + "*")) {
            redisCache.deleteObject(staffByRoleDept);
        }
        for (String roleStaff : redisCache.keys(CacheConstants.DATA_CACHE_KEY + "roleStaff" + "*")) {
            redisCache.deleteObject(roleStaff);
        }
        return toAjax(userService.deleteUserByIds(userIds));
    }

    /**
     * 重置密码
     */
    @PreAuthorize("@ss.hasPermi('system:user:resetPwd')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public AjaxResult resetPwd(@RequestBody RsStaff user) {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getStaffId());
        user.setStaffPassword(SecurityUtils.encryptPassword(user.getStaffPassword()));
        user.setUpdateBy(getUserId());
        for (String staffByRoleDept : redisCache.keys(CacheConstants.DATA_CACHE_KEY + "staffByRoleDept" + "*")) {
            redisCache.deleteObject(staffByRoleDept);
        }
        for (String roleStaff : redisCache.keys(CacheConstants.DATA_CACHE_KEY + "roleStaff" + "*")) {
            redisCache.deleteObject(roleStaff);
        }
        return toAjax(userService.resetPwd(user));
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsStaff user) {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getStaffId());
        user.setUpdateBy(getUserId());
        for (String staffByRoleDept : redisCache.keys(CacheConstants.DATA_CACHE_KEY + "staffByRoleDept" + "*")) {
            redisCache.deleteObject(staffByRoleDept);
        }
        for (String roleStaff : redisCache.keys(CacheConstants.DATA_CACHE_KEY + "roleStaff" + "*")) {
            redisCache.deleteObject(roleStaff);
        }
        return toAjax(userService.updateUserStatus(user));
    }

    /**
     * 获取缓存中的所有员工
     */
    @GetMapping("/allRsStaff")
    public AjaxResult allRsStaff() {
        List<RsStaff> rsStaffs = (List<RsStaff>) redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "allRsStaff");
        if (rsStaffs == null || rsStaffs.isEmpty()) {
            Redis.allRsStaff();
            rsStaffs = (List<RsStaff>) redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "allRsStaff");
        }
        return AjaxResult.success(rsStaffs);
    }
}
