<template>
  <view class="template-page">
    <view class="page-header">
      <view class="title">标签模板设计</view>
      <button type="primary" @click="saveTemplate">保存模板</button>
    </view>

    <view class="form-container">
      <view class="form-group">
        <view class="form-title">基础设置</view>
        <view class="form-item">
          <text class="label">模板名称</text>
          <input v-model="template.name" placeholder="请输入模板名称" type="text"/>
        </view>
        <view class="form-item">
          <text class="label">使用默认模板</text>
          <picker :range="defaultTemplateNames" :value="defaultTemplateIndex" @change="loadDefaultTemplate">
            <view class="picker">
              {{ defaultTemplateNames[defaultTemplateIndex] }}
            </view>
          </picker>
        </view>
        <view class="form-item">
          <button type="primary" @click="loadFromJsonFile">使用JSON默认模板</button>
        </view>
        <view class="form-item">
          <text class="label">标签宽度(mm)</text>
          <input v-model="template.width" placeholder="请输入宽度" type="number"/>
        </view>
        <view class="form-item">
          <text class="label">标签高度(mm)</text>
          <input v-model="template.height" placeholder="请输入高度" type="number"/>
        </view>
        <view class="form-item">
          <text class="label">标签间距(mm)</text>
          <input v-model="template.gap" placeholder="请输入间距" type="number"/>
        </view>
      </view>

      <view class="form-group">
        <view class="form-title">内容设置</view>
        <view class="form-item">
          <view class="label-with-btn">
            <text class="label">文本内容</text>
            <button size="mini" type="primary" @click="addTextElement">添加</button>
          </view>
        </view>

        <view v-if="template.elements.text.length > 0" class="element-list">
          <view v-for="(item, index) in template.elements.text" :key="index" class="element-item">
            <view class="element-header">
              <text class="element-title">文本 {{ index + 1 }}</text>
              <text class="element-delete" @click="deleteElement('text', index)">删除</text>
            </view>
            <view class="element-form">
              <view class="form-item">
                <text class="label">内容</text>
                <input v-model="item.content" placeholder="请输入文本内容" type="text"/>
              </view>
              <view class="form-item">
                <text class="label">X坐标</text>
                <input v-model="item.x" placeholder="X位置" type="number"/>
              </view>
              <view class="form-item">
                <text class="label">Y坐标</text>
                <input v-model="item.y" placeholder="Y位置" type="number"/>
              </view>
              <view class="form-item">
                <text class="label">字体</text>
                <picker :range="fontOptions" :value="item.fontIndex" @change="bindFontChange($event, 'text', index)">
                  <view class="picker">
                    {{ fontOptions[item.fontIndex] }}
                  </view>
                </picker>
              </view>
              <view class="form-item">
                <text class="label">旋转</text>
                <picker :range="rotationOptions" :value="item.rotationIndex"
                        @change="bindRotationChange($event, 'text', index)">
                  <view class="picker">
                    {{ rotationOptions[item.rotationIndex] }}
                  </view>
                </picker>
              </view>
              <view class="form-item">
                <text class="label">字体大小</text>
                <picker :range="sizeOptions" :value="item.sizeIndex" @change="bindSizeChange($event, 'text', index)">
                  <view class="picker">
                    {{ sizeOptions[item.sizeIndex] }}
                  </view>
                </picker>
              </view>
            </view>
          </view>
        </view>

        <view class="form-item">
          <view class="label-with-btn">
            <text class="label">条形码</text>
            <button size="mini" type="primary" @click="addBarcodeElement">添加</button>
          </view>
        </view>

        <view v-if="template.elements.barcode.length > 0" class="element-list">
          <view v-for="(item, index) in template.elements.barcode" :key="index + 1000" class="element-item">
            <view class="element-header">
              <text class="element-title">条形码 {{ index + 1 }}</text>
              <text class="element-delete" @click="deleteElement('barcode', index)">删除</text>
            </view>
            <view class="element-form">
              <view class="form-item">
                <text class="label">内容</text>
                <input v-model="item.content" placeholder="请输入条形码内容" type="text"/>
              </view>
              <view class="form-item">
                <text class="label">X坐标</text>
                <input v-model="item.x" placeholder="X位置" type="number"/>
              </view>
              <view class="form-item">
                <text class="label">Y坐标</text>
                <input v-model="item.y" placeholder="Y位置" type="number"/>
              </view>
              <view class="form-item">
                <text class="label">类型</text>
                <picker :range="barcodeOptions" :value="item.typeIndex" @change="bindBarcodeTypeChange($event, index)">
                  <view class="picker">
                    {{ barcodeOptions[item.typeIndex] }}
                  </view>
                </picker>
              </view>
              <view class="form-item">
                <text class="label">高度</text>
                <input v-model="item.height" placeholder="高度" type="number"/>
              </view>
              <view class="form-item">
                <text class="label">可读</text>
                <switch :checked="item.readable" @change="switchChange($event, 'barcode', index, 'readable')"/>
              </view>
            </view>
          </view>
        </view>

        <view class="form-item">
          <view class="label-with-btn">
            <text class="label">二维码</text>
            <button size="mini" type="primary" @click="addQRElement">添加</button>
          </view>
        </view>

        <view v-if="template.elements.qrcode.length > 0" class="element-list">
          <view v-for="(item, index) in template.elements.qrcode" :key="index + 2000" class="element-item">
            <view class="element-header">
              <text class="element-title">二维码 {{ index + 1 }}</text>
              <text class="element-delete" @click="deleteElement('qrcode', index)">删除</text>
            </view>
            <view class="element-form">
              <view class="form-item">
                <text class="label">内容</text>
                <input v-model="item.content" placeholder="请输入二维码内容" type="text"/>
              </view>
              <view class="form-item">
                <text class="label">X坐标</text>
                <input v-model="item.x" placeholder="X位置" type="number"/>
              </view>
              <view class="form-item">
                <text class="label">Y坐标</text>
                <input v-model="item.y" placeholder="Y位置" type="number"/>
              </view>
              <view class="form-item">
                <text class="label">等级</text>
                <picker :range="qrLevelOptions" :value="item.levelIndex" @change="bindQRLevelChange($event, index)">
                  <view class="picker">
                    {{ qrLevelOptions[item.levelIndex] }}
                  </view>
                </picker>
              </view>
              <view class="form-item">
                <text class="label">尺寸</text>
                <input v-model="item.width" placeholder="尺寸" type="number"/>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="template-preview">
      <view class="preview-title">模板预览</view>
      <view :style="{width: template.width + 'mm', height: template.height + 'mm'}" class="preview-content">
        <view v-for="(item, index) in template.elements.text" :key="index + 3000"
              :style="{left: item.x + 'mm', top: item.y + 'mm'}"
              class="preview-text">
          {{ item.content }}
        </view>
        <view v-for="(item, index) in template.elements.barcode" :key="index + 4000"
              :style="{left: item.x + 'mm', top: item.y + 'mm', height: item.height + 'mm'}"
              class="preview-barcode">
          条形码: {{ item.content }}
        </view>
        <view v-for="(item, index) in template.elements.qrcode" :key="index + 5000"
              :style="{left: item.x + 'mm', top: item.y + 'mm', width: item.width + 'mm', height: item.width + 'mm'}"
              class="preview-qrcode">
          二维码
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import defaultTemplates from './default-templates.js'

export default {
  data() {
    // 从默认模板获取模板名称列表
    const defaultTemplateNames = defaultTemplates.map(t => t.name);
    
    return {
      template: {
        name: '新模板',
        width: 40,
        height: 30,
        gap: 2,
        elements: {
          text: [],
          barcode: [],
          qrcode: []
        }
      },
      defaultTemplates: defaultTemplates,
      fontOptions: ['TSS24.BF2', '8x12', '12x20', '16x24', '20x24', '24x32', '28x48'],
      rotationOptions: ['0°', '90°', '180°', '270°'],
      sizeOptions: ['1x1', '1x2', '2x1', '2x2', '3x3'],
      barcodeOptions: ['128', '39', '93', 'EAN8', 'EAN13', 'UPC-A', 'UPC-E', 'ITF', 'Codabar'],
      qrLevelOptions: ['L', 'M', 'Q', 'H'],
      defaultTemplateNames: defaultTemplateNames,
      defaultTemplateIndex: 0
    }
  },

  onLoad(options) {
    // 如果是编辑模式，从storage中读取模板数据
    if (options.id) {
      try {
        const templates = uni.getStorageSync('label_templates') || '[]'
        const templateList = JSON.parse(templates)
        const currentTemplate = templateList.find(item => item.id === options.id)

        if (currentTemplate) {
          this.template = currentTemplate
        }
      } catch (e) {
        this.showToast('获取模板失败')
        console.error(e)
      }
    } else {
      // 新建模板时，使用默认的NODAT货物标签模板
      const defaultNodatIndex = this.defaultTemplates.findIndex(t => t.id === 'nodat-cargo-label')
      if (defaultNodatIndex !== -1) {
        // 创建深拷贝，避免直接修改默认模板
        this.template = JSON.parse(JSON.stringify(this.defaultTemplates[defaultNodatIndex]))
        // 设置默认模板索引
        this.defaultTemplateIndex = defaultNodatIndex
        // 清除ID，让保存时生成新ID
        this.template.id = null
      }
    }
  },

  methods: {
    /**
     * 添加文本元素
     */
    addTextElement() {
      this.template.elements.text.push({
        content: '文本内容',
        x: 10,
        y: 10,
        fontIndex: 0,
        rotationIndex: 0,
        sizeIndex: 0
      })
    },

    /**
     * 添加条形码元素
     */
    addBarcodeElement() {
      this.template.elements.barcode.push({
        content: '123456789',
        x: 10,
        y: 50,
        typeIndex: 0,
        height: 30,
        readable: 1,
        narrow: 2,
        wide: 4
      })
    },

    /**
     * 添加二维码元素
     */
    addQRElement() {
      this.template.elements.qrcode.push({
        content: 'https://example.com',
        x: 10,
        y: 90,
        levelIndex: 0,
        width: 20,
        mode: 'A'
      })
    },

    /**
     * 删除元素
     * @param {String} type 元素类型
     * @param {Number} index 元素索引
     */
    deleteElement(type, index) {
      this.template.elements[type].splice(index, 1)
    },

    /**
     * 字体选择变更事件
     */
    bindFontChange(e, type, index) {
      this.template.elements[type][index].fontIndex = e.detail.value
    },

    /**
     * 旋转选择变更事件
     */
    bindRotationChange(e, type, index) {
      this.template.elements[type][index].rotationIndex = e.detail.value
    },

    /**
     * 尺寸选择变更事件
     */
    bindSizeChange(e, type, index) {
      this.template.elements[type][index].sizeIndex = e.detail.value
    },

    /**
     * 条形码类型变更事件
     */
    bindBarcodeTypeChange(e, index) {
      this.template.elements.barcode[index].typeIndex = e.detail.value
    },

    /**
     * 二维码等级变更事件
     */
    bindQRLevelChange(e, index) {
      this.template.elements.qrcode[index].levelIndex = e.detail.value
    },

    /**
     * 开关变更事件
     */
    switchChange(e, type, index, prop) {
      this.template.elements[type][index][prop] = e.detail.value ? 1 : 0
    },

    /**
     * 保存模板
     */
    saveTemplate() {
      if (!this.template.name) {
        this.showToast('请输入模板名称')
        return
      }

      if (!this.template.width || !this.template.height) {
        this.showToast('请输入标签尺寸')
        return
      }

      try {
        // 从本地存储获取已有模板
        const templates = uni.getStorageSync('label_templates') || '[]'
        let templateList = JSON.parse(templates)

        // 如果模板没有ID，说明是新建
        if (!this.template.id) {
          this.template.id = Date.now().toString()
          templateList.push(this.template)
        } else {
          // 更新已有模板
          const index = templateList.findIndex(item => item.id === this.template.id)
          if (index !== -1) {
            templateList[index] = this.template
          } else {
            templateList.push(this.template)
          }
        }

        // 保存到本地存储
        uni.setStorageSync('label_templates', JSON.stringify(templateList))
        this.showToast('保存成功')

        // 返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      } catch (e) {
        this.showToast('保存失败')
        console.error(e)
      }
    },

    /**
     * 显示提示
     */
    showToast(title) {
      uni.showToast({
        title: title,
        icon: 'none',
        duration: 2000
      })
    },

    /**
     * 加载默认模板
     */
    loadDefaultTemplate(e) {
      const index = e.detail.value
      this.defaultTemplateIndex = index

      // 创建深拷贝，避免直接修改默认模板
      const templateToLoad = JSON.parse(JSON.stringify(this.defaultTemplates[index]))

      // 保留当前模板的ID和名称
      if (this.template && this.template.id) {
        templateToLoad.id = this.template.id
        templateToLoad.name = this.template.name
      }

      this.template = templateToLoad
    },

    /**
     * 从JSON文件加载默认模板
     */
    loadFromJsonFile() {
      // 从默认模板列表加载第一个模板
      if (this.defaultTemplates && this.defaultTemplates.length > 0) {
        this.defaultTemplateIndex = 0
        // 创建深拷贝，避免直接修改默认模板
        const templateToLoad = JSON.parse(JSON.stringify(this.defaultTemplates[0]))

        // 保留当前模板的ID和名称，如果有的话
        if (this.template && this.template.id) {
          templateToLoad.id = this.template.id
          templateToLoad.name = this.template.name
        }

        this.template = templateToLoad
      }
    }
  }
}
</script>

<style lang="scss">
.template-page {
  padding: 20rpx;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20rpx;
    margin-bottom: 20rpx;
    border-bottom: 1px solid #eee;

    .title {
      font-size: 36rpx;
      font-weight: bold;
    }
  }

  .form-container {
    .form-group {
      margin-bottom: 40rpx;
      border: 1px solid #eee;
      border-radius: 10rpx;
      padding: 20rpx;

      .form-title {
        font-size: 32rpx;
        font-weight: bold;
        margin-bottom: 20rpx;
      }

      .form-item {
        margin-bottom: 20rpx;

        .label, .label-with-btn {
          display: block;
          margin-bottom: 10rpx;
          font-size: 28rpx;
          color: #666;
        }

        .label-with-btn {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        input {
          height: 70rpx;
          border: 1px solid #ddd;
          border-radius: 6rpx;
          padding: 0 20rpx;
          font-size: 28rpx;
        }

        .picker {
          height: 70rpx;
          line-height: 70rpx;
          border: 1px solid #ddd;
          border-radius: 6rpx;
          padding: 0 20rpx;
          font-size: 28rpx;
        }
      }
    }
  }

  .element-list {
    .element-item {
      margin-bottom: 30rpx;
      padding: 20rpx;
      border: 1px solid #ddd;
      border-radius: 8rpx;

      .element-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20rpx;

        .element-title {
          font-weight: bold;
        }

        .element-delete {
          color: #e74c3c;
        }
      }
    }
  }

  .template-preview {
    margin-top: 40rpx;
    padding: 20rpx;
    border: 1px solid #eee;

    .preview-title {
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
    }

    .preview-content {
      position: relative;
      margin: 0 auto;
      border: 1px dashed #ddd;
      background-color: #fff;
      min-height: 200rpx;

      .preview-text, .preview-barcode, .preview-qrcode {
        position: absolute;
        font-size: 24rpx;
        word-break: break-all;
        overflow: hidden;
      }

      .preview-barcode {
        background-color: #f0f0f0;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 4rpx;
        font-size: 20rpx;
      }

      .preview-qrcode {
        background-color: #f0f0f0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20rpx;
      }
    }
  }
}
</style> 