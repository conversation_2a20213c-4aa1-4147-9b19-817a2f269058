package com.rich.common.core.domain.entity;

import com.rich.common.core.domain.BaseEntity;

/**
 * 货量对象 mid_revenue_tons
 *
 * <AUTHOR>
 * @date 2023-05-04
 */
public class MidRevenueTons extends BaseEntity {
    private static final long serialVersionUID = 1L;
    private Long quotationId;

    private Integer index;
    
    private Integer count;

    private Long unitId;

    private String unit;

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public void setQuotationId(Long quotationId) {
        this.quotationId = quotationId;
    }

    public Long getQuotationId() {
        return quotationId;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Integer getCount() {
        return count;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public Long getUnitId() {
        return unitId;
    }

}
