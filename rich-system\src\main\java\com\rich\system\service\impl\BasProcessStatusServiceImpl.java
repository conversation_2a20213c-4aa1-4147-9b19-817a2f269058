package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.BasProcessStatus;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.system.mapper.BasProcessStatusMapper;
import com.rich.system.service.BasProcessStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 进度状态Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-05
 */
@Service
public class BasProcessStatusServiceImpl implements BasProcessStatusService {
    @Autowired
    private BasProcessStatusMapper basProcessStatusMapper;

    /**
     * 查询进度状态
     *
     * @param processStatusId 进度状态主键
     * @return 进度状态
     */
    @Override
    public BasProcessStatus selectBasProcessStatusByProcessStatusId(Long processStatusId) {
        return basProcessStatusMapper.selectBasProcessStatusByProcessStatusId(processStatusId);
    }

    /**
     * 查询进度状态列表
     *
     * @param basProcessStatus 进度状态
     * @return 进度状态
     */
    @Override
    public List<BasProcessStatus> selectBasProcessStatusList(BasProcessStatus basProcessStatus) {
        return basProcessStatusMapper.selectBasProcessStatusList(basProcessStatus);
    }

    /**
     * 新增进度状态
     *
     * @param basProcessStatus 进度状态
     * @return 结果
     */
    @Override
    public int insertBasProcessStatus(BasProcessStatus basProcessStatus) {
        basProcessStatus.setCreateTime(DateUtils.getNowDate());
        basProcessStatus.setCreateBy(SecurityUtils.getUserId());
        return basProcessStatusMapper.insertBasProcessStatus(basProcessStatus);
    }

    /**
     * 修改进度状态
     *
     * @param basProcessStatus 进度状态
     * @return 结果
     */
    @Override
    public int updateBasProcessStatus(BasProcessStatus basProcessStatus) {
        basProcessStatus.setUpdateTime(DateUtils.getNowDate());
        basProcessStatus.setUpdateBy(SecurityUtils.getUserId());
        return basProcessStatusMapper.updateBasProcessStatus(basProcessStatus);
    }

    /**
     * 修改进度状态状态
     *
     * @param basProcessStatus 进度状态
     * @return 进度状态
     */
    @Override
    public int changeStatus(BasProcessStatus basProcessStatus) {
        return basProcessStatusMapper.updateBasProcessStatus(basProcessStatus);
    }

    /**
     * 批量删除进度状态
     *
     * @param processStatusIds 需要删除的进度状态主键
     * @return 结果
     */
    @Override
    public int deleteBasProcessStatusByProcessStatusIds(Long[] processStatusIds) {
        return basProcessStatusMapper.deleteBasProcessStatusByProcessStatusIds(processStatusIds);
    }

    /**
     * 删除进度状态信息
     *
     * @param processStatusId 进度状态主键
     * @return 结果
     */
    @Override
    public int deleteBasProcessStatusByProcessStatusId(Long processStatusId) {
        return basProcessStatusMapper.deleteBasProcessStatusByProcessStatusId(processStatusId);
    }
}
