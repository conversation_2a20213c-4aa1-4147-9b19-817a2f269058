package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasTradingTerms;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 贸易条款Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-30
 */
@Mapper
public interface BasTradingTermsMapper {
    /**
     * 查询贸易条款
     *
     * @param tradingTermsId 贸易条款主键
     * @return 贸易条款
     */
    BasTradingTerms selectBasTradingTermsByTradingTermsId(Long tradingTermsId);

    /**
     * 查询贸易条款列表
     *
     * @param basTradingTerms 贸易条款
     * @return 贸易条款集合
     */
    List<BasTradingTerms> selectBasTradingTermsList(BasTradingTerms basTradingTerms);

    /**
     * 新增贸易条款
     *
     * @param basTradingTerms 贸易条款
     * @return 结果
     */
    int insertBasTradingTerms(BasTradingTerms basTradingTerms);

    /**
     * 修改贸易条款
     *
     * @param basTradingTerms 贸易条款
     * @return 结果
     */
    int updateBasTradingTerms(BasTradingTerms basTradingTerms);

    /**
     * 删除贸易条款
     *
     * @param tradingTermsId 贸易条款主键
     * @return 结果
     */
    int deleteBasTradingTermsByTradingTermsId(Long tradingTermsId);

    /**
     * 批量删除贸易条款
     *
     * @param tradingTermsIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasTradingTermsByTradingTermsIds(Long[] tradingTermsIds);
}
