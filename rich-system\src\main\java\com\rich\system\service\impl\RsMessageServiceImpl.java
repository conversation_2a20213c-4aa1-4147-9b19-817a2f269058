package com.rich.system.service.impl;

import com.rich.common.config.WebSocket;
import com.rich.common.core.domain.entity.RsMessage;
import com.rich.common.core.domain.entity.RsStaff;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.system.mapper.RsMessageMapper;
import com.rich.system.mapper.RsStaffMapper;
import com.rich.system.service.RsMessageService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 消息通知Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-02-15
 */
@Service

public class RsMessageServiceImpl implements RsMessageService {
    @Autowired
    private RsMessageMapper rsMessageMapper;
    @Autowired
    private RsStaffMapper rsStaffMapper;
    @Autowired
    private WebSocket webSocket;

    /**
     * 查询消息通知
     *
     * @param messageId 消息通知主键
     * @return 消息通知
     */
    @Override
    public RsMessage selectRsMessageByMessageId(Long messageId) {
        return rsMessageMapper.selectRsMessageByMessageId(messageId);
    }

    /**
     * 查询消息通知列表
     *
     * @param rsMessage 消息通知
     * @return 消息通知
     */
    @Override
    public List<RsMessage> selectRsMessageList(RsMessage rsMessage) {
        return rsMessageMapper.selectRsMessageList(rsMessage);
    }

    /**
     * 新增消息通知
     *
     * @param rsMessage 消息通知
     * @return 结果
     */
    @Override
    public int insertRsMessage(RsMessage rsMessage) {
        rsMessage.setCreateTime(DateUtils.getNowDate());
        rsMessage.setCreateBy(SecurityUtils.getUserId());
        RsStaff staff = rsStaffMapper.selectUserById(Long.parseLong(rsMessage.getMessageOwner()));
        webSocket.sendOneMessage(staff.getStaffId() + staff.getStaffCode(), rsMessage.getMessageTitle());
        return rsMessageMapper.insertRsMessage(rsMessage);
    }

    /**
     * 修改消息通知
     *
     * @param rsMessage 消息通知
     * @return 结果
     */
    @Override
    public int updateRsMessage(RsMessage rsMessage) {
        rsMessage.setUpdateBy(SecurityUtils.getUserId());
        rsMessage.setUpdateTime(DateUtils.getNowDate());
        return rsMessageMapper.updateRsMessage(rsMessage);
    }

    /**
     * 修改消息通知状态
     *
     * @param rsMessage 消息通知
     * @return 消息通知
     */
    @Override
    public int changeStatus(RsMessage rsMessage) {
        return rsMessageMapper.updateRsMessage(rsMessage);
    }

    @Override
    public int countNewMessage(RsMessage rsMessage) {
        return rsMessageMapper.countNewMessage(rsMessage);
    }

    /**
     * 批量删除消息通知
     *
     * @param messageIds 需要删除的消息通知主键
     * @return 结果
     */
    @Override
    public int deleteRsMessageByMessageIds(Long[] messageIds) {
        return rsMessageMapper.deleteRsMessageByMessageIds(messageIds);
    }

    /**
     * 删除消息通知信息
     *
     * @param messageId 消息通知主键
     * @return 结果
     */
    @Override
    public int deleteRsMessageByMessageId(Long messageId) {
        return rsMessageMapper.deleteRsMessageByMessageId(messageId);
    }
}
