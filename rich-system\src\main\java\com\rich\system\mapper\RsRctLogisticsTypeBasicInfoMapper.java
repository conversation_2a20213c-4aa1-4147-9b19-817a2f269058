package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsRctLogisticsTypeBasicInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 操作单基础物流信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-15
 */
@Mapper
public interface RsRctLogisticsTypeBasicInfoMapper {
    /**
     * 查询操作单基础物流信息
     *
     * @return 操作单基础物流信息
     */
    RsRctLogisticsTypeBasicInfo selectRsRctLogisticsTypeBasicInfoByRctId(Long rctId);

    /**
     * 查询操作单基础物流信息列表
     *
     * @param rsRctLogisticsTypeBasicInfo 操作单基础物流信息
     * @return 操作单基础物流信息集合
     */
    List<RsRctLogisticsTypeBasicInfo> selectRsRctLogisticsTypeBasicInfoList(RsRctLogisticsTypeBasicInfo rsRctLogisticsTypeBasicInfo);

    /**
     * 新增操作单基础物流信息
     *
     * @param rsRctLogisticsTypeBasicInfo 操作单基础物流信息
     * @return 结果
     */
    int insertRsRctLogisticsTypeBasicInfo(RsRctLogisticsTypeBasicInfo rsRctLogisticsTypeBasicInfo);

    /**
     * 修改操作单基础物流信息
     *
     * @param rsRctLogisticsTypeBasicInfo 操作单基础物流信息
     * @return 结果
     */
    int updateRsRctLogisticsTypeBasicInfo(RsRctLogisticsTypeBasicInfo rsRctLogisticsTypeBasicInfo);

    /**
     * 删除操作单基础物流信息
     *
     * @return 结果
     */
    int deleteRsRctLogisticsTypeBasicInfoById(Long rctId);

    /**
     * 批量删除操作单基础物流信息
     *
     * @return 结果
     */
    int deleteRsRctLogisticsTypeBasicInfoByIds(Long[] rctIds);
}
