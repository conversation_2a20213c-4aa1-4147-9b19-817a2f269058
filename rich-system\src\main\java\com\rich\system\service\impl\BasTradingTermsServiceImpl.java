package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.BasTradingTerms;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.system.mapper.BasTradingTermsMapper;
import com.rich.system.service.BasTradingTermsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 贸易条款Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-30
 */
@Service
public class BasTradingTermsServiceImpl implements BasTradingTermsService {
    @Autowired
    private BasTradingTermsMapper basTradingTermsMapper;

    /**
     * 查询贸易条款
     *
     * @param tradingTermsId 贸易条款主键
     * @return 贸易条款
     */
    @Override
    public BasTradingTerms selectBasTradingTermsByTradingTermsId(Long tradingTermsId) {
        return basTradingTermsMapper.selectBasTradingTermsByTradingTermsId(tradingTermsId);
    }

    /**
     * 查询贸易条款列表
     *
     * @param basTradingTerms 贸易条款
     * @return 贸易条款
     */
    @Override
    public List<BasTradingTerms> selectBasTradingTermsList(BasTradingTerms basTradingTerms) {
        return basTradingTermsMapper.selectBasTradingTermsList(basTradingTerms);
    }

    /**
     * 新增贸易条款
     *
     * @param basTradingTerms 贸易条款
     * @return 结果
     */
    @Override
    public int insertBasTradingTerms(BasTradingTerms basTradingTerms) {
        basTradingTerms.setCreateTime(DateUtils.getNowDate());
        basTradingTerms.setCreateBy(SecurityUtils.getUserId());
        return basTradingTermsMapper.insertBasTradingTerms(basTradingTerms);
    }

    /**
     * 修改贸易条款
     *
     * @param basTradingTerms 贸易条款
     * @return 结果
     */
    @Override
    public int updateBasTradingTerms(BasTradingTerms basTradingTerms) {
        basTradingTerms.setUpdateTime(DateUtils.getNowDate());
        basTradingTerms.setUpdateBy(SecurityUtils.getUserId());
        return basTradingTermsMapper.updateBasTradingTerms(basTradingTerms);
    }

    /**
     * 修改贸易条款状态
     *
     * @param basTradingTerms 贸易条款
     * @return 贸易条款
     */
    @Override
    public int changeStatus(BasTradingTerms basTradingTerms) {
        return basTradingTermsMapper.updateBasTradingTerms(basTradingTerms);
    }

    /**
     * 批量删除贸易条款
     *
     * @param tradingTermsIds 需要删除的贸易条款主键
     * @return 结果
     */
    @Override
    public int deleteBasTradingTermsByTradingTermsIds(Long[] tradingTermsIds) {
        return basTradingTermsMapper.deleteBasTradingTermsByTradingTermsIds(tradingTermsIds);
    }

    /**
     * 删除贸易条款信息
     *
     * @param tradingTermsId 贸易条款主键
     * @return 结果
     */
    @Override
    public int deleteBasTradingTermsByTradingTermsId(Long tradingTermsId) {
        return basTradingTermsMapper.deleteBasTradingTermsByTradingTermsId(tradingTermsId);
    }
}
