package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsDispatch;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsDispatchService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 尾程运输Controller
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@RestController
@RequestMapping("/system/dispatch")
public class RsDispatchController extends BaseController {
    @Autowired
    private RsDispatchService rsDispatchService;

    /**
     * 查询尾程运输列表
     */
    @PreAuthorize("@ss.hasPermi('system:dispatch:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsDispatch rsDispatch) {
        startPage();
        List<RsDispatch> list = rsDispatchService.selectRsDispatchList(rsDispatch);
        return getDataTable(list);
    }

    /**
     * 导出尾程运输列表
     */
    @PreAuthorize("@ss.hasPermi('system:dispatch:export')")
    @Log(title = "尾程运输", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsDispatch rsDispatch) {
        List<RsDispatch> list = rsDispatchService.selectRsDispatchList(rsDispatch);
        ExcelUtil<RsDispatch> util = new ExcelUtil<RsDispatch>(RsDispatch.class);
        util.exportExcel(response, list, "尾程运输数据");
    }

    /**
     * 获取尾程运输详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:dispatch:query')")
    @GetMapping(value = "/{dispatchId}")
    public AjaxResult getInfo(@PathVariable("dispatchId") Long dispatchId) {
        return AjaxResult.success(rsDispatchService.selectRsDispatchByDispatchId(dispatchId));
    }

    /**
     * 新增尾程运输
     */
    @PreAuthorize("@ss.hasPermi('system:dispatch:add')")
    @Log(title = "尾程运输", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsDispatch rsDispatch) {
        return toAjax(rsDispatchService.insertRsDispatch(rsDispatch));
    }

    /**
     * 修改尾程运输
     */
    @PreAuthorize("@ss.hasPermi('system:dispatch:edit')")
    @Log(title = "尾程运输", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsDispatch rsDispatch) {
        return toAjax(rsDispatchService.updateRsDispatch(rsDispatch));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:dispatch:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsDispatch rsDispatch) {
        rsDispatch.setUpdateBy(getUserId());
        return toAjax(rsDispatchService.changeStatus(rsDispatch));
    }

    /**
     * 删除尾程运输
     */
    @PreAuthorize("@ss.hasPermi('system:dispatch:remove')")
    @Log(title = "尾程运输", businessType = BusinessType.DELETE)
    @DeleteMapping("/{dispatchIds}")
    public AjaxResult remove(@PathVariable Long[] dispatchIds) {
        return toAjax(rsDispatchService.deleteRsDispatchByDispatchIds(dispatchIds));
    }
}
