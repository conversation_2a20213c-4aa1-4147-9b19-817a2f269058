package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsOpExpandService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsOpExpandServiceService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 扩展服务服务Controller
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@RestController
@RequestMapping("/system/opexpandservice")
public class RsOpExpandServiceController extends BaseController {
    @Autowired
    private RsOpExpandServiceService rsOpExpandServiceService;

    /**
     * 查询扩展服务服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opexpandservice:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsOpExpandService rsOpExpandService) {
        startPage();
        List<RsOpExpandService> list = rsOpExpandServiceService.selectRsOpExpandServiceList(rsOpExpandService);
        return getDataTable(list);
    }

    /**
     * 导出扩展服务服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opexpandservice:export')")
    @Log(title = "扩展服务服务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsOpExpandService rsOpExpandService) {
        List<RsOpExpandService> list = rsOpExpandServiceService.selectRsOpExpandServiceList(rsOpExpandService);
        ExcelUtil<RsOpExpandService> util = new ExcelUtil<RsOpExpandService>(RsOpExpandService.class);
        util.exportExcel(response, list, "扩展服务服务数据");
    }

    /**
     * 获取扩展服务服务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:opexpandservice:query')")
    @GetMapping(value = "/{expandServiceId}")
    public AjaxResult getInfo(@PathVariable("expandServiceId") Long expandServiceId) {
        return AjaxResult.success(rsOpExpandServiceService.selectRsOpExpandServiceByExpandServiceId(expandServiceId));
    }

    /**
     * 新增扩展服务服务
     */
    @PreAuthorize("@ss.hasPermi('system:opexpandservice:add')")
    @Log(title = "扩展服务服务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsOpExpandService rsOpExpandService) {
        return toAjax(rsOpExpandServiceService.insertRsOpExpandService(rsOpExpandService));
    }

    /**
     * 修改扩展服务服务
     */
    @PreAuthorize("@ss.hasPermi('system:opexpandservice:edit')")
    @Log(title = "扩展服务服务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsOpExpandService rsOpExpandService) {
        return toAjax(rsOpExpandServiceService.updateRsOpExpandService(rsOpExpandService));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:opexpandservice:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsOpExpandService rsOpExpandService) {
        rsOpExpandService.setUpdateBy(getUserId());
        return toAjax(rsOpExpandServiceService.changeStatus(rsOpExpandService));
    }

    /**
     * 删除扩展服务服务
     */
    @PreAuthorize("@ss.hasPermi('system:opexpandservice:remove')")
    @Log(title = "扩展服务服务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{expandServiceIds}")
    public AjaxResult remove(@PathVariable Long[] expandServiceIds) {
        return toAjax(rsOpExpandServiceService.deleteRsOpExpandServiceByExpandServiceIds(expandServiceIds));
    }
}
