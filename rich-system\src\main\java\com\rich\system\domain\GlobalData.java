package com.rich.system.domain;

/**
 * <AUTHOR>
 * @Date 2023/1/13 11:02
 * @Version 1.0
 */
public class GlobalData {

    private boolean staff;
    private boolean unit;
    private boolean location;
    private boolean cargoType;
    private boolean currency;
    private boolean serviceType;
    private boolean companySource;
    private boolean companyRole;
    private boolean contractType;
    private boolean local;
    private boolean docType;
    private boolean issue;
    private boolean dept;
    private boolean chargeType;
    private boolean commonInfo;
    private boolean organization;
    private boolean commonInfoType;
    private boolean charge;
    private boolean companyRoleType;
    private boolean line;
    private boolean carrier;
    private boolean role;
    private boolean businessesList;
    private boolean opList;
    private boolean serviceTypeCarriers;
    private boolean salesList;
    private boolean allBelongList;
    private boolean logisticsTimeNodeList;
    private boolean chargeList;
    private boolean messageTypeList;
    private boolean transportationTermsList;
    private boolean exchangeRateList;

    private boolean supplier;
    private boolean client;
    private boolean paymentType;
    private boolean releaseType;
    private boolean paymentChannels;
    private boolean docIssueType;
    private boolean docReleaseWay;
    private boolean tradingTerms;
    private boolean processStatus;
    private boolean process;
    private boolean doc;
    private boolean docFlowDirection;

    public boolean isPaymentType() {
        return paymentType;
    }

    public void setPaymentType(boolean paymentType) {
        this.paymentType = paymentType;
    }

    public boolean isReleaseType() {
        return releaseType;
    }

    public void setReleaseType(boolean releaseType) {
        this.releaseType = releaseType;
    }

    public boolean isPaymentChannels() {
        return paymentChannels;
    }

    public void setPaymentChannels(boolean paymentChannels) {
        this.paymentChannels = paymentChannels;
    }

    public boolean isDocIssueType() {
        return docIssueType;
    }

    public void setDocIssueType(boolean docIssueType) {
        this.docIssueType = docIssueType;
    }

    public boolean isDocReleaseWay() {
        return docReleaseWay;
    }

    public void setDocReleaseWay(boolean docReleaseWay) {
        this.docReleaseWay = docReleaseWay;
    }

    public boolean isTradingTerms() {
        return tradingTerms;
    }

    public void setTradingTerms(boolean tradingTerms) {
        this.tradingTerms = tradingTerms;
    }

    public boolean isProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(boolean processStatus) {
        this.processStatus = processStatus;
    }

    public boolean isProcess() {
        return process;
    }

    public void setProcess(boolean process) {
        this.process = process;
    }

    public boolean isDoc() {
        return doc;
    }

    public void setDoc(boolean doc) {
        this.doc = doc;
    }

    public boolean isDocFlowDirection() {
        return docFlowDirection;
    }

    public void setDocFlowDirection(boolean docFlowDirection) {
        this.docFlowDirection = docFlowDirection;
    }

    public boolean isSupplier() {
        return supplier;
    }

    public void setSupplier(boolean supplier) {
        this.supplier = supplier;
    }

    public boolean isClient() {
        return client;
    }

    public void setClient(boolean client) {
        this.client = client;
    }

    public boolean isExchangeRateList() {
        return exchangeRateList;
    }

    public void setExchangeRateList(boolean exchangeRateList) {
        this.exchangeRateList = exchangeRateList;
    }

    public boolean isOpList() {
        return opList;
    }

    public void setOpList(boolean opList) {
        this.opList = opList;
    }

    public boolean isTransportationTermsList() {
        return transportationTermsList;
    }

    public void setTransportationTermsList(boolean transportationTermsList) {
        this.transportationTermsList = transportationTermsList;
    }

    public boolean isStaff() {
        return staff;
    }

    public void setStaff(boolean staff) {
        this.staff = staff;
    }

    public boolean isMessageTypeList() {
        return messageTypeList;
    }

    public void setMessageTypeList(boolean messageTypeList) {
        this.messageTypeList = messageTypeList;
    }

    public boolean isChargeList() {
        return chargeList;
    }

    public void setChargeList(boolean chargeList) {
        this.chargeList = chargeList;
    }

    public boolean isRole() {
        return role;
    }

    public void setRole(boolean role) {
        this.role = role;
    }

    public boolean isAllBelongList() {
        return allBelongList;
    }

    public void setAllBelongList(boolean allBelongList) {
        this.allBelongList = allBelongList;
    }

    public boolean isBusinessesList() {
        return businessesList;
    }

    public void setBusinessesList(boolean businessesList) {
        this.businessesList = businessesList;
    }

    public boolean isServiceTypeCarriers() {
        return serviceTypeCarriers;
    }

    public void setServiceTypeCarriers(boolean serviceTypeCarriers) {
        this.serviceTypeCarriers = serviceTypeCarriers;
    }

    public boolean isSalesList() {
        return salesList;
    }

    public void setSalesList(boolean salesList) {
        this.salesList = salesList;
    }

    public boolean isLogisticsTimeNodeList() {
        return logisticsTimeNodeList;
    }

    public void setLogisticsTimeNodeList(boolean logisticsTimeNodeList) {
        this.logisticsTimeNodeList = logisticsTimeNodeList;
    }

    public boolean isUnit() {
        return unit;
    }

    public void setUnit(boolean unit) {
        this.unit = unit;
    }

    public boolean isLocation() {
        return location;
    }

    public void setLocation(boolean location) {
        this.location = location;
    }

    public boolean isCargoType() {
        return cargoType;
    }

    public void setCargoType(boolean cargoType) {
        this.cargoType = cargoType;
    }

    public boolean isCurrency() {
        return currency;
    }

    public void setCurrency(boolean currency) {
        this.currency = currency;
    }

    public boolean isServiceType() {
        return serviceType;
    }

    public void setServiceType(boolean serviceType) {
        this.serviceType = serviceType;
    }

    public boolean isCompanySource() {
        return companySource;
    }

    public void setCompanySource(boolean companySource) {
        this.companySource = companySource;
    }

    public boolean isCompanyRole() {
        return companyRole;
    }

    public void setCompanyRole(boolean companyRole) {
        this.companyRole = companyRole;
    }

    public boolean isContractType() {
        return contractType;
    }

    public void setContractType(boolean contractType) {
        this.contractType = contractType;
    }

    public boolean isLocal() {
        return local;
    }

    public void setLocal(boolean local) {
        this.local = local;
    }

    public boolean isDocType() {
        return docType;
    }

    public void setDocType(boolean docType) {
        this.docType = docType;
    }

    public boolean isIssue() {
        return issue;
    }

    public void setIssue(boolean issue) {
        this.issue = issue;
    }

    public boolean isDept() {
        return dept;
    }

    public void setDept(boolean dept) {
        this.dept = dept;
    }

    public boolean isChargeType() {
        return chargeType;
    }

    public void setChargeType(boolean chargeType) {
        this.chargeType = chargeType;
    }

    public boolean isCommonInfo() {
        return commonInfo;
    }

    public void setCommonInfo(boolean commonInfo) {
        this.commonInfo = commonInfo;
    }

    public boolean isOrganization() {
        return organization;
    }

    public void setOrganization(boolean organization) {
        this.organization = organization;
    }

    public boolean isCommonInfoType() {
        return commonInfoType;
    }

    public void setCommonInfoType(boolean commonInfoType) {
        this.commonInfoType = commonInfoType;
    }

    public boolean isCharge() {
        return charge;
    }

    public void setCharge(boolean charge) {
        this.charge = charge;
    }

    public boolean isCompanyRoleType() {
        return companyRoleType;
    }

    public void setCompanyRoleType(boolean companyRoleType) {
        this.companyRoleType = companyRoleType;
    }

    public boolean isLine() {
        return line;
    }

    public void setLine(boolean line) {
        this.line = line;
    }

    public boolean isCarrier() {
        return carrier;
    }

    public void setCarrier(boolean carrier) {
        this.carrier = carrier;
    }
}
