package com.rich.system.domain;

import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 装运区域对象 mid_location_loading
 *
 * <AUTHOR>
 * @date 2023-05-05
 */
public class MidLocationLoading extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long belongId;

    private Long locationId;

    private String belongTo;

    public void setBelongId(Long belongId) {
        this.belongId = belongId;
    }

    public Long getBelongId() {
        return belongId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setBelongTo(String belongTo) {
        this.belongTo = belongTo;
    }

    public String getBelongTo() {
        return belongTo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("belongId", getBelongId())
                .append("locationId", getLocationId())
                .append("belongTo", getBelongTo())
                .toString();
    }
}
