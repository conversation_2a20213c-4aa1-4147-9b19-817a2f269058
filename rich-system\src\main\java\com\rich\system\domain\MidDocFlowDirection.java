package com.rich.system.domain;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 mid_doc_flow_direction
 * 
 * <AUTHOR>
 * @date 2023-06-16
 */
public class MidDocFlowDirection extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long docFlowDirectionId;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long docId;

    public void setDocFlowDirectionId(Long docFlowDirectionId) 
    {
        this.docFlowDirectionId = docFlowDirectionId;
    }

    public Long getDocFlowDirectionId() 
    {
        return docFlowDirectionId;
    }
    public void setDocId(Long docId) 
    {
        this.docId = docId;
    }

    public Long getDocId() 
    {
        return docId;
    }
}
