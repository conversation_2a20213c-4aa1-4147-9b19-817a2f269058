package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 装柜方式对象 bas_loading_way
 *
 * <AUTHOR>
 * @date 2024-03-25
 */
public class BasLoadingWay extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private String loadingWayCode;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String loadingWayLocalName;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String loadingWayEnName;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String orderNum;

    public String getLoadingWayCode() {
        return loadingWayCode;
    }

    public void setLoadingWayCode(String loadingWayCode) {
        this.loadingWayCode = loadingWayCode;
    }

    public String getLoadingWayLocalName() {
        return loadingWayLocalName;
    }

    public void setLoadingWayLocalName(String loadingWayLocalName) {
        this.loadingWayLocalName = loadingWayLocalName;
    }

    public String getLoadingWayEnName() {
        return loadingWayEnName;
    }

    public void setLoadingWayEnName(String loadingWayEnName) {
        this.loadingWayEnName = loadingWayEnName;
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("loadingWayCode", getLoadingWayCode())
                .append("loadingWayLocalName", getLoadingWayLocalName())
                .append("loadingWayEnName", getLoadingWayEnName())
                .append("orderNum", getOrderNum())
                .append("remark", getRemark())
                .toString();
    }
}
