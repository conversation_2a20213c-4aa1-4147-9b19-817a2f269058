package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.RsCargoDetails;
import com.rich.common.core.domain.entity.RsInventory;
import com.rich.common.core.domain.entity.RsOutboundRecord;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.RedisIdGeneratorService;
import com.rich.common.utils.SecurityUtils;
import com.rich.system.mapper.RsInventoryMapper;
import com.rich.system.mapper.RsOutboundRecordMapper;
import com.rich.system.service.RsOutboundRecordService;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 出仓记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-11
 */
@Service
public class RsOutboundRecordServiceImpl implements RsOutboundRecordService {
    @Autowired
    private RsOutboundRecordMapper rsOutboundRecordMapper;

    @Autowired
    private RsInventoryMapper rsInventoryMapper;

    @Resource
    private RedisIdGeneratorService redisIdGeneratorService;

    /**
     * 查询出仓记录
     *
     * @param outboundRecordId 出仓记录主键
     * @return 出仓记录
     */
    @Override
    public RsOutboundRecord selectRsOutboundRecordByOutboundRecordId(Long outboundRecordId) {
        return rsOutboundRecordMapper.selectRsOutboundRecordByOutboundRecordId(outboundRecordId);
    }

    /**
     * 查询出仓记录列表
     *
     * @param rsOutboundRecord 出仓记录
     * @return 出仓记录
     */
    @Override
    public List<RsOutboundRecord> selectRsOutboundRecordList(RsOutboundRecord rsOutboundRecord) {
        return rsOutboundRecordMapper.selectRsOutboundRecordList(rsOutboundRecord);
    }

    /**
     * 新增出仓记录
     *
     * @param rsOutboundRecord 出仓记录
     * @return 结果
     */
    @Override
    public Long insertRsOutboundRecord(RsOutboundRecord rsOutboundRecord) {
        String outboundId = redisIdGeneratorService.generateUniqueId("outbound");
        String date = DateUtils.dateTime();
        rsOutboundRecord.setOutboundNo("OB" + date.substring(2) + outboundId);
        rsOutboundRecord.setOperatorId(SecurityUtils.getUserId());
        int i = rsOutboundRecordMapper.insertRsOutboundRecord(rsOutboundRecord);
        return rsOutboundRecord.getOutboundRecordId();
    }

    /**
     * 修改出仓记录
     *
     * @param rsOutboundRecord 出仓记录
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateRsOutboundRecord(RsOutboundRecord rsOutboundRecord) {
        int i = rsOutboundRecordMapper.updateRsOutboundRecord(rsOutboundRecord);
        List<RsInventory> rsInventoryList = rsOutboundRecord.getRsInventoryList();
        if (rsInventoryList != null && !rsInventoryList.isEmpty()) {
            for (RsInventory rsInventory : rsInventoryList) {
                rsInventoryMapper.updateRsInventory(rsInventory);
            }
        }
        return i;
    }

    /**
     * 修改出仓记录状态
     *
     * @param rsOutboundRecord 出仓记录
     * @return 出仓记录
     */
    @Override
    public int changeStatus(RsOutboundRecord rsOutboundRecord) {
        return rsOutboundRecordMapper.updateRsOutboundRecord(rsOutboundRecord);
    }

    @Override
    public RsOutboundRecord selectRsOutboundRecordsByOutboundRecordId(Long outboundRecordId) {
        return rsOutboundRecordMapper.selectRsOutboundRecordsByOutboundRecordId(outboundRecordId);
    }

    @Override
    public void writeData(RsOutboundRecord rsOutboundRecord, Workbook workbook) {
        // 获取第一个工作表
        Sheet sheet = workbook.getSheetAt(0);

        // TODO
        // TO
        sheet.getRow(7).getCell(1).setCellValue(rsOutboundRecord.getClientName() + "-" + rsOutboundRecord.getClientCode());
        // LoadingNo
        sheet.getRow(9).getCell(2).setCellValue(rsOutboundRecord.getOutboundNo());
        // LoadingDate
        sheet.getRow(9).getCell(8).setCellValue(rsOutboundRecord.getOutboundDate() == null ? null : DateUtils.dateTime(rsOutboundRecord.getOutboundDate()));
        // TotalCTNS
        sheet.getRow(10).getCell(2).setCellValue(Optional.ofNullable(rsOutboundRecord.getTotalBoxes()).orElse(0L) + " CTNS");
        // ShippingMark
        sheet.getRow(10).getCell(8).setCellValue("");
        // GrossWeight
        sheet.getRow(11).getCell(2).setCellValue(Optional.ofNullable(rsOutboundRecord.getTotalGrossWeight()).orElse(new BigDecimal(0)).doubleValue() + " KGS");
        // containerType
        sheet.getRow(11).getCell(8).setCellValue(rsOutboundRecord.getContainerType());
        // NetWeight
        sheet.getRow(12).getCell(2).setCellValue("");
        // ContainerNo
        sheet.getRow(12).getCell(8).setCellValue(rsOutboundRecord.getContainerNo());
        // Volume
        sheet.getRow(13).getCell(2).setCellValue(Optional.ofNullable(rsOutboundRecord.getTotalVolume()).orElse(new BigDecimal(0)).doubleValue() + " CBM");
        // SealNo
        sheet.getRow(13).getCell(8).setCellValue(rsOutboundRecord.getSealNo());

        // 填充列表
        int inventoryIndex = 18;
        int cargoDetailIndex = 18;
        int noIndex = 1;
        BigDecimal totalUnpaidUnloadingFee = new BigDecimal(0);
        BigDecimal totalUnpaidPackingFee = new BigDecimal(0);
        BigDecimal totalLogisticsAdvanceFee = new BigDecimal(0);
        BigDecimal totalOverdueRentalFee = new BigDecimal(0);
        BigDecimal totalUnpaidInboundFee = new BigDecimal(0);
        for (RsInventory rsInventory : rsOutboundRecord.getRsInventoryList()) {
            Row inventoryRow = sheet.getRow(inventoryIndex);
            if (inventoryRow == null) {
                inventoryRow = sheet.createRow(inventoryIndex);
            }

            // 填充数据到单元格
            // 流水号
            inventoryRow.createCell(12).setCellValue(rsInventory.getInboundSerialNo());
            // 分单号
            inventoryRow.createCell(13).setCellValue(rsInventory.getSubOrderNo());
            // 被打包至
            inventoryRow.createCell(14).setCellValue(rsInventory.getPackageIntoNo());
            // 入仓时间
            inventoryRow.createCell(15).setCellValue(rsInventory.getActualInboundTime() == null ? null : DateUtils.dateTime(rsInventory.getActualInboundTime()));
            // 供货商
            inventoryRow.createCell(16).setCellValue(rsInventory.getSupplier());
            // 联系方式
            inventoryRow.createCell(17).setCellValue("");
            // 未收入仓费(补收入仓费)
            inventoryRow.createCell(18).setCellValue(Optional.ofNullable(rsOutboundRecord.getAdditionalStorageFee()).orElse(new BigDecimal(0)).doubleValue());
            // 未收卸货费
            inventoryRow.createCell(19).setCellValue(Optional.ofNullable(rsInventory.getUnpaidUnloadingFee()).orElse(new BigDecimal(0)).doubleValue());
            // 未收打包费
            inventoryRow.createCell(20).setCellValue(Optional.ofNullable(rsInventory.getUnpaidPackingFee()).orElse(new BigDecimal(0)).doubleValue());
            // 物流代垫费
            inventoryRow.createCell(21).setCellValue(Optional.ofNullable(rsInventory.getLogisticsAdvanceFee()).orElse(new BigDecimal(0)).doubleValue());
            // 计租天数
            inventoryRow.createCell(22).setCellValue(rsInventory.getRentalDays());
            // 租金单价
            inventoryRow.createCell(23).setCellValue(Optional.ofNullable(rsInventory.getOverdueRentalUnitPrice()).orElse(new BigDecimal(0)).doubleValue());
            // 超期仓租
            inventoryRow.createCell(24).setCellValue(Optional.ofNullable(rsInventory.getOverdueRentalFee()).orElse(new BigDecimal(0)).doubleValue());
            // 其他费用
            inventoryRow.createCell(25).setCellValue("");
            inventoryRow.createCell(26).setCellValue(rsInventory.getDriverInfo());

            totalUnpaidUnloadingFee = Optional.ofNullable(rsInventory.getUnpaidUnloadingFee()).orElse(new BigDecimal(0)).add(totalUnpaidUnloadingFee);
            totalUnpaidPackingFee = Optional.ofNullable(rsInventory.getUnpaidPackingFee()).orElse(new BigDecimal(0).add(totalUnpaidPackingFee));
            totalLogisticsAdvanceFee = Optional.ofNullable(rsInventory.getLogisticsAdvanceFee()).orElse(new BigDecimal(0).add(totalLogisticsAdvanceFee));
            totalOverdueRentalFee = Optional.ofNullable(rsInventory.getOverdueRentalFee()).orElse(new BigDecimal(0).add(totalOverdueRentalFee));
            totalUnpaidInboundFee = Optional.ofNullable(rsOutboundRecord.getUnpaidInboundFee()).orElse(new BigDecimal(0).add(totalUnpaidInboundFee));

            // 获取当前主记录的子记录数量
            int cargoDetailsCount = rsInventory.getRsCargoDetailsList().size();
            cargoDetailIndex = inventoryIndex;
            
            for (RsCargoDetails rsCargoDetails : rsInventory.getRsCargoDetailsList()) {
                Row cargoDetailRow = sheet.getRow(cargoDetailIndex);
                if (cargoDetailRow == null) {
                    cargoDetailRow = sheet.createRow(cargoDetailIndex);
                }

                // 编号
                cargoDetailRow.createCell(0).setCellValue(noIndex);
                // 唛头
                cargoDetailRow.createCell(1).setCellValue(rsCargoDetails.getShippingMark());
                // 货名
                cargoDetailRow.createCell(2).setCellValue(rsCargoDetails.getItemName());
                // 箱数
                cargoDetailRow.createCell(3).setCellValue(rsCargoDetails.getBoxCount());
                // 总件数
                cargoDetailRow.createCell(4).setCellValue(Optional.ofNullable(rsCargoDetails.getBoxCount()).orElse(0L));
                // 总净重
                cargoDetailRow.createCell(5).setCellValue(Optional.ofNullable(rsCargoDetails.getUnitGrossWeight()).orElse(new BigDecimal(0)).doubleValue());
                // 总毛重
                cargoDetailRow.createCell(6).setCellValue(Optional.ofNullable(rsCargoDetails.getUnitGrossWeight()).orElse(new BigDecimal(0)).doubleValue());
                // 总体积
                cargoDetailRow.createCell(7).setCellValue(Optional.ofNullable(rsCargoDetails.getUnitVolume()).orElse(new BigDecimal(0)).doubleValue());
                // 备注
                cargoDetailRow.createCell(8).setCellValue(rsCargoDetails.getDamageStatus());
                noIndex++;
                cargoDetailIndex++;
            }

            // 更新inventoryIndex，为下一个主记录预留足够的行
            // 如果子记录数量大于1，则需要跳过子记录数量的行
            inventoryIndex = cargoDetailIndex;
        }
        // 仓库装柜费
        Row row9 = sheet.getRow(9);
        if (row9 == null) {
            row9 = sheet.createRow(9);
        }
        // 装柜报价
        row9.createCell(13).setCellValue(Optional.ofNullable(rsOutboundRecord.getWarehouseQuote()).orElse(new BigDecimal(0)).doubleValue());
        // 补收入仓费
        row9.createCell(14).setCellValue(Optional.ofNullable(rsOutboundRecord.getAdditionalStorageFee()).orElse(BigDecimal.ZERO).doubleValue());
        // 未收卸货
        row9.createCell(15).setCellValue(Optional.ofNullable(rsOutboundRecord.getUnpaidUnloadingFee()).orElse(new BigDecimal(0)).doubleValue());
        // 未收打包
        row9.createCell(16).setCellValue(Optional.ofNullable(rsOutboundRecord.getUnpaidPackingFee()).orElse(new BigDecimal(0)).doubleValue());
        // 物流代垫费
        row9.createCell(17).setCellValue(Optional.ofNullable(rsOutboundRecord.getLogisticsAdvanceFee()).orElse(new BigDecimal(0)).doubleValue());
        // 超期租金
        row9.createCell(18).setCellValue(Optional.ofNullable(rsOutboundRecord.getOverdueRentalFee()).orElse(new BigDecimal(0)).doubleValue());
        // 未收入仓费
//        row9.createCell(18).setCellValue(Optional.ofNullable(rsOutboundRecord.getUnpaidInboundFee()).orElse(new BigDecimal(0)).doubleValue());
//         其他费用
        row9.createCell(19).setCellValue(Optional.ofNullable(rsOutboundRecord.getWarehouseAdvanceOtherFee()).orElse(new BigDecimal(0)).doubleValue());
        // 费用总计
//        row9.createCell(20).setCellValue("");
        // 已付费用
//        row9.createCell(20).setCellValue(Optional.ofNullable(rsOutboundRecord.getWarehouseCollection()).orElse(new BigDecimal(0)).doubleValue());
        // 余额
//        row9.createCell(22).setCellValue("");
    }

    @Override
    public List<RsOutboundRecord> selectRentalRecordList(RsOutboundRecord rsOutboundRecord) {
        return rsOutboundRecordMapper.selectRentalRecordList(rsOutboundRecord);
    }

    @Override
    public RsOutboundRecord selectRentalsByOutboundRecordId(Long outboundRecordId) {
        return rsOutboundRecordMapper.selectRentalsByOutboundRecordId(outboundRecordId);
    }

    @Override
    public RsOutboundRecord selectRsOutboundRecordByRctId(Long rctId) {
        return rsOutboundRecordMapper.selectRsOutboundRecordByRctId(rctId);
    }

    /**
     * 批量删除出仓记录
     *
     * @param outboundRecordIds 需要删除的出仓记录主键
     * @return 结果
     */
    @Override
    public int deleteRsOutboundRecordByOutboundRecordIds(Long[] outboundRecordIds) {
        return rsOutboundRecordMapper.deleteRsOutboundRecordByOutboundRecordIds(outboundRecordIds);
    }

    /**
     * 删除出仓记录信息
     *
     * @param outboundRecordId 出仓记录主键
     * @return 结果
     */
    @Override
    public int deleteRsOutboundRecordByOutboundRecordId(Long outboundRecordId) {
        return rsOutboundRecordMapper.deleteRsOutboundRecordByOutboundRecordId(outboundRecordId);
    }
}
