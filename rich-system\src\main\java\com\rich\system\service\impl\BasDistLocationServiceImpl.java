package com.rich.system.service.impl;

import com.rich.common.constant.CacheConstants;
import com.rich.common.constant.UserConstants;
import com.rich.common.core.domain.entity.BasDistLine;
import com.rich.common.core.domain.entity.BasDistLocation;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.core.text.Convert;
import com.rich.common.exception.ServiceException;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.StringUtils;
import com.rich.system.mapper.BasDistLocationMapper;
import com.rich.system.service.BasDistLocationService;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


/**
 * 区域Service业务层处理
 *
 * <AUTHOR>
 * &#064;date  2022-08-15
 */
@Service
public class BasDistLocationServiceImpl implements BasDistLocationService {
    @Autowired
    private BasDistLocationMapper basDistLocationMapper;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private RedisCacheImpl RedisCache;

    /**
     * 查询区域
     *
     * @param locationId 区域主键
     * @return 区域
     */
    @Override
    public BasDistLocation selectBasDistLocationByLocationId(Long locationId) {
        return basDistLocationMapper.selectBasDistLocationByLocationId(locationId);
    }

    /**
     * 查询区域列表
     *
     * @param basDistLocation 区域
     * @return 区域
     */
    @Override
    public List<BasDistLocation> selectBasDistLocationList(BasDistLocation basDistLocation) {
        List<BasDistLocation> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        if (list == null) {
            RedisCache.location();
            list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        }
        // 输入框输入的内容
        String query = basDistLocation.getLocationQuery();
        if (query != null) {
            List<BasDistLocation> output = new ArrayList<>();
            for (BasDistLocation l : list) {
                // 区域名
                if ((l.getLocationShortName() != null && l.getLocationShortName().contains(query))
                        || (l.getLocationLocalName() != null && l.getLocationLocalName().contains(query))
                        || (l.getLocationEnName() != null && l.getLocationEnName().toLowerCase().contains(query.toLowerCase()))) {
                    Set<Long> kids = new HashSet<>();
                    for (BasDistLocation ll : list) {
                        if (ll.getAncestors() != null && ArrayUtils.contains(ll.getAncestors().split(","), l.getLocationId().toString())) {
                            kids.add(ll.getLocationId());
                        }
                    }
                    Set<Long> delList = new HashSet<>();
                    for (Long k : kids) {
                        if (ArrayUtils.contains(basDistLocation.getLocationSelectList(), k)) {
                            delList.add(k);
                        }
                    }
                    l.setLocationSelectList(delList.toArray(new Long[0]));
                    output.add(l);
                }
                // 海港代码
                if ((l.getPortCode() != null && l.getPortCode().equals(query)) || (l.getPortCode() != null && l.getPortCode().contains(query)) || (l.getPortCode() != null && l.getPortCode().toLowerCase().contains(query.toLowerCase()))) {
                    output.add(l);
                }
                // 空运代码
                if ((l.getPortIataCode() != null && l.getPortIataCode().equals(query)) || (l.getPortIataCode() != null && l.getPortIataCode().contains(query)) || (l.getPortIataCode() != null && l.getPortIataCode().toLowerCase().contains(query.toLowerCase()))) {
                    output.add(l);
                }
                // 铁路代码
                if ((l.getPortRailCode() != null && l.getPortRailCode().equals(query)) || (l.getPortRailCode() != null && l.getPortRailCode().contains(query)) || (l.getPortRailCode() != null && l.getPortRailCode().toLowerCase().contains(query.toLowerCase()))) {
                    output.add(l);
                }
            }
            return output;
        }
        return list;
    }

    @Override
    public List<BasDistLocation> selectBasDistLocationByIds(Set<Long> ids) {
        List<BasDistLocation> locations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        if (locations == null) {
            RedisCache.location();
            locations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        }
        List<BasDistLocation> out = new ArrayList<>();
        for (BasDistLocation location : locations) {
            if (ids.contains(location.getLocationId())) {
                out.add(location);
            }
        }
        return out;
    }

    @Override
    public List<BasDistLocation> selectLoadLocationList(BasDistLocation basDistLocation) {
        return basDistLocationMapper.selectLoadLocationList(basDistLocation);
    }

    /**
     * 新增区域
     *
     * @param basDistLocation 区域
     * @return 结果
     */
    @Override
    public int insertBasDistLocation(BasDistLocation basDistLocation) {
        BasDistLocation info = basDistLocationMapper.selectBasDistLocationByLocationId(basDistLocation.getParentId());
        // 如果父节点不为正常状态,则不允许新增子节点
        if (!UserConstants.DEPT_NORMAL.equals(info.getStatus())) {
            throw new ServiceException("区域停用，不允许新增");
        }
        List<BasDistLine> lines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        if (lines == null) {
            RedisCache.line();
            lines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        }
        if (basDistLocation.getLineId() != null) {
            for (BasDistLine line : lines) {
                if (basDistLocation.getLineId().equals(line.getLineId())) {
                    basDistLocation.setLineAncestors(line.getAncestors());
                }
            }
        } else {
            if (info.getLineId() != null && info.getLineAncestors() != null) {
                basDistLocation.setLineAncestors(info.getLineAncestors());
            }
        }
        basDistLocation.setAncestors(info.getAncestors() + "," + info.getLocationId());
        basDistLocation.setCreateTime(DateUtils.getNowDate());
        basDistLocation.setCreateBy(SecurityUtils.getUserId());
        int out = basDistLocationMapper.insertBasDistLocation(basDistLocation);
        basDistLocationMapper.updateLocationPriority(basDistLocation.getLocationId());
        return out;
    }

    /**
     * 修改区域
     *
     * @param basDistLocation 区域
     * @return 结果
     */
    @Override
    public int updateBasDistLocation(BasDistLocation basDistLocation) {
        basDistLocation.setUpdateTime(DateUtils.getNowDate());
        basDistLocation.setUpdateBy(SecurityUtils.getUserId());
        BasDistLocation newParentLocation = basDistLocationMapper.selectBasDistLocationByLocationId(basDistLocation.getParentId());
        BasDistLocation oldLocation = basDistLocationMapper.selectBasDistLocationByLocationId(basDistLocation.getLocationId());
        if (StringUtils.isNotNull(newParentLocation) && StringUtils.isNotNull(oldLocation)) {
            String newAncestors = newParentLocation.getAncestors() + "," + newParentLocation.getLocationId();
            String oldAncestors = oldLocation.getAncestors();
            basDistLocation.setAncestors(newAncestors);
            updateLocationChildren(basDistLocation.getLocationId(), newAncestors, oldAncestors);
        }
        List<BasDistLine> lines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        if (lines == null) {
            RedisCache.line();
            lines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        }
        if (basDistLocation.getLineId() != null) {
            for (BasDistLine line : lines) {
                if (basDistLocation.getLineId().equals(line.getLineId())) {
                    basDistLocation.setLineAncestors(line.getAncestors());
                }
            }
        } else {
            if (newParentLocation.getLineId() != null && newParentLocation.getLineAncestors() != null) {
                basDistLocation.setLineAncestors(newParentLocation.getLineAncestors());
            }
        }
        int result = basDistLocationMapper.updateBasDistLocation(basDistLocation);
        basDistLocationMapper.updateLocationPriority(basDistLocation.getLocationId());
        if (UserConstants.DEPT_NORMAL.equals(basDistLocation.getStatus()) && StringUtils.isNotEmpty(basDistLocation.getAncestors())
                && !StringUtils.equals("0", basDistLocation.getAncestors())) {
            // 如果该部门是启用状态，则启用该部门的所有上级部门
            updateParentLocationStatusNormal(basDistLocation);
        }
        return result;
    }

    /**
     * 修改该部门的父级部门状态
     */
    private void updateParentLocationStatusNormal(BasDistLocation basDistLocation) {
        String ancestors = basDistLocation.getAncestors();
        Long[] locationIds = Convert.toLongArray(ancestors);
        basDistLocationMapper.updateLocationStatusNormal(locationIds);
    }

    /**
     * 修改子元素关系
     *
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateLocationChildren(Long locationId, String newAncestors, String oldAncestors) {
        List<BasDistLocation> children = basDistLocationMapper.selectChildrenLocationById(locationId);
        for (BasDistLocation child : children) {
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
        }
        if (children.size() > 0) {
            basDistLocationMapper.updateLocationChildren(children);
        }
    }

    /**
     * 批量删除区域
     *
     * @param locationIds 需要删除的区域主键
     * @return 结果
     */
    @Override
    public int deleteBasDistLocationByLocationIds(Long[] locationIds) {
        return basDistLocationMapper.deleteBasDistLocationByLocationIds(locationIds);
    }

    /**
     * 删除区域信息
     *
     * @param locationId 区域主键
     * @return 结果
     */
    @Override
    public int deleteBasDistLocationByLocationId(Long locationId) {
        return basDistLocationMapper.deleteBasDistLocationByLocationId(locationId);
    }

    @Override
    public int changeStatus(BasDistLocation basDistLocation) {
        return basDistLocationMapper.updateBasDistLocation(basDistLocation);
    }

}
