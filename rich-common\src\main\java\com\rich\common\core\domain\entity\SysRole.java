package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @TableName sec_role
 */
public class SysRole extends BaseEntity {
    /**
     * 角色ID
     */
    @Excel(name = "角色序号")
    private Long roleId;

    private Long parentId;

    private String ancestors;

    /**
     * 角色名称
     */
    @Excel(name = "角色中文名")
    private String roleLocalName;

    /**
     * 角色名称
     */
    @Excel(name = "角色英文名")
    private String roleEnName;

    /**
     * 角色权限字符串
     */
    @Excel(name = "角色权限")
    private String roleKey;

    /**
     * 显示顺序
     */
    @Excel(name = "角色排序")
    private Integer roleSort;

    /**
     * 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）
     */
    @Excel(name = "数据范围", readConverterExp = "1=所有数据权限,2=自定义数据权限,3=本部门数据权限,4=本部门及以下数据权限,5=仅本人数据权限")
    private String dataScope;

    /**
     * 菜单树选择项是否关联显示
     */
    private Boolean menuCheckStrictly;

    /**
     * 部门树选择项是否关联显示
     */
    private Boolean deptCheckStrictly;

    /**
     * 角色状态（0正常 1停用）
     */
    @Excel(name = "角色状态", readConverterExp = "0=正常,1=停用")
    private String status;

    private BasDistDept dept;

    /**
     * 菜单组
     */
    private Long[] menuIds;

    /**
     * 部门组（数据权限）
     */
    private Long[] deptIds;

    private Long deptId;

    private Long positionId;

    private BasPosition position;

    private List<MidRsStaffRole> midRsStaffRoles;

    private MidRsStaffRole midRsStaffRole;

    private List<MidRsStaffRole> children;

    private Long[] cargoTypeIds;

    private String cargoTypes;

    private Long[] serviceTypeIds;

    private String serviceTypes;

    private Long[] locationDepartureIds;

    private String locationDepartures;

    private Long[] lineDepartureIds;

    private String lineDepartures;

    private Long[] locationDestinationIds;

    private String locationDestinations;

    private Long[] lineDestinationIds;

    private String lineDestinations;

    private Long serviceTypeId;

    private Long[] serviceTypeRoles;

    private String roleQuery;

    private Long userId;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getRoleQuery() {
        return roleQuery;
    }

    public void setRoleQuery(String roleQuery) {
        this.roleQuery = roleQuery;
    }

    public BasDistDept getDept() {
        return dept;
    }

    public void setDept(BasDistDept dept) {
        this.dept = dept;
    }

    public Long[] getServiceTypeRoles() {
        return serviceTypeRoles;
    }

    public void setServiceTypeRoles(Long[] serviceTypeRoles) {
        this.serviceTypeRoles = serviceTypeRoles;
    }

    public Long getServiceTypeId() {
        return serviceTypeId;
    }

    public void setServiceTypeId(Long serviceTypeId) {
        this.serviceTypeId = serviceTypeId;
    }

    public Long[] getCargoTypeIds() {
        return cargoTypeIds;
    }

    public void setCargoTypeIds(Long[] cargoTypeIds) {
        this.cargoTypeIds = cargoTypeIds;
    }

    public String getCargoTypes() {
        return cargoTypes;
    }

    public void setCargoTypes(String cargoTypes) {
        this.cargoTypes = cargoTypes;
    }

    public Long[] getServiceTypeIds() {
        return serviceTypeIds;
    }

    public void setServiceTypeIds(Long[] serviceTypeIds) {
        this.serviceTypeIds = serviceTypeIds;
    }

    public String getServiceTypes() {
        return serviceTypes;
    }

    public void setServiceTypes(String serviceTypes) {
        this.serviceTypes = serviceTypes;
    }

    public Long[] getLocationDepartureIds() {
        return locationDepartureIds;
    }

    public void setLocationDepartureIds(Long[] locationDepartureIds) {
        this.locationDepartureIds = locationDepartureIds;
    }

    public String getLocationDepartures() {
        return locationDepartures;
    }

    public void setLocationDepartures(String locationDepartures) {
        this.locationDepartures = locationDepartures;
    }

    public Long[] getLineDepartureIds() {
        return lineDepartureIds;
    }

    public void setLineDepartureIds(Long[] lineDepartureIds) {
        this.lineDepartureIds = lineDepartureIds;
    }

    public String getLineDepartures() {
        return lineDepartures;
    }

    public void setLineDepartures(String lineDepartures) {
        this.lineDepartures = lineDepartures;
    }

    public Long[] getLocationDestinationIds() {
        return locationDestinationIds;
    }

    public void setLocationDestinationIds(Long[] locationDestinationIds) {
        this.locationDestinationIds = locationDestinationIds;
    }

    public String getLocationDestinations() {
        return locationDestinations;
    }

    public void setLocationDestinations(String locationDestinations) {
        this.locationDestinations = locationDestinations;
    }

    public Long[] getLineDestinationIds() {
        return lineDestinationIds;
    }

    public void setLineDestinationIds(Long[] lineDestinationIds) {
        this.lineDestinationIds = lineDestinationIds;
    }

    public String getLineDestinations() {
        return lineDestinations;
    }

    public void setLineDestinations(String lineDestinations) {
        this.lineDestinations = lineDestinations;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getAncestors() {
        return ancestors;
    }

    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }

    public List<MidRsStaffRole> getChildren() {
        return children;
    }

    public void setChildren(List<MidRsStaffRole> children) {
        this.children = children;
    }

    public List<MidRsStaffRole> getMidRsStaffRoles() {
        return midRsStaffRoles;
    }

    public void setMidRsStaffRoles(List<MidRsStaffRole> midRsStaffRoles) {
        this.midRsStaffRoles = midRsStaffRoles;
    }

    public MidRsStaffRole getMidRsStaffRole() {
        return midRsStaffRole;
    }

    public void setMidRsStaffRole(MidRsStaffRole midRsStaffRole) {
        this.midRsStaffRole = midRsStaffRole;
    }

    public BasPosition getPosition() {
        return position;
    }

    public void setPosition(BasPosition position) {
        this.position = position;
    }


    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public Long getPositionId() {
        return positionId;
    }

    public void setPositionId(Long positionId) {
        this.positionId = positionId;
    }


    public Long[] getMenuIds() {
        return menuIds;
    }

    public void setMenuIds(Long[] menuIds) {
        this.menuIds = menuIds;
    }

    public Long[] getDeptIds() {
        return deptIds;
    }

    public void setDeptIds(Long[] deptIds) {
        this.deptIds = deptIds;
    }

    public boolean isAdmin() {
        return isAdmin(this.roleId);
    }

    public static boolean isAdmin(Long roleId) {
        return roleId != null && 1L == roleId;
    }

    public SysRole() {

    }

    public SysRole(Long roleId) {
        this.roleId = roleId;
    }


    private static final long serialVersionUID = 1L;

    /**
     * 角色ID
     */
    public Long getRoleId() {
        return roleId;
    }

    /**
     * 角色ID
     */
    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    @Size(min = 0, max = 30, message = "角色名称长度不能超过30个字符")
    public String getRoleLocalName() {
        return roleLocalName;
    }

    public String getRoleEnName() {
        return roleEnName;
    }

    public void setRoleEnName(String roleEnName) {
        this.roleEnName = roleEnName;
    }


    /**
     * 角色名称
     */
    public void setRoleLocalName(String roleLocalName) {
        this.roleLocalName = roleLocalName;
    }

    /**
     * 角色权限字符串
     */
    @NotBlank(message = "权限字符不能为空")
    @Size(min = 0, max = 100, message = "权限字符长度不能超过100个字符")
    public String getRoleKey() {
        return roleKey;
    }

    /**
     * 角色权限字符串
     */
    public void setRoleKey(String roleKey) {
        this.roleKey = roleKey;
    }

    /**
     * 显示顺序
     */
    @NotNull(message = "显示顺序不能为空")
    public Integer getRoleSort() {
        return roleSort;
    }

    /**
     * 显示顺序
     */
    public void setRoleSort(Integer roleSort) {
        this.roleSort = roleSort;
    }

    /**
     * 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）
     */
    public String getDataScope() {
        return dataScope;
    }

    /**
     * 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）
     */
    public void setDataScope(String dataScope) {
        this.dataScope = dataScope;
    }

    public Boolean getMenuCheckStrictly() {
        return menuCheckStrictly;
    }

    public void setMenuCheckStrictly(Boolean menuCheckStrictly) {
        this.menuCheckStrictly = menuCheckStrictly;
    }

    public Boolean getDeptCheckStrictly() {
        return deptCheckStrictly;
    }

    public void setDeptCheckStrictly(Boolean deptCheckStrictly) {
        this.deptCheckStrictly = deptCheckStrictly;
    }

    /**
     * 角色状态（0正常 1停用）
     */
    public String getStatus() {
        return status;
    }

    /**
     * 角色状态（0正常 1停用）
     */
    public void setStatus(String status) {
        this.status = status;
    }

}