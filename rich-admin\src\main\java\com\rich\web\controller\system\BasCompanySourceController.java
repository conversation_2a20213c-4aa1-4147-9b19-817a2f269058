package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasCompanySource;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasCompanySourceService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 客户来源Controller
 *
 * <AUTHOR>
 * @date 2022-09-30
 */
@RestController
@RequestMapping("/system/companysource")

public class BasCompanySourceController extends BaseController {
   @Autowired
    private  BasCompanySourceService basCompanySourceService;

   @Autowired
    private  RedisCache redisCache;

    /**
     * 查询客户来源列表
     */
    @PreAuthorize("@ss.hasPermi('system:companysource:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasCompanySource basCompanySource) {
        startPage();
        List<BasCompanySource> list = basCompanySourceService.selectBasCompanySourceList(basCompanySource);
        return getDataTable(list);
    }

    @GetMapping("/selectList")
    public AjaxResult selectList(BasCompanySource basCompanySource) {
        List<BasCompanySource> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "companySource");
        if (list == null) {
            basCompanySource.setStatus("0");
            list = basCompanySourceService.selectBasCompanySourceList(basCompanySource);
            redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "companySource");
            redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "companySource", list);
        }
        return AjaxResult.success(AjaxResult.DATA_TAG, list);
    }

    /**
     * 导出客户来源列表
     */
    @PreAuthorize("@ss.hasPermi('system:companysource:export')")
    @Log(title = "客户来源", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasCompanySource basCompanySource) {
        List<BasCompanySource> list = basCompanySourceService.selectBasCompanySourceList(basCompanySource);
        ExcelUtil<BasCompanySource> util = new ExcelUtil<BasCompanySource>(BasCompanySource.class);
        util.exportExcel(response, list, "客户来源数据");
    }

    /**
     * 获取客户来源详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:companysource:edit')")
    @GetMapping(value = "/{sourceId}")
    public AjaxResult getInfo(@PathVariable("sourceId") Long sourceId) {
        return AjaxResult.success(basCompanySourceService.selectBasCompanySourceBySourceId(sourceId));
    }

    /**
     * 新增客户来源
     */
    @PreAuthorize("@ss.hasPermi('system:companysource:add')")
    @Log(title = "客户来源", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasCompanySource basCompanySource) {
        int out = basCompanySourceService.insertBasCompanySource(basCompanySource);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "companySource");
        return toAjax(out);
    }

    /**
     * 修改客户来源
     */
    @PreAuthorize("@ss.hasPermi('system:companysource:edit')")
    @Log(title = "客户来源", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasCompanySource basCompanySource) {
        int out = basCompanySourceService.updateBasCompanySource(basCompanySource);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "companySource");
        return toAjax(out);
    }

    /**
     * 修改客户来源
     */
    @PreAuthorize("@ss.hasPermi('system:companysource:edit')")
    @Log(title = "客户来源", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasCompanySource basCompanySource) {
        int out = basCompanySourceService.changeStatus(basCompanySource);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "companySource");
        return toAjax(out);
    }

    /**
     * 删除客户来源
     */
    @PreAuthorize("@ss.hasPermi('system:companysource:remove')")
    @Log(title = "客户来源", businessType = BusinessType.DELETE)
    @DeleteMapping("/{sourceIds}")
    public AjaxResult remove(@PathVariable Long[] sourceIds) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "companySource");
        return toAjax(basCompanySourceService.deleteBasCompanySourceBySourceIds(sourceIds));
    }
}
