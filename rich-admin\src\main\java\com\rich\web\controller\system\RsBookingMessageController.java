package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsBookingMessage;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsBookingMessageService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 记录提单信息Controller
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@RestController
@RequestMapping("/system/bookingmessage")
public class RsBookingMessageController extends BaseController {
    @Autowired
    private RsBookingMessageService rsBookingMessageService;

    /**
     * 查询记录提单信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:bookingmessage:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsBookingMessage rsBookingMessage) {
        startPage();
        List<RsBookingMessage> list = rsBookingMessageService.selectRsBookingMessageList(rsBookingMessage);
        return getDataTable(list);
    }

    /**
     * 导出记录提单信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:bookingmessage:export')")
    @Log(title = "记录提单信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsBookingMessage rsBookingMessage) {
        List<RsBookingMessage> list = rsBookingMessageService.selectRsBookingMessageList(rsBookingMessage);
        ExcelUtil<RsBookingMessage> util = new ExcelUtil<RsBookingMessage>(RsBookingMessage.class);
        util.exportExcel(response, list, "记录提单信息数据");
    }

    /**
     * 获取记录提单信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:bookingmessage:query')")
    @GetMapping(value = "/{rsBookingMessageId}")
    public AjaxResult getInfo(@PathVariable("rsBookingMessageId") Long rsBookingMessageId) {
        return AjaxResult.success(rsBookingMessageService.selectRsBookingMessageByRsBookingMessageId(rsBookingMessageId));
    }

    /**
     * 新增记录提单信息
     */
    @PreAuthorize("@ss.hasPermi('system:bookingmessage:add')")
    @Log(title = "记录提单信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsBookingMessage rsBookingMessage) {
        return toAjax(rsBookingMessageService.insertRsBookingMessage(rsBookingMessage));
    }

    /**
     * 修改记录提单信息
     */
    @PreAuthorize("@ss.hasPermi('system:bookingmessage:edit')")
    @Log(title = "记录提单信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsBookingMessage rsBookingMessage) {
        return toAjax(rsBookingMessageService.updateRsBookingMessage(rsBookingMessage));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:bookingmessage:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsBookingMessage rsBookingMessage) {
        rsBookingMessage.setUpdateBy(getUserId());
        return toAjax(rsBookingMessageService.changeStatus(rsBookingMessage));
    }

    /**
     * 删除记录提单信息
     */
    @PreAuthorize("@ss.hasPermi('system:bookingmessage:remove')")
    @Log(title = "记录提单信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{rsBookingMessageIds}")
    public AjaxResult remove(@PathVariable Long[] rsBookingMessageIds) {
        return toAjax(rsBookingMessageService.deleteRsBookingMessageByRsBookingMessageIds(rsBookingMessageIds));
    }
}
