package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasMailRules;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 邮件规则Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@Mapper
public interface BasMailRulesMapper {
    /**
     * 查询邮件规则
     *
     * @param mailRulesId 邮件规则主键
     * @return 邮件规则
     */
    BasMailRules selectBasMailRulesByMailRulesId(Long mailRulesId);

    /**
     * 查询邮件规则列表
     *
     * @param basMailRules 邮件规则
     * @return 邮件规则集合
     */
    List<BasMailRules> selectBasMailRulesList(BasMailRules basMailRules);

    /**
     * 新增邮件规则
     *
     * @param basMailRules 邮件规则
     * @return 结果
     */
    int insertBasMailRules(BasMailRules basMailRules);

    /**
     * 修改邮件规则
     *
     * @param basMailRules 邮件规则
     * @return 结果
     */
    int updateBasMailRules(BasMailRules basMailRules);

    /**
     * 删除邮件规则
     *
     * @param mailRulesId 邮件规则主键
     * @return 结果
     */
    int deleteBasMailRulesByMailRulesId(Long mailRulesId);

    /**
     * 批量删除邮件规则
     *
     * @param mailRulesIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasMailRulesByMailRulesIds(Long[] mailRulesIds);
}
