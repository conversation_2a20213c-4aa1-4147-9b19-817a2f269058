package com.rich.system.mapper;

import com.rich.system.domain.MidLineDeparture;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 启运港航线Mapper接口
 *
 * <AUTHOR>
 * @date 2022-09-16
 */
@Mapper
public interface MidLineDepartureMapper {
    /**
     * 查询启运港航线
     *
     * @param belongId 启运港航线主键
     * @param belongTo 从属
     * @return 启运港航线
     */
    List<Long> selectMidLineDepartureById(Long belongId, String belongTo);

    /**
     * 查询启运港航线列表
     *
     * @return 启运港航线集合
     */
    List<MidLineDeparture> selectMidLineDepartureByLineIds(@Param("lineIds") Long[] lineIds, String belongTo);

    /**
     * 查询启运港航线列表
     *
     * @return 启运港航线集合
     */
    List<MidLineDeparture> selectMidLineDepartureList(MidLineDeparture MidLineDeparture);

    /**
     * 新增启运港航线
     *
     * @param MidLineDeparture 启运港航线
     * @return 结果
     */
    int insertMidLineDeparture(MidLineDeparture MidLineDeparture);

    /**
     * 删除启运港航线
     *
     * @param belongId 启运港航线主键
     * @param belongTo 从属
     * @return 结果
     */
    int deleteMidLineDepartureById(Long belongId, String belongTo);

    /**
     * 批量删除启运港航线
     *
     * @param belongIds 需要删除的数据主键集合
     * @param belongTo  从属
     * @return 结果
     */
    int deleteMidLineDepartureByIds(Long[] belongIds, String belongTo);


    /**
     * 批量新增${subTable.functionName}
     *
     * @param belongList ${subTable.functionName}列表
     * @return 结果
     */
    int batchLD(List<MidLineDeparture> belongList);


}
