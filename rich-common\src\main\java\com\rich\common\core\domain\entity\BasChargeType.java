package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 【请填写功能名称】对象 bas_charge_type
 * 
 * <AUTHOR>
 * @date 2022-08-30
 */
public class BasChargeType extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    private Long chargeTypeId;

    private Long parentId;

    private String chargeTypeShortName;

    private String chargeTypeEnName;

    private String chargeTypeLocalName;

    private Integer orderNum;

    private String status;

    private String checkDepts;

    private Long[] checkDeptIds;

    private String enterDepts;

    private Long[] enterDeptIds;

    private BasCharge charge;

    private String chargeTypeQuery;
    private String isReimbursable;

    public String getIsReimbursable() {
        return isReimbursable;
    }

    public void setIsReimbursable(String isReimbursable) {
        this.isReimbursable = isReimbursable;
    }

    public String getChargeTypeQuery() {
        return chargeTypeQuery;
    }

    public void setChargeTypeQuery(String chargeTypeQuery) {
        this.chargeTypeQuery = chargeTypeQuery;
    }

    public String getEnterDepts() {
        return enterDepts;
    }

    public void setEnterDepts(String enterDepts) {
        this.enterDepts = enterDepts;
    }

    public BasCharge getCharge() {
        return charge;
    }

    public void setCharge(BasCharge charge) {
        this.charge = charge;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCheckDepts() {
        return checkDepts;
    }

    public void setCheckDepts(String checkDepts) {
        this.checkDepts = checkDepts;
    }


    public Long[] getCheckDeptIds() {
        return checkDeptIds;
    }

    public void setCheckDeptIds(Long[] checkDeptIds) {
        this.checkDeptIds = checkDeptIds;
    }


    public Long[] getEnterDeptIds() {
        return enterDeptIds;
    }

    public void setEnterDeptIds(Long[] enterDeptIds) {
        this.enterDeptIds = enterDeptIds;
    }

    public void setChargeTypeId(Long chargeTypeId)
    {
        this.chargeTypeId = chargeTypeId;
    }

    public Long getChargeTypeId() 
    {
        return chargeTypeId;
    }
    public void setChargeTypeShortName(String chargeTypeShortName) 
    {
        this.chargeTypeShortName = chargeTypeShortName;
    }

    public String getChargeTypeShortName() 
    {
        return chargeTypeShortName;
    }
    public void setChargeTypeEnName(String chargeTypeEnName) 
    {
        this.chargeTypeEnName = chargeTypeEnName;
    }

    public String getChargeTypeEnName() 
    {
        return chargeTypeEnName;
    }
    public void setChargeTypeLocalName(String chargeTypeLocalName) 
    {
        this.chargeTypeLocalName = chargeTypeLocalName;
    }

    public String getChargeTypeLocalName() 
    {
        return chargeTypeLocalName;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("chargeTypeId", getChargeTypeId())
            .append("chargeTypeShortName", getChargeTypeShortName())
            .append("chargeTypeEnName", getChargeTypeEnName())
            .append("chargeTypeLocalName", getChargeTypeLocalName())
            .append("orderNum", getOrderNum())
            .append("remark", getRemark())
            .toString();
    }
}
