package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpImportDispatchTruck;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsOpImportDispatchTruckMapper;
import com.rich.system.service.RsOpImportDispatchTruckService;

/**
 * 进口派送服务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Service
public class RsOpImportDispatchTruckServiceImpl implements RsOpImportDispatchTruckService {
    @Autowired
    private RsOpImportDispatchTruckMapper rsOpImportDispatchTruckMapper;

    /**
     * 查询进口派送服务
     *
     * @param importDispatchTruckId 进口派送服务主键
     * @return 进口派送服务
     */
    @Override
    public RsOpImportDispatchTruck selectRsOpImportDispatchTruckByImportDispatchTruckId(Long importDispatchTruckId) {
        return rsOpImportDispatchTruckMapper.selectRsOpImportDispatchTruckByImportDispatchTruckId(importDispatchTruckId);
    }

    /**
     * 查询进口派送服务列表
     *
     * @param rsOpImportDispatchTruck 进口派送服务
     * @return 进口派送服务
     */
    @Override
    public List<RsOpImportDispatchTruck> selectRsOpImportDispatchTruckList(RsOpImportDispatchTruck rsOpImportDispatchTruck) {
        return rsOpImportDispatchTruckMapper.selectRsOpImportDispatchTruckList(rsOpImportDispatchTruck);
    }

    /**
     * 新增进口派送服务
     *
     * @param rsOpImportDispatchTruck 进口派送服务
     * @return 结果
     */
    @Override
    public int insertRsOpImportDispatchTruck(RsOpImportDispatchTruck rsOpImportDispatchTruck) {
        return rsOpImportDispatchTruckMapper.insertRsOpImportDispatchTruck(rsOpImportDispatchTruck);
    }

    /**
     * 修改进口派送服务
     *
     * @param rsOpImportDispatchTruck 进口派送服务
     * @return 结果
     */
    @Override
    public int updateRsOpImportDispatchTruck(RsOpImportDispatchTruck rsOpImportDispatchTruck) {
        return rsOpImportDispatchTruckMapper.updateRsOpImportDispatchTruck(rsOpImportDispatchTruck);
    }

    /**
     * 修改进口派送服务状态
     *
     * @param rsOpImportDispatchTruck 进口派送服务
     * @return 进口派送服务
     */
    @Override
    public int changeStatus(RsOpImportDispatchTruck rsOpImportDispatchTruck) {
        return rsOpImportDispatchTruckMapper.updateRsOpImportDispatchTruck(rsOpImportDispatchTruck);
    }

    /**
     * 批量删除进口派送服务
     *
     * @param importDispatchTruckIds 需要删除的进口派送服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpImportDispatchTruckByImportDispatchTruckIds(Long[] importDispatchTruckIds) {
        return rsOpImportDispatchTruckMapper.deleteRsOpImportDispatchTruckByImportDispatchTruckIds(importDispatchTruckIds);
    }

    /**
     * 删除进口派送服务信息
     *
     * @param importDispatchTruckId 进口派送服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpImportDispatchTruckByImportDispatchTruckId(Long importDispatchTruckId) {
        return rsOpImportDispatchTruckMapper.deleteRsOpImportDispatchTruckByImportDispatchTruckId(importDispatchTruckId);
    }
}
