package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.BasMessageType;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.system.mapper.BasMessageTypeMapper;
import com.rich.system.service.BasMessageTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-02-21
 */
@Service

public class BasMessageTypeServiceImpl implements BasMessageTypeService {
    @Autowired
    private  BasMessageTypeMapper basMessageTypeMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param messageTypeId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public BasMessageType selectBasMessageTypeByMessageTypeId(Long messageTypeId) {
        return basMessageTypeMapper.selectBasMessageTypeByMessageTypeId(messageTypeId);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param basMessageType 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<BasMessageType> selectBasMessageTypeList(BasMessageType basMessageType) {
        return basMessageTypeMapper.selectBasMessageTypeList(basMessageType);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param basMessageType 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertBasMessageType(BasMessageType basMessageType) {
        basMessageType.setCreateTime(DateUtils.getNowDate());
        basMessageType.setCreateBy(SecurityUtils.getUserId());
        return basMessageTypeMapper.insertBasMessageType(basMessageType);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param basMessageType 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateBasMessageType(BasMessageType basMessageType) {
        basMessageType.setUpdateTime(DateUtils.getNowDate());
        basMessageType.setUpdateBy(SecurityUtils.getUserId());
        return basMessageTypeMapper.updateBasMessageType(basMessageType);
    }

    /**
     * 修改【请填写功能名称】状态
     *
     * @param basMessageType 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public int changeStatus(BasMessageType basMessageType) {
        return basMessageTypeMapper.updateBasMessageType(basMessageType);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param messageTypeIds 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteBasMessageTypeByMessageTypeIds(Long[] messageTypeIds) {
        return basMessageTypeMapper.deleteBasMessageTypeByMessageTypeIds(messageTypeIds);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param messageTypeId 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteBasMessageTypeByMessageTypeId(Long messageTypeId) {
        return basMessageTypeMapper.deleteBasMessageTypeByMessageTypeId(messageTypeId);
    }
}
