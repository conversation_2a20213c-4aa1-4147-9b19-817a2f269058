package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasProcess;
import com.rich.common.core.domain.entity.BasProcessType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 进度分类Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-05
 */
@Mapper
public interface BasProcessTypeMapper {
    /**
     * 查询进度分类
     *
     * @param processTypeId 进度分类主键
     * @return 进度分类
     */
    BasProcessType selectBasProcessTypeByProcessTypeId(Long processTypeId);

    /**
     * 查询进度分类列表
     *
     * @param basProcessType 进度分类
     * @return 进度分类集合
     */
    List<BasProcessType> selectBasProcessTypeList(BasProcessType basProcessType);

    /**
     * 新增进度分类
     *
     * @param basProcessType 进度分类
     * @return 结果
     */
    int insertBasProcessType(BasProcessType basProcessType);

    /**
     * 修改进度分类
     *
     * @param basProcessType 进度分类
     * @return 结果
     */
    int updateBasProcessType(BasProcessType basProcessType);

    /**
     * 删除进度分类
     *
     * @param processTypeId 进度分类主键
     * @return 结果
     */
    int deleteBasProcessTypeByProcessTypeId(Long processTypeId);

    /**
     * 批量删除进度分类
     *
     * @param processTypeIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasProcessTypeByProcessTypeIds(Long[] processTypeIds);

    /**
     * 批量删除进程名称
     *
     * @param processTypeIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasProcessByProcessTypeIds(Long[] processTypeIds);

    /**
     * 批量新增进程名称
     *
     * @param basProcessList 进程名称列表
     * @return 结果
     */
    int batchBasProcess(List<BasProcess> basProcessList);


    /**
     * 通过进度分类主键删除进程名称信息
     *
     * @param processTypeId 进度分类ID
     * @return 结果
     */
    int deleteBasProcessByProcessTypeId(Long processTypeId);
}
