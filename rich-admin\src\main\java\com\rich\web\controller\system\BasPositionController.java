package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.constant.UserConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.domain.entity.BasPosition;
import com.rich.system.service.BasPositionService;

/**
 * 岗位信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/post")
public class BasPositionController extends BaseController {

    @Autowired
    private BasPositionService postService;

    /**
     * 获取岗位列表
     */
    @GetMapping("/list")
    public TableDataInfo list(BasPosition post) {
        List<BasPosition> list = postService.selectPostList(post);
        return getDataTable(list);
    }

    @Log(title = "岗位管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:post:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasPosition post) {
        List<BasPosition> list = postService.selectPostList(post);
        ExcelUtil<BasPosition> util = new ExcelUtil<BasPosition>(BasPosition.class);
        util.exportExcel(response, list, "岗位数据");
    }

    /**
     * 根据岗位编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:post:edit')")
    @GetMapping(value = "/{postId}")
    public AjaxResult getInfo(@PathVariable Long postId)
    {
        return AjaxResult.success(postService.selectPostById(postId));
    }

    /**
     * 新增岗位
     */
    @PreAuthorize("@ss.hasPermi('system:post:add')")
    @Log(title = "岗位管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody BasPosition post) {
        if (!postService.checkPostNameUnique(post)) {
            return AjaxResult.error("新增岗位'" + post.getPositionLocalName() + "'失败，岗位名称已存在");
        } else if (!postService.checkPostCodeUnique(post)) {
            return AjaxResult.error("新增岗位'" + post.getPositionLocalName() + "'失败，岗位编码已存在");
        }
        post.setCreateBy(getUserId());
        return toAjax(postService.insertPost(post));
    }

    /**
     * 修改岗位
     */
    @PreAuthorize("@ss.hasPermi('system:post:edit')")
    @Log(title = "岗位管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody BasPosition post) {
        if (!postService.checkPostNameUnique(post)) {
            return AjaxResult.error("修改岗位'" + post.getPositionLocalName() + "'失败，岗位名称已存在");
        } else if (!postService.checkPostCodeUnique(post)) {
            return AjaxResult.error("修改岗位'" + post.getPositionLocalName() + "'失败，岗位编码已存在");
        }
        post.setUpdateBy(getUserId());
        return toAjax(postService.updatePost(post));
    }

    /**
     * 删除岗位
     */
    @PreAuthorize("@ss.hasPermi('system:post:remove')")
    @Log(title = "岗位管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{postIds}")
    public AjaxResult remove(@PathVariable Long[] postIds)
    {
        return toAjax(postService.deletePostByIds(postIds));
    }

    /**
     * 获取岗位选择框列表
     */
    @GetMapping("/optionselect")
    public AjaxResult optionselect()
    {
        List<BasPosition> posts = postService.selectPostAll();
        return AjaxResult.success(posts);
    }

    /**
     * 根据用户id获取自己职级以下的职级
     */
    @GetMapping("/underUser/{userId}")
    public TableDataInfo list(@PathVariable("userId") Long userId) {
        List<BasPosition> list = postService.selectPostListUnderUser(userId);
        return getDataTable(list);
    }

    /**
     * 根据用户id获取自己的职级
     */
    @GetMapping("/user/{userId}")
    public Long getBasPositionByUserId(@PathVariable("userId") Long userId) {
        return postService.selectPostByUserId(userId);
    }
}
