package com.rich.system.mapper;

import com.rich.common.core.domain.entity.MidRevenueTons;
import com.rich.common.core.domain.entity.RsQuotation;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 报价列表Mapper接口
 *
 * <AUTHOR>
 * @date 2023-05-04
 */
@Mapper
public interface RsQuotationMapper {
    /**
     * 查询报价列表
     *
     * @param quotationId 报价列表主键
     * @return 报价列表
     */
    RsQuotation selectRsQuotationByQuotationId(Long quotationId);

    /**
     * 查询报价列表列表
     *
     * @param rsQuotation 报价列表
     * @return 报价列表集合
     */
    List<RsQuotation> selectRsQuotationList(RsQuotation rsQuotation);

    /**
     * 新增报价列表
     *
     * @param rsQuotation 报价列表
     * @return 结果
     */
    int insertRsQuotation(RsQuotation rsQuotation);

    /**
     * 修改报价列表
     *
     * @param rsQuotation 报价列表
     * @return 结果
     */
    int updateRsQuotation(RsQuotation rsQuotation);

    /**
     * 删除报价列表
     *
     * @param quotationId 报价列表主键
     * @return 结果
     */
    int deleteRsQuotationByQuotationId(Long quotationId);

    /**
     * 批量删除报价列表
     *
     * @param quotationIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsQuotationByQuotationIds(Long[] quotationIds);

    /**
     * 批量删除货量
     *
     * @param quotationIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteMidRevenueTonsByQuotationIds(Long[] quotationIds);

    /**
     * 批量新增货量
     *
     * @param midrevenueTonList 货量列表
     * @return 结果
     */
    int batchMidRevenueTons(List<MidRevenueTons> midrevenueTonList);


    /**
     * 通过报价列表主键删除货量信息
     *
     * @param quotationId 报价列表ID
     * @return 结果
     */
    int deleteMidRevenueTonsByQuotationId(Long quotationId);
}
