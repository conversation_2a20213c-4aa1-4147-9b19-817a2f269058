package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 记录提单信息对象 rs_booking_message
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
public class RsBookingMessage extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long rsBookingMessageId;

    /**
     * rct
     */
    @Excel(name = "rct")
    private Long rctId;

    /**
     * 发货人
     */
    @Excel(name = "发货人")
    private String bookingShipper;

    /**
     * 收货人
     */
    @Excel(name = "收货人")
    private String bookingConsignee;

    /**
     * 通知人
     */
    @Excel(name = "通知人")
    private String bookingNotifyParty;

    /**
     * 代理
     */
    @Excel(name = "代理")
    private String bookingAgent;

    /**
     * 唛头
     */
    @Excel(name = "唛头")
    private String shippingMark;

    /**
     * 件数
     */
    @Excel(name = "件数")
    private String packageQuantity;

    /**
     * 货描
     */
    @Excel(name = "货描")
    private String goodsDescription;

    /**
     * 体积
     */
    @Excel(name = "体积")
    private String goodsVolume;

    /**
     * 重量
     */
    @Excel(name = "重量")
    private String grossWeight;

    /**
     * 提单类型
     */
    @Excel(name = "提单类型")
    private String blTypeCode;

    /**
     * 出单方式
     */
    @Excel(name = "出单方式")
    private String sqdIssueType;

    /**
     * 交单方式
     */
    @Excel(name = "交单方式")
    private String sqdDocDeliveryWay;
    private String isMain;
    private String blNo;
    private String containerNo;
    private String sealNo;
    private String containerType;
    private String mBlNo;
    private String hBlNo;
    private String blRemark;
    private Date onBoardDate;
    private String payWay;
    private String polName;
    private String podName;
    private String destinationPort;

    public String getDestinationPort() {
        return destinationPort;
    }

    public void setDestinationPort(String destinationPort) {
        this.destinationPort = destinationPort;
    }

    public String getPodName() {
        return podName;
    }

    public void setPodName(String podName) {
        this.podName = podName;
    }

    public String getPolName() {
        return polName;
    }

    public void setPolName(String polName) {
        this.polName = polName;
    }

    public String getPayWay() {
        return payWay;
    }

    public void setPayWay(String payWay) {
        this.payWay = payWay;
    }

    public Date getOnBoardDate() {
        return onBoardDate;
    }

    public void setOnBoardDate(Date onBoardDate) {
        this.onBoardDate = onBoardDate;
    }

    public String getBlRemark() {
        return blRemark;
    }

    public void setBlRemark(String blRemark) {
        this.blRemark = blRemark;
    }

    public String gethBlNo() {
        return hBlNo;
    }

    public void sethBlNo(String hBlNo) {
        this.hBlNo = hBlNo;
    }

    public String getmBlNo() {
        return mBlNo;
    }

    public void setmBlNo(String mBlNo) {
        this.mBlNo = mBlNo;
    }

    public String getContainerType() {
        return containerType;
    }

    public void setContainerType(String containerType) {
        this.containerType = containerType;
    }

    public String getSealNo() {
        return sealNo;
    }

    public void setSealNo(String sealNo) {
        this.sealNo = sealNo;
    }

    public String getContainerNo() {
        return containerNo;
    }

    public void setContainerNo(String containerNo) {
        this.containerNo = containerNo;
    }

    public String getBlNo() {
        return blNo;
    }

    public void setBlNo(String blNo) {
        this.blNo = blNo;
    }

    public String getIsMain() {
        return isMain;
    }

    public void setIsMain(String isMain) {
        this.isMain = isMain;
    }

    public Long getRsBookingMessageId() {
        return rsBookingMessageId;
    }

    public void setRsBookingMessageId(Long rsBookingMessageId) {
        this.rsBookingMessageId = rsBookingMessageId;
    }

    public Long getRctId() {
        return rctId;
    }

    public void setRctId(Long rctId) {
        this.rctId = rctId;
    }

    public String getBookingShipper() {
        return bookingShipper;
    }

    public void setBookingShipper(String bookingShipper) {
        this.bookingShipper = bookingShipper;
    }

    public String getBookingConsignee() {
        return bookingConsignee;
    }

    public void setBookingConsignee(String bookingConsignee) {
        this.bookingConsignee = bookingConsignee;
    }

    public String getBookingNotifyParty() {
        return bookingNotifyParty;
    }

    public void setBookingNotifyParty(String bookingNotifyParty) {
        this.bookingNotifyParty = bookingNotifyParty;
    }

    public String getBookingAgent() {
        return bookingAgent;
    }

    public void setBookingAgent(String bookingAgent) {
        this.bookingAgent = bookingAgent;
    }

    public String getShippingMark() {
        return shippingMark;
    }

    public void setShippingMark(String shippingMark) {
        this.shippingMark = shippingMark;
    }

    public String getPackageQuantity() {
        return packageQuantity;
    }

    public void setPackageQuantity(String packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public String getGoodsDescription() {
        return goodsDescription;
    }

    public void setGoodsDescription(String goodsDescription) {
        this.goodsDescription = goodsDescription;
    }

    public String getGoodsVolume() {
        return goodsVolume;
    }

    public void setGoodsVolume(String goodsVolume) {
        this.goodsVolume = goodsVolume;
    }

    public String getGrossWeight() {
        return grossWeight;
    }

    public void setGrossWeight(String grossWeight) {
        this.grossWeight = grossWeight;
    }

    public String getBlTypeCode() {
        return blTypeCode;
    }

    public void setBlTypeCode(String blTypeCode) {
        this.blTypeCode = blTypeCode;
    }

    public String getSqdIssueType() {
        return sqdIssueType;
    }

    public void setSqdIssueType(String sqdIssueType) {
        this.sqdIssueType = sqdIssueType;
    }

    public String getSqdDocDeliveryWay() {
        return sqdDocDeliveryWay;
    }

    public void setSqdDocDeliveryWay(String sqdDocDeliveryWay) {
        this.sqdDocDeliveryWay = sqdDocDeliveryWay;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("rsBookingMessageId", getRsBookingMessageId())
                .append("rctId", getRctId())
                .append("bookingShipper", getBookingShipper())
                .append("bookingConsignee", getBookingConsignee())
                .append("bookingNotifyParty", getBookingNotifyParty())
                .append("bookingAgent", getBookingAgent())
                .append("shippingMark", getShippingMark())
                .append("packageQuantity", getPackageQuantity())
                .append("goodsDescription", getGoodsDescription())
                .append("goodsVolume", getGoodsVolume())
                .append("grossWeight", getGrossWeight())
                .append("blTypeCode", getBlTypeCode())
                .append("sqdIssueType", getSqdIssueType())
                .append("sqdDocDeliveryWay", getSqdDocDeliveryWay())
                .toString();
    }
}
