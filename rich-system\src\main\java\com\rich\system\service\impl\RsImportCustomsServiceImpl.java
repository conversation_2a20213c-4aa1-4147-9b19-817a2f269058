package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsImportCustoms;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsImportCustomsMapper;
import com.rich.system.service.RsImportCustomsService;

/**
 * 进口清关Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@Service
public class RsImportCustomsServiceImpl implements RsImportCustomsService {
    @Autowired
    private RsImportCustomsMapper rsImportCustomsMapper;

    /**
     * 查询进口清关
     *
     * @param importCustomsId 进口清关主键
     * @return 进口清关
     */
    @Override
    public RsImportCustoms selectRsImportCustomsByImportCustomsId(Long importCustomsId) {
        return rsImportCustomsMapper.selectRsImportCustomsByImportCustomsId(importCustomsId);
    }

    /**
     * 查询进口清关列表
     *
     * @param rsImportCustoms 进口清关
     * @return 进口清关
     */
    @Override
    public List<RsImportCustoms> selectRsImportCustomsList(RsImportCustoms rsImportCustoms) {
        return rsImportCustomsMapper.selectRsImportCustomsList(rsImportCustoms);
    }

    /**
     * 新增进口清关
     *
     * @param rsImportCustoms 进口清关
     * @return 结果
     */
    @Override
    public int insertRsImportCustoms(RsImportCustoms rsImportCustoms) {
        return rsImportCustomsMapper.insertRsImportCustoms(rsImportCustoms);
    }

    /**
     * 修改进口清关
     *
     * @param rsImportCustoms 进口清关
     * @return 结果
     */
    @Override
    public int updateRsImportCustoms(RsImportCustoms rsImportCustoms) {
        return rsImportCustomsMapper.updateRsImportCustoms(rsImportCustoms);
    }

    /**
     * 修改进口清关状态
     *
     * @param rsImportCustoms 进口清关
     * @return 进口清关
     */
    @Override
    public int changeStatus(RsImportCustoms rsImportCustoms) {
        return rsImportCustomsMapper.updateRsImportCustoms(rsImportCustoms);
    }

    /**
     * 批量删除进口清关
     *
     * @param importCustomsIds 需要删除的进口清关主键
     * @return 结果
     */
    @Override
    public int deleteRsImportCustomsByImportCustomsIds(Long[] importCustomsIds) {
        return rsImportCustomsMapper.deleteRsImportCustomsByImportCustomsIds(importCustomsIds);
    }

    /**
     * 删除进口清关信息
     *
     * @param importCustomsId 进口清关主键
     * @return 结果
     */
    @Override
    public int deleteRsImportCustomsByImportCustomsId(Long importCustomsId) {
        return rsImportCustomsMapper.deleteRsImportCustomsByImportCustomsId(importCustomsId);
    }
}
