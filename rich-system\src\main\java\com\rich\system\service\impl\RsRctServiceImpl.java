package com.rich.system.service.impl;

import com.rich.common.config.NotificationWebSocket;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.domain.entity.*;
import com.rich.common.core.domain.model.LoginUser;
import com.rich.common.core.text.Convert;
import com.rich.common.utils.*;
import com.rich.system.domain.MidCargoType;
import com.rich.system.domain.MidCarrier;
import com.rich.system.domain.MidServiceType;
import com.rich.system.mapper.*;
import com.rich.system.service.BasPositionService;
import com.rich.system.service.RsRctService;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.rich.common.utils.PageUtils.startPage;

/**
 * 操作单Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-20
 */
@Service
public class RsRctServiceImpl implements RsRctService {
    private static final Logger log = LoggerFactory.getLogger(RsRctServiceImpl.class);

    @Autowired
    private RsRctMapper rsRctMapper;

    @Resource
    private RsServiceInstancesMapper rsServiceInstancesMapper;

    @Resource
    private RsBasicLogisticsMapper rsBasicLogisticsMapper;

    @Resource
    private RsChargeMapper rsChargeMapper;

    @Resource
    private RsOpLogMapper rsOpLogMapper;

    @Autowired
    private MidServiceTypeMapper midServiceTypeMapper;

    @Autowired
    private MidCargoTypeMapper midCargoTypeMapper;

    @Autowired
    private MidCarrierMapper midCarrierMapper;

    @Autowired
    private RedisCacheImpl RedisCache;

    @Autowired
    private RsDocMapper rsDocMapper;

    @Autowired
    private RsPrecarriageMapper rsPrecarriageMapper;

    @Autowired
    private RsImportCustomsMapper rsImportCustomsMapper;

    @Autowired
    private RsExportCustomsMapper rsExportCustomsMapper;

    @Autowired
    private com.rich.common.core.redis.RedisCache redisCache;

    @Autowired
    private ExtCompanyMapper extCompanyMapper;

    @Resource
    private RsOpSeaFclMapper rsOpSeaFclMapper;
    @Resource
    private RsOpSeaLclMapper rsOpSeaLclMapper;
    @Resource
    private RsOpBulkShipMapper rsOpBulkShipMapper;
    @Resource
    private RsOpRoroShipMapper rsOpRoroShipMapper;
    @Resource
    private RsOpAirMapper rsOpAirMapper;
    @Resource
    private RsOpRailMapper rsOpRailMapper;
    @Resource
    private RsOpExpressMapper rsOpExpressMapper;
    @Resource
    private RsOpPortServiceMapper rsOpPortServiceMapper;
    @Resource
    private RsOpTruckMapper rsOpTruckMapper;
    @Resource
    private RsOpExportCustomsClearanceMapper rsOpExportCustomsClearanceMapper;
    @Resource
    private RsOpImportCustomsClearanceMapper rsOpImportCustomsClearanceMapper;
    @Resource
    private RsOpImportDispatchTruckMapper rsOpImportDispatchTruckMapper;
    @Resource
    private RsOpWarehouseMapper rsOpWarehouseMapper;
    @Resource
    private RsOpInspectionAndCertificateMapper rsOpInspectionAndCertificateMapper;
    @Resource
    private RsOpLandMapper rsOpLandMapper;
    @Resource
    private RsOpInsuranceMapper rsOpInsuranceMapper;
    @Resource
    private RsOpExpandServiceMapper rsOpExpandServiceMapper;

    @Resource
    private RsBookingMessageMapper rsBookingMessageMapper;

    @Autowired
    private DataSourceTransactionManager transactionManager;

    @Resource
    private RsOpTruckListMapper rsOpTruckListMapper;

    @Resource
    private RedisIdGeneratorService redisIdGeneratorService;

    @Resource
    private BasPositionService basPositionService;

    /**
     * 查询操作单
     *
     * @param rctId 操作单主键
     * @return 操作单
     */
    @Override
    public RsRct selectRsRctByRctId(Long rctId) {
        RsRct rsRct = rsRctMapper.selectRsRctByRctId(rctId);
        if (rsRct == null) {
            return null;
        }
        RsBookingMessage rsBookingMessage = new RsBookingMessage();
        rsBookingMessage.setRctId(rctId);
        rsRct.setBookingMessagesList(rsBookingMessageMapper.selectRsBookingMessageList(rsBookingMessage));
        rsRct.setCarrierIds(midCarrierMapper.selectMidCarrierById(rctId, "rct").toArray(new Long[0]));
        rsRct.setCargoTypeIds(midCargoTypeMapper.selectMidCargoTypeById(rctId, "rct").toArray(new Long[0]));
        rsRct.setServiceTypeIds(midServiceTypeMapper.selectMidServiceTypeById(rctId, "rct").toArray(new Long[0]));

        List<RsCharge> rsChargeList = rsChargeMapper.selectRsChargeListByRctId(rctId);
        List<RsOpLog> rsOpLogList = rsOpLogMapper.selectRsOpLogByRctId(rctId);

        RsClientMessage rsClientMessage = new RsClientMessage();
        RsServiceInstances rsServiceInstances = rsServiceInstancesMapper.selectRsServiceInstances(rctId, "client");
        if (rsServiceInstances != null) {
            rsClientMessage.setRsServiceInstances(rsServiceInstances);
            Long serviceId = rsServiceInstances.getServiceId();
            rsClientMessage.setRsOpLogList(new ArrayList<>());
            List<RsCharge> clientChargeList = rsChargeMapper.selectRsChargeListByServiceId(serviceId);
            if (clientChargeList != null) {
                rsClientMessage.setRsChargeList(clientChargeList);
            }
            rsOpLogList.forEach(rsOpLog -> {
                if (rsOpLog.getServiceId() != null && rsOpLog.getServiceId().equals(serviceId)) {
                    rsClientMessage.getRsOpLogList().add(rsOpLog);
                }
            });
        } else {
            rsClientMessage.setRsServiceInstances(new RsServiceInstances());
            rsClientMessage.setRsChargeList(Collections.emptyList());
        }

        Long[] serviceTypeIds = rsRct.getServiceTypeIds();

        rsRct.setSupplierIds(new LinkedHashSet<>());

        for (Long serviceTypeId : serviceTypeIds) {
            // 整柜海运
            if (serviceTypeId.equals(1L)) {
                List<RsOpSeaFcl> rsOpSeaFclList = rsOpSeaFclMapper.selectRsOpSeaFclByRctId(rctId, 1L);
                if (!rsOpSeaFclList.isEmpty()) {
                    for (RsOpSeaFcl rsOpSeaFcl : rsOpSeaFclList) {
                        rsOpSeaFcl.setRsChargeList(new ArrayList<>());
                        rsOpSeaFcl.setRsOpLogList(new ArrayList<>());
                        Long serviceId = rsOpSeaFcl.getRsServiceInstances().getServiceId();
                        rsChargeList.forEach(rsCharge -> {
                            if (rsCharge.getServiceId() != null && rsCharge.getServiceId().equals(serviceId)) {
                                rsOpSeaFcl.getRsChargeList().add(rsCharge);
                            }
                        });
                        rsOpLogList.forEach(rsOpLog -> {
                            if (rsOpLog.getServiceId() != null && rsOpLog.getServiceId().equals(serviceId)) {
                                rsOpSeaFcl.getRsOpLogList().add(rsOpLog);
                            }
                        });
                        // 收集供应商id
                        if (rsOpSeaFcl.getRsServiceInstances() != null && rsOpSeaFcl.getRsServiceInstances().getSupplierId() != null) {
                            rsRct.getSupplierIds().add(rsOpSeaFcl.getRsServiceInstances().getSupplierId());
                        }
                    }
                }
                rsRct.setRsOpSeaFclList(rsOpSeaFclList);
            }
            // 拼柜海运
            if (serviceTypeId.equals(2L)) {
                List<RsOpSeaLcl> rsOpSeaLclList = rsOpSeaLclMapper.selectRsOpSeaLclByRctId(rctId, 2L);
                if (!rsOpSeaLclList.isEmpty()) {
                    for (RsOpSeaLcl rsOpSeaLcl : rsOpSeaLclList) {
                        rsOpSeaLcl.setRsChargeList(new ArrayList<>());
                        rsOpSeaLcl.setRsOpLogList(new ArrayList<>());
                        Long serviceId = rsOpSeaLcl.getRsServiceInstances().getServiceId();
                        rsChargeList.forEach(rsCharge -> {
                            if (rsCharge.getServiceId() != null && rsCharge.getServiceId().equals(serviceId)) {
                                rsOpSeaLcl.getRsChargeList().add(rsCharge);
                            }
                        });
                        rsOpLogList.forEach(rsOpLog -> {
                            if (rsOpLog.getServiceId() != null && rsOpLog.getServiceId().equals(serviceId)) {
                                rsOpSeaLcl.getRsOpLogList().add(rsOpLog);
                            }
                        });
                        // 收集供应商id
                        if (rsOpSeaLcl.getRsServiceInstances() != null && rsOpSeaLcl.getRsServiceInstances().getSupplierId() != null) {
                            rsRct.getSupplierIds().add(rsOpSeaLcl.getRsServiceInstances().getSupplierId());
                        }
                    }
                }
                rsRct.setRsOpSeaLclList(rsOpSeaLclList);
            }
            //空运
            if (serviceTypeId.equals(10L)) {
                List<RsOpAir> rsOpAirList = rsOpAirMapper.selectRsOpAirByRctId(rctId, 10L);
                if (!rsOpAirList.isEmpty()) {
                    for (RsOpAir rsOpAir : rsOpAirList) {
                        rsOpAir.setRsChargeList(new ArrayList<>());
                        rsOpAir.setRsOpLogList(new ArrayList<>());
                        Long serviceId = rsOpAir.getRsServiceInstances().getServiceId();
                        rsChargeList.forEach(rsCharge -> {
                            if (rsCharge.getServiceId() != null && rsCharge.getServiceId().equals(serviceId)) {
                                rsOpAir.getRsChargeList().add(rsCharge);
                            }
                        });
                        rsOpLogList.forEach(rsOpLog -> {
                            if (rsOpLog.getServiceId() != null && rsOpLog.getServiceId().equals(serviceId)) {
                                rsOpAir.getRsOpLogList().add(rsOpLog);
                            }
                        });
                        // 收集供应商id
                        if (rsOpAir.getRsServiceInstances() != null && rsOpAir.getRsServiceInstances().getSupplierId() != null) {
                            rsRct.getSupplierIds().add(rsOpAir.getRsServiceInstances().getSupplierId());
                        }
                    }
                }
                rsRct.setRsOpAirList(rsOpAirList);
            }
            //整柜铁路
            if (serviceTypeId.equals(20L)) {
                RsOpRailFCL rsOpRailFCL = rsOpRailMapper.selectRsOpRailFclByRctId(rctId, 20L);
                if (ObjectUtils.isNotEmpty(rsOpRailFCL)) {
                    rsOpRailFCL.setRsChargeList(new ArrayList<>());
                    rsOpRailFCL.setRsOpLogList(new ArrayList<>());
                    Long serviceId = rsOpRailFCL.getRsServiceInstances().getServiceId();
                    rsChargeList.forEach(rsCharge -> {
                        if (rsCharge.getServiceId() != null && rsCharge.getServiceId().equals(serviceId)) {
                            rsOpRailFCL.getRsChargeList().add(rsCharge);
                        }
                    });
                    rsOpLogList.forEach(rsOpLog -> {
                        if (rsOpLog.getServiceId() != null && rsOpLog.getServiceId().equals(serviceId)) {
                            rsOpRailFCL.getRsOpLogList().add(rsOpLog);
                        }
                    });
                    // 收集供应商id
                    if (rsOpRailFCL.getRsServiceInstances() != null && rsOpRailFCL.getRsServiceInstances().getSupplierId() != null) {
                        rsRct.getSupplierIds().add(rsOpRailFCL.getRsServiceInstances().getSupplierId());
                    }
                }
                rsRct.setRsOpRailFCL(rsOpRailFCL);
            }
            //拼柜铁路
            if (serviceTypeId.equals(21L)) {
                RsOpRailLCL rsOpRailLCL = rsOpRailMapper.selectRsOpRailLclByRctId(rctId, 21L);
                if (ObjectUtils.isNotEmpty(rsOpRailLCL)) {
                    rsOpRailLCL.setRsChargeList(new ArrayList<>());
                    rsOpRailLCL.setRsOpLogList(new ArrayList<>());
                    Long serviceId = rsOpRailLCL.getRsServiceInstances().getServiceId();
                    rsChargeList.forEach(rsCharge -> {
                        if (rsCharge.getServiceId() != null && rsCharge.getServiceId().equals(serviceId)) {
                            rsOpRailLCL.getRsChargeList().add(rsCharge);
                        }
                    });
                    rsOpLogList.forEach(rsOpLog -> {
                        if (rsOpLog.getServiceId() != null && rsOpLog.getServiceId().equals(serviceId)) {
                            rsOpRailLCL.getRsOpLogList().add(rsOpLog);
                        }
                    });
                    // 收集供应商id
                    if (rsOpRailLCL.getRsServiceInstances() != null && rsOpRailLCL.getRsServiceInstances().getSupplierId() != null) {
                        rsRct.getSupplierIds().add(rsOpRailLCL.getRsServiceInstances().getSupplierId());
                    }
                }
                rsRct.setRsOpRailLCL(rsOpRailLCL);
            }
            // 快递
            if (serviceTypeId.equals(40L)) {
                RsOpExpress rsOpExpress = rsOpExpressMapper.selectRsOpExpressByRctId(rctId, 40L);
                if (ObjectUtils.isNotEmpty(rsOpExpress)) {
                    rsOpExpress.setRsChargeList(new ArrayList<>());
                    rsOpExpress.setRsOpLogList(new ArrayList<>());
                    Long serviceId = rsOpExpress.getRsServiceInstances().getServiceId();
                    rsChargeList.forEach(rsCharge -> {
                        if (rsCharge.getServiceId() != null && rsCharge.getServiceId().equals(serviceId)) {
                            rsOpExpress.getRsChargeList().add(rsCharge);
                        }
                    });
                    rsOpLogList.forEach(rsOpLog -> {
                        if (rsOpLog.getServiceId() != null && rsOpLog.getServiceId().equals(serviceId)) {
                            rsOpExpress.getRsOpLogList().add(rsOpLog);
                        }
                    });
                    // 收集供应商id
                    if (rsOpExpress.getRsServiceInstances() != null && rsOpExpress.getRsServiceInstances().getSupplierId() != null) {
                        rsRct.getSupplierIds().add(rsOpExpress.getRsServiceInstances().getSupplierId());
                    }
                }
                rsRct.setRsOpExpress(rsOpExpress);
            }
            // 整柜拖车
            if (serviceTypeId.equals(50L)) {
                List<RsOpCtnrTruck> rsOpCtnrTruckList = rsOpTruckMapper.selectRsOpCtnrTruckByRctId(rctId, 50L);
                if (!rsOpCtnrTruckList.isEmpty()) {
                    for (RsOpCtnrTruck rsOpCtnrTruck : rsOpCtnrTruckList) {
                        rsOpCtnrTruck.setRsChargeList(new ArrayList<>());
                        rsOpCtnrTruck.setRsOpLogList(new ArrayList<>());
                        Long serviceId = rsOpCtnrTruck.getRsServiceInstances().getServiceId();
                        List<RsOpTruckList> rsOpTruckListList = rsOpTruckListMapper.selectRsOpTruckListByServiceId(serviceId);
                        rsChargeList.forEach(rsCharge -> {
                            if (rsCharge.getServiceId() != null && rsCharge.getServiceId().equals(serviceId)) {
                                rsOpCtnrTruck.getRsChargeList().add(rsCharge);
                            }
                        });
                        rsOpLogList.forEach(rsOpLog -> {
                            if (rsOpLog.getServiceId() != null && rsOpLog.getServiceId().equals(serviceId)) {
                                rsOpCtnrTruck.getRsOpLogList().add(rsOpLog);
                            }
                        });
                        // 收集供应商id
                        if (rsOpCtnrTruck.getRsServiceInstances() != null && rsOpCtnrTruck.getRsServiceInstances().getSupplierId() != null) {
                            rsRct.getSupplierIds().add(rsOpCtnrTruck.getRsServiceInstances().getSupplierId());
                        }
                        rsOpCtnrTruck.setRsOpTruckList(rsOpTruckListList);
                    }
                }
                rsRct.setRsOpCtnrTruckList(rsOpCtnrTruckList);
            }
            // 散货拖车
            if (serviceTypeId.equals(51L)) {
                List<RsOpBulkTruck> rsOpBulkTruckList = rsOpTruckMapper.selectRsOpBulkTruckByRctId(rctId, 51L);
                if (!rsOpBulkTruckList.isEmpty()) {
                    for (RsOpBulkTruck rsOpBulkTruck : rsOpBulkTruckList) {
                        rsOpBulkTruck.setRsChargeList(new ArrayList<>());
                        rsOpBulkTruck.setRsOpLogList(new ArrayList<>());
                        Long serviceId = rsOpBulkTruck.getRsServiceInstances().getServiceId();
                        List<RsOpTruckList> rsOpTruckListList = rsOpTruckListMapper.selectRsOpTruckListByServiceId(serviceId);
                        rsChargeList.forEach(rsCharge -> {
                            if (rsCharge.getServiceId() != null && rsCharge.getServiceId().equals(serviceId)) {
                                rsOpBulkTruck.getRsChargeList().add(rsCharge);
                            }
                        });
                        rsOpLogList.forEach(rsOpLog -> {
                            if (rsOpLog.getServiceId() != null && rsOpLog.getServiceId().equals(serviceId)) {
                                rsOpBulkTruck.getRsOpLogList().add(rsOpLog);
                            }
                        });
                        // 收集供应商id
                        if (rsOpBulkTruck.getRsServiceInstances() != null && rsOpBulkTruck.getRsServiceInstances().getSupplierId() != null) {
                            rsRct.getSupplierIds().add(rsOpBulkTruck.getRsServiceInstances().getSupplierId());
                        }
                        rsOpBulkTruck.setRsOpTruckList(rsOpTruckListList);
                    }
                }
                rsRct.setRsOpBulkTruckList(rsOpBulkTruckList);
            }
            // 单证报关
            if (serviceTypeId.equals(60L)) {
                List<RsOpDocDeclare> rsOpDocDeclareList = rsOpExportCustomsClearanceMapper.selectRsOpDocDeclareByRctId(rctId, 60L);
                if (!rsOpDocDeclareList.isEmpty()) {
                    for (RsOpDocDeclare rsOpDocDeclare : rsOpDocDeclareList) {
                        rsOpDocDeclare.setRsChargeList(new ArrayList<>());
                        rsOpDocDeclare.setRsOpLogList(new ArrayList<>());
                        Long serviceId = rsOpDocDeclare.getRsServiceInstances().getServiceId();
                        rsChargeList.forEach(rsCharge -> {
                            if (rsCharge.getServiceId() != null && rsCharge.getServiceId().equals(serviceId)) {
                                rsOpDocDeclare.getRsChargeList().add(rsCharge);
                            }
                        });
                        rsOpLogList.forEach(rsOpLog -> {
                            if (rsOpLog.getServiceId() != null && rsOpLog.getServiceId().equals(serviceId)) {
                                rsOpDocDeclare.getRsOpLogList().add(rsOpLog);
                            }
                        });
                        // 收集供应商id
                        if (rsOpDocDeclare.getRsServiceInstances() != null && rsOpDocDeclare.getRsServiceInstances().getSupplierId() != null) {
                            rsRct.getSupplierIds().add(rsOpDocDeclare.getRsServiceInstances().getSupplierId());
                        }
                    }
                }
                rsRct.setRsOpDocDeclareList(rsOpDocDeclareList);
            }
            // 全包报关
            if (serviceTypeId.equals(61L)) {
                List<RsOpFreeDeclare> rsOpFreeDeclareList = rsOpExportCustomsClearanceMapper.selectRsOpFreeDeclareByRctId(rctId, 61L);
                if (!rsOpFreeDeclareList.isEmpty()) {
                    for (RsOpFreeDeclare rsOpFreeDeclare : rsOpFreeDeclareList) {
                        rsOpFreeDeclare.setRsChargeList(new ArrayList<>());
                        rsOpFreeDeclare.setRsOpLogList(new ArrayList<>());
                        Long serviceId = rsOpFreeDeclare.getRsServiceInstances().getServiceId();
                        rsChargeList.forEach(rsCharge -> {
                            if (rsCharge.getServiceId() != null && rsCharge.getServiceId().equals(serviceId)) {
                                rsOpFreeDeclare.getRsChargeList().add(rsCharge);
                            }
                        });
                        rsOpLogList.forEach(rsOpLog -> {
                            if (rsOpLog.getServiceId() != null && rsOpLog.getServiceId().equals(serviceId)) {
                                rsOpFreeDeclare.getRsOpLogList().add(rsOpLog);
                            }
                        });
                        // 收集供应商id
                        if (rsOpFreeDeclare.getRsServiceInstances() != null && rsOpFreeDeclare.getRsServiceInstances().getSupplierId() != null) {
                            rsRct.getSupplierIds().add(rsOpFreeDeclare.getRsServiceInstances().getSupplierId());
                        }
                    }
                }
                rsRct.setRsOpFreeDeclareList(rsOpFreeDeclareList);
            }
            // 清关派送
            // 代理放单
            if (serviceTypeId.equals(70L)) {
                RsOpDOAgent rsOpDOAgent = rsOpImportCustomsClearanceMapper.selectRsOpDOAgentByRctId(rctId, 70L);
                if (ObjectUtils.isNotEmpty(rsOpDOAgent)) {
                    rsOpDOAgent.setRsChargeList(new ArrayList<>());
                    rsOpDOAgent.setRsOpLogList(new ArrayList<>());
                    Long serviceId = rsOpDOAgent.getRsServiceInstances().getServiceId();
                    rsChargeList.forEach(rsCharge -> {
                        if (rsCharge.getServiceId() != null && rsCharge.getServiceId().equals(serviceId)) {
                            rsOpDOAgent.getRsChargeList().add(rsCharge);
                        }
                    });
                    rsOpLogList.forEach(rsOpLog -> {
                        if (rsOpLog.getServiceId() != null && rsOpLog.getServiceId().equals(serviceId)) {
                            rsOpDOAgent.getRsOpLogList().add(rsOpLog);
                        }
                    });
                    // 收集供应商id
                    if (rsOpDOAgent.getRsServiceInstances() != null && rsOpDOAgent.getRsServiceInstances().getSupplierId() != null) {
                        rsRct.getSupplierIds().add(rsOpDOAgent.getRsServiceInstances().getSupplierId());
                    }
                }
                rsRct.setRsOpDOAgent(rsOpDOAgent);
            }
            // 代理清关
            if (serviceTypeId.equals(71L)) {
                RsOpClearAgent rsOpClearAgent = rsOpImportCustomsClearanceMapper.selectRsOpClearAgentByRctId(rctId, 71L);
                if (ObjectUtils.isNotEmpty(rsOpClearAgent)) {
                    rsOpClearAgent.setRsChargeList(new ArrayList<>());
                    rsOpClearAgent.setRsOpLogList(new ArrayList<>());
                    Long serviceId = rsOpClearAgent.getRsServiceInstances().getServiceId();
                    rsChargeList.forEach(rsCharge -> {
                        if (rsCharge.getServiceId() != null && rsCharge.getServiceId().equals(serviceId)) {
                            rsOpClearAgent.getRsChargeList().add(rsCharge);
                        }
                    });
                    rsOpLogList.forEach(rsOpLog -> {
                        if (rsOpLog.getServiceId() != null && rsOpLog.getServiceId().equals(serviceId)) {
                            rsOpClearAgent.getRsOpLogList().add(rsOpLog);
                        }
                    });
                    // 收集供应商id
                    if (rsOpClearAgent.getRsServiceInstances() != null && rsOpClearAgent.getRsServiceInstances().getSupplierId() != null) {
                        rsRct.getSupplierIds().add(rsOpClearAgent.getRsServiceInstances().getSupplierId());
                    }
                }
                rsRct.setRsOpClearAgent(rsOpClearAgent);
            }
            // 码头与仓储 TODO
            if (serviceTypeId.equals(80L)) {
                RsOpWHS rsOpWHS = rsOpWarehouseMapper.selectRsOpWHSByRctId(rctId, 80L);
                if (ObjectUtils.isNotEmpty(rsOpWHS)) {
                    rsOpWHS.setRsChargeList(new ArrayList<>());
                    rsOpWHS.setRsOpLogList(new ArrayList<>());
                    Long serviceId = rsOpWHS.getRsServiceInstances().getServiceId();
                    rsChargeList.forEach(rsCharge -> {
                        if (rsCharge.getServiceId() != null && rsCharge.getServiceId().equals(serviceId)) {
                            rsOpWHS.getRsChargeList().add(rsCharge);
                        }
                    });
                    rsOpLogList.forEach(rsOpLog -> {
                        if (rsOpLog.getServiceId() != null && rsOpLog.getServiceId().equals(serviceId)) {
                            rsOpWHS.getRsOpLogList().add(rsOpLog);
                        }
                    });
                    // 收集供应商id
                    if (rsOpWHS.getRsServiceInstances() != null && rsOpWHS.getRsServiceInstances().getSupplierId() != null) {
                        rsRct.getSupplierIds().add(rsOpWHS.getRsServiceInstances().getSupplierId());
                    }
                }
                rsRct.setRsOpWHS(rsOpWHS);
            }
            // 扩展服务
            if (serviceTypeId.equals(90L)) {
                RsOp3rdCert rsOp3rdCert = rsOpExpandServiceMapper.selectRsOp3rdCertByRctId(rctId, 90L);
                if (ObjectUtils.isNotEmpty(rsOp3rdCert)) {
                    rsOp3rdCert.setRsChargeList(new ArrayList<>());
                    rsOp3rdCert.setRsOpLogList(new ArrayList<>());
                    Long serviceId = rsOp3rdCert.getRsServiceInstances().getServiceId();
                    rsChargeList.forEach(rsCharge -> {
                        if (rsCharge.getServiceId() != null && rsCharge.getServiceId().equals(serviceId)) {
                            rsOp3rdCert.getRsChargeList().add(rsCharge);
                        }
                    });
                    rsOpLogList.forEach(rsOpLog -> {
                        if (rsOpLog.getServiceId() != null && rsOpLog.getServiceId().equals(serviceId)) {
                            rsOp3rdCert.getRsOpLogList().add(rsOpLog);
                        }
                    });
                    // 收集供应商id
                    if (rsOp3rdCert.getRsServiceInstances() != null && rsOp3rdCert.getRsServiceInstances().getSupplierId() != null) {
                        rsRct.getSupplierIds().add(rsOp3rdCert.getRsServiceInstances().getSupplierId());
                    }
                }
                rsRct.setRsOp3rdCert(rsOp3rdCert);
            }
            if (serviceTypeId.equals(100L)) {
                RsOpINS rsOpINS = rsOpExpandServiceMapper.selectRsOpINSByRctId(rctId, 100L);
                if (ObjectUtils.isNotEmpty(rsOpINS)) {
                    rsOpINS.setRsChargeList(new ArrayList<>());
                    rsOpINS.setRsOpLogList(new ArrayList<>());
                    Long serviceId = rsOpINS.getRsServiceInstances().getServiceId();
                    rsChargeList.forEach(rsCharge -> {
                        if (rsCharge.getServiceId() != null && rsCharge.getServiceId().equals(serviceId)) {
                            rsOpINS.getRsChargeList().add(rsCharge);
                        }
                    });
                    rsOpLogList.forEach(rsOpLog -> {
                        if (rsOpLog.getServiceId() != null && rsOpLog.getServiceId().equals(serviceId)) {
                            rsOpINS.getRsOpLogList().add(rsOpLog);
                        }
                    });
                    // 收集供应商id
                    if (rsOpINS.getRsServiceInstances() != null && rsOpINS.getRsServiceInstances() != null && rsOpINS.getRsServiceInstances().getSupplierId() != null) {
                        rsRct.getSupplierIds().add(rsOpINS.getRsServiceInstances().getSupplierId());
                    }
                }
                rsRct.setRsOpINS(rsOpINS);
            }
            if (serviceTypeId.equals(101L)) {
                RsOpTrading rsOpTrading = rsOpExpandServiceMapper.selectRsOpTradingByRctId(rctId, 101L);
                if (ObjectUtils.isNotEmpty(rsOpTrading)) {
                    rsOpTrading.setRsChargeList(new ArrayList<>());
                    rsOpTrading.setRsOpLogList(new ArrayList<>());
                    Long serviceId = rsOpTrading.getRsServiceInstances().getServiceId();
                    rsChargeList.forEach(rsCharge -> {
                        if (rsCharge.getServiceId() != null && rsCharge.getServiceId().equals(serviceId)) {
                            rsOpTrading.getRsChargeList().add(rsCharge);
                        }
                    });
                    rsOpLogList.forEach(rsOpLog -> {
                        if (rsOpLog.getServiceId() != null && rsOpLog.getServiceId().equals(serviceId)) {
                            rsOpTrading.getRsOpLogList().add(rsOpLog);
                        }
                    });
                    // 收集供应商id
                    if (rsOpTrading.getRsServiceInstances() != null && rsOpTrading.getRsServiceInstances().getSupplierId() != null) {
                        rsRct.getSupplierIds().add(rsOpTrading.getRsServiceInstances().getSupplierId());
                    }
                }
                rsRct.setRsOpTrading(rsOpTrading);
            }
            if (serviceTypeId.equals(102L)) {
                RsOpFumigation rsOpFumigation = rsOpExpandServiceMapper.selectRsOpFumigationByRctId(rctId, 102L);
                if (ObjectUtils.isNotEmpty(rsOpFumigation)) {
                    rsOpFumigation.setRsChargeList(new ArrayList<>());
                    rsOpFumigation.setRsOpLogList(new ArrayList<>());
                    Long serviceId = rsOpFumigation.getRsServiceInstances().getServiceId();
                    rsChargeList.forEach(rsCharge -> {
                        if (rsCharge.getServiceId() != null && rsCharge.getServiceId().equals(serviceId)) {
                            rsOpFumigation.getRsChargeList().add(rsCharge);
                        }
                    });
                    rsOpLogList.forEach(rsOpLog -> {
                        if (rsOpLog.getServiceId() != null && rsOpLog.getServiceId().equals(serviceId)) {
                            rsOpFumigation.getRsOpLogList().add(rsOpLog);
                        }
                    });
                    // 收集供应商id
                    if (rsOpFumigation.getRsServiceInstances() != null && rsOpFumigation.getRsServiceInstances().getSupplierId() != null) {
                        rsRct.getSupplierIds().add(rsOpFumigation.getRsServiceInstances().getSupplierId());
                    }
                }
                rsRct.setRsOpFumigation(rsOpFumigation);
            }
            if (serviceTypeId.equals(103L)) {
                RsOpCO rsOpCO = rsOpExpandServiceMapper.selectRsOpCOByRctId(rctId, 103L);
                if (ObjectUtils.isNotEmpty(rsOpCO)) {
                    rsOpCO.setRsChargeList(new ArrayList<>());
                    rsOpCO.setRsOpLogList(new ArrayList<>());
                    Long serviceId = rsOpCO.getRsServiceInstances().getServiceId();
                    rsChargeList.forEach(rsCharge -> {
                        if (rsCharge.getServiceId() != null && rsCharge.getServiceId().equals(serviceId)) {
                            rsOpCO.getRsChargeList().add(rsCharge);
                        }
                    });
                    rsOpLogList.forEach(rsOpLog -> {
                        if (rsOpLog.getServiceId() != null && rsOpLog.getServiceId().equals(serviceId)) {
                            rsOpCO.getRsOpLogList().add(rsOpLog);
                        }
                    });
                    // 收集供应商id
                    if (rsOpCO.getRsServiceInstances() != null && rsOpCO.getRsServiceInstances().getSupplierId() != null) {
                        rsRct.getSupplierIds().add(rsOpCO.getRsServiceInstances().getSupplierId());
                    }
                }
                rsRct.setRsOpCO(rsOpCO);
            }
            if (serviceTypeId.equals(104L)) {
                RsOpOther rsOpOther = rsOpExpandServiceMapper.selectRsOpOtherByRctId(rctId, 104L);
                if (ObjectUtils.isNotEmpty(rsOpOther)) {
                    rsOpOther.setRsChargeList(new ArrayList<>());
                    rsOpOther.setRsOpLogList(new ArrayList<>());
                    Long serviceId = rsOpOther.getRsServiceInstances().getServiceId();
                    rsChargeList.forEach(rsCharge -> {
                        if (rsCharge.getServiceId() != null && rsCharge.getServiceId().equals(serviceId)) {
                            rsOpOther.getRsChargeList().add(rsCharge);
                        }
                    });
                    rsOpLogList.forEach(rsOpLog -> {
                        if (rsOpLog.getServiceId() != null && rsOpLog.getServiceId().equals(serviceId)) {
                            rsOpOther.getRsOpLogList().add(rsOpLog);
                        }
                    });
                    // 收集供应商id
                    if (rsOpOther.getRsServiceInstances() != null && rsOpOther.getRsServiceInstances().getSupplierId() != null) {
                        rsRct.getSupplierIds().add(rsOpOther.getRsServiceInstances().getSupplierId());
                    }
                }
                rsRct.setRsOpOther(rsOpOther);
            }
        }

        rsRct.setRsClientMessage(rsClientMessage);

        return rsRct;
    }

    /**
     * 查询未审核的订舱单
     *
     * @param rsRct 操作单
     * @return 操作单
     */
    @Override
    public List<RsRct> selectUnVerifyRsRctList(RsRct rsRct) {
        boolean search = rsRct.getPolIds() != null || rsRct.getDestinationPortIds() != null || rsRct.getLineIds() != null;
        if (search) {
            Map<String, List<?>> query = queryRctList(rsRct);
            List<List<Long>> queryList = (List<List<Long>>) query.get("list");
            List<Long> queryPolIds = (List<Long>) query.get("locationDeparture");
            List<Long> queryDestinationIds = (List<Long>) query.get("locationDestination");
            if (!queryList.isEmpty()) {
                rsRct.setRctIds(SearchUtils.getLongs(queryList));
            }
            if (!queryPolIds.isEmpty()) {
                rsRct.setPolIds(queryPolIds.toArray(new Long[0]));
            } else {
                rsRct.setPolIds(null);
            }
            if (!queryDestinationIds.isEmpty()) {
                rsRct.setDestinationPortIds(queryDestinationIds.toArray(new Long[0]));
            } else {
                rsRct.setDestinationPortIds(null);
            }
        }
        rsRct.setUserId(SecurityUtils.getUserId());
        startPage();
        return rsRctMapper.selectUnVerifyRsRctList(rsRct);
    }

    @Override
    public List<RsRct> selectUnVerifyExportRsRctList(RsRct rsRct) {
        boolean search = rsRct.getPolIds() != null || rsRct.getDestinationPortIds() != null || rsRct.getLineIds() != null;
        if (search) {
            Map<String, List<?>> query = queryRctList(rsRct);
            List<List<Long>> queryList = (List<List<Long>>) query.get("list");
            List<Long> queryPolIds = (List<Long>) query.get("locationDeparture");
            List<Long> queryDestinationIds = (List<Long>) query.get("locationDestination");
            if (!queryList.isEmpty()) {
                rsRct.setRctIds(SearchUtils.getLongs(queryList));
            }
            if (!queryPolIds.isEmpty()) {
                rsRct.setPolIds(queryPolIds.toArray(new Long[0]));
            } else {
                rsRct.setPolIds(null);
            }
            if (!queryDestinationIds.isEmpty()) {
                rsRct.setDestinationPortIds(queryDestinationIds.toArray(new Long[0]));
            } else {
                rsRct.setDestinationPortIds(null);
            }
        }
        rsRct.setUserId(SecurityUtils.getUserId());
        return rsRctMapper.selectUnVerifyRsRctList(rsRct);
    }

    @Override
    public int opNotification(Long opId) {
        return rsRctMapper.opNotification(opId);
    }

    @Override
    public int psaNotification() {
        return rsRctMapper.psaNotification();
    }


    @Override
    public List<RsRct> selectRsRctList(RsRct rsRct) {
        boolean search = rsRct.getPolIds() != null || rsRct.getDestinationPortIds() != null || rsRct.getLineIds() != null;
        if (search) {
            Map<String, List<?>> query = queryRctList(rsRct);
            List<List<Long>> queryList = (List<List<Long>>) query.get("list");
            List<Long> queryPolIds = (List<Long>) query.get("locationDeparture");
            List<Long> queryDestinationIds = (List<Long>) query.get("locationDestination");
            if (!queryList.isEmpty()) {
                rsRct.setRctIds(SearchUtils.getLongs(queryList));
            }
            if (!queryPolIds.isEmpty()) {
                rsRct.setPolIds(queryPolIds.toArray(new Long[0]));
            } else {
                rsRct.setPolIds(null);
            }
            if (!queryDestinationIds.isEmpty()) {
                rsRct.setDestinationPortIds(queryDestinationIds.toArray(new Long[0]));
            } else {
                rsRct.setDestinationPortIds(null);
            }
        }
        startPage();
        return rsRctMapper.selectRsRctList(rsRct);
    }

    /**
     * 新增操作单
     *
     * @param rsRct 操作单
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertRsRct(RsRct rsRct) {
        rsRct.setCreateTime(DateUtils.getNowDate());
        rsRct.setCreateBy(SecurityUtils.getUserId());

        rsRct.setServiceTypeIdList(StringUtils.join(rsRct.getServiceTypeIds(), ","));

        // 委托单位概要
        if (rsRct.getClientId() != null) {
            ExtCompany extCompany = extCompanyMapper.selectExtCompanyByCompanyId(rsRct.getClientId());
            rsRct.setClientSummary(extCompany.getCompanyTaxCode() + '/' + ((extCompany.getCompanyShortName() != null && !extCompany.getCompanyShortName().isEmpty()) ? extCompany.getCompanyShortName() : extCompany.getCompanyEnShortName()) + '/' + extCompany.getCompanyLocalName());
        }

        rsRctMapper.insertRsRct(rsRct);
        // 插入货物类型（普货、食品等）到中间表mid_cargo_type
        if (rsRct.getCargoTypeIds() != null && rsRct.getCargoTypeIds().length > 0) {
            insertCargoType(rsRct);
        }
        // 插入承运人（CMA、MSC等）到mid_carriers
        if (rsRct.getCarrierIds() != null && rsRct.getCarrierIds().length > 0) {
            insertCarriers(rsRct);
        }
        // 插入服务类型(海运、空运等)到mid_service_type
        if (rsRct.getServiceTypeIds() != null && rsRct.getServiceTypeIds().length > 0) {
            insertServiceType(rsRct);
        }

        // 插入提单信息列表
        List<RsBookingMessage> bookingMessagesList = rsRct.getBookingMessagesList();
        if (bookingMessagesList != null && !bookingMessagesList.isEmpty()) {
            for (RsBookingMessage rsBookingMessage : bookingMessagesList) {
                rsBookingMessageMapper.insertRsBookingMessage(rsBookingMessage);
            }
        }

        return rsRct.getRctId();
    }

    /**
     * 修改操作单
     *
     * @param rsRct 操作单
     * @return 结果
     */
    @Override
    public int updateRsRct(RsRct rsRct) {
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        definition.setPropagationBehaviorName("PROPAGATION_REQUIRED");
        TransactionStatus transaction = transactionManager.getTransaction(definition);

        if (rsRct.getRctId() == null) {
            throw new RuntimeException("更新异常");
        }
        rsRct.setUpdateTime(DateUtils.getNowDate());
        rsRct.setUpdateBy(SecurityUtils.getUserId());

        rsRct.setServiceTypeIdList(StringUtils.join(rsRct.getServiceTypeIds(), ","));

        // 委托单位概要
        if (rsRct.getClientId() != null) {
            ExtCompany extCompany = extCompanyMapper.selectExtCompanyByCompanyId(rsRct.getClientId());
            rsRct.setClientSummary(extCompany.getCompanyTaxCode() + '/' + ((extCompany.getCompanyShortName() != null && !extCompany.getCompanyShortName().isEmpty()) ? extCompany.getCompanyShortName() : extCompany.getCompanyEnShortName()) + '/' + extCompany.getCompanyLocalName());
        }

/*        // 计算费用
        if (rsRct.getQuotationRmbSumVat() != null && rsRct.getInquiryCnRmbSumVat() != null) {

        }*/

        try {
            midServiceTypeMapper.deleteMidServiceTypeById(rsRct.getRctId(), "rct");
            if (rsRct.getServiceTypeIds() != null && rsRct.getServiceTypeIds().length > 0) {
                insertServiceType(rsRct);
            }
            midCarrierMapper.deleteMidCarrierById(rsRct.getRctId(), "rct");
            midCargoTypeMapper.deleteMidCargoTypeById(rsRct.getRctId(), "rct");
            if (rsRct.getCargoTypeIds() != null && rsRct.getCargoTypeIds().length > 0) {
                insertCargoType(rsRct);
            }
            if (rsRct.getCarrierIds() != null && rsRct.getCarrierIds().length > 0) {
                insertCarriers(rsRct);
            }

            rsBookingMessageMapper.deleteRsBookingMessageByRctId(rsRct.getRctId());
            List<RsBookingMessage> bookingMessagesList = rsRct.getBookingMessagesList();
            if (bookingMessagesList != null && !bookingMessagesList.isEmpty()) {
                for (RsBookingMessage rsBookingMessage : bookingMessagesList) {
                    rsBookingMessageMapper.insertRsBookingMessage(rsBookingMessage);
                }
            }

            if (rsRct.getOpAskingBlGetTime() != null) {
                rsRct.setDocStatusA("期望赎单:" + rsRct.getOpAskingBlGetTime());
            }
            if (rsRct.getOpAskingBlReleaseTime() != null) {
                rsRct.setDocStatusA("期望放单:" + rsRct.getOpAskingBlReleaseTime());
            }
            if (rsRct.getAccPromissBlGetTime() != null) {
                rsRct.setDocStatusA("预计赎单:" + rsRct.getAccPromissBlGetTime());
            }
            if (rsRct.getActualBlGotTime() != null) {
                rsRct.setDocStatusA("提单赎回:" + rsRct.getActualBlGotTime());
            }
            if (rsRct.getAccPromissBlReleaseTime() != null) {
                rsRct.setDocStatusA("预计放单:" + rsRct.getAccPromissBlReleaseTime());
            }
            if (rsRct.getActualBlReleaseTime() != null) {
                rsRct.setDocStatusA("已放提单:" + rsRct.getActualBlReleaseTime());
            }

            if (rsRct.getAgentNoticeTime() != null) {
                rsRct.setDocStatusB("已发代理:" + rsRct.getAgentNoticeTime());
            }

            transactionManager.commit(transaction);
        } catch (Exception ex) {
            transactionManager.rollback(transaction);
            throw ex;
        }

        return rsRctMapper.updateRsRct(rsRct);
    }


    /**
     * 修改操作单状态
     *
     * @param rsRct 操作单
     * @return 操作单
     */
    @Override
    public int changeStatus(RsRct rsRct) {
        return rsRctMapper.updateRsRct(rsRct);
    }

    /**
     * 批量删除操作单
     *
     * @param rctIds 需要删除的操作单主键
     * @return 结果
     */
    @Override
    public int deleteRsRctByRctIds(Long[] rctIds) {
        return rsRctMapper.deleteRsRctByRctIds(rctIds);
    }

    /**
     * 删除操作单信息
     *
     * @param rctId 操作单主键
     * @return 结果
     */
    @Override
    public int deleteRsRctByRctId(Long rctId) {
        return rsRctMapper.deleteRsRctByRctId(rctId);
    }

    private static void resetCharge(List<RsCharge> rsChargeList) {
        for (RsCharge rsCharge : rsChargeList) {
            rsCharge.setServiceId(null);
            rsCharge.setSqdRctId(null);
            rsCharge.setSqdServiceTypeId(null);
            rsCharge.setChargeId(null);
            rsCharge.setIsAccountConfirmed("0");
            rsCharge.setAccountConfirmTime(null);
            rsCharge.setChargeRemark(null);
            rsCharge.setBankRecordId(null);
            rsCharge.setDnCurrencyPaid(BigDecimal.ZERO);
            rsCharge.setDnCurrencyBalance(rsCharge.getSubtotal());
            rsCharge.setSqdDnCurrencyBalance(rsCharge.getSubtotal());
            rsCharge.setSqdDnCurrencyPaid(BigDecimal.ZERO);
            rsCharge.setDnCurrencyReceived(BigDecimal.ZERO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveBasicLogistics(RsRct rsRct) {
        int out;
        // 关联服务实例
        RsServiceInstances rsServiceInstances = rsServiceInstancesMapper.selectRsServiceInstances(rsRct.getRctId(), "logistics");
        if (rsServiceInstances == null) {
            out = rsServiceInstancesMapper.insertRsServiceInstances(rsRct.getRsBasicLogistics().getRsServiceInstances());
        } else {
            out = rsServiceInstancesMapper.updateRsServiceInstances(rsRct.getRsBasicLogistics().getRsServiceInstances());
            rsRct.getRsBasicLogistics().getRsServiceInstances().setServiceId(rsServiceInstances.getServiceId());
        }


        // 基础物流信息
        Long serviceInstanceId = rsRct.getRsBasicLogistics().getRsServiceInstances().getServiceId();
        RsBasicLogistics rsBasicLogistics = rsBasicLogisticsMapper.selectRsBasicLogistics(serviceInstanceId);
        rsRct.getRsBasicLogistics().setServiceId(serviceInstanceId);
        rsRct.getRsBasicLogistics().setSqdRctNo(rsRct.getRctNo());
        if (rsBasicLogistics != null) {
            out += rsBasicLogisticsMapper.updateRsBasicLogistics(rsRct.getRsBasicLogistics());
        } else {
            out += rsBasicLogisticsMapper.insertRsBasicLogistics(rsRct.getRsBasicLogistics());
        }

        // 文件信息

        // 物流进度

        // 费用信息
        out += rsChargeMapper.deleteRsCharge(serviceInstanceId);
        List<RsCharge> rsCharges = rsRct.getRsBasicLogistics().getRsChargeList();
        for (RsCharge rsCharge : rsCharges) {
            rsCharge.setServiceId(serviceInstanceId);
            out += rsChargeMapper.insertRsCharge(rsCharge);
        }
        return out;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int savePreCarriage(RsRct rsRct) {
        int out;
        // 关联服务实例
        RsServiceInstances rsServiceInstances = rsServiceInstancesMapper.selectRsServiceInstances(rsRct.getRctId(), "pre-carriage");
        if (rsServiceInstances == null) {
            out = rsServiceInstancesMapper.insertRsServiceInstances(rsRct.getRsPrecarriage().getRsServiceInstances());
        } else {
            out = rsServiceInstancesMapper.updateRsServiceInstances(rsRct.getRsPrecarriage().getRsServiceInstances());
            rsRct.getRsPrecarriage().getRsServiceInstances().setServiceId(rsServiceInstances.getServiceId());
        }


        // 前程运输信息
        Long serviceInstanceId = rsRct.getRsPrecarriage().getRsServiceInstances().getServiceId();
        RsPrecarriage rsPrecarriage = rsPrecarriageMapper.selectRsPrecarriage(serviceInstanceId);
        rsRct.getRsPrecarriage().setServiceId(serviceInstanceId);
        rsRct.getRsPrecarriage().setSqdRctNo(rsRct.getRctNo());
        if (rsPrecarriage != null) {
            out += rsPrecarriageMapper.updateRsPrecarriage(rsRct.getRsPrecarriage());
        } else {
            out += rsPrecarriageMapper.insertRsPrecarriage(rsRct.getRsPrecarriage());
        }

        // 文件信息

        // 物流进度

        // 费用信息
        out += rsChargeMapper.deleteRsCharge(serviceInstanceId);
        List<RsCharge> rsCharges = rsRct.getRsPrecarriage().getRsChargeList();
        for (RsCharge rsCharge : rsCharges) {
            rsCharge.setServiceId(serviceInstanceId);
            out += rsChargeMapper.insertRsCharge(rsCharge);
        }
        return out;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveExportDeclaration(RsRct rsRct) {
        int out;
        // 关联服务实例
        RsServiceInstances rsServiceInstances = rsServiceInstancesMapper.selectRsServiceInstances(rsRct.getRctId(), "export-declaration");
        if (rsServiceInstances == null) {
            out = rsServiceInstancesMapper.insertRsServiceInstances(rsRct.getRsExportCustoms().getRsServiceInstances());
        } else {
            out = rsServiceInstancesMapper.updateRsServiceInstances(rsRct.getRsExportCustoms().getRsServiceInstances());
            rsRct.getRsExportCustoms().getRsServiceInstances().setServiceId(rsServiceInstances.getServiceId());
        }


        // 出口报关信息
        Long serviceInstanceId = rsRct.getRsExportCustoms().getRsServiceInstances().getServiceId();
        RsExportCustoms rsExportCustoms = rsExportCustomsMapper.selectRsExportCustoms(serviceInstanceId);
        rsRct.getRsExportCustoms().setServiceId(serviceInstanceId);
        rsRct.getRsExportCustoms().setSqdRctNo(rsRct.getRctNo());
        if (rsExportCustoms != null) {
            out += rsExportCustomsMapper.updateRsExportCustoms(rsRct.getRsExportCustoms());
        } else {
            out += rsExportCustomsMapper.insertRsExportCustoms(rsRct.getRsExportCustoms());
        }

        // 文件信息

        // 物流进度

        // 费用信息
        out += rsChargeMapper.deleteRsCharge(serviceInstanceId);
        List<RsCharge> rsCharges = rsRct.getRsExportCustoms().getRsChargeList();
        for (RsCharge rsCharge : rsCharges) {
            rsCharge.setServiceId(serviceInstanceId);
            out += rsChargeMapper.insertRsCharge(rsCharge);
        }
        return out;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveImportClearance(RsRct rsRct) {
        int out;
        // 关联服务实例
        RsServiceInstances rsServiceInstances = rsServiceInstancesMapper.selectRsServiceInstances(rsRct.getRctId(), "import-clearance");
        if (rsServiceInstances == null) {
            out = rsServiceInstancesMapper.insertRsServiceInstances(rsRct.getRsImportCustoms().getRsServiceInstances());
        } else {
            out = rsServiceInstancesMapper.updateRsServiceInstances(rsRct.getRsImportCustoms().getRsServiceInstances());
            rsRct.getRsImportCustoms().getRsServiceInstances().setServiceId(rsServiceInstances.getServiceId());
        }


        // 基础物流信息
        Long serviceInstanceId = rsRct.getRsImportCustoms().getRsServiceInstances().getServiceId();
        RsImportCustoms rsImportCustoms = rsImportCustomsMapper.selectRsImportCustoms(serviceInstanceId);
        rsRct.getRsImportCustoms().setServiceId(serviceInstanceId);
        rsRct.getRsImportCustoms().setSqdRctNo(rsRct.getRctNo());
        if (rsImportCustoms != null) {
            out += rsImportCustomsMapper.updateRsImportCustoms(rsRct.getRsImportCustoms());
        } else {
            out += rsImportCustomsMapper.insertRsImportCustoms(rsRct.getRsImportCustoms());
        }

        // 文件信息

        // 物流进度

        // 费用信息
        out += rsChargeMapper.deleteRsCharge(serviceInstanceId);
        List<RsCharge> rsCharges = rsRct.getRsImportCustoms().getRsChargeList();
        for (RsCharge rsCharge : rsCharges) {
            rsCharge.setServiceId(serviceInstanceId);
            out += rsChargeMapper.insertRsCharge(rsCharge);
        }
        return out;
    }

    @Override
    public int getMon() {
        return rsRctMapper.getMon();
    }

    private Map<String, List<?>> queryRctList(RsRct rsRct) {
        List<List<Long>> lists = new ArrayList<>();
        List<BasDistLocation> basDistLocations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        if (basDistLocations == null) {
            RedisCache.location();
            basDistLocations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        }
        List<BasDistLine> basDistLines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        if (basDistLines == null) {
            RedisCache.line();
            basDistLines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        }
        Set<Long> lineDestination = new HashSet<>();
        if (rsRct.getLineIds() != null) {
            for (BasDistLine line : basDistLines) {
                String[] lineAncestors = line.getAncestors().split(",");
                if (ArrayUtils.contains(rsRct.getLineIds(), line.getLineId())) {
                    for (String a : lineAncestors) {
                        lineDestination.add(Convert.toLong(a));
                    }
                }
                if (SearchUtils.existSame(lineAncestors, rsRct.getLineIds())) {
                    lineDestination.add(line.getLineId());
                }
            }
        }
        // 启运目的区域id集合
        Set<Long> locationDeparture = new HashSet<>();
        // 目的区域id集合
        Set<Long> locationDestination = new HashSet<>();
        //启运港或目的港
        if (rsRct.getPolIds() != null || rsRct.getDestinationPortIds() != null) {
            // 遍历所有的区域(3885)
            for (BasDistLocation location : basDistLocations) {
                // 当前地名的父级
                String[] ancestors = location.getAncestors().split(",");
                // 启运区域id没有包含-1(亚洲、欧洲、南美洲、北美洲、非洲、大洋洲)
                if (rsRct.getPolIds() != null && !ArrayUtils.contains(rsRct.getPolIds(), -1L)) {
                    // 当前区域的id在搜索的id集合中
                    if (ArrayUtils.contains(rsRct.getPolIds(), location.getLocationId())) {
                        locationDeparture.add(location.getLocationId());
                        for (String a : ancestors) {
                            locationDeparture.add(Convert.toLong(a));
                        }
                    }
                    // 是否存在重复
                    if (SearchUtils.existSame(ancestors, rsRct.getPolIds())) {
                        locationDeparture.add(location.getLocationId());
                    }
                }
                // 目的港不为空且不为顶级区域(亚洲、欧洲、南美洲、北美洲、非洲、大洋洲)
                if (rsRct.getDestinationPortIds() != null && !ArrayUtils.contains(rsRct.getDestinationPortIds(), -1L)) {
                    if (ArrayUtils.contains(rsRct.getDestinationPortIds(), location.getLocationId())) {
                        locationDestination.add(location.getLocationId());
                        for (String a : ancestors) {
                            locationDestination.add(Convert.toLong(a));
                        }
                    }
                    if (SearchUtils.existSame(ancestors, rsRct.getDestinationPortIds())) {
                        locationDestination.add(location.getLocationId());
                    }
                    if (lineDestination.contains(location.getLineId())) {
                        locationDestination.add(location.getLocationId());
                    }
                }
            }
        }
        HashMap<String, List<?>> out = new HashMap<>();
        out.put("list", lists);
        out.put("locationDeparture", Arrays.asList(locationDeparture.toArray()));
        out.put("locationDestination", Arrays.asList(locationDestination.toArray()));
        return out;
    }

    public void insertCarriers(RsRct rsRct) {
        Long[] roles = rsRct.getCarrierIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidCarrier> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidCarrier midCarrier = new MidCarrier();
                midCarrier.setBelongId(rsRct.getRctId());
                midCarrier.setCarrierId(r);
                midCarrier.setBelongTo("rct");
                list.add(midCarrier);
            }
            midCarrierMapper.batchCarrier(list);
        }
        RedisCache.midCarrier("rct", "rctCarriers");
    }

    public void insertCargoType(RsRct rsRct) {
        Long[] roles = rsRct.getCargoTypeIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidCargoType> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidCargoType MidCargoType = new MidCargoType();
                MidCargoType.setBelongId(rsRct.getRctId());
                MidCargoType.setBelongTo("rct");
                MidCargoType.setCargoTypeId(r);
                list.add(MidCargoType);
            }
            midCargoTypeMapper.batchCargoType(list);
        }
        RedisCache.midCargoType("rct", "rctCargoType");
    }

    public void insertServiceType(RsRct rsRct) {
        Long[] roles = rsRct.getServiceTypeIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidServiceType> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidServiceType MidServiceType = new MidServiceType();
                MidServiceType.setBelongId(rsRct.getRctId());
                MidServiceType.setServiceTypeId(r);
                MidServiceType.setBelongTo("rct");
                list.add(MidServiceType);
            }
            midServiceTypeMapper.batchServiceType(list);
        }
        RedisCache.midServiceType("rct", "rctServiceType");
    }

    @Override
    public List<Long> getCarrierIds(Long rctId) {
        return midCarrierMapper.selectMidCarrierById(rctId, "rct");
    }

    @Override
    public List<Long> getCargoTypeIds(Long rctId) {
        return midCargoTypeMapper.selectMidCargoTypeById(rctId, "rct");
    }

    @Override
    public List<Long> getServiceTypeIds(Long rctId) {
        return midServiceTypeMapper.selectMidServiceTypeById(rctId, "rct");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RsRct saveAllServices(RsRct rsRct) {
        // 关联服务实例
        List<RsServiceInstances> rsServiceInstances = rsServiceInstancesMapper.selectRsServiceInstancesByRctId(rsRct.getRctId());
        List<String> serviceNames = rsServiceInstances.stream().map(RsServiceInstances::getServiceBelongTo).collect(Collectors.toList());
        List<Long> serviceTypeIds = rsServiceInstances.stream().map(RsServiceInstances::getServiceTypeId).collect(Collectors.toList());

        List<RsOpLog> rsOpLogList = new ArrayList<>();

        rsChargeMapper.deleteRsChargeByRctId(rsRct.getRctId());

        saveClientMessage(rsRct);
        if (rsRct.getRsOpSeaFclList() != null && !rsRct.getRsOpSeaFclList().isEmpty()) {
            for (RsOpSeaFcl rsOpSeaFcl : rsRct.getRsOpSeaFclList()) {
                List<RsOpLog> rsOpLogs = rsOpSeaFcl.getRsOpLogList();
                if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                    rsOpLogList.addAll(rsOpLogs);
                }
            }
            saveSeaFcl(rsRct);
        }

        if (rsRct.getRsOpSeaLclList() != null && !rsRct.getRsOpSeaLclList().isEmpty()) {
            for (RsOpSeaLcl rsOpSeaLcl : rsRct.getRsOpSeaLclList()) {
                List<RsOpLog> rsOpLogs = rsOpSeaLcl.getRsOpLogList();
                if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                    rsOpLogList.addAll(rsOpLogs);
                }
            }
            saveSeaLcl(rsRct);
        }

        if (rsRct.getRsOpAirList() != null && !rsRct.getRsOpAirList().isEmpty()) {
            for (RsOpAir rsOpAir : rsRct.getRsOpAirList()) {
                List<RsOpLog> rsOpLogs = rsOpAir.getRsOpLogList();
                if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                    rsOpLogList.addAll(rsOpLogs);
                }
                saveAir(rsRct);
            }
        }
        if (rsRct.getRsOpRailFCL() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpRailFCL().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            saveRailFCL(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpRailLCL() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpRailLCL().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            saveRailLCL(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpExpress() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpExpress().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            saveExpress(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpCtnrTruckList() != null && !rsRct.getRsOpCtnrTruckList().isEmpty()) {
            for (RsOpCtnrTruck rsOpCtnrTruck : rsRct.getRsOpCtnrTruckList()) {
                List<RsOpLog> rsOpLogs = rsOpCtnrTruck.getRsOpLogList();
                if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                    rsOpLogList.addAll(rsOpLogs);
                }
                saveCtnrTruck(rsRct);
            }
        }
        if (rsRct.getRsOpBulkTruckList() != null && !rsRct.getRsOpBulkTruckList().isEmpty()) {
            for (RsOpBulkTruck rsOpBulkTruck : rsRct.getRsOpBulkTruckList()) {
                List<RsOpLog> rsOpLogs = rsOpBulkTruck.getRsOpLogList();
                if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                    rsOpLogList.addAll(rsOpLogs);
                }
                saveBulkTruck(rsRct);
            }
        }
        if (rsRct.getRsOpFreeDeclareList() != null && !rsRct.getRsOpFreeDeclareList().isEmpty()) {
            for (RsOpFreeDeclare rsOpFreeDeclare : rsRct.getRsOpFreeDeclareList()) {
                List<RsOpLog> rsOpLogs = rsOpFreeDeclare.getRsOpLogList();
                if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                    rsOpLogList.addAll(rsOpLogs);
                }
                saveFreeDeclare(rsRct);
            }
        }
        if (rsRct.getRsOpDocDeclareList() != null && !rsRct.getRsOpDocDeclareList().isEmpty()) {
            for (RsOpDocDeclare rsOpDocDeclare : rsRct.getRsOpDocDeclareList()) {
                List<RsOpLog> rsOpLogs = rsOpDocDeclare.getRsOpLogList();
                if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                    rsOpLogList.addAll(rsOpLogs);
                }
                saveDocDeclare(rsRct);
            }
        }
        if (rsRct.getRsOpDOAgent() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpDOAgent().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            saveDOAgent(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpClearAgent() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpClearAgent().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            saveClearAgent(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpWHS() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpWHS().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            saveWHS(rsRct, serviceNames, serviceTypeIds);
        }

        // 拓展服务
        if (rsRct.getRsOp3rdCert() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOp3rdCert().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            save3rdCert(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpINS() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpINS().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            saveINS(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpTrading() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpTrading().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            saveTrading(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpFumigation() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpFumigation().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            saveFumigation(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpCO() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpCO().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            saveCO(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpOther() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpOther().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            saveOther(rsRct, serviceNames, serviceTypeIds);
        }
        return rsRct;
    }

    public String getProcessStatus(Long processStatusId) {
        if (processStatusId.equals(1L)) {
            return "通过";
        }
        if (processStatusId.equals(2L)) {
            return "进行";
        }
        if (processStatusId.equals(4L)) {
            return "异常";
        }
        if (processStatusId.equals(6L)) {
            return "确认";
        }
        if (processStatusId.equals(7L)) {
            return "完成";
        }
        if (processStatusId.equals(8L)) {
            return "取消";
        }
        if (processStatusId.equals(9L)) {
            return "驳回";
        }
        return "";
    }

    @Override
    public List<RsRct> selectRsRctByCompanyId(Long companyId) {
        return rsRctMapper.selectRsRctByCompanyId(companyId);
    }

    @Override
    public int getCFMon() {
        return rsRctMapper.getCFMon();
    }

    @Override
    public Long getRctIdByRctNo(String rctNo) {
        return rsRctMapper.selectRctIdByRctNo(rctNo);
    }

    @Override
    public List<StatisticsOpDTO> statisticsOp() {
        return rsRctMapper.statisticsOp();
    }

    @Override
    public List<RsRct> selectRsRctListToExport(RsRct rsRct) {
        boolean search = rsRct.getPolIds() != null || rsRct.getDestinationPortIds() != null || rsRct.getLineIds() != null;
        if (search) {
            Map<String, List<?>> query = queryRctList(rsRct);
            List<List<Long>> queryList = (List<List<Long>>) query.get("list");
            List<Long> queryPolIds = (List<Long>) query.get("locationDeparture");
            List<Long> queryDestinationIds = (List<Long>) query.get("locationDestination");
            if (!queryList.isEmpty()) {
                rsRct.setRctIds(SearchUtils.getLongs(queryList));
            }
            if (!queryPolIds.isEmpty()) {
                rsRct.setPolIds(queryPolIds.toArray(new Long[0]));
            } else {
                rsRct.setPolIds(null);
            }
            if (!queryDestinationIds.isEmpty()) {
                rsRct.setDestinationPortIds(queryDestinationIds.toArray(new Long[0]));
            } else {
                rsRct.setDestinationPortIds(null);
            }
        }
        return rsRctMapper.selectRsRctList(rsRct);
    }

    @Override
    public int getRSWHMon() {
        return rsRctMapper.getRSWHMon();
    }

    /**
     * 第三方证书
     *
     * @param rsRct
     * @param serviceNames
     * @param serviceTypeIds
     */
    @Transactional(rollbackFor = Exception.class)
    public void save3rdCert(RsRct rsRct, List<String> serviceNames, List<Long> serviceTypeIds) {
        RsServiceInstances rsServiceInstance;
        if (ObjectUtils.isNotEmpty(rsRct.getRsOp3rdCert().getRsServiceInstances())) {
            rsServiceInstance = rsRct.getRsOp3rdCert().getRsServiceInstances();
        } else {
            rsServiceInstance = new RsServiceInstances();
        }
        Long rctId = rsRct.getRctId();

        if (serviceNames.contains("Cert") && serviceTypeIds.contains(90L)) {
            // 更新服务实例
            rsServiceInstancesMapper.updateRsServiceInstances(rsServiceInstance);

            // 更新服务
            rsRct.getRsOp3rdCert().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOp3rdCert().setSqdServiceTypeId(90L);
            rsOpExpandServiceMapper.updateRsOpExpandService(rsRct.getRsOp3rdCert());

            // 更新费用
            updateFreight(rsServiceInstance, rsRct.getRsOp3rdCert().getRsChargeList(), rctId, 90L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOp3rdCert().getRsOpLogList(), rctId, 90L);
        } else {
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("Cert");
            rsServiceInstance.setServiceTypeId(90L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.insertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsRct.getRsOp3rdCert().setServiceId(rsServiceInstance.getServiceId());
            rsRct.getRsOp3rdCert().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOp3rdCert().setSqdServiceTypeId(90L);
            rsOpExpandServiceMapper.insertRsOpExpandService(rsRct.getRsOp3rdCert());

            // 插入费用
            updateFreight(rsServiceInstance, rsRct.getRsOp3rdCert().getRsChargeList(), rctId, 90L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOp3rdCert().getRsOpLogList(), rctId, 90L);
        }
    }

    /**
     * 保险
     *
     * @param rsRct
     * @param serviceNames
     * @param serviceTypeIds
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveINS(RsRct rsRct, List<String> serviceNames, List<Long> serviceTypeIds) {
        RsServiceInstances rsServiceInstance;
        if (ObjectUtils.isNotEmpty(rsRct.getRsOpINS().getRsServiceInstances())) {
            rsServiceInstance = rsRct.getRsOpINS().getRsServiceInstances();
        } else {
            rsServiceInstance = new RsServiceInstances();
        }
        Long rctId = rsRct.getRctId();

        if (serviceNames.contains("INS") && serviceTypeIds.contains(100L)) {
            // 更新服务实例
            rsServiceInstancesMapper.updateRsServiceInstances(rsServiceInstance);

            // 更新服务
            rsRct.getRsOpINS().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpINS().setSqdServiceTypeId(100L);
            rsOpExpandServiceMapper.updateRsOpExpandService(rsRct.getRsOpINS());

            // 更新费用
            updateFreight(rsServiceInstance, rsRct.getRsOpINS().getRsChargeList(), rctId, 100L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpINS().getRsOpLogList(), rctId, 100L);
        } else {
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("INS");
            rsServiceInstance.setServiceTypeId(100L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.insertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsRct.getRsOpINS().setServiceId(rsServiceInstance.getServiceId());
            rsRct.getRsOpINS().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpINS().setSqdServiceTypeId(100L);
            rsOpExpandServiceMapper.insertRsOpExpandService(rsRct.getRsOpINS());

            // 插入费用
            updateFreight(rsServiceInstance, rsRct.getRsOpINS().getRsChargeList(), rctId, 100L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpINS().getRsOpLogList(), rctId, 100L);
        }
    }

    /**
     * 保存检验与证书服务信息
     *
     * @param rsRct
     * @param serviceNames
     * @param serviceTypeIds
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveTrading(RsRct rsRct, List<String> serviceNames, List<Long> serviceTypeIds) {
        RsServiceInstances rsServiceInstance;
        if (ObjectUtils.isNotEmpty(rsRct.getRsOpTrading().getRsServiceInstances())) {
            rsServiceInstance = rsRct.getRsOpTrading().getRsServiceInstances();
        } else {
            rsServiceInstance = new RsServiceInstances();
        }
        Long rctId = rsRct.getRctId();

        if (serviceNames.contains("Trading") && serviceTypeIds.contains(101L)) {
            // 更新服务实例
            rsServiceInstancesMapper.updateRsServiceInstances(rsServiceInstance);

            // 更新服务
            rsRct.getRsOpTrading().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpTrading().setSqdServiceTypeId(101L);
            rsOpExpandServiceMapper.updateRsOpExpandService(rsRct.getRsOpTrading());

            // 更新费用
            updateFreight(rsServiceInstance, rsRct.getRsOpTrading().getRsChargeList(), rctId, 101L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpTrading().getRsOpLogList(), rctId, 101L);
        } else {
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("Trading");
            rsServiceInstance.setServiceTypeId(101L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.insertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsRct.getRsOpTrading().setServiceId(rsServiceInstance.getServiceId());
            rsRct.getRsOpTrading().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpTrading().setSqdServiceTypeId(101L);
            rsOpExpandServiceMapper.insertRsOpExpandService(rsRct.getRsOpTrading());

            // 插入费用
            updateFreight(rsServiceInstance, rsRct.getRsOpTrading().getRsChargeList(), rctId, 101L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpTrading().getRsOpLogList(), rctId, 101L);
        }
    }

    /**
     * 保存检验与证书服务信息
     *
     * @param rsRct
     * @param serviceNames
     * @param serviceTypeIds
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveFumigation(RsRct rsRct, List<String> serviceNames, List<Long> serviceTypeIds) {
        RsServiceInstances rsServiceInstance;
        if (ObjectUtils.isNotEmpty(rsRct.getRsOpFumigation().getRsServiceInstances())) {
            rsServiceInstance = rsRct.getRsOpFumigation().getRsServiceInstances();
        } else {
            rsServiceInstance = new RsServiceInstances();
        }
        Long rctId = rsRct.getRctId();

        if (serviceNames.contains("Fumigation") && serviceTypeIds.contains(102L)) {
            // 更新服务实例
            rsServiceInstancesMapper.updateRsServiceInstances(rsServiceInstance);

            // 更新服务
            rsRct.getRsOpFumigation().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpFumigation().setSqdServiceTypeId(102L);
            rsOpExpandServiceMapper.updateRsOpExpandService(rsRct.getRsOpFumigation());

            // 更新费用
            updateFreight(rsServiceInstance, rsRct.getRsOpFumigation().getRsChargeList(), rctId, 102L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpFumigation().getRsOpLogList(), rctId, 102L);
        } else {
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("Fumigation");
            rsServiceInstance.setServiceTypeId(102L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.insertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsRct.getRsOpFumigation().setServiceId(rsServiceInstance.getServiceId());
            rsRct.getRsOpFumigation().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpFumigation().setSqdServiceTypeId(102L);
            rsOpExpandServiceMapper.insertRsOpExpandService(rsRct.getRsOpFumigation());

            // 插入费用
            updateFreight(rsServiceInstance, rsRct.getRsOpFumigation().getRsChargeList(), rctId, 102L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpFumigation().getRsOpLogList(), rctId, 102L);
        }
    }

    /**
     * 保存检验与证书服务信息
     *
     * @param rsRct
     * @param serviceNames
     * @param serviceTypeIds
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveCO(RsRct rsRct, List<String> serviceNames, List<Long> serviceTypeIds) {
        RsServiceInstances rsServiceInstance;
        if (ObjectUtils.isNotEmpty(rsRct.getRsOpCO().getRsServiceInstances())) {
            rsServiceInstance = rsRct.getRsOpCO().getRsServiceInstances();
        } else {
            rsServiceInstance = new RsServiceInstances();
        }
        Long rctId = rsRct.getRctId();

        if (serviceNames.contains("CO") && serviceTypeIds.contains(103L)) {
            // 更新服务实例
            rsServiceInstancesMapper.updateRsServiceInstances(rsServiceInstance);

            // 更新服务
            rsRct.getRsOpCO().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpCO().setSqdServiceTypeId(103L);
            rsOpExpandServiceMapper.updateRsOpExpandService(rsRct.getRsOpCO());

            // 更新费用
            updateFreight(rsServiceInstance, rsRct.getRsOpCO().getRsChargeList(), rctId, 103L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpCO().getRsOpLogList(), rctId, 103L);
        } else {
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("CO");
            rsServiceInstance.setServiceTypeId(103L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.insertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsRct.getRsOpCO().setServiceId(rsServiceInstance.getServiceId());
            rsRct.getRsOpCO().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpCO().setSqdServiceTypeId(103L);
            rsOpExpandServiceMapper.insertRsOpExpandService(rsRct.getRsOpCO());

            // 插入费用
            updateFreight(rsServiceInstance, rsRct.getRsOpCO().getRsChargeList(), rctId, 103L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpCO().getRsOpLogList(), rctId, 103L);
        }
    }


    /**
     * 保存其他服务信息
     *
     * @param rsRct
     * @param serviceNames
     * @param serviceTypeIds
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOther(RsRct rsRct, List<String> serviceNames, List<Long> serviceTypeIds) {
        RsServiceInstances rsServiceInstance;
        if (ObjectUtils.isNotEmpty(rsRct.getRsOpOther().getRsServiceInstances())) {
            rsServiceInstance = rsRct.getRsOpOther().getRsServiceInstances();
        } else {
            rsServiceInstance = new RsServiceInstances();
        }
        Long rctId = rsRct.getRctId();

        if (serviceNames.contains("报销") && serviceTypeIds.contains(104L)) {
            // 更新服务实例
            rsServiceInstancesMapper.updateRsServiceInstances(rsServiceInstance);

            // 更新服务
            rsRct.getRsOpOther().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpOther().setSqdServiceTypeId(104L);
            rsOpExpandServiceMapper.updateRsOpExpandService(rsRct.getRsOpOther());

            // 更新费用
            updateFreight(rsServiceInstance, rsRct.getRsOpOther().getRsChargeList(), rctId, 104L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpOther().getRsOpLogList(), rctId, 104L);
        } else {
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("报销");
            rsServiceInstance.setServiceTypeId(104L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsRct.getRsOpOther().setServiceId(rsServiceInstance.getServiceId());
            rsRct.getRsOpOther().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpOther().setSqdServiceTypeId(104L);
            rsOpExpandServiceMapper.insertRsOpExpandService(rsRct.getRsOpOther());

            // 插入费用
            updateFreight(rsServiceInstance, rsRct.getRsOpOther().getRsChargeList(), rctId, 104L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpOther().getRsOpLogList(), rctId, 104L);
        }
    }


    /**
     * 保存清关服务信息
     *
     * @param rsRct
     * @param serviceNames
     * @param serviceTypeIds
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveDOAgent(RsRct rsRct, List<String> serviceNames, List<Long> serviceTypeIds) {
        RsServiceInstances rsServiceInstance;
        if (ObjectUtils.isNotEmpty(rsRct.getRsOpDOAgent().getRsServiceInstances())) {
            rsServiceInstance = rsRct.getRsOpDOAgent().getRsServiceInstances();
        } else {
            rsServiceInstance = new RsServiceInstances();
        }
        Long rctId = rsRct.getRctId();

        if (serviceNames.contains("DOAgent") && serviceTypeIds.contains(70L)) {
            // 更新服务实例
            rsServiceInstancesMapper.updateRsServiceInstances(rsServiceInstance);

            // 更新服务
            rsRct.getRsOpDOAgent().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpDOAgent().setSqdServiceTypeId(70L);
            rsOpImportCustomsClearanceMapper.updateRsOpImportCustomsClearance(rsRct.getRsOpDOAgent());

            // 更新费用
            updateFreight(rsServiceInstance, rsRct.getRsOpDOAgent().getRsChargeList(), rctId, 70L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpDOAgent().getRsOpLogList(), rctId, 70L);
        } else {
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("DOAgent");
            rsServiceInstance.setServiceTypeId(70L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.insertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsRct.getRsOpDOAgent().setServiceId(rsServiceInstance.getServiceId());
            rsRct.getRsOpDOAgent().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpDOAgent().setSqdServiceTypeId(70L);
            rsOpImportCustomsClearanceMapper.insertRsOpImportCustomsClearance(rsRct.getRsOpDOAgent());

            // 插入费用
            updateFreight(rsServiceInstance, rsRct.getRsOpDOAgent().getRsChargeList(), rctId, 70L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpDOAgent().getRsOpLogList(), rctId, 70L);
        }
    }

    /**
     * 保存清关服务信息
     *
     * @param rsRct
     * @param serviceNames
     * @param serviceTypeIds
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveClearAgent(RsRct rsRct, List<String> serviceNames, List<Long> serviceTypeIds) {
        RsServiceInstances rsServiceInstance;
        if (ObjectUtils.isNotEmpty(rsRct.getRsOpClearAgent().getRsServiceInstances())) {
            rsServiceInstance = rsRct.getRsOpClearAgent().getRsServiceInstances();
        } else {
            rsServiceInstance = new RsServiceInstances();
        }
        Long rctId = rsRct.getRctId();

        if (serviceNames.contains("ClearAgent") && serviceTypeIds.contains(71L)) {
            // 更新服务实例
            rsServiceInstancesMapper.updateRsServiceInstances(rsServiceInstance);

            // 更新服务
            rsRct.getRsOpClearAgent().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpClearAgent().setSqdServiceTypeId(71L);
            rsOpImportCustomsClearanceMapper.updateRsOpImportCustomsClearance(rsRct.getRsOpClearAgent());

            // 更新费用
            updateFreight(rsServiceInstance, rsRct.getRsOpClearAgent().getRsChargeList(), rctId, 71L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpClearAgent().getRsOpLogList(), rctId, 71L);
        } else {
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("ClearAgent");
            rsServiceInstance.setServiceTypeId(71L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsRct.getRsOpClearAgent().setServiceId(rsServiceInstance.getServiceId());
            rsRct.getRsOpClearAgent().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpClearAgent().setSqdServiceTypeId(71L);
            rsOpImportCustomsClearanceMapper.insertRsOpImportCustomsClearance(rsRct.getRsOpClearAgent());

            // 插入费用
            updateFreight(rsServiceInstance, rsRct.getRsOpClearAgent().getRsChargeList(), rctId, 71L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpClearAgent().getRsOpLogList(), rctId, 71L);
        }
    }

    /**
     * 报关服务信息
     *
     * @param rsRct
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveDocDeclare(RsRct rsRct) {
        Long rctId = rsRct.getRctId();

        List<RsOpDocDeclare> existingList = rsOpExportCustomsClearanceMapper.selectRsOpDocDeclareByRctId(rctId, 60L);
        List<RsOpDocDeclare> newList = rsRct.getRsOpDocDeclareList();

        List<RsOpDocDeclare> toDeleteServiceObject = existingList.stream()
                .filter(existing -> newList.stream()
                        .noneMatch(newItem -> newItem.getExportCustomsClearanceId() != null &&
                                newItem.getExportCustomsClearanceId().equals(existing.getExportCustomsClearanceId())))
                .collect(Collectors.toList());

        if (!toDeleteServiceObject.isEmpty()) {
            for (RsOpDocDeclare rsOpDocDeclare : toDeleteServiceObject) {
                rsOpExportCustomsClearanceMapper.deleteRsOpExportCustomsClearanceByExportCustomsClearanceId(rsOpDocDeclare.getExportCustomsClearanceId());
                rsServiceInstancesMapper.deleteRsServiceInstancesByServiceId(rsOpDocDeclare.getRsServiceInstances().getServiceId());
                if (rsOpDocDeclare.getRsChargeList() != null && !rsOpDocDeclare.getRsChargeList().isEmpty()) {
                    for (RsCharge rsCharge : rsOpDocDeclare.getRsChargeList()) {
                        rsChargeMapper.deleteRsChargeByChargeId(rsCharge.getChargeId());
                    }
                }
            }
        }

        for (RsOpDocDeclare rsOpDocDeclare : rsRct.getRsOpDocDeclareList()) {
            RsServiceInstances rsServiceInstance = rsOpDocDeclare.getRsServiceInstances();

            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("DocDeclare");
            rsServiceInstance.setServiceTypeId(60L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsOpDocDeclare.setServiceId(rsServiceInstance.getServiceId());
            rsOpDocDeclare.setSqdRctNo(rsRct.getRctNo());
            rsOpDocDeclare.setSqdServiceTypeId(60L);
            rsOpExportCustomsClearanceMapper.upsertRsOpExportCustomsClearance(rsOpDocDeclare);

            // 插入费用
            updateFreight(rsServiceInstance, rsOpDocDeclare.getRsChargeList(), rctId, 60L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsOpDocDeclare.getRsOpLogList(), rctId, 60L);

        }
    }

    /**
     * 报关服务信息
     *
     * @param rsRct
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveFreeDeclare(RsRct rsRct) {
        Long rctId = rsRct.getRctId();

        List<RsOpFreeDeclare> existingList = rsOpExportCustomsClearanceMapper.selectRsOpFreeDeclareByRctId(rctId, 61L);
        List<RsOpFreeDeclare> newList = rsRct.getRsOpFreeDeclareList();

        List<RsOpFreeDeclare> toDeleteServiceObject = existingList.stream()
                .filter(existing -> newList.stream()
                        .noneMatch(newItem -> newItem.getExportCustomsClearanceId() != null &&
                                newItem.getExportCustomsClearanceId().equals(existing.getExportCustomsClearanceId())))
                .collect(Collectors.toList());

        if (!toDeleteServiceObject.isEmpty()) {
            for (RsOpFreeDeclare rsOpFreeDeclare : toDeleteServiceObject) {
                rsOpExportCustomsClearanceMapper.deleteRsOpExportCustomsClearanceByExportCustomsClearanceId(rsOpFreeDeclare.getExportCustomsClearanceId());
                rsServiceInstancesMapper.deleteRsServiceInstancesByServiceId(rsOpFreeDeclare.getRsServiceInstances().getServiceId());
                if (rsOpFreeDeclare.getRsChargeList() != null && !rsOpFreeDeclare.getRsChargeList().isEmpty()) {
                    for (RsCharge rsCharge : rsOpFreeDeclare.getRsChargeList()) {
                        rsChargeMapper.deleteRsChargeByChargeId(rsCharge.getChargeId());
                    }
                }
            }
        }

        for (RsOpFreeDeclare rsOpFreeDeclare : rsRct.getRsOpFreeDeclareList()) {
            RsServiceInstances rsServiceInstance = rsOpFreeDeclare.getRsServiceInstances();

            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("FreeDeclare");
            rsServiceInstance.setServiceTypeId(61L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsOpFreeDeclare.setServiceId(rsServiceInstance.getServiceId());
            rsOpFreeDeclare.setSqdRctNo(rsRct.getRctNo());
            rsOpFreeDeclare.setSqdServiceTypeId(61L);
            rsOpExportCustomsClearanceMapper.upsertRsOpExportCustomsClearance(rsOpFreeDeclare);

            // 插入费用
            updateFreight(rsServiceInstance, rsOpFreeDeclare.getRsChargeList(), rctId, 61L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsOpFreeDeclare.getRsOpLogList(), rctId, 61L);

        }

    }

    /**
     * 保存拖车服务信息
     *
     * @param rsRct
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveCtnrTruck(RsRct rsRct) {
        Long rctId = rsRct.getRctId();

        List<RsOpCtnrTruck> existingList = rsOpTruckMapper.selectRsOpCtnrTruckByRctId(rctId, 50L);
        List<RsOpCtnrTruck> newList = rsRct.getRsOpCtnrTruckList();

        List<RsOpCtnrTruck> toDeleteServiceObject = existingList.stream()
                .filter(existing -> newList.stream()
                        .noneMatch(newItem -> newItem.getTruckId() != null &&
                                newItem.getTruckId().equals(existing.getTruckId())))
                .collect(Collectors.toList());

        if (!toDeleteServiceObject.isEmpty()) {
            for (RsOpCtnrTruck rsOpCtnrTruck : toDeleteServiceObject) {
                rsOpTruckMapper.deleteRsOpTruckByTruckId(rsOpCtnrTruck.getTruckId());
                rsServiceInstancesMapper.deleteRsServiceInstancesByServiceId(rsOpCtnrTruck.getRsServiceInstances().getServiceId());
                if (rsOpCtnrTruck.getRsChargeList() != null && !rsOpCtnrTruck.getRsChargeList().isEmpty()) {
                    for (RsCharge rsCharge : rsOpCtnrTruck.getRsChargeList()) {
                        rsChargeMapper.deleteRsChargeByChargeId(rsCharge.getChargeId());
                    }
                }
            }
        }
        rsOpTruckListMapper.deleteRsOpTruckListByRctNo(rsRct.getRctNo());

        for (RsOpCtnrTruck rsOpCtnrTruck : rsRct.getRsOpCtnrTruckList()) {
            RsServiceInstances rsServiceInstance = rsOpCtnrTruck.getRsServiceInstances();

            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("CtnrTruck");
            rsServiceInstance.setServiceTypeId(50L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsOpCtnrTruck.setServiceId(rsServiceInstance.getServiceId());
            rsOpCtnrTruck.setSqdRctNo(rsRct.getRctNo());
            rsOpCtnrTruck.setRctId(rctId);
            rsOpCtnrTruck.setSqdServiceTypeId(50L);
            rsOpTruckMapper.upsertRsOpCtnrTruck(rsOpCtnrTruck);

            List<RsOpTruckList> rsOpTruckList = rsOpCtnrTruck.getRsOpTruckList();
            for (RsOpTruckList opTruckListItem : rsOpTruckList) {
                opTruckListItem.setServiceId(rsServiceInstance.getServiceId());
                opTruckListItem.setSqdRctNo(rsRct.getRctNo());
                opTruckListItem.setSqdServiceTypeId(50L);
                rsOpTruckListMapper.insertRsOpTruckList(opTruckListItem);
            }

            // 插入费用
            updateFreight(rsServiceInstance, rsOpCtnrTruck.getRsChargeList(), rctId, 50L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsOpCtnrTruck.getRsOpLogList(), rctId, 50L);

        }

    }

    /**
     * 保存拖车服务信息
     *
     * @param rsRct
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveBulkTruck(RsRct rsRct) {
        Long rctId = rsRct.getRctId();

        List<RsOpBulkTruck> existingList = rsOpTruckMapper.selectRsOpBulkTruckByRctId(rctId, 51L);
        List<RsOpBulkTruck> newList = rsRct.getRsOpBulkTruckList();

        List<RsOpBulkTruck> toDeleteServiceObject = existingList.stream()
                .filter(existing -> newList.stream()
                        .noneMatch(newItem -> newItem.getTruckId() != null &&
                                newItem.getTruckId().equals(existing.getTruckId())))
                .collect(Collectors.toList());

        if (!toDeleteServiceObject.isEmpty()) {
            for (RsOpBulkTruck rsOpBulkTruck : toDeleteServiceObject) {
                rsOpTruckMapper.deleteRsOpTruckByTruckId(rsOpBulkTruck.getTruckId());
                rsServiceInstancesMapper.deleteRsServiceInstancesByServiceId(rsOpBulkTruck.getRsServiceInstances().getServiceId());
                if (rsOpBulkTruck.getRsChargeList() != null && !rsOpBulkTruck.getRsChargeList().isEmpty())
                    for (RsCharge rsCharge : rsOpBulkTruck.getRsChargeList()) {
                        rsChargeMapper.deleteRsChargeByChargeId(rsCharge.getChargeId());
                    }
            }
        }
        rsOpTruckListMapper.deleteRsOpTruckListByRctNo(rsRct.getRctNo());

        for (RsOpBulkTruck rsOpBulkTruck : rsRct.getRsOpBulkTruckList()) {
            RsServiceInstances rsServiceInstance = rsOpBulkTruck.getRsServiceInstances();

            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("BulkTruck");
            rsServiceInstance.setServiceTypeId(51L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsOpBulkTruck.setServiceId(rsServiceInstance.getServiceId());
            rsOpBulkTruck.setSqdRctNo(rsRct.getRctNo());
            rsOpBulkTruck.setSqdServiceTypeId(51L);
            rsOpBulkTruck.setRctId(rctId);
            rsOpTruckMapper.upsertRsOpBulkTruck(rsOpBulkTruck);

            List<RsOpTruckList> rsOpTruckLists = rsOpBulkTruck.getRsOpTruckList();
            for (RsOpTruckList rsOpTruckListItem : rsOpTruckLists) {
                rsOpTruckListItem.setServiceId(rsServiceInstance.getServiceId());
                rsOpTruckListItem.setSqdRctNo(rsRct.getRctNo());
                rsOpTruckListItem.setSqdServiceTypeId(51L);
                rsOpTruckListMapper.insertRsOpTruckList(rsOpTruckListItem);
            }

            // 插入费用
            updateFreight(rsServiceInstance, rsOpBulkTruck.getRsChargeList(), rctId, 51L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsOpBulkTruck.getRsOpLogList(), rctId, 51L);
        }
    }

    /**
     * 保存码头与仓储服务信息
     *
     * @param rsRct
     * @param serviceNames
     * @param serviceTypeIds
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveWHS(RsRct rsRct, List<String> serviceNames, List<Long> serviceTypeIds) {
        RsServiceInstances rsServiceInstance;
        if (ObjectUtils.isNotEmpty(rsRct.getRsOpWHS().getRsServiceInstances())) {
            rsServiceInstance = rsRct.getRsOpWHS().getRsServiceInstances();
        } else {
            rsServiceInstance = new RsServiceInstances();
        }
        Long rctId = rsRct.getRctId();

        if (serviceNames.contains("WHS") && serviceTypeIds.contains(80L)) {
            // 更新服务实例
            rsServiceInstancesMapper.updateRsServiceInstances(rsServiceInstance);

            // 更新服务
            rsRct.getRsOpWHS().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpWHS().setSqdServiceTypeId(80L);
            rsOpWarehouseMapper.updateRsOpWarehouse(rsRct.getRsOpWHS());

            // 更新费用
            updateFreight(rsServiceInstance, rsRct.getRsOpWHS().getRsChargeList(), rctId, 80L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpWHS().getRsOpLogList(), rctId, 80L);
        } else {
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("WHS");
            rsServiceInstance.setServiceTypeId(80L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsRct.getRsOpWHS().setServiceId(rsServiceInstance.getServiceId());
            rsRct.getRsOpWHS().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpWHS().setSqdServiceTypeId(80L);
            rsOpWarehouseMapper.insertRsOpWarehouse(rsRct.getRsOpWHS());

            // 插入费用
            updateFreight(rsServiceInstance, rsRct.getRsOpWHS().getRsChargeList(), rctId, 80L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpWHS().getRsOpLogList(), rctId, 80L);
        }
    }

    /**
     * 保存快递服务信息
     *
     * @param rsRct
     * @param serviceNames
     * @param serviceTypeIds
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveExpress(RsRct rsRct, List<String> serviceNames, List<Long> serviceTypeIds) {
        RsServiceInstances rsServiceInstance;
        if (ObjectUtils.isNotEmpty(rsRct.getRsOpExpress().getRsServiceInstances())) {
            rsServiceInstance = rsRct.getRsOpExpress().getRsServiceInstances();
        } else {
            rsServiceInstance = new RsServiceInstances();
        }
        Long rctId = rsRct.getRctId();

        if (serviceNames.contains("EXPRESS") && serviceTypeIds.contains(40L)) {
            // 更新服务实例
            rsServiceInstancesMapper.updateRsServiceInstances(rsServiceInstance);

            // 更新服务
            rsRct.getRsOpExpress().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpExpress().setSqdServiceTypeId(40L);
            rsOpExpressMapper.updateRsOpExpress(rsRct.getRsOpExpress());

            // 更新费用
            updateFreight(rsServiceInstance, rsRct.getRsOpExpress().getRsChargeList(), rctId, 40L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpExpress().getRsOpLogList(), rctId, 40L);
        } else {
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("EXPRESS");
            rsServiceInstance.setServiceTypeId(40L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsRct.getRsOpExpress().setServiceId(rsServiceInstance.getServiceId());
            rsRct.getRsOpExpress().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpExpress().setSqdServiceTypeId(40L);
            rsOpExpressMapper.insertRsOpExpress(rsRct.getRsOpExpress());

            // 插入费用
            updateFreight(rsServiceInstance, rsRct.getRsOpExpress().getRsChargeList(), rctId, 40L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpExpress().getRsOpLogList(), rctId, 40L);
        }
    }

    /**
     * 保存铁路服务信息
     *
     * @param rsRct
     * @param serviceNames
     * @param serviceTypeIds
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveRailFCL(RsRct rsRct, List<String> serviceNames, List<Long> serviceTypeIds) {
        RsServiceInstances rsServiceInstance;
        if (ObjectUtils.isNotEmpty(rsRct.getRsOpRailFCL().getRsServiceInstances())) {
            rsServiceInstance = rsRct.getRsOpRailFCL().getRsServiceInstances();
        } else {
            rsServiceInstance = new RsServiceInstances();
        }
        Long rctId = rsRct.getRctId();

        if (serviceNames.contains("RailFCL") && serviceTypeIds.contains(20L)) {
            // 更新服务实例
            rsServiceInstancesMapper.updateRsServiceInstances(rsServiceInstance);

            // 更新服务
            rsRct.getRsOpRailFCL().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpRailFCL().setSqdServiceTypeId(20L);
            rsOpRailMapper.updateRsOpRail(rsRct.getRsOpRailFCL());

            // 更新费用
            updateFreight(rsServiceInstance, rsRct.getRsOpRailFCL().getRsChargeList(), rctId, 20L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpRailFCL().getRsOpLogList(), rctId, 20L);
        } else {
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("RailFCL");
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setServiceTypeId(20L);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsRct.getRsOpRailFCL().setServiceId(rsServiceInstance.getServiceId());
            rsRct.getRsOpRailFCL().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpRailFCL().setSqdServiceTypeId(20L);
            rsOpRailMapper.insertRsOpRail(rsRct.getRsOpRailFCL());

            // 插入费用
            updateFreight(rsServiceInstance, rsRct.getRsOpRailFCL().getRsChargeList(), rctId, 20L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpRailFCL().getRsOpLogList(), rctId, 20L);
        }
    }

    /**
     * 保存铁路服务信息
     *
     * @param rsRct
     * @param serviceNames
     * @param serviceTypeIds
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveRailLCL(RsRct rsRct, List<String> serviceNames, List<Long> serviceTypeIds) {
        RsServiceInstances rsServiceInstance;
        if (ObjectUtils.isNotEmpty(rsRct.getRsOpRailLCL().getRsServiceInstances())) {
            rsServiceInstance = rsRct.getRsOpRailLCL().getRsServiceInstances();
        } else {
            rsServiceInstance = new RsServiceInstances();
        }
        Long rctId = rsRct.getRctId();

        if (serviceNames.contains("RailLCL") && serviceTypeIds.contains(21L)) {
            // 更新服务实例
            rsServiceInstancesMapper.updateRsServiceInstances(rsServiceInstance);

            // 更新服务
            rsRct.getRsOpRailLCL().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpRailLCL().setSqdServiceTypeId(21L);
            rsOpRailMapper.updateRsOpRail(rsRct.getRsOpRailLCL());

            // 更新费用
            updateFreight(rsServiceInstance, rsRct.getRsOpRailLCL().getRsChargeList(), rctId, 21L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpRailLCL().getRsOpLogList(), rctId, 21L);
        } else {
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("RailLCL");
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setServiceTypeId(21L);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsRct.getRsOpRailLCL().setServiceId(rsServiceInstance.getServiceId());
            rsRct.getRsOpRailLCL().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpRailLCL().setSqdServiceTypeId(21L);
            rsOpRailMapper.insertRsOpRail(rsRct.getRsOpRailLCL());

            // 插入费用
            updateFreight(rsServiceInstance, rsRct.getRsOpRailLCL().getRsChargeList(), rctId, 21L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpRailLCL().getRsOpLogList(), rctId, 21L);
        }
    }

    /**
     * 保存空运服务信息
     *
     * @param rsRct
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveAir(RsRct rsRct) {
        Long rctId = rsRct.getRctId();

        List<RsOpAir> existingList = rsOpAirMapper.selectRsOpAirByRctId(rctId, 10L);

        List<RsOpAir> newList = rsRct.getRsOpAirList();

        // 找出需要删除的记录
        List<RsOpAir> toDeleteServiceObject = existingList.stream()
                .filter(existing -> newList.stream()
                        .noneMatch(newItem -> newItem.getAirId() != null &&
                                newItem.getAirId().equals(existing.getAirId())))
                .collect(Collectors.toList());

        if (!toDeleteServiceObject.isEmpty()) {
            for (RsOpAir rsOpAir : toDeleteServiceObject) {
//                rsOpSeaFclMapper.deleteRsOpSeaFclBySeaFclId(rsOpAir.getAirId());
                rsOpAirMapper.deleteRsOpAirByAirId(rsOpAir.getAirId());
                rsServiceInstancesMapper.deleteRsServiceInstancesByServiceId(rsOpAir.getRsServiceInstances().getServiceId());
                if (rsOpAir.getRsChargeList() != null && !rsOpAir.getRsChargeList().isEmpty()) {
                    for (RsCharge rsCharge : rsOpAir.getRsChargeList()) {
                        rsChargeMapper.deleteRsChargeByChargeId(rsCharge.getChargeId());
                    }
                }
            }
        }

        for (RsOpAir rsOpAir : rsRct.getRsOpAirList()) {
            RsServiceInstances rsServiceInstance = rsOpAir.getRsServiceInstances();
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("AIR");
            rsServiceInstance.setServiceTypeId(10L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsOpAir.setServiceId(rsServiceInstance.getServiceId());
            rsOpAir.setSqdRctNo(rsRct.getRctNo());
            rsOpAir.setSqdServiceTypeId(10L);
            rsOpAirMapper.upsertRsOpAir(rsOpAir);

            // 插入费用
            updateFreight(rsServiceInstance, rsOpAir.getRsChargeList(), rctId, 10L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsOpAir.getRsOpLogList(), rctId, 10L);

        }
    }

    /**
     * 保存整柜海运服务信息
     *
     * @param rsRct
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveSeaFcl(RsRct rsRct) {
        Long rctId = rsRct.getRctId();

        List<RsOpSeaFcl> existingList = rsOpSeaFclMapper.selectRsOpSeaFclByRctId(rctId, 1L);

        List<RsOpSeaFcl> newList = rsRct.getRsOpSeaFclList();

        // 找出需要删除的记录
        List<RsOpSeaFcl> toDeleteServiceObject = existingList.stream()
                .filter(existing -> newList.stream()
                        .noneMatch(newItem -> newItem.getSeaId() != null &&
                                newItem.getSeaId().equals(existing.getSeaId())))
                .collect(Collectors.toList());

        if (!toDeleteServiceObject.isEmpty()) {
            for (RsOpSeaFcl rsOpSeaFcl : toDeleteServiceObject) {
                rsOpSeaFclMapper.deleteRsOpSeaFclBySeaFclId(rsOpSeaFcl.getSeaId());
                rsServiceInstancesMapper.deleteRsServiceInstancesByServiceId(rsOpSeaFcl.getRsServiceInstances().getServiceId());
                if (rsOpSeaFcl.getRsChargeList() != null && !rsOpSeaFcl.getRsChargeList().isEmpty()) {
                    for (RsCharge rsCharge : rsOpSeaFcl.getRsChargeList()) {
                        rsChargeMapper.deleteRsChargeByChargeId(rsCharge.getChargeId());
                    }
                }
            }
        }

        for (RsOpSeaFcl rsOpSeaFcl : newList) {
            RsServiceInstances rsServiceInstance = rsOpSeaFcl.getRsServiceInstances();

            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("SeaFCL");
            rsServiceInstance.setServiceTypeId(1L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsOpSeaFcl.setServiceId(rsServiceInstance.getServiceId());
            rsOpSeaFcl.setSqdRctNo(rsRct.getRctNo());
            rsOpSeaFcl.setSqdServiceTypeId(1L);
            rsOpSeaFcl.setRctId(rctId);
            if (rsOpSeaFcl.getSeaId() == null) {
                String psaNo = redisIdGeneratorService.generateUniqueId("psa_no");
                String date = DateUtils.dateTime();
                rsOpSeaFcl.setSqdPsaNo("PSA" + date.substring(2, 6) + psaNo);
            }
            rsOpSeaFclMapper.upsertRsOpSeaFcl(rsOpSeaFcl);

            // 插入费用
            updateFreight(rsServiceInstance, rsOpSeaFcl.getRsChargeList(), rctId, 1L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsOpSeaFcl.getRsOpLogList(), rctId, 1L);
        }
    }

    /**
     * 保存拼柜海运服务信息
     *
     * @param rsRct
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveSeaLcl(RsRct rsRct) {
        Long rctId = rsRct.getRctId();

        List<RsOpSeaLcl> existingList = rsOpSeaLclMapper.selectRsOpSeaLclByRctId(rctId, 2L);

        List<RsOpSeaLcl> newList = rsRct.getRsOpSeaLclList();

        // 找出需要删除的记录
        List<RsOpSeaLcl> toDeleteServiceObject = existingList.stream()
                .filter(existing -> newList.stream()
                        .noneMatch(newItem -> newItem.getSeaId() != null &&
                                newItem.getSeaId().equals(existing.getSeaId())))
                .collect(Collectors.toList());

        if (!toDeleteServiceObject.isEmpty()) {
            for (RsOpSeaLcl rsOpSeaLcl : toDeleteServiceObject) {
                rsOpSeaLclMapper.deleteRsOpSeaLclBySeaLclId(rsOpSeaLcl.getSeaId());
                rsServiceInstancesMapper.deleteRsServiceInstancesByServiceId(rsOpSeaLcl.getRsServiceInstances().getServiceId());
                if (rsOpSeaLcl.getRsChargeList() != null && !rsOpSeaLcl.getRsChargeList().isEmpty()) {
                    for (RsCharge rsCharge : rsOpSeaLcl.getRsChargeList()) {
                        rsChargeMapper.deleteRsChargeByChargeId(rsCharge.getChargeId());
                    }
                }
            }
        }

        for (RsOpSeaLcl rsOpSeaLcl : rsRct.getRsOpSeaLclList()) {
            RsServiceInstances rsServiceInstance = rsOpSeaLcl.getRsServiceInstances();
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("SeaLCL");
            rsServiceInstance.setServiceTypeId(2L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsOpSeaLcl.setServiceId(rsServiceInstance.getServiceId());
            rsOpSeaLcl.setSqdRctNo(rsRct.getRctNo());
            rsOpSeaLcl.setSqdServiceTypeId(2L);
            if (rsOpSeaLcl.getSeaId() == null) {
                String psaNo = redisIdGeneratorService.generateUniqueId("psa_no");
                String date = DateUtils.dateTime();
                rsOpSeaLcl.setSqdPsaNo("PSA" + date.substring(2, 6) + psaNo);
            }
            rsOpSeaLclMapper.upsertRsOpSeaLcl(rsOpSeaLcl);

            // 插入费用
            updateFreight(rsServiceInstance, rsOpSeaLcl.getRsChargeList(), rctId, 2L, rsRct.getRctNo(), rsRct.getRctCreateTime());

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsOpSeaLcl.getRsOpLogList(), rctId, 2L);
        }
    }

    private void updateOpLog(Long serviceId, List<RsOpLog> rsOpLogs, Long rctId, Long sqdServiceTypeId) {
        if (rsOpLogs == null || rsOpLogs.isEmpty()) {
            return;
        }
        rsOpLogMapper.deleteRsOpLogByServiceId(serviceId);
        for (RsOpLog rsOpLog : rsOpLogs) {
            rsOpLog.setServiceId(serviceId);
            rsOpLog.setSqdRctId(rctId);
            rsOpLog.setSqdServiceTypeId(sqdServiceTypeId);
            rsOpLog.setServerSystemTime(new Date());
            rsOpLogMapper.insertRsOpLog(rsOpLog);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateFreight(RsServiceInstances rsServiceInstances, List<RsCharge> rsCharges, Long rctId, long sqdServiceTypeId, String rctNo, Date rctCreateTime) {
        if (rsCharges == null || rsCharges.isEmpty()) {
            return;
        }

        /*// 找出数据库与传入的最新费用列表作比较删除掉旧的
        List<RsCharge> existingList = rsChargeMapper.selectRsChargeListByServiceId(rsServiceInstances.getServiceId());

        List<RsCharge> toDeleteServiceObject = existingList.stream()
                .filter(existing -> rsCharges.stream()
                        .noneMatch(newItem -> newItem.getChargeId() != null &&
                                newItem.getChargeId().equals(existing.getChargeId())))
                .collect(Collectors.toList());

        if (!toDeleteServiceObject.isEmpty()) {
            rsChargeMapper.deleteRsChargeByChargeIds(toDeleteServiceObject.stream().map(RsCharge::getChargeId).toArray(Long[]::new));
        }*/

        if (rsServiceInstances.getServiceId() != null) {
            for (RsCharge rsCharge : rsCharges) {
                rsCharge.setServiceId(rsServiceInstances.getServiceId());
                rsCharge.setSqdRctId(rctId);
                rsCharge.setSqdServiceTypeId(sqdServiceTypeId);
                rsCharge.setPaymentTitleCode(rsServiceInstances.getPaymentTitleCode());
                rsCharge.setIsRecievingOrPaying(1L);
                rsCharge.setSqdRctNo(rctNo);
                if (rsCharge.getIsAccountConfirmed() == null) {
                    rsCharge.setIsAccountConfirmed("0");
                }
                rsCharge.setCurrencyRateCalculateDate(rctCreateTime);
                if (rsCharge.getChargeId() == null || rsCharge.getIsAccountConfirmed().equals("0")) {
                    rsCharge.setSqdDnCurrencyBalance(rsCharge.getSubtotal());
                    rsCharge.setDnCurrencyBalance(rsCharge.getSubtotal());
                }
                rsChargeMapper.upsertRsCharge(rsCharge);
            }
        }
    }

    @Override
    public void notification() {
        try {
            // 获取通知数量
            // 获取用户ID
            Long userId = SecurityUtils.getUserId();
            // 获取用户职位
            Long position = basPositionService.selectPostByUserId(userId);
            // 获取登录用户信息
            LoginUser loginUser = SecurityUtils.getLoginUser();

            // 检查是否满足特定条件
            boolean isOperator = loginUser.getUser().getRoles().stream()
                    .anyMatch(role -> "Operator".equals(role.getRoleKey()));
            int deptCreateListNum = loginUser.getUser().getDept().getCreateListNum();

            // 根据条件返回不同的通知数量
            int opNotificationCount;
            if (isOperator && position < 14 && deptCreateListNum != 4) {
                opNotificationCount = rsRctMapper.opNotification(SecurityUtils.getUserId());
            } else {
                opNotificationCount = rsRctMapper.opNotification(null);
            }

            int psaNotificationCount = rsRctMapper.psaNotification();

            // 存储到Redis中
            // 使用具有业务意义的键名
            redisCache.setCacheObject("notification:psa:count", psaNotificationCount);

            // 通过WebSocket广播通知 - 使用静态方法

            NotificationWebSocket.broadcastNotification(opNotificationCount, psaNotificationCount);
        } catch (Exception e) {
            log.error("发送通知失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveClientMessage(RsRct rsRct) {
        int out;
        // 关联服务实例
        RsServiceInstances rsServiceInstances = rsServiceInstancesMapper.selectRsServiceInstances(rsRct.getRctId(), "client");
        if (rsServiceInstances == null) {
            out = rsServiceInstancesMapper.insertRsServiceInstances(rsRct.getRsClientMessage().getRsServiceInstances());
            rsServiceInstances = rsRct.getRsClientMessage().getRsServiceInstances();
        } else {
            out = rsServiceInstancesMapper.updateRsServiceInstances(rsRct.getRsClientMessage().getRsServiceInstances());
            rsRct.getRsClientMessage().getRsServiceInstances().setServiceId(rsServiceInstances.getServiceId());
        }

        Long serviceInstanceId = rsRct.getRsClientMessage().getRsServiceInstances().getServiceId();

        assert rsServiceInstances != null;
        updateOpLog(rsServiceInstances.getServiceId(), rsRct.getRsClientMessage().getRsOpLogList(), rsRct.getRctId(), serviceInstanceId);

        // 费用信息
        List<RsCharge> rsCharges = rsRct.getRsClientMessage().getRsChargeList();
        for (RsCharge rsCharge : rsCharges) {
            rsCharge.setServiceId(serviceInstanceId);
            rsCharge.setSqdRctId(rsRct.getRctId());
//            rsCharge.setSqdServiceTypeId();
            rsCharge.setIsRecievingOrPaying(0L);
            rsCharge.setPaymentTitleCode(rsServiceInstances.getPaymentTitleCode());
            rsCharge.setSqdRctNo(rsRct.getRctNo());
            rsCharge.setCurrencyRateCalculateDate(rsRct.getRctCreateTime());
            if (rsCharge.getChargeId() == null) {
                rsCharge.setSqdDnCurrencyBalance(rsCharge.getSubtotal());
            }
            out += rsChargeMapper.upsertRsCharge(rsCharge);
        }
        return out;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveAsAllServices(RsRct rsRct) {
        int out;
        // 关联服务实例
        List<String> serviceNames = Collections.emptyList();
        List<Long> serviceTypeIds = Collections.emptyList();

        List<RsOpLog> rsOpLogList = new ArrayList<>();

        saveAsClientMessage(rsRct);
        if (rsRct.getRsOpSeaFclList() != null && !rsRct.getRsOpSeaFclList().isEmpty()) {
            for (RsOpSeaFcl rsOpSeaFcl : rsRct.getRsOpSeaFclList()) {
                List<RsOpLog> rsOpLogs = rsOpSeaFcl.getRsOpLogList();
                if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                    rsOpLogList.addAll(rsOpLogs);
                }

                rsOpSeaFcl.setSeaId(null);
                rsOpSeaFcl.setPsaNo(null);
                rsOpSeaFcl.setPsaId(null);
                rsOpSeaFcl.setSqdPsaNo(null);
                rsOpSeaFcl.setSqdRctNo(null);
                rsOpSeaFcl.setSqdPsaNo(null);
                rsOpSeaFcl.getRsServiceInstances().setServiceId(null);

                resetCharge(rsOpSeaFcl.getRsChargeList());
            }
            saveSeaFcl(rsRct);
        }

        if (rsRct.getRsOpSeaLclList() != null && !rsRct.getRsOpSeaLclList().isEmpty()) {
            for (RsOpSeaLcl rsOpSeaLcl : rsRct.getRsOpSeaLclList()) {
                List<RsOpLog> rsOpLogs = rsOpSeaLcl.getRsOpLogList();
                if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                    rsOpLogList.addAll(rsOpLogs);
                }

                rsOpSeaLcl.setSeaId(null);
                rsOpSeaLcl.setPsaNo(null);
                rsOpSeaLcl.setPsaId(null);
                rsOpSeaLcl.setSqdPsaNo(null);
                rsOpSeaLcl.setSqdRctNo(null);
                rsOpSeaLcl.setSqdPsaNo(null);
                rsOpSeaLcl.getRsServiceInstances().setServiceId(null);

                resetCharge(rsOpSeaLcl.getRsChargeList());
            }
            saveSeaLcl(rsRct);
        }

        if (rsRct.getRsOpAirList() != null && !rsRct.getRsOpAirList().isEmpty()) {
            for (RsOpAir rsOpAir : rsRct.getRsOpAirList()) {
                List<RsOpLog> rsOpLogs = rsOpAir.getRsOpLogList();
                if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                    rsOpLogList.addAll(rsOpLogs);
                }
                rsOpAir.setAirId(null);
                rsOpAir.getRsServiceInstances().setServiceId(null);

                resetCharge(rsOpAir.getRsChargeList());
                saveAir(rsRct);
            }
        }
        if (rsRct.getRsOpRailFCL() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpRailFCL().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            rsRct.getRsOpRailFCL().setRailId(null);
            rsRct.getRsOpRailFCL().getRsServiceInstances().setServiceId(null);

            resetCharge(rsRct.getRsOpRailFCL().getRsChargeList());
            saveRailFCL(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpRailLCL() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpRailLCL().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            rsRct.getRsOpRailLCL().setRailId(null);
            rsRct.getRsOpRailLCL().getRsServiceInstances().setServiceId(null);

            resetCharge(rsRct.getRsOpRailLCL().getRsChargeList());
            saveRailLCL(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpExpress() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpExpress().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            rsRct.getRsOpExpress().setExpressId(null);
            rsRct.getRsOpExpress().getRsServiceInstances().setServiceId(null);

            resetCharge(rsRct.getRsOpExpress().getRsChargeList());
            saveExpress(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpCtnrTruckList() != null && !rsRct.getRsOpCtnrTruckList().isEmpty()) {
            for (RsOpCtnrTruck rsOpCtnrTruck : rsRct.getRsOpCtnrTruckList()) {
                List<RsOpLog> rsOpLogs = rsOpCtnrTruck.getRsOpLogList();
                if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                    rsOpLogList.addAll(rsOpLogs);
                }
                rsOpCtnrTruck.setTruckId(null);
                rsOpCtnrTruck.getRsServiceInstances().setServiceId(null);
                resetCharge(rsOpCtnrTruck.getRsChargeList());
                saveCtnrTruck(rsRct);
            }
        }
        if (rsRct.getRsOpBulkTruckList() != null && !rsRct.getRsOpBulkTruckList().isEmpty()) {
            for (RsOpBulkTruck rsOpBulkTruck : rsRct.getRsOpBulkTruckList()) {
                List<RsOpLog> rsOpLogs = rsOpBulkTruck.getRsOpLogList();
                if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                    rsOpLogList.addAll(rsOpLogs);
                }
                rsOpBulkTruck.setTruckId(null);
                rsOpBulkTruck.getRsServiceInstances().setServiceId(null);
                resetCharge(rsOpBulkTruck.getRsChargeList());
                saveBulkTruck(rsRct);
            }
        }
        if (rsRct.getRsOpFreeDeclareList() != null && !rsRct.getRsOpFreeDeclareList().isEmpty()) {
            for (RsOpFreeDeclare rsOpFreeDeclare : rsRct.getRsOpFreeDeclareList()) {
                List<RsOpLog> rsOpLogs = rsOpFreeDeclare.getRsOpLogList();
                if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                    rsOpLogList.addAll(rsOpLogs);
                }
                rsOpFreeDeclare.setExportCustomsClearanceId(null);
                rsOpFreeDeclare.getRsServiceInstances().setServiceId(null);
                resetCharge(rsOpFreeDeclare.getRsChargeList());
                saveFreeDeclare(rsRct);
            }
        }
        if (rsRct.getRsOpDocDeclareList() != null && !rsRct.getRsOpDocDeclareList().isEmpty()) {
            for (RsOpDocDeclare rsOpDocDeclare : rsRct.getRsOpDocDeclareList()) {
                List<RsOpLog> rsOpLogs = rsOpDocDeclare.getRsOpLogList();
                if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                    rsOpLogList.addAll(rsOpLogs);
                }
                rsOpDocDeclare.setExportCustomsClearanceId(null);
                rsOpDocDeclare.getRsServiceInstances().setServiceId(null);
                resetCharge(rsOpDocDeclare.getRsChargeList());
                saveDocDeclare(rsRct);
            }
        }
        if (rsRct.getRsOpDOAgent() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpDOAgent().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            rsRct.getRsOpDOAgent().setImportCustomsClearanceId(null);
            rsRct.getRsOpDOAgent().getRsServiceInstances().setServiceId(null);
            resetCharge(rsRct.getRsOpDOAgent().getRsChargeList());
            saveDOAgent(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpClearAgent() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpClearAgent().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            rsRct.getRsOpClearAgent().setImportCustomsClearanceId(null);
            rsRct.getRsOpClearAgent().getRsServiceInstances().setServiceId(null);
            resetCharge(rsRct.getRsOpClearAgent().getRsChargeList());
            saveClearAgent(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpWHS() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpWHS().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            rsRct.getRsOpWHS().setWarehouseId(null);
            rsRct.getRsOpWHS().getRsServiceInstances().setServiceId(null);
            resetCharge(rsRct.getRsOpWHS().getRsChargeList());
            saveWHS(rsRct, serviceNames, serviceTypeIds);
        }

        // 拓展服务
        if (rsRct.getRsOp3rdCert() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOp3rdCert().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            rsRct.getRsOp3rdCert().setExpandServiceId(null);
            rsRct.getRsOp3rdCert().getRsServiceInstances().setServiceId(null);
            resetCharge(rsRct.getRsOp3rdCert().getRsChargeList());
            save3rdCert(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpINS() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpINS().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            rsRct.getRsOpINS().setExpandServiceId(null);
            rsRct.getRsOpINS().getRsServiceInstances().setServiceId(null);
            resetCharge(rsRct.getRsOpINS().getRsChargeList());
            saveINS(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpTrading() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpTrading().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            rsRct.getRsOpTrading().setExpandServiceId(null);
            rsRct.getRsOpTrading().getRsServiceInstances().setServiceId(null);
            resetCharge(rsRct.getRsOpTrading().getRsChargeList());
            saveTrading(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpFumigation() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpFumigation().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            rsRct.getRsOpFumigation().setExpandServiceId(null);
            rsRct.getRsOpFumigation().getRsServiceInstances().setServiceId(null);
            resetCharge(rsRct.getRsOpFumigation().getRsChargeList());
            saveFumigation(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpCO() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpCO().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            rsRct.getRsOpCO().setExpandServiceId(null);
            rsRct.getRsOpCO().getRsServiceInstances().setServiceId(null);
            resetCharge(rsRct.getRsOpCO().getRsChargeList());
            saveCO(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpOther() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpOther().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            rsRct.getRsOpOther().setExpandServiceId(null);
            rsRct.getRsOpOther().getRsServiceInstances().setServiceId(null);
            resetCharge(rsRct.getRsOpOther().getRsChargeList());
            saveOther(rsRct, serviceNames, serviceTypeIds);
        }
        return 0;
    }

    private void saveAsClientMessage(RsRct rsRct) {
        // 关联服务实例
        RsServiceInstances rsServiceInstances = rsServiceInstancesMapper.selectRsServiceInstances(rsRct.getRctId(), "client");
        if (rsServiceInstances == null) {
            rsServiceInstancesMapper.insertRsServiceInstances(rsRct.getRsClientMessage().getRsServiceInstances());
            rsServiceInstances = rsRct.getRsClientMessage().getRsServiceInstances();
        } else {
            rsServiceInstancesMapper.updateRsServiceInstances(rsRct.getRsClientMessage().getRsServiceInstances());
            rsRct.getRsClientMessage().getRsServiceInstances().setServiceId(rsServiceInstances.getServiceId());
        }

        Long serviceInstanceId = rsRct.getRsClientMessage().getRsServiceInstances().getServiceId();

        assert rsServiceInstances != null;
        updateOpLog(rsServiceInstances.getServiceId(), rsRct.getRsClientMessage().getRsOpLogList(), rsRct.getRctId(), serviceInstanceId);

        // 费用信息
        List<RsCharge> rsCharges = rsRct.getRsClientMessage().getRsChargeList();
        resetCharge(rsCharges);
        for (RsCharge rsCharge : rsCharges) {
            rsCharge.setServiceId(serviceInstanceId);
            rsCharge.setSqdRctId(rsRct.getRctId());
//            rsCharge.setSqdServiceTypeId();
            rsCharge.setIsRecievingOrPaying(0L);
            rsCharge.setPaymentTitleCode(rsServiceInstances.getPaymentTitleCode());
            rsCharge.setSqdRctNo(rsRct.getRctNo());
            rsCharge.setCurrencyRateCalculateDate(rsRct.getRctCreateTime());
            if (rsCharge.getChargeId() == null) {
                rsCharge.setSqdDnCurrencyBalance(rsCharge.getSubtotal());
            }
            rsChargeMapper.upsertRsCharge(rsCharge);
        }
    }

    @Override
    public void writeoff(List<String> rctNoList) {
        for (String rctNo : rctNoList) {
            rsRctMapper.updateByWriteoff(rctNo);
        }
    }

    @Override
    public List<RsRct> selectUnVerifyRsRctAggregatorList(RsRct rsRct) {
        boolean search = rsRct.getPolIds() != null || rsRct.getDestinationPortIds() != null || rsRct.getLineIds() != null;
        if (search) {
            Map<String, List<?>> query = queryRctList(rsRct);
            List<List<Long>> queryList = (List<List<Long>>) query.get("list");
            List<Long> queryPolIds = (List<Long>) query.get("locationDeparture");
            List<Long> queryDestinationIds = (List<Long>) query.get("locationDestination");
            if (!queryList.isEmpty()) {
                rsRct.setRctIds(SearchUtils.getLongs(queryList));
            }
            if (!queryPolIds.isEmpty()) {
                rsRct.setPolIds(queryPolIds.toArray(new Long[0]));
            } else {
                rsRct.setPolIds(null);
            }
            if (!queryDestinationIds.isEmpty()) {
                rsRct.setDestinationPortIds(queryDestinationIds.toArray(new Long[0]));
            } else {
                rsRct.setDestinationPortIds(null);
            }
        }
        rsRct.setUserId(SecurityUtils.getUserId());
        return rsRctMapper.selectUnVerifyRsRctList(rsRct);
    }
}
