package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.BasServiceDetails;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.BasServiceDetailsService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 服务明细Controller
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@RestController
@RequestMapping("/system/servicedetails")
public class BasServiceDetailsController extends BaseController {
    @Autowired
    private BasServiceDetailsService basServiceDetailsService;

    /**
     * 查询服务明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:servicedetails:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasServiceDetails basServiceDetails) {
        startPage();
        List<BasServiceDetails> list = basServiceDetailsService.selectBasServiceDetailsList(basServiceDetails);
        return getDataTable(list);
    }

    /**
     * 导出服务明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:servicedetails:export')")
    @Log(title = "服务明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasServiceDetails basServiceDetails) {
        List<BasServiceDetails> list = basServiceDetailsService.selectBasServiceDetailsList(basServiceDetails);
        ExcelUtil<BasServiceDetails> util = new ExcelUtil<BasServiceDetails>(BasServiceDetails.class);
        util.exportExcel(response, list, "服务明细数据");
    }

    /**
     * 获取服务明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:servicedetails:query')")
    @GetMapping(value = "/{serviceDetailsCode}")
    public AjaxResult getInfo(@PathVariable("serviceDetailsCode") String serviceDetailsCode) {
        return AjaxResult.success(basServiceDetailsService.selectBasServiceDetailsByServiceDetailsCode(serviceDetailsCode));
    }

    /**
     * 新增服务明细
     */
    @PreAuthorize("@ss.hasPermi('system:servicedetails:add')")
    @Log(title = "服务明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasServiceDetails basServiceDetails) {
        return toAjax(basServiceDetailsService.insertBasServiceDetails(basServiceDetails));
    }

    /**
     * 修改服务明细
     */
    @PreAuthorize("@ss.hasPermi('system:servicedetails:edit')")
    @Log(title = "服务明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasServiceDetails basServiceDetails) {
        return toAjax(basServiceDetailsService.updateBasServiceDetails(basServiceDetails));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:servicedetails:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasServiceDetails basServiceDetails) {
        basServiceDetails.setUpdateBy(getUserId());
        return toAjax(basServiceDetailsService.changeStatus(basServiceDetails));
    }

    /**
     * 删除服务明细
     */
    @PreAuthorize("@ss.hasPermi('system:servicedetails:remove')")
    @Log(title = "服务明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{serviceDetailsCodes}")
    public AjaxResult remove(@PathVariable String[] serviceDetailsCodes) {
        return toAjax(basServiceDetailsService.deleteBasServiceDetailsByServiceDetailsCodes(serviceDetailsCodes));
    }
}
