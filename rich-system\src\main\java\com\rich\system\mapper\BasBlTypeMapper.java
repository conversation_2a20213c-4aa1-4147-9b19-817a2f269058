package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.BasBlType;
import org.apache.ibatis.annotations.Mapper;

/**
 * 提单类型Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@Mapper
public interface BasBlTypeMapper {
    /**
     * 查询提单类型
     *
     * @param blTypeCode 提单类型主键
     * @return 提单类型
     */
    BasBlType selectBasBlTypeByBlTypeCode(String blTypeCode);

    /**
     * 查询提单类型列表
     *
     * @param basBlType 提单类型
     * @return 提单类型集合
     */
    List<BasBlType> selectBasBlTypeList(BasBlType basBlType);

    /**
     * 新增提单类型
     *
     * @param basBlType 提单类型
     * @return 结果
     */
    int insertBasBlType(BasBlType basBlType);

    /**
     * 修改提单类型
     *
     * @param basBlType 提单类型
     * @return 结果
     */
    int updateBasBlType(BasBlType basBlType);

    /**
     * 删除提单类型
     *
     * @param blTypeCode 提单类型主键
     * @return 结果
     */
    int deleteBasBlTypeByBlTypeCode(String blTypeCode);

    /**
     * 批量删除提单类型
     *
     * @param blTypeCodes 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasBlTypeByBlTypeCodes(String[] blTypeCodes);
}
