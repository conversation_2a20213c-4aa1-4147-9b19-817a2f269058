package com.rich.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 基础运费对象 rs_freight
 *
 * <AUTHOR>
 * @date 2023-01-04
 */
public class RsFreight extends BaseEntity {

    private static final long serialVersionUID = 1L;

    private boolean query;

    private String typeId;

    /**
     * 运费
     */
    @Excel(name = "序号", width = 8)
    private Long freightId;

    @Excel(name = "询价单号", width = 22)
    private String inquiryNo;

    @Excel(name = "服务项目", width = 8)
    private String serviceType;

    @Excel(name = "所属物流", width = 8)
    private String logisticsType;

    @Excel(name = "启运港", width = 15)
    private String departure;
    @Excel(name = "装运区域", width = 30)
    private String loading;
    @Excel(name = "中转港", width = 15)
    private String transitPort;
    @Excel(name = "目的港", width = 17)
    private String destination;

    @Excel(name = "币种", width = 5)
    private String currency;

    @Excel(name = "20GP", width = 6)
    private BigDecimal priceB;

    @Excel(name = "40GP", width = 6)
    private BigDecimal priceC;

    @Excel(name = "40HQ", width = 6)
    private BigDecimal priceD;

    @Excel(name = "通用", width = 6)
    private BigDecimal priceA;

    @Excel(name = "柜型", width = 5)
    private String unit;

    @Excel(name = "货物特征", width = 8)
    private String cargoType;

    @Excel(name = "货物限重", width = 9)
    private BigDecimal maxWeight;

    @Excel(name = "船期", width = 17)
    private String logisticsSchedule;
    @Excel(name = "航程", width = 5)
    private BigDecimal logisticsTimeliness;
    @Excel(name = "承运人", width = 9)
    private String carrier;
    @Excel(name = "订舱口", width = 10)
    private String company;
    @Excel(name = "合约类别", width = 8)
    private String contractType;
    @Excel(name = "合约号", width = 10)
    private String agreementNo;

    @Excel(name = "有效期节点", width = 7, isExport = false)
    private String validPeriodTimeNode;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "有效期从", width = 8, dateFormat = "yyyy-MM-dd")
    private Date validFrom;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "有效期至", width = 8, dateFormat = "yyyy-MM-dd")
    private Date validTo;

    @Excel(name = "业务备注", width = 30)
    private String noticeForSales;

    private Long requireSalesId;

    @Excel(name = "询价业务", width = 10)
    private String salesName;

    // 询价标志
    private Integer isSalesRequired;

    private Integer isReplied;

    @Excel(name = "询价备注", width = 30)
    private String requireRemark;

    private Date goodsReadyTime;

    @Excel(name = "商务备注", width = 30)
    private String psaRemark;

    /**
     * 服务类型
     */
    private String departureIata;

    private String destinationIata;


    private Long serviceTypeId;

    private String agreementCode;

    private String carrierCode;

    private Long[] carrierIds;

    private Long supplierId;

    private Long[] companyIds;

    private Long logisticsTypeId;
    private String importExport;

    private String companyRoleName;


    //    @Excel(name = "价格类型")
    private String charge;

    private String chargeEn;

    private Integer chargeOrderNum;

    private Integer chargeTypeOrderNum;


    //    @Excel(name = "1000KGS")
    private BigDecimal priceE;

    //    @Excel(name = "物流时效节点")
    private String logisticsEfficiencyNode;


    //    @Excel(name = "物流时效单位")
    private String logisticsUnit;


    /**
     * 装箱区域
     */
    private Long precarriageRegionId;


    /**
     * 前程启运港
     */
    private Long polId;

    /**
     * 中转港
     */
    private Long transitPortId;


    /**
     * 目的地
     */
    private Long destinationPortId;

    private String cargoCurrencyCode;

    private String cargoCurrency;

    private BigDecimal cargoValue;

    private Long weightUnitId;

    private String cargoUnit;

    /**
     * 费用ID
     */
    private Long chargeId;

    /**
     * 币种ID
     */
    private String currencyCode;

    /**
     * 单位ID
     */
    private Long unitId;

    /**
     * 航班时间按日期
     */
//    @Excel(name = "时效按日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date shippingDate;

    /**
     * 航班时间按星期
     */
//    @Excel(name = "时效按星期")
    private String shippingWeek;

    /**
     * 物流时效节点
     */
    private Long logisticsEfficiencyNodeId;


    /**
     * 物流时效单位
     */

    private String ttUnitCode;


    /**
     * 有效期时间节点
     */
    private Long validPeriodTimeNodeId;

    private String isValid;

    /**
     * 状态
     */
    private String status;

    private String freightQuery;

    private List<RsLocalCharge> locals;

    private Long[] cargoTypeIds;

    private List<Long> freightIds;

    private BigDecimal freeStorage;

    private BigDecimal demurrage;

    private String storageUnitCode;

    private String storageUnit;

    private String demurrageUnitCode;

    private String demurrageUnit;

    private String companyGrade;

    private Long[] departureIds;
    private Long[] destinationIds;
    private Long[] lineDepartureIds;
    private Long[] lineDestinationIds;
    private Long[] transitPortIds;

    private Long[] contractTypeIds;

//    private Long[] unitIds;


    private String[] unitIds;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date recordFrom;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date recordTo;

    private String unitCode;
    private String serviceShortType;
    private String destinationLocation;
    private String logisticsEnType;
    private String serviceEnType;
    private String precarriageRegion;

    public String getPrecarriageRegion() {
        return precarriageRegion;
    }

    public void setPrecarriageRegion(String precarriageRegion) {
        this.precarriageRegion = precarriageRegion;
    }

    public String getServiceEnType() {
        return serviceEnType;
    }

    public void setServiceEnType(String serviceEnType) {
        this.serviceEnType = serviceEnType;
    }

    public String getLogisticsEnType() {
        return logisticsEnType;
    }

    public void setLogisticsEnType(String logisticsEnType) {
        this.logisticsEnType = logisticsEnType;
    }

    public String getDestinationLocation() {
        return destinationLocation;
    }

    public void setDestinationLocation(String destinationLocation) {
        this.destinationLocation = destinationLocation;
    }

    public String getServiceShortType() {
        return serviceShortType;
    }

    public void setServiceShortType(String serviceShortType) {
        this.serviceShortType = serviceShortType;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getRequireRemark() {
        return requireRemark;
    }

    public void setRequireRemark(String requireRemark) {
        this.requireRemark = requireRemark;
    }

    public String getSalesName() {
        return salesName;
    }

    public void setSalesName(String salesName) {
        this.salesName = salesName;
    }

    public Long getRequireSalesId() {
        return requireSalesId;
    }

    public void setRequireSalesId(Long requireSalesId) {
        this.requireSalesId = requireSalesId;
    }

    public Integer getIsSalesRequired() {
        return isSalesRequired;
    }

    public void setIsSalesRequired(Integer isSalesRequired) {
        this.isSalesRequired = isSalesRequired;
    }

    public Integer getIsReplied() {
        return isReplied;
    }

    public void setIsReplied(Integer isReplied) {
        this.isReplied = isReplied;
    }

    public Date getGoodsReadyTime() {
        return goodsReadyTime;
    }

    public void setGoodsReadyTime(Date goodsReadyTime) {
        this.goodsReadyTime = goodsReadyTime;
    }

    public Date getRecordFrom() {
        return recordFrom;
    }

    public void setRecordFrom(Date recordFrom) {
        this.recordFrom = recordFrom;
    }

    public Date getRecordTo() {
        return recordTo;
    }

    public void setRecordTo(Date recordTo) {
        this.recordTo = recordTo;
    }

    public Long[] getContractTypeIds() {
        return contractTypeIds;
    }

    public void setContractTypeIds(Long[] contractTypeIds) {
        this.contractTypeIds = contractTypeIds;
    }

    public String[] getUnitIds() {
        return unitIds;
    }

    public void setUnitIds(String[] unitIds) {
        this.unitIds = unitIds;
    }

    public Long[] getDepartureIds() {
        return departureIds;
    }

    public void setDepartureIds(Long[] departureIds) {
        this.departureIds = departureIds;
    }

    public Long[] getDestinationIds() {
        return destinationIds;
    }

    public void setDestinationIds(Long[] destinationIds) {
        this.destinationIds = destinationIds;
    }

    public Long[] getLineDepartureIds() {
        return lineDepartureIds;
    }

    public void setLineDepartureIds(Long[] lineDepartureIds) {
        this.lineDepartureIds = lineDepartureIds;
    }

    public Long[] getLineDestinationIds() {
        return lineDestinationIds;
    }

    public void setLineDestinationIds(Long[] lineDestinationIds) {
        this.lineDestinationIds = lineDestinationIds;
    }

    public Long[] getTransitPortIds() {
        return transitPortIds;
    }

    public void setTransitPortIds(Long[] transitPortIds) {
        this.transitPortIds = transitPortIds;
    }

    public String getCompanyGrade() {
        return companyGrade;
    }

    public void setCompanyGrade(String companyGrade) {
        this.companyGrade = companyGrade;
    }

    public String getDepartureIata() {
        return departureIata;
    }

    public void setDepartureIata(String departureIata) {
        this.departureIata = departureIata;
    }

    public String getDestinationIata() {
        return destinationIata;
    }

    public void setDestinationIata(String destinationIata) {
        this.destinationIata = destinationIata;
    }

    public String getChargeEn() {
        return chargeEn;
    }

    public void setChargeEn(String chargeEn) {
        this.chargeEn = chargeEn;
    }

    public Integer getChargeOrderNum() {
        return chargeOrderNum;
    }

    public void setChargeOrderNum(Integer chargeOrderNum) {
        this.chargeOrderNum = chargeOrderNum;
    }

    public Integer getChargeTypeOrderNum() {
        return chargeTypeOrderNum;
    }

    public void setChargeTypeOrderNum(Integer chargeTypeOrderNum) {
        this.chargeTypeOrderNum = chargeTypeOrderNum;
    }

    public String getLogisticsSchedule() {
        return logisticsSchedule;
    }

    public void setLogisticsSchedule(String logisticsSchedule) {
        this.logisticsSchedule = logisticsSchedule;
    }

    public String getImportExport() {
        return importExport;
    }

    public void setImportExport(String importExport) {
        this.importExport = importExport;
    }

    public boolean isQuery() {
        return query;
    }

    public void setQuery(boolean query) {
        this.query = query;
    }

    public Long getLogisticsTypeId() {
        return logisticsTypeId;
    }

    public void setLogisticsTypeId(Long logisticsTypeId) {
        this.logisticsTypeId = logisticsTypeId;
    }

    public String getCargoCurrencyCode() {
        return cargoCurrencyCode;
    }

    public void setCargoCurrencyCode(String cargoCurrencyCode) {
        this.cargoCurrencyCode = cargoCurrencyCode;
    }

    public String getCargoCurrency() {
        return cargoCurrency;
    }

    public void setCargoCurrency(String cargoCurrency) {
        this.cargoCurrency = cargoCurrency;
    }

    public String getTypeId() {
        return typeId;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

    public String getStorageUnit() {
        return storageUnit;
    }

    public void setStorageUnit(String storageUnit) {
        this.storageUnit = storageUnit;
    }

    public String getDemurrageUnit() {
        return demurrageUnit;
    }

    public void setDemurrageUnit(String demurrageUnit) {
        this.demurrageUnit = demurrageUnit;
    }

    public String getCargoUnit() {
        return cargoUnit;
    }

    public void setCargoUnit(String cargoUnit) {
        this.cargoUnit = cargoUnit;
    }

    public BigDecimal getPriceE() {
        return priceE;
    }

    public void setPriceE(BigDecimal priceE) {
        this.priceE = priceE;
    }

    public String getLoading() {
        return loading;
    }

    public void setLoading(String loading) {
        this.loading = loading;
    }

    public String getLogisticsType() {
        return logisticsType;
    }

    public void setLogisticsType(String logisticsType) {
        this.logisticsType = logisticsType;
    }

    public Long getPrecarriageRegionId() {
        return precarriageRegionId;
    }

    public void setPrecarriageRegionId(Long precarriageRegionId) {
        this.precarriageRegionId = precarriageRegionId;
    }

    public BigDecimal getMaxWeight() {
        return maxWeight;
    }

    public void setMaxWeight(BigDecimal maxWeight) {
        this.maxWeight = maxWeight;
    }

    public BigDecimal getCargoValue() {
        return cargoValue;
    }

    public void setCargoValue(BigDecimal cargoValue) {
        this.cargoValue = cargoValue;
    }

    public Long getWeightUnitId() {
        return weightUnitId;
    }

    public void setWeightUnitId(Long weightUnitId) {
        this.weightUnitId = weightUnitId;
    }

    public BigDecimal getFreeStorage() {
        return freeStorage;
    }

    public void setFreeStorage(BigDecimal freeStorage) {
        this.freeStorage = freeStorage;
    }

    public BigDecimal getDemurrage() {
        return demurrage;
    }

    public void setDemurrage(BigDecimal demurrage) {
        this.demurrage = demurrage;
    }

    public String getStorageUnitCode() {
        return storageUnitCode;
    }

    public void setStorageUnitCode(String storageUnitCode) {
        this.storageUnitCode = storageUnitCode;
    }

    public String getDemurrageUnitCode() {
        return demurrageUnitCode;
    }

    public void setDemurrageUnitCode(String demurrageUnitCode) {
        this.demurrageUnitCode = demurrageUnitCode;
    }

    public List<Long> getFreightIds() {
        return freightIds;
    }

    public void setFreightIds(List<Long> freightIds) {
        this.freightIds = freightIds;
    }

    public Long[] getCarrierIds() {
        return carrierIds;
    }

    public void setCarrierIds(Long[] carrierIds) {
        this.carrierIds = carrierIds;
    }

    public Long[] getCompanyIds() {
        return companyIds;
    }

    public void setCompanyIds(Long[] companyIds) {
        this.companyIds = companyIds;
    }

    public String getCargoType() {
        return cargoType;
    }

    public void setCargoType(String cargoType) {
        this.cargoType = cargoType;
    }

    public String getNoticeForSales() {
        return noticeForSales;
    }

    public void setNoticeForSales(String noticeForSales) {
        this.noticeForSales = noticeForSales;
    }

    public String getPsaRemark() {
        return psaRemark;
    }

    public void setPsaRemark(String psaRemark) {
        this.psaRemark = psaRemark;
    }

    public Long[] getCargoTypeIds() {
        return cargoTypeIds;
    }

    public void setCargoTypeIds(Long[] cargoTypeIds) {
        this.cargoTypeIds = cargoTypeIds;
    }

    public String getDeparture() {
        return departure;
    }

    public void setDeparture(String departure) {
        this.departure = departure;
    }

    public String getCompanyRoleName() {
        return companyRoleName;
    }

    public void setCompanyRoleName(String companyRoleName) {
        this.companyRoleName = companyRoleName;
    }


    public List<RsLocalCharge> getLocals() {
        return locals;
    }

    public void setLocals(List<RsLocalCharge> locals) {
        this.locals = locals;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public String getCharge() {
        return charge;
    }

    public void setCharge(String charge) {
        this.charge = charge;
    }

    public String getAgreementCode() {
        return agreementCode;
    }

    public void setAgreementCode(String agreementCode) {
        this.agreementCode = agreementCode;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public Long getPolId() {
        return polId;
    }

    public void setPolId(Long polId) {
        this.polId = polId;
    }


    public String getFreightQuery() {
        return freightQuery;
    }

    public void setFreightQuery(String freightQuery) {
        this.freightQuery = freightQuery;
    }

    public String getInquiryNo() {
        return inquiryNo;
    }

    public void setInquiryNo(String inquiryNo) {
        this.inquiryNo = inquiryNo;
    }

    public String getTtUnitCode() {
        return ttUnitCode;
    }

    public void setTtUnitCode(String ttUnitCode) {
        this.ttUnitCode = ttUnitCode;
    }

    public String getLogisticsUnit() {
        return logisticsUnit;
    }

    public void setLogisticsUnit(String logisticsUnit) {
        this.logisticsUnit = logisticsUnit;
    }

    public String getCarrier() {
        return carrier;
    }

    public void setCarrier(String carrier) {
        this.carrier = carrier;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }


    public String getTransitPort() {
        return transitPort;
    }

    public void setTransitPort(String transitPort) {
        this.transitPort = transitPort;
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getLogisticsEfficiencyNode() {
        return logisticsEfficiencyNode;
    }

    public void setLogisticsEfficiencyNode(String logisticsEfficiencyNode) {
        this.logisticsEfficiencyNode = logisticsEfficiencyNode;
    }

    public String getValidPeriodTimeNode() {
        return validPeriodTimeNode;
    }

    public void setValidPeriodTimeNode(String validPeriodTimeNode) {
        this.validPeriodTimeNode = validPeriodTimeNode;
    }

    public String getAgreementNo() {
        return agreementNo;
    }

    public void setAgreementNo(String agreementNo) {
        this.agreementNo = agreementNo;
    }

    public String getCarrierCode() {
        return carrierCode;
    }

    public void setCarrierCode(String carrierCode) {
        this.carrierCode = carrierCode;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public Date getValidFrom() {
        return validFrom;
    }

    public void setValidFrom(Date validFrom) {
        this.validFrom = validFrom;
    }

    public Date getValidTo() {
        return validTo;
    }

    public void setValidTo(Date validTo) {
        this.validTo = validTo;
    }

    public String getIsValid() {
        return isValid;
    }

    public void setIsValid(String isValid) {
        this.isValid = isValid;
    }

    public void setFreightId(Long freightId) {
        this.freightId = freightId;
    }

    public Long getFreightId() {
        return freightId;
    }

    public void setServiceTypeId(Long serviceTypeId) {
        this.serviceTypeId = serviceTypeId;
    }

    public Long getServiceTypeId() {
        return serviceTypeId;
    }

    public void setTransitPortId(Long transitPortId) {
        this.transitPortId = transitPortId;
    }

    public Long getTransitPortId() {
        return transitPortId;
    }

    public Long getDestinationPortId() {
        return destinationPortId;
    }

    public void setDestinationPortId(Long destinationPortId) {
        this.destinationPortId = destinationPortId;
    }

    public void setChargeId(Long chargeId) {
        this.chargeId = chargeId;
    }

    public Long getChargeId() {
        return chargeId;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public Long getUnitId() {
        return unitId;
    }

    public BigDecimal getPriceA() {
        return priceA;
    }

    public void setPriceA(BigDecimal priceA) {
        this.priceA = priceA;
    }

    public BigDecimal getPriceB() {
        return priceB;
    }

    public void setPriceB(BigDecimal priceB) {
        this.priceB = priceB;
    }

    public BigDecimal getPriceC() {
        return priceC;
    }

    public void setPriceC(BigDecimal priceC) {
        this.priceC = priceC;
    }

    public BigDecimal getPriceD() {
        return priceD;
    }

    public void setPriceD(BigDecimal priceD) {
        this.priceD = priceD;
    }

    public void setShippingDate(Date shippingDate) {
        this.shippingDate = shippingDate;
    }

    public Date getShippingDate() {
        return shippingDate;
    }

    public void setShippingWeek(String shippingWeek) {
        this.shippingWeek = shippingWeek;
    }

    public String getShippingWeek() {
        return shippingWeek;
    }

    public void setLogisticsEfficiencyNodeId(Long logisticsEfficiencyNodeId) {
        this.logisticsEfficiencyNodeId = logisticsEfficiencyNodeId;
    }

    public Long getLogisticsEfficiencyNodeId() {
        return logisticsEfficiencyNodeId;
    }

    public void setLogisticsTimeliness(BigDecimal logisticsTimeliness) {
        this.logisticsTimeliness = logisticsTimeliness;
    }

    public BigDecimal getLogisticsTimeliness() {
        return logisticsTimeliness;
    }


    public void setValidPeriodTimeNodeId(Long validPeriodTimeNodeId) {
        this.validPeriodTimeNodeId = validPeriodTimeNodeId;
    }

    public Long getValidPeriodTimeNodeId() {
        return validPeriodTimeNodeId;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public static void main(String[] args) {
        String s = "南沙(CNNNS)";
        System.out.println(s.substring(0, s.indexOf("(")));
    }
}
