<template>
  <view class="bind-container">
    <view class="logo-content align-center justify-center flex">
      <image :src="globalConfig.appInfo.logo" mode="widthFix" style="width: 100rpx;height: 100rpx;">
      </image>
      <text class="title">完善信息</text>
    </view>

    <view class="form-content">
      <!-- 用户头像 -->
      <view class="user-avatar flex align-center justify-center">
        <image :src="userInfo.avatarUrl || '/static/images/profile.jpg'" class="avatar" mode="aspectFill"></image>
      </view>

      <!-- 用户昵称 -->
      <view class="nickname text-center">
        <text>{{ userInfo.nickName || '微信用户' }}</text>
      </view>

      <!-- 绑定表单 -->
      <view class="bind-form">
        <!-- 手机号 -->
        <view class="form-item flex align-center">
          <text class="label">手机号</text>
          <input v-model="form.mobile" class="input" maxlength="11" placeholder="请输入手机号码" type="number"/>
        </view>

        <!-- 验证码 -->
        <view class="form-item flex align-center">
          <text class="label">验证码</text>
          <input v-model="form.code" class="input" maxlength="6" placeholder="请输入验证码" type="number"/>
          <view class="code-btn" @click="getVerifyCode">{{ countDown > 0 ? countDown + 's' : '获取验证码' }}</view>
        </view>

        <!-- 其他可选信息 -->
        <view class="form-item flex align-center">
          <text class="label">昵称</text>
          <input v-model="form.nickname" class="input" placeholder="请输入昵称" type="text"/>
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="action-btns">
        <button class="submit-btn" @click="handleSubmit">完成绑定</button>
      </view>

      <!-- 隐私协议 -->
      <view class="agreement text-center">
        <text class="text-grey1">点击完成绑定，即表示您同意</text>
        <text class="text-blue" @click="handleUserAgrement">《用户协议》</text>
        <text class="text-blue" @click="handlePrivacy">《隐私协议》</text>
      </view>
    </view>
  </view>
</template>

<script>
import {bindWechatUser, getVerifyCode} from '@/api/login'

export default {
  data() {
    return {
      globalConfig: getApp().globalData.config,
      countDown: 0,
      timer: null,
      userInfo: {
        nickName: '',
        avatarUrl: ''
      },
      form: {
        mobile: '',
        code: '',
        nickname: '',
        wechatCode: ''
      }
    }
  },
  onLoad(options) {
    // 接收传递的微信code
    if (options.code) {
      this.form.wechatCode = options.code
    }

    // 获取微信用户信息
    this.getUserProfile()
  },
  onUnload() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  },
  methods: {
    // 获取微信用户信息
    getUserProfile() {
      // #ifdef MP-WEIXIN
      uni.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          this.userInfo = res.userInfo
          this.form.nickname = res.userInfo.nickName
        },
        fail: () => {
          uni.showToast({
            title: '获取用户信息失败',
            icon: 'none'
          })
        }
      })
      // #endif

      // #ifdef APP-PLUS
      uni.getUserInfo({
        provider: 'weixin',
        success: (res) => {
          this.userInfo = res.userInfo
          this.form.nickname = res.userInfo.nickName
        },
        fail: () => {
          uni.showToast({
            title: '获取用户信息失败',
            icon: 'none'
          })
        }
      })
      // #endif
    },

    // 获取验证码
    getVerifyCode() {
      if (this.countDown > 0) return

      // 验证手机号
      if (!this.form.mobile) {
        uni.showToast({
          title: '请输入手机号码',
          icon: 'none'
        })
        return
      }

      if (!/^1[3-9]\d{9}$/.test(this.form.mobile)) {
        uni.showToast({
          title: '手机号码格式不正确',
          icon: 'none'
        })
        return
      }

      // 调用发送验证码接口
      getVerifyCode(this.form.mobile).then(() => {
        uni.showToast({
          title: '验证码已发送',
          icon: 'success'
        })

        // 开始倒计时
        this.countDown = 60
        this.timer = setInterval(() => {
          if (this.countDown > 0) {
            this.countDown--
          } else {
            clearInterval(this.timer)
            this.timer = null
          }
        }, 1000)
      }).catch(err => {
        uni.showToast({
          title: err.message || '验证码发送失败',
          icon: 'none'
        })
      })
    },

    // 提交表单
    handleSubmit() {
      // 表单验证
      if (!this.form.mobile) {
        uni.showToast({
          title: '请输入手机号码',
          icon: 'none'
        })
        return
      }

      if (!this.form.code) {
        uni.showToast({
          title: '请输入验证码',
          icon: 'none'
        })
        return
      }

      if (!this.form.nickname) {
        uni.showToast({
          title: '请输入昵称',
          icon: 'none'
        })
        return
      }

      uni.showLoading({
        title: '提交中...'
      })

      // 提交绑定信息
      bindWechatUser({
        mobile: this.form.mobile,
        code: this.form.code,
        nickname: this.form.nickname,
        wechatCode: this.form.wechatCode,
        avatar: this.userInfo.avatarUrl
      }).then(res => {
        uni.hideLoading()

        // 绑定成功，设置token
        this.$store.commit('SET_TOKEN', res.token)

        // 获取用户信息
        this.$store.dispatch('GetInfo').then(() => {
          uni.showToast({
            title: '绑定成功',
            icon: 'success',
            duration: 1500,
            success: () => {
              setTimeout(() => {
                this.$tab.reLaunch('/pages/index')
              }, 1500)
            }
          })
        })
      }).catch(err => {
        uni.hideLoading()
        uni.showToast({
          title: err.message || '绑定失败',
          icon: 'none'
        })
      })
    },

    // 用户协议
    handleUserAgrement() {
      let site = this.globalConfig.appInfo.agreements[1]
      this.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)
    },

    // 隐私协议
    handlePrivacy() {
      let site = this.globalConfig.appInfo.agreements[0]
      this.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)
    }
  }
}
</script>

<style lang="scss" scoped>
page {
  background-color: #ffffff;
}

.bind-container {
  width: 100%;

  .logo-content {
    width: 100%;
    font-size: 21px;
    text-align: center;
    padding-top: 10%;

    image {
      border-radius: 4px;
    }

    .title {
      margin-left: 10px;
    }
  }

  .form-content {
    width: 85%;
    margin: 20px auto;

    .user-avatar {
      margin-top: 30px;
      margin-bottom: 10px;

      .avatar {
        width: 150rpx;
        height: 150rpx;
        border-radius: 50%;
      }
    }

    .nickname {
      font-size: 18px;
      margin-bottom: 30px;
    }

    .bind-form {
      margin-top: 20px;

      .form-item {
        margin-bottom: 20px;
        height: 90rpx;
        border-bottom: 1px solid #f5f5f5;

        .label {
          width: 150rpx;
          font-size: 16px;
        }

        .input {
          flex: 1;
          font-size: 16px;
        }

        .code-btn {
          width: 200rpx;
          text-align: center;
          color: #2979ff;
          font-size: 14px;
        }
      }
    }

    .action-btns {
      margin-top: 50px;

      .submit-btn {
        background-color: #2979ff;
        color: #ffffff;
        border-radius: 45rpx;
        height: 90rpx;
        line-height: 90rpx;
        font-size: 16px;
      }
    }

    .agreement {
      margin-top: 30px;
      font-size: 14px;
      color: #999;
    }
  }
}
</style> 