package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * @TableName bas_dict_type
 */
public class BasDictType extends BaseEntity {
    /**
     * 字典主键
     */
    @Excel(name = "字典主键", cellType = Excel.ColumnType.NUMERIC)
    private Long dictTypeId;

    /**
     * 字典名称
     */
    @Excel(name = "字典名称")
    private String dictName;

    /**
     * 字典类型
     */
    @Excel(name = "字典类型")
    private String dictType;

    /**
     * 状态（0正常 1停用）
     */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    public Long getDictTypeId() {
        return dictTypeId;
    }

    /**
     *
     */
    public void setDictTypeId(Long dictTypeId) {
        this.dictTypeId = dictTypeId;
    }

    /**
     * 字典名称
     */
    @NotBlank(message = "字典名称不能为空")
    @Size(min = 0, max = 100, message = "字典类型名称长度不能超过100个字符")
    public String getDictName() {
        return dictName;
    }

    /**
     * 字典名称
     */
    public void setDictName(String dictName) {
        this.dictName = dictName;
    }

    /**
     * 字典类型
     */
    @NotBlank(message = "字典类型不能为空")
    @Size(min = 0, max = 100, message = "字典类型类型长度不能超过100个字符")
    @Pattern(regexp = "^[a-z][a-z0-9_]*$", message = "字典类型必须以字母开头，且只能为（小写字母，数字，下滑线）")
    public String getDictType() {
        return dictType;
    }

    /**
     * 字典类型
     */
    public void setDictType(String dictType) {
        this.dictType = dictType;
    }

    /**
     * 状态（0正常 1停用）
     */
    public String getStatus() {
        return status;
    }

    /**
     * 状态（0正常 1停用）
     */
    public void setStatus(String status) {
        this.status = status;
    }


    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BasDictType other = (BasDictType) that;
        return (this.getDictTypeId() == null ? other.getDictTypeId() == null : this.getDictTypeId().equals(other.getDictTypeId()))
                && (this.getDictName() == null ? other.getDictName() == null : this.getDictName().equals(other.getDictName()))
                && (this.getDictType() == null ? other.getDictType() == null : this.getDictType().equals(other.getDictType()))
                && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getDictTypeId() == null) ? 0 : getDictTypeId().hashCode());
        result = prime * result + ((getDictName() == null) ? 0 : getDictName().hashCode());
        result = prime * result + ((getDictType() == null) ? 0 : getDictType().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        return result;
    }

    @Override
    public String toString() {
        String sb = getClass().getSimpleName() +
                " [" +
                "Hash = " + hashCode() +
                ", dictTypeId=" + dictTypeId +
                ", dictName=" + dictName +
                ", dictType=" + dictType +
                ", status=" + status +
                ", serialVersionUID=" + serialVersionUID +
                "]";
        return sb;
    }
}