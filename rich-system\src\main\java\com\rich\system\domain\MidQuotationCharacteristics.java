package com.rich.system.domain;

import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 【请填写功能名称】对象 mid_quotation_characteristics
 *
 * <AUTHOR>
 * @date 2023-05-26
 */
public class MidQuotationCharacteristics extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long quotationId;
    private Long characteristicsId;

    public void setQuotationId(Long quotationId) {
        this.quotationId = quotationId;
    }

    public Long getQuotationId() {
        return quotationId;
    }

    public void setCharacteristicsId(Long characteristicsId) {
        this.characteristicsId = characteristicsId;
    }

    public Long getCharacteristicsId() {
        return characteristicsId;
    }

}
