package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.RsBooking;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasDistLocationService;
import com.rich.system.service.RsBookingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 订舱单列表Controller
 *
 * <AUTHOR>
 * @date 2023-08-15
 */
@RestController
@RequestMapping("/system/booking")
public class RsBookingController extends BaseController {
    @Autowired
    private RsBookingService rsBookingService;

    @Autowired
    private BasDistLocationService basDistLocationService;

    /**
     * 查询订舱单列表列表
     */
    @PreAuthorize("@ss.hasPermi('system:booking:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsBooking rsBooking) {
        List<RsBooking> list = rsBookingService.selectRsBookingList(rsBooking);
        if (list != null && list.size() > 0) {
            return getDataTable(list);
        } else {
            return null;
        }
    }

    @PreAuthorize("@ss.hasPermi('system:psa:list')")
    @GetMapping("/psalist")
    public TableDataInfo psalist(RsBooking rsBooking) {
        List<RsBooking> list = rsBookingService.selectPsaRsBookingList(rsBooking);
        if (list != null && list.size() > 0) {
            return getDataTable(list);
        } else {
            return null;
        }
    }

    @GetMapping("/mon")
    public AjaxResult getMon() {
        return AjaxResult.success(rsBookingService.getMon());
    }

    /**
     * 导出订舱单列表列表
     */
    @PreAuthorize("@ss.hasPermi('system:booking:export')")
    @Log(title = "订舱单列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsBooking rsBooking) {
        List<RsBooking> list = rsBookingService.selectRsBookingList(rsBooking);
        ExcelUtil<RsBooking> util = new ExcelUtil<RsBooking>(RsBooking.class);
        util.exportExcel(response, list, "订舱单列表数据");
    }

    /**
     * 获取订舱单列表详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:booking:query')")
    @GetMapping(value = "/{bookingId}")
    public AjaxResult getInfo(@PathVariable("bookingId") Long bookingId) {
        // 设置了订舱单相关的各个服务，如前程运输、基础物流等
        RsBooking rsBooking = rsBookingService.selectRsBookingByBookingId(bookingId);
        AjaxResult ajaxResult = AjaxResult.success();
        Set<Long> set = new HashSet<>();
        rsBooking.setCarrierIds(rsBookingService.getCarrierIds(bookingId).toArray(new Long[0]));
        rsBooking.setCargoTypeIds(rsBookingService.getCargoTypeIds(bookingId).toArray(new Long[0]));
        rsBooking.setServiceTypeIds(rsBookingService.getServiceTypeIds(bookingId).toArray(new Long[0]));
        if (rsBooking.getPolId() != null) {
            set.add(rsBooking.getPolId());
        }
        if (rsBooking.getDestinationPortId() != null) {
            set.add(rsBooking.getDestinationPortId());
        }
        if (rsBooking.getRsBookingLogisticsTypeBasicInfo() != null) {
            if (rsBooking.getRsBookingLogisticsTypeBasicInfo().getPolId() != null) {
                set.add(rsBooking.getRsBookingLogisticsTypeBasicInfo().getPolId());
            }
            if (rsBooking.getRsBookingLogisticsTypeBasicInfo().getLocalBasicPortId() != null) {
                set.add(rsBooking.getRsBookingLogisticsTypeBasicInfo().getLocalBasicPortId());
            }
            if (rsBooking.getRsBookingLogisticsTypeBasicInfo().getTransitPortId() != null) {
                set.add(rsBooking.getRsBookingLogisticsTypeBasicInfo().getTransitPortId());
            }
            if (rsBooking.getRsBookingLogisticsTypeBasicInfo().getPodId() != null) {
                set.add(rsBooking.getRsBookingLogisticsTypeBasicInfo().getPodId());
            }
            if (rsBooking.getRsBookingLogisticsTypeBasicInfo().getDestinationPortId() != null) {
                set.add(rsBooking.getRsBookingLogisticsTypeBasicInfo().getDestinationPortId());
            }
        }

        if (rsBooking.getRsBookingPreCarriageBasicInfo() != null && rsBooking.getRsBookingPreCarriageBasicInfo().getPreCarriageRegionId() != null) {
            set.add(rsBooking.getRsBookingPreCarriageBasicInfo().getPreCarriageRegionId());
        }
        if (rsBooking.getRsBookingExportDeclarationBasicInfo() != null && rsBooking.getRsBookingExportDeclarationBasicInfo().getDispatchRegionId() != null) {
            set.add(rsBooking.getRsBookingExportDeclarationBasicInfo().getDispatchRegionId());
        }
        ajaxResult.put(AjaxResult.DATA_TAG, rsBooking);
        ajaxResult.put("locationOptions", set.size() > 0 ? basDistLocationService.selectBasDistLocationByIds(set) : null);
        return ajaxResult;
    }

    /**
     * 新增订舱单列表
     */
    @PreAuthorize("@ss.hasPermi('system:booking:add')")
    @Log(title = "订舱单列表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsBooking rsBooking) {
        return AjaxResult.success(rsBookingService.insertRsBooking(rsBooking));
    }

    /**
     * 修改订舱单列表
     */
    @PreAuthorize("@ss.hasPermi('system:booking:edit')")
    @Log(title = "订舱单列表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsBooking rsBooking) {
        return toAjax(rsBookingService.updateRsBooking(rsBooking));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:booking:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsBooking rsBooking) {
        rsBooking.setUpdateBy(getUserId());
        return toAjax(rsBookingService.changeStatus(rsBooking));
    }

    /**
     * 删除订舱单列表
     */
    @PreAuthorize("@ss.hasPermi('system:booking:remove')")
    @Log(title = "订舱单列表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{bookingIds}")
    public AjaxResult remove(@PathVariable Long[] bookingIds) {
        return toAjax(rsBookingService.deleteRsBookingByBookingIds(bookingIds));
    }

    @PreAuthorize("@ss.hasAnyPermi('system:booking:add,system:booking:edit')")
    @PostMapping("/saveBookingLogistics")
    public AjaxResult saveBookingLogistics(@RequestBody RsBooking rsBooking) {
        return toAjax(rsBookingService.saveBookingLogistics(rsBooking));
    }

    @PreAuthorize("@ss.hasAnyPermi('system:booking:add,system:booking:edit')")
    @PostMapping("/saveBookingPreCarriage")
    public AjaxResult saveBookingPreCarriage(@RequestBody RsBooking rsBooking) {
        return toAjax(rsBookingService.saveBookingPreCarriage(rsBooking));
    }

    @PreAuthorize("@ss.hasAnyPermi('system:booking:add,system:booking:edit')")
    @PostMapping("/saveBookingExportDeclaration")
    public AjaxResult saveBookingExportDeclaration(@RequestBody RsBooking rsBooking) {
        return toAjax(rsBookingService.saveBookingExportDeclaration(rsBooking));
    }

    @PreAuthorize("@ss.hasAnyPermi('system:booking:add,system:booking:edit')")
    @PostMapping("/saveBookingImportClearance")
    public AjaxResult saveBookingImportClearance(@RequestBody RsBooking rsBooking) {
        return toAjax(rsBookingService.saveBookingImportClearance(rsBooking));
    }
}
