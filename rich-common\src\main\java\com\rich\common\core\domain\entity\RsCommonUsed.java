package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

/**
 * 常用信息对象 rs_common_used
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
public class RsCommonUsed extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 常用信息
     */
    private Long commonUsedId;

    /**
     * 客户信息
     */
    @Excel(name = "客户信息")
    private Long clientId;
    private String client;

    /**
     * 服务类型
     */
    @Excel(name = "服务类型")
    private Long serviceTypeId;
    private String serviceType;

    /**
     * 装运区域
     */
    @Excel(name = "装运区域")
    private Long precarriageRegionId;
    private String precarriageRegion;

    /**
     * 启运港
     */
    @Excel(name = "启运港")
    private Long polId;
    private String pol;

    /**
     * 目的港
     */
    @Excel(name = "目的港")
    private Long destinationPortId;
    private String destinationPort;

    /**
     * 派送区域
     */
    @Excel(name = "派送区域")
    private Long dispatchRegionId;
    private String dispatchRegion;

    /**
     * 发货人简称
     */
    @Excel(name = "发货人简称")
    private String shipperShortName;

    /**
     * 发货人明细
     */
    @Excel(name = "发货人明细")
    private String shipperDetails;

    /**
     * 发货人简称
     */
    @Excel(name = "收货人简称")
    private String consigneeShortName;

    /**
     * 发货人明细
     */
    @Excel(name = "收货人明细")
    private String consigneeDetails;

    /**
     * 发货人简称
     */
    @Excel(name = "通知人简称")
    private String notifyPartyShortName;

    /**
     * 发货人明细
     */
    @Excel(name = "通知人明细")
    private String notifyPartyDetails;

    /**
     * 装运详址
     */
    @Excel(name = "装运详址")
    private String precarriageAddress;

    /**
     * 装运联系人
     */
    @Excel(name = "装运联系人")
    private String precarriageContact;

    /**
     * 装运电话
     */
    @Excel(name = "装运电话")
    private String precarriageTel;

    /**
     * 装运备注
     */
    @Excel(name = "装运备注")
    private String precarriageRemark;

    /**
     * 派送区域
     */
    @Excel(name = "派送区域")
    private String dispatchAddress;

    /**
     * 派送联系人
     */
    @Excel(name = "派送联系人")
    private String dispatchContact;

    /**
     * 派送电话
     */
    @Excel(name = "派送电话")
    private String dispatchTel;

    /**
     * 派送备注
     */
    @Excel(name = "派送备注")
    private String dispatchRemark;

    public String getClient() {
        return client;
    }

    public void setClient(String client) {
        this.client = client;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public String getPrecarriageRegion() {
        return precarriageRegion;
    }

    public void setPrecarriageRegion(String precarriageRegion) {
        this.precarriageRegion = precarriageRegion;
    }

    public String getPol() {
        return pol;
    }

    public void setPol(String pol) {
        this.pol = pol;
    }

    public String getDestinationPort() {
        return destinationPort;
    }

    public void setDestinationPort(String destinationPort) {
        this.destinationPort = destinationPort;
    }

    public String getDispatchRegion() {
        return dispatchRegion;
    }

    public void setDispatchRegion(String dispatchRegion) {
        this.dispatchRegion = dispatchRegion;
    }

    public void setCommonUsedId(Long commonUsedId) {
        this.commonUsedId = commonUsedId;
    }

    public Long getCommonUsedId() {
        return commonUsedId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setServiceTypeId(Long serviceTypeId) {
        this.serviceTypeId = serviceTypeId;
    }

    public Long getServiceTypeId() {
        return serviceTypeId;
    }

    public void setPrecarriageRegionId(Long precarriageRegionId) {
        this.precarriageRegionId = precarriageRegionId;
    }

    public Long getPrecarriageRegionId() {
        return precarriageRegionId;
    }

    public void setPolId(Long polId) {
        this.polId = polId;
    }

    public Long getPolId() {
        return polId;
    }

    public void setDestinationPortId(Long destinationPortId) {
        this.destinationPortId = destinationPortId;
    }

    public Long getDestinationPortId() {
        return destinationPortId;
    }

    public void setDispatchRegionId(Long dispatchRegionId) {
        this.dispatchRegionId = dispatchRegionId;
    }

    public Long getDispatchRegionId() {
        return dispatchRegionId;
    }

    public void setShipperShortName(String shipperShortName) {
        this.shipperShortName = shipperShortName;
    }

    public String getShipperShortName() {
        return shipperShortName;
    }

    public void setShipperDetails(String shipperDetails) {
        this.shipperDetails = shipperDetails;
    }

    public String getShipperDetails() {
        return shipperDetails;
    }

    public void setConsigneeShortName(String consigneeShortName) {
        this.consigneeShortName = consigneeShortName;
    }

    public String getConsigneeShortName() {
        return consigneeShortName;
    }

    public void setConsigneeDetails(String consigneeDetails) {
        this.consigneeDetails = consigneeDetails;
    }

    public String getConsigneeDetails() {
        return consigneeDetails;
    }

    public void setNotifyPartyShortName(String notifyPartyShortName) {
        this.notifyPartyShortName = notifyPartyShortName;
    }

    public String getNotifyPartyShortName() {
        return notifyPartyShortName;
    }

    public void setNotifyPartyDetails(String notifyPartyDetails) {
        this.notifyPartyDetails = notifyPartyDetails;
    }

    public String getNotifyPartyDetails() {
        return notifyPartyDetails;
    }

    public void setPrecarriageAddress(String precarriageAddress) {
        this.precarriageAddress = precarriageAddress;
    }

    public String getPrecarriageAddress() {
        return precarriageAddress;
    }

    public void setPrecarriageContact(String precarriageContact) {
        this.precarriageContact = precarriageContact;
    }

    public String getPrecarriageContact() {
        return precarriageContact;
    }

    public void setPrecarriageTel(String precarriageTel) {
        this.precarriageTel = precarriageTel;
    }

    public String getPrecarriageTel() {
        return precarriageTel;
    }

    public void setPrecarriageRemark(String precarriageRemark) {
        this.precarriageRemark = precarriageRemark;
    }

    public String getPrecarriageRemark() {
        return precarriageRemark;
    }

    public void setDispatchAddress(String dispatchAddress) {
        this.dispatchAddress = dispatchAddress;
    }

    public String getDispatchAddress() {
        return dispatchAddress;
    }

    public void setDispatchContact(String dispatchContact) {
        this.dispatchContact = dispatchContact;
    }

    public String getDispatchContact() {
        return dispatchContact;
    }

    public void setDispatchTel(String dispatchTel) {
        this.dispatchTel = dispatchTel;
    }

    public String getDispatchTel() {
        return dispatchTel;
    }

    public void setDispatchRemark(String dispatchRemark) {
        this.dispatchRemark = dispatchRemark;
    }

    public String getDispatchRemark() {
        return dispatchRemark;
    }

}
