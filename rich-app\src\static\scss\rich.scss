/* 表单区域默认背景色 */
.form-table {
  .uni-easyinput__content-input, .uni-date-x, .uni-date-editor--x, .uni-select {
    background-color: rgb(255, 242, 204);
    width: 100%;
  }

  .uni-easyinput__content-textarea, .easyinput--uni-easyinput__content-textarea {
    margin: 0;
    background-color: rgb(255, 242, 204);
    width: 100%;
  }
}

/* 针对禁用状态的组件样式，优先级更高 */
.form-table {
  /* 禁用的文本域 */
  .is-disabled, .easyinput--is-disabled {
    .uni-easyinput__content-input, .easyinput--uni-easyinput__content-input {
      background-color: #f5f5f5 !important;
      block-size: 500;
      color: black;
    }
  }

  .uni-select--disabled {
    background-color: #f5f5f5 !important;
    block-size: 500;
    color: black;
  }

  .is-disabled {
    .uni-easyinput__content-textarea, .input-padding {
      background-color: #f5f5f5 !important;
      block-size: 500;
      color: black;
    }
  }
}
