package com.rich.system.domain;

import com.rich.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 mid_company_service_type
 *
 * <AUTHOR>
 * @date 2022-09-07
 */
public class MidServiceType extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long serviceTypeId;

    private Long belongId;

    private String belongTo;

    public Long getBelongId() {
        return belongId;
    }

    public void setBelongId(Long belongId) {
        this.belongId = belongId;
    }

    public String getBelongTo() {
        return belongTo;
    }

    public void setBelongTo(String belongTo) {
        this.belongTo = belongTo;
    }

    public Long getServiceTypeId() {
        return serviceTypeId;
    }

    public void setServiceTypeId(Long serviceTypeId) {
        this.serviceTypeId = serviceTypeId;
    }


}
