<component name="libraryTable">
  <library name="elasticsearch" type="repository">
    <properties maven-id="org.elasticsearch:elasticsearch:7.12.1" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/elasticsearch/elasticsearch/7.12.1/elasticsearch-7.12.1.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/org/elasticsearch/elasticsearch-core/7.12.1/elasticsearch-core-7.12.1.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/org/elasticsearch/elasticsearch-secure-sm/7.12.1/elasticsearch-secure-sm-7.12.1.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/org/elasticsearch/elasticsearch-x-content/7.12.1/elasticsearch-x-content-7.12.1.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/org/yaml/snakeyaml/1.26/snakeyaml-1.26.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/com/fasterxml/jackson/core/jackson-core/2.10.4/jackson-core-2.10.4.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/com/fasterxml/jackson/dataformat/jackson-dataformat-smile/2.10.4/jackson-dataformat-smile-2.10.4.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.10.4/jackson-dataformat-yaml-2.10.4.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/com/fasterxml/jackson/dataformat/jackson-dataformat-cbor/2.10.4/jackson-dataformat-cbor-2.10.4.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/org/elasticsearch/elasticsearch-geo/7.12.1/elasticsearch-geo-7.12.1.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/org/apache/lucene/lucene-core/8.8.0/lucene-core-8.8.0.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/org/apache/lucene/lucene-analyzers-common/8.8.0/lucene-analyzers-common-8.8.0.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/org/apache/lucene/lucene-backward-codecs/8.8.0/lucene-backward-codecs-8.8.0.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/org/apache/lucene/lucene-grouping/8.8.0/lucene-grouping-8.8.0.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/org/apache/lucene/lucene-highlighter/8.8.0/lucene-highlighter-8.8.0.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/org/apache/lucene/lucene-join/8.8.0/lucene-join-8.8.0.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/org/apache/lucene/lucene-memory/8.8.0/lucene-memory-8.8.0.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/org/apache/lucene/lucene-misc/8.8.0/lucene-misc-8.8.0.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/org/apache/lucene/lucene-queries/8.8.0/lucene-queries-8.8.0.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/org/apache/lucene/lucene-queryparser/8.8.0/lucene-queryparser-8.8.0.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/org/apache/lucene/lucene-sandbox/8.8.0/lucene-sandbox-8.8.0.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/org/apache/lucene/lucene-spatial-extras/8.8.0/lucene-spatial-extras-8.8.0.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/org/apache/lucene/lucene-spatial3d/8.8.0/lucene-spatial3d-8.8.0.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/org/apache/lucene/lucene-suggest/8.8.0/lucene-suggest-8.8.0.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/org/elasticsearch/elasticsearch-cli/7.12.1/elasticsearch-cli-7.12.1.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/net/sf/jopt-simple/jopt-simple/5.0.2/jopt-simple-5.0.2.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/com/carrotsearch/hppc/0.8.1/hppc-0.8.1.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/joda-time/joda-time/2.10.4/joda-time-2.10.4.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/com/tdunning/t-digest/3.2/t-digest-3.2.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/org/hdrhistogram/HdrHistogram/2.1.9/HdrHistogram-2.1.9.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/org/apache/logging/log4j/log4j-api/2.11.1/log4j-api-2.11.1.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/org/elasticsearch/jna/5.7.0-1/jna-5.7.0-1.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/org/elasticsearch/elasticsearch-plugin-classloader/7.12.1/elasticsearch-plugin-classloader-7.12.1.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>