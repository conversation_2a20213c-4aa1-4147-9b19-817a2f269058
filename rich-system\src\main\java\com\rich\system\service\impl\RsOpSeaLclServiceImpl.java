package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpSeaLcl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsOpSeaLclMapper;
import com.rich.system.service.RsOpSeaLclService;

/**
 * 拼柜海运服务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Service
public class RsOpSeaLclServiceImpl implements RsOpSeaLclService {
    @Autowired
    private RsOpSeaLclMapper rsOpSeaLclMapper;

    /**
     * 查询拼柜海运服务
     *
     * @param seaLclId 拼柜海运服务主键
     * @return 拼柜海运服务
     */
    @Override
    public RsOpSeaLcl selectRsOpSeaLclBySeaLclId(Long seaLclId) {
        return rsOpSeaLclMapper.selectRsOpSeaLclBySeaLclId(seaLclId);
    }

    /**
     * 查询拼柜海运服务列表
     *
     * @param rsOpSeaLcl 拼柜海运服务
     * @return 拼柜海运服务
     */
    @Override
    public List<RsOpSeaLcl> selectRsOpSeaLclList(RsOpSeaLcl rsOpSeaLcl) {
        return rsOpSeaLclMapper.selectRsOpSeaLclList(rsOpSeaLcl);
    }

    /**
     * 新增拼柜海运服务
     *
     * @param rsOpSeaLcl 拼柜海运服务
     * @return 结果
     */
    @Override
    public int insertRsOpSeaLcl(RsOpSeaLcl rsOpSeaLcl) {
        return rsOpSeaLclMapper.insertRsOpSeaLcl(rsOpSeaLcl);
    }

    /**
     * 修改拼柜海运服务
     *
     * @param rsOpSeaLcl 拼柜海运服务
     * @return 结果
     */
    @Override
    public int updateRsOpSeaLcl(RsOpSeaLcl rsOpSeaLcl) {
        return rsOpSeaLclMapper.updateRsOpSeaLcl(rsOpSeaLcl);
    }

    /**
     * 修改拼柜海运服务状态
     *
     * @param rsOpSeaLcl 拼柜海运服务
     * @return 拼柜海运服务
     */
    @Override
    public int changeStatus(RsOpSeaLcl rsOpSeaLcl) {
        return rsOpSeaLclMapper.updateRsOpSeaLcl(rsOpSeaLcl);
    }

    /**
     * 批量删除拼柜海运服务
     *
     * @param seaLclIds 需要删除的拼柜海运服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpSeaLclBySeaLclIds(Long[] seaLclIds) {
        return rsOpSeaLclMapper.deleteRsOpSeaLclBySeaLclIds(seaLclIds);
    }

    /**
     * 删除拼柜海运服务信息
     *
     * @param seaLclId 拼柜海运服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpSeaLclBySeaLclId(Long seaLclId) {
        return rsOpSeaLclMapper.deleteRsOpSeaLclBySeaLclId(seaLclId);
    }
}
