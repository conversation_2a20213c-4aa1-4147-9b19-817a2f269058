package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

/**
 * 进程名称对象 bas_process
 *
 * <AUTHOR>
 * @date 2023-06-05
 */
public class BasProcess extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 进程名称表
     */
    private Long processId;

    /**
     * 进度分类ID
     */
    @Excel(name = "进度分类ID")
    private Long processTypeId;
    private String processType;

    /**
     * 服务类型
     */
    @Excel(name = "服务类型")
    private Long serviceTypeId;
    private String serviceType;

    /**
     * 简称
     */
    @Excel(name = "简称")
    private String processShortName;

    /**
     * 全称
     */
    @Excel(name = "全称")
    private String processLocalName;

    /**
     * 英文名
     */
    @Excel(name = "英文名")
    private String processEnName;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Integer orderNum;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;

    private String processQuery;

    public String getProcessQuery() {
        return processQuery;
    }

    public void setProcessQuery(String processQuery) {
        this.processQuery = processQuery;
    }

    public String getProcessType() {
        return processType;
    }

    public void setProcessType(String processType) {
        this.processType = processType;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public void setProcessId(Long processId) {
        this.processId = processId;
    }

    public Long getProcessId() {
        return processId;
    }

    public void setProcessTypeId(Long processTypeId) {
        this.processTypeId = processTypeId;
    }

    public Long getProcessTypeId() {
        return processTypeId;
    }

    public void setServiceTypeId(Long serviceTypeId) {
        this.serviceTypeId = serviceTypeId;
    }

    public Long getServiceTypeId() {
        return serviceTypeId;
    }

    public void setProcessShortName(String processShortName) {
        this.processShortName = processShortName;
    }

    public String getProcessShortName() {
        return processShortName;
    }

    public void setProcessLocalName(String processLocalName) {
        this.processLocalName = processLocalName;
    }

    public String getProcessLocalName() {
        return processLocalName;
    }

    public void setProcessEnName(String processEnName) {
        this.processEnName = processEnName;
    }

    public String getProcessEnName() {
        return processEnName;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
}
