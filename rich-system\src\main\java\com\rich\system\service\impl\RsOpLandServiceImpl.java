package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpLand;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsOpLandMapper;
import com.rich.system.service.RsOpLandService;

/**
 * 陆运服务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Service
public class RsOpLandServiceImpl implements RsOpLandService {
    @Autowired
    private RsOpLandMapper rsOpLandMapper;

    /**
     * 查询陆运服务
     *
     * @param landId 陆运服务主键
     * @return 陆运服务
     */
    @Override
    public RsOpLand selectRsOpLandByLandId(Long landId) {
        return rsOpLandMapper.selectRsOpLandByLandId(landId);
    }

    /**
     * 查询陆运服务列表
     *
     * @param rsOpLand 陆运服务
     * @return 陆运服务
     */
    @Override
    public List<RsOpLand> selectRsOpLandList(RsOpLand rsOpLand) {
        return rsOpLandMapper.selectRsOpLandList(rsOpLand);
    }

    /**
     * 新增陆运服务
     *
     * @param rsOpLand 陆运服务
     * @return 结果
     */
    @Override
    public int insertRsOpLand(RsOpLand rsOpLand) {
        return rsOpLandMapper.insertRsOpLand(rsOpLand);
    }

    /**
     * 修改陆运服务
     *
     * @param rsOpLand 陆运服务
     * @return 结果
     */
    @Override
    public int updateRsOpLand(RsOpLand rsOpLand) {
        return rsOpLandMapper.updateRsOpLand(rsOpLand);
    }

    /**
     * 修改陆运服务状态
     *
     * @param rsOpLand 陆运服务
     * @return 陆运服务
     */
    @Override
    public int changeStatus(RsOpLand rsOpLand) {
        return rsOpLandMapper.updateRsOpLand(rsOpLand);
    }

    /**
     * 批量删除陆运服务
     *
     * @param landIds 需要删除的陆运服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpLandByLandIds(Long[] landIds) {
        return rsOpLandMapper.deleteRsOpLandByLandIds(landIds);
    }

    /**
     * 删除陆运服务信息
     *
     * @param landId 陆运服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpLandByLandId(Long landId) {
        return rsOpLandMapper.deleteRsOpLandByLandId(landId);
    }
}
