package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasDistDept;
import com.rich.common.core.domain.entity.MidRsStaffRole;
import com.rich.common.core.domain.entity.RsStaff;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 权限分配Mapper接口
 *
 * <AUTHOR>
 * @date 2022-09-06
 */
@Mapper
public interface MidRsStaffRoleMapper {
    /**
     * 查询权限分配
     *
     * @param staffRoleDeptId 权限分配主键
     * @return 权限分配
     */
    MidRsStaffRole selectMidRsStaffRoleByStaffRoleDeptId(Long staffRoleDeptId);

    /**
     * 通过角色ID查询角色使用数量
     *
     * @param roleId 角色ID
     * @return 结果
     */
    int countUserRoleByRoleId(Long roleId);

    /**
     * 查询权限分配列表
     *
     * @param midRsStaffRole 权限分配
     * @return 权限分配集合
     */
    List<MidRsStaffRole> selectMidRsStaffRoleList(MidRsStaffRole midRsStaffRole);

    /**
     * 查询自己管理部门下的员工角色
     *
     * @param midRsStaffRole
     * @return
     */
    List<MidRsStaffRole> queryMidRsStaffRoleList(MidRsStaffRole midRsStaffRole);

    /**
     * 新增权限分配
     *
     * @param midRsStaffRole 权限分配
     * @return 结果
     */
    int insertMidRsStaffRole(MidRsStaffRole midRsStaffRole);

    /**
     * 修改权限分配
     *
     * @param midRsStaffRole 权限分配
     * @return 结果
     */
    int updateMidRsStaffRole(MidRsStaffRole midRsStaffRole);

    /**
     * 删除权限分配
     *
     * @param staffId 权限分配主键
     * @return 结果
     */
    int deleteUserRoleByUserId(Long staffId);

    /**
     * 批量删除权限分配
     *
     * @param staffRoleDeptIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteUserRole(Long[] staffRoleDeptIds);

    /**
     * 删除用户和角色关联信息
     *
     * @param userRole 用户和角色关联信息
     * @return 结果
     */
    int deleteUserRoleInfo(MidRsStaffRole userRole);

    List<MidRsStaffRole> selectSales(BasDistDept deptName);

    List<MidRsStaffRole> selectBusinesses(BasDistDept deptName);

    List<RsStaff> selectStaffsByRoleIds(Long[] roleIds);

    List<Long> selectStaffsByRoleId(Long[] roleIds);

    Long[] getUnderManagerDeptIds(Long userId);

    /**
     * 根据部门id查找该部门下用户有多少个角色
     *
     * @param deptId
     * @return
     */
    int countRoleByDeptId(@Param("deptId") Long deptId, @Param("userId") Long userId);
}
