package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasMessageType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-02-21
 */
@Mapper
public interface BasMessageTypeMapper {
    /**
     * 查询【请填写功能名称】
     *
     * @param messageTypeId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    BasMessageType selectBasMessageTypeByMessageTypeId(Long messageTypeId);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param basMessageType 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    List<BasMessageType> selectBasMessageTypeList(BasMessageType basMessageType);

    /**
     * 新增【请填写功能名称】
     *
     * @param basMessageType 【请填写功能名称】
     * @return 结果
     */
    int insertBasMessageType(BasMessageType basMessageType);

    /**
     * 修改【请填写功能名称】
     *
     * @param basMessageType 【请填写功能名称】
     * @return 结果
     */
    int updateBasMessageType(BasMessageType basMessageType);

    /**
     * 删除【请填写功能名称】
     *
     * @param messageTypeId 【请填写功能名称】主键
     * @return 结果
     */
    int deleteBasMessageTypeByMessageTypeId(Long messageTypeId);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param messageTypeIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasMessageTypeByMessageTypeIds(Long[] messageTypeIds);
}
