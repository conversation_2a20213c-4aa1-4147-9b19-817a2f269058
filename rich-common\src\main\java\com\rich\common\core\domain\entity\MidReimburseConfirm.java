package com.rich.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 【请填写功能名称】对象 mid_reimburse_confirm
 *
 * <AUTHOR>
 * @date 2023-04-03
 */
public class MidReimburseConfirm extends BaseEntity {
    private static final long serialVersionUID = 1L;


    private Long reimburseId;
    private Long staffId;

    private String staffName;

    private String reimburseConfirmType;


    private String reimburseConfirm;


    private String reimburseConfirmContent;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public void setReimburseId(Long reimburseId) {
        this.reimburseId = reimburseId;
    }

    public Long getReimburseId() {
        return reimburseId;
    }

    public void setReimburseConfirmType(String reimburseConfirmType) {
        this.reimburseConfirmType = reimburseConfirmType;
    }

    public String getReimburseConfirmType() {
        return reimburseConfirmType;
    }

    public void setReimburseConfirm(String reimburseConfirm) {
        this.reimburseConfirm = reimburseConfirm;
    }

    public String getReimburseConfirm() {
        return reimburseConfirm;
    }

    public void setReimburseConfirmContent(String reimburseConfirmContent) {
        this.reimburseConfirmContent = reimburseConfirmContent;
    }

    public String getReimburseConfirmContent() {
        return reimburseConfirmContent;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("reimburseId", getReimburseId())
                .append("reimburseConfirmType", getReimburseConfirmType())
                .append("reimburseConfirm", getReimburseConfirm())
                .append("reimburseConfirmContent", getReimburseConfirmContent())
                .append("createDate", getCreateDate())
                .toString();
    }
}
