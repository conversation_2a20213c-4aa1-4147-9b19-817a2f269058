package com.rich.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 消息通知对象 rs_message
 *
 * <AUTHOR>
 * @date 2023-02-15
 */
public class RsMessage extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 信息
     */
    private Long messageId;

    /**
     * 信息所属
     */
    @Excel(name = "信息所属")
    private String messageOwner;

    /**
     * 信息类型
     */
    @Excel(name = "信息类型")
    private Long messageType;

    private String messageTypeName;

    /**
     * 信息来源
     */
    @Excel(name = "信息来源")
    private Long messageFrom;

    private String messageFromName;

    /**
     * 标题
     */
    @Excel(name = "标题")
    private String messageTitle;

    /**
     * 详细内容
     */
    @Excel(name = "详细内容")
    private String messageContent;

    /**
     * 时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date messageDate;

    /**
     * 是否已解决
     */
    @Excel(name = "是否已解决")
    private String isSolve;

    /**
     * 解决人
     */
    @Excel(name = "解决人")
    private String solveBy;

    public String getMessageFromName() {
        return messageFromName;
    }

    public void setMessageFromName(String messageFromName) {
        this.messageFromName = messageFromName;
    }

    public String getMessageTypeName() {
        return messageTypeName;
    }

    public void setMessageTypeName(String messageTypeName) {
        this.messageTypeName = messageTypeName;
    }

    public Long getMessageId() {
        return messageId;
    }

    public void setMessageId(Long messageId) {
        this.messageId = messageId;
    }

    public String getMessageOwner() {
        return messageOwner;
    }

    public void setMessageOwner(String messageOwner) {
        this.messageOwner = messageOwner;
    }

    public Long getMessageType() {
        return messageType;
    }

    public void setMessageType(Long messageType) {
        this.messageType = messageType;
    }

    public Long getMessageFrom() {
        return messageFrom;
    }

    public void setMessageFrom(Long messageFrom) {
        this.messageFrom = messageFrom;
    }

    public String getMessageTitle() {
        return messageTitle;
    }

    public void setMessageTitle(String messageTitle) {
        this.messageTitle = messageTitle;
    }

    public String getMessageContent() {
        return messageContent;
    }

    public void setMessageContent(String messageContent) {
        this.messageContent = messageContent;
    }

    public Date getMessageDate() {
        return messageDate;
    }

    public void setMessageDate(Date messageDate) {
        this.messageDate = messageDate;
    }

    public String getIsSolve() {
        return isSolve;
    }

    public void setIsSolve(String isSolve) {
        this.isSolve = isSolve;
    }

    public String getSolveBy() {
        return solveBy;
    }

    public void setSolveBy(String solveBy) {
        this.solveBy = solveBy;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("messageId", getMessageId())
                .append("messageOwner", getMessageOwner())
                .append("messageType", getMessageType())
                .append("messageFrom", getMessageFrom())
                .append("messageTitle", getMessageTitle())
                .append("messageContent", getMessageContent())
                .append("messageDate", getMessageDate())
                .append("isSolve", getIsSolve())
                .append("solveBy", getSolveBy())
                .append("remark", getRemark())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("deleteBy", getDeleteBy())
                .append("deleteTime", getDeleteTime())
                .append("deleteStatus", getDeleteStatus())
                .toString();
    }
}
