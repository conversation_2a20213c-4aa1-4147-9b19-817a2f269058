package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsOpLand;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsOpLandService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 陆运服务Controller
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@RestController
@RequestMapping("/system/opland")
public class RsOpLandController extends BaseController {
    @Autowired
    private RsOpLandService rsOpLandService;

    /**
     * 查询陆运服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opland:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsOpLand rsOpLand) {
        startPage();
        List<RsOpLand> list = rsOpLandService.selectRsOpLandList(rsOpLand);
        return getDataTable(list);
    }

    /**
     * 导出陆运服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opland:export')")
    @Log(title = "陆运服务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsOpLand rsOpLand) {
        List<RsOpLand> list = rsOpLandService.selectRsOpLandList(rsOpLand);
        ExcelUtil<RsOpLand> util = new ExcelUtil<RsOpLand>(RsOpLand.class);
        util.exportExcel(response, list, "陆运服务数据");
    }

    /**
     * 获取陆运服务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:opland:query')")
    @GetMapping(value = "/{landId}")
    public AjaxResult getInfo(@PathVariable("landId") Long landId) {
        return AjaxResult.success(rsOpLandService.selectRsOpLandByLandId(landId));
    }

    /**
     * 新增陆运服务
     */
    @PreAuthorize("@ss.hasPermi('system:opland:add')")
    @Log(title = "陆运服务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsOpLand rsOpLand) {
        return toAjax(rsOpLandService.insertRsOpLand(rsOpLand));
    }

    /**
     * 修改陆运服务
     */
    @PreAuthorize("@ss.hasPermi('system:opland:edit')")
    @Log(title = "陆运服务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsOpLand rsOpLand) {
        return toAjax(rsOpLandService.updateRsOpLand(rsOpLand));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:opland:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsOpLand rsOpLand) {
        rsOpLand.setUpdateBy(getUserId());
        return toAjax(rsOpLandService.changeStatus(rsOpLand));
    }

    /**
     * 删除陆运服务
     */
    @PreAuthorize("@ss.hasPermi('system:opland:remove')")
    @Log(title = "陆运服务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{landIds}")
    public AjaxResult remove(@PathVariable Long[] landIds) {
        return toAjax(rsOpLandService.deleteRsOpLandByLandIds(landIds));
    }
}
