---
description:
globs:
alwaysApply: false
---
# 数据库设计与模型关系

本文档描述瑞旗管理系统的数据库设计规范和主要数据模型之间的关系，帮助开发人员理解系统中的数据结构。

## 数据库设计规范

### 命名规范

- 表名：使用小写字母，单词间用下划线分隔，使用前缀区分模块，如 `sys_user`
- 字段名：使用小写字母，单词间用下划线分隔，如 `user_name`
- 主键：一般使用 `id` 或 `模块_id`，如 `user_id`
- 外键：使用关联表名的单数形式加 `_id`，如 `role_id`

### 字段设计规范

- 每个表必须包含以下基础字段：
  - `id`：主键，一般使用 BIGINT 类型
  - `create_by`：创建人，VARCHAR(64)
  - `create_time`：创建时间，DATETIME
  - `update_by`：更新人，VARCHAR(64)
  - `update_time`：更新时间，DATETIME
  - `remark`：备注，VARCHAR(500)，可为空
  - `del_flag`：删除标志，CHAR(1)，默认为 '0'（正常），'2'（删除）

- 状态字段规范：
  - 状态通常使用 CHAR(1) 类型
  - 使用数字表示状态，如 '0'（正常）, '1'（停用）
  - 在代码中使用常量或枚举定义状态值

### 索引设计规范

- 主键索引：每个表必须有主键索引
- 唯一索引：对唯一约束的字段创建唯一索引，如用户名、编码等
- 普通索引：对常用于查询条件的字段创建索引，如状态字段、外键字段
- 联合索引：对经常同时出现在查询条件中的多个字段创建联合索引

## 核心数据模型

### 用户权限相关表

#### 用户表 (sys_user)

```sql
CREATE TABLE `sys_user` (
  `user_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint(20) DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) NOT NULL COMMENT '用户昵称',
  `user_type` varchar(2) DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) DEFAULT '' COMMENT '手机号码',
  `sex` char(1) DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) DEFAULT '' COMMENT '密码',
  `status` char(1) DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB COMMENT='用户信息表';
```

#### 角色表 (sys_role)

```sql
CREATE TABLE `sys_role` (
  `role_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) NOT NULL COMMENT '角色权限字符串',
  `role_sort` int(4) NOT NULL COMMENT '显示顺序',
  `status` char(1) NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`)
) ENGINE=InnoDB COMMENT='角色信息表';
```

#### 菜单表 (sys_menu)

```sql
CREATE TABLE `sys_menu` (
  `menu_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) NOT NULL COMMENT '菜单名称',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '父菜单ID',
  `order_num` int(4) DEFAULT '0' COMMENT '显示顺序',
  `path` varchar(200) DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) DEFAULT NULL COMMENT '路由参数',
  `is_frame` int(1) DEFAULT '1' COMMENT '是否为外链（0是 1否）',
  `is_cache` int(1) DEFAULT '0' COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`)
) ENGINE=InnoDB COMMENT='菜单权限表';
```

#### 用户和角色关联表 (sys_user_role)

```sql
CREATE TABLE `sys_user_role` (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`,`role_id`)
) ENGINE=InnoDB COMMENT='用户和角色关联表';
```

#### 角色和菜单关联表 (sys_role_menu)

```sql
CREATE TABLE `sys_role_menu` (
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `menu_id` bigint(20) NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`,`menu_id`)
) ENGINE=InnoDB COMMENT='角色和菜单关联表';
```

## 数据模型关系图

```
┌───────────┐       ┌───────────┐       ┌───────────┐
│  sys_user │       │ sys_role  │       │ sys_menu  │
├───────────┤       ├───────────┤       ├───────────┤
│ user_id   │       │ role_id   │       │ menu_id   │
│ dept_id   │       │ role_name │       │ parent_id │
│ user_name │       │ role_key  │       │ menu_name │
│ password  │       │ role_sort │       │ path      │
│ ...       │       │ ...       │       │ perms     │
└─────┬─────┘       └─────┬─────┘       └─────┬─────┘
      │                   │                   │
      │                   │                   │
      │                   │                   │
      │        ┌──────────┴──────────┐        │
      │        │                     │        │
┌─────▼────────▼───┐           ┌─────▼────────▼───┐
│  sys_user_role   │           │  sys_role_menu   │
├──────────────────┤           ├──────────────────┤
│ user_id          │           │ role_id          │
│ role_id          │           │ menu_id          │
└──────────────────┘           └──────────────────┘
```

## 业务表设计示例

### 部门表 (sys_dept)

```sql
CREATE TABLE `sys_dept` (
  `dept_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '父部门id',
  `ancestors` varchar(50) DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) DEFAULT '' COMMENT '部门名称',
  `order_num` int(4) DEFAULT '0' COMMENT '显示顺序',
  `leader` varchar(20) DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) DEFAULT NULL COMMENT '邮箱',
  `status` char(1) DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dept_id`)
) ENGINE=InnoDB COMMENT='部门表';
```

### 字典类型表 (sys_dict_type)

```sql
CREATE TABLE `sys_dict_type` (
  `dict_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) DEFAULT '' COMMENT '字典类型',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`),
  UNIQUE KEY `dict_type` (`dict_type`)
) ENGINE=InnoDB COMMENT='字典类型表';
```

### 字典数据表 (sys_dict_data)

```sql
CREATE TABLE `sys_dict_data` (
  `dict_code` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int(4) DEFAULT '0' COMMENT '字典排序',
  `dict_label` varchar(100) DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_code`)
) ENGINE=InnoDB COMMENT='字典数据表';
```

## 数据库设计最佳实践

1. **使用逻辑删除**：通过 `del_flag` 字段标记数据是否被删除，而不是物理删除

2. **数据审计**：使用 `create_by`, `create_time`, `update_by`, `update_time` 字段记录数据变更

3. **树形结构设计**：使用 `parent_id` 和 `ancestors` 字段设计树形结构（如部门、菜单）

4. **状态管理**：使用状态字段（如 `status`）管理数据状态，避免删除数据

5. **字典表设计**：使用字典表管理系统中的枚举值和配置项

6. **关联表命名**：多对多关系的关联表名称使用两个实体的名称组合，如 `sys_user_role`

7. **字段冗余**：适当冗余一些字段以提高查询效率，但要控制冗余范围

8. **索引优化**：根据查询需求合理设计索引，避免过度索引
