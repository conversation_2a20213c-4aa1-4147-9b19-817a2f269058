package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.BasFreightPaidWay;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.BasFreightPaidWayMapper;
import com.rich.system.service.BasFreightPaidWayService;

/**
 * 付款方式Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-25
 */
@Service
public class BasFreightPaidWayServiceImpl implements BasFreightPaidWayService {
    @Autowired
    private BasFreightPaidWayMapper basFreightPaidWayMapper;

    /**
     * 查询付款方式
     *
     * @param freightPaidWayCode 付款方式主键
     * @return 付款方式
     */
    @Override
    public BasFreightPaidWay selectBasFreightPaidWayByFreightPaidWayCode(String freightPaidWayCode) {
        return basFreightPaidWayMapper.selectBasFreightPaidWayByFreightPaidWayCode(freightPaidWayCode);
    }

    /**
     * 查询付款方式列表
     *
     * @param basFreightPaidWay 付款方式
     * @return 付款方式
     */
    @Override
    public List<BasFreightPaidWay> selectBasFreightPaidWayList(BasFreightPaidWay basFreightPaidWay) {
        return basFreightPaidWayMapper.selectBasFreightPaidWayList(basFreightPaidWay);
    }

    /**
     * 新增付款方式
     *
     * @param basFreightPaidWay 付款方式
     * @return 结果
     */
    @Override
    public int insertBasFreightPaidWay(BasFreightPaidWay basFreightPaidWay) {
        return basFreightPaidWayMapper.insertBasFreightPaidWay(basFreightPaidWay);
    }

    /**
     * 修改付款方式
     *
     * @param basFreightPaidWay 付款方式
     * @return 结果
     */
    @Override
    public int updateBasFreightPaidWay(BasFreightPaidWay basFreightPaidWay) {
        return basFreightPaidWayMapper.updateBasFreightPaidWay(basFreightPaidWay);
    }

    /**
     * 修改付款方式状态
     *
     * @param basFreightPaidWay 付款方式
     * @return 付款方式
     */
    @Override
    public int changeStatus(BasFreightPaidWay basFreightPaidWay) {
        return basFreightPaidWayMapper.updateBasFreightPaidWay(basFreightPaidWay);
    }

    /**
     * 批量删除付款方式
     *
     * @param freightPaidWayCodes 需要删除的付款方式主键
     * @return 结果
     */
    @Override
    public int deleteBasFreightPaidWayByFreightPaidWayCodes(String[] freightPaidWayCodes) {
        return basFreightPaidWayMapper.deleteBasFreightPaidWayByFreightPaidWayCodes(freightPaidWayCodes);
    }

    /**
     * 删除付款方式信息
     *
     * @param freightPaidWayCode 付款方式主键
     * @return 结果
     */
    @Override
    public int deleteBasFreightPaidWayByFreightPaidWayCode(String freightPaidWayCode) {
        return basFreightPaidWayMapper.deleteBasFreightPaidWayByFreightPaidWayCode(freightPaidWayCode);
    }
}
