package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasTradingTerms;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasTradingTermsService;
import com.rich.system.service.impl.RedisCacheImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 贸易条款Controller
 *
 * <AUTHOR>
 * @date 2023-06-30
 */
@RestController
@RequestMapping("/system/tradingterms")
public class BasTradingTermsController extends BaseController {
    @Autowired
    private BasTradingTermsService basTradingTermsService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisCacheImpl RedisCache;

    /**
     * 查询贸易条款列表
     */
    @PreAuthorize("@ss.hasPermi('system:tradingterms:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasTradingTerms basTradingTerms) {
        startPage();
        List<BasTradingTerms> list = basTradingTermsService.selectBasTradingTermsList(basTradingTerms);
        return getDataTable(list);
    }

    @GetMapping("/selectList")
    public AjaxResult selectList() {
        List<BasTradingTerms> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "tradingTerms");
        if (list == null) {
            RedisCache.tradingTerms();
            list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "tradingTerms");
        }
        return AjaxResult.success(list);
    }

    /**
     * 导出贸易条款列表
     */
    @PreAuthorize("@ss.hasPermi('system:tradingterms:export')")
    @Log(title = "贸易条款", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasTradingTerms basTradingTerms) {
        List<BasTradingTerms> list = basTradingTermsService.selectBasTradingTermsList(basTradingTerms);
        ExcelUtil<BasTradingTerms> util = new ExcelUtil<BasTradingTerms>(BasTradingTerms.class);
        util.exportExcel(response, list, "贸易条款数据");
    }

    /**
     * 获取贸易条款详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:tradingterms:query')")
    @GetMapping(value = "/{tradingTermsId}")
    public AjaxResult getInfo(@PathVariable("tradingTermsId") Long tradingTermsId) {
        return AjaxResult.success(basTradingTermsService.selectBasTradingTermsByTradingTermsId(tradingTermsId));
    }

    /**
     * 新增贸易条款
     */
    @PreAuthorize("@ss.hasPermi('system:tradingterms:add')")
    @Log(title = "贸易条款", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasTradingTerms basTradingTerms) {
        return toAjax(basTradingTermsService.insertBasTradingTerms(basTradingTerms));
    }

    /**
     * 修改贸易条款
     */
    @PreAuthorize("@ss.hasPermi('system:tradingterms:edit')")
    @Log(title = "贸易条款", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasTradingTerms basTradingTerms) {
        return toAjax(basTradingTermsService.updateBasTradingTerms(basTradingTerms));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:tradingterms:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasTradingTerms basTradingTerms) {
        basTradingTerms.setUpdateBy(getUserId());
        return toAjax(basTradingTermsService.changeStatus(basTradingTerms));
    }

    /**
     * 删除贸易条款
     */
    @PreAuthorize("@ss.hasPermi('system:tradingterms:remove')")
    @Log(title = "贸易条款", businessType = BusinessType.DELETE)
    @DeleteMapping("/{tradingTermsIds}")
    public AjaxResult remove(@PathVariable Long[] tradingTermsIds) {
        return toAjax(basTradingTermsService.deleteBasTradingTermsByTradingTermsIds(tradingTermsIds));
    }
}
