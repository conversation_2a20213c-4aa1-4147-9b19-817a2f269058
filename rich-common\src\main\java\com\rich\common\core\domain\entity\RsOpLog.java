package com.rich.common.core.domain.entity;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 操作单操作记录对象 rs_op_log
 * 
 * <AUTHOR>
 * @date 2024-03-08
 */
public class RsOpLog extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long opLogId;

    /** 所属服务实例id , */
    @Excel(name = "所属服务实例id ,")
    private Long serviceId;

    /** 所属服务类型id , */
    @Excel(name = "所属服务类型id ,")
    private Long sqdServiceTypeId;

    /** 所属操作单号 , */
    @Excel(name = "所属操作单号 ,")
    private String sqdRctNo;

    /** 关联供应商 , */
    @Excel(name = "关联供应商 ,")
    private Long supplierId;

    /**
     * 关联供应商 ,
     */
    @Excel(name = "关联供应商 ,")
    private Long sqdSupplierId;

    /**
     * 进度id ,
     */
    @Excel(name = "进度id ,")
    private Long processId;

    /**
     * 进度状态id ,
     */
    @Excel(name = "进度状态id ,")
    private Long processStatusId;

    /**
     * 发送方id ,
     */
    @Excel(name = "发送方id ,")
    private Long infoFromId;

    /**
     * 接收方id ,
     */
    @Excel(name = "接收方id ,")
    private Long infoToId;

    /**
     * 随附文件列表 ,
     */
    @Excel(name = "随附文件列表 ,")
    private String attachedDocList;

    /**
     * 交付方式 ,
     */
    @Excel(name = "交付方式 ,")
    private String deliverWay;

    /**
     * 进度发生时间 ,
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "进度发生时间 ,", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date processStatusTime;

    /**
     * 操作员 ,
     */
    @Excel(name = "操作员 ,")
    private Long opId;

    /**
     * 系统日期 ,
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "系统日期 ,", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date serverSystemTime;
    private Long sqdRctId;

    private BasProcess basProcess;

    private BasProcessStatus basProcessStatus;
    private boolean showProgress;
    private boolean showStatus;
    private String sqdProcessEnName;

    public String getSqdProcessEnName() {
        return sqdProcessEnName;
    }

    public void setSqdProcessEnName(String sqdProcessEnName) {
        this.sqdProcessEnName = sqdProcessEnName;
    }

    public boolean getShowStatus() {
        return showStatus;
    }

    public void setShowStatus(boolean showStatus) {
        this.showStatus = showStatus;
    }

    public boolean getShowProgress() {
        return showProgress;
    }

    public void setShowProgress(boolean showProgress) {
        this.showProgress = showProgress;
    }

    public BasProcess getBasProcess() {
        return basProcess;
    }

    public void setBasProcess(BasProcess basProcess) {
        this.basProcess = basProcess;
    }

    public BasProcessStatus getBasProcessStatus() {
        return basProcessStatus;
    }

    public void setBasProcessStatus(BasProcessStatus basProcessStatus) {
        this.basProcessStatus = basProcessStatus;
    }

    public Long getSqdRctId() {
        return sqdRctId;
    }

    public void setSqdRctId(Long sqdRctId) {
        this.sqdRctId = sqdRctId;
    }

    public void setOpLogId(Long opLogId) {
        this.opLogId = opLogId;
    }

    public Long getOpLogId() {
        return opLogId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    public Long getServiceId() {
        return serviceId;
    }

    public Long getSqdServiceTypeId()
    {
        return sqdServiceTypeId;
    }

    public void setSqdServiceTypeId(Long sqdServiceTypeId)
    {
        this.sqdServiceTypeId = sqdServiceTypeId;
    }
    public void setSqdRctNo(String sqdRctNo) {
        this.sqdRctNo = sqdRctNo;
    }

    public String getSqdRctNo() {
        return sqdRctNo;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public Long getSqdSupplierId() {
        return sqdSupplierId;
    }

    public void setSqdSupplierId(Long sqdSupplierId) {
        this.sqdSupplierId = sqdSupplierId;
    }

    public Long getProcessId() {
        return processId;
    }

    public void setProcessId(Long processId) {
        this.processId = processId;
    }

    public Long getProcessStatusId() {
        return processStatusId;
    }

    public void setProcessStatusId(Long processStatusId) {
        this.processStatusId = processStatusId;
    }

    public Long getInfoFromId() {
        return infoFromId;
    }

    public void setInfoFromId(Long infoFromId) {
        this.infoFromId = infoFromId;
    }

    public Long getInfoToId() {
        return infoToId;
    }

    public void setInfoToId(Long infoToId) {
        this.infoToId = infoToId;
    }

    public String getAttachedDocList() {
        return attachedDocList;
    }

    public void setAttachedDocList(String attachedDocList) {
        this.attachedDocList = attachedDocList;
    }

    public String getDeliverWay() {
        return deliverWay;
    }

    public void setDeliverWay(String deliverWay) {
        this.deliverWay = deliverWay;
    }

    public Date getProcessStatusTime() {
        return processStatusTime;
    }

    public void setProcessStatusTime(Date processStatusTime) {
        this.processStatusTime = processStatusTime;
    }

    public Long getOpId() {
        return opId;
    }

    public void setOpId(Long opId) {
        this.opId = opId;
    }

    public Date getServerSystemTime()
    {
        return serverSystemTime;
    }

    public void setServerSystemTime(Date serverSystemTime) {
        this.serverSystemTime = serverSystemTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("opLogId", getOpLogId())
                .append("serviceId", getServiceId())
                .append("sqdServiceTypeId", getSqdServiceTypeId())
                .append("sqdRctNo", getSqdRctNo())
                .append("supplierId", getSupplierId())
                .append("sqdSupplierId", getSqdSupplierId())
                .append("processId", getProcessId())
                .append("processStatusId", getProcessStatusId())
                .append("infoFromId", getInfoFromId())
                .append("infoToId", getInfoToId())
                .append("attachedDocList", getAttachedDocList())
                .append("deliverWay", getDeliverWay())
                .append("processStatusTime", getProcessStatusTime())
                .append("remark", getRemark())
            .append("opId", getOpId())
            .append("serverSystemTime", getServerSystemTime())
            .toString();
    }
}
