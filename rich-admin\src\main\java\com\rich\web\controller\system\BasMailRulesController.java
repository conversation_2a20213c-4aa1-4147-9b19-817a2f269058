package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasMailRules;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasMailRulesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 邮件规则Controller
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@RestController
@RequestMapping("/system/mailrules")
public class BasMailRulesController extends BaseController {
    @Autowired
    private BasMailRulesService basMailRulesService;

    /**
     * 查询邮件规则列表
     */
    @PreAuthorize("@ss.hasPermi('system:mailrules:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasMailRules basMailRules) {
        startPage();
        List<BasMailRules> list = basMailRulesService.selectBasMailRulesList(basMailRules);
        return getDataTable(list);
    }

    /**
     * 导出邮件规则列表
     */
    @PreAuthorize("@ss.hasPermi('system:mailrules:export')")
    @Log(title = "邮件规则", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasMailRules basMailRules) {
        List<BasMailRules> list = basMailRulesService.selectBasMailRulesList(basMailRules);
        ExcelUtil<BasMailRules> util = new ExcelUtil<BasMailRules>(BasMailRules.class);
        util.exportExcel(response, list, "邮件规则数据");
    }

    /**
     * 获取邮件规则详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:mailrules:query')")
    @GetMapping(value = "/{mailRulesId}")
    public AjaxResult getInfo(@PathVariable("mailRulesId") Long mailRulesId) {
        return AjaxResult.success(basMailRulesService.selectBasMailRulesByMailRulesId(mailRulesId));
    }

    /**
     * 新增邮件规则
     */
    @PreAuthorize("@ss.hasPermi('system:mailrules:add')")
    @Log(title = "邮件规则", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasMailRules basMailRules) {
        return toAjax(basMailRulesService.insertBasMailRules(basMailRules));
    }

    /**
     * 修改邮件规则
     */
    @PreAuthorize("@ss.hasPermi('system:mailrules:edit')")
    @Log(title = "邮件规则", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasMailRules basMailRules) {
        return toAjax(basMailRulesService.updateBasMailRules(basMailRules));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:mailrules:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasMailRules basMailRules) {
        basMailRules.setUpdateBy(getUserId());
        return toAjax(basMailRulesService.changeStatus(basMailRules));
    }

    /**
     * 删除邮件规则
     */
    @PreAuthorize("@ss.hasPermi('system:mailrules:remove')")
    @Log(title = "邮件规则", businessType = BusinessType.DELETE)
    @DeleteMapping("/{mailRulesIds}")
    public AjaxResult remove(@PathVariable Long[] mailRulesIds) {
        return toAjax(basMailRulesService.deleteBasMailRulesByMailRulesIds(mailRulesIds));
    }
}
