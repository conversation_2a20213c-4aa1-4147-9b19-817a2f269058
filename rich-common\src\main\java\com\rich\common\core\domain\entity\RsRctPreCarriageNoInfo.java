package com.rich.common.core.domain.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

/**
 * 操作单前程运输编号信息对象 rs_rct_pre_carriage_no_info
 * 
 * <AUTHOR>
 * @date 2023-08-15
 */
public class RsRctPreCarriageNoInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 前程运输编号信息 */
    @Excel(name = "前程运输编号信息")
    private Long preCarriageNoInfoId;

    /** 操作单 */
    @Excel(name = "操作单")
    private Long rctId;

    /** so号码 */
    @Excel(name = "so号码")
    private String soNo;

    /** 司机姓名 */
    @Excel(name = "司机姓名")
    private String preCarriageDriverName;

    /** 司机电话 */
    @Excel(name = "司机电话")
    private String preCarriageDriverTel;

    /** 司机车牌 */
    @Excel(name = "司机车牌")
    private String preCarriageTruckNo;

    /** 司机备注 */
    @Excel(name = "司机备注")
    private String preCarriageTruckRemark;

    /** 装柜地址 */
    @Excel(name = "装柜地址")
    private String preCarriageAddress;

    /** 到场时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "到场时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date preCarriageTime;

    /** 柜号 */
    @Excel(name = "柜号")
    private String containerNo;

    /** 柜型 */
    @Excel(name = "柜型")
    private Long containerType;

    /** 封条 */
    @Excel(name = "封条")
    private String sealNo;

    /** 磅单 */
    @Excel(name = "磅单")
    private String weightPaper;


    public void setPreCarriageNoInfoId(Long preCarriageNoInfoId)
    {
        this.preCarriageNoInfoId = preCarriageNoInfoId;
    }

    public Long getPreCarriageNoInfoId()
    {
        return preCarriageNoInfoId;
    }
    public void setRctId(Long rctId) 
    {
        this.rctId = rctId;
    }

    public Long getRctId() 
    {
        return rctId;
    }
    public void setSoNo(String soNo) 
    {
        this.soNo = soNo;
    }

    public String getSoNo() 
    {
        return soNo;
    }
    public void setPreCarriageDriverName(String preCarriageDriverName) 
    {
        this.preCarriageDriverName = preCarriageDriverName;
    }

    public String getPreCarriageDriverName() 
    {
        return preCarriageDriverName;
    }
    public void setPreCarriageDriverTel(String preCarriageDriverTel) 
    {
        this.preCarriageDriverTel = preCarriageDriverTel;
    }

    public String getPreCarriageDriverTel() 
    {
        return preCarriageDriverTel;
    }
    public void setPreCarriageTruckNo(String preCarriageTruckNo) 
    {
        this.preCarriageTruckNo = preCarriageTruckNo;
    }

    public String getPreCarriageTruckNo() 
    {
        return preCarriageTruckNo;
    }
    public void setPreCarriageTruckRemark(String preCarriageTruckRemark) 
    {
        this.preCarriageTruckRemark = preCarriageTruckRemark;
    }

    public String getPreCarriageTruckRemark() 
    {
        return preCarriageTruckRemark;
    }
    public void setPreCarriageAddress(String preCarriageAddress) 
    {
        this.preCarriageAddress = preCarriageAddress;
    }

    public String getPreCarriageAddress() 
    {
        return preCarriageAddress;
    }
    public void setPreCarriageTime(Date preCarriageTime) 
    {
        this.preCarriageTime = preCarriageTime;
    }

    public Date getPreCarriageTime() 
    {
        return preCarriageTime;
    }
    public void setContainerNo(String containerNo) 
    {
        this.containerNo = containerNo;
    }

    public String getContainerNo() 
    {
        return containerNo;
    }
    public void setContainerType(Long containerType) 
    {
        this.containerType = containerType;
    }

    public Long getContainerType() 
    {
        return containerType;
    }
    public void setSealNo(String sealNo) 
    {
        this.sealNo = sealNo;
    }

    public String getSealNo() 
    {
        return sealNo;
    }
    public void setWeightPaper(String weightPaper) 
    {
        this.weightPaper = weightPaper;
    }

    public String getWeightPaper() 
    {
        return weightPaper;
    }

}
