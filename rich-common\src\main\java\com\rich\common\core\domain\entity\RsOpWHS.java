package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.List;

/**
 * 仓储服务对象 rs_op_warehouse
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
public class RsOpWHS extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long warehouseId;

    /**
     * 所属服务实例id ,
     */
    @Excel(name = "所属服务实例id ,")
    private Long serviceId;

    /**
     * 所属服务类型id ,
     */
    @Excel(name = "所属服务类型id ,")
    private Long sqdServiceTypeId;

    /**
     * 所属操作单号 ,
     */
    @Excel(name = "所属操作单号 ,")
    private String sqdRctNo;

    private RsServiceInstances rsServiceInstances;

    private List<RsCharge> rsChargeList;

    private List<RsDoc> rsDocList;

    private List<RsOpLog> rsOpLogList;
    private String sqdServiceDetailsCode;
    private String loadingWayCode;
    private BigDecimal payableRMB;
    private BigDecimal payableUSD;
    private BigDecimal payableUSDTax;
    private BigDecimal payableRMBTax;

    public BigDecimal getPayableRMBTax() {
        return payableRMBTax;
    }

    public void setPayableRMBTax(BigDecimal payableRMBTax) {
        this.payableRMBTax = payableRMBTax;
    }

    public BigDecimal getPayableUSDTax() {
        return payableUSDTax;
    }

    public void setPayableUSDTax(BigDecimal payableUSDTax) {
        this.payableUSDTax = payableUSDTax;
    }

    public BigDecimal getPayableUSD() {
        return payableUSD;
    }

    public void setPayableUSD(BigDecimal payableUSD) {
        this.payableUSD = payableUSD;
    }

    public BigDecimal getPayableRMB() {
        return payableRMB;
    }

    public void setPayableRMB(BigDecimal payableRMB) {
        this.payableRMB = payableRMB;
    }

    public String getLoadingWayCode() {
        return loadingWayCode;
    }

    public void setLoadingWayCode(String loadingWayCode) {
        this.loadingWayCode = loadingWayCode;
    }

    public String getSqdServiceDetailsCode() {
        return sqdServiceDetailsCode;
    }

    public void setSqdServiceDetailsCode(String sqdServiceDetailsCode) {
        this.sqdServiceDetailsCode = sqdServiceDetailsCode;
    }

    public List<RsOpLog> getRsOpLogList() {
        return rsOpLogList;
    }

    public void setRsOpLogList(List<RsOpLog> rsOpLogList) {
        this.rsOpLogList = rsOpLogList;
    }

    public RsServiceInstances getRsServiceInstances() {
        return rsServiceInstances;
    }

    public void setRsServiceInstances(RsServiceInstances rsServiceInstances) {
        this.rsServiceInstances = rsServiceInstances;
    }

    public List<RsCharge> getRsChargeList() {
        return rsChargeList;
    }

    public void setRsChargeList(List<RsCharge> rsChargeList) {
        this.rsChargeList = rsChargeList;
    }

    public List<RsDoc> getRsDocList() {
        return rsDocList;
    }

    public void setRsDocList(List<RsDoc> rsDocList) {
        this.rsDocList = rsDocList;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getServiceId() {
        return serviceId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    public Long getSqdServiceTypeId() {
        return sqdServiceTypeId;
    }

    public void setSqdServiceTypeId(Long sqdServiceTypeId) {
        this.sqdServiceTypeId = sqdServiceTypeId;
    }

    public String getSqdRctNo() {
        return sqdRctNo;
    }

    public void setSqdRctNo(String sqdRctNo) {
        this.sqdRctNo = sqdRctNo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("warehouseId", getWarehouseId())
                .append("serviceId", getServiceId())
                .append("sqdServiceTypeId", getSqdServiceTypeId())
                .append("sqdRctNo", getSqdRctNo())
                .toString();
    }
}
