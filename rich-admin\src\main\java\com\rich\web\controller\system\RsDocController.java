package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsDoc;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsDocService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 文件信息Controller
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@RestController
@RequestMapping("/system/rsdoc")
public class RsDocController extends BaseController {
    @Autowired
    private RsDocService rsDocService;

    /**
     * 查询文件信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:doc:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsDoc rsDoc) {
        startPage();
        List<RsDoc> list = rsDocService.selectRsDocList(rsDoc);
        return getDataTable(list);
    }

    /**
     * 导出文件信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:doc:export')")
    @Log(title = "文件信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsDoc rsDoc) {
        List<RsDoc> list = rsDocService.selectRsDocList(rsDoc);
        ExcelUtil<RsDoc> util = new ExcelUtil<RsDoc>(RsDoc.class);
        util.exportExcel(response, list, "文件信息数据");
    }

    /**
     * 获取文件信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:doc:query')")
    @GetMapping(value = "/{docId}")
    public AjaxResult getInfo(@PathVariable("docId") Long docId) {
        return AjaxResult.success(rsDocService.selectRsDocByDocId(docId));
    }

    /**
     * 新增文件信息
     */
    @PreAuthorize("@ss.hasPermi('system:doc:add')")
    @Log(title = "文件信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsDoc rsDoc) {
        return toAjax(rsDocService.insertRsDoc(rsDoc));
    }

    /**
     * 修改文件信息
     */
    @PreAuthorize("@ss.hasPermi('system:doc:edit')")
    @Log(title = "文件信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsDoc rsDoc) {
        return toAjax(rsDocService.updateRsDoc(rsDoc));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:doc:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsDoc rsDoc) {
        rsDoc.setUpdateBy(getUserId());
        return toAjax(rsDocService.changeStatus(rsDoc));
    }

    /**
     * 删除文件信息
     */
    @PreAuthorize("@ss.hasPermi('system:doc:remove')")
    @Log(title = "文件信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{docIds}")
    public AjaxResult remove(@PathVariable Long[] docIds) {
        return toAjax(rsDocService.deleteRsDocByDocIds(docIds));
    }
}
