package com.rich.system.mapper;

import com.rich.system.domain.MidCarrier;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 承运人Mapper接口
 *
 * <AUTHOR>
 * @date 2022-10-31
 */
@Mapper
public interface MidCarrierMapper {
    /**
     * 查询承运人
     *
     * @return 承运人
     */
    List<Long> selectMidCarrierById(Long belongId, String belongTo);

    List<Long> selectMidCarrierByCarrierId(Long carrierId, String belongTo);

    /**
     * 查询承运人列表
     *
     * @param midCarrier 承运人
     * @return 承运人集合
     */
    List<MidCarrier> selectMidCarrierList(MidCarrier midCarrier);

    /**
     * 新增承运人
     *
     * @param midCarrier 承运人
     * @return 结果
     */
    int insertMidCarrier(MidCarrier midCarrier);

    /**
     * 修改承运人
     *
     * @param midCarrier 承运人
     * @return 结果
     */
    int updateMidCarrier(MidCarrier midCarrier);

    /**
     * 删除承运人
     *
     * @return 结果
     */
    int deleteMidCarrierById(Long belongId, String belongTo);

    int deleteMidCarrierByCarrierId(Long carrier);

    /**
     * 批量删除承运人
     *
     * @return 结果
     */
    int deleteMidCarrierByIds(Long[] belongIds, String belongTo);


    int deleteMidCarrierByCarrierIds(Long[] carrierIds);


    int batchCarrier(List<MidCarrier> list);
}
