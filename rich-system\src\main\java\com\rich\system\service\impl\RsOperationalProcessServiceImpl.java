package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.RsOperationalProcess;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.system.mapper.RsOperationalProcessMapper;
import com.rich.system.service.RsOperationalProcessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 操作进度Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@Service
public class RsOperationalProcessServiceImpl implements RsOperationalProcessService {
    @Autowired
    private RsOperationalProcessMapper rsOperationalProcessMapper;

    /**
     * 查询操作进度
     *
     * @param operationalProcessId 操作进度主键
     * @return 操作进度
     */
    @Override
    public RsOperationalProcess selectRsOperationalProcessByOperationalProcessId(Long operationalProcessId) {
        return rsOperationalProcessMapper.selectRsOperationalProcessByOperationalProcessId(operationalProcessId);
    }

    /**
     * 查询操作进度列表
     *
     * @param rsOperationalProcess 操作进度
     * @return 操作进度
     */
    @Override
    public List<RsOperationalProcess> selectRsOperationalProcessList(RsOperationalProcess rsOperationalProcess) {
        return rsOperationalProcessMapper.selectRsOperationalProcessList(rsOperationalProcess);
    }

    /**
     * 新增操作进度
     *
     * @param rsOperationalProcess 操作进度
     * @return 结果
     */
    @Override
    public RsOperationalProcess insertRsOperationalProcess(RsOperationalProcess rsOperationalProcess) {
        rsOperationalProcess.setCreateTime(DateUtils.getNowDate());
        rsOperationalProcess.setCreateBy(SecurityUtils.getUserId());
        rsOperationalProcessMapper.insertRsOperationalProcess(rsOperationalProcess);
        return rsOperationalProcess;
    }

    /**
     * 修改操作进度
     *
     * @param rsOperationalProcess 操作进度
     * @return 结果
     */
    @Override
    public int updateRsOperationalProcess(RsOperationalProcess rsOperationalProcess) {
        rsOperationalProcess.setUpdateTime(DateUtils.getNowDate());
        rsOperationalProcess.setUpdateBy(SecurityUtils.getUserId());
        return rsOperationalProcessMapper.updateRsOperationalProcess(rsOperationalProcess);
    }

    /**
     * 修改操作进度状态
     *
     * @param rsOperationalProcess 操作进度
     * @return 操作进度
     */
    @Override
    public int changeStatus(RsOperationalProcess rsOperationalProcess) {
        return rsOperationalProcessMapper.updateRsOperationalProcess(rsOperationalProcess);
    }

    @Override
    public int delRsOperationalProcess(RsOperationalProcess rsOperationalProcess) {
        return rsOperationalProcessMapper.deleteRsOperationalProcess(rsOperationalProcess.getRctId(), rsOperationalProcess.getTypeId(), rsOperationalProcess.getBasicInfoId());
    }
}
