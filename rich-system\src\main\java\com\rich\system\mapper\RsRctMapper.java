package com.rich.system.mapper;

import java.util.List;
import java.util.Map;

import com.rich.common.core.domain.entity.RsRct;
import com.rich.common.core.domain.entity.RsRctExportVO;
import com.rich.common.core.domain.entity.StatisticsOpDTO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 操作单Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-12-18
 */
@Mapper
public interface RsRctMapper {
    /**
     * 查询操作单
     *
     * @param rctId 操作单主键
     * @return 操作单
     */
    RsRct selectRsRctByRctId(Long rctId);

    /**
     * 查询操作单列表
     *
     * @param rsRct 操作单
     * @return 操作单集合
     */
    List<RsRct> selectRsRctList(RsRct rsRct);

    /**
     * 新增操作单
     *
     * @param rsRct 操作单
     * @return 结果
     */
    int insertRsRct(RsRct rsRct);

    /**
     * 修改操作单
     *
     * @param rsRct 操作单
     * @return 结果
     */
    int updateRsRct(RsRct rsRct);

    /**
     * 删除操作单
     *
     * @param rctId 操作单主键
     * @return 结果
     */
    int deleteRsRctByRctId(Long rctId);

    /**
     * 批量删除操作单
     * 
     * @param rctIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsRctByRctIds(Long[] rctIds);

    int getMon();

    List<RsRct> selectUnVerifyRsRctList(RsRct rsRct);

    List<RsRct> selectRsRctByCompanyId(Long companyId);

    int getCFMon();

    Long selectRctIdByRctNo(String rctNo);

    List<StatisticsOpDTO> statisticsOp();

    List<RsRctExportVO> selectRsRctListToExport(RsRct rsRct);

    int getRSWHMon();

    int opNotification(Long opId);

    int psaNotification();

    void updateByWriteoff(String rctNo);
}
