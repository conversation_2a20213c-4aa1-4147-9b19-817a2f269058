package com.rich.system.service.impl;

import com.rich.common.constant.CacheConstants;
import com.rich.common.core.domain.entity.*;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.core.text.Convert;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SearchUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.StringUtils;
import com.rich.system.domain.*;
import com.rich.system.mapper.*;
import com.rich.system.service.RsBookingService;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.rich.common.utils.PageUtils.startPage;

/**
 * 订舱单列表Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-15
 */
@Service
public class RsBookingServiceImpl implements RsBookingService {
    @Autowired
    private RsBookingMapper rsBookingMapper;

    @Autowired
    private RsBookingLogisticsTypeBasicInfoMapper rsBookingLogisticsTypeBasicInfoMapper;

    @Autowired
    private RsBookingPreCarriageBasicInfoMapper rsBookingPreCarriageBasicInfoMapper;

    @Autowired
    private RsBookingExportDeclarationBasicInfoMapper rsBookingExportDeclarationBasicInfoMapper;

    @Autowired
    private RsBookingImportClearanceBasicInfoMapper rsBookingImportClearanceBasicInfoMapper;

    @Autowired
    private RsBookingReceivablePayableMapper rsBookingReceivablePayableMapper;

    @Autowired
    private MidRsStaffRoleMapper midRsStaffRoleMapper;

    @Autowired
    private MidLocationDepartureMapper midLocationDepartureMapper;

    @Autowired
    private MidLocationDestinationMapper midLocationDestinationMapper;

    @Autowired
    private MidServiceTypeMapper midServiceTypeMapper;

    @Autowired
    private MidCargoTypeMapper midCargoTypeMapper;

    @Autowired
    private MidCarrierMapper midCarrierMapper;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisCacheImpl RedisCache;

    /**
     * 查询订舱单列表
     *
     * @param bookingId 订舱单列表主键
     * @return 订舱单列表
     */
    @Override
    public RsBooking selectRsBookingByBookingId(Long bookingId) {
        RsBooking rsBooking = rsBookingMapper.selectRsBookingByBookingId(bookingId);
        RsBookingLogisticsTypeBasicInfo rsBookingLogisticsTypeBasicInfo = rsBookingLogisticsTypeBasicInfoMapper.selectRsBookingLogisticsTypeBasicInfoByBookingId(bookingId);
        if (rsBookingLogisticsTypeBasicInfo != null) {
            rsBookingLogisticsTypeBasicInfo.setRsBookingReceivablePayableList(rsBookingReceivablePayableMapper.selectRsBookingReceivablePayable(rsBooking.getBookingId(), rsBookingLogisticsTypeBasicInfo.getTypeId(), rsBookingLogisticsTypeBasicInfo.getLogisticsTypeInfoId()));
        }
        RsBookingPreCarriageBasicInfo rsBookingPreCarriageBasicInfo = rsBookingPreCarriageBasicInfoMapper.selectRsBookingPreCarriageBasicInfoByBookingId(bookingId);
        if (rsBookingPreCarriageBasicInfo != null) {
            rsBookingPreCarriageBasicInfo.setRsBookingReceivablePayableList(rsBookingReceivablePayableMapper.selectRsBookingReceivablePayable(rsBooking.getBookingId(), rsBookingPreCarriageBasicInfo.getTypeId(), rsBookingPreCarriageBasicInfo.getPreCarriageInfoId()));
        }
        RsBookingExportDeclarationBasicInfo rsBookingExportDeclarationBasicInfo = rsBookingExportDeclarationBasicInfoMapper.selectRsBookingExportDeclarationBasicInfoByBookingId(bookingId);
        if (rsBookingExportDeclarationBasicInfo != null) {
            rsBookingExportDeclarationBasicInfo.setRsBookingReceivablePayableList(rsBookingReceivablePayableMapper.selectRsBookingReceivablePayable(rsBooking.getBookingId(), rsBookingExportDeclarationBasicInfo.getTypeId(), rsBookingExportDeclarationBasicInfo.getExportDeclarationId()));
        }
        RsBookingImportClearanceBasicInfo rsBookingImportClearanceBasicInfo = rsBookingImportClearanceBasicInfoMapper.selectRsBookingImportClearanceBasicInfoByBookingId(bookingId);
        if (rsBookingImportClearanceBasicInfo != null) {
            rsBookingImportClearanceBasicInfo.setRsBookingReceivablePayableList(rsBookingReceivablePayableMapper.selectRsBookingReceivablePayable(rsBooking.getBookingId(), rsBookingImportClearanceBasicInfo.getTypeId(), rsBookingImportClearanceBasicInfo.getImportClearanceId()));
        }
        rsBooking.setRsBookingLogisticsTypeBasicInfo(rsBookingLogisticsTypeBasicInfo);
        rsBooking.setRsBookingPreCarriageBasicInfo(rsBookingPreCarriageBasicInfo);
        rsBooking.setRsBookingExportDeclarationBasicInfo(rsBookingExportDeclarationBasicInfo);
        rsBooking.setRsBookingImportClearanceBasicInfo(rsBookingImportClearanceBasicInfo);
        return rsBooking;
    }


    @Override
    public List<Long> getCarrierIds(Long bookingId) {
        return midCarrierMapper.selectMidCarrierById(bookingId, "booking");
    }

    @Override
    public List<Long> getCargoTypeIds(Long bookingId) {
        return midCargoTypeMapper.selectMidCargoTypeById(bookingId, "booking");
    }

    @Override
    public List<Long> getServiceTypeIds(Long bookingId) {
        return midServiceTypeMapper.selectMidServiceTypeById(bookingId, "booking");
    }


    /**
     * 查询订舱单列表列表
     *
     * @param rsBooking 订舱单列表
     * @return 订舱单列表
     */
    @Override
    public List<RsBooking> selectRsBookingList(RsBooking rsBooking) {
        boolean search = rsBooking.getPolIds() != null || rsBooking.getDestinationPortIds() != null || rsBooking.getLineIds() != null;
        if (search) {
            Map<String, List<?>> query = queryBookingList(rsBooking);
            List<List<Long>> queryList = (List<List<Long>>) query.get("list");
            List<Long> queryPolIds = (List<Long>) query.get("locationDeparture");
            List<Long> queryDestinationIds = (List<Long>) query.get("locationDestination");
            if (queryList.size() > 0) {
                rsBooking.setBookingIds(SearchUtils.getLongs(queryList));
            } else {
                return null;
            }
            if (queryPolIds.size() > 0) {
                rsBooking.setPolIds(queryPolIds.toArray(new Long[0]));
            } else {
                rsBooking.setPolIds(null);
            }
            if (queryDestinationIds.size() > 0) {
                rsBooking.setDestinationPortIds(queryDestinationIds.toArray(new Long[0]));
            } else {
                rsBooking.setDestinationPortIds(null);
            }
        }
        startPage();
        return rsBookingMapper.selectRsBookingList(rsBooking);
    }

    @Override
    public List<RsBooking> selectPsaRsBookingList(RsBooking rsBooking) {
        rsBooking.setIsPsaVerified(0);
        Map<String, List<?>> map = psaRsBookingList();
        List<List<Long>> psa = (List<List<Long>>) map.get("list");
        List<Long> psaPolIds = (List<Long>) map.get("locationDeparture");
        List<Long> psaDestinationIds = (List<Long>) map.get("locationDestination");
        List<Long> ids = new ArrayList<>();
        List<Long> polIds = new ArrayList<>();
        List<Long> destinationIds = new ArrayList<>();
        boolean search = rsBooking.getPolIds() != null || rsBooking.getDestinationPortIds() != null || rsBooking.getLineIds() != null;
        if (search) {
            Map<String, List<?>> query = queryBookingList(rsBooking);
            List<List<Long>> queryList = (List<List<Long>>) query.get("list");
            List<Long> queryPolIds = (List<Long>) query.get("locationDeparture");
            List<Long> queryDestinationIds = (List<Long>) query.get("locationDestination");
            if (queryList != null) {
                ids.addAll(SearchUtils.getLongs(Stream.concat(psa.stream(), queryList.stream()).collect(Collectors.toList())));
                if (queryPolIds.size() > 0) {
                    for (Long p : queryPolIds) {
                        if (psaPolIds.contains(p)) {
                            polIds.add(p);
                        }
                    }
                }
                if (queryDestinationIds.size() > 0) {
                    for (Long d : queryDestinationIds) {
                        if (psaDestinationIds.contains(d)) {
                            destinationIds.add(d);
                        }
                    }
                }
            } else {
                return null;
            }
        } else {
            ids.addAll(SearchUtils.getLongs(psa));
            polIds.addAll(psaPolIds);
            destinationIds.addAll(psaDestinationIds);
        }
        if (ids.size() > 0) {
            rsBooking.setBookingIds(Arrays.asList(ids.toArray(new Long[0])));
        }
        if (polIds.size() > 0) {
            rsBooking.setPolIds(polIds.toArray(new Long[0]));
        } else {
            rsBooking.setPolIds(null);
        }
        if (destinationIds.size() > 0) {
            rsBooking.setDestinationPortIds(destinationIds.toArray(new Long[0]));
        } else {
            rsBooking.setDestinationPortIds(null);
        }
        startPage();
        return rsBookingMapper.selectRsBookingList(rsBooking);
    }

    private Map<String, List<?>> queryBookingList(RsBooking rsBooking) {
        List<List<Long>> lists = new ArrayList<>();
        List<BasDistLocation> basDistLocations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        if (basDistLocations == null) {
            RedisCache.location();
            basDistLocations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        }
        List<BasDistLine> basDistLines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        if (basDistLines == null) {
            RedisCache.line();
            basDistLines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        }
        Set<Long> lineDestination = new HashSet<>();
        if (rsBooking.getLineIds() != null) {
            for (BasDistLine line : basDistLines) {
                String[] lineAncestors = line.getAncestors().split(",");
                if (ArrayUtils.contains(rsBooking.getLineIds(), line.getLineId())) {
                    for (String a : lineAncestors) {
                        lineDestination.add(Convert.toLong(a));
                    }
                }
                if (SearchUtils.existSame(lineAncestors, rsBooking.getLineIds())) {
                    lineDestination.add(line.getLineId());
                }
            }
        }
        Set<Long> locationDeparture = new HashSet<>();
        Set<Long> locationDestination = new HashSet<>();
        if (rsBooking.getPolIds() != null || rsBooking.getDestinationPortIds() != null) {
            for (BasDistLocation location : basDistLocations) {
                String[] ancestors = location.getAncestors().split(",");
                if (rsBooking.getPolIds() != null && !ArrayUtils.contains(rsBooking.getPolIds(), -1L)) {
                    if (ArrayUtils.contains(rsBooking.getPolIds(), location.getLocationId())) {
                        locationDeparture.add(location.getLocationId());
                        for (String a : ancestors) {
                            locationDeparture.add(Convert.toLong(a));
                        }
                    }
                    if (SearchUtils.existSame(ancestors, rsBooking.getPolIds())) {
                        locationDeparture.add(location.getLocationId());
                    }
                }
                if (rsBooking.getDestinationPortIds() != null && !ArrayUtils.contains(rsBooking.getDestinationPortIds(), -1L)) {
                    if (ArrayUtils.contains(rsBooking.getDestinationPortIds(), location.getLocationId())) {
                        locationDestination.add(location.getLocationId());
                        for (String a : ancestors) {
                            locationDestination.add(Convert.toLong(a));
                        }
                    }
                    if (SearchUtils.existSame(ancestors, rsBooking.getDestinationPortIds())) {
                        locationDestination.add(location.getLocationId());
                    }
                    if (lineDestination.contains(location.getLineId())) {
                        locationDestination.add(location.getLocationId());
                    }
                }
            }
        }
        HashMap<String, List<?>> out = new HashMap<>();
        out.put("list", lists);
        out.put("locationDeparture", Arrays.asList(locationDeparture.toArray()));
        out.put("locationDestination", Arrays.asList(locationDestination.toArray()));
        return out;
    }

    public Map<String, List<?>> psaRsBookingList() {
        List<List<Long>> lists = new ArrayList<>();
        List<BasDistLocation> basDistLocations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        if (basDistLocations == null) {
            RedisCache.location();
            basDistLocations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        }
        List<BasDistLine> basDistLines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        if (basDistLines == null) {
            RedisCache.line();
            basDistLines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        }
        List<BasDistCargoType> basDistCargoTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        if (basDistCargoTypes == null) {
            RedisCache.cargoType();
            basDistCargoTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        }
        List<BasDistServiceType> basDistServiceTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
        if (basDistServiceTypes == null) {
            RedisCache.serviceType();
            basDistServiceTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
        }
        List<MidLocationDeparture> midRoleLocationDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleLocationDeparture");
        if (midRoleLocationDepartures == null) {
            RedisCache.locationDeparture("role", "roleLocationDeparture");
            midRoleLocationDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleLocationDeparture");
        }
        List<MidLineDeparture> midRoleLineDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleLineDeparture");
        if (midRoleLineDepartures == null) {
            RedisCache.lineDeparture("role", "roleLineDeparture");
            midRoleLineDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleLineDeparture");
        }
        List<MidLocationDestination> midRoleLocationDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleLocationDestination");
        if (midRoleLocationDestinations == null) {
            RedisCache.locationDestination("role", "roleLocationDestination");
            midRoleLocationDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleLocationDestination");
        }
        List<MidLineDestination> midRoleLineDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleLineDestination");
        if (midRoleLineDestinations == null) {
            RedisCache.lineDestination("role", "roleLineDestination");
            midRoleLineDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleLineDestination");
        }
        List<MidCargoType> midBookingCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "bookingCargoType");
        if (midBookingCargoTypes == null) {
            RedisCache.midCargoType("booking", "bookingCargoType");
            midBookingCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "bookingCargoType");
        }
        List<MidCargoType> midRoleCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleCargoType");
        if (midRoleCargoTypes == null) {
            RedisCache.midCargoType("role", "roleCargoType");
            midRoleCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleCargoType");
        }
        List<MidServiceType> midBookingServiceTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "bookingServiceType");
        if (midBookingServiceTypes == null) {
            RedisCache.midServiceType("booking", "bookingServiceType");
            midBookingServiceTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "bookingServiceType");
        }
        List<MidServiceType> midRoleServiceTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleServiceType");
        if (midRoleServiceTypes == null) {
            RedisCache.midServiceType("role", "roleServiceType");
            midRoleServiceTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "roleServiceType");
        }
        List<MidRsStaffRole> distribute;
        MidRsStaffRole midRsStaffRole = new MidRsStaffRole();
        Set<Long> locationDepartures = new HashSet<>();
        Set<Long> lineDepartures = new HashSet<>();
        Set<Long> locationDestinations = new HashSet<>();
        Set<Long> lineDestinations = new HashSet<>();
        Set<Long> cargoTypes = new HashSet<>();
        Set<Long> serviceTypes = new HashSet<>();
        distribute = midRsStaffRoleMapper.selectMidRsStaffRoleList(midRsStaffRole);
        for (MidRsStaffRole r : distribute) {
            for (MidLocationDeparture m : midRoleLocationDepartures) {
                if (m.getBelongId().equals(r.getRoleId())) {
                    locationDepartures.add(m.getLocationId());
                }
            }
            for (MidLocationDestination m : midRoleLocationDestinations) {
                if (m.getBelongId().equals(r.getRoleId())) {
                    locationDestinations.add(m.getLocationId());
                }
            }
            for (MidLineDeparture m : midRoleLineDepartures) {
                if (m.getBelongId().equals(r.getRoleId())) {
                    lineDepartures.add(m.getLineId());
                }
            }
            for (MidLineDestination m : midRoleLineDestinations) {
                if (m.getBelongId().equals(r.getRoleId())) {
                    lineDestinations.add(m.getLineId());
                }
            }
            for (MidServiceType m : midRoleServiceTypes) {
                if (m.getBelongId().equals(r.getRoleId())) {
                    serviceTypes.add(m.getServiceTypeId());
                }
            }
            for (MidCargoType m : midRoleCargoTypes) {
                if (m.getBelongId().equals(r.getRoleId())) {
                    cargoTypes.add(m.getCargoTypeId());
                }
            }
        }
        Set<Long> linedeparture = new HashSet<>();
        Set<Long> lineDestination = new HashSet<>();
        for (BasDistLine line : basDistLines) {
            String[] lineAncestors = line.getAncestors().split(",");
            if (lineDepartures.contains(line.getLineId())) {
                for (String a : lineAncestors) {
                    linedeparture.add(Convert.toLong(a));
                }
            }
            if (SearchUtils.existSame(lineAncestors, lineDepartures.toArray())) {
                linedeparture.add(line.getLineId());
            }
            if (lineDestinations.contains(line.getLineId())) {
                for (String a : lineAncestors) {
                    lineDestination.add(Convert.toLong(a));
                }
            }
            if (SearchUtils.existSame(lineAncestors, lineDestinations.toArray())) {
                lineDestination.add(line.getLineId());
            }
        }
        Set<Long> locationDeparture = new HashSet<>();
        Set<Long> locationDestination = new HashSet<>();
        for (BasDistLocation location : basDistLocations) {
            String[] ancestors = location.getAncestors().split(",");
            if (locationDepartures.size() > 0 && !ArrayUtils.contains(locationDepartures.toArray(), -1L)) {
                if (ArrayUtils.contains(locationDepartures.toArray(), location.getLocationId())) {
                    locationDeparture.add(location.getLocationId());
                    for (String a : ancestors) {
                        locationDeparture.add(Convert.toLong(a));
                    }
                }
                if (SearchUtils.existSame(ancestors, locationDepartures.toArray())) {
                    locationDeparture.add(location.getLocationId());
                }
                if (linedeparture.contains(location.getLineId())) {
                    locationDeparture.add(location.getLocationId());
                }
            }
            if (locationDestinations.size() > 0 && !ArrayUtils.contains(locationDestinations.toArray(), -1L)) {
                if (ArrayUtils.contains(locationDestinations.toArray(), location.getLocationId())) {
                    locationDestination.add(location.getLocationId());
                    for (String a : ancestors) {
                        locationDestination.add(Convert.toLong(a));
                    }
                }
                if (SearchUtils.existSame(ancestors, locationDestinations.toArray())) {
                    locationDestination.add(location.getLocationId());
                }
                if (lineDestination.contains(location.getLineId())) {
                    locationDestination.add(location.getLocationId());
                }
            }
        }
        if (serviceTypes.size() > 0 && !ArrayUtils.contains(serviceTypes.toArray(), -1L)) {
            Set<Long> set = new HashSet<>();
            Set<Long> c = new HashSet<>();
            for (BasDistServiceType serviceType : basDistServiceTypes) {
                String[] ancestors = serviceType.getAncestors().split(",");
                if (SearchUtils.existSame(ancestors, serviceTypes.toArray())) {
                    c.add(serviceType.getServiceTypeId());
                }
                if (ArrayUtils.contains(serviceTypes.toArray(), serviceType.getServiceTypeId())) {
                    c.add(serviceType.getServiceTypeId());
                    for (String a : ancestors) {
                        c.add(Convert.toLong(a));
                    }
                }
            }
            for (MidServiceType serviceType : midBookingServiceTypes) {
                if (c.contains(serviceType.getServiceTypeId())) {
                    set.add(serviceType.getBelongId());
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (list.size() > 0) {
                lists.add(list);
            }
        }
        if (cargoTypes.size() > 0 && !ArrayUtils.contains(cargoTypes.toArray(), -1L)) {
            Set<Long> set = new HashSet<>();
            Set<Long> c = new HashSet<>();
            for (BasDistCargoType cargoType : basDistCargoTypes) {
                String[] ancestors = cargoType.getAncestors().split(",");
                if (SearchUtils.existSame(ancestors, cargoTypes.toArray())) {
                    c.add(cargoType.getCargoTypeId());
                }
                if (ArrayUtils.contains(cargoTypes.toArray(), cargoType.getCargoTypeId())) {
                    c.add(cargoType.getCargoTypeId());
                    for (String a : ancestors) {
                        c.add(Convert.toLong(a));
                    }
                }
            }
            for (MidCargoType midCargoType : midBookingCargoTypes) {
                if (c.contains(midCargoType.getCargoTypeId())) {
                    set.add(midCargoType.getBelongId());
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (list.size() > 0) {
                lists.add(list);
            }
        }
        HashMap<String, List<?>> out = new HashMap<>();
        out.put("list", lists);
        out.put("locationDeparture", Arrays.asList(locationDeparture.toArray()));
        out.put("locationDestination", Arrays.asList(locationDestination.toArray()));
        return out;
    }

    /**
     * 新增订舱单列表
     *
     * @param rsBooking 订舱单列表
     * @return 结果
     */
    @Override
    public Long insertRsBooking(RsBooking rsBooking) {
        rsBooking.setNewBookingNo("RPB" + DateUtils.dateTimeNow() + String.format("%05d", new SecureRandom().nextInt(100000)));
        rsBooking.setNewBookingTime(DateUtils.getNowDate());
        rsBooking.setCreateTime(DateUtils.getNowDate());
        rsBooking.setCreateBy(SecurityUtils.getUserId());
        rsBookingMapper.insertRsBooking(rsBooking);
        insertCargoType(rsBooking);
        insertCarriers(rsBooking);
        insertDeparture(rsBooking);
        insertDestination(rsBooking);
        insertServiceType(rsBooking);
        return rsBooking.getBookingId();
    }

    /**
     * 修改订舱单列表
     *
     * @param rsBooking 订舱单列表
     * @return 结果
     */
    @Override
    public int updateRsBooking(RsBooking rsBooking) {
        rsBooking.setUpdateTime(DateUtils.getNowDate());
        rsBooking.setUpdateBy(SecurityUtils.getUserId());
        midLocationDepartureMapper.deleteMidLocationDepartureById(rsBooking.getBookingId(), "booking");
        midLocationDestinationMapper.deleteMidLocationDestinationById(rsBooking.getBookingId(), "booking");
        midServiceTypeMapper.deleteMidServiceTypeById(rsBooking.getBookingId(), "booking");
        midCarrierMapper.deleteMidCarrierById(rsBooking.getBookingId(), "booking");
        midCargoTypeMapper.deleteMidCargoTypeById(rsBooking.getBookingId(), "booking");
        insertCargoType(rsBooking);
        insertCarriers(rsBooking);
        insertDeparture(rsBooking);
        insertDestination(rsBooking);
        insertServiceType(rsBooking);
        return rsBookingMapper.updateRsBooking(rsBooking);
    }

    /**
     * 修改订舱单列表状态
     *
     * @param rsBooking 订舱单列表
     * @return 订舱单列表
     */
    @Override
    public int changeStatus(RsBooking rsBooking) {
        return rsBookingMapper.updateRsBooking(rsBooking);
    }

    @Override
    public int getMon() {
        return rsBookingMapper.getMon();
    }

    /**
     * 批量删除订舱单列表
     *
     * @param bookingIds 需要删除的订舱单列表主键
     * @return 结果
     */
    @Override
    public int deleteRsBookingByBookingIds(Long[] bookingIds) {
        midLocationDepartureMapper.deleteMidLocationDepartureByIds(bookingIds, "booking");
        midLocationDestinationMapper.deleteMidLocationDestinationByIds(bookingIds, "booking");
        midServiceTypeMapper.deleteMidServiceTypeByIds(bookingIds, "booking");
        midCarrierMapper.deleteMidCarrierByIds(bookingIds, "booking");
        midCargoTypeMapper.deleteMidCargoTypeByIds(bookingIds, "booking");
        rsBookingLogisticsTypeBasicInfoMapper.deleteRsBookingLogisticsTypeBasicInfoByIds(bookingIds);
        rsBookingPreCarriageBasicInfoMapper.deleteRsBookingPreCarriageBasicInfoByIds(bookingIds);
        rsBookingExportDeclarationBasicInfoMapper.deleteRsBookingExportDeclarationBasicInfoByIds(bookingIds);
        rsBookingImportClearanceBasicInfoMapper.deleteRsBookingImportClearanceBasicInfoByIds(bookingIds);
        rsBookingReceivablePayableMapper.deleteRsBookingReceivablePayableByIds(bookingIds);
        return rsBookingMapper.deleteRsBookingByBookingIds(bookingIds);
    }

    /**
     * 删除订舱单列表信息
     *
     * @param bookingId 订舱单列表主键
     * @return 结果
     */
    @Override
    public int deleteRsBookingByBookingId(Long bookingId) {
        midLocationDepartureMapper.deleteMidLocationDepartureById(bookingId, "booking");
        midLocationDestinationMapper.deleteMidLocationDestinationById(bookingId, "booking");
        midServiceTypeMapper.deleteMidServiceTypeById(bookingId, "booking");
        midCarrierMapper.deleteMidCarrierById(bookingId, "booking");
        midCargoTypeMapper.deleteMidCargoTypeById(bookingId, "booking");
        rsBookingLogisticsTypeBasicInfoMapper.deleteRsBookingLogisticsTypeBasicInfoById(bookingId);
        rsBookingPreCarriageBasicInfoMapper.deleteRsBookingPreCarriageBasicInfoById(bookingId);
        rsBookingExportDeclarationBasicInfoMapper.deleteRsBookingExportDeclarationBasicInfoById(bookingId);
        rsBookingImportClearanceBasicInfoMapper.deleteRsBookingImportClearanceBasicInfoById(bookingId);
        rsBookingReceivablePayableMapper.deleteRsBookingReceivablePayableById(bookingId);
        return rsBookingMapper.deleteRsBookingByBookingId(bookingId);
    }

    @Override
    public int saveBookingLogistics(RsBooking rsBooking) {
        int out;
        RsBookingLogisticsTypeBasicInfo rsBookingLogisticsTypeBasicInfo = rsBookingLogisticsTypeBasicInfoMapper.selectRsBookingLogisticsTypeBasicInfoByBookingId(rsBooking.getBookingId());
        // 基础物流信息记录是否存在，存在则更新，不存在新增
        if (rsBookingLogisticsTypeBasicInfo != null) {
            rsBooking.getRsBookingLogisticsTypeBasicInfo().setLogisticsTypeInfoId(rsBookingLogisticsTypeBasicInfo.getLogisticsTypeInfoId());
            out = rsBookingLogisticsTypeBasicInfoMapper.updateRsBookingLogisticsTypeBasicInfo(rsBooking.getRsBookingLogisticsTypeBasicInfo());
        } else {
            rsBookingLogisticsTypeBasicInfo = rsBooking.getRsBookingLogisticsTypeBasicInfo();
            out = rsBookingLogisticsTypeBasicInfoMapper.insertRsBookingLogisticsTypeBasicInfo(rsBookingLogisticsTypeBasicInfo);
        }
        out += rsBookingReceivablePayableMapper.deleteRsBookingReceivablePayable(rsBooking.getBookingId(), rsBookingLogisticsTypeBasicInfo.getTypeId(), rsBookingLogisticsTypeBasicInfo.getLogisticsTypeInfoId());
        // 获取应收应付列表
        List<RsBookingReceivablePayable> rsBookingReceivablePayables = rsBooking.getRsBookingLogisticsTypeBasicInfo().getRsBookingReceivablePayableList();
        // 循环便利插入
        if (rsBookingReceivablePayables != null && !rsBookingReceivablePayables.isEmpty()) {
            for (RsBookingReceivablePayable rsBookingReceivablePayable : rsBookingReceivablePayables) {
                // 基础物流信息记录id
                rsBookingReceivablePayable.setBasicInfoId(rsBookingLogisticsTypeBasicInfo.getLogisticsTypeInfoId());
                // 订舱单记录id
                rsBookingReceivablePayable.setBookingId(rsBooking.getBookingId());
                // 基础物流信息记录服务类型id
                rsBookingReceivablePayable.setTypeId(rsBookingLogisticsTypeBasicInfo.getTypeId());
                out += rsBookingReceivablePayableMapper.insertRsBookingReceivablePayable(rsBookingReceivablePayable);
            }
        }
        return out;
    }

    @Override
    public int saveBookingPreCarriage(RsBooking rsBooking) {
        int out;
        RsBookingPreCarriageBasicInfo rsBookingPreCarriageBasicInfo = rsBookingPreCarriageBasicInfoMapper.selectRsBookingPreCarriageBasicInfoByBookingId(rsBooking.getBookingId());
        if (rsBookingPreCarriageBasicInfo != null) {
            rsBooking.getRsBookingPreCarriageBasicInfo().setPreCarriageInfoId(rsBookingPreCarriageBasicInfo.getPreCarriageInfoId());
            out = rsBookingPreCarriageBasicInfoMapper.updateRsBookingPreCarriageBasicInfo(rsBooking.getRsBookingPreCarriageBasicInfo());
        } else {
            rsBookingPreCarriageBasicInfo = rsBooking.getRsBookingPreCarriageBasicInfo();
            out = rsBookingPreCarriageBasicInfoMapper.insertRsBookingPreCarriageBasicInfo(rsBookingPreCarriageBasicInfo);
        }
        out += rsBookingReceivablePayableMapper.deleteRsBookingReceivablePayable(rsBooking.getBookingId(), rsBookingPreCarriageBasicInfo.getTypeId(), rsBookingPreCarriageBasicInfo.getPreCarriageInfoId());
        List<RsBookingReceivablePayable> rsBookingReceivablePayables = rsBooking.getRsBookingPreCarriageBasicInfo().getRsBookingReceivablePayableList();
        if (rsBookingReceivablePayables != null && rsBookingReceivablePayables.size() > 0) {
            for (RsBookingReceivablePayable rsBookingReceivablePayable : rsBookingReceivablePayables) {
                rsBookingReceivablePayable.setBasicInfoId(rsBookingPreCarriageBasicInfo.getPreCarriageInfoId());
                rsBookingReceivablePayable.setBookingId(rsBooking.getBookingId());
                rsBookingReceivablePayable.setTypeId(rsBookingPreCarriageBasicInfo.getTypeId());
                out += rsBookingReceivablePayableMapper.insertRsBookingReceivablePayable(rsBookingReceivablePayable);
            }
        }
        return out;
    }

    @Override
    public int saveBookingExportDeclaration(RsBooking rsBooking) {
        int out;
        RsBookingExportDeclarationBasicInfo rsBookingExportDeclarationBasicInfo = rsBookingExportDeclarationBasicInfoMapper.selectRsBookingExportDeclarationBasicInfoByBookingId(rsBooking.getBookingId());
        if (rsBookingExportDeclarationBasicInfo != null) {
            rsBooking.getRsBookingExportDeclarationBasicInfo().setExportDeclarationId(rsBookingExportDeclarationBasicInfo.getExportDeclarationId());
            out = rsBookingExportDeclarationBasicInfoMapper.updateRsBookingExportDeclarationBasicInfo(rsBooking.getRsBookingExportDeclarationBasicInfo());
        } else {
            rsBookingExportDeclarationBasicInfo = rsBooking.getRsBookingExportDeclarationBasicInfo();
            out = rsBookingExportDeclarationBasicInfoMapper.insertRsBookingExportDeclarationBasicInfo(rsBookingExportDeclarationBasicInfo);
        }
        out += rsBookingReceivablePayableMapper.deleteRsBookingReceivablePayable(rsBooking.getBookingId(), rsBookingExportDeclarationBasicInfo.getTypeId(), rsBookingExportDeclarationBasicInfo.getExportDeclarationId());
        List<RsBookingReceivablePayable> rsBookingReceivablePayables = rsBooking.getRsBookingExportDeclarationBasicInfo().getRsBookingReceivablePayableList();
        if (rsBookingReceivablePayables != null && rsBookingReceivablePayables.size() > 0) {
            for (RsBookingReceivablePayable rsBookingReceivablePayable : rsBookingReceivablePayables) {
                rsBookingReceivablePayable.setBasicInfoId(rsBookingExportDeclarationBasicInfo.getExportDeclarationId());
                rsBookingReceivablePayable.setBookingId(rsBooking.getBookingId());
                rsBookingReceivablePayable.setTypeId(rsBookingExportDeclarationBasicInfo.getTypeId());
                out += rsBookingReceivablePayableMapper.insertRsBookingReceivablePayable(rsBookingReceivablePayable);
            }
        }
        return out;
    }

    @Override
    public int saveBookingImportClearance(RsBooking rsBooking) {
        int out;
        RsBookingImportClearanceBasicInfo rsBookingImportClearanceBasicInfo = rsBookingImportClearanceBasicInfoMapper.selectRsBookingImportClearanceBasicInfoByBookingId(rsBooking.getBookingId());
        if (rsBookingImportClearanceBasicInfo != null) {
            rsBooking.getRsBookingImportClearanceBasicInfo().setImportClearanceId(rsBookingImportClearanceBasicInfo.getImportClearanceId());
            out = rsBookingImportClearanceBasicInfoMapper.updateRsBookingImportClearanceBasicInfo(rsBooking.getRsBookingImportClearanceBasicInfo());
        } else {
            rsBookingImportClearanceBasicInfo = rsBooking.getRsBookingImportClearanceBasicInfo();
            out = rsBookingImportClearanceBasicInfoMapper.insertRsBookingImportClearanceBasicInfo(rsBookingImportClearanceBasicInfo);
        }
        out += rsBookingReceivablePayableMapper.deleteRsBookingReceivablePayable(rsBooking.getBookingId(), rsBookingImportClearanceBasicInfo.getTypeId(), rsBookingImportClearanceBasicInfo.getImportClearanceId());
        List<RsBookingReceivablePayable> rsBookingReceivablePayables = rsBooking.getRsBookingImportClearanceBasicInfo().getRsBookingReceivablePayableList();
        if (rsBookingReceivablePayables != null && rsBookingReceivablePayables.size() > 0) {
            for (RsBookingReceivablePayable rsBookingReceivablePayable : rsBookingReceivablePayables) {
                rsBookingReceivablePayable.setBasicInfoId(rsBookingImportClearanceBasicInfo.getImportClearanceId());
                rsBookingReceivablePayable.setBookingId(rsBooking.getBookingId());
                rsBookingReceivablePayable.setTypeId(rsBookingImportClearanceBasicInfo.getTypeId());
                out += rsBookingReceivablePayableMapper.insertRsBookingReceivablePayable(rsBookingReceivablePayable);
            }
        }
        return out;
    }

    private void insertCarriers(RsBooking rsBooking) {
        Long[] roles = rsBooking.getCarrierIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidCarrier> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidCarrier midCarrier = new MidCarrier();
                midCarrier.setBelongId(rsBooking.getBookingId());
                midCarrier.setCarrierId(r);
                midCarrier.setBelongTo("booking");
                list.add(midCarrier);
            }
            midCarrierMapper.batchCarrier(list);
        }
        RedisCache.midCarrier("booking", "bookingCarriers");
    }

    private void insertDeparture(RsBooking rsBooking) {
        Long[] roles = rsBooking.getPolIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidLocationDeparture> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidLocationDeparture MidLocationDeparture = new MidLocationDeparture();
                MidLocationDeparture.setBelongId(rsBooking.getBookingId());
                MidLocationDeparture.setLocationId(r);
                MidLocationDeparture.setBelongTo("booking");
                list.add(MidLocationDeparture);
            }
            midLocationDepartureMapper.batchLD(list);
        }
        RedisCache.locationDeparture("booking", "bookingLocationDeparture");
    }

    private void insertDestination(RsBooking rsBooking) {
        Long[] roles = rsBooking.getDestinationPortIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidLocationDestination> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidLocationDestination MidLocationDestination = new MidLocationDestination();
                MidLocationDestination.setBelongId(rsBooking.getBookingId());
                MidLocationDestination.setLocationId(r);
                MidLocationDestination.setBelongTo("booking");
                list.add(MidLocationDestination);
            }
            midLocationDestinationMapper.batchLD(list);
        }
        RedisCache.locationDestination("booking", "bookingLocationDestination");

    }

    private void insertCargoType(RsBooking rsBooking) {
        Long[] roles = rsBooking.getCargoTypeIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidCargoType> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidCargoType MidCargoType = new MidCargoType();
                MidCargoType.setBelongId(rsBooking.getBookingId());
                MidCargoType.setBelongTo("booking");
                MidCargoType.setCargoTypeId(r);
                list.add(MidCargoType);
            }
            midCargoTypeMapper.batchCargoType(list);
        }
        RedisCache.midCargoType("booking", "bookingCargoType");
    }

    private void insertServiceType(RsBooking rsBooking) {
        Long[] roles = rsBooking.getServiceTypeIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidServiceType> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidServiceType MidServiceType = new MidServiceType();
                MidServiceType.setBelongId(rsBooking.getBookingId());
                MidServiceType.setServiceTypeId(r);
                MidServiceType.setBelongTo("booking");
                list.add(MidServiceType);
            }
            midServiceTypeMapper.batchServiceType(list);
        }
        RedisCache.midServiceType("booking", "bookingServiceType");
    }
}
