package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpClearAgent;
import com.rich.common.core.domain.entity.RsOpDOAgent;
import com.rich.common.core.domain.entity.RsOpImportCustomsClearance;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 进口清关服务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Mapper
public interface RsOpImportCustomsClearanceMapper {
    /**
     * 查询进口清关服务
     *
     * @param importCustomsClearanceId 进口清关服务主键
     * @return 进口清关服务
     */
    RsOpImportCustomsClearance selectRsOpImportCustomsClearanceByImportCustomsClearanceId(Long importCustomsClearanceId);

    /**
     * 查询进口清关服务列表
     *
     * @param rsOpImportCustomsClearance 进口清关服务
     * @return 进口清关服务集合
     */
    List<RsOpImportCustomsClearance> selectRsOpImportCustomsClearanceList(RsOpImportCustomsClearance rsOpImportCustomsClearance);

    /**
     * 新增进口清关服务
     *
     * @param rsOpImportCustomsClearance 进口清关服务
     * @return 结果
     */
    int insertRsOpImportCustomsClearance(RsOpImportCustomsClearance rsOpImportCustomsClearance);

    int insertRsOpImportCustomsClearance(RsOpDOAgent rsOpDOAgent);

    int insertRsOpImportCustomsClearance(RsOpClearAgent rsOpClearAgent);

    /**
     * 修改进口清关服务
     *
     * @param rsOpImportCustomsClearance 进口清关服务
     * @return 结果
     */
    int updateRsOpImportCustomsClearance(RsOpImportCustomsClearance rsOpImportCustomsClearance);

    int updateRsOpImportCustomsClearance(RsOpDOAgent rsOpDOAgent);

    int updateRsOpImportCustomsClearance(RsOpClearAgent rsOpClearAgent);

    /**
     * 删除进口清关服务
     *
     * @param importCustomsClearanceId 进口清关服务主键
     * @return 结果
     */
    int deleteRsOpImportCustomsClearanceByImportCustomsClearanceId(Long importCustomsClearanceId);

    /**
     * 批量删除进口清关服务
     *
     * @param importCustomsClearanceIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsOpImportCustomsClearanceByImportCustomsClearanceIds(Long[] importCustomsClearanceIds);

    RsOpImportCustomsClearance selectRsOpImportCustomsClearanceByRctId(Long rctId);

    RsOpDOAgent selectRsOpDOAgentByRctId(@Param("rctId") Long rctId, @Param("sqdServiceTypeId") Long sqdServiceTypeId);

    RsOpClearAgent selectRsOpClearAgentByRctId(@Param("rctId") Long rctId, @Param("sqdServiceTypeId") Long sqdServiceTypeId);
}
