# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Rich Management System (瑞旗管理系统) is a multi-module enterprise resource planning (ERP) system focused on logistics and freight management. It consists of:

- **Backend**: Spring Boot 2.5.14 + MyBatis multi-module Maven project
- **Web Frontend**: Vue 2.6.12 + Element UI 2.15.14 admin dashboard (`rich-ui`)
- **Mobile App**: UniApp cross-platform application (`rich-app`)

## Essential Commands

### Backend Development

```bash
# Build entire project
mvn clean package

# Run in development mode
mvn spring-boot:run -pl rich-admin -Dspring.profiles.active=dev

# Run tests
mvn test

# Build specific module
mvn clean package -pl rich-admin -am

# Skip tests during build
mvn clean package -DskipTests
```

### Frontend Development (rich-ui)

```bash
cd rich-ui
npm install                  # Install dependencies
npm run dev                 # Start dev server (port 80)
npm run build:prod          # Production build
npm run build:stage         # Staging build
```

### Mobile Development (rich-app)

```bash
cd rich-app
npm install                  # Install dependencies
npm run serve               # H5 development server
npm run build               # H5 production build
npm run dev:mp-weixin       # WeChat mini-program dev
npm run build:mp-weixin     # WeChat mini-program build
npm run test:h5             # Run H5 tests
npm run test:mp-weixin      # Run WeChat mini-program tests
```

## High-Level Architecture

### Backend Module Structure

1. **rich-admin**: Application entry point, controllers, and web configurations
   - Controllers follow pattern: `*/controller/system/*Controller.java`
   - Main class: `RichApplication.java`
   - Configuration: `application-{profile}.yml`

2. **rich-common**: Shared utilities, annotations, domain entities, and exceptions
   - Domain entities: `core/domain/entity/*`
   - Utils: `utils/*` (StringUtils, DateUtils, SecurityUtils, etc.)
   - Custom annotations for logging, rate limiting, data permissions

3. **rich-framework**: Core framework components
   - Security configuration with JWT authentication
   - AOP aspects for logging, data permissions, rate limiting
   - Global exception handling
   - Redis caching configuration

4. **rich-system**: Business logic implementation
   - Services follow pattern: `*/service/impl/*ServiceImpl.java`
   - MyBatis mappers: `*/mapper/*Mapper.java`
   - XML mappings: `resources/mapper/system/*Mapper.xml`

5. **rich-quartz**: Scheduled task management
   - Job scheduling with Quartz framework
   - Task execution and monitoring

6. **rich-generator**: Code generation for CRUD operations

### Frontend Architecture (rich-ui)

- **Entry**: `src/main.js` - Vue initialization
- **Routing**: `src/router/index.js` - Route definitions
- **Store**: `src/store/` - Vuex state management
- **API**: `src/api/` - Backend API calls using axios
- **Views**: `src/views/` - Page components organized by module
- **Components**: `src/components/` - Reusable UI components
- **Utils**: `src/utils/request.js` - HTTP request interceptor with token handling

### Mobile Architecture (rich-app)

- **Entry**: `src/main.js` - UniApp initialization
- **Pages**: `src/pages/` - Application pages
- **Components**: `src/componetsPackage/` - Custom components
- **API**: `src/api/` - Backend API integration
- **Store**: `src/store/` - Vuex state management
- **Platform**: Supports H5, WeChat mini-program, and native apps

## Key Development Patterns

### Backend API Pattern

```java
// Controller: Handle HTTP requests
@RestController
@RequestMapping("/system/user")
public class SysUserController extends BaseController {
    @GetMapping("/list")
    public TableDataInfo list(SysUser user) {
        startPage(); // Pagination
        List<SysUser> list = userService.selectUserList(user);
        return getDataTable(list);
    }
}

// Service: Business logic
@Service
public class SysUserServiceImpl implements ISysUserService {
    @Override
    public List<SysUser> selectUserList(SysUser user) {
        return userMapper.selectUserList(user);
    }
}
```

### Frontend API Call Pattern

```javascript
// API definition
export function listUser(query) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: query
  })
}

// Usage in component
async getList() {
  this.loading = true;
  const res = await listUser(this.queryParams);
  this.userList = res.rows;
  this.total = res.total;
  this.loading = false;
}
```

## Important Configuration

### Backend Ports and Paths
- Application Port: **8088**
- Context Path: `/`
- Swagger UI: `http://localhost:8088/swagger-ui/index.html`
- File Upload Limits: 10MB single file, 20MB total

### Frontend Development
- rich-ui Dev Server: **Port 80**
- API Proxy: Points to `http://localhost:8088`
- Build Output: `dist/` directory

### Database
- MySQL with MyBatis
- PageHelper for pagination
- Druid connection pool
- Database migrations in `sql/` directory

### Security
- JWT token authentication (360 minute expiry)
- RBAC permission system
- XSS and SQL injection protection
- Request rate limiting

## Common Development Tasks

### Adding a New CRUD Module

1. Generate code using rich-generator module
2. Place generated files in appropriate modules:
   - Entity → rich-common
   - Mapper → rich-system
   - Service → rich-system
   - Controller → rich-admin
3. Add menu permissions in database
4. Create frontend pages in rich-ui/src/views/

### Running a Single Test

```bash
# Backend: Run specific test class
mvn test -Dtest=YourTestClass

# Frontend: Run specific test file
npm run test:unit -- YourTest.spec.js
```

### Debugging

- Backend: Enable debug logging in `application-dev.yml`
- Frontend: Vue DevTools for component inspection
- Mobile: Use browser DevTools for H5, WeChat DevTools for mini-program

## Critical System Components

### Permission System
- Menu-based permissions stored in `sys_menu` table
- Role-permission mapping in `sys_role_menu`
- Data permissions with `@DataScope` annotation
- Permission checking with `@PreAuthorize`

### Multi-tenant Architecture
- Company-based data isolation
- Company ID in most business tables
- Automatic company filtering in queries

### Real-time Features
- WebSocket for notifications (`NotificationWebSocket`)
- Canal for database change detection
- Redis pub/sub for distributed events

### File Handling
- Local file storage in `profile.upload` directory
- Support for images, documents, Excel files
- File metadata stored in database

## Development Workflow

1. Create feature branch from develop
2. Implement backend API (entity → mapper → service → controller)
3. Generate and customize frontend pages
4. Write unit tests
5. Test integration between backend and frontend
6. Submit PR to develop branch

## Recent Updates

### Transaction Management Optimization (2025-06-24)
- **Issue**: Nested @Transactional annotations causing transaction management issues in RsRctServiceImpl
- **Solution**: 
  - Removed @Transactional from all sub-methods (save3rdCert, saveINS, saveTrading, etc.)
  - Centralized transaction management in main methods (saveAllServices, saveAsAllServices)
  - Added isolation level (READ_COMMITTED) and timeout (300 seconds) to main transactions
  - Enhanced error handling with try-catch blocks and comprehensive logging
- **Benefits**:
  - Eliminates nested transaction conflicts
  - Ensures all operations within a single transaction boundary
  - Better error propagation and rollback behavior
  - Improved debugging with detailed error logs
- **Note**: Kept @Transactional on saveClientMessage as it may be called independently

### Code Quality Improvements
- Refactored repetitive code in RsRctServiceImpl into helper methods:
  - `processListServices()`: Handles list-type services (SeaFcl, SeaLcl, Air, etc.)
  - `processSingleServices()`: Handles single service objects (RailFCL, RailLCL, etc.)
  - `processExpandServices()`: Handles extension services (3rdCert, INS, Trading, etc.)
- Maintained exact business logic while improving readability and maintainability

## Troubleshooting

- **CORS issues**: Check `SecurityConfig` and `ResourcesConfig`
- **Token expiration**: Verify token settings in `TokenService`
- **Database connection**: Check datasource config in `application-{profile}.yml`
- **File upload failures**: Verify upload directory permissions
- **Build failures**: Ensure all Maven dependencies are available
- **Transaction issues**: Check for nested @Transactional annotations and ensure proper propagation settings

Always answer in Chinese