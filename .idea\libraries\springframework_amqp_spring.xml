<component name="libraryTable">
  <library name="springframework.amqp.spring" type="repository">
    <properties maven-id="org.springframework.amqp:spring-amqp:2.3.16" />
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/amqp/spring-amqp/2.3.16/spring-amqp-2.3.16.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/spring-core/5.3.19/spring-core-5.3.19.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/spring-jcl/5.3.19/spring-jcl-5.3.19.jar!/" />
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/retry/spring-retry/1.3.3/spring-retry-1.3.3.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>