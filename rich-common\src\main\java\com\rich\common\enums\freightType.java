package com.rich.common.enums;

/**
 * <AUTHOR>
 * @Date 2023/6/12 14:48
 * @Version 1.0
 */
public enum freightType {
    seaFreight("1","海运费"),
    airFreight("2","空运费"),
    expressDelivery("3","快递费"),
    trailer("4","拖车费"),
    declare("5", "报关费"),
    benchmark("6","检测费"),
    insurance("7","保险费"),
    warehouse("8","仓储费");

    freightType(String code, String info) {
        this.code = code;
        this.info = info;
    }

    private final String code;
    private final String info;

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
