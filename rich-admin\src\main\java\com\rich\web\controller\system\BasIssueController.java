package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasIssue;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasIssueService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 问题Controller
 *
 * <AUTHOR>
 * @date 2022-10-18
 */
@RestController
@RequestMapping("/system/issue")

public class BasIssueController extends BaseController {

    @Autowired
    private BasIssueService basIssueService;


    @Autowired
    private RedisCache redisCache;

    /**
     * 查询问题列表
     */
    @PreAuthorize("@ss.hasPermi('system:issue:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasIssue basIssue) {
        startPage();
        List<BasIssue> list = basIssueService.selectBasIssueList(basIssue);
        return getDataTable(list);
    }

    @GetMapping("/selectList")
    public AjaxResult selectList(BasIssue basIssue) {
        List<BasIssue> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "issue");
        if (list == null) {
            basIssue.setStatus("0");
            list = basIssueService.selectBasIssueList(basIssue);
            redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "issue");
            redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "issue", list);
        }
        return AjaxResult.success(AjaxResult.DATA_TAG, list);
    }

    /**
     * 导出问题列表
     */
    @PreAuthorize("@ss.hasPermi('system:issue:export')")
    @Log(title = "问题", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasIssue basIssue) {
        List<BasIssue> list = basIssueService.selectBasIssueList(basIssue);
        ExcelUtil<BasIssue> util = new ExcelUtil<BasIssue>(BasIssue.class);
        util.exportExcel(response, list, "问题数据");
    }

    /**
     * 获取问题详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:issue:edit')")
    @GetMapping(value = "/{issueId}")
    public AjaxResult getInfo(@PathVariable("issueId") Long issueId) {
        return AjaxResult.success(basIssueService.selectBasIssueByIssueId(issueId));
    }

    /**
     * 新增问题
     */
    @PreAuthorize("@ss.hasPermi('system:issue:add')")
    @Log(title = "问题", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasIssue basIssue) {
        int out = basIssueService.insertBasIssue(basIssue);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "issue");
        return toAjax(out);
    }

    /**
     * 修改问题
     */
    @PreAuthorize("@ss.hasPermi('system:issue:edit')")
    @Log(title = "问题", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasIssue basIssue) {
        int out = basIssueService.updateBasIssue(basIssue);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "issue");
        return toAjax(out);
    }

    /**
     * 修改问题
     */
    @PreAuthorize("@ss.hasPermi('system:issue:edit')")
    @Log(title = "问题", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasIssue basIssue) {
        int out = basIssueService.changeStatus(basIssue);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "issue");
        return toAjax(out);
    }

    /**
     * 删除问题
     */
    @PreAuthorize("@ss.hasPermi('system:issue:remove')")
    @Log(title = "问题", businessType = BusinessType.DELETE)
    @DeleteMapping("/{issueIds}")
    public AjaxResult remove(@PathVariable Long[] issueIds) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "issue");
        return toAjax(basIssueService.deleteBasIssueByIssueIds(issueIds));
    }
}
