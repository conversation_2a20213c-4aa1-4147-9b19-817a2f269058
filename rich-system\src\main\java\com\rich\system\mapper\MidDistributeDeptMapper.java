package com.rich.system.mapper;

import com.rich.system.domain.MidDistributeDept;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-03-20
 */
@Mapper
public interface MidDistributeDeptMapper {
    /**
     * 查询【请填写功能名称】
     *
     * @param distributeId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    List<Long> selectMidDistributeDeptByDistributeId(Long distributeId);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param midDistributeDept 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    List<MidDistributeDept> selectMidDistributeDeptList(MidDistributeDept midDistributeDept);

    /**
     * 新增【请填写功能名称】
     *
     * @param midDistributeDept 【请填写功能名称】
     * @return 结果
     */
    int insertMidDistributeDept(MidDistributeDept midDistributeDept);

    /**
     * 修改【请填写功能名称】
     *
     * @param midDistributeDept 【请填写功能名称】
     * @return 结果
     */
    int updateMidDistributeDept(MidDistributeDept midDistributeDept);

    /**
     * 删除【请填写功能名称】
     *
     * @param distributeId 【请填写功能名称】主键
     * @return 结果
     */
    int deleteMidDistributeDeptByDistributeId(Long distributeId);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param distributeIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteMidDistributeDeptByDistributeIds(Long[] distributeIds);

    int batchDept(List<MidDistributeDept> item);
}
