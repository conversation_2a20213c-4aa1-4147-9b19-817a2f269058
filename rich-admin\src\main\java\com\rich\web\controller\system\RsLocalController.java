package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.RsFreight;
import com.rich.common.core.domain.entity.RsLocalCharge;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasDistLocationService;
import com.rich.system.service.RsLocalService;
import com.rich.system.service.impl.RedisCacheImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 物流附加费策略Controller
 *
 * <AUTHOR>
 * @date 2022-12-14
 */
@RestController
@RequestMapping("/system/local")
public class RsLocalController extends BaseController {

    @Autowired
    private  RsLocalService rsLocalService;


    @Autowired
    private  BasDistLocationService basDistLocationService;

    @Autowired
    private com.rich.common.core.redis.RedisCache redisCache;
    @Autowired
    private RedisCacheImpl RedisCache;

    /**
     * 查询物流附加费策略列表
     */
    @PreAuthorize("@ss.hasPermi('system:local:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsLocalCharge RsLocalCharge) {
        List<RsLocalCharge> list = rsLocalService.selectRsLocalList(RsLocalCharge);
        if (list != null && !list.isEmpty()) {
            return getDataTable(list);
        } else {
            return null;
        }
    }

    /**
     * 导出物流附加费策略列表
     */
    @PreAuthorize("@ss.hasPermi('system:local:export')")
    @Log(title = "物流附加费策略", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsLocalCharge RsLocalCharge) {
        List<RsLocalCharge> list = rsLocalService.selectRsLocalList(RsLocalCharge);
        ExcelUtil<RsLocalCharge> util = new ExcelUtil<RsLocalCharge>(RsLocalCharge.class);
        util.exportExcel(response, list, "物流附加费策略数据");
    }

    /**
     * 获取物流附加费策略详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:local:edit')")
    @GetMapping(value = "/{localChargeId}")
    public AjaxResult getInfo(@PathVariable("localChargeId") Long localId) {
        AjaxResult ajaxResult = AjaxResult.success();
        Set<Long> set = new HashSet<>();
        List<Long> locationDepartureIds = rsLocalService.selectLocationDeparture(localId);
        List<Long> locationDestinationIds = rsLocalService.selectLocationDestination(localId);
        if (!locationDepartureIds.isEmpty()) {
            set.addAll(locationDepartureIds);
        }
        if (!locationDestinationIds.isEmpty()) {
            set.addAll(locationDestinationIds);
        }
        ajaxResult.put(AjaxResult.DATA_TAG, rsLocalService.selectRsLocalByLocalId(localId));
        ajaxResult.put("cargoTypeIds", rsLocalService.selectCargoTypes(localId));
        ajaxResult.put("locationDepartureIds", locationDepartureIds);
        ajaxResult.put("locationDestinationIds", locationDestinationIds);
        ajaxResult.put("locationOptions", !set.isEmpty() ? basDistLocationService.selectBasDistLocationByIds(set) : null);
        ajaxResult.put("lineDepartureIds", rsLocalService.selectLineDeparture(localId));
        ajaxResult.put("lineDestinationIds", rsLocalService.selectLineDestination(localId));
        ajaxResult.put("carrierIds", rsLocalService.selectCarriers(localId));
        return ajaxResult;
    }

    /**
     * 新增物流附加费策略
     */
    @PreAuthorize("@ss.hasPermi('system:local:add')")
    @Log(title = "物流附加费策略", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsLocalCharge RsLocalCharge) {
        return toAjax(rsLocalService.insertRsLocal(RsLocalCharge));
    }

    /**
     * 修改物流附加费策略
     */
    @PreAuthorize("@ss.hasPermi('system:local:edit')")
    @Log(title = "物流附加费策略", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsLocalCharge RsLocalCharge) {
        return toAjax(rsLocalService.updateRsLocal(RsLocalCharge));
    }

    /**
     * 删除物流附加费策略
     */
    @PreAuthorize("@ss.hasPermi('system:local:remove')")
    @Log(title = "物流附加费策略", businessType = BusinessType.DELETE)
    @DeleteMapping("/{localIds}")
    public AjaxResult remove(@PathVariable Long[] localIds) {
        return toAjax(rsLocalService.deleteRsLocalByLocalIds(localIds));
    }

    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        // 移除缓存
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "freight");
        ExcelUtil<RsLocalCharge> util = new ExcelUtil<>(RsLocalCharge.class);
        // 解析excel文件,获取数据
        List<RsLocalCharge> freightList = util.importExcel(file.getInputStream());
        List<RsLocalCharge> failList = rsLocalService.importFreight(freightList, updateSupport);
        if (!failList.isEmpty()) {
            redisCache.setCacheObject("importFailList-local", failList);
            return AjaxResult.success("上传失败列表");
        } else {
            return AjaxResult.success("全部上传成功");
        }
    }

    @PostMapping("/failList")
    public void failList(HttpServletResponse response) {
        List<RsLocalCharge> list = redisCache.getCacheObject("importFailList-local");
        ExcelUtil<RsLocalCharge> util = new ExcelUtil<>(RsLocalCharge.class);
        util.exportExcel(response, list, "上传失败列表");
        redisCache.deleteObject("importFailList-local");
    }
}
