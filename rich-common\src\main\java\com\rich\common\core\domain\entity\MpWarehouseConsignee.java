package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 收货人信息对象 mp_warehouse_consignee
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
public class MpWarehouseConsignee extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 收货人ID
     */
    private Long consigneeId;

    /**
     * 所属客户ID
     */
    @Excel(name = "所属客户ID")
    private Long clientId;

    /**
     * 收货人代码
     */
    @Excel(name = "收货人代码")
    private String consigneeCode;

    /**
     * 收货人姓名
     */
    @Excel(name = "收货人姓名")
    private String consigneeName;

    /**
     * 收货人电话
     */
    @Excel(name = "收货人电话")
    private String consigneeTel;

    /**
     * 备注信息
     */
    @Excel(name = "备注信息")
    private String remarks;
    private String clientRegion;
    private String clientCode;
    private MpWarehouseClient mpWarehouseClient;
    private Long userId;

    /**
     * 标记收货人代码是否已存在(用于查重返回)
     */
    private transient boolean exist;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public MpWarehouseClient getMpWarehouseClient() {
        return mpWarehouseClient;
    }

    public void setMpWarehouseClient(MpWarehouseClient mpWarehouseClient) {
        this.mpWarehouseClient = mpWarehouseClient;
    }

    public String getClientCode() {
        return clientCode;
    }

    public void setClientCode(String clientCode) {
        this.clientCode = clientCode;
    }

    public String getClientRegion() {
        return clientRegion;
    }

    public void setClientRegion(String clientRegion) {
        this.clientRegion = clientRegion;
    }

    public Long getConsigneeId() {
        return consigneeId;
    }

    public void setConsigneeId(Long consigneeId) {
        this.consigneeId = consigneeId;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public String getConsigneeCode() {
        return consigneeCode;
    }

    public void setConsigneeCode(String consigneeCode) {
        this.consigneeCode = consigneeCode;
    }

    public String getConsigneeName() {
        return consigneeName;
    }

    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName;
    }

    public String getConsigneeTel() {
        return consigneeTel;
    }

    public void setConsigneeTel(String consigneeTel) {
        this.consigneeTel = consigneeTel;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public boolean isExist() {
        return exist;
    }

    public void setExist(boolean exist) {
        this.exist = exist;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("consigneeId", getConsigneeId())
                .append("clientId", getClientId())
                .append("consigneeCode", getConsigneeCode())
                .append("consigneeName", getConsigneeName())
                .append("consigneeTel", getConsigneeTel())
                .append("remarks", getRemarks())
                .append("exist", isExist())
                .toString();
    }
}
