package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasDocFlowDirection;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasDocFlowDirectionService;
import com.rich.system.service.impl.RedisCacheImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 文件流向Controller
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@RestController
@RequestMapping("/system/docflowdirection")
public class BasDocFlowDirectionController extends BaseController {
    @Autowired
    private BasDocFlowDirectionService basDocFlowDirectionService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisCacheImpl RedisCache;

    /**
     * 查询文件流向列表
     */
    @PreAuthorize("@ss.hasPermi('system:docflowdirection:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasDocFlowDirection basDocFlowDirection) {
        startPage();
        List<BasDocFlowDirection> list = basDocFlowDirectionService.selectBasDocFlowDirectionList(basDocFlowDirection);
        return getDataTable(list);
    }

    @GetMapping("/selectList")
    public AjaxResult selectList() {
        List<BasDocFlowDirection> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "docFlowDirection");
        if (list == null) {
            RedisCache.docFlowDirection();
            list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "docFlowDirection");
        }
        return AjaxResult.success(list);
    }

    /**
     * 导出文件流向列表
     */
    @PreAuthorize("@ss.hasPermi('system:docflowdirection:export')")
    @Log(title = "文件流向", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasDocFlowDirection basDocFlowDirection) {
        List<BasDocFlowDirection> list = basDocFlowDirectionService.selectBasDocFlowDirectionList(basDocFlowDirection);
        ExcelUtil<BasDocFlowDirection> util = new ExcelUtil<BasDocFlowDirection>(BasDocFlowDirection.class);
        util.exportExcel(response, list, "文件流向数据");
    }

    /**
     * 获取文件流向详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:docflowdirection:query')")
    @GetMapping(value = "/{docFlowDirectionId}")
    public AjaxResult getInfo(@PathVariable("docFlowDirectionId") Long docFlowDirectionId) {
        return AjaxResult.success(basDocFlowDirectionService.selectBasDocFlowDirectionByDocFlowDirectionId(docFlowDirectionId));
    }

    /**
     * 新增文件流向
     */
    @PreAuthorize("@ss.hasPermi('system:docflowdirection:add')")
    @Log(title = "文件流向", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasDocFlowDirection basDocFlowDirection) {
        return toAjax(basDocFlowDirectionService.insertBasDocFlowDirection(basDocFlowDirection));
    }

    /**
     * 修改文件流向
     */
    @PreAuthorize("@ss.hasPermi('system:docflowdirection:edit')")
    @Log(title = "文件流向", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasDocFlowDirection basDocFlowDirection) {
        return toAjax(basDocFlowDirectionService.updateBasDocFlowDirection(basDocFlowDirection));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:docflowdirection:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasDocFlowDirection basDocFlowDirection) {
        basDocFlowDirection.setUpdateBy(getUserId());
        return toAjax(basDocFlowDirectionService.changeStatus(basDocFlowDirection));
    }

    /**
     * 删除文件流向
     */
    @PreAuthorize("@ss.hasPermi('system:docflowdirection:remove')")
    @Log(title = "文件流向", businessType = BusinessType.DELETE)
    @DeleteMapping("/{docFlowDirectionIds}")
    public AjaxResult remove(@PathVariable Long[] docFlowDirectionIds) {
        return toAjax(basDocFlowDirectionService.deleteBasDocFlowDirectionByDocFlowDirectionIds(docFlowDirectionIds));
    }
}
