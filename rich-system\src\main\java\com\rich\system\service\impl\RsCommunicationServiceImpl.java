package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.RsCommunication;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.system.mapper.RsCommunicationMapper;
import com.rich.system.service.RsCommunicationService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 交流Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-10-18
 */
@Service

public class RsCommunicationServiceImpl implements RsCommunicationService {
    @Autowired
    private  RsCommunicationMapper rsCommunicationMapper;

    /**
     * 查询交流
     *
     * @param communicationId 交流主键
     * @return 交流
     */
    @Override
    public RsCommunication selectRsCommunicationByCommunicationId(Long communicationId) {
        return rsCommunicationMapper.selectRsCommunicationByCommunicationId(communicationId);
    }

    /**
     * 查询交流列表
     *
     * @param rsCommunication 交流
     * @return 交流
     */
    @Override
    public List<RsCommunication> selectRsCommunicationList(RsCommunication rsCommunication) {
        return rsCommunicationMapper.selectRsCommunicationList(rsCommunication);
    }

    /**
     * 新增交流
     *
     * @param rsCommunication 交流
     * @return 结果
     */
    @Override
    public int insertRsCommunication(RsCommunication rsCommunication) {
        rsCommunication.setCreateTime(DateUtils.getNowDate());
        rsCommunication.setCreateBy(SecurityUtils.getUserId());
        rsCommunication.setStaffId(SecurityUtils.getUserId());
        return rsCommunicationMapper.insertRsCommunication(rsCommunication);
    }

    /**
     * 修改交流
     *
     * @param rsCommunication 交流
     * @return 结果
     */
    @Override
    public int updateRsCommunication(RsCommunication rsCommunication) {
        rsCommunication.setUpdateBy(SecurityUtils.getUserId());
        rsCommunication.setUpdateTime(DateUtils.getNowDate());
        return rsCommunicationMapper.updateRsCommunication(rsCommunication);
    }

    /**
     * 批量删除交流
     *
     * @param communicationIds 需要删除的交流主键
     * @return 结果
     */
    @Override
    public int deleteRsCommunicationByCommunicationIds(Long[] communicationIds) {
        return rsCommunicationMapper.deleteRsCommunicationByCommunicationIds(communicationIds);
    }

    /**
     * 删除交流信息
     *
     * @param communicationId 交流主键
     * @return 结果
     */
    @Override
    public int deleteRsCommunicationByCommunicationId(Long communicationId) {
        return rsCommunicationMapper.deleteRsCommunicationByCommunicationId(communicationId);
    }
}
