<template>
  <view :style="{ height: `${windowHeight}px` }" class="mine-container">
    <!--顶部个人信息栏-->
    <view class="header-section">
      <view class="flex padding justify-between">
        <view class="flex align-center">
          <view v-if="!avatar" class="cu-avatar xl round bg-white">
            <view class="iconfont icon-people text-gray icon"></view>
          </view>
          <image v-if="avatar" :src="avatar" class="cu-avatar xl round" mode="widthFix" @click="handleToAvatar">
          </image>
          <view v-if="!name" class="login-tip" @click="handleToLogin">
            点击登录
          </view>
          <view v-if="name" class="user-info" @click="handleToInfo">
            <view class="u_title">
              用户名：{{ name }}
            </view>
          </view>
        </view>
        <view class="flex align-center" @click="handleToInfo">
          <text>个人信息</text>
          <view class="iconfont icon-right"></view>
        </view>
      </view>
    </view>

    <view class="content-section">
      <view class="mine-actions grid col-3 text-center">
        <view class="action-item" @click="handleBuilding">
          <uni-icons size="30" type="contact-filled"></uni-icons>
          <text class="text">联系仓管</text>
        </view>
        <view class="action-item" @click="handleBuilding">
          <uni-icons size="30" type="location-filled"></uni-icons>
          <text class="text">仓库详址</text>
        </view>
        <view class="action-item" @click="handleBuilding">
          <uni-icons size="30" type="hand-up-filled"></uni-icons>
          <text class="text">点赞我们</text>
        </view>
      </view>

      <view class="menu-list">
        <view class="list-cell list-cell-arrow" @click="handleToEditInfo">
          <view class="menu-item-box">
            <view class="iconfont icon-user menu-icon"></view>
            <view>编辑资料</view>
          </view>
        </view>
        <view class="list-cell list-cell-arrow" @click="handleAbout">
          <view class="menu-item-box">
            <view class="iconfont icon-aixin menu-icon"></view>
            <view>关于我们</view>
          </view>
        </view>
        <view class="list-cell list-cell-arrow" @click="handleToSetting">
          <view class="menu-item-box">
            <view class="iconfont icon-setting menu-icon"></view>
            <view>应用设置</view>
          </view>
        </view>
      </view>

    </view>
  </view>
</template>

<script>
import uniIcons from '@/componetsPackage/uni-icons/uni-icons.vue'

export default {
  components: {
    uniIcons
  },
  data() {
    return {
      name: this.$store.state.user.name
    }
  },
  computed: {
    avatar() {
      return this.$store.state.user.avatar
    },
    windowHeight() {
      return uni.getSystemInfoSync().windowHeight - 50
    }
  },
  methods: {
    handleToInfo() {
      this.$tab.navigateTo('/packageB/mine/info/index')
    },
    handleToEditInfo() {
      this.$tab.navigateTo('/packageB/mine/info/edit')
    },
    handleToSetting() {
      this.$tab.navigateTo('/packageB/mine/setting/index')
    },
    handleToLogin() {
      this.$tab.reLaunch('/pages/login')
    },
    handleToAvatar() {
      this.$tab.navigateTo('/packageB/mine/avatar/index')
    },
    handleAbout() {
      this.$tab.navigateTo('/packageB/mine/about/index')
    },
    handleJiaoLiuQun() {
      this.$modal.showToast('QQ群：①133713780(满)、②146013835(满)、③189091635')
    },
    handleBuilding() {
      this.$modal.showToast('模块建设中~')
    }
  }
}
</script>

<style lang="scss" scoped>
page {
  background-color: #f5f6f7;
}

.mine-container {
  width: 100%;
  height: 100%;


  .header-section {
    padding: 15px 15px 45px 15px;
    background-color: #3c96f3;
    color: white;

    .login-tip {
      font-size: 18px;
      margin-left: 10px;
    }

    .cu-avatar {
      border: 2px solid #eaeaea;

      .icon {
        font-size: 40px;
      }
    }

    .user-info {
      margin-left: 15px;

      .u_title {
        font-size: 18px;
        line-height: 30px;
      }
    }
  }

  .content-section {
    position: relative;
    top: -50px;

    .mine-actions {
      margin: 15px 15px;
      padding: 20px 0px;
      border-radius: 8px;
      background-color: white;

      .action-item {
        .icon {
          font-size: 28px;
        }

        .text {
          display: block;
          font-size: 13px;
          margin: 8px 0px;
        }
      }
    }
  }
}
</style>
