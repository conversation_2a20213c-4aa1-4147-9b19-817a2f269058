package com.rich.system.mapper;


import com.rich.common.core.domain.entity.BasAccount;
import com.rich.common.core.domain.entity.RsStaff;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @Entity
 */
@Mapper
public interface RsStaffMapper {

    List<RsStaff> selectUserList(RsStaff user);

    /**
     * 通过用户名查询用户
     *
     * @param staffUsername 用户名
     * @return 用户对象信息
     */
    RsStaff selectUserByUserName(String staffUsername);

    /**
     * 通过用户ID查询用户
     *
     * @param staffId 用户ID
     * @return 用户对象信息
     */
    RsStaff selectUserById(Long staffId);

    /**
     * 通过微信OpenID查询用户
     *
     * @param openid 微信OpenID
     * @return 用户对象信息
     */
    RsStaff selectUserByOpenid(String openid);

    /**
     * 新增用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    int insertUser(RsStaff user);

    /**
     * 修改用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    int updateUser(RsStaff user);

    /**
     * 修改用户头像
     *
     * @param staffUsername 用户名
     * @param staffAvatar   头像地址
     * @return 结果
     */
    int updateUserAvatar(@Param("staffUsername") String staffUsername, @Param("staffAvatar") String staffAvatar);

    /**
     * 重置用户密码
     *
     * @param staffUsername 用户名
     * @param staffPassword 密码
     * @return 结果
     */
    int resetUserPwd(@Param("staffUsername") String staffUsername, @Param("staffPassword") String staffPassword);

    /**
     * 通过用户ID删除用户
     *
     * @param staffId 用户ID
     * @return 结果
     */
    int deleteUserById(Long staffId);

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    int deleteUserByIds(Long[] userIds);

    /**
     * 校验用户名称是否唯一
     *
     * @param staffUserName 用户名称
     * @return 结果
     */
    RsStaff checkUserNameUnique(String staffUserName);

    /**
     * 校验手机号码是否唯一
     *
     * @param staffPhoneNum 手机号码
     * @return 结果
     */
    RsStaff checkPhoneUnique(String staffPhoneNum);

    /**
     * 校验email是否唯一
     *
     * @param staffEmailEnterprise 用户邮箱
     * @return 结果
     */
    RsStaff checkEmailUnique(String staffEmailEnterprise);

    List<BasAccount> selectUserAccounts(Long staffId);

    List<RsStaff> queryByIds(Long[] staffIds);

    List<RsStaff> selectUserBasicList(RsStaff user);

    boolean selectUnidByUserName(String userName, String unid);

    /**
     * 用于缓存公司所有员工
     *
     * @return
     */
    List<RsStaff> selectAllRsStaff();

    List<Long> selectPermissionLevelA();

    List<Long> selectPermissionLevelB(Long userId);

    List<Long> selectPermissionLevelC(Long userId);

    /**
     * 绑定微信用户信息
     *
     * @param staffId    用户ID
     * @param openid     微信OpenID
     * @param unionid    微信UnionID
     * @param wxNickName 微信昵称
     * @return 结果
     */
    int bindWechatUser(@Param("staffId") Long staffId, @Param("openid") String openid,
                       @Param("unionid") String unionid, @Param("wxNickName") String wxNickName);

    /**
     * 更新用户的微信信息
     *
     * @param user 用户信息
     * @return 结果
     */
    int updateUserWechatInfo(RsStaff user);
}




