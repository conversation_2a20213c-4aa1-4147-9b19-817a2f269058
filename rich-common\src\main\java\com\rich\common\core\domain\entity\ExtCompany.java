package com.rich.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 【请填写功能名称】对象 ext_company
 *
 * <AUTHOR>
 * @date 2022-08-18
 */
@Data
public class ExtCompany extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 公司角色分类：瑞旗内部/客户/物流供应商/办公供货商，多选
     * 业务员：客户
     * 行政：办公供货商
     * 商务：物流供应商
     * 操作：瑞旗内部
     */
    private Long companyId;

    /**
     * 国际通用简称
     */
    @Excel(name = "国际通用简称")
    private String companyIntlCode;

    /**
     * 公司简称
     */
    @Excel(name = "公司简称")
    private String companyShortName;

    /**
     * 公司简称
     */
    @Excel(name = "英文简称")
    private String companyEnShortName;

    /**
     * 公司母语名
     */
    @Excel(name = "公司全称")
    private String companyLocalName;

    /**
     * 公司英文名
     */
    @Excel(name = "公司英文名")
    private String companyEnName;

    /**
     * 单位代码/统一编码
     */
    @Excel(name = "单位代码/统一编码")
    private String companyTaxCode;

    /**
     * 公司地址
     */
    private Long locationId;

    @Excel(name = "公司地址")
    private String locationName;

    @Excel(name = "公司详细地址")
    private String locationDetail;

    private ExtStaff staff;

    private String accountNum;

    private String staffNum;

    private String agreementNum;

    private RsAgreementRecord agreementRecord;

    private String communicationNum;

    private RsCommunication communication;

    /**
     * 角色类型
     */
    private Long roleTypeId;

    private Long belongTo;

    private String belongLocalName;

    private String belongEnName;

    private Long followUp;

    private String followLocalName;
    private String followEnName;

    private String showPriority;

    private String idleStatus;

    private String isBlacklist;

    private String blacklistContent;

    private Long blacklistStaffId;

    private String blacklistStaffLocalName;

    private String blacklistStaffEnName;

    @Excel(name = "物流角色")
    private String role;

    private Long[] roleIds;

    private BasCompanyRoleType roleType;

    @Excel(name = "货物特征")
    private String cargoType;

    private Long[] cargoTypeIds;

    @Excel(name = "服务类型")
    private String serviceType;

    @Excel(name = "银行账号")
    private String account;

    @Excel(name = "启运航线")
    private String lineDeparture;

    @Excel(name = "启运港口")
    private String locationDeparture;

    @Excel(name = "目的航线")
    private String lineDestination;

    @Excel(name = "目的港口")
    private String locationDestination;

    @Excel(name = "承运人")
    private String carrier;

    @Excel(name = "所属组织")
    private String organization;

    private Long[] serviceTypeIds;

    private Long[] lineDepartureIds;

    private Long[] locationDepartureIds;

    private Long[] lineDestinationIds;

    private Long[] locationDestinationIds;

    private Long[] companyIds;

    private Long[] carrierIds;

    private Long[] organizationIds;

    private String sourceId;

    private String score;

    private Long queryBFStaffId;

    private Long queryBStaffId;

    private Long sqdDeptId;

    private String companyQuery;

    @Excel(name = "联系人")
    private String staffName;
    @Excel(name = "联系人号码")
    private String staffPhone;
    @Excel(name = "联系人邮箱")
    private String staffEmail;
    @Excel(name = "默认币种")
    private String currencyName;

    /**
     * 经理确认
     */
    @Excel(name = "部门确认")
    private String salesConfirmed;
    private String salesConfirmedName;

    private Long salesConfirmedId;

    /**
     * 经理确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "业务确认时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date salesConfirmedDate;

    /**
     * 财务确认
     */
    @Excel(name = "财务确认")
    private String accConfirmed;
    private String accConfirmedName;

    private Long accConfirmedId;

    /**
     * 财务确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "财务确认时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date accConfirmedDate;

    /**
     * 财务确认
     */
    @Excel(name = "商务确认")
    private String psaConfirmed;
    private String psaConfirmedName;

    private Long psaConfirmedId;

    /**
     * 财务确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "商务确认时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date psaConfirmedDate;

    /**
     * 财务确认
     */
    @Excel(name = "商务确认")
    private String opConfirmed;
    private String opConfirmedName;

    private Long opConfirmedId;

    /**
     * 财务确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "操作确认时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date opConfirmedDate;

    // 当前用户id(用于搜索客户)
    private Long userId;

    /**
     * level :权限等级(用于查询自己得公司)
     *
     * @return
     */
    private String level;

    /**
     * 信用等级
     */
    @Excel(name = "信用等级")
    private String creditLevel;

    /**
     * 合同协议号
     */
    @Excel(name = "合同协议号")
    private String agreementNumber;

    /**
     * 协议类型
     */
    @Excel(name = "协议类型")
    private String releaseType;

    /**
     * 协议有效期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "协议有效期开始", width = 30, dateFormat = "yyyy-MM-dd")
    private Date agreementStartDate;

    /**
     * 协议有效期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "协议有效期结束", width = 30, dateFormat = "yyyy-MM-dd")
    private Date agreementEndDate;

    /**
     * 协议是否有效
     */
    @Excel(name = "协议是否有效")
    private String agreementStatus;

    /**
     * 信用额度
     */
    @Excel(name = "信用额度")
    private BigDecimal creditLimit;

    /**
     * 信用额度币种
     */
    @Excel(name = "信用额度币种")
    private String agreementCurrencyCode;

    /**
     * 结款日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结款日", width = 30, dateFormat = "yyyy-MM-dd")
    private String settlementDate;

    /**
     * 信用周期(自然月)
     */
    @Excel(name = "信用周期(自然月)")
    private Long creditCycleMonth;

    /**
     * 综合评分(根据员工沟通记录打分计算得出,0-100 两位小数)
     */
    @Excel(name = "综合评分(根据员工沟通记录打分计算得出,0-100 两位小数)")
    private Long sqdOverallScore;

    /**
     * 主联系人(展示简称)
     */
    @Excel(name = "主联系人(展示简称)")
    private String sqdMainAttn;

    private String agreementRemark;

    /**
     * 用于接受用户的权限范围
     */
    private Long[] permissionLevel;

    /**
     * 收付路径
     *
     * @return
     */
    private String rsPaymentTitle;

    /**
     * 瑞旗分支
     */
    @Excel(name = "瑞旗分支")
    private String roleRich;

    /**
     * 客户
     */
    @Excel(name = "客户")
    private String roleClient;

    /**
     * 供应商
     */
    @Excel(name = "供应商")
    private String roleSupplier;

    /**
     * 运营支持
     */
    @Excel(name = "运营支持")
    private String roleSupport;
    private String belongsCompany;

    private List<RsRct> rctList;
    private String mainStaffOfficialName;
    private String staffMobile;
    private String staffWhatsapp;
    private String staffWechat;
    private String partnerHabit;
    private String staffOtherContact;
    private String staffQq;


    /**
     * 收款基准
     */
    @Excel(name = "收款基准")
    private String receiveStandard;

    /**
     * 付款基准
     */
    @Excel(name = "付款基准")
    private String payStandard;

    /**
     * 收款方式
     */
    @Excel(name = "收款方式")
    private String payWay;

    /**
     * 付款方式
     */
    @Excel(name = "付款方式")
    private String receiveWay;

    /**
     * 收款币种
     */
    @Excel(name = "收款币种")
    private String receiveCurrencyCode;

    /**
     * 付款币种
     */
    @Excel(name = "付款币种")
    private String payCurrencyCode;

    /**
     * 收款信用额度
     */
    @Excel(name = "收款信用额度")
    private BigDecimal receiveCreditLimit;

    /**
     * 付款信用额度
     */
    @Excel(name = "付款信用额度")
    private BigDecimal payCreditLimit;

    /**
     * 收款期限
     */
    @Excel(name = "收款期限")
    private String receiveTerm;

    /**
     * 付款期限
     */
    @Excel(name = "付款期限")
    private String payTerm;

    /**
     * 业务标记
     */
    @Excel(name = "业务标记")
    private String salesRemark;
    @Excel(name = "公司所属")
    private String companyBelongTo;
    private String developmentWeight;
    private String sqdPaymentTimeSummary;
    private String sqdPayTermsSummary;
    private String sqdReceiveTermsSummary;
    private String isRecievingOrPaying;
    private String isAccountConfirmed;
    private String taxCode;
    private String taxNo;

    public String getTaxNo() {
        return taxNo;
    }

    public void setTaxNo(String taxNo) {
        this.taxNo = taxNo;
    }

    public String getTaxCode() {
        return taxCode;
    }

    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode;
    }

    public String getIsAccountConfirmed() {
        return isAccountConfirmed;
    }

    public void setIsAccountConfirmed(String isAccountConfirmed) {
        this.isAccountConfirmed = isAccountConfirmed;
    }

    public String getIsRecievingOrPaying() {
        return isRecievingOrPaying;
    }

    public void setIsRecievingOrPaying(String isRecievingOrPaying) {
        this.isRecievingOrPaying = isRecievingOrPaying;
    }

    public String getSqdReceiveTermsSummary() {
        return sqdReceiveTermsSummary;
    }

    public void setSqdReceiveTermsSummary(String sqdReceiveTermsSummary) {
        this.sqdReceiveTermsSummary = sqdReceiveTermsSummary;
    }

    public String getSqdPayTermsSummary() {
        return sqdPayTermsSummary;
    }

    public void setSqdPayTermsSummary(String sqdPayTermsSummary) {
        this.sqdPayTermsSummary = sqdPayTermsSummary;
    }

    public String getSqdPaymentTimeSummary() {
        return sqdPaymentTimeSummary;
    }

    public void setSqdPaymentTimeSummary(String sqdPaymentTimeSummary) {
        this.sqdPaymentTimeSummary = sqdPaymentTimeSummary;
    }

    public String getDevelopmentWeight() {
        return developmentWeight;
    }

    public void setDevelopmentWeight(String developmentWeight) {
        this.developmentWeight = developmentWeight;
    }

    public String getCompanyBelongTo() {
        return companyBelongTo;
    }

    public void setCompanyBelongTo(String companyBelongTo) {
        this.companyBelongTo = companyBelongTo;
    }

    public List<RsRct> getRctList() {
        return rctList;
    }

    public void setRctList(List<RsRct> rctList) {
        this.rctList = rctList;
    }

    public String getReceiveStandard() {
        return receiveStandard;
    }

    public void setReceiveStandard(String receiveStandard) {
        this.receiveStandard = receiveStandard;
    }

    public String getPayStandard() {
        return payStandard;
    }

    public void setPayStandard(String payStandard) {
        this.payStandard = payStandard;
    }

    public String getPayWay() {
        return payWay;
    }

    public void setPayWay(String payWay) {
        this.payWay = payWay;
    }

    public String getReceiveWay() {
        return receiveWay;
    }

    public void setReceiveWay(String receiveWay) {
        this.receiveWay = receiveWay;
    }

    public String getReceiveCurrencyCode() {
        return receiveCurrencyCode;
    }

    public void setReceiveCurrencyCode(String receiveCurrencyCode) {
        this.receiveCurrencyCode = receiveCurrencyCode;
    }

    public String getPayCurrencyCode() {
        return payCurrencyCode;
    }

    public void setPayCurrencyCode(String payCurrencyCode) {
        this.payCurrencyCode = payCurrencyCode;
    }

    public BigDecimal getReceiveCreditLimit() {
        return receiveCreditLimit;
    }

    public void setReceiveCreditLimit(BigDecimal receiveCreditLimit) {
        this.receiveCreditLimit = receiveCreditLimit;
    }

    public BigDecimal getPayCreditLimit() {
        return payCreditLimit;
    }

    public void setPayCreditLimit(BigDecimal payCreditLimit) {
        this.payCreditLimit = payCreditLimit;
    }

    public String getReceiveTerm() {
        return receiveTerm;
    }

    public void setReceiveTerm(String receiveTerm) {
        this.receiveTerm = receiveTerm;
    }

    public String getPayTerm() {
        return payTerm;
    }

    public void setPayTerm(String payTerm) {
        this.payTerm = payTerm;
    }

    public String getSalesRemark() {
        return salesRemark;
    }

    public void setSalesRemark(String salesRemark) {
        this.salesRemark = salesRemark;
    }

    public String getStaffQq() {
        return staffQq;
    }

    public void setStaffQq(String staffQq) {
        this.staffQq = staffQq;
    }

    public String getStaffOtherContact() {
        return staffOtherContact;
    }

    public void setStaffOtherContact(String staffOtherContact) {
        this.staffOtherContact = staffOtherContact;
    }

    public String getPartnerHabit() {
        return partnerHabit;
    }

    public void setPartnerHabit(String partnerHabit) {
        this.partnerHabit = partnerHabit;
    }

    public String getStaffWechat() {
        return staffWechat;
    }

    public void setStaffWechat(String staffWechat) {
        this.staffWechat = staffWechat;
    }

    public String getStaffWhatsapp() {
        return staffWhatsapp;
    }

    public void setStaffWhatsapp(String staffWhatsapp) {
        this.staffWhatsapp = staffWhatsapp;
    }

    public String getStaffMobile() {
        return staffMobile;
    }

    public void setStaffMobile(String staffMobile) {
        this.staffMobile = staffMobile;
    }

    public String getMainStaffOfficialName() {
        return mainStaffOfficialName;
    }

    public void setMainStaffOfficialName(String mainStaffOfficialName) {
        this.mainStaffOfficialName = mainStaffOfficialName;
    }

    public List<RsRct> getRctNoList() {
        return rctList;
    }

    public void setRctNoList(List<RsRct> rctNoList) {
        this.rctList = rctNoList;
    }

    public String getBelongsCompany() {
        return belongsCompany;
    }

    public void setBelongsCompany(String belongsCompany) {
        this.belongsCompany = belongsCompany;
    }

    public String getRoleRich() {
        return roleRich;
    }

    public void setRoleRich(String roleRich) {
        this.roleRich = roleRich;
    }

    public String getRoleClient() {
        return roleClient;
    }

    public void setRoleClient(String roleClient) {
        this.roleClient = roleClient;
    }

    public String getRoleSupplier() {
        return roleSupplier;
    }

    public void setRoleSupplier(String roleSupplier) {
        this.roleSupplier = roleSupplier;
    }

    public String getRoleSupport() {
        return roleSupport;
    }

    public void setRoleSupport(String roleSupport) {
        this.roleSupport = roleSupport;
    }

    public String getReleaseType() {
        return releaseType;
    }

    public void setReleaseType(String releaseType) {
        this.releaseType = releaseType;
    }

    public String getRsPaymentTitle() {
        return rsPaymentTitle;
    }

    public void setRsPaymentTitle(String rsPaymentTitle) {
        this.rsPaymentTitle = rsPaymentTitle;
    }

    public Long[] getPermissionLevel() {
        return permissionLevel;
    }

    public void setPermissionLevel(Long[] permissionLevel) {
        this.permissionLevel = permissionLevel;
    }

    public String getAgreementRemark() {
        return agreementRemark;
    }

    public void setAgreementRemark(String agreementRemark) {
        this.agreementRemark = agreementRemark;
    }

    public String getSqdMainAttn() {
        return sqdMainAttn;
    }

    public void setSqdMainAttn(String sqdMainAttn) {
        this.sqdMainAttn = sqdMainAttn;
    }

    public String getSalesConfirmed() {
        return salesConfirmed;
    }

    public void setSalesConfirmed(String salesConfirmed) {
        this.salesConfirmed = salesConfirmed;
    }

    public String getSalesConfirmedName() {
        return salesConfirmedName;
    }

    public void setSalesConfirmedName(String salesConfirmedName) {
        this.salesConfirmedName = salesConfirmedName;
    }

    public Long getSalesConfirmedId() {
        return salesConfirmedId;
    }

    public void setSalesConfirmedId(Long salesConfirmedId) {
        this.salesConfirmedId = salesConfirmedId;
    }

    public Date getSalesConfirmedDate() {
        return salesConfirmedDate;
    }

    public void setSalesConfirmedDate(Date salesConfirmedDate) {
        this.salesConfirmedDate = salesConfirmedDate;
    }

    public String getAccConfirmed() {
        return accConfirmed;
    }

    public void setAccConfirmed(String accConfirmed) {
        this.accConfirmed = accConfirmed;
    }

    public String getAccConfirmedName() {
        return accConfirmedName;
    }

    public void setAccConfirmedName(String accConfirmedName) {
        this.accConfirmedName = accConfirmedName;
    }

    public Long getAccConfirmedId() {
        return accConfirmedId;
    }

    public void setAccConfirmedId(Long accConfirmedId) {
        this.accConfirmedId = accConfirmedId;
    }

    public Date getAccConfirmedDate() {
        return accConfirmedDate;
    }

    public void setAccConfirmedDate(Date accConfirmedDate) {
        this.accConfirmedDate = accConfirmedDate;
    }

    public String getCreditLevel() {
        return creditLevel;
    }

    public void setCreditLevel(String creditLevel) {
        this.creditLevel = creditLevel;
    }

    public String getAgreementNumber() {
        return agreementNumber;
    }

    public void setAgreementNumber(String agreementNumber) {
        this.agreementNumber = agreementNumber;
    }


    public Date getAgreementStartDate() {
        return agreementStartDate;
    }

    public void setAgreementStartDate(Date agreementStartDate) {
        this.agreementStartDate = agreementStartDate;
    }

    public Date getAgreementEndDate() {
        return agreementEndDate;
    }

    public void setAgreementEndDate(Date agreementEndDate) {
        this.agreementEndDate = agreementEndDate;
    }

    public String getAgreementStatus() {
        return agreementStatus;
    }

    public void setAgreementStatus(String agreementStatus) {
        this.agreementStatus = agreementStatus;
    }

    public BigDecimal getCreditLimit() {
        return creditLimit;
    }

    public void setCreditLimit(BigDecimal creditLimit) {
        this.creditLimit = creditLimit;
    }

    public String getAgreementCurrencyCode() {
        return agreementCurrencyCode;
    }

    public void setAgreementCurrencyCode(String agreementCurrencyCode) {
        this.agreementCurrencyCode = agreementCurrencyCode;
    }

    public String getSettlementDate() {
        return settlementDate;
    }

    public void setSettlementDate(String settlementDate) {
        this.settlementDate = settlementDate;
    }

    public Long getCreditCycleMonth() {
        return creditCycleMonth;
    }

    public void setCreditCycleMonth(Long creditCycleMonth) {
        this.creditCycleMonth = creditCycleMonth;
    }

    public Long getSqdOverallScore() {
        return sqdOverallScore;
    }

    public void setSqdOverallScore(Long sqdOverallScore) {
        this.sqdOverallScore = sqdOverallScore;
    }

    public String getLevel() {
        return level;
    }


    public void setLevel(String level) {
        this.level = level;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }


    public String getPsaConfirmedName() {
        return psaConfirmedName;
    }

    public void setPsaConfirmedName(String psaConfirmedName) {
        this.psaConfirmedName = psaConfirmedName;
    }

    public String getOpConfirmedName() {
        return opConfirmedName;
    }

    public void setOpConfirmedName(String opConfirmedName) {
        this.opConfirmedName = opConfirmedName;
    }

    public String getCompanyEnShortName() {
        return companyEnShortName;
    }

    public void setCompanyEnShortName(String companyEnShortName) {
        this.companyEnShortName = companyEnShortName;
    }

    public String getPsaConfirmed() {
        return psaConfirmed;
    }

    public void setPsaConfirmed(String psaConfirmed) {
        this.psaConfirmed = psaConfirmed;
    }

    public Long getPsaConfirmedId() {
        return psaConfirmedId;
    }

    public void setPsaConfirmedId(Long psaConfirmedId) {
        this.psaConfirmedId = psaConfirmedId;
    }

    public Date getPsaConfirmedDate() {
        return psaConfirmedDate;
    }

    public void setPsaConfirmedDate(Date psaConfirmedDate) {
        this.psaConfirmedDate = psaConfirmedDate;
    }

    public String getOpConfirmed() {
        return opConfirmed;
    }

    public void setOpConfirmed(String opConfirmed) {
        this.opConfirmed = opConfirmed;
    }

    public Long getOpConfirmedId() {
        return opConfirmedId;
    }

    public void setOpConfirmedId(Long opConfirmedId) {
        this.opConfirmedId = opConfirmedId;
    }

    public Date getOpConfirmedDate() {
        return opConfirmedDate;
    }

    public void setOpConfirmedDate(Date opConfirmedDate) {
        this.opConfirmedDate = opConfirmedDate;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public String getStaffPhone() {
        return staffPhone;
    }

    public void setStaffPhone(String staffPhone) {
        this.staffPhone = staffPhone;
    }

    public String getStaffEmail() {
        return staffEmail;
    }

    public void setStaffEmail(String staffEmail) {
        this.staffEmail = staffEmail;
    }

    public String getCurrencyName() {
        return currencyName;
    }

    public void setCurrencyName(String currencyName) {
        this.currencyName = currencyName;
    }

    public Long getRoleTypeId() {
        return roleTypeId;
    }

    public void setRoleTypeId(Long roleTypeId) {
        this.roleTypeId = roleTypeId;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public Long[] getRoleIds() {
        return roleIds;
    }

    public void setRoleIds(Long[] roleIds) {
        this.roleIds = roleIds;
    }

    public BasCompanyRoleType getRoleType() {
        return roleType;
    }

    public void setRoleType(BasCompanyRoleType roleType) {
        this.roleType = roleType;
    }

    public String getCargoType() {
        return cargoType;
    }

    public void setCargoType(String cargoType) {
        this.cargoType = cargoType;
    }

    public Long[] getCargoTypeIds() {
        return cargoTypeIds;
    }

    public void setCargoTypeIds(Long[] cargoTypeIds) {
        this.cargoTypeIds = cargoTypeIds;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public Long[] getServiceTypeIds() {
        return serviceTypeIds;
    }

    public void setServiceTypeIds(Long[] serviceTypeIds) {
        this.serviceTypeIds = serviceTypeIds;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getLineDeparture() {
        return lineDeparture;
    }

    public void setLineDeparture(String lineDeparture) {
        this.lineDeparture = lineDeparture;
    }

    public Long[] getLineDepartureIds() {
        return lineDepartureIds;
    }

    public void setLineDepartureIds(Long[] lineDepartureIds) {
        this.lineDepartureIds = lineDepartureIds;
    }

    public String getLocationDeparture() {
        return locationDeparture;
    }

    public void setLocationDeparture(String locationDeparture) {
        this.locationDeparture = locationDeparture;
    }

    public Long[] getLocationDepartureIds() {
        return locationDepartureIds;
    }

    public void setLocationDepartureIds(Long[] locationDepartureIds) {
        this.locationDepartureIds = locationDepartureIds;
    }

    public String getLineDestination() {
        return lineDestination;
    }

    public void setLineDestination(String lineDestination) {
        this.lineDestination = lineDestination;
    }

    public Long[] getLineDestinationIds() {
        return lineDestinationIds;
    }

    public void setLineDestinationIds(Long[] lineDestinationIds) {
        this.lineDestinationIds = lineDestinationIds;
    }

    public String getLocationDestination() {
        return locationDestination;
    }

    public void setLocationDestination(String locationDestination) {
        this.locationDestination = locationDestination;
    }

    public Long[] getLocationDestinationIds() {
        return locationDestinationIds;
    }

    public void setLocationDestinationIds(Long[] locationDestinationIds) {
        this.locationDestinationIds = locationDestinationIds;
    }

    public String getCarrier() {
        return carrier;
    }

    public void setCarrier(String carrier) {
        this.carrier = carrier;
    }

    public Long[] getCarrierIds() {
        return carrierIds;
    }

    public void setCarrierIds(Long[] carrierIds) {
        this.carrierIds = carrierIds;
    }

    public String getOrganization() {
        return organization;
    }

    public void setOrganization(String organization) {
        this.organization = organization;
    }

    public Long[] getOrganizationIds() {
        return organizationIds;
    }

    public void setOrganizationIds(Long[] organizationIds) {
        this.organizationIds = organizationIds;
    }

    public String getCompanyQuery() {
        return companyQuery;
    }

    public void setCompanyQuery(String companyQuery) {
        this.companyQuery = companyQuery;
    }

    public String getBlacklistStaffLocalName() {
        return blacklistStaffLocalName;
    }

    public void setBlacklistStaffLocalName(String blacklistStaffLocalName) {
        this.blacklistStaffLocalName = blacklistStaffLocalName;
    }

    public String getBlacklistStaffEnName() {
        return blacklistStaffEnName;
    }

    public void setBlacklistStaffEnName(String blacklistStaffEnName) {
        this.blacklistStaffEnName = blacklistStaffEnName;
    }

    public String getBlacklistContent() {
        return blacklistContent;
    }

    public void setBlacklistContent(String blacklistContent) {
        this.blacklistContent = blacklistContent;
    }

    public Long getBlacklistStaffId() {
        return blacklistStaffId;
    }

    public void setBlacklistStaffId(Long blacklistStaffId) {
        this.blacklistStaffId = blacklistStaffId;
    }

    public String getIsBlacklist() {
        return isBlacklist;
    }

    public void setIsBlacklist(String isBlacklist) {
        this.isBlacklist = isBlacklist;
    }

    public Long getSqdDeptId() {
        return sqdDeptId;
    }

    public void setSqdDeptId(Long sqdDeptId) {
        this.sqdDeptId = sqdDeptId;
    }

    public Long getQueryBFStaffId() {
        return queryBFStaffId;
    }

    public void setQueryBFStaffId(Long queryBFStaffId) {
        this.queryBFStaffId = queryBFStaffId;
    }

    public Long getQueryBStaffId() {
        return queryBStaffId;
    }

    public void setQueryBStaffId(Long queryBStaffId) {
        this.queryBStaffId = queryBStaffId;
    }

    public String getScore() {
        return score;
    }

    public void setScore(String score) {
        this.score = score;
    }

    public RsAgreementRecord getAgreementRecord() {
        return agreementRecord;
    }

    public void setAgreementRecord(RsAgreementRecord agreementRecord) {
        this.agreementRecord = agreementRecord;
    }

    public RsCommunication getCommunication() {
        return communication;
    }

    public void setCommunication(RsCommunication communication) {
        this.communication = communication;
    }


    public String getStaffNum() {
        return staffNum;
    }

    public void setStaffNum(String staffNum) {
        this.staffNum = staffNum;
    }

    public String getAgreementNum() {
        return agreementNum;
    }

    public void setAgreementNum(String agreementNum) {
        this.agreementNum = agreementNum;
    }

    public String getCommunicationNum() {
        return communicationNum;
    }

    public void setCommunicationNum(String communicationNum) {
        this.communicationNum = communicationNum;
    }

    public ExtStaff getStaff() {
        return staff;
    }

    public void setStaff(ExtStaff staff) {
        this.staff = staff;
    }

    public String getAccountNum() {
        return accountNum;
    }

    public void setAccountNum(String accountNum) {
        this.accountNum = accountNum;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getShowPriority() {
        return showPriority;
    }

    public void setShowPriority(String showPriority) {
        this.showPriority = showPriority;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Long[] getCompanyIds() {
        return companyIds;
    }

    public void setCompanyIds(Long[] companyIds) {
        this.companyIds = companyIds;
    }

    public Long getBelongTo() {
        return belongTo;
    }

    public void setBelongTo(Long belongTo) {
        this.belongTo = belongTo;
    }

    public Long getFollowUp() {
        return followUp;
    }

    public void setFollowUp(Long followUp) {
        this.followUp = followUp;
    }

    public String getBelongLocalName() {
        return belongLocalName;
    }

    public void setBelongLocalName(String belongLocalName) {
        this.belongLocalName = belongLocalName;
    }

    public String getBelongEnName() {
        return belongEnName;
    }

    public void setBelongEnName(String belongEnName) {
        this.belongEnName = belongEnName;
    }

    public String getFollowLocalName() {
        return followLocalName;
    }

    public void setFollowLocalName(String followLocalName) {
        this.followLocalName = followLocalName;
    }

    public String getFollowEnName() {
        return followEnName;
    }

    public void setFollowEnName(String followEnName) {
        this.followEnName = followEnName;
    }

    public String getLocationDetail() {
        return locationDetail;
    }

    public void setLocationDetail(String locationDetail) {
        this.locationDetail = locationDetail;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyIntlCode(String companyIntlCode) {
        this.companyIntlCode = companyIntlCode;
    }

    public String getCompanyIntlCode() {
        return companyIntlCode;
    }

    public void setCompanyShortName(String companyShortName) {
        this.companyShortName = companyShortName;
    }

    public String getCompanyShortName() {
        return companyShortName;
    }

    public void setCompanyLocalName(String companyLocalName) {
        this.companyLocalName = companyLocalName;
    }

    public String getCompanyLocalName() {
        return companyLocalName;
    }

    public void setCompanyEnName(String companyEnName) {
        this.companyEnName = companyEnName;
    }

    public String getCompanyEnName() {
        return companyEnName;
    }

    public void setCompanyTaxCode(String companyTaxCode) {
        this.companyTaxCode = companyTaxCode;
    }

    public String getCompanyTaxCode() {
        return companyTaxCode;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setIdleStatus(String idleStatus) {
        this.idleStatus = idleStatus;
    }

    public String getIdleStatus() {
        return idleStatus;
    }

}
