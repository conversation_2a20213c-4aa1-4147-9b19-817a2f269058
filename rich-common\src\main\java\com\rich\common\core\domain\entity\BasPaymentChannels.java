package com.rich.common.core.domain.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

/**
 * 收汇方式对象 bas_payment_channels
 * 
 * <AUTHOR>
 * @date 2023-06-16
 */
public class BasPaymentChannels extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 收汇方式 */
    private Long paymentChannelsId;

    /** 简称 */
    @Excel(name = "简称")
    private String paymentChannelsShortName;

    /** 中文名 */
    @Excel(name = "中文名")
    private String paymentChannelsLocalName;

    /** 英文名 */
    @Excel(name = "英文名")
    private String paymentChannelsEnName;

    /** 排序 */
    @Excel(name = "排序")
    private Integer orderNum;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 删除人 */
    private String deleteBy;

    /** 删除时间 */
    private Date deleteTime;

    /** 数据状态（-1:删除或不可用，0：正常） */
    private String deleteStatus;

    public void setPaymentChannelsId(Long paymentChannelsId) 
    {
        this.paymentChannelsId = paymentChannelsId;
    }

    public Long getPaymentChannelsId() 
    {
        return paymentChannelsId;
    }
    public void setPaymentChannelsShortName(String paymentChannelsShortName) 
    {
        this.paymentChannelsShortName = paymentChannelsShortName;
    }

    public String getPaymentChannelsShortName() 
    {
        return paymentChannelsShortName;
    }
    public void setPaymentChannelsLocalName(String paymentChannelsLocalName) 
    {
        this.paymentChannelsLocalName = paymentChannelsLocalName;
    }

    public String getPaymentChannelsLocalName() 
    {
        return paymentChannelsLocalName;
    }
    public void setPaymentChannelsEnName(String paymentChannelsEnName) 
    {
        this.paymentChannelsEnName = paymentChannelsEnName;
    }

    public String getPaymentChannelsEnName() 
    {
        return paymentChannelsEnName;
    }
    public void setOrderNum(Integer orderNum) 
    {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() 
    {
        return orderNum;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
}
