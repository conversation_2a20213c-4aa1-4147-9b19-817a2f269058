package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

import java.util.Date;
import java.util.List;

/**
 * 进度分类对象 bas_process_type
 *
 * <AUTHOR>
 * @date 2023-06-05
 */
public class BasProcessType extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 进度分类表
     */
    private Long processTypeId;

    /**
     * 简称
     */
    @Excel(name = "简称")
    private String processTypeShortName;

    /**
     * 全称
     */
    @Excel(name = "全称")
    private String processTypeLocalName;

    /**
     * 英文名
     */
    @Excel(name = "英文名")
    private String processTypeEnName;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Integer orderNum;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;

    /**
     * 进程名称信息
     */
    private List<BasProcess> basProcessList;

    public List<BasProcess> getBasProcessList() {
        return basProcessList;
    }

    public void setBasProcessList(List<BasProcess> basProcessList) {
        this.basProcessList = basProcessList;
    }

    public void setProcessTypeId(Long processTypeId) {
        this.processTypeId = processTypeId;
    }

    public Long getProcessTypeId() {
        return processTypeId;
    }

    public void setProcessTypeShortName(String processTypeShortName) {
        this.processTypeShortName = processTypeShortName;
    }

    public String getProcessTypeShortName() {
        return processTypeShortName;
    }

    public void setProcessTypeLocalName(String processTypeLocalName) {
        this.processTypeLocalName = processTypeLocalName;
    }

    public String getProcessTypeLocalName() {
        return processTypeLocalName;
    }

    public void setProcessTypeEnName(String processTypeEnName) {
        this.processTypeEnName = processTypeEnName;
    }

    public String getProcessTypeEnName() {
        return processTypeEnName;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
}
