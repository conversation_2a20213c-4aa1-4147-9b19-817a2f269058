<template>
  <view class="normal-login-container">
    <view class="logo-content align-center justify-center flex">
      <image :src="globalConfig.appInfo.logo" mode="widthFix" style="width: 100rpx;height: 100rpx;">
      </image>
      <text class="title">瑞旗仓储登录</text>
    </view>
    <view class="login-form-content">
      <view v-if="loginType === 'account'" class="input-item flex align-center">
        <view class="iconfont icon-user icon"></view>
        <input v-model="loginForm.username" class="input" maxlength="30" placeholder="请输入账号" type="text"/>
      </view>
      <view v-if="loginType === 'account'" class="input-item flex align-center">
        <view class="iconfont icon-password icon"></view>
        <input v-model="loginForm.password" class="input" maxlength="20" placeholder="请输入密码" type="password"/>
      </view>
      <view v-if="captchaEnabled && loginType === 'account'" class="input-item flex align-center"
            style="width: 60%;margin: 0px;">
        <view class="iconfont icon-code icon"></view>
        <input v-model="loginForm.code" class="input" maxlength="4" placeholder="请输入验证码" type="number"
               @confirm="handleLogin"/>
        <view class="login-code">
          <image :src="codeUrl" class="login-code-img" @click="getCode"></image>
        </view>
      </view>

      <view v-if="loginType === 'account'" class="action-btn">
        <button class="login-btn cu-btn block bg-blue lg round" @click="handleLogin">账号登录</button>
      </view>

      <view class="action-btn">
        <button class="wechat-login-btn cu-btn block lg round" @click="handleWechatLogin">
          <text class="cuIcon-weixin"></text>
          微信登录
        </button>
      </view>

      <view class="reg text-center">
        <text class="text-blue" @click="switchLoginType">{{
            loginType === 'account' ? '使用微信登录' : '使用账号密码登录'
          }}
        </text>
      </view>

      <view v-if="register && loginType === 'account'" class="reg text-center">
        <text class="text-grey1">没有账号？</text>
        <text class="text-blue" @click="handleUserRegister">立即注册</text>
      </view>

      <view class="protocol-checkbox flex align-center justify-center">
        <checkbox :checked="isAgreeProtocol" color="#2979ff"
                  style="transform: scale(0.7);" @tap="toggleAgreeProtocol"/>
        <text class="text-grey1">我已阅读并同意</text>
        <text class="text-blue" @click="handleUserAgrement">《用户协议》</text>
        <text class="text-grey1">和</text>
        <text class="text-blue" @click="handlePrivacy">《隐私协议》</text>
      </view>

      <view class="account-limit-tip">
        账号仅限特定人群登录并进行登录账号鉴权
      </view>
    </view>

  </view>
</template>

<script>
import {getCodeImg} from '@/api/login'
import {wechatLogin} from '@/api/login'

export default {
  data() {
    return {
      codeUrl: "",
      captchaEnabled: true,
      // 用户注册开关
      register: false,
      // 登录类型：account-账号密码，wechat-微信登录
      loginType: "wechat",
      globalConfig: getApp().globalData.config,
      loginForm: {
        username: "",
        password: "",
        code: "",
        uuid: ""
      },
      // 登录后跳转的页面
      redirectUrl: '',
      isAgreeProtocol: false
    }
  },
  onLoad(options) {
    // 获取跳转参数
    if (options && options.redirect) {
      this.redirectUrl = decodeURIComponent(options.redirect)
    }

    // 从本地存储中读取协议勾选状态
    try {
      const agreeProtocol = uni.getStorageSync('agreeProtocol')
      if (agreeProtocol) {
        this.isAgreeProtocol = agreeProtocol === 'true'
      }
    } catch (e) {
      console.log('获取本地存储的协议勾选状态失败')
    }
  },
  created() {
    this.getCode()
  },
  methods: {
    // 切换登录方式
    switchLoginType() {
      this.loginType = this.loginType === 'account' ? 'wechat' : 'account'
      if (this.loginType === 'account' && this.captchaEnabled) {
        this.getCode()
      }
    },
    // 微信授权登录
    handleWechatLogin() {
      // 检查是否勾选协议
      if (!this.isAgreeProtocol) {
        uni.showToast({
          title: '请阅读并同意用户协议和隐私政策',
          icon: 'none'
        })
        return
      }
      
      // #ifdef MP-WEIXIN
      this.$modal.loading("微信登录中...")
      uni.login({
        provider: 'weixin',
        success: (res) => {
          if (res.code) {
            // 发送 code 到后台换取 openId, sessionKey, unionId
            this.wechatLoginWithCode(res.code)
          } else {
            this.$modal.closeLoading()
            this.$modal.msgError('微信登录失败：' + res.errMsg)
          }
        },
        fail: (err) => {
          this.$modal.closeLoading()
          this.$modal.msgError('微信登录失败，请重试')
        }
      })
      // #endif

      // #ifdef H5
      this.$modal.msgError('H5环境不支持微信登录，请在微信小程序中使用')
      // #endif

      // #ifdef APP-PLUS
      this.$modal.loading("微信登录中...")
      uni.login({
        provider: 'weixin',
        success: (res) => {
          if (res.code) {
            // 发送 code 到后台换取 openId, sessionKey, unionId
            this.wechatLoginWithCode(res.code)
          } else {
            this.$modal.closeLoading()
            this.$modal.msgError('微信登录失败：' + res.errMsg)
          }
        },
        fail: (err) => {
          this.$modal.closeLoading()
          this.$modal.msgError('微信登录失败，请重试')
        }
      })
      // #endif
    },
    // 使用微信code登录
    wechatLoginWithCode(code) {
      this.$store.dispatch('WechatLogin', code).then(() => {
        this.$modal.closeLoading()
        this.loginSuccess()
      }).catch(err => {
        this.$modal.closeLoading()
        this.$modal.msgError(err.message || '微信登录失败，请重试')
      })
    },
    // 用户注册
    // handleUserRegister() {
    //   this.$tab.redirectTo(`/packageB/register/register`)
    // },
    // 隐私协议
    handlePrivacy() {
      // 优先使用配置文件中的协议URL
      // if (this.globalConfig.appInfo.agreements && this.globalConfig.appInfo.agreements.length > 0) {
      //   let site = this.globalConfig.appInfo.agreements[0]
      //   this.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)
      // } else {
      //   // 使用本地协议页面
      //   this.$tab.navigateTo('/pages/common/agreements/privacy-policy')
      // }
      this.$tab.navigateTo('/pages/common/agreements/privacy-policy')
    },
    // 用户协议
    handleUserAgrement() {
      // 优先使用配置文件中的协议URL
      // if (this.globalConfig.appInfo.agreements && this.globalConfig.appInfo.agreements.length > 1) {
      //   let site = this.globalConfig.appInfo.agreements[1]
      //   this.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)
      // } else {
      //   // 使用本地协议页面
      //   this.$tab.navigateTo('/pages/common/agreements/user-agreement')
      // }
      this.$tab.navigateTo('/pages/common/agreements/user-agreement')
    },
    // 获取图形验证码
    getCode() {
      getCodeImg().then(res => {
        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled
        if (this.captchaEnabled) {
          this.codeUrl = 'data:image/gif;base64,' + res.img
          this.loginForm.uuid = res.uuid
        }
      })
    },
    // 登录方法
    handleLogin() {
      // 检查是否勾选协议
      if (!this.isAgreeProtocol) {
        uni.showToast({
          title: '请阅读并同意用户协议和隐私政策',
          icon: 'none'
        })
        return
      }

      if (this.loginForm.username === "") {
        this.$modal.msgError("请输入账号")
      } else if (this.loginForm.password === "") {
        this.$modal.msgError("请输入密码")
      } else if (this.loginForm.code === "" && this.captchaEnabled) {
        this.$modal.msgError("请输入验证码")
      } else {
        this.$modal.loading("登录中，请耐心等待...")
        this.pwdLogin()
      }

    },
    // 密码登录
    async pwdLogin() {
      this.$store.dispatch('Login', this.loginForm).then(() => {
        this.$modal.closeLoading()
        this.loginSuccess()
      }).catch(() => {
        if (this.captchaEnabled) {
          this.getCode()
        }
      })
    },
    // 登录成功后，处理函数
    loginSuccess(result) {
      // 保存协议勾选状态到本地存储
      if (this.isAgreeProtocol) {
        try {
          uni.setStorageSync('agreeProtocol', 'true')
        } catch (e) {
          console.log('保存协议勾选状态失败')
        }
      }
      
      // 设置用户信息
      this.$store.dispatch('GetInfo').then(res => {
        // 如果有redirect参数，跳转到指定页面，否则跳转到首页
        if (this.redirectUrl) {
          this.$tab.reLaunch(this.redirectUrl)
        } else {
          this.$tab.reLaunch('/pages/index')
        }
      })
    },
    // 协议勾选状态变更
    toggleAgreeProtocol() {
      this.isAgreeProtocol = !this.isAgreeProtocol
      // 保存勾选状态到本地存储
      try {
        uni.setStorageSync('agreeProtocol', this.isAgreeProtocol.toString())
      } catch (e) {
        console.log('保存协议勾选状态失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
page {
  background-color: #ffffff;
}

.normal-login-container {
  width: 100%;

  .logo-content {
    width: 100%;
    font-size: 21px;
    text-align: center;
    padding-top: 15%;

    image {
      border-radius: 4px;
    }

    .title {
      margin-left: 10px;
    }
  }

  .login-form-content {
    text-align: center;
    margin: 20px auto;
    margin-top: 15%;
    width: 80%;

    .input-item {
      margin: 20px auto;
      background-color: #f5f6f7;
      height: 45px;
      border-radius: 20px;

      .icon {
        font-size: 38rpx;
        margin-left: 10px;
        color: #999;
      }

      .input {
        width: 100%;
        font-size: 14px;
        line-height: 20px;
        text-align: left;
        padding-left: 15px;
      }

    }

    .login-btn {
      margin-top: 40px;
      height: 45px;
    }

    .wechat-login-btn {
      margin-top: 40px;
      height: 45px;
      background-color: #07c160;
      color: #ffffff;
    }

    .reg {
      margin-top: 15px;
    }

    .protocol-checkbox {
      margin-top: 20px;
      font-size: 24rpx;

      .text-blue {
        margin: 0 4rpx;
      }
    }

    .account-limit-tip {
      color: #999;
      margin-top: 40px;
      font-size: 22rpx;
      text-align: center;
    }

    .login-code {
      height: 38px;
      float: right;

      .login-code-img {
        height: 38px;
        position: absolute;
        margin-left: 10px;
        width: 200rpx;
      }
    }
  }
}
</style>
