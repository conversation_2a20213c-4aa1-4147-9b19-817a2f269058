package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 客户来源对象 bas_company_source
 *
 * <AUTHOR>
 * @date 2022-09-30
 */
public class BasCompanySource extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 客户来源
     */
    private Long sourceId;

    /**
     * 简称
     */
    @Excel(name = "简称")
    private String sourceShortName;

    /**
     * 中文名
     */
    @Excel(name = "中文名")
    private String sourceLocalName;

    /**
     * 英文名
     */
    @Excel(name = "英文名")
    private String sourceEnName;

    /**
     * 展示优先级
     */
    @Excel(name = "展示优先级")
    private Integer orderNum;

    private String status;

    private String sourceQuery;

    public String getSourceQuery() {
        return sourceQuery;
    }

    public void setSourceQuery(String sourceQuery) {
        this.sourceQuery = sourceQuery;
    }

    public void setSourceId(Long sourceId) {
        this.sourceId = sourceId;
    }

    public Long getSourceId() {
        return sourceId;
    }

    public void setSourceShortName(String sourceShortName) {
        this.sourceShortName = sourceShortName;
    }

    public String getSourceShortName() {
        return sourceShortName;
    }

    public void setSourceLocalName(String sourceLocalName) {
        this.sourceLocalName = sourceLocalName;
    }

    public String getSourceLocalName() {
        return sourceLocalName;
    }

    public void setSourceEnName(String sourceEnName) {
        this.sourceEnName = sourceEnName;
    }

    public String getSourceEnName() {
        return sourceEnName;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("sourceId", getSourceId())
                .append("sourceShortName", getSourceShortName())
                .append("sourceLocalName", getSourceLocalName())
                .append("sourceEnName", getSourceEnName())
                .append("orderNum", getOrderNum())
                .append("remark", getRemark())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("deleteBy", getDeleteBy())
                .append("deleteTime", getDeleteTime())
                .append("deleteStatus", getDeleteStatus())
                .toString();
    }
}
