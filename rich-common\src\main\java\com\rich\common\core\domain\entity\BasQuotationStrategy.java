package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 【请填写功能名称】对象 bas_quotation_strategy
 *
 * <AUTHOR>
 * @date 2024-04-25
 */
public class BasQuotationStrategy extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private String strategyCode;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String strategyLocalName;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String strategyEnName;

    public String getStrategyCode() {
        return strategyCode;
    }

    public void setStrategyCode(String strategyCode) {
        this.strategyCode = strategyCode;
    }

    public String getStrategyLocalName() {
        return strategyLocalName;
    }

    public void setStrategyLocalName(String strategyLocalName) {
        this.strategyLocalName = strategyLocalName;
    }

    public String getStrategyEnName() {
        return strategyEnName;
    }

    public void setStrategyEnName(String strategyEnName) {
        this.strategyEnName = strategyEnName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("strategyCode", getStrategyCode())
                .append("strategyLocalName", getStrategyLocalName())
                .append("strategyEnName", getStrategyEnName())
                .toString();
    }
}
