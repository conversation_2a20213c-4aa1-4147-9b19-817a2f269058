package com.rich.system.mapper;

import com.rich.system.domain.MidLocationDestination;
import com.rich.system.domain.MidLocationLoading;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 装运区域Mapper接口
 *
 * <AUTHOR>
 * @date 2023-05-05
 */
@Mapper
public interface MidLocationLoadingMapper {
    /**
     * 查询装运区域
     *
     * @param belongId 装运区域主键
     * @return 装运区域
     */
    List<Long> selectMidLocationLoadingById(Long belongId, String belongTo);

    /**
     * 查询目的港区域列表
     *
     * @return 目的港区域集合
     */
    List<MidLocationLoading> selectMidLocationLoadingByLocationIds(@Param("locationIds") Long[] locationIds, String belongTo);

    /**
     * 查询装运区域列表
     *
     * @param midLocationLoading 装运区域
     * @return 装运区域集合
     */
    List<MidLocationLoading> selectMidLocationLoadingList(MidLocationLoading midLocationLoading);

    /**
     * 新增装运区域
     *
     * @param midLocationLoading 装运区域
     * @return 结果
     */
    int insertMidLocationLoading(MidLocationLoading midLocationLoading);

    /**
     * 修改装运区域
     *
     * @param midLocationLoading 装运区域
     * @return 结果
     */
    int updateMidLocationLoading(MidLocationLoading midLocationLoading);

    /**
     * 删除装运区域
     *
     * @param belongId 装运区域主键
     * @return 结果
     */
    int deleteMidLocationLoadingByBelongId(Long belongId, String belongTo);

    /**
     * 批量删除装运区域
     *
     * @param belongIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteMidLocationLoadingByBelongIds(Long[] belongIds, String belongTo);

    /**
     * 批量新增${subTable.functionName}
     *
     * @param belongList ${subTable.functionName}列表
     * @return 结果
     */
    int batchLD(List<MidLocationLoading> belongList);
}
