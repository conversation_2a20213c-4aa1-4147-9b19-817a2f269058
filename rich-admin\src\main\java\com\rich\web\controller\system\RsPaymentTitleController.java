package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.constant.CacheConstants;
import com.rich.common.core.domain.entity.BasProcess;
import com.rich.common.core.domain.entity.RsPaymentTitle;
import com.rich.common.core.redis.RedisCache;
import com.rich.system.service.impl.RedisCacheImpl;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsPaymentTitleService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 *
 * <AUTHOR>
 * @date 2023-11-29
 */
@RestController
@RequestMapping("/system/paymenttitle")
public class RsPaymentTitleController extends BaseController {
    @Autowired
    private RsPaymentTitleService rsPaymentTitleService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisCacheImpl RedisCache;

    /**
     * 查询【请填写功能名称】列表
     */
//    @PreAuthorize("@ss.hasPermi('system:paymenttitle:list')")
    @GetMapping("/list")
    public AjaxResult list(RsPaymentTitle rsPaymentTitle) {


        List<RsPaymentTitle> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "paymentTitle");
        if (list == null) {
            RedisCache.paymentTitle();
            list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "paymentTitle");
        }
        return AjaxResult.success(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:paymenttitle:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsPaymentTitle rsPaymentTitle) {
        List<RsPaymentTitle> list = rsPaymentTitleService.selectRsPaymentTitleList(rsPaymentTitle);
        ExcelUtil<RsPaymentTitle> util = new ExcelUtil<RsPaymentTitle>(RsPaymentTitle.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:paymenttitle:query')")
    @GetMapping(value = "/{code}")
    public AjaxResult getInfo(@PathVariable("code") String code) {
        return AjaxResult.success(rsPaymentTitleService.selectRsPaymentTitleByCode(code));
    }

    /**
     * 新增【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:paymenttitle:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsPaymentTitle rsPaymentTitle) {
        return toAjax(rsPaymentTitleService.insertRsPaymentTitle(rsPaymentTitle));
    }

    /**
     * 修改【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:paymenttitle:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsPaymentTitle rsPaymentTitle) {
        return toAjax(rsPaymentTitleService.updateRsPaymentTitle(rsPaymentTitle));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:paymenttitle:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsPaymentTitle rsPaymentTitle) {
        rsPaymentTitle.setUpdateBy(getUserId());
        return toAjax(rsPaymentTitleService.changeStatus(rsPaymentTitle));
    }

    /**
     * 删除【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:paymenttitle:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{codes}")
    public AjaxResult remove(@PathVariable String[] codes) {
        return toAjax(rsPaymentTitleService.deleteRsPaymentTitleByCodes(codes));
    }
}
