package com.rich.system.service.impl;

import com.rich.common.constant.CacheConstants;
import com.rich.common.core.domain.entity.BasDoc;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.StringUtils;
import com.rich.system.domain.*;
import com.rich.system.mapper.*;
import com.rich.system.service.BasDocService;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 文件名称Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@Service
public class BasDocServiceImpl implements BasDocService {
    @Autowired
    private BasDocMapper basDocMapper;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisCacheImpl RedisCache;

    @Autowired
    private MidDocFlowDirectionMapper midDocFlowDirectionMapper;

    @Autowired
    private MidDocIssueTypeMapper midDocIssueTypeMapper;

    @Autowired
    private MidCarrierMapper midCarrierMapper;

    @Autowired
    private MidCargoTypeMapper midCargoTypeMapper;

    @Autowired
    private MidServiceTypeMapper midServiceTypeMapper;

    @Autowired
    private MidLocationDepartureMapper midLocationDepartureMapper;

    @Autowired
    private MidLineDepartureMapper midLineDepartureMapper;

    @Autowired
    private MidLocationDestinationMapper midLocationDestinationMapper;

    @Autowired
    private MidLineDestinationMapper midLineDestinationMapper;

    /**
     * 查询文件名称
     *
     * @param docId 文件名称主键
     * @return 文件名称
     */
    @Override
    public BasDoc selectBasDocByDocId(Long docId) {
        return basDocMapper.selectBasDocByDocId(docId);
    }

    /**
     * 查询文件名称列表
     *
     * @param basDoc 文件名称
     * @return 文件名称
     */
    @Override
    public List<BasDoc> selectBasDocList(BasDoc basDoc) {
        return basDocMapper.selectBasDocList(basDoc);
    }

    /**
     * 新增文件名称
     *
     * @param basDoc 文件名称
     * @return 结果
     */
    @Override
    public int insertBasDoc(BasDoc basDoc) {
        basDoc.setCreateTime(DateUtils.getNowDate());
        basDoc.setCreateBy(SecurityUtils.getUserId());
        int out = basDocMapper.insertBasDoc(basDoc);
        insertCargoType(basDoc);
        insertServiceType(basDoc);
        insertLineDeparture(basDoc);
        insertLocationDeparture(basDoc);
        insertLineDestination(basDoc);
        insertLocationDestination(basDoc);
        insertCarriers(basDoc);
        insertDocFlowDirection(basDoc);
        insertDocIssueType(basDoc);
        return out;
    }

    /**
     * 修改文件名称
     *
     * @param basDoc 文件名称
     * @return 结果
     */
    @Override
    public int updateBasDoc(BasDoc basDoc) {
        midCargoTypeMapper.deleteMidCargoTypeById(basDoc.getDocId(), "doc");
        insertCargoType(basDoc);
        midServiceTypeMapper.deleteMidServiceTypeById(basDoc.getDocId(), "doc");
        insertServiceType(basDoc);
        midLineDepartureMapper.deleteMidLineDepartureById(basDoc.getDocId(), "doc");
        insertLineDeparture(basDoc);
        midLocationDepartureMapper.deleteMidLocationDepartureById(basDoc.getDocId(), "doc");
        insertLocationDeparture(basDoc);
        midLineDestinationMapper.deleteMidLineDestinationById(basDoc.getDocId(), "doc");
        insertLineDestination(basDoc);
        midLocationDestinationMapper.deleteMidLocationDestinationById(basDoc.getDocId(), "doc");
        insertLocationDestination(basDoc);
        midCarrierMapper.deleteMidCarrierById(basDoc.getDocId(), "doc");
        insertCarriers(basDoc);
        basDoc.setUpdateTime(DateUtils.getNowDate());
        basDoc.setUpdateBy(SecurityUtils.getUserId());
        return basDocMapper.updateBasDoc(basDoc);
    }

    /**
     * 修改文件名称状态
     *
     * @param basDoc 文件名称
     * @return 文件名称
     */
    @Override
    public int changeStatus(BasDoc basDoc) {
        return basDocMapper.updateBasDoc(basDoc);
    }

    @Override
    public List<Long> selectLocationDeparture(Long docId) {
        List<MidLocationDeparture> locationDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "docLocationDeparture");
        if (locationDepartures == null) {
            RedisCache.locationDeparture("doc", "docLocationDeparture");
            locationDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "docLocationDeparture");
        }
        List<Long> r = new ArrayList<>();
        for (MidLocationDeparture locationDeparture : locationDepartures) {
            if (locationDeparture.getBelongId().equals(docId)) {
                r.add(locationDeparture.getLocationId());
            }
        }
        return r;
    }

    @Override
    public List<Long> selectLocationDestination(Long docId) {
        List<MidLocationDestination> locationDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "docLocationDestination");
        if (locationDestinations == null) {
            RedisCache.locationDestination("doc", "docLocationDestination");
            locationDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "docLocationDestination");
        }
        List<Long> r = new ArrayList<>();
        for (MidLocationDestination locationDestination : locationDestinations) {
            if (locationDestination.getBelongId().equals(docId)) {
                r.add(locationDestination.getLocationId());
            }
        }
        return r;
    }

    @Override
    public List<Long> selectServiceTypes(Long docId) {
        List<MidServiceType> serviceTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "docServiceType");
        if (serviceTypes == null) {
            RedisCache.midServiceType("doc", "docServiceType");
            serviceTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "docServiceType");
        }
        List<Long> r = new ArrayList<>();
        for (MidServiceType serviceType : serviceTypes) {
            if (serviceType.getBelongId().equals(docId)) {
                r.add(serviceType.getServiceTypeId());
            }
        }
        return r;
    }

    @Override
    public List<Long> selectCargoTypes(Long docId) {
        List<MidCargoType> cargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "docCargoType");
        if (cargoTypes == null) {
            RedisCache.midCargoType("doc", "docCargoType");
            cargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "docCargoType");
        }
        List<Long> r = new ArrayList<>();
        for (MidCargoType cargoType : cargoTypes) {
            if (cargoType.getBelongId().equals(docId)) {
                r.add(cargoType.getCargoTypeId());
            }
        }
        return r;
    }

    @Override
    public List<Long> selectLineDeparture(Long docId) {
        List<MidLineDeparture> lineDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "docLineDeparture");
        if (lineDepartures == null) {
            RedisCache.lineDeparture("doc", "docLineDeparture");
            lineDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "docLineDeparture");
        }
        List<Long> r = new ArrayList<>();
        for (MidLineDeparture lineDeparture : lineDepartures) {
            if (lineDeparture.getBelongId().equals(docId)) {
                r.add(lineDeparture.getLineId());
            }
        }
        return r;
    }

    @Override
    public List<Long> selectLineDestination(Long docId) {
        List<MidLineDestination> lineDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "docLineDestination");
        if (lineDestinations == null) {
            RedisCache.lineDestination("doc", "docLineDestination");
            lineDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "docLineDestination");
        }
        List<Long> r = new ArrayList<>();
        for (MidLineDestination lineDestination : lineDestinations) {
            if (lineDestination.getBelongId().equals(docId)) {
                r.add(lineDestination.getLineId());
            }
        }
        return r;
    }

    @Override
    public List<Long> selectCarriers(Long docId) {
        List<MidCarrier> carriers = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "docCarriers");
        if (carriers == null) {
            RedisCache.midCarrier("doc", "docCarriers");
            carriers = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "docCarriers");
        }
        List<Long> r = new ArrayList<>();
        for (MidCarrier carrier : carriers) {
            if (carrier.getBelongId().equals(docId)) {
                r.add(carrier.getCarrierId());
            }
        }
        return r;
    }

    @Override
    public List<Long> selectDocFlowDirection(Long docId) {
        List<MidDocFlowDirection> midDocFlowDirections = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "docFlowDirection");
        if (midDocFlowDirections == null) {
            redisCache.setCacheObject(CacheConstants.MID_CACHE_KEY + "docFlowDirection", midDocFlowDirectionMapper.selectMidDocFlowDirectionList(new MidDocFlowDirection()));
            midDocFlowDirections = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "docFlowDirection");
        }
        List<Long> r = new ArrayList<>();
        for (MidDocFlowDirection midDocFlowDirection : midDocFlowDirections) {
            if (midDocFlowDirection.getDocId().equals(docId)) {
                r.add(midDocFlowDirection.getDocFlowDirectionId());
            }
        }
        return r;
    }

    @Override
    public List<Long> selectDocIssueType(Long docId) {
        List<MidDocIssueType> midDocIssueTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "docIssueType");
        if (midDocIssueTypes == null) {
            redisCache.setCacheObject(CacheConstants.MID_CACHE_KEY + "docIssueType", midDocIssueTypeMapper.selectMidDocIssueTypeList(new MidDocIssueType()));
            midDocIssueTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "docIssueType");
        }
        List<Long> r = new ArrayList<>();
        for (MidDocIssueType midDocIssueType : midDocIssueTypes) {
            if (midDocIssueType.getDocId().equals(docId)) {
                r.add(midDocIssueType.getDocIssueTypeId());
            }
        }
        return r;
    }

    /**
     * 批量删除文件名称
     *
     * @param docIds 需要删除的文件名称主键
     * @return 结果
     */
    @Override
    public int deleteBasDocByDocIds(Long[] docIds) {
        return basDocMapper.deleteBasDocByDocIds(docIds);
    }

    /**
     * 删除文件名称信息
     *
     * @param docId 文件名称主键
     * @return 结果
     */
    @Override
    public int deleteBasDocByDocId(Long docId) {
        midCargoTypeMapper.deleteMidCargoTypeById(docId, "doc");
        RedisCache.midCargoType("doc", "docCargoType");

        midServiceTypeMapper.deleteMidServiceTypeById(docId, "doc");
        RedisCache.midServiceType("doc", "docServiceType");

        midLineDepartureMapper.deleteMidLineDepartureById(docId, "doc");
        RedisCache.lineDeparture("doc", "docLineDeparture");

        midLocationDepartureMapper.deleteMidLocationDepartureById(docId, "doc");
        RedisCache.locationDeparture("doc", "docLocationDeparture");

        midLineDestinationMapper.deleteMidLineDestinationById(docId, "doc");
        RedisCache.lineDestination("doc", "docLineDestination");

        midLocationDestinationMapper.deleteMidLocationDestinationById(docId, "doc");
        RedisCache.locationDestination("doc", "docLocationDestination");

        midCarrierMapper.deleteMidCarrierById(docId, "doc");
        RedisCache.midCarrier("doc", "docCarriers");
        return basDocMapper.deleteBasDocByDocId(docId);
    }

    public void insertCargoType(@NotNull BasDoc basDoc) {
        Long[] roles = basDoc.getCargoTypeIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidCargoType> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidCargoType MidCargoType = new MidCargoType();
                MidCargoType.setBelongId(basDoc.getDocId());
                MidCargoType.setBelongTo("doc");
                MidCargoType.setCargoTypeId(r);
                list.add(MidCargoType);
            }
            midCargoTypeMapper.batchCargoType(list);
        }
        RedisCache.midCargoType("doc", "docCargoType");
    }


    public void insertServiceType(@NotNull BasDoc basDoc) {
        Long[] roles = basDoc.getServiceTypeIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidServiceType> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidServiceType MidServiceType = new MidServiceType();
                MidServiceType.setBelongId(basDoc.getDocId());
                MidServiceType.setServiceTypeId(r);
                MidServiceType.setBelongTo("doc");
                list.add(MidServiceType);
            }
            midServiceTypeMapper.batchServiceType(list);
        }
        RedisCache.midServiceType("doc", "docServiceType");
    }

    private void insertLineDeparture(@NotNull BasDoc basDoc) {
        Long[] roles = basDoc.getLineDepartureIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidLineDeparture> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidLineDeparture MidLineDeparture = new MidLineDeparture();
                MidLineDeparture.setBelongId(basDoc.getDocId());
                MidLineDeparture.setLineId(r);
                MidLineDeparture.setBelongTo("doc");
                list.add(MidLineDeparture);
            }
            midLineDepartureMapper.batchLD(list);
        }
        RedisCache.lineDeparture("doc", "docLineDeparture");
    }


    private void insertLocationDeparture(@NotNull BasDoc basDoc) {
        Long[] roles = basDoc.getLocationDepartureIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidLocationDeparture> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidLocationDeparture MidLocationDeparture = new MidLocationDeparture();
                MidLocationDeparture.setBelongId(basDoc.getDocId());
                MidLocationDeparture.setLocationId(r);
                MidLocationDeparture.setBelongTo("doc");
                list.add(MidLocationDeparture);
            }
            midLocationDepartureMapper.batchLD(list);
        }
        RedisCache.locationDeparture("doc", "docLocationDeparture");
    }

    private void insertLocationDestination(@NotNull BasDoc basDoc) {
        Long[] roles = basDoc.getLocationDestinationIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidLocationDestination> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidLocationDestination MidLocationDestination = new MidLocationDestination();
                MidLocationDestination.setBelongId(basDoc.getDocId());
                MidLocationDestination.setLocationId(r);
                MidLocationDestination.setBelongTo("doc");
                list.add(MidLocationDestination);
            }
            midLocationDestinationMapper.batchLD(list);
        }
        RedisCache.locationDestination("doc", "docLocationDestination");
    }

    private void insertLineDestination(@NotNull BasDoc basDoc) {
        Long[] roles = basDoc.getLineDestinationIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidLineDestination> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidLineDestination MidLineDestination = new MidLineDestination();
                MidLineDestination.setBelongId(basDoc.getDocId());
                MidLineDestination.setLineId(r);
                MidLineDestination.setBelongTo("doc");
                list.add(MidLineDestination);
            }
            midLineDestinationMapper.batchLD(list);
        }
        RedisCache.lineDestination("doc", "docLineDestination");
    }

    private void insertCarriers(@NotNull BasDoc basDoc) {
        Long[] roles = basDoc.getCarrierIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidCarrier> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidCarrier midCarrier = new MidCarrier();
                midCarrier.setBelongId(basDoc.getDocId());
                midCarrier.setCarrierId(r);
                midCarrier.setBelongTo("doc");
                list.add(midCarrier);
            }
            midCarrierMapper.batchCarrier(list);
        }
        RedisCache.midCarrier("doc", "docCarriers");
    }

    private void insertDocIssueType(BasDoc basDoc) {
        Long[] roles = basDoc.getDocIssueTypeIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidDocIssueType> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidDocIssueType midDocIssueType = new MidDocIssueType();
                midDocIssueType.setDocId(basDoc.getDocId());
                midDocIssueType.setDocIssueTypeId(r);
                list.add(midDocIssueType);
            }
            midDocIssueTypeMapper.batchDocIssueType(list);
        }
        redisCache.setCacheObject(CacheConstants.MID_CACHE_KEY + "docIssueType", midDocIssueTypeMapper.selectMidDocIssueTypeList(new MidDocIssueType()));
    }

    private void insertDocFlowDirection(BasDoc basDoc) {
        Long[] roles = basDoc.getDocFlowDirectionIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidDocFlowDirection> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidDocFlowDirection midDocFlowDirection = new MidDocFlowDirection();
                midDocFlowDirection.setDocId(basDoc.getDocId());
                midDocFlowDirection.setDocFlowDirectionId(r);
                list.add(midDocFlowDirection);
            }
            midDocFlowDirectionMapper.batchDocFlowDirection(list);
        }
        redisCache.setCacheObject(CacheConstants.MID_CACHE_KEY + "docFlowDirection", midDocFlowDirectionMapper.selectMidDocFlowDirectionList(new MidDocFlowDirection()));
    }
}
