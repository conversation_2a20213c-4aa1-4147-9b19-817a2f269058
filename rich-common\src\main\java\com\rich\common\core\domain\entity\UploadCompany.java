package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

/**
 * <AUTHOR>
 * @Date 2022/11/3 10:18
 * @Version 1.0
 */
public class UploadCompany extends BaseEntity {
    @Excel(name = "公司简称")
    private String shortName;

    @Excel(name = "公司全称")
    private String localName;

    @Excel(name = "服务类型")
    private String serviceType;

    @Excel(name = "货物特征")
    private String cargoType;

    @Excel(name = "公司角色")
    private String role;

    @Excel(name = "联系人1")
    private String contactor1;
    @Excel(name = "联系人2")
    private String contactor2;
    @Excel(name = "联系人3")
    private String contactor3;

    @Excel(name = "联系人1简称")
    private String shortName1;
    @Excel(name = "联系人2简称")
    private String shortName2;
    @Excel(name = "联系人3简称")
    private String shortName3;

    @Excel(name = "联系人1全称")
    private String localName1;
    @Excel(name = "联系人2全称")
    private String localName2;
    @Excel(name = "联系人3全称")
    private String localName3;

    @Excel(name = "联系人1英称")
    private String enName1;
    @Excel(name = "联系人2英称")
    private String enName2;
    @Excel(name = "联系人3英称")
    private String enName3;

    @Excel(name = "电话1")
    private String phone1;
    @Excel(name = "电话2")
    private String phone2;
    @Excel(name = "电话3")
    private String phone3;

    @Excel(name = "微信1")
    private String wechat1;
    @Excel(name = "微信2")
    private String wechat2;
    @Excel(name = "微信3")
    private String wechat3;

    @Excel(name = "QQ1")
    private String qq1;
    @Excel(name = "QQ2")
    private String qq2;
    @Excel(name = "QQ3")
    private String qq3;

    @Excel(name = "邮箱1")
    private String email1;
    @Excel(name = "邮箱2")
    private String email2;
    @Excel(name = "邮箱3")
    private String email3;

    @Excel(name = "所在地")
    private String location;

    @Excel(name = "承运人")
    private String carrier;

    @Excel(name = "优势")
    private String advantage;

    @Excel(name = "备注")
    private String remark;

    @Excel(name = "航线")
    private String line;

    public String getCargoType() {
        return cargoType;
    }

    public void setCargoType(String cargoType) {
        this.cargoType = cargoType;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getShortName1() {
        return shortName1;
    }

    public void setShortName1(String shortName1) {
        this.shortName1 = shortName1;
    }

    public String getShortName2() {
        return shortName2;
    }

    public void setShortName2(String shortName2) {
        this.shortName2 = shortName2;
    }

    public String getShortName3() {
        return shortName3;
    }

    public void setShortName3(String shortName3) {
        this.shortName3 = shortName3;
    }

    public String getLocalName1() {
        return localName1;
    }

    public void setLocalName1(String localName1) {
        this.localName1 = localName1;
    }

    public String getLocalName2() {
        return localName2;
    }

    public void setLocalName2(String localName2) {
        this.localName2 = localName2;
    }

    public String getLocalName3() {
        return localName3;
    }

    public void setLocalName3(String localName3) {
        this.localName3 = localName3;
    }

    public String getEnName1() {
        return enName1;
    }

    public void setEnName1(String enName1) {
        this.enName1 = enName1;
    }

    public String getEnName2() {
        return enName2;
    }

    public void setEnName2(String enName2) {
        this.enName2 = enName2;
    }

    public String getEnName3() {
        return enName3;
    }

    public void setEnName3(String enName3) {
        this.enName3 = enName3;
    }

    public String getContactor1() {
        return contactor1;
    }

    public void setContactor1(String contactor1) {
        this.contactor1 = contactor1;
    }

    public String getContactor2() {
        return contactor2;
    }

    public void setContactor2(String contactor2) {
        this.contactor2 = contactor2;
    }

    public String getContactor3() {
        return contactor3;
    }

    public void setContactor3(String contactor3) {
        this.contactor3 = contactor3;
    }

    public String getPhone1() {
        return phone1;
    }

    public void setPhone1(String phone1) {
        this.phone1 = phone1;
    }

    public String getPhone2() {
        return phone2;
    }

    public void setPhone2(String phone2) {
        this.phone2 = phone2;
    }

    public String getPhone3() {
        return phone3;
    }

    public void setPhone3(String phone3) {
        this.phone3 = phone3;
    }

    public String getWechat1() {
        return wechat1;
    }

    public void setWechat1(String wechat1) {
        this.wechat1 = wechat1;
    }

    public String getWechat2() {
        return wechat2;
    }

    public void setWechat2(String wechat2) {
        this.wechat2 = wechat2;
    }

    public String getWechat3() {
        return wechat3;
    }

    public void setWechat3(String wechat3) {
        this.wechat3 = wechat3;
    }

    public String getQq1() {
        return qq1;
    }

    public void setQq1(String qq1) {
        this.qq1 = qq1;
    }

    public String getQq2() {
        return qq2;
    }

    public void setQq2(String qq2) {
        this.qq2 = qq2;
    }

    public String getQq3() {
        return qq3;
    }

    public void setQq3(String qq3) {
        this.qq3 = qq3;
    }

    public String getEmail1() {
        return email1;
    }

    public void setEmail1(String email1) {
        this.email1 = email1;
    }

    public String getEmail2() {
        return email2;
    }

    public void setEmail2(String email2) {
        this.email2 = email2;
    }

    public String getEmail3() {
        return email3;
    }

    public void setEmail3(String email3) {
        this.email3 = email3;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getLocalName() {
        return localName;
    }

    public void setLocalName(String localName) {
        this.localName = localName;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getCarrier() {
        return carrier;
    }

    public void setCarrier(String carrier) {
        this.carrier = carrier;
    }

    public String getAdvantage() {
        return advantage;
    }

    public void setAdvantage(String advantage) {
        this.advantage = advantage;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getLine() {
        return line;
    }

    public void setLine(String line) {
        this.line = line;
    }
}
