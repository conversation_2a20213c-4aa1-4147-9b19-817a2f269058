package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.RsCommunication;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.RsCommunicationService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 交流Controller
 *
 * <AUTHOR>
 * @date 2022-10-18
 */
@RestController
@RequestMapping("/system/communication")
public class RsCommunicationController extends BaseController {
    @Autowired
    private  RsCommunicationService rsCommunicationService;

    /**
     * 查询交流列表
     */
    @PreAuthorize("@ss.hasPermi('system:communication:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsCommunication rsCommunication) {
        startPage();
        List<RsCommunication> list = rsCommunicationService.selectRsCommunicationList(rsCommunication);
        return getDataTable(list);
    }

    /**
     * 导出交流列表
     */
    @PreAuthorize("@ss.hasPermi('system:communication:export')")
    @Log(title = "交流", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsCommunication rsCommunication) {
        List<RsCommunication> list = rsCommunicationService.selectRsCommunicationList(rsCommunication);
        ExcelUtil<RsCommunication> util = new ExcelUtil<RsCommunication>(RsCommunication.class);
        util.exportExcel(response, list, "交流数据");
    }

    /**
     * 获取交流详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:communication:edit')")
    @GetMapping(value = "/{communicationId}")
    public AjaxResult getInfo(@PathVariable("communicationId") Long communicationId) {
        return AjaxResult.success(rsCommunicationService.selectRsCommunicationByCommunicationId(communicationId));
    }

    /**
     * 新增交流
     */
    @PreAuthorize("@ss.hasPermi('system:communication:add')")
    @Log(title = "交流", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsCommunication rsCommunication) {
        return toAjax(rsCommunicationService.insertRsCommunication(rsCommunication));
    }

    /**
     * 修改交流
     */
    @PreAuthorize("@ss.hasPermi('system:communication:edit')")
    @Log(title = "交流", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsCommunication rsCommunication) {
        return toAjax(rsCommunicationService.updateRsCommunication(rsCommunication));
    }

    /**
     * 删除交流
     */
    @PreAuthorize("@ss.hasPermi('system:communication:remove')")
    @Log(title = "交流", businessType = BusinessType.DELETE)
    @DeleteMapping("/{communicationIds}")
    public AjaxResult remove(@PathVariable Long[] communicationIds) {
        return toAjax(rsCommunicationService.deleteRsCommunicationByCommunicationIds(communicationIds));
    }
}
