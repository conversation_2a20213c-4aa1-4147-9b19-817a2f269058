package com.rich.system.mapper;


import com.rich.common.core.domain.entity.SysRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Entity generator.domain.SysRole
 */
@Mapper
public interface SysRoleMapper {

    /**
     * 根据条件分页查询角色数据
     *
     * @param role 角色信息
     * @return 角色数据集合信息
     */
    List<SysRole> selectRoleList(SysRole role);

    /**
     * 根据用户ID查询角色
     *
     * @param staffId 用户ID
     * @return 角色列表
     */
    List<SysRole> selectRolePermissionByUserId(Long staffId);

    /**
     * 查询所有角色
     *
     * @return 角色列表
     */
    List<SysRole> selectRoleAll();

    /**
     * 通过角色ID查询角色
     *
     * @param roleId 角色ID
     * @return 角色对象信息
     */
    SysRole selectRoleById(Long roleId);

    /**
     * 根据用户ID查询角色
     *
     * @param staffUsername 用户名
     * @return 角色列表
     */
    List<SysRole> selectRolesByUserName(String staffUsername);

    /**
     * 校验角色名称是否唯一
     *
     * @param roleLocalName 角色名称
     * @return 角色信息
     */
    SysRole checkroleLocalNameUnique(String roleLocalName);

    /**
     * 校验角色权限是否唯一
     *
     * @param roleKey 角色权限
     * @return 角色信息
     */
    SysRole checkRoleKeyUnique(String roleKey);

    /**
     * 修改角色信息
     *
     * @param role 角色信息
     * @return 结果
     */
    int updateRole(SysRole role);

    /**
     * 新增角色信息
     *
     * @param role 角色信息
     * @return 结果
     */
    int insertRole(SysRole role);

    /**
     * 通过角色ID删除角色
     *
     * @param roleId 角色ID
     * @return 结果
     */
    int deleteRoleById(Long roleId);

    /**
     * 批量删除角色信息
     *
     * @param roleIds 需要删除的角色ID
     * @return 结果
     */
    int deleteRoleByIds(Long[] roleIds);

    int countPositionById(Long positionId);

    List<SysRole> selectChildrenRoleById(Long roleId);

    List<SysRole> selectChildrenRoleByIds(@Param("roles") Long[] roleIds);

    int updateRoleChildren(@Param("roles") List<SysRole> roles);

    void updateRoleStatusNormal(Long[] roleIds);

    List<SysRole> selectRoleListAll(SysRole role);

    Long[] selectRoleMenuById(Long roleId);

    /**
     * 查询部门最高权限所拥有的所有权限
     *
     * @param deptId
     * @return
     */
    Long[] selectDeptMaxRoleMenuIds(Long deptId);

    List<SysRole> selectDeptBasicRoles();
}




