package com.rich.system.mapper;

import com.rich.system.domain.MidRoleType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 角色类型Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-06
 */
@Mapper
public interface MidRoleTypeMapper {
    /**
     * 查询角色类型
     *
     * @return 角色类型
     */
    List<Long> selectMidRoleTypeById(Long belongId, String belongTo);

    /**
     * 查询角色类型列表
     *
     * @param midRoleTypeMenu 角色类型
     * @return 角色类型集合
     */
    List<MidRoleType> selectMidRoleTypeList(MidRoleType midRoleTypeMenu);

    /**
     * 新增角色类型
     *
     * @param midRoleTypeMenu 角色类型
     * @return 结果
     */
    int insertMidRoleType(MidRoleType midRoleTypeMenu);

    /**
     * 修改角色类型
     *
     * @param midRoleTypeMenu 角色类型
     * @return 结果
     */
    int updateMidRoleType(MidRoleType midRoleTypeMenu);

    /**
     * 删除角色类型
     *
     * @return 结果
     */
    int deleteMidRoleTypeByRoleTypeId(Long belongId, String belongTo);

    /**
     * 批量删除角色类型
     *
     * @return 结果
     */
    int deleteMidRoleTypeByRoleTypeIds(Long[] belongIds, String belongTo);

    int batchRoleType(List<MidRoleType> midRoleTypes);
}
