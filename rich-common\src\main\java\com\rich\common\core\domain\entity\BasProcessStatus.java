package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 进度状态对象 bas_process_status
 *
 * <AUTHOR>
 * @date 2023-06-05
 */
public class BasProcessStatus extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 进度状态类型
     */
    private Long processStatusId;

    /**
     * 简称
     */
    @Excel(name = "简称")
    private String processStatusShortName;

    /**
     * 全称
     */
    @Excel(name = "全称")
    private String processStatusLocalName;

    /**
     * 英文名
     */
    @Excel(name = "英文名")
    private String processStatusEnName;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Integer orderNum;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;
    private Long typeId;

    public Long getTypeId() {
        return typeId;
    }

    public void setTypeId(Long typeId) {
        this.typeId = typeId;
    }


    public void setProcessStatusId(Long processStatusId) {
        this.processStatusId = processStatusId;
    }

    public Long getProcessStatusId() {
        return processStatusId;
    }

    public void setProcessStatusShortName(String processStatusShortName) {
        this.processStatusShortName = processStatusShortName;
    }

    public String getProcessStatusShortName() {
        return processStatusShortName;
    }

    public void setProcessStatusLocalName(String processStatusLocalName) {
        this.processStatusLocalName = processStatusLocalName;
    }

    public String getProcessStatusLocalName() {
        return processStatusLocalName;
    }

    public void setProcessStatusEnName(String processStatusEnName) {
        this.processStatusEnName = processStatusEnName;
    }

    public String getProcessStatusEnName() {
        return processStatusEnName;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
}
