<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="AutoCloseableResource" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="METHOD_MATCHER_CONFIG" value="java.util.Formatter,format,java.io.Writer,append,com.google.common.base.Preconditions,checkNotNull,org.hibernate.Session,close,java.io.PrintWriter,printf,java.io.PrintStream,printf,java.nio.file.Files,newOutputStream,org.springframework.data.elasticsearch.client.RestClients,create,com.github.pagehelper.page.PageMethod,startPage" />
    </inspection_tool>
    <inspection_tool class="DuplicatedCode" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <Languages>
        <language minSize="100" name="Java" />
      </Languages>
    </inspection_tool>
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="FieldCanBeLocal" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="EXCLUDE_ANNOS">
        <value>
          <list size="1">
            <item index="0" class="java.lang.String" itemvalue="com.rich.common.annotation.Excel" />
          </list>
        </value>
      </option>
      <option name="IGNORE_FIELDS_USED_IN_MULTIPLE_METHODS" value="true" />
    </inspection_tool>
    <inspection_tool class="HtmlUnknownAttribute" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="myValues">
        <value>
          <list size="1">
            <item index="0" class="java.lang.String" itemvalue="canvas-id" />
          </list>
        </value>
      </option>
      <option name="myCustomValuesEnabled" value="true" />
    </inspection_tool>
  </profile>
</component>