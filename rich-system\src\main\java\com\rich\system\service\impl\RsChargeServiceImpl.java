package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.MidChargeBankWriteoff;
import com.rich.common.core.domain.entity.RsCharge;
import com.rich.common.utils.SecurityUtils;
import com.rich.system.mapper.MidChargeBankWriteoffMapper;
import com.rich.system.mapper.RsChargeMapper;
import com.rich.system.service.RsChargeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 费用明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@Service
public class RsChargeServiceImpl implements RsChargeService {
    @Autowired
    private RsChargeMapper rsChargeMapper;

    @Resource
    private MidChargeBankWriteoffMapper midChargeBankWriteoffMapper;

    /**
     * 查询费用明细
     *
     * @param chargeId 费用明细主键
     * @return 费用明细
     */
    @Override
    public RsCharge selectRsChargeByChargeId(Long chargeId) {
        return rsChargeMapper.selectRsChargeByChargeId(chargeId);
    }

    /**
     * 查询费用明细列表
     *
     * @param rsCharge 费用明细
     * @return 费用明细
     */
    @Override
    public List<RsCharge> selectRsChargeList(RsCharge rsCharge) {
        return rsChargeMapper.selectRsChargeList(rsCharge);
    }

    /**
     * 新增费用明细
     *
     * @param rsCharge 费用明细
     * @return 结果
     */
    @Override
    public int insertRsCharge(RsCharge rsCharge) {
        return rsChargeMapper.insertRsCharge(rsCharge);
    }

    /**
     * 修改费用明细
     *
     * @param rsCharge 费用明细
     * @return 结果
     */
    @Override
    public int updateRsCharge(RsCharge rsCharge) {
        return rsChargeMapper.updateRsCharge(rsCharge);
    }

    /**
     * 修改费用明细状态
     *
     * @param rsCharge 费用明细
     * @return 费用明细
     */
    @Override
    public int changeStatus(RsCharge rsCharge) {
        return rsChargeMapper.updateRsCharge(rsCharge);
    }

    @Override
    public List<RsCharge> selectRsChargeByrctId(RsCharge rsCharge) {
        return rsChargeMapper.selectRsChargeList(rsCharge);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertRsChargeAndMidChargeBankWriteoff(List<RsCharge> rsChargeList, List<MidChargeBankWriteoff> midChargeBankWriteoffList) {
        if (midChargeBankWriteoffList == null || midChargeBankWriteoffList.isEmpty()) {
            return;
        }

        if (rsChargeList == null || rsChargeList.isEmpty()) {
            return;
        }

        // 更新费用列表
        for (RsCharge rsCharge : rsChargeList) {
            if (rsCharge == null) {
                continue;
            }

            // 更新费用
            MidChargeBankWriteoff item = null;
            for (MidChargeBankWriteoff midChargeBankWriteoff : midChargeBankWriteoffList) {
                if (midChargeBankWriteoff != null && midChargeBankWriteoff.getChargeId() != null
                        && midChargeBankWriteoff.getChargeId().equals(rsCharge.getChargeId())) {
                    item = midChargeBankWriteoff;
                    break;
                }
            }

            if (item != null) {
                // 费用余额
                BigDecimal dnCurrencyBalance = rsCharge.getDnCurrencyBalance();
                BigDecimal writeoffFromDnBalance = item.getWriteoffFromDnBalance();
                if (dnCurrencyBalance != null && writeoffFromDnBalance != null) {
                    BigDecimal newBalance = dnCurrencyBalance.subtract(writeoffFromDnBalance);
                    rsCharge.setDnCurrencyBalance(newBalance.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : newBalance);
                }

                // 已销账
                BigDecimal sqdDnCurrencyPaid = rsCharge.getSqdDnCurrencyPaid();
                if (sqdDnCurrencyPaid != null && writeoffFromDnBalance != null) {
                    rsCharge.setSqdDnCurrencyPaid(sqdDnCurrencyPaid.add(writeoffFromDnBalance));
                }

                // 未销账余额
                BigDecimal subtotal = rsCharge.getSubtotal();
                sqdDnCurrencyPaid = rsCharge.getSqdDnCurrencyPaid();
                if (subtotal != null && sqdDnCurrencyPaid != null) {
                    BigDecimal balance = subtotal.subtract(sqdDnCurrencyPaid);
                    rsCharge.setSqdDnCurrencyBalance(balance.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : balance);
                }
            } else {
                // 费用余额
                BigDecimal dnUnitRate = rsCharge.getDnUnitRate();
                BigDecimal dnAmount = rsCharge.getDnAmount();
                if (dnUnitRate != null && dnAmount != null) {
                    rsCharge.setDnCurrencyBalance(dnUnitRate.multiply(dnAmount));
                }

                // 已销账
                rsCharge.setSqdDnCurrencyPaid(BigDecimal.ZERO);

                // 未销账余额
                BigDecimal dnCurrencyBalance = rsCharge.getDnCurrencyBalance();
                if (dnCurrencyBalance != null) {
                    rsCharge.setSqdDnCurrencyBalance(dnCurrencyBalance);
                }
            }

            // 销账状态
            BigDecimal sqdDnCurrencyBalance = rsCharge.getSqdDnCurrencyBalance();
            if (sqdDnCurrencyBalance != null) {
                rsCharge.setWriteoffStatus(sqdDnCurrencyBalance.compareTo(BigDecimal.ZERO) == 0 ? "1" : "0");
            }

            rsChargeMapper.updateRsCharge(rsCharge);
        }

        for (MidChargeBankWriteoff midChargeBankWriteoff : midChargeBankWriteoffList) {
            if (midChargeBankWriteoff != null) {
                midChargeBankWriteoff.setWriteoffStaffId(SecurityUtils.getUserId());
                midChargeBankWriteoff.setWriteoffTime(new Date());
            }
        }
        // 插入销账中间表
        midChargeBankWriteoffMapper.batchMidChargeBankWriteoff(midChargeBankWriteoffList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void verifyRsCharge(List<RsCharge> rsCharge) {
        for (RsCharge charge : rsCharge) {
            if (charge.getIsAccountConfirmed().equals("0")) {
                // 费用审核
                charge.setSqdDnCurrencyPaid(BigDecimal.ZERO);
                charge.setIsAccountConfirmed("1");
            } else {
                // 取消审核
                charge.setIsAccountConfirmed("0");
            }
            rsChargeMapper.updateRsCharge(charge);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void turnBackWriteoffList(List<RsCharge> rsChargeList) {
        if (rsChargeList == null || rsChargeList.isEmpty()) {
            return;
        }

        // 1. 先收集所有需要删除的中间表记录ID
        List<Long> midChargeBankIdsToDelete = new ArrayList<>();
        for (RsCharge rsCharge : rsChargeList) {
            if (rsCharge.getMidChargeBankWriteoff() == null) {
                continue;
            }

            // 收集需要删除的中间表ID
            MidChargeBankWriteoff midChargeBankWriteoff = new MidChargeBankWriteoff();
            midChargeBankWriteoff.setBankRecordId(rsCharge.getMidChargeBankWriteoff().getBankRecordId());
            midChargeBankWriteoff.setChargeId(rsCharge.getMidChargeBankWriteoff().getChargeId());
            List<MidChargeBankWriteoff> midChargeBankWriteoffs = midChargeBankWriteoffMapper.selectMidChargeBankWriteoffList(midChargeBankWriteoff);

            if (!midChargeBankWriteoffs.isEmpty()) {
                midChargeBankWriteoffs.stream()
                        .map(MidChargeBankWriteoff::getMidChargeBankId)
                        .forEach(midChargeBankIdsToDelete::add);
            }

            // 更新费用余额
            BigDecimal writeoffBalance = rsCharge.getMidChargeBankWriteoff().getWriteoffFromDnBalance();
            if (writeoffBalance != null) {
                // 未销账金额
                rsCharge.setSqdDnCurrencyBalance(rsCharge.getSqdDnCurrencyBalance().add(writeoffBalance));
                // 已销账金额
                rsCharge.setSqdDnCurrencyPaid(rsCharge.getSqdDnCurrencyPaid().subtract(writeoffBalance));
                // 费用余额
                rsCharge.setDnCurrencyBalance(rsCharge.getSqdDnCurrencyBalance());
            }

            rsCharge.setWriteoffStatus("0");
        }

        // 2. 先执行删除操作
        if (!midChargeBankIdsToDelete.isEmpty()) {
            midChargeBankWriteoffMapper.deleteMidChargeBankWriteoffByMidChargeBankIds(
                    midChargeBankIdsToDelete.toArray(new Long[0]));
        }

        // 3. 批量更新费用记录，而不是一条一条更新
        for (RsCharge rsCharge : rsChargeList) {
            if (rsCharge.getMidChargeBankWriteoff() != null) {
                rsChargeMapper.updateRsCharge(rsCharge);
            }
        }
    }
    @Override
    public int batchRsCharge(List<RsCharge> rsCharge) {
        return rsChargeMapper.batchRsCharge(rsCharge);
    }

    @Override
    public List<RsCharge> selectRsChargeByServiceId(Long serviceId) {
        return rsChargeMapper.selectRsChargeListByServiceId(serviceId);
    }

    @Override
    public List<RsCharge> selectWriteOffRsChargeList(RsCharge rsCharge) {
        ArrayList<RsCharge> rsCharges = new ArrayList<>();
        // 销完的费用
        rsCharges.addAll(rsChargeMapper.selectWriteOffRsChargeList(rsCharge));
        // 没销完 没销过的费用
        rsCharges.addAll(rsChargeMapper.selectRsChargeListForWriteOff(rsCharge));
        return rsCharges;
    }

    @Override
    public List<RsCharge> findHedging(RsCharge rsCharge) {
        return rsChargeMapper.findHedging(rsCharge);
    }

    /**
     * 批量删除费用明细
     *
     * @param chargeIds 需要删除的费用明细主键
     * @return 结果
     */
    @Override
    public int deleteRsChargeByChargeIds(Long[] chargeIds) {
        return rsChargeMapper.deleteRsChargeByChargeIds(chargeIds);
    }

    /**
     * 删除费用明细信息
     *
     * @param chargeId 费用明细主键
     * @return 结果
     */
    @Override
    public int deleteRsChargeByChargeId(Long chargeId) {
        return rsChargeMapper.deleteRsChargeByChargeId(chargeId);
    }
}
