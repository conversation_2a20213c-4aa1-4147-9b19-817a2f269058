package com.rich.web.job;

import com.rich.system.service.RsRctService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 通知数量更新定时任务
 */
@Component
public class NotificationTask {

    @Autowired
    private RsRctService rsRctService;

    /**
     * 每隔5分钟更新一次通知数量
     */
    @Scheduled(fixedRate = 300000)
    public void updateNotification() {
        rsRctService.notification();
    }
} 