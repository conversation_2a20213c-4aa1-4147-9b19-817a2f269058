package com.rich.system.mapper;

/**
 * <AUTHOR>
 * @Date 2024/9/18 16:57
 * @Version 1.0
 */

import com.rich.common.core.domain.entity.RsOpCtnrTruck;
import com.rich.common.core.domain.entity.RsOpTruckList;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 拖车服务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-18
 */
@Mapper
public interface RsOpTruckListMapper {
    /**
     * 查询拖车服务
     *
     * @param truckId 拖车服务主键
     * @return 拖车服务
     */
    RsOpTruckList selectRsOpTruckListByTruckId(Long truckId);

    /**
     * 查询拖车服务列表
     *
     * @param rsOpTruckList 拖车服务
     * @return 拖车服务集合
     */
    List<RsOpTruckList> selectRsOpTruckListList(RsOpTruckList rsOpTruckList);

    /**
     * 新增拖车服务
     *
     * @param rsOpTruckList 拖车服务
     * @return 结果
     */
    int insertRsOpTruckList(RsOpTruckList rsOpTruckList);

    /**
     * 修改拖车服务
     *
     * @param rsOpTruckList 拖车服务
     * @return 结果
     */
    int updateRsOpTruckList(RsOpTruckList rsOpTruckList);

    /**
     * 删除拖车服务
     *
     * @param truckId 拖车服务主键
     * @return 结果
     */
    int deleteRsOpTruckListByTruckId(Long truckId);

    /**
     * 批量删除拖车服务
     *
     * @param truckIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsOpTruckListByTruckIds(Long[] truckIds);

    void insertRsOpTruckList(RsOpCtnrTruck rsOpCtnrTruckItem);

    void deleteRsOpTruckListByServiceId(Long serviceId);

    void deleteRsOpTruckListByRctNo(String rctNo);

    List<RsOpTruckList> selectRsOpTruckListByServiceId(Long serviceId);
}
