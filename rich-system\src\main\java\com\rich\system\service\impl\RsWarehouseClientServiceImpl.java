package com.rich.system.service.impl;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import com.rich.common.core.domain.entity.RsWarehouseClient;
import com.rich.system.mapper.ExtCompanyMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsWarehouseClientMapper;
import com.rich.system.service.RsWarehouseClientService;

/**
 * 仓库客户信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-11
 */
@Service
public class RsWarehouseClientServiceImpl implements RsWarehouseClientService {
    @Autowired
    private RsWarehouseClientMapper rsWarehouseClientMapper;

    @Autowired
    private ExtCompanyMapper extCompanyMapper;

    /**
     * 查询仓库客户信息
     *
     * @param warehouseClientId 仓库客户信息主键
     * @return 仓库客户信息
     */
    @Override
    public RsWarehouseClient selectRsWarehouseClientByWarehouseClientId(Long warehouseClientId) {

        RsWarehouseClient rsWarehouseClient = rsWarehouseClientMapper.selectRsWarehouseClientByWarehouseClientId(warehouseClientId);
        rsWarehouseClient.setCompanyList(Collections.singletonList(extCompanyMapper.selectExtCompanyByCompanyId(rsWarehouseClient.getClientSystemId())));
        return rsWarehouseClient;
    }

    /**
     * 查询仓库客户信息列表
     *
     * @param rsWarehouseClient 仓库客户信息
     * @return 仓库客户信息
     */
    @Override
    public List<RsWarehouseClient> selectRsWarehouseClientList(RsWarehouseClient rsWarehouseClient) {
        return rsWarehouseClientMapper.selectRsWarehouseClientList(rsWarehouseClient);
    }

    /**
     * 新增仓库客户信息
     *
     * @param rsWarehouseClient 仓库客户信息
     * @return 结果
     */
    @Override
    public int insertRsWarehouseClient(RsWarehouseClient rsWarehouseClient) {
        return rsWarehouseClientMapper.insertRsWarehouseClient(rsWarehouseClient);
    }

    /**
     * 修改仓库客户信息
     *
     * @param rsWarehouseClient 仓库客户信息
     * @return 结果
     */
    @Override
    public int updateRsWarehouseClient(RsWarehouseClient rsWarehouseClient) {
        return rsWarehouseClientMapper.updateRsWarehouseClient(rsWarehouseClient);
    }

    /**
     * 修改仓库客户信息状态
     *
     * @param rsWarehouseClient 仓库客户信息
     * @return 仓库客户信息
     */
    @Override
    public int changeStatus(RsWarehouseClient rsWarehouseClient) {
        return rsWarehouseClientMapper.updateRsWarehouseClient(rsWarehouseClient);
    }

    @Override
    public int checkClientCode(String clientCode) {

        return rsWarehouseClientMapper.checkClientCode(clientCode);
    }

    /**
     * 批量删除仓库客户信息
     *
     * @param warehouseClientIds 需要删除的仓库客户信息主键
     * @return 结果
     */
    @Override
    public int deleteRsWarehouseClientByWarehouseClientIds(Long[] warehouseClientIds) {
        return rsWarehouseClientMapper.deleteRsWarehouseClientByWarehouseClientIds(warehouseClientIds);
    }

    /**
     * 删除仓库客户信息信息
     *
     * @param warehouseClientId 仓库客户信息主键
     * @return 结果
     */
    @Override
    public int deleteRsWarehouseClientByWarehouseClientId(Long warehouseClientId) {
        return rsWarehouseClientMapper.deleteRsWarehouseClientByWarehouseClientId(warehouseClientId);
    }
}
