package com.rich.system.mapper;

import com.rich.common.core.domain.entity.ExtCompany;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 客户供应商公司Mapper接口
 *
 * <AUTHOR>
 * @date 2022-08-18
 */
@Mapper
public interface ExtCompanyMapper {
    /**
     * 查询客户供应商公司
     *
     * @param companyId 客户供应商公司主键
     * @return 客户供应商公司
     */
    ExtCompany selectExtCompanyByCompanyId(Long companyId);

    ExtCompany selectExtCompanyByNames(ExtCompany extCompany);

    /**
     * 查询客户供应商公司列表
     *
     * @param extCompany 客户供应商公司
     * @return 客户供应商公司集合
     */
    List<ExtCompany> queryCompany(ExtCompany extCompany);

    /**
     * 新增客户供应商公司
     *
     * @param extCompany 客户供应商公司
     * @return 结果
     */
    int insertExtCompany(ExtCompany extCompany);

    /**
     * 修改客户供应商公司
     *
     * @param extCompany 客户供应商公司
     * @return 结果
     */
    int updateExtCompany(ExtCompany extCompany);

    /**
     * 删除客户供应商公司
     *
     * @param companyId 客户供应商公司主键
     * @return 结果
     */
    int deleteExtCompanyByCompanyId(Long companyId);

    /**
     * 批量删除客户供应商公司
     *
     * @param companyIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteExtCompanyByCompanyIds(Long[] companyIds);

    ExtCompany checkCompanyShortNameUnique(String companyShortName,String companyEnShortName);

    int checkCompanyLocalNameUnique(String companyLocalName, Long roleType, Long companyId);

    int checkCompanyLocalNameUniqueByRoleRich(String companyLocalName, Long roleType, Long companyId);

    int checkCompanyLocalNameUniqueByRoleClient(String companyLocalName, Long roleType, Long companyId);

    int checkCompanyLocalNameUniqueByRoleSupplier(String companyLocalName, Long roleType, Long companyId);

    int checkCompanyLocalNameUniqueByRoleSupport(String companyLocalName, Long roleType, Long companyId);


    int checkCompanyEnNameUnique(String companyEnName, Long roleTypeId,Long companyId);

    int checkCompanyEnNameUniqueByRoleRich(String companyEnName, Long roleTypeId, Long companyId);

    int checkCompanyEnNameUniqueByRoleClient(String companyEnName, Long roleTypeId, Long companyId);

    int checkCompanyEnNameUniqueByRoleSupplier(String companyEnName, Long roleTypeId, Long companyId);

    int checkCompanyEnNameUniqueByRoleSupport(String companyEnName, Long roleTypeId, Long companyId);

    int setDeleteExtCompanyByCompanyIds(Long[] companyIds);

    ExtCompany getDelCompany(Long companyId);

    List<ExtCompany> selectExtCompanyList(ExtCompany extCompany);

    List<ExtCompany> selectExtCompanyByCompanyIds(@Param("companyIds") Set<Long> companyIds);

    List<ExtCompany> queryCompanyByRoleControlWithoutPage(ExtCompany extCompany);

    ExtCompany checkCompanyShortNameUniqueByRoleRich(String companyShortName, String companyEnShortName);

    ExtCompany checkCompanyShortNameUniqueByRoleClient(String companyShortName, String companyEnShortName);

    ExtCompany checkCompanyShortNameUniqueByRoleSupplier(String companyShortName, String companyEnShortName);

    ExtCompany checkCompanyShortNameUniqueByRoleSupport(String companyShortName, String companyEnShortName);

    ExtCompany selectExtCompanyByCompanyName(String companyShortName, String companyEnShortName);

    int deleteExtCompanyByRoleType(ExtCompany extCompany);

    List<ExtCompany> selectExtCompanys(ExtCompany extCompany);

    List<ExtCompany> selectExtCompanyByRct(ExtCompany extCompany);
}
