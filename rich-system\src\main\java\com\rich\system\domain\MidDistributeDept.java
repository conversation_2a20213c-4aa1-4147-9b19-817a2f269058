package com.rich.system.domain;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 【请填写功能名称】对象 mid_distribute_dept
 *
 * <AUTHOR>
 * @date 2023-03-20
 */
public class MidDistributeDept extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long distributeId;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long deptId;

    public Long getDistributeId() {
        return distributeId;
    }

    public void setDistributeId(Long distributeId) {
        this.distributeId = distributeId;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("distributeId", getDistributeId())
                .append("deptId", getDeptId())
                .toString();
    }
}
