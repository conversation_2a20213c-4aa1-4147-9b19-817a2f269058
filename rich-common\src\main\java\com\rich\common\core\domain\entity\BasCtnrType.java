package com.rich.common.core.domain.entity;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 箱型特征对象 bas_ctnr_type
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
public class BasCtnrType extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long ctnrTypeId;

    /**
     * 箱型类型名缩写
     */
    @Excel(name = "箱型类型名缩写")
    private String ctnrTypeShortName;

    /**
     * 箱型类型中文名
     */
    @Excel(name = "箱型类型中文名")
    private String ctnrTypeLocalName;

    /**
     * 箱型类型英文名
     */
    @Excel(name = "箱型类型英文名")
    private String ctnrTypeEnName;

    /**
     * 箱型类型等级
     */
    @Excel(name = "箱型类型等级")
    private Integer ctnrTypeLevel;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String featureType;

    /**
     * 是否上锁
     */
    @Excel(name = "是否上锁")
    private Integer isLocked;

    /**
     * 上下层排序
     */
    @Excel(name = "上下层排序")
    private Integer verticalSort;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Integer orderNum;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;

    /**
     * 删除时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "删除时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date deleteTime;


    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long deleteBy;
    private String ctnrTypeCode;

    public String getCtnrTypeCode() {
        return ctnrTypeCode;
    }

    public void setCtnrTypeCode(String ctnrTypeCode) {
        this.ctnrTypeCode = ctnrTypeCode;
    }

    public Long getCtnrTypeId() {
        return ctnrTypeId;
    }

    public void setCtnrTypeId(Long ctnrTypeId) {
        this.ctnrTypeId = ctnrTypeId;
    }

    public String getCtnrTypeShortName() {
        return ctnrTypeShortName;
    }

    public void setCtnrTypeShortName(String ctnrTypeShortName) {
        this.ctnrTypeShortName = ctnrTypeShortName;
    }

    public String getCtnrTypeLocalName() {
        return ctnrTypeLocalName;
    }

    public void setCtnrTypeLocalName(String ctnrTypeLocalName) {
        this.ctnrTypeLocalName = ctnrTypeLocalName;
    }

    public String getCtnrTypeEnName() {
        return ctnrTypeEnName;
    }

    public void setCtnrTypeEnName(String ctnrTypeEnName) {
        this.ctnrTypeEnName = ctnrTypeEnName;
    }

    public Integer getCtnrTypeLevel() {
        return ctnrTypeLevel;
    }

    public void setCtnrTypeLevel(Integer ctnrTypeLevel) {
        this.ctnrTypeLevel = ctnrTypeLevel;
    }

    public String getFeatureType() {
        return featureType;
    }

    public void setFeatureType(String featureType) {
        this.featureType = featureType;
    }

    public Integer getIsLocked() {
        return isLocked;
    }

    public void setIsLocked(Integer isLocked) {
        this.isLocked = isLocked;
    }

    public Integer getVerticalSort() {
        return verticalSort;
    }

    public void setVerticalSort(Integer verticalSort) {
        this.verticalSort = verticalSort;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Long getDeleteBy() {
        return deleteBy;
    }

    public void setDeleteBy(Long deleteBy) {
        this.deleteBy = deleteBy;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("ctnrTypeId", getCtnrTypeId())
                .append("ctnrTypeShortName", getCtnrTypeShortName())
                .append("ctnrTypeLocalName", getCtnrTypeLocalName())
                .append("ctnrTypeEnName", getCtnrTypeEnName())
                .append("ctnrTypeLevel", getCtnrTypeLevel())
                .append("featureType", getFeatureType())
                .append("isLocked", getIsLocked())
                .append("verticalSort", getVerticalSort())
                .append("orderNum", getOrderNum())
                .append("status", getStatus())
                .append("remark", getRemark())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("deleteTime", getDeleteTime())
                .append("deleteStatus", getDeleteStatus())
                .append("deleteBy", getDeleteBy())
                .append("updateBy", getUpdateBy())
                .append("createBy", getCreateBy())
                .toString();
    }
}
