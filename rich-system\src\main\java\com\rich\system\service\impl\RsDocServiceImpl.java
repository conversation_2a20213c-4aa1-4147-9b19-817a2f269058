package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsDoc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsDocMapper;
import com.rich.system.service.RsDocService;

/**
 * 文件信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@Service
public class RsDocServiceImpl implements RsDocService {
    @Autowired
    private RsDocMapper rsDocMapper;

    /**
     * 查询文件信息
     *
     * @param docId 文件信息主键
     * @return 文件信息
     */
    @Override
    public RsDoc selectRsDocByDocId(Long docId) {
        return rsDocMapper.selectRsDocByDocId(docId);
    }

    /**
     * 查询文件信息列表
     *
     * @param rsDoc 文件信息
     * @return 文件信息
     */
    @Override
    public List<RsDoc> selectRsDocList(RsDoc rsDoc) {
        return rsDocMapper.selectRsDocList(rsDoc);
    }

    /**
     * 新增文件信息
     *
     * @param rsDoc 文件信息
     * @return 结果
     */
    @Override
    public int insertRsDoc(RsDoc rsDoc) {
        return rsDocMapper.insertRsDoc(rsDoc);
    }

    /**
     * 修改文件信息
     *
     * @param rsDoc 文件信息
     * @return 结果
     */
    @Override
    public int updateRsDoc(RsDoc rsDoc) {
        return rsDocMapper.updateRsDoc(rsDoc);
    }

    /**
     * 修改文件信息状态
     *
     * @param rsDoc 文件信息
     * @return 文件信息
     */
    @Override
    public int changeStatus(RsDoc rsDoc) {
        return rsDocMapper.updateRsDoc(rsDoc);
    }

    /**
     * 批量删除文件信息
     *
     * @param docIds 需要删除的文件信息主键
     * @return 结果
     */
    @Override
    public int deleteRsDocByDocIds(Long[] docIds) {
        return rsDocMapper.deleteRsDocByDocIds(docIds);
    }

    /**
     * 删除文件信息信息
     *
     * @param docId 文件信息主键
     * @return 结果
     */
    @Override
    public int deleteRsDocByDocId(Long docId) {
        return rsDocMapper.deleteRsDocByDocId(docId);
    }
}
