package com.rich.system.service.impl;

import com.rich.common.constant.CacheConstants;
import com.rich.common.core.domain.entity.*;
import com.rich.common.core.redis.RedisCache;
import com.rich.system.domain.*;
import com.rich.system.mapper.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/12/19 16:30
 * @Version 1.0
 */
@Service

public class RedisCacheImpl {
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private RsLocalMapper rsLocalMapper;
    @Autowired
    private ExtCompanyMapper extCompanyMapper;
    @Autowired
    private BasDistLocationMapper basDistLocationMapper;
    @Autowired
    private BasDistLineMapper basDistLineMapper;
    @Autowired
    private SysRoleMapper sysRoleMapper;
    @Autowired
    private SysMenuMapper sysMenuMapper;
    @Autowired
    private RsFreightMapper rsFreightMapper;
    @Autowired
    private BasDistUnitMapper basDistUnitMapper;
    @Autowired
    private RsCharacteristicsMapper rsCharacteristicsMapper;
    @Autowired
    private BasDistDeptMapper basDistDeptMapper;
    @Autowired
    private BasCarrierMapper basCarrierMapper;
    @Autowired
    private RsWarehouseClientMapper rsWarehouseClientMapper;
    @Autowired
    private BasChargeMapper basChargeMapper;
    @Autowired
    private BasDistCargoTypeMapper basDistCargoTypeMapper;
    @Autowired
    private BasCurrencyMapper basCurrencyMapper;
    @Autowired
    private BasCommonInfoMapper basCommonInfoMapper;
    @Autowired
    private BasDistServiceTypeMapper basDistServiceTypeMapper;
    @Autowired
    private BasContractTypeMapper basContractTypeMapper;
    @Autowired
    private MidCompanyRoleMapper midCompanyRoleMapper;
    @Autowired
    private MidCargoTypeMapper midCargoTypeMapper;
    @Autowired
    private MidCarrierMapper midCarrierMapper;
    @Autowired
    private MidOrganizationMapper midOrganizationMapper;
    @Autowired
    private MidServiceTypeMapper midServiceTypeMapper;
    @Autowired
    private MidLocationLoadingMapper midLocationLoadingMapper;
    @Autowired
    private MidLineDepartureMapper midLineDepartureMapper;
    @Autowired
    private MidLineDestinationMapper midLineDestinationMapper;
    @Autowired
    private MidLocationDepartureMapper midLocationDepartureMapper;
    @Autowired
    private MidLocationDestinationMapper midLocationDestinationMapper;
    @Autowired
    private BasExchangeRateMapper basExchangeRateMapper;
    @Autowired
    private BasPaymentTypeMapper basPaymentTypeMapper;
    @Autowired
    private BasReleaseTypeMapper basReleaseTypeMapper;
    @Autowired
    private BasPaymentChannelsMapper basPaymentChannelsMapper;
    @Autowired
    private BasTransportationTermsMapper basTransportationTermsMapper;
    @Autowired
    private BasDocIssueTypeMapper basDocIssueTypeMapper;
    @Autowired
    private BasDocReleaseWayMapper basDocReleaseWayMapper;
    @Autowired
    private BasTradingTermsMapper basTradingTermsMapper;
    @Autowired
    private BasProcessStatusMapper basProcessStatusMapper;
    @Autowired
    private BasProcessMapper basProcessMapper;
    @Autowired
    private BasDocMapper basDocMapper;
    @Autowired
    private BasDocFlowDirectionMapper basDocFlowDirectionMapper;
    @Autowired
    private RsStaffMapper rsStaffMapper;
    @Autowired
    private RsPaymentTitleMapper rsPaymentTitleMapper;
    @Resource
    private BasAccountMapper basAccountMapper;

    public synchronized void companyAccount() {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "companyAccount");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "companyAccount", basAccountMapper.selectCompanyAccount());
    }

    public synchronized void role() {
        SysRole role = new SysRole();
        role.setStatus("0");
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "role");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "role", sysRoleMapper.selectRoleList(new SysRole()));
    }

    public synchronized void menu() {
        SysMenu menu = new SysMenu();
        menu.setStatus("0");
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "menu");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "menu", sysMenuMapper.selectMenuList(new SysMenu()));
    }

    public synchronized void exchangeRate() {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "exchangeRateList");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "exchangeRateList", basExchangeRateMapper.selectList());
    }

    public synchronized void docIssueType() {
        BasDocIssueType basDocIssueType = new BasDocIssueType();
        basDocIssueType.setStatus("0");
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "docIssueType");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "docIssueType", basDocIssueTypeMapper.selectBasDocIssueTypeList(basDocIssueType));
    }

    public synchronized void doc() {
        BasDoc basDoc = new BasDoc();
        basDoc.setStatus("0");
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "doc");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "doc", basDocMapper.selectBasDocList(basDoc));
    }

    public synchronized void docFlowDirection() {
        BasDocFlowDirection basDocFlowDirection = new BasDocFlowDirection();
        basDocFlowDirection.setStatus("0");
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "docFlowDirection");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "docFlowDirection", basDocFlowDirectionMapper.selectBasDocFlowDirectionList(basDocFlowDirection));
    }

    public synchronized void process() {
        BasProcess basProcess = new BasProcess();
        basProcess.setStatus("0");
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "process");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "process", basProcessMapper.selectBasProcessList(basProcess));
    }

    public synchronized void processStatus() {
        BasProcessStatus basProcessStatus = new BasProcessStatus();
        basProcessStatus.setStatus("0");
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "processStatus");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "processStatus", basProcessStatusMapper.selectBasProcessStatusList(basProcessStatus));
    }

    public synchronized void tradingTerms() {
        BasTradingTerms basTradingTerms = new BasTradingTerms();
        basTradingTerms.setStatus("0");
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "tradingTerms");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "tradingTerms", basTradingTermsMapper.selectBasTradingTermsList(basTradingTerms));
    }

    public synchronized void docReleaseWay() {
        BasDocReleaseWay basDocReleaseWay = new BasDocReleaseWay();
        basDocReleaseWay.setStatus("0");
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "docReleaseWay");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "docReleaseWay", basDocReleaseWayMapper.selectBasDocReleaseWayList(basDocReleaseWay));
    }

    public synchronized void paymentChannels() {
        BasPaymentChannels basPaymentChannels = new BasPaymentChannels();
        basPaymentChannels.setStatus("0");
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "paymentChannels");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "paymentChannels", basPaymentChannelsMapper.selectBasPaymentChannelsList(basPaymentChannels));
    }

    public synchronized void paymentType() {
        BasPaymentType basPaymentType = new BasPaymentType();
        basPaymentType.setStatus("0");
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "paymentType");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "paymentType", basPaymentTypeMapper.selectBasPaymentTypeList(basPaymentType));
    }

    public synchronized void releaseType() {
        BasReleaseType basReleaseType = new BasReleaseType();
        basReleaseType.setStatus("0");
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "releaseType");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "releaseType", basReleaseTypeMapper.selectBasReleaseTypeList(basReleaseType));
    }


    public synchronized void transportationTerms() {
        BasTransportationTerms basTransportationTerms = new BasTransportationTerms();
        basTransportationTerms.setStatus("0");
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "transportationTerms");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "transportationTerms", basTransportationTermsMapper.selectBasTransportationTermsList(basTransportationTerms));
    }

    public synchronized void dept() {
        BasDistDept dept = new BasDistDept();
        dept.setStatus("0");
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "dept");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "dept", basDistDeptMapper.selectDeptList(dept));
    }

    public synchronized void location() {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "location");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "location", basDistLocationMapper.selectLocationList());
    }

    public synchronized void commonInfo() {
        BasCommonInfo basCommonInfo = new BasCommonInfo();
        basCommonInfo.setStatus("0");
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "commonInfo");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "commonInfo", basCommonInfoMapper.selectBasCommonInfoList(basCommonInfo));
    }

    public synchronized void line() {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "line");
        BasDistLine basDistLine = new BasDistLine();
        basDistLine.setStatus("0");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "line", basDistLineMapper.selectBasDistLineList(basDistLine));
    }

    public synchronized void local() {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "local");
        RsLocalCharge RsLocalCharge = new RsLocalCharge();
        RsLocalCharge.setStatus("0");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "local", rsLocalMapper.selectRsLocalList(RsLocalCharge));
    }

    public synchronized void serviceType() {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
        BasDistServiceType basDistServiceType = new BasDistServiceType();
        basDistServiceType.setStatus("0");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "serviceType", basDistServiceTypeMapper.selectBasDistServiceTypeList(basDistServiceType));
    }

    public synchronized void freight() {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "freight");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "freight", rsFreightMapper.selectFreightList());
    }

    public synchronized void characteristics() {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "characteristics");
        RsCharacteristics rsCharacteristics = new RsCharacteristics();
        rsCharacteristics.setStatus("0");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "characteristics", rsCharacteristicsMapper.selectRsCharacteristicsList(rsCharacteristics));
    }

    public synchronized void unit() {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "unit");
        BasDistUnit basDistUnit = new BasDistUnit();
        basDistUnit.setStatus("0");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "unit", basDistUnitMapper.selectBasUnitList(basDistUnit));
    }

    public synchronized void carrier() {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "carrier");
        BasCarrier basCarrier = new BasCarrier();
        basCarrier.setStatus("0");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "carrier", basCarrierMapper.selectBasCarrierList(basCarrier));
    }

    public synchronized void company() {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "company");
        ExtCompany extCompany = new ExtCompany();
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "company", extCompanyMapper.queryCompany(extCompany));
    }

    public synchronized void warehouseClient() {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "warehouseClient");
        RsWarehouseClient rsWarehouseClient = new RsWarehouseClient();
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "warehouseClient", rsWarehouseClientMapper.selectRsWarehouseClientList(rsWarehouseClient));
    }

    private synchronized void charge() {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "charge");
        BasCharge basCharge = new BasCharge();
        basCharge.setStatus("0");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "charge", basChargeMapper.selectBasChargeList(basCharge));
    }

    public synchronized void cargoType() {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        BasDistCargoType basDistCargoType = new BasDistCargoType();
        basDistCargoType.setStatus("0");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType", basDistCargoTypeMapper.selectBasDistCargoTypeList(basDistCargoType));
    }

    public synchronized void currency() {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "currency");
        BasCurrency basCurrency = new BasCurrency();
        basCurrency.setStatus("0");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "currency", basCurrencyMapper.selectBasCurrencyList(basCurrency));
    }


    private synchronized void contractType() {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "contractType");
        BasContractType basContractType = new BasContractType();
        basContractType.setStatus("0");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "contractType", basContractTypeMapper.selectBasContractTypeList(basContractType));
    }

    public synchronized void companyRole(String belongDetail) {
        redisCache.deleteObject(CacheConstants.MID_CACHE_KEY + belongDetail);
        redisCache.setCacheObject(CacheConstants.MID_CACHE_KEY + belongDetail, midCompanyRoleMapper.selectMidCompanyRoleList(new MidCompanyRole()));
    }

    public synchronized void midCarrier(String belongTo, String belongDetail) {
        redisCache.deleteObject(CacheConstants.MID_CACHE_KEY + belongDetail);
        MidCarrier midCarrier = new MidCarrier();
        midCarrier.setBelongTo(belongTo);
        redisCache.setCacheObject(CacheConstants.MID_CACHE_KEY + belongDetail, midCarrierMapper.selectMidCarrierList(midCarrier));
    }

    public synchronized void midCargoType(String belongTo, String belongDetail) {
        redisCache.deleteObject(CacheConstants.MID_CACHE_KEY + belongDetail);
        MidCargoType MidCargoType = new MidCargoType();
        MidCargoType.setBelongTo(belongTo);
        redisCache.setCacheObject(CacheConstants.MID_CACHE_KEY + belongDetail, midCargoTypeMapper.selectMidCargoTypeList(MidCargoType));
    }

    public synchronized void midServiceType(String belongTo, String belongDetail) {
        redisCache.deleteObject(CacheConstants.MID_CACHE_KEY + belongDetail);
        MidServiceType MidServiceType = new MidServiceType();
        MidServiceType.setBelongTo(belongTo);
        redisCache.setCacheObject(CacheConstants.MID_CACHE_KEY + belongDetail, midServiceTypeMapper.selectMidServiceTypeList(MidServiceType));
    }

    public synchronized void organization(String belongTo, String belongDetail) {
        redisCache.deleteObject(CacheConstants.MID_CACHE_KEY + belongDetail);
        MidOrganization midOrganization = new MidOrganization();
        midOrganization.setBelongTo(belongTo);
        redisCache.setCacheObject(CacheConstants.MID_CACHE_KEY + belongDetail, midOrganizationMapper.selectMidOrganizationList(midOrganization));
    }


    public synchronized void locationLoading(String belongTo, String belongDetail) {
        redisCache.deleteObject(CacheConstants.MID_CACHE_KEY + belongDetail);
        MidLocationLoading MidLocationLoading = new MidLocationLoading();
        MidLocationLoading.setBelongTo(belongTo);
        redisCache.setCacheObject(CacheConstants.MID_CACHE_KEY + belongDetail, midLocationLoadingMapper.selectMidLocationLoadingList(MidLocationLoading));
    }

    public synchronized void lineDeparture(String belongTo, String belongDetail) {
        redisCache.deleteObject(CacheConstants.MID_CACHE_KEY + belongDetail);
        MidLineDeparture MidLineDeparture = new MidLineDeparture();
        MidLineDeparture.setBelongTo(belongTo);
        redisCache.setCacheObject(CacheConstants.MID_CACHE_KEY + belongDetail, midLineDepartureMapper.selectMidLineDepartureList(MidLineDeparture));
    }

    public synchronized void lineDestination(String belongTo, String belongDetail) {
        redisCache.deleteObject(CacheConstants.MID_CACHE_KEY + belongDetail);
        MidLineDestination MidLineDestination = new MidLineDestination();
        MidLineDestination.setBelongTo(belongTo);
        redisCache.setCacheObject(CacheConstants.MID_CACHE_KEY + belongDetail, midLineDestinationMapper.selectMidLineDestinationList(MidLineDestination));
    }

    /**
     * 缓存启运港区域列表
     *
     * @param belongTo
     * @param belongDetail
     */
    public synchronized void locationDeparture(String belongTo, String belongDetail) {
        redisCache.deleteObject(CacheConstants.MID_CACHE_KEY + belongDetail);
        MidLocationDeparture MidLocationDeparture = new MidLocationDeparture();
        MidLocationDeparture.setBelongTo(belongTo);
        redisCache.setCacheObject(CacheConstants.MID_CACHE_KEY + belongDetail, midLocationDepartureMapper.selectMidLocationDepartureList(MidLocationDeparture));
    }

    public synchronized void locationDestination(String belongTo, String belongDetail) {
        redisCache.deleteObject(CacheConstants.MID_CACHE_KEY + belongDetail);
        MidLocationDestination MidLocationDestination = new MidLocationDestination();
        MidLocationDestination.setBelongTo(belongTo);
        redisCache.setCacheObject(CacheConstants.MID_CACHE_KEY + belongDetail, midLocationDestinationMapper.selectMidLocationDestinationList(MidLocationDestination));
    }

    /**
     * 缓存公司所有人员
     *
     * @return
     */
    public synchronized void allRsStaff() {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "allRsStaff");
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "allRsStaff", rsStaffMapper.selectAllRsStaff());
    }

    public GlobalData exist() {
        GlobalData globalData = new GlobalData();
        globalData.setStaff(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "staff"));
        globalData.setUnit(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "unit"));
        globalData.setLocation(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "location"));
        globalData.setCargoType(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "cargoType"));
        globalData.setCurrency(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "currency"));
        globalData.setServiceType(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "serviceType"));
        globalData.setCompanySource(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "companySource"));
        globalData.setCompanyRole(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "companyRole"));
        globalData.setContractType(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "contractType"));
        globalData.setLocal(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "local"));
        globalData.setDocType(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "docType"));
        globalData.setIssue(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "issue"));
        globalData.setChargeType(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "chargeType"));
        globalData.setCommonInfo(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "commonInfo"));
        globalData.setOrganization(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "organization"));
        globalData.setCommonInfoType(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "commonInfoType"));
        globalData.setCharge(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "charge"));
        globalData.setCompanyRoleType(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "companyRoleType"));
        globalData.setLine(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "line"));
        globalData.setCarrier(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "carrier"));
        globalData.setDept(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "dept"));
        globalData.setRole(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "role"));
        globalData.setBusinessesList(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "roleStaff商务部"));
        globalData.setOpList(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "roleStaff操作部"));
        globalData.setServiceTypeCarriers(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "serviceTypeCarriers"));
        globalData.setSalesList(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "staffByRoleDept业务部"));
        globalData.setAllBelongList(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "staffByRoleDept瑞旗公司"));
        globalData.setLogisticsTimeNodeList(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "commonInfoType物流时间节点"));
        globalData.setChargeList(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "chargeList"));
        globalData.setMessageTypeList(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "messageTypeList"));
        globalData.setTransportationTermsList(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "transportationTermsList"));
        globalData.setExchangeRateList(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "exchangeRateList"));
        globalData.setSupplier(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "supplierList"));
        globalData.setClient(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "clientList"));
        globalData.setPaymentType(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "paymentType"));
        globalData.setReleaseType(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "releaseType"));
        globalData.setPaymentChannels(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "paymentChannels"));
        globalData.setDocIssueType(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "docIssueType"));
        globalData.setDocReleaseWay(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "docReleaseWay"));
        globalData.setTradingTerms(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "tradingTerms"));
        globalData.setProcessStatus(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "processStatus"));
        globalData.setProcess(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "process"));
        globalData.setDoc(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "doc"));
        globalData.setDocFlowDirection(!redisCache.hasKey(CacheConstants.DATA_CACHE_KEY + "docFlowDirection"));
        return globalData;
    }


    public Long getId(String belong, String value) {
        Long re = null;
        if (belong.equals("启运港")) {
            String[] location = value.split(",");
            List<BasDistLocation> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
            if (list == null || list.isEmpty()) {
                location();
                list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
            }
            for (BasDistLocation o : list) {
                if (o.getPortCode() != null && location[0].equalsIgnoreCase(o.getPortCode().replace(" ", ""))
                        || o.getPortIataCode() != null && location[0].equalsIgnoreCase(o.getPortIataCode().replace(" ", ""))
                        || o.getPortRailCode() != null && location[0].equalsIgnoreCase(o.getPortRailCode().replace(" ", ""))
                        || o.getLocationShortName() != null && location[0].equalsIgnoreCase(o.getLocationShortName().replace(" ", ""))
                        || o.getLocationLocalName() != null && location[0].equalsIgnoreCase(o.getLocationLocalName().replace(" ", ""))
                        || o.getLocationEnName() != null && location[0].equalsIgnoreCase(o.getLocationEnName().replace(" ", ""))
                ) {
                    re = o.getLocationId();
                }
            }
        }
        if (belong.equals("航线")) {
            String[] location = value.split(",");
            List<BasDistLine> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
            if (list == null || list.isEmpty()) {
                location();
                list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
            }
            for (BasDistLine o : list) {
                if (o.getLineEnName() != null && location[0].equalsIgnoreCase(o.getLineEnName().replace(" ", ""))
                        || o.getLineShortName() != null && location[0].equalsIgnoreCase(o.getLineShortName().replace(" ", ""))
                        || o.getLineLocalName() != null && location[0].equalsIgnoreCase(o.getLineLocalName().replace(" ", ""))

                ) {
                    re = o.getLineId();
                }
            }
        }
        if (belong.equals("装运区域")) {
            String[] location = value.split(",");
            List<BasDistLocation> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
            if (list == null || list.isEmpty()) {
                location();
                list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
            }
            for (BasDistLocation o : list) {
                if (o.getPortCode() != null && location[0].equalsIgnoreCase(o.getPortCode().replace(" ", ""))
                        || o.getPortIataCode() != null && location[0].equalsIgnoreCase(o.getPortIataCode().replace(" ", ""))
                        || o.getPortRailCode() != null && location[0].equalsIgnoreCase(o.getPortRailCode().replace(" ", ""))
                        || o.getLocationShortName() != null && location[0].equalsIgnoreCase(o.getLocationShortName().replace(" ", ""))
                        || o.getLocationLocalName() != null && location[0].equalsIgnoreCase(o.getLocationLocalName().replace(" ", ""))
                        || o.getLocationEnName() != null && location[0].equalsIgnoreCase(o.getLocationEnName().replace(" ", ""))
                ) {
                    re = o.getLocationId();
                }
            }
        }
        if (belong.equals("中转港") || belong.equals("目的港")) {
            String[] location = value.split(",");
            List<BasDistLocation> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
            if (list == null || list.isEmpty()) {
                location();
                list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
            }
            for (BasDistLocation o : list) {
                if (o.getPortCode() != null && location[0].equalsIgnoreCase(o.getPortCode().replace(" ", ""))
                        || o.getPortIataCode() != null && location[0].equalsIgnoreCase(o.getPortIataCode().replace(" ", ""))
                        || o.getPortRailCode() != null && location[0].equalsIgnoreCase(o.getPortRailCode().replace(" ", ""))
                        || o.getLocationShortName() != null && location[0].equalsIgnoreCase(o.getLocationShortName().replace(" ", ""))
                        || o.getLocationLocalName() != null && location[0].equalsIgnoreCase(o.getLocationLocalName().replace(" ", ""))
                        || o.getLocationEnName() != null && location[0].equalsIgnoreCase(o.getLocationEnName().replace(" ", ""))) {
                    if (location.length > 1) {
                        if (o.getPortCode() != null && location[1].equalsIgnoreCase(o.getPortCode().substring(0, 2).replace(" ", ""))) {
                            re = o.getLocationId();
                        }
                    } else {
                        re = o.getLocationId();
                    }
                }
            }
        }
        if (belong.equals("柜型") || belong.equals("物流时效单位")) {
            List<BasDistUnit> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "unit");
            if (list == null || list.isEmpty()) {
                unit();
                list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "unit");
            }
            for (BasDistUnit o : list) {
                if (o.getUnitShortName() != null && value.equalsIgnoreCase(o.getUnitShortName().replace(" ", ""))
                        || o.getUnitLocalName() != null && value.equalsIgnoreCase(o.getUnitLocalName().replace(" ", ""))
                        || o.getUnitEnName() != null && value.equalsIgnoreCase(o.getUnitEnName().replace(" ", ""))) {
                    re = o.getUnitId();
                }
            }
        }
        if (belong.equals("合约类别")) {
            List<BasContractType> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "contractType");
            if (list == null || list.isEmpty()) {
                contractType();
                list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "contractType");
            }
            for (BasContractType o : list) {
                if (o.getContractShortName() != null && value.equalsIgnoreCase(o.getContractShortName().replace(" ", ""))
                        || o.getContractLocalName() != null && value.equalsIgnoreCase(o.getContractLocalName().replace(" ", ""))
                        || o.getContractEnName() != null && value.equalsIgnoreCase(o.getContractEnName().replace(" ", ""))) {
                    re = o.getContractTypeId();
                }
            }
        }
        if (belong.equals("承运人")) {
            List<BasCarrier> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "carrier");
            if (list == null || list.isEmpty()) {
                carrier();
                list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "carrier");
            }
            for (BasCarrier o : list) {
                if (o.getCarrierShortName() != null && value.equalsIgnoreCase(o.getCarrierShortName().replace(" ", ""))
                        || o.getCarrierLocalName() != null && value.equalsIgnoreCase(o.getCarrierLocalName().replace(" ", ""))
                        || o.getCarrierEnName() != null && value.equalsIgnoreCase(o.getCarrierEnName().replace(" ", ""))
                        || o.getCarrierIntlCode() != null && value.equalsIgnoreCase(o.getCarrierIntlCode().replace(" ", ""))) {
                    re = o.getCarrierId();
                }
            }
        }
        if (belong.equals("价格类别")) {
            List<BasCharge> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "charge");
            if (list == null || list.isEmpty()) {
                charge();
                list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "charge");
            }
            for (BasCharge o : list) {
                if (o.getChargeShortName() != null && value.equalsIgnoreCase(o.getChargeShortName().replace(" ", ""))
                        || o.getChargeLocalName() != null && value.equalsIgnoreCase(o.getChargeLocalName().replace(" ", ""))
                        || o.getChargeEnName() != null && value.equalsIgnoreCase(o.getChargeEnName().replace(" ", ""))) {
                    re = o.getChargeId();
                }
            }
        }
        if (belong.equals("币种")) {
            List<BasCurrency> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "currency");
            if (list == null || list.isEmpty()) {
                currency();
                list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "currency");
            }
            for (BasCurrency o : list) {
                if (o.getCurrencyCode() != null && value.equalsIgnoreCase(o.getCurrencyCode().replace(" ", ""))
                        || o.getCurrencyLocalName() != null && value.equalsIgnoreCase(o.getCurrencyLocalName().replace(" ", ""))) {
                    re = o.getCurrencyId();
                }
            }
        }
        if (belong.equals("货物类型")) {
            List<BasDistCargoType> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
            if (list == null || list.isEmpty()) {
                cargoType();
                list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
            }
            for (BasDistCargoType o : list) {
                if (o.getCargoTypeLocalName() != null && value.equalsIgnoreCase(o.getCargoTypeLocalName().replace(" ", ""))
                        || o.getCargoTypeShortName() != null && value.equalsIgnoreCase(o.getCargoTypeShortName().replace(" ", ""))) {
                    re = o.getCargoTypeId();
                }
            }
        }
        if (belong.equals("物流时效节点") || belong.equals("有效期节点")) {
            List<BasCommonInfo> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "commonInfo");
            if (list == null || list.isEmpty()) {
                commonInfo();
                list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "commonInfo");
            }
            for (BasCommonInfo o : list) {
                if (o.getInfoShortName() != null && value.equalsIgnoreCase(o.getInfoShortName().replace(" ", ""))
                        || o.getInfoLocalName() != null && value.equalsIgnoreCase(o.getInfoLocalName().replace(" ", ""))
                        || o.getInfoEnName() != null && value.equalsIgnoreCase(o.getInfoEnName().replace(" ", ""))) {
                    re = o.getInfoId();
                }
            }
        }
        if (belong.equals("服务项目")) {
            List<BasDistServiceType> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
            if (list == null || list.isEmpty()) {
                commonInfo();
                list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
            }
            for (BasDistServiceType o : list) {
                if (o.getServiceShortName() != null && value.equalsIgnoreCase(o.getServiceShortName().replace(" ", ""))
                        || o.getServiceLocalName() != null && value.equalsIgnoreCase(o.getServiceLocalName().replace(" ", ""))
                        || o.getServiceEnName() != null && value.equalsIgnoreCase(o.getServiceEnName().replace(" ", ""))) {
                    re = o.getServiceTypeId();
                }
            }
        }
        if (belong.equals("服务大类")) {
            List<BasDistServiceType> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
            if (list == null || list.isEmpty()) {
                commonInfo();
                list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "serviceType");
            }
            for (BasDistServiceType o : list) {
                if (o.getServiceShortName() != null && value.equalsIgnoreCase(o.getServiceShortName().replace(" ", ""))
                        || o.getServiceLocalName() != null && value.equalsIgnoreCase(o.getServiceLocalName().replace(" ", ""))
                        || o.getServiceEnName() != null && value.equalsIgnoreCase(o.getServiceEnName().replace(" ", ""))) {
                    re = Long.parseLong(o.getTypeId());
                }
            }
        }
        if (belong.equals("订舱口")) {
            ExtCompany company = new ExtCompany();
            company.setDeleteStatus(0);
            List<ExtCompany> list = extCompanyMapper.queryCompany(company);
            if (list != null && !list.isEmpty()) {
                for (ExtCompany o : list) {
                    if (o.getRoleSupplier().equals("1") && o.getCompanyShortName() != null && value.equalsIgnoreCase(o.getCompanyShortName().replace(" ", ""))
                            || o.getCompanyLocalName() != null && value.equalsIgnoreCase(o.getCompanyLocalName().replace(" ", ""))
                            || o.getCompanyEnName() != null && value.equalsIgnoreCase(o.getCompanyEnName().replace(" ", ""))) {
                        re = o.getCompanyId();
                    }
                }
            }
        }
        if (belong.equals("公司")) {
            List<ExtCompany> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "company");
            if (list == null) {
                this.company();
                list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "company");
            }
            if (list != null && !list.isEmpty()) {
                for (ExtCompany o : list) {
                    if (o.getCompanyShortName() != null && value.equalsIgnoreCase(o.getCompanyShortName().replace(" ", ""))
                            || o.getCompanyLocalName() != null && value.equalsIgnoreCase(o.getCompanyLocalName().replace(" ", ""))
                            || o.getCompanyEnName() != null && value.equalsIgnoreCase(o.getCompanyEnName().replace(" ", ""))
                            || o.getCompanyEnShortName() != null && value.equalsIgnoreCase(o.getCompanyEnShortName().replace(" ", ""))) {
                        re = o.getCompanyId();
                    }
                }
            }
        }

        return re;
    }

    /**
     * 将部门基础角色菜单权限缓存
     */
    public void basicRoleMenu() {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "basicRoleMenu");
        List<SysMenu> deptBasicMenus = sysMenuMapper.selectDeptBasicMenu();
        Map<Long, List<SysMenu>> groupByDeptBasicMenu = deptBasicMenus.stream().collect(Collectors.groupingBy(SysMenu::getDeptId));
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "basicRoleMenu", groupByDeptBasicMenu);

    }


    public void paymentTitle() {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "paymentTitle");

        List<RsPaymentTitle> rsPaymentTitles = rsPaymentTitleMapper.selectRsPaymentTitleList(new RsPaymentTitle());
        redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "paymentTitle", rsPaymentTitles);
    }
}
