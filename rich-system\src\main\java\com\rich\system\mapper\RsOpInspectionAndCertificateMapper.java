package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpInspectionAndCertificate;
import org.apache.ibatis.annotations.Mapper;

/**
 * 检验与证书服务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Mapper
public interface RsOpInspectionAndCertificateMapper {
    /**
     * 查询检验与证书服务
     *
     * @param inspectionAndCertificateId 检验与证书服务主键
     * @return 检验与证书服务
     */
    RsOpInspectionAndCertificate selectRsOpInspectionAndCertificateByInspectionAndCertificateId(Long inspectionAndCertificateId);

    /**
     * 查询检验与证书服务列表
     *
     * @param rsOpInspectionAndCertificate 检验与证书服务
     * @return 检验与证书服务集合
     */
    List<RsOpInspectionAndCertificate> selectRsOpInspectionAndCertificateList(RsOpInspectionAndCertificate rsOpInspectionAndCertificate);

    /**
     * 新增检验与证书服务
     *
     * @param rsOpInspectionAndCertificate 检验与证书服务
     * @return 结果
     */
    int insertRsOpInspectionAndCertificate(RsOpInspectionAndCertificate rsOpInspectionAndCertificate);

    /**
     * 修改检验与证书服务
     *
     * @param rsOpInspectionAndCertificate 检验与证书服务
     * @return 结果
     */
    int updateRsOpInspectionAndCertificate(RsOpInspectionAndCertificate rsOpInspectionAndCertificate);

    /**
     * 删除检验与证书服务
     *
     * @param inspectionAndCertificateId 检验与证书服务主键
     * @return 结果
     */
    int deleteRsOpInspectionAndCertificateByInspectionAndCertificateId(Long inspectionAndCertificateId);

    /**
     * 批量删除检验与证书服务
     *
     * @param inspectionAndCertificateIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsOpInspectionAndCertificateByInspectionAndCertificateIds(Long[] inspectionAndCertificateIds);

    RsOpInspectionAndCertificate selectRsOpInspectionAndCertificateByRctId(Long rctId);
}
