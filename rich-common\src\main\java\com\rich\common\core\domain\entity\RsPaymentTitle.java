package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 【请填写功能名称】对象 rs_payment_title
 *
 * <AUTHOR>
 * @date 2023-11-29
 */
public class RsPaymentTitle extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * GZRS 广州瑞旗
     * GZVS 广州外海
     * HKRS 香港瑞旗
     * PRVT 私人账户
     */
    private String code;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Long orderNum;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Long getOrder() {
        return orderNum;
    }

    public void setOrder(Long orderNum) {
        this.orderNum = orderNum;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("code", getCode())
                .append("remark", getRemark())
                .append("orderNum", getOrder())
                .toString();
    }
}
