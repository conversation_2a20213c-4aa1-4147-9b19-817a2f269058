package com.rich.system.mapper;

import java.util.List;
import java.util.Map;

import com.rich.common.core.domain.entity.MpInventory;
import org.apache.ibatis.annotations.Mapper;

/**
 * 库存Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Mapper
public interface MpInventoryMapper {
    /**
     * 查询库存
     *
     * @param inventoryId 库存主键
     * @return 库存
     */
    MpInventory selectMpInventoryByInventoryId(Long inventoryId);

    /**
     * 查询库存列表
     *
     * @param mpInventory 库存
     * @return 库存集合
     */
    List<MpInventory> selectMpInventoryList(MpInventory mpInventory);

    /**
     * 新增库存
     *
     * @param mpInventory 库存
     * @return 结果
     */
    int insertMpInventory(MpInventory mpInventory);

    /**
     * 修改库存
     *
     * @param mpInventory 库存
     * @return 结果
     */
    int updateMpInventory(MpInventory mpInventory);

    /**
     * 删除库存
     *
     * @param inventoryId 库存主键
     * @return 结果
     */
    int deleteMpInventoryByInventoryId(Long inventoryId);

    /**
     * 批量删除库存
     *
     * @param inventoryIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteMpInventoryByInventoryIds(Long[] inventoryIds);

    int getInboundNo();

    MpInventory selectMpInventoryByExpressNo(String expressNo);

    /**
     * 根据收货人代码查询库存
     *
     * @param consigneeCode 收货人代码
     * @return 库存信息
     */
    MpInventory selectMpInventoryByConsigneeCode(String consigneeCode);

    Map<String, Object> getStatusNumber(MpInventory mpInventory);

}
