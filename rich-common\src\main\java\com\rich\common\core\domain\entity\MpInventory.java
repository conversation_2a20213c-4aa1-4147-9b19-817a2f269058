package com.rich.common.core.domain.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 库存对象 mp_inventory
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
public class MpInventory {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private Long inventoryId;

    /** 库存标志：在库/ 已出库/ 被打包 */
    @Excel(name = "库存标志：在库/ 已出库/ 被打包")
    private String inventoryStatus;

    /**
     * 标记库存是否已存在(用于查重返回)
     */
    private transient boolean exist;

    /** 入仓流水号 */
    @Excel(name = "入仓流水号")
    private String inboundSerialNo;

    /** 入仓流水号拆分 */
    @Excel(name = "入仓流水号拆分")
    private String inboundSerialSplit;

    /** 入仓日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "入仓日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date inboundDate;

    /** 出仓单号 */
    @Excel(name = "出仓单号")
    private String outboundNo;

    /** 货代单号 */
    @Excel(name = "货代单号")
    private String forwarderNo;

    /** 最新计租日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最新计租日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date rentalSettlementDate;

    /** 出仓日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出仓日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date outboundDate;

    /** 客户代码 */
    @Excel(name = "客户代码")
    private String clientCode;

    /** 原始单号 */
    @Excel(name = "原始单号")
    private String subOrderNo;

    /** 供货商（工厂代码/ 简称） */
    @Excel(name = "供货商", readConverterExp = "工=厂代码/,简=称")
    private String supplier;

    /** 送货司机 */
    @Excel(name = "送货司机")
    private String driverInfo;

    /** sqd_唛头 */
    @Excel(name = "sqd_唛头")
    private String sqdShippingMark;

    /** 货物描述 */
    @Excel(name = "货物描述")
    private String cargoName;

    /** 总箱数 */
    @Excel(name = "总箱数")
    private Long totalBoxes;

    /** 包装类型（纸箱/ 木箱/ 托盘/ 吨袋等） */
    @Excel(name = "包装类型", readConverterExp = "纸=箱/,木=箱/,托=盘/,吨=袋等")
    private String packageType;

    /** 总毛重 */
    @Excel(name = "总毛重")
    private BigDecimal totalGrossWeight;

    /** 总体积 */
    @Excel(name = "总体积")
    private BigDecimal totalVolume;

    /** 破损标志 */
    @Excel(name = "破损标志")
    private String damageStatus;

    /** 仓库代码 */
    @Excel(name = "仓库代码")
    private String warehouseCode;

    /** 记录方式('快递', '详细', '总结') */
    @Excel(name = "记录方式('快递', '详细', '总结')")
    private String recordType;

    /** 入仓方式('入仓', '外置', '对装', '自提') */
    @Excel(name = "入仓方式('入仓', '外置', '对装', '自提')")
    private String inboundType;

    /** 货物性质('普货', '大件', '鲜活', '危品', '冷冻', '标记') */
    @Excel(name = "货物性质('普货', '大件', '鲜活', '危品', '冷冻', '标记')")
    private String cargoNature;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date createdAt;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String preOutboundFlag;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String outboundRequestFlag;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date sqdPlannedOutboundDate;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String contractType;

    /** 已收供应商总额 */
    @Excel(name = "已收供应商总额")
    private BigDecimal receivedSupplier;

    /**
     *
     */
    @Excel(name = "")
    private String consigneeName;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String consigneeTel;

    /**
     *
     */
    @Excel(name = "")
    private String clientName;

    /** 实际签收时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "实际签收时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date actualDeliveryTime;

    /**
     * 瑞旗签收流水号
     */
    @Excel(name = "瑞旗签收流水号")
    private String ruichiSignFlowNo;

    /**
     * 签收人
     */
    @Excel(name = "签收人")
    private String signerName;

    /**
     * 货物状态
     */
    @Excel(name = "货物状态")
    private String cargoStatus;

    /**
     * 出库单号
     */
    @Excel(name = "出库单号")
    private String outboundOrderNo;

    /**
     * 送货方式：客户自送/ 工厂直送/ 国内物流/ 快递公司
     */
    @Excel(name = "送货方式：客户自送/ 工厂直送/ 国内物流/ 快递公司")
    private String deliveryType;

    /**
     * 物流快递信息登记
     */
    @Excel(name = "物流快递信息登记")
    private String logisticsInfo;

    /**
     * 预录单号
     */
    @Excel(name = "预录单号")
    private String preEntrySerialNo;

    /**
     * 预计送达时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "预计送达时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date estimatedArrivalTime;

    /**
     * 实际送达时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "实际送达时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date actualArrivalTime;

    /**
     * 瑞旗签收流水号
     */
    @Excel(name = "瑞旗签收流水号")
    private String receiptSerialNo;

    /**
     * 签收人
     */
    @Excel(name = "签收人")
    private String receiptPerson;

    /**
     * 认领时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "认领时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date claimTime;

    /** 认领备注 */
    @Excel(name = "认领备注")
    private String claimRemark;

    /**
     * 入仓核实人
     */
    @Excel(name = "入仓核实人")
    private String verifyPerson;

    /**
     * 入仓核实时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入仓核实时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date verifyTime;

    /**
     * 货物状态：预录入/ 已入仓/ 被打包/ 已出仓
     */
    @Excel(name = "货物状态：预录入/ 已入仓/ 被打包/ 已出仓")
    private String goodsStatus;

    /**
     * 记录状态：未知客户/ 拟认领/ 待完善/ 已完善/ 已核实
     */
    @Excel(name = "记录状态：未知客户/ 拟认领/ 待完善/ 已完善/ 已核实")
    private String recordStatus;

    /**
     * 客户目的国公司名称
     */
    @Excel(name = "客户目的国公司名称")
    private String destinationCompanyName;

    /**
     * 客户目的国仓库地址
     */
    @Excel(name = "客户目的国仓库地址")
    private String destinationWarehouseAddress;

    /**
     * 客户目的国仓库联系方式
     */
    @Excel(name = "客户目的国仓库联系方式")
    private String destinationWarehouseContact;

    /**
     * 删除时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "删除时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date deleteTime;

    /**
     * 数据状态(-1:删除或不可用，0：正常)
     */
    @Excel(name = "数据状态(-1:删除或不可用，0：正常)")
    private Integer deleteStatus;

    private List<MpCargoDetails> mpCargoDetails;
    private String remarks;
    private String consigneeCode;
    private String clientRegion;
    private String specialMark;
    private String search;
    private String createBy;
    private Long packageTo;
    private String packageInto;
    private Long rsInventoryId;
    private Long createdBy;
    private String purchaseNo;
    private String createdUser;

    public String getCreatedUser() {
        return createdUser;
    }

    public void setCreatedUser(String createdUser) {
        this.createdUser = createdUser;
    }

    public String getPurchaseNo() {
        return purchaseNo;
    }

    public void setPurchaseNo(String purchaseNo) {
        this.purchaseNo = purchaseNo;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public Long getRsInventoryId() {
        return rsInventoryId;
    }

    public void setRsInventoryId(Long rsInventoryId) {
        this.rsInventoryId = rsInventoryId;
    }

    public String getPackageInto() {
        return packageInto;
    }

    public void setPackageInto(String packageInto) {
        this.packageInto = packageInto;
    }

    public Long getPackageTo() {
        return packageTo;
    }

    public void setPackageTo(Long packageTo) {
        this.packageTo = packageTo;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getSearch() {
        return search;
    }

    public void setSearch(String search) {
        this.search = search;
    }

    public String getSpecialMark() {
        return specialMark;
    }

    public void setSpecialMark(String specialMark) {
        this.specialMark = specialMark;
    }

    public String getClientRegion() {
        return clientRegion;
    }

    public void setClientRegion(String clientRegion) {
        this.clientRegion = clientRegion;
    }

    public String getConsigneeCode() {
        return consigneeCode;
    }

    public void setConsigneeCode(String consigneeCode) {
        this.consigneeCode = consigneeCode;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public List<MpCargoDetails> getMpCargoDetails() {
        return mpCargoDetails;
    }

    public void setMpCargoDetails(List<MpCargoDetails> mpCargoDetails) {
        this.mpCargoDetails = mpCargoDetails;
    }

    public void setInventoryId(Long inventoryId) {
        this.inventoryId = inventoryId;
    }

    public Long getInventoryId() {
        return inventoryId;
    }

    public void setInventoryStatus(String inventoryStatus) {
        this.inventoryStatus = inventoryStatus;
    }

    public String getInventoryStatus() {
        return inventoryStatus;
    }

    public void setInboundSerialNo(String inboundSerialNo) {
        this.inboundSerialNo = inboundSerialNo;
    }

    public String getInboundSerialNo() {
        return inboundSerialNo;
    }

    public void setInboundSerialSplit(String inboundSerialSplit) {
        this.inboundSerialSplit = inboundSerialSplit;
    }

    public String getInboundSerialSplit()
    {
        return inboundSerialSplit;
    }

    public void setInboundDate(Date inboundDate) {
        this.inboundDate = inboundDate;
    }

    public Date getInboundDate() {
        return inboundDate;
    }

    public void setOutboundNo(String outboundNo)
    {
        this.outboundNo = outboundNo;
    }

    public String getOutboundNo() {
        return outboundNo;
    }

    public void setForwarderNo(String forwarderNo) {
        this.forwarderNo = forwarderNo;
    }

    public String getForwarderNo() {
        return forwarderNo;
    }

    public void setRentalSettlementDate(Date rentalSettlementDate) {
        this.rentalSettlementDate = rentalSettlementDate;
    }

    public Date getRentalSettlementDate() {
        return rentalSettlementDate;
    }

    public Date getOutboundDate()
    {
        return outboundDate;
    }

    public void setOutboundDate(Date outboundDate)
    {
        this.outboundDate = outboundDate;
    }

    public void setClientCode(String clientCode) {
        this.clientCode = clientCode;
    }

    public String getClientCode() {
        return clientCode;
    }

    public void setSubOrderNo(String subOrderNo) {
        this.subOrderNo = subOrderNo;
    }

    public String getSubOrderNo()
    {
        return subOrderNo;
    }

    public void setSupplier(String supplier) {
        this.supplier = supplier;
    }

    public String getSupplier()
    {
        return supplier;
    }
    public void setDriverInfo(String driverInfo) {
        this.driverInfo = driverInfo;
    }

    public String getDriverInfo() {
        return driverInfo;
    }

    public String getSqdShippingMark()
    {
        return sqdShippingMark;
    }

    public void setSqdShippingMark(String sqdShippingMark)
    {
        this.sqdShippingMark = sqdShippingMark;
    }

    public void setCargoName(String cargoName) {
        this.cargoName = cargoName;
    }

    public String getCargoName() {
        return cargoName;
    }

    public void setTotalBoxes(Long totalBoxes)
    {
        this.totalBoxes = totalBoxes;
    }

    public Long getTotalBoxes() {
        return totalBoxes;
    }

    public void setPackageType(String packageType) {
        this.packageType = packageType;
    }

    public String getPackageType() {
        return packageType;
    }

    public void setTotalGrossWeight(BigDecimal totalGrossWeight) {
        this.totalGrossWeight = totalGrossWeight;
    }

    public BigDecimal getTotalGrossWeight() {
        return totalGrossWeight;
    }

    public void setTotalVolume(BigDecimal totalVolume)
    {
        this.totalVolume = totalVolume;
    }

    public BigDecimal getTotalVolume() {
        return totalVolume;
    }
    public void setDamageStatus(String damageStatus) {
        this.damageStatus = damageStatus;
    }

    public String getDamageStatus() {
        return damageStatus;
    }

    public String getWarehouseCode()
    {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode)
    {
        this.warehouseCode = warehouseCode;
    }

    public void setRecordType(String recordType) {
        this.recordType = recordType;
    }

    public String getRecordType()
    {
        return recordType;
    }

    public void setInboundType(String inboundType) {
        this.inboundType = inboundType;
    }

    public String getInboundType() {
        return inboundType;
    }

    public String getCargoNature()
    {
        return cargoNature;
    }

    public void setCargoNature(String cargoNature)
    {
        this.cargoNature = cargoNature;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setPreOutboundFlag(String preOutboundFlag) {
        this.preOutboundFlag = preOutboundFlag;
    }

    public String getPreOutboundFlag() {
        return preOutboundFlag;
    }

    public void setOutboundRequestFlag(String outboundRequestFlag)
    {
        this.outboundRequestFlag = outboundRequestFlag;
    }

    public String getOutboundRequestFlag() {
        return outboundRequestFlag;
    }

    public void setSqdPlannedOutboundDate(Date sqdPlannedOutboundDate) {
        this.sqdPlannedOutboundDate = sqdPlannedOutboundDate;
    }

    public Date getSqdPlannedOutboundDate() {
        return sqdPlannedOutboundDate;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public String getContractType() {
        return contractType;
    }
    public void setReceivedSupplier(BigDecimal receivedSupplier) {
        this.receivedSupplier = receivedSupplier;
    }

    public BigDecimal getReceivedSupplier() {
        return receivedSupplier;
    }

    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName;
    }

    public String getConsigneeName()
    {
        return consigneeName;
    }

    public void setConsigneeTel(String consigneeTel) {
        this.consigneeTel = consigneeTel;
    }

    public String getConsigneeTel()
    {
        return consigneeTel;
    }
    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getClientName() {
        return clientName;
    }

    public void setActualDeliveryTime(Date actualDeliveryTime) {
        this.actualDeliveryTime = actualDeliveryTime;
    }

    public Date getActualDeliveryTime() {
        return actualDeliveryTime;
    }

    public String getRuichiSignFlowNo() {
        return ruichiSignFlowNo;
    }

    public void setRuichiSignFlowNo(String ruichiSignFlowNo) {
        this.ruichiSignFlowNo = ruichiSignFlowNo;
    }

    public String getSignerName() {
        return signerName;
    }

    public void setSignerName(String signerName)
    {
        this.signerName = signerName;
    }

    public String getCargoStatus() {
        return cargoStatus;
    }

    public void setCargoStatus(String cargoStatus) {
        this.cargoStatus = cargoStatus;
    }

    public String getOutboundOrderNo() {
        return outboundOrderNo;
    }

    public void setOutboundOrderNo(String outboundOrderNo) {
        this.outboundOrderNo = outboundOrderNo;
    }

    public String getDeliveryType() {
        return deliveryType;
    }

    public void setDeliveryType(String deliveryType) {
        this.deliveryType = deliveryType;
    }

    public String getLogisticsInfo() {
        return logisticsInfo;
    }

    public void setLogisticsInfo(String logisticsInfo) {
        this.logisticsInfo = logisticsInfo;
    }

    public String getPreEntrySerialNo() {
        return preEntrySerialNo;
    }

    public void setPreEntrySerialNo(String preEntrySerialNo) {
        this.preEntrySerialNo = preEntrySerialNo;
    }

    public Date getEstimatedArrivalTime() {
        return estimatedArrivalTime;
    }

    public void setEstimatedArrivalTime(Date estimatedArrivalTime) {
        this.estimatedArrivalTime = estimatedArrivalTime;
    }

    public Date getActualArrivalTime() {
        return actualArrivalTime;
    }

    public void setActualArrivalTime(Date actualArrivalTime) {
        this.actualArrivalTime = actualArrivalTime;
    }

    public String getReceiptSerialNo() {
        return receiptSerialNo;
    }

    public void setReceiptSerialNo(String receiptSerialNo) {
        this.receiptSerialNo = receiptSerialNo;
    }

    public String getReceiptPerson() {
        return receiptPerson;
    }

    public void setReceiptPerson(String receiptPerson) {
        this.receiptPerson = receiptPerson;
    }

    public Date getClaimTime() {
        return claimTime;
    }

    public void setClaimTime(Date claimTime) {
        this.claimTime = claimTime;
    }

    public String getClaimRemark() {
        return claimRemark;
    }

    public void setClaimRemark(String claimRemark) {
        this.claimRemark = claimRemark;
    }

    public String getVerifyPerson() {
        return verifyPerson;
    }

    public void setVerifyPerson(String verifyPerson) {
        this.verifyPerson = verifyPerson;
    }

    public Date getVerifyTime() {
        return verifyTime;
    }

    public void setVerifyTime(Date verifyTime) {
        this.verifyTime = verifyTime;
    }

    public String getGoodsStatus() {
        return goodsStatus;
    }

    public void setGoodsStatus(String goodsStatus) {
        this.goodsStatus = goodsStatus;
    }

    public String getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(String recordStatus) {
        this.recordStatus = recordStatus;
    }

    public String getDestinationCompanyName() {
        return destinationCompanyName;
    }

    public void setDestinationCompanyName(String destinationCompanyName) {
        this.destinationCompanyName = destinationCompanyName;
    }

    public String getDestinationWarehouseAddress() {
        return destinationWarehouseAddress;
    }

    public void setDestinationWarehouseAddress(String destinationWarehouseAddress) {
        this.destinationWarehouseAddress = destinationWarehouseAddress;
    }

    public String getDestinationWarehouseContact() {
        return destinationWarehouseContact;
    }

    public void setDestinationWarehouseContact(String destinationWarehouseContact) {
        this.destinationWarehouseContact = destinationWarehouseContact;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Integer getDeleteStatus()
    {
        return deleteStatus;
    }

    public void setDeleteStatus(Integer deleteStatus)
    {
        this.deleteStatus = deleteStatus;
    }

    public boolean isExist() {
        return exist;
    }

    public void setExist(boolean exist) {
        this.exist = exist;
    }


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("inventoryId", getInventoryId())
                .append("inventoryStatus", getInventoryStatus())
                .append("inboundSerialNo", getInboundSerialNo())
                .append("inboundSerialSplit", getInboundSerialSplit())
                .append("inboundDate", getInboundDate())
                .append("outboundNo", getOutboundNo())
                .append("forwarderNo", getForwarderNo())
                .append("rentalSettlementDate", getRentalSettlementDate())
                .append("outboundDate", getOutboundDate())
                .append("clientCode", getClientCode())
                .append("subOrderNo", getSubOrderNo())
                .append("supplier", getSupplier())
                .append("driverInfo", getDriverInfo())
                .append("sqdShippingMark", getSqdShippingMark())
                .append("cargoName", getCargoName())
                .append("totalBoxes", getTotalBoxes())
                .append("packageType", getPackageType())
                .append("totalGrossWeight", getTotalGrossWeight())
                .append("totalVolume", getTotalVolume())
                .append("damageStatus", getDamageStatus())
                .append("warehouseCode", getWarehouseCode())
                .append("recordType", getRecordType())
                .append("inboundType", getInboundType())
                .append("cargoNature", getCargoNature())
                .append("createdAt", getCreatedAt())
                .append("preOutboundFlag", getPreOutboundFlag())
                .append("outboundRequestFlag", getOutboundRequestFlag())
                .append("sqdPlannedOutboundDate", getSqdPlannedOutboundDate())
                .append("contractType", getContractType())
                .append("receivedSupplier", getReceivedSupplier())
                .append("consigneeName", getConsigneeName())
                .append("consigneeTel", getConsigneeTel())
                .append("clientName", getClientName())
                .append("actualDeliveryTime", getActualDeliveryTime())
                .append("ruichiSignFlowNo", getRuichiSignFlowNo())
                .append("signerName", getSignerName())
                .append("cargoStatus", getCargoStatus())
                .append("outboundOrderNo", getOutboundOrderNo())
                .append("deliveryType", getDeliveryType())
                .append("logisticsInfo", getLogisticsInfo())
                .append("preEntrySerialNo", getPreEntrySerialNo())
                .append("estimatedArrivalTime", getEstimatedArrivalTime())
                .append("actualArrivalTime", getActualArrivalTime())
                .append("receiptSerialNo", getReceiptSerialNo())
                .append("receiptPerson", getReceiptPerson())
                .append("claimTime", getClaimTime())
                .append("claimRemark", getClaimRemark())
                .append("verifyPerson", getVerifyPerson())
                .append("verifyTime", getVerifyTime())
                .append("goodsStatus", getGoodsStatus())
                .append("recordStatus", getRecordStatus())
                .append("destinationCompanyName", getDestinationCompanyName())
                .append("destinationWarehouseAddress", getDestinationWarehouseAddress())
                .append("destinationWarehouseContact", getDestinationWarehouseContact())
                .append("createBy", getCreateBy())
                .append("deleteTime", getDeleteTime())
                .append("deleteStatus", getDeleteStatus())
                .append("exist", isExist())
                .toString();
    }
}
