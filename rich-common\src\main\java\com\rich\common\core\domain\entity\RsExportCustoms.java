package com.rich.common.core.domain.entity;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 出口报关对象 rs_export_customs
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
public class RsExportCustoms extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long exportCustomsId;

    /**
     * 所属服务实例id ,
     */
    @Excel(name = "所属服务实例id ,")
    private Long serviceId;

    /**
     * 所属服务类型id ,
     */
    @Excel(name = "所属服务类型id ,")
    private Long sqdServiceTypeId;

    /**
     * 所属操作单号 ,
     */
    @Excel(name = "所属操作单号 ,")
    private String sqdRctNo;

    /**
     * 报关时间 ,（启运港报关时间）
     */
    @Excel(name = "报关时间 ,", readConverterExp = "启=运港报关时间")
    private Date cvDeclaringTime;

    /**
     * 截VGM ,（时间）
     */
    @Excel(name = "截VGM ,", readConverterExp = "时=间")
    private Date vgmClosingTime;

    // 服务实例
    private RsServiceInstances rsServiceInstances;

    private List<RsCharge> rsChargeList;

    private List<RsDoc> rsDocList;

    public List<RsCharge> getRsChargeList() {
        return rsChargeList;
    }

    public void setRsChargeList(List<RsCharge> rsChargeList) {
        this.rsChargeList = rsChargeList;
    }

    public List<RsDoc> getRsDocList() {
        return rsDocList;
    }

    public void setRsDocList(List<RsDoc> rsDocList) {
        this.rsDocList = rsDocList;
    }

    public RsServiceInstances getRsServiceInstances() {
        return rsServiceInstances;
    }

    public void setRsServiceInstances(RsServiceInstances rsServiceInstances) {
        this.rsServiceInstances = rsServiceInstances;
    }

    public Long getExportCustomsId() {
        return exportCustomsId;
    }

    public void setExportCustomsId(Long exportCustomsId) {
        this.exportCustomsId = exportCustomsId;
    }

    public Long getServiceId() {
        return serviceId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    public Long getSqdServiceTypeId() {
        return sqdServiceTypeId;
    }

    public void setSqdServiceTypeId(Long sqdServiceTypeId) {
        this.sqdServiceTypeId = sqdServiceTypeId;
    }

    public String getSqdRctNo() {
        return sqdRctNo;
    }

    public void setSqdRctNo(String sqdRctNo) {
        this.sqdRctNo = sqdRctNo;
    }

    public Date getCvDeclaringTime() {
        return cvDeclaringTime;
    }

    public void setCvDeclaringTime(Date cvDeclaringTime) {
        this.cvDeclaringTime = cvDeclaringTime;
    }

    public Date getVgmClosingTime() {
        return vgmClosingTime;
    }

    public void setVgmClosingTime(Date vgmClosingTime) {
        this.vgmClosingTime = vgmClosingTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("exportCustomsId", getExportCustomsId())
                .append("serviceId", getServiceId())
                .append("sqdServiceTypeId", getSqdServiceTypeId())
                .append("sqdRctNo", getSqdRctNo())
                .append("cvDeclaringTime", getCvDeclaringTime())
                .append("vgmClosingTime", getVgmClosingTime())
                .toString();
    }
}
