package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsCommunication;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 交流Mapper接口
 *
 * <AUTHOR>
 * @date 2022-10-18
 */
@Mapper
public interface RsCommunicationMapper {
    /**
     * 查询交流
     *
     * @param communicationId 交流主键
     * @return 交流
     */
    RsCommunication selectRsCommunicationByCommunicationId(Long communicationId);

    /**
     * 查询交流列表
     *
     * @param rsCommunication 交流
     * @return 交流集合
     */
    List<RsCommunication> selectRsCommunicationList(RsCommunication rsCommunication);

    /**
     * 新增交流
     *
     * @param rsCommunication 交流
     * @return 结果
     */
    int insertRsCommunication(RsCommunication rsCommunication);

    /**
     * 修改交流
     *
     * @param rsCommunication 交流
     * @return 结果
     */
    int updateRsCommunication(RsCommunication rsCommunication);

    /**
     * 删除交流
     *
     * @param communicationId 交流主键
     * @return 结果
     */
    int deleteRsCommunicationByCommunicationId(Long communicationId);

    /**
     * 批量删除交流
     *
     * @param communicationIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsCommunicationByCommunicationIds(Long[] communicationIds);
}
