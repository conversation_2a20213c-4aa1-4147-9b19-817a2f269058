package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsOpInsurance;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsOpInsuranceService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 保险服务Controller
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@RestController
@RequestMapping("/system/opinsurance")
public class RsOpInsuranceController extends BaseController {
    @Autowired
    private RsOpInsuranceService rsOpInsuranceService;

    /**
     * 查询保险服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opinsurance:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsOpInsurance rsOpInsurance) {
        startPage();
        List<RsOpInsurance> list = rsOpInsuranceService.selectRsOpInsuranceList(rsOpInsurance);
        return getDataTable(list);
    }

    /**
     * 导出保险服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:opinsurance:export')")
    @Log(title = "保险服务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsOpInsurance rsOpInsurance) {
        List<RsOpInsurance> list = rsOpInsuranceService.selectRsOpInsuranceList(rsOpInsurance);
        ExcelUtil<RsOpInsurance> util = new ExcelUtil<RsOpInsurance>(RsOpInsurance.class);
        util.exportExcel(response, list, "保险服务数据");
    }

    /**
     * 获取保险服务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:opinsurance:query')")
    @GetMapping(value = "/{insuranceId}")
    public AjaxResult getInfo(@PathVariable("insuranceId") Long insuranceId) {
        return AjaxResult.success(rsOpInsuranceService.selectRsOpInsuranceByInsuranceId(insuranceId));
    }

    /**
     * 新增保险服务
     */
    @PreAuthorize("@ss.hasPermi('system:opinsurance:add')")
    @Log(title = "保险服务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsOpInsurance rsOpInsurance) {
        return toAjax(rsOpInsuranceService.insertRsOpInsurance(rsOpInsurance));
    }

    /**
     * 修改保险服务
     */
    @PreAuthorize("@ss.hasPermi('system:opinsurance:edit')")
    @Log(title = "保险服务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsOpInsurance rsOpInsurance) {
        return toAjax(rsOpInsuranceService.updateRsOpInsurance(rsOpInsurance));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:opinsurance:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsOpInsurance rsOpInsurance) {
        rsOpInsurance.setUpdateBy(getUserId());
        return toAjax(rsOpInsuranceService.changeStatus(rsOpInsurance));
    }

    /**
     * 删除保险服务
     */
    @PreAuthorize("@ss.hasPermi('system:opinsurance:remove')")
    @Log(title = "保险服务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{insuranceIds}")
    public AjaxResult remove(@PathVariable Long[] insuranceIds) {
        return toAjax(rsOpInsuranceService.deleteRsOpInsuranceByInsuranceIds(insuranceIds));
    }
}
