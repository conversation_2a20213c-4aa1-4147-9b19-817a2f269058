package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsCharacteristics;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 物流注意事项Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-30
 */
@Mapper
public interface RsCharacteristicsMapper {
    /**
     * 查询物流注意事项
     *
     * @param characteristicsId 物流注意事项主键
     * @return 物流注意事项
     */
    RsCharacteristics selectRsCharacteristicsByCharacteristicsId(Long characteristicsId);

    /**
     * 查询物流注意事项列表
     *
     * @param rsCharacteristics 物流注意事项
     * @return 物流注意事项集合
     */
    List<RsCharacteristics> selectRsCharacteristicsList(RsCharacteristics rsCharacteristics);

    /**
     * 新增物流注意事项
     *
     * @param rsCharacteristics 物流注意事项
     * @return 结果
     */
    int insertRsCharacteristics(RsCharacteristics rsCharacteristics);

    /**
     * 修改物流注意事项
     *
     * @param rsCharacteristics 物流注意事项
     * @return 结果
     */
    int updateRsCharacteristics(RsCharacteristics rsCharacteristics);

    /**
     * 删除物流注意事项
     *
     * @param characteristicsId 物流注意事项主键
     * @return 结果
     */
    int deleteRsCharacteristicsByCharacteristicsId(Long characteristicsId);

    /**
     * 批量删除物流注意事项
     *
     * @param characteristicsIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsCharacteristicsByCharacteristicsIds(Long[] characteristicsIds);
}
