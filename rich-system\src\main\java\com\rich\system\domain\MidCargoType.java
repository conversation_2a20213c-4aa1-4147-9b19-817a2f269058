package com.rich.system.domain;

import com.rich.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 mid_company_cargo_type
 *
 * <AUTHOR>
 * @date 2022-09-07
 */
public class MidCargoType extends BaseEntity {
    private static final long serialVersionUID = 1L;


    /**
     * 货柜类型ID
     */
    private Long cargoTypeId;

    private Long belongId;

    private String belongTo;

    public Long getBelongId() {
        return belongId;
    }

    public void setBelongId(Long belongId) {
        this.belongId = belongId;
    }

    public String getBelongTo() {
        return belongTo;
    }

    public void setBelongTo(String belongTo) {
        this.belongTo = belongTo;
    }

    public Long getCargoTypeId() {
        return cargoTypeId;
    }

    public void setCargoTypeId(Long cargoTypeId) {
        this.cargoTypeId = cargoTypeId;
    }

}
