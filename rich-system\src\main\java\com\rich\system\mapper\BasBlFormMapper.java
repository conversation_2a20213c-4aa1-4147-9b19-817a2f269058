package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.BasBlForm;
import org.apache.ibatis.annotations.Mapper;

/**
 * 提单形式Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@Mapper
public interface BasBlFormMapper {
    /**
     * 查询提单形式
     *
     * @param blFormCode 提单形式主键
     * @return 提单形式
     */
    BasBlForm selectBasBlFormByBlFormCode(String blFormCode);

    /**
     * 查询提单形式列表
     *
     * @param basBlForm 提单形式
     * @return 提单形式集合
     */
    List<BasBlForm> selectBasBlFormList(BasBlForm basBlForm);

    /**
     * 新增提单形式
     *
     * @param basBlForm 提单形式
     * @return 结果
     */
    int insertBasBlForm(BasBlForm basBlForm);

    /**
     * 修改提单形式
     *
     * @param basBlForm 提单形式
     * @return 结果
     */
    int updateBasBlForm(BasBlForm basBlForm);

    /**
     * 删除提单形式
     *
     * @param blFormCode 提单形式主键
     * @return 结果
     */
    int deleteBasBlFormByBlFormCode(String blFormCode);

    /**
     * 批量删除提单形式
     *
     * @param blFormCodes 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasBlFormByBlFormCodes(String[] blFormCodes);
}
