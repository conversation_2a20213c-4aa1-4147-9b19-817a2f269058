package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasPaymentChannels;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 收汇方式Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@Mapper
public interface BasPaymentChannelsMapper {
    /**
     * 查询收汇方式
     *
     * @param paymentChannelsId 收汇方式主键
     * @return 收汇方式
     */
    BasPaymentChannels selectBasPaymentChannelsByPaymentChannelsId(Long paymentChannelsId);

    /**
     * 查询收汇方式列表
     *
     * @param basPaymentChannels 收汇方式
     * @return 收汇方式集合
     */
    List<BasPaymentChannels> selectBasPaymentChannelsList(BasPaymentChannels basPaymentChannels);

    /**
     * 新增收汇方式
     *
     * @param basPaymentChannels 收汇方式
     * @return 结果
     */
    int insertBasPaymentChannels(BasPaymentChannels basPaymentChannels);

    /**
     * 修改收汇方式
     *
     * @param basPaymentChannels 收汇方式
     * @return 结果
     */
    int updateBasPaymentChannels(BasPaymentChannels basPaymentChannels);

    /**
     * 删除收汇方式
     *
     * @param paymentChannelsId 收汇方式主键
     * @return 结果
     */
    int deleteBasPaymentChannelsByPaymentChannelsId(Long paymentChannelsId);

    /**
     * 批量删除收汇方式
     *
     * @param paymentChannelsIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasPaymentChannelsByPaymentChannelsIds(Long[] paymentChannelsIds);
}
