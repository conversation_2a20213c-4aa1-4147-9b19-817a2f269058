package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.BasIssue;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.system.mapper.BasIssueMapper;
import com.rich.system.service.BasIssueService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 问题Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-10-18
 */
@Service

public class BasIssueServiceImpl implements BasIssueService {

    @Autowired
    private  BasIssueMapper basIssueMapper;

    /**
     * 查询问题
     *
     * @param issueId 问题主键
     * @return 问题
     */
    @Override
    public BasIssue selectBasIssueByIssueId(Long issueId) {
        return basIssueMapper.selectBasIssueByIssueId(issueId);
    }

    /**
     * 查询问题列表
     *
     * @param basIssue 问题
     * @return 问题
     */
    @Override
    public List<BasIssue> selectBasIssueList(BasIssue basIssue) {
        return basIssueMapper.selectBasIssueList(basIssue);
    }

    /**
     * 新增问题
     *
     * @param basIssue 问题
     * @return 结果
     */
    @Override
    public int insertBasIssue(BasIssue basIssue) {
        basIssue.setCreateTime(DateUtils.getNowDate());
        basIssue.setCreateBy(SecurityUtils.getUserId());
        return basIssueMapper.insertBasIssue(basIssue);
    }

    /**
     * 修改问题
     *
     * @param basIssue 问题
     * @return 结果
     */
    @Override
    public int updateBasIssue(BasIssue basIssue) {
        basIssue.setUpdateTime(DateUtils.getNowDate());
        basIssue.setUpdateBy(SecurityUtils.getUserId());
        return basIssueMapper.updateBasIssue(basIssue);
    }

    /**
     * 批量删除问题
     *
     * @param issueIds 需要删除的问题主键
     * @return 结果
     */
    @Override
    public int deleteBasIssueByIssueIds(Long[] issueIds) {
        return basIssueMapper.deleteBasIssueByIssueIds(issueIds);
    }

    /**
     * 删除问题信息
     *
     * @param issueId 问题主键
     * @return 结果
     */
    @Override
    public int deleteBasIssueByIssueId(Long issueId) {
        return basIssueMapper.deleteBasIssueByIssueId(issueId);
    }

    @Override
    public int changeStatus(BasIssue basIssue) {
        return basIssueMapper.updateBasIssue(basIssue);
    }
}
