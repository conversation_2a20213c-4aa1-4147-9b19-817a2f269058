package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpInsurance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsOpInsuranceMapper;
import com.rich.system.service.RsOpInsuranceService;

/**
 * 保险服务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Service
public class RsOpInsuranceServiceImpl implements RsOpInsuranceService {
    @Autowired
    private RsOpInsuranceMapper rsOpInsuranceMapper;

    /**
     * 查询保险服务
     *
     * @param insuranceId 保险服务主键
     * @return 保险服务
     */
    @Override
    public RsOpInsurance selectRsOpInsuranceByInsuranceId(Long insuranceId) {
        return rsOpInsuranceMapper.selectRsOpInsuranceByInsuranceId(insuranceId);
    }

    /**
     * 查询保险服务列表
     *
     * @param rsOpInsurance 保险服务
     * @return 保险服务
     */
    @Override
    public List<RsOpInsurance> selectRsOpInsuranceList(RsOpInsurance rsOpInsurance) {
        return rsOpInsuranceMapper.selectRsOpInsuranceList(rsOpInsurance);
    }

    /**
     * 新增保险服务
     *
     * @param rsOpInsurance 保险服务
     * @return 结果
     */
    @Override
    public int insertRsOpInsurance(RsOpInsurance rsOpInsurance) {
        return rsOpInsuranceMapper.insertRsOpInsurance(rsOpInsurance);
    }

    /**
     * 修改保险服务
     *
     * @param rsOpInsurance 保险服务
     * @return 结果
     */
    @Override
    public int updateRsOpInsurance(RsOpInsurance rsOpInsurance) {
        return rsOpInsuranceMapper.updateRsOpInsurance(rsOpInsurance);
    }

    /**
     * 修改保险服务状态
     *
     * @param rsOpInsurance 保险服务
     * @return 保险服务
     */
    @Override
    public int changeStatus(RsOpInsurance rsOpInsurance) {
        return rsOpInsuranceMapper.updateRsOpInsurance(rsOpInsurance);
    }

    /**
     * 批量删除保险服务
     *
     * @param insuranceIds 需要删除的保险服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpInsuranceByInsuranceIds(Long[] insuranceIds) {
        return rsOpInsuranceMapper.deleteRsOpInsuranceByInsuranceIds(insuranceIds);
    }

    /**
     * 删除保险服务信息
     *
     * @param insuranceId 保险服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpInsuranceByInsuranceId(Long insuranceId) {
        return rsOpInsuranceMapper.deleteRsOpInsuranceByInsuranceId(insuranceId);
    }
}
