package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.RsCargoDetails;
import com.rich.common.core.domain.entity.RsInventory;
import com.rich.common.core.domain.entity.RsOutboundRecord;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.RsOutboundRecordService;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 出仓记录Controller
 *
 * <AUTHOR>
 * @date 2024-11-11
 */
@RestController
@RequestMapping("/system/outboundrecord")
public class RsOutboundRecordController extends BaseController {
    @Autowired
    private RsOutboundRecordService rsOutboundRecordService;

    /**
     * 查询已出库记录 Outbound Records
     */
    @PreAuthorize("@ss.hasPermi('system:outboundrecord:list')")
    @GetMapping("/outboundrecords")
    public TableDataInfo outboundRecords(RsOutboundRecord rsOutboundRecord) {
        rsOutboundRecord.setPreOutboundFlag("0");
        startPage();
        List<RsOutboundRecord> list = rsOutboundRecordService.selectRsOutboundRecordList(rsOutboundRecord);
        return getDataTable(list);
    }

    /**
     * 查询出仓记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:outboundrecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsOutboundRecord rsOutboundRecord) {
        rsOutboundRecord.setPreOutboundFlag("1");
        startPage();
        List<RsOutboundRecord> list = rsOutboundRecordService.selectRsOutboundRecordList(rsOutboundRecord);
        return getDataTable(list);
    }

    /**
     * 查询仓租结算记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:outboundrecord:list')")
    @GetMapping("/listRental")
    public TableDataInfo listRental(RsOutboundRecord rsOutboundRecord) {
        startPage();
        List<RsOutboundRecord> list = rsOutboundRecordService.selectRentalRecordList(rsOutboundRecord);
        return getDataTable(list);
    }

    /**
     * 导出出仓记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:outboundrecord:export')")
    @Log(title = "出仓记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsOutboundRecord rsOutboundRecord) {
        List<RsOutboundRecord> list = rsOutboundRecordService.selectRsOutboundRecordList(rsOutboundRecord);
        ExcelUtil<RsOutboundRecord> util = new ExcelUtil<RsOutboundRecord>(RsOutboundRecord.class);
        util.exportExcel(response, list, "出仓记录数据");
    }

    /**
     * 获取出仓记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:outboundrecord:query')")
    @GetMapping(value = "/{outboundRecordId}")
    public AjaxResult getInfo(@PathVariable("outboundRecordId") Long outboundRecordId) {
        return AjaxResult.success(rsOutboundRecordService.selectRsOutboundRecordByOutboundRecordId(outboundRecordId));
    }

    /**
     * 获取已出仓记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:outboundrecord:query')")
    @GetMapping(value = "/outboundrecords/{outboundRecordId}")
    public AjaxResult getOutboundRecords(@PathVariable("outboundRecordId") Long outboundRecordId) {
        return AjaxResult.success(rsOutboundRecordService.selectRsOutboundRecordsByOutboundRecordId(outboundRecordId));
    }

    /**
     * 获取已结算仓租记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:outboundrecord:query')")
    @GetMapping(value = "/rentals/{outboundRecordId}")
    public AjaxResult getRentals(@PathVariable("outboundRecordId") Long outboundRecordId) {
        return AjaxResult.success(rsOutboundRecordService.selectRentalsByOutboundRecordId(outboundRecordId));
    }

    /**
     * 新增出仓记录
     */
    @PreAuthorize("@ss.hasPermi('system:outboundrecord:add')")
    @Log(title = "出仓记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsOutboundRecord rsOutboundRecord) {
        return AjaxResult.success(rsOutboundRecordService.insertRsOutboundRecord(rsOutboundRecord));
    }

    /**
     * 修改出仓记录
     */
    @PreAuthorize("@ss.hasPermi('system:outboundrecord:edit')")
    @Log(title = "出仓记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsOutboundRecord rsOutboundRecord) {
        return AjaxResult.success(rsOutboundRecordService.updateRsOutboundRecord(rsOutboundRecord));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:outboundrecord:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsOutboundRecord rsOutboundRecord) {
        rsOutboundRecord.setUpdateBy(getUserId());
        return toAjax(rsOutboundRecordService.changeStatus(rsOutboundRecord));
    }

    /**
     * 删除出仓记录
     */
    @PreAuthorize("@ss.hasPermi('system:outboundrecord:remove')")
    @Log(title = "出仓记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{outboundRecordIds}")
    public AjaxResult remove(@PathVariable Long[] outboundRecordIds) {
        return toAjax(rsOutboundRecordService.deleteRsOutboundRecordByOutboundRecordIds(outboundRecordIds));
    }

    /**
     * 生成出仓单
     *
     * @return
     */
    @PutMapping("/outboundBill")
    public ResponseEntity<Object> downloadExcel(@RequestBody RsOutboundRecord rsOutboundRecord) throws IOException {

        // 从 resources 目录加载模板文件
        InputStream templateStream =
                RsOutboundRecordController.class.getResourceAsStream("/template/outbound.xlsx");
        if (templateStream == null) {
            throw new FileNotFoundException("未找到模板文件");
        }
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        // 使用正确的 Workbook 类型加载模板
        Workbook workbook = WorkbookFactory.create(templateStream);

        rsOutboundRecordService.writeData(rsOutboundRecord, workbook);

        // 将工作簿写入输出流
        workbook.write(outputStream);

        // 关闭工作簿
        workbook.close();

        // 返回 Excel 文件作为响应
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=GeneratedTemplate.xlsm")
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(outputStream.toByteArray());
    }
}