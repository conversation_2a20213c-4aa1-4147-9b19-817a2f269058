package com.rich.system.service.impl;

import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.RsPsaRct;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.RedisIdGeneratorService;
import com.rich.system.mapper.RsPsaRctMapper;
import com.rich.system.service.BasDistLocationService;
import com.rich.system.service.RsPsaRctService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

/**
 * 商务订舱Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-01
 */
@Service
public class RsPsaRctServiceImpl implements RsPsaRctService {
    @Autowired
    private RsPsaRctMapper rsPsaRctMapper;

    @Resource
    private RedisIdGeneratorService redisIdGeneratorService;


    /**
     * 查询商务订舱
     *
     * @param psaRctId 商务订舱主键
     * @return 商务订舱
     */
    @Override
    public RsPsaRct selectRsPsaRctByPsaRctId(Long psaRctId) {
        return rsPsaRctMapper.selectRsPsaRctByPsaRctId(psaRctId);
    }

    /**
     * 查询商务订舱列表
     *
     * @param rsPsaRct 商务订舱
     * @return 商务订舱
     */
    @Override
    public List<RsPsaRct> selectRsPsaRctList(RsPsaRct rsPsaRct) {
        return rsPsaRctMapper.selectRsPsaRctList(rsPsaRct);
    }

    /**
     * 新增商务订舱
     *
     * @param rsPsaRct 商务订舱
     * @return 结果
     */
    @Override
    public RsPsaRct insertRsPsaRct(RsPsaRct rsPsaRct) {
        String psaNo = redisIdGeneratorService.generateUniqueId("psa_no");
        String date = DateUtils.dateTime();
        rsPsaRct.setPsaNo("PSA" + date.substring(2, 6) + psaNo);
        rsPsaRctMapper.insertRsPsaRct(rsPsaRct);
        return rsPsaRct;
    }

    /**
     * 修改商务订舱
     *
     * @param rsPsaRct 商务订舱
     * @return 结果
     */
    @Override
    public int updateRsPsaRct(RsPsaRct rsPsaRct) {
        return rsPsaRctMapper.updateRsPsaRct(rsPsaRct);
    }

    /**
     * 修改商务订舱状态
     *
     * @param rsPsaRct 商务订舱
     * @return 商务订舱
     */
    @Override
    public int changeStatus(RsPsaRct rsPsaRct) {
        return rsPsaRctMapper.updateRsPsaRct(rsPsaRct);
    }

    /**
     * 批量删除商务订舱
     *
     * @param psaRctIds 需要删除的商务订舱主键
     * @return 结果
     */
    @Override
    public int deleteRsPsaRctByPsaRctIds(Long[] psaRctIds) {
        return rsPsaRctMapper.deleteRsPsaRctByPsaRctIds(psaRctIds);
    }

    /**
     * 删除商务订舱信息
     *
     * @param psaRctId 商务订舱主键
     * @return 结果
     */
    @Override
    public int deleteRsPsaRctByPsaRctId(Long psaRctId) {
        return rsPsaRctMapper.deleteRsPsaRctByPsaRctId(psaRctId);
    }
}
