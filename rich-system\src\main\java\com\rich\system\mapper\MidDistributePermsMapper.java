package com.rich.system.mapper;

import com.rich.system.domain.MidDistributePerms;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-03-20
 */
@Mapper
public interface MidDistributePermsMapper {
    /**
     * 查询【请填写功能名称】
     *
     * @param distributeId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    List<Long> selectMidDistributePermsByDistributeId(Long distributeId);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param midDistributePerms 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    List<MidDistributePerms> selectMidDistributePermsList(MidDistributePerms midDistributePerms);

    /**
     * 新增【请填写功能名称】
     *
     * @param midDistributePerms 【请填写功能名称】
     * @return 结果
     */
    int insertMidDistributePerms(MidDistributePerms midDistributePerms);

    /**
     * 修改【请填写功能名称】
     *
     * @param midDistributePerms 【请填写功能名称】
     * @return 结果
     */
    int updateMidDistributePerms(MidDistributePerms midDistributePerms);

    /**
     * 删除【请填写功能名称】
     *
     * @param distributeId 【请填写功能名称】主键
     * @return 结果
     */
    int deleteMidDistributePermsByDistributeId(Long distributeId);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param distributeIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteMidDistributePermsByDistributeIds(Long[] distributeIds);

    int batchPerms(List<MidDistributePerms> item);
}
