package com.rich.system.mapper;

import com.rich.system.domain.MidLineDestination;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 目的港航线Mapper接口
 *
 * <AUTHOR>
 * @date 2022-09-16
 */
@Mapper
public interface MidLineDestinationMapper {
    /**
     * 查询目的港航线
     *
     * @param belongId 目的港航线主键
     * @param belongTo 属于
     * @return 目的港航线
     */
    List<Long> selectMidLineDestinationById(Long belongId, String belongTo);

    /**
     * 查询目的港航线列表
     *
     * @return 目的港航线集合
     */
    List<MidLineDestination> selectMidLineDestinationByLineIds(@Param("lineIds") Long[] lineIds, String belongTo);

    /**
     * 查询目的港航线列表
     *
     * @return 目的港航线集合
     */
    List<MidLineDestination> selectMidLineDestinationList(MidLineDestination MidLineDestination);

    /**
     * 新增目的港航线
     *
     * @param MidLineDestination 目的港航线
     * @return 结果
     */
    int insertMidLineDestination(MidLineDestination MidLineDestination);


    /**
     * 删除目的港航线
     *
     * @param belongId 目的港航线主键
     * @param belongTo 属于
     * @return 结果
     */
    int deleteMidLineDestinationById(Long belongId, String belongTo);

    /**
     * 批量删除目的港航线
     *
     * @param belongIds 需要删除的数据主键集合
     * @param belongTo  属于
     * @return 结果
     */
    int deleteMidLineDestinationByIds(Long[] belongIds, String belongTo);


    /**
     * 批量新增${subTable.functionName}
     *
     * @param belongList ${subTable.functionName}列表
     * @return 结果
     */
    int batchLD(List<MidLineDestination> belongList);

}
