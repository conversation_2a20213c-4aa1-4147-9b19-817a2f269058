package com.rich.common.core.domain.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 仓租结算中间对象 mid_outbound_settlement
 *
 * <AUTHOR>
 * @date 2025-02-11
 */
public class MidOutboundSettlement extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long outboundRecordId;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long cargoDetailsId;

    /**
     * 结算日（什么时候结算）
     */
    @Excel(name = "结算日", readConverterExp = "什=么时候结算")
    private Date settlementDate;

    /**
     * 结算时的入仓日期（结算时的入仓日期）
     */
    @Excel(name = "结算时的入仓日期", readConverterExp = "结=算时的入仓日期")
    private Date settlementInboundDate;

    /**
     * 结算金额（这一次总共结算的仓租金额）
     */
    @Excel(name = "结算金额", readConverterExp = "这=一次总共结算的仓租金额")
    private BigDecimal settlementRate;
    private Long InventoryId;

    public Long getInventoryId() {
        return InventoryId;
    }

    public void setInventoryId(Long inventoryId) {
        InventoryId = inventoryId;
    }

    public Long getOutboundRecordId() {
        return outboundRecordId;
    }

    public void setOutboundRecordId(Long outboundRecordId) {
        this.outboundRecordId = outboundRecordId;
    }

    public Long getCargoDetailsId() {
        return cargoDetailsId;
    }

    public void setCargoDetailsId(Long cargoDetailsId) {
        this.cargoDetailsId = cargoDetailsId;
    }

    public Date getSettlementDate() {
        return settlementDate;
    }

    public void setSettlementDate(Date settlementDate) {
        this.settlementDate = settlementDate;
    }

    public Date getSettlementInboundDate() {
        return settlementInboundDate;
    }

    public void setSettlementInboundDate(Date settlementInboundDate) {
        this.settlementInboundDate = settlementInboundDate;
    }

    public BigDecimal getSettlementRate() {
        return settlementRate;
    }

    public void setSettlementRate(BigDecimal settlementRate) {
        this.settlementRate = settlementRate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("outboundRecordId", getOutboundRecordId())
                .append("cargoDetailsId", getCargoDetailsId())
                .append("settlementDate", getSettlementDate())
                .append("settlementInboundDate", getSettlementInboundDate())
                .append("settlementRate", getSettlementRate())
                .toString();
    }
}
