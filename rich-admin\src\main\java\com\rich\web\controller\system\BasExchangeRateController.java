package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasExchangeRate;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasExchangeRateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 汇率Controller
 *
 * <AUTHOR>
 * @date 2023-06-05
 */
@RestController
@RequestMapping("/system/exchangerate")
public class BasExchangeRateController extends BaseController {
    @Autowired
    private BasExchangeRateService basExchangeRateService;
    @Autowired
    private RedisCache redisCache;

    /**
     * 查询汇率列表
     */
    @PreAuthorize("@ss.hasPermi('system:exchangerate:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasExchangeRate basExchangeRate) {
        startPage();
        List<BasExchangeRate> list = basExchangeRateService.selectBasExchangeRateList(basExchangeRate);
        return getDataTable(list);
    }

    /**
     * 查询汇率列表
     */
    @GetMapping("/selectList")
    public AjaxResult selectList() {
        List<BasExchangeRate> list = basExchangeRateService.selectList();
        return AjaxResult.success(list);
    }

    /**
     * 导出汇率列表
     */
    @PreAuthorize("@ss.hasPermi('system:exchangerate:export')")
    @Log(title = "汇率", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasExchangeRate basExchangeRate) {
        List<BasExchangeRate> list = basExchangeRateService.selectBasExchangeRateList(basExchangeRate);
        ExcelUtil<BasExchangeRate> util = new ExcelUtil<BasExchangeRate>(BasExchangeRate.class);
        util.exportExcel(response, list, "汇率数据");
    }

    /**
     * 获取汇率详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:exchangerate:query')")
    @GetMapping(value = "/{exchangeRateId}")
    public AjaxResult getInfo(@PathVariable("exchangeRateId") Long exchangeRateId) {
        return AjaxResult.success(basExchangeRateService.selectBasExchangeRateByExchangeRateId(exchangeRateId));
    }

    /**
     * 新增汇率
     */
    @PreAuthorize("@ss.hasPermi('system:exchangerate:add')")
    @Log(title = "汇率", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasExchangeRate basExchangeRate) {
        int out = basExchangeRateService.insertBasExchangeRate(basExchangeRate);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "exchangeRateList");
        return toAjax(out);
    }

    /**
     * 修改汇率
     */
    @PreAuthorize("@ss.hasPermi('system:exchangerate:edit')")
    @Log(title = "汇率", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasExchangeRate basExchangeRate) {
        int out = basExchangeRateService.updateBasExchangeRate(basExchangeRate);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "exchangeRateList");
        return toAjax(out);
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:exchangerate:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasExchangeRate basExchangeRate) {
        basExchangeRate.setUpdateBy(getUserId());
        int out = basExchangeRateService.changeStatus(basExchangeRate);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "exchangeRateList");
        return toAjax(out);
    }

    /**
     * 删除汇率
     */
    @PreAuthorize("@ss.hasPermi('system:exchangerate:remove')")
    @Log(title = "汇率", businessType = BusinessType.DELETE)
    @DeleteMapping("/{exchangeRateIds}")
    public AjaxResult remove(@PathVariable Long[] exchangeRateIds) {
        int out = basExchangeRateService.deleteBasExchangeRateByExchangeRateIds(exchangeRateIds);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "exchangeRateList");
        return toAjax(out);
    }
}
