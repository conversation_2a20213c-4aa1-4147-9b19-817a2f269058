package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.BasBlForm;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.BasBlFormService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 提单形式Controller
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@RestController
@RequestMapping("/system/blform")
public class BasBlFormController extends BaseController {
    @Autowired
    private BasBlFormService basBlFormService;

    /**
     * 查询提单形式列表
     */
    @GetMapping("/list")
    public TableDataInfo list(BasBlForm basBlForm) {
        startPage();
        List<BasBlForm> list = basBlFormService.selectBasBlFormList(basBlForm);
        return getDataTable(list);
    }

    /**
     * 导出提单形式列表
     */
    @PreAuthorize("@ss.hasPermi('system:blform:export')")
    @Log(title = "提单形式", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasBlForm basBlForm) {
        List<BasBlForm> list = basBlFormService.selectBasBlFormList(basBlForm);
        ExcelUtil<BasBlForm> util = new ExcelUtil<BasBlForm>(BasBlForm.class);
        util.exportExcel(response, list, "提单形式数据");
    }

    /**
     * 获取提单形式详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:blform:query')")
    @GetMapping(value = "/{blFormCode}")
    public AjaxResult getInfo(@PathVariable("blFormCode") String blFormCode) {
        return AjaxResult.success(basBlFormService.selectBasBlFormByBlFormCode(blFormCode));
    }

    /**
     * 新增提单形式
     */
    @PreAuthorize("@ss.hasPermi('system:blform:add')")
    @Log(title = "提单形式", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasBlForm basBlForm) {
        return toAjax(basBlFormService.insertBasBlForm(basBlForm));
    }

    /**
     * 修改提单形式
     */
    @PreAuthorize("@ss.hasPermi('system:blform:edit')")
    @Log(title = "提单形式", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasBlForm basBlForm) {
        return toAjax(basBlFormService.updateBasBlForm(basBlForm));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:blform:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasBlForm basBlForm) {
        basBlForm.setUpdateBy(getUserId());
        return toAjax(basBlFormService.changeStatus(basBlForm));
    }

    /**
     * 删除提单形式
     */
    @PreAuthorize("@ss.hasPermi('system:blform:remove')")
    @Log(title = "提单形式", businessType = BusinessType.DELETE)
    @DeleteMapping("/{blFormCodes}")
    public AjaxResult remove(@PathVariable String[] blFormCodes) {
        return toAjax(basBlFormService.deleteBasBlFormByBlFormCodes(blFormCodes));
    }
}
