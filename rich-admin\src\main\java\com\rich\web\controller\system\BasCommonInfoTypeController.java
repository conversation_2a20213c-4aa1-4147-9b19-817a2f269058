package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasCommonInfoType;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasCommonInfoTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 通用信息类型Controller
 *
 * <AUTHOR>
 * @date 2022-12-07
 */
@RestController
@RequestMapping("/system/commoninfotype")
public class BasCommonInfoTypeController extends BaseController {

    @Autowired
    private BasCommonInfoTypeService basCommonInfoTypeService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询通用信息类型列表
     */
    @PreAuthorize("@ss.hasPermi('system:commoninfotype:list')")
    @GetMapping("/list")
    public AjaxResult list(BasCommonInfoType basCommonInfoType) {
        return AjaxResult.success(basCommonInfoTypeService.selectBasCommonInfoTypeList(basCommonInfoType));
    }

    @GetMapping("/selectList")
    public AjaxResult selectList(BasCommonInfoType basCommonInfoType) {
        List<BasCommonInfoType> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "commonInfoType");
        if (list == null) {
            basCommonInfoType.setStatus("0");
            list = basCommonInfoTypeService.selectBasCommonInfoTypeList(basCommonInfoType);
            redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "commonInfoType");
            redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "commonInfoType", list);
        }
        return AjaxResult.success(list);
    }

    @PostMapping("/selectCommonInfoList")
    public AjaxResult selectCommonInfoList(@RequestBody BasCommonInfoType basCommonInfoType) {
        List<BasCommonInfoType> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "commonInfoType" + basCommonInfoType.getInfoTypeLocalName());
        if (list == null) {
            list = basCommonInfoTypeService.selectBasCommonInfoList(basCommonInfoType);
            redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "commonInfoType" + basCommonInfoType.getInfoTypeLocalName());
            redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "commonInfoType" + basCommonInfoType.getInfoTypeLocalName(), list);
        }
        return AjaxResult.success(list);
    }

    /**
     * 导出通用信息类型列表
     */
    @PreAuthorize("@ss.hasPermi('system:commoninfotype:export')")
    @Log(title = "通用信息类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasCommonInfoType basCommonInfoType) {
        List<BasCommonInfoType> list = basCommonInfoTypeService.selectBasCommonInfoTypeList(basCommonInfoType);
        ExcelUtil<BasCommonInfoType> util = new ExcelUtil<>(BasCommonInfoType.class);
        util.exportExcel(response, list, "通用信息类型数据");
    }

    /**
     * 获取通用信息类型详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:commoninfotype:edit')")
    @GetMapping(value = "/{infoTypeId}")
    public AjaxResult getInfo(@PathVariable("infoTypeId") Long infoTypeId) {
        return AjaxResult.success(basCommonInfoTypeService.selectBasCommonInfoTypeByInfoTypeId(infoTypeId));
    }

    /**
     * 新增通用信息类型
     */
    @PreAuthorize("@ss.hasPermi('system:commoninfotype:add')")
    @Log(title = "通用信息类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasCommonInfoType basCommonInfoType) {
        int out = basCommonInfoTypeService.insertBasCommonInfoType(basCommonInfoType);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "commonInfoType");
        return toAjax(out);
    }

    /**
     * 修改通用信息类型
     */
    @PreAuthorize("@ss.hasPermi('system:commoninfotype:edit')")
    @Log(title = "通用信息类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasCommonInfoType basCommonInfoType) {
        int out = basCommonInfoTypeService.updateBasCommonInfoType(basCommonInfoType);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "commonInfoType");
        return toAjax(out);
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('system:commoninfotype:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasCommonInfoType basCommonInfoType) {
        basCommonInfoType.setUpdateBy(getUserId());
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "commonInfoType");
        return toAjax(basCommonInfoTypeService.changeStatus(basCommonInfoType));
    }

    /**
     * 删除通用信息类型
     */
    @PreAuthorize("@ss.hasPermi('system:commoninfotype:remove')")
    @Log(title = "通用信息类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{infoTypeIds}")
    public AjaxResult remove(@PathVariable Long[] infoTypeIds) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "commonInfoType");
        return toAjax(basCommonInfoTypeService.deleteBasCommonInfoTypeByInfoTypeIds(infoTypeIds));
    }
}
