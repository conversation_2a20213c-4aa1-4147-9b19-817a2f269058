package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsAgreementRecord;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 协议记录Mapper接口
 *
 * <AUTHOR>
 * @date 2022-10-28
 */
@Mapper
public interface RsAgreementRecordMapper {
    /**
     * 查询协议记录
     *
     * @param agreementId 协议记录主键
     * @return 协议记录
     */
    RsAgreementRecord selectRsAgreementRecordByAgreementId(Long agreementId);

    /**
     * 查询协议记录列表
     *
     * @param rsAgreementRecord 协议记录
     * @return 协议记录集合
     */
    List<RsAgreementRecord> selectRsAgreementRecordList(RsAgreementRecord rsAgreementRecord);

    /**
     * 新增协议记录
     *
     * @param rsAgreementRecord 协议记录
     * @return 结果
     */
    int insertRsAgreementRecord(RsAgreementRecord rsAgreementRecord);

    /**
     * 修改协议记录
     *
     * @param rsAgreementRecord 协议记录
     * @return 结果
     */
    int updateRsAgreementRecord(RsAgreementRecord rsAgreementRecord);

    /**
     * 删除协议记录
     *
     * @param agreementId 协议记录主键
     * @return 结果
     */
    int deleteRsAgreementRecordByAgreementId(Long agreementId);

    /**
     * 批量删除协议记录
     *
     * @param agreementIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsAgreementRecordByAgreementIds(Long[] agreementIds);
}
