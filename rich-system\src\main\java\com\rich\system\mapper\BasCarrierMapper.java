package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasCarrier;
import com.rich.common.core.domain.entity.BasDistServiceType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 船公司Mapper接口
 *
 * <AUTHOR>
 * @date 2022-10-31
 */
@Mapper
public interface BasCarrierMapper {
    /**
     * 查询船公司
     *
     * @param carrierId 船公司主键
     * @return 船公司
     */
    BasCarrier selectBasCarrierByCarrierId(Long carrierId);

    /**
     * 查询船公司列表
     *
     * @param basCarrier 船公司
     * @return 船公司集合
     */
    List<BasCarrier> selectBasCarrierList(BasCarrier basCarrier);

    /**
     * 新增船公司
     *
     * @param basCarrier 船公司
     * @return 结果
     */
    int insertBasCarrier(BasCarrier basCarrier);

    /**
     * 修改船公司
     *
     * @param basCarrier 船公司
     * @return 结果
     */
    int updateBasCarrier(BasCarrier basCarrier);

    /**
     * 删除船公司
     *
     * @param carrierId 船公司主键
     * @return 结果
     */
    int deleteBasCarrierByCarrierId(Long carrierId);

    /**
     * 批量删除船公司
     *
     * @param carrierIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasCarrierByCarrierIds(Long[] carrierIds);

    List<BasDistServiceType> selectServiceTypeCarriers();
}
