package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsBasicLogistics;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsBasicLogisticsMapper;
import com.rich.system.service.RsBasicLogisticsService;

/**
 * 基础物流Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@Service
public class RsBasicLogisticsServiceImpl implements RsBasicLogisticsService {
    @Autowired
    private RsBasicLogisticsMapper rsBasicLogisticsMapper;

    /**
     * 查询基础物流
     *
     * @param basicLogisticsId 基础物流主键
     * @return 基础物流
     */
    @Override
    public RsBasicLogistics selectRsBasicLogisticsByBasicLogisticsId(Long basicLogisticsId) {
        return rsBasicLogisticsMapper.selectRsBasicLogisticsByBasicLogisticsId(basicLogisticsId);
    }

    /**
     * 查询基础物流列表
     *
     * @param rsBasicLogistics 基础物流
     * @return 基础物流
     */
    @Override
    public List<RsBasicLogistics> selectRsBasicLogisticsList(RsBasicLogistics rsBasicLogistics) {
        return rsBasicLogisticsMapper.selectRsBasicLogisticsList(rsBasicLogistics);
    }

    /**
     * 新增基础物流
     *
     * @param rsBasicLogistics 基础物流
     * @return 结果
     */
    @Override
    public int insertRsBasicLogistics(RsBasicLogistics rsBasicLogistics) {
        return rsBasicLogisticsMapper.insertRsBasicLogistics(rsBasicLogistics);
    }

    /**
     * 修改基础物流
     *
     * @param rsBasicLogistics 基础物流
     * @return 结果
     */
    @Override
    public int updateRsBasicLogistics(RsBasicLogistics rsBasicLogistics) {
        return rsBasicLogisticsMapper.updateRsBasicLogistics(rsBasicLogistics);
    }

    /**
     * 修改基础物流状态
     *
     * @param rsBasicLogistics 基础物流
     * @return 基础物流
     */
    @Override
    public int changeStatus(RsBasicLogistics rsBasicLogistics) {
        return rsBasicLogisticsMapper.updateRsBasicLogistics(rsBasicLogistics);
    }

    /**
     * 批量删除基础物流
     *
     * @param basicLogisticsIds 需要删除的基础物流主键
     * @return 结果
     */
    @Override
    public int deleteRsBasicLogisticsByBasicLogisticsIds(Long[] basicLogisticsIds) {
        return rsBasicLogisticsMapper.deleteRsBasicLogisticsByBasicLogisticsIds(basicLogisticsIds);
    }

    /**
     * 删除基础物流信息
     *
     * @param basicLogisticsId 基础物流主键
     * @return 结果
     */
    @Override
    public int deleteRsBasicLogisticsByBasicLogisticsId(Long basicLogisticsId) {
        return rsBasicLogisticsMapper.deleteRsBasicLogisticsByBasicLogisticsId(basicLogisticsId);
    }
}
