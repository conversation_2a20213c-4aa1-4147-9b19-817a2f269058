package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpAir;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsOpAirMapper;
import com.rich.system.service.RsOpAirService;

/**
 * 空运服务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Service
public class RsOpAirServiceImpl implements RsOpAirService {
    @Autowired
    private RsOpAirMapper rsOpAirMapper;

    /**
     * 查询空运服务
     *
     * @param airId 空运服务主键
     * @return 空运服务
     */
    @Override
    public RsOpAir selectRsOpAirByAirId(Long airId) {
        return rsOpAirMapper.selectRsOpAirByAirId(airId);
    }

    /**
     * 查询空运服务列表
     *
     * @param rsOpAir 空运服务
     * @return 空运服务
     */
    @Override
    public List<RsOpAir> selectRsOpAirList(RsOpAir rsOpAir) {
        return rsOpAirMapper.selectRsOpAirList(rsOpAir);
    }

    /**
     * 新增空运服务
     *
     * @param rsOpAir 空运服务
     * @return 结果
     */
    @Override
    public int insertRsOpAir(RsOpAir rsOpAir) {
        return rsOpAirMapper.insertRsOpAir(rsOpAir);
    }

    /**
     * 修改空运服务
     *
     * @param rsOpAir 空运服务
     * @return 结果
     */
    @Override
    public int updateRsOpAir(RsOpAir rsOpAir) {
        return rsOpAirMapper.updateRsOpAir(rsOpAir);
    }

    /**
     * 修改空运服务状态
     *
     * @param rsOpAir 空运服务
     * @return 空运服务
     */
    @Override
    public int changeStatus(RsOpAir rsOpAir) {
        return rsOpAirMapper.updateRsOpAir(rsOpAir);
    }

    /**
     * 批量删除空运服务
     *
     * @param airIds 需要删除的空运服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpAirByAirIds(Long[] airIds) {
        return rsOpAirMapper.deleteRsOpAirByAirIds(airIds);
    }

    /**
     * 删除空运服务信息
     *
     * @param airId 空运服务主键
     * @return 结果
     */
    @Override
    public int deleteRsOpAirByAirId(Long airId) {
        return rsOpAirMapper.deleteRsOpAirByAirId(airId);
    }
}
