package com.rich.system.mapper;

import com.rich.common.core.domain.entity.BasCompanySource;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 客户来源Mapper接口
 *
 * <AUTHOR>
 * @date 2022-09-30
 */
@Mapper
public interface BasCompanySourceMapper {
    /**
     * 查询客户来源
     *
     * @param sourceId 客户来源主键
     * @return 客户来源
     */
    BasCompanySource selectBasCompanySourceBySourceId(Long sourceId);

    /**
     * 查询客户来源列表
     *
     * @param basCompanySource 客户来源
     * @return 客户来源集合
     */
    List<BasCompanySource> selectBasCompanySourceList(BasCompanySource basCompanySource);

    /**
     * 新增客户来源
     *
     * @param basCompanySource 客户来源
     * @return 结果
     */
    int insertBasCompanySource(BasCompanySource basCompanySource);

    /**
     * 修改客户来源
     *
     * @param basCompanySource 客户来源
     * @return 结果
     */
    int updateBasCompanySource(BasCompanySource basCompanySource);

    /**
     * 删除客户来源
     *
     * @param sourceId 客户来源主键
     * @return 结果
     */
    int deleteBasCompanySourceBySourceId(Long sourceId);

    /**
     * 批量删除客户来源
     *
     * @param sourceIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBasCompanySourceBySourceIds(Long[] sourceIds);
}
