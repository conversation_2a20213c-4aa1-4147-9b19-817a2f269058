package com.rich.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

import java.util.Date;
import java.util.List;

/**
 * 物流注意事项对象 rs_characteristics
 *
 * <AUTHOR>
 * @date 2022-12-30
 */
public class RsCharacteristics extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 物流注意事项
     */
    private Long characteristicsId;

    private List<Long> characteristicsIds;

    private String richNo;

    private Long pageNum;

    private Long pageSize;

    private Long[] serviceTypeIds;

    /**
     * 通用信息
     */
    @Excel(name = "通用信息")
    private Long infoId;

    private String info;
    private String infoEn;

    private Long infoTypeId;

    /**
     * 显示模式
     */
    @Excel(name = "显示模式")
    private String showMode;

    /**
     * 要素详细
     */
    @Excel(name = "要素详细")
    private String essentialDetail;

    /**
     * 有效期从
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "有效期从", width = 30, dateFormat = "yyyy-MM-dd")
    private Date validFrom;

    /**
     * 有效期至
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "有效期至", width = 30, dateFormat = "yyyy-MM-dd")
    private Date validTo;

    /**
     * 是否有效
     */
    @Excel(name = "是否有效")
    private String isValid;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;

    private String orderNum;

    /**
     * 服务类型ID
     */
    private Long serviceTypeId;
    @Excel(name = "服务类型ID")
    private String serviceType;
    private String serviceTypeEn;


    private String cargoType;

    private Long[] cargoTypeIds;

    private String lineDeparture;
    private String lineDepartureEn;

    private Long[] lineDepartureIds;

    private String locationDeparture;
    private String locationDepartureEn;

    private Long[] locationDepartureIds;

    private String lineDestination;
    private String lineDestinationEn;

    private Long[] lineDestinationIds;

    private String locationDestination;
    private String locationDestinationEn;

    private Long[] locationDestinationIds;

    private String carrier;

    private Long[] carrierIds;

    private Long companyId;

    private String company;
    private String companyEn;

    private Integer accurate;

    public Integer getAccurate() {
        return accurate;
    }

    public void setAccurate(Integer accurate) {
        this.accurate = accurate;
    }

    public Long getPageNum() {
        return pageNum;
    }

    public void setPageNum(Long pageNum) {
        this.pageNum = pageNum;
    }

    public Long getPageSize() {
        return pageSize;
    }

    public void setPageSize(Long pageSize) {
        this.pageSize = pageSize;
    }

    public Long[] getServiceTypeIds() {
        return serviceTypeIds;
    }

    public void setServiceTypeIds(Long[] serviceTypeIds) {
        this.serviceTypeIds = serviceTypeIds;
    }

    public String getInfoEn() {
        return infoEn;
    }

    public void setInfoEn(String infoEn) {
        this.infoEn = infoEn;
    }

    public String getServiceTypeEn() {
        return serviceTypeEn;
    }

    public void setServiceTypeEn(String serviceTypeEn) {
        this.serviceTypeEn = serviceTypeEn;
    }

    public String getLineDepartureEn() {
        return lineDepartureEn;
    }

    public void setLineDepartureEn(String lineDepartureEn) {
        this.lineDepartureEn = lineDepartureEn;
    }

    public String getLocationDepartureEn() {
        return locationDepartureEn;
    }

    public void setLocationDepartureEn(String locationDepartureEn) {
        this.locationDepartureEn = locationDepartureEn;
    }

    public String getLineDestinationEn() {
        return lineDestinationEn;
    }

    public void setLineDestinationEn(String lineDestinationEn) {
        this.lineDestinationEn = lineDestinationEn;
    }

    public String getLocationDestinationEn() {
        return locationDestinationEn;
    }

    public void setLocationDestinationEn(String locationDestinationEn) {
        this.locationDestinationEn = locationDestinationEn;
    }

    public String getCompanyEn() {
        return companyEn;
    }

    public void setCompanyEn(String companyEn) {
        this.companyEn = companyEn;
    }

    public String getRichNo() {
        return richNo;
    }

    public void setRichNo(String richNo) {
        this.richNo = richNo;
    }

    public Date getValidFrom() {
        return validFrom;
    }

    public void setValidFrom(Date validFrom) {
        this.validFrom = validFrom;
    }

    public Date getValidTo() {
        return validTo;
    }

    public void setValidTo(Date validTo) {
        this.validTo = validTo;
    }

    public Long getInfoTypeId() {
        return infoTypeId;
    }

    public void setInfoTypeId(Long infoTypeId) {
        this.infoTypeId = infoTypeId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    public List<Long> getCharacteristicsIds() {
        return characteristicsIds;
    }

    public void setCharacteristicsIds(List<Long> characteristicsIds) {
        this.characteristicsIds = characteristicsIds;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public Long getServiceTypeId() {
        return serviceTypeId;
    }

    public void setServiceTypeId(Long serviceTypeId) {
        this.serviceTypeId = serviceTypeId;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public String getCargoType() {
        return cargoType;
    }

    public void setCargoType(String cargoType) {
        this.cargoType = cargoType;
    }

    public Long[] getCargoTypeIds() {
        return cargoTypeIds;
    }

    public void setCargoTypeIds(Long[] cargoTypeIds) {
        this.cargoTypeIds = cargoTypeIds;
    }

    public String getLineDeparture() {
        return lineDeparture;
    }

    public void setLineDeparture(String lineDeparture) {
        this.lineDeparture = lineDeparture;
    }

    public Long[] getLineDepartureIds() {
        return lineDepartureIds;
    }

    public void setLineDepartureIds(Long[] lineDepartureIds) {
        this.lineDepartureIds = lineDepartureIds;
    }

    public String getLocationDeparture() {
        return locationDeparture;
    }

    public void setLocationDeparture(String locationDeparture) {
        this.locationDeparture = locationDeparture;
    }

    public Long[] getLocationDepartureIds() {
        return locationDepartureIds;
    }

    public void setLocationDepartureIds(Long[] locationDepartureIds) {
        this.locationDepartureIds = locationDepartureIds;
    }

    public String getLineDestination() {
        return lineDestination;
    }

    public void setLineDestination(String lineDestination) {
        this.lineDestination = lineDestination;
    }

    public Long[] getLineDestinationIds() {
        return lineDestinationIds;
    }

    public void setLineDestinationIds(Long[] lineDestinationIds) {
        this.lineDestinationIds = lineDestinationIds;
    }

    public String getLocationDestination() {
        return locationDestination;
    }

    public void setLocationDestination(String locationDestination) {
        this.locationDestination = locationDestination;
    }

    public Long[] getLocationDestinationIds() {
        return locationDestinationIds;
    }

    public void setLocationDestinationIds(Long[] locationDestinationIds) {
        this.locationDestinationIds = locationDestinationIds;
    }

    public String getCarrier() {
        return carrier;
    }

    public void setCarrier(String carrier) {
        this.carrier = carrier;
    }

    public Long[] getCarrierIds() {
        return carrierIds;
    }

    public void setCarrierIds(Long[] carrierIds) {
        this.carrierIds = carrierIds;
    }

    public void setCharacteristicsId(Long characteristicsId) {
        this.characteristicsId = characteristicsId;
    }

    public Long getCharacteristicsId() {
        return characteristicsId;
    }

    public void setInfoId(Long infoId) {
        this.infoId = infoId;
    }

    public Long getInfoId() {
        return infoId;
    }

    public void setShowMode(String showMode) {
        this.showMode = showMode;
    }

    public String getShowMode() {
        return showMode;
    }

    public void setEssentialDetail(String essentialDetail) {
        this.essentialDetail = essentialDetail;
    }

    public String getEssentialDetail() {
        return essentialDetail;
    }

    public void setIsValid(String isValid) {
        this.isValid = isValid;
    }

    public String getIsValid() {
        return isValid;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

}
