package com.rich.system.service.impl;

import com.rich.common.constant.CacheConstants;
import com.rich.common.core.domain.entity.BasDistDept;
import com.rich.common.core.domain.entity.SysDistribute;
import com.rich.common.core.domain.entity.SysMenu;
import com.rich.common.core.domain.entity.SysRole;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.exception.ServiceException;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SearchUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.StringUtils;
import com.rich.system.domain.MidDistributeDept;
import com.rich.system.domain.MidDistributeMenu;
import com.rich.system.domain.MidDistributePerms;
import com.rich.system.domain.MidRoleMenu;
import com.rich.system.mapper.*;
import com.rich.system.service.SysDistributeService;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 权限分配Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-02-13
 */
@Service

public class SysDistributeServiceImpl implements SysDistributeService {
    @Autowired
    private SysDistributeMapper sysDistributeMapper;
    @Autowired
    private MidDistributeMenuMapper midDistributeMenuMapper;
    @Autowired
    private MidDistributeDeptMapper midDistributeDeptMapper;
    @Autowired
    private MidDistributePermsMapper midDistributePermsMapper;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private RedisCacheImpl RedisCache;
    @Autowired
    private MidRoleMenuMapper midRoleMenuMapper;


    /**
     * 查询权限分配
     *
     * @param distributeId 权限分配主键
     * @return 权限分配
     */
    @Override
    public SysDistribute selectSysDistributeByDistributeId(Long distributeId) {
        SysDistribute sysDistribute = sysDistributeMapper.selectSysDistributeByDistributeId(distributeId);
        sysDistribute.setDeptIds(midDistributeDeptMapper.selectMidDistributeDeptByDistributeId(distributeId).toArray(new Long[0]));
        sysDistribute.setMenuIds(midDistributeMenuMapper.selectMidDistributeMenuByDistributeId(distributeId).toArray(new Long[0]));
        sysDistribute.setPermsIds(midDistributePermsMapper.selectMidDistributePermsByDistributeId(distributeId).toArray(new Long[0]));
        return sysDistribute;
    }

    /**
     * 查询权限分配列表
     *
     * @param sysDistribute 权限分配
     * @return 权限分配
     */
    @Override
    public List<SysDistribute> selectSysDistributeList(SysDistribute sysDistribute) {
        List<SysDistribute> list = sysDistributeMapper.selectSysDistributeList(sysDistribute);
        for (SysDistribute s : list) {
            s.setDeptIds(midDistributeDeptMapper.selectMidDistributeDeptByDistributeId(s.getDistributeId()).toArray(new Long[0]));
            s.setMenuIds(midDistributeMenuMapper.selectMidDistributeMenuByDistributeId(s.getDistributeId()).toArray(new Long[0]));
            s.setPermsIds(midDistributePermsMapper.selectMidDistributePermsByDistributeId(s.getDistributeId()).toArray(new Long[0]));
        }
        return list;
    }

    /**
     * 新增权限分配
     *
     * @param sysDistribute 权限分配
     * @return 结果
     */
    @Override
    public int insertSysDistribute(SysDistribute sysDistribute) {
        sysDistribute.setCreateTime(DateUtils.getNowDate());
        sysDistribute.setCreateBy(SecurityUtils.getUserId());
        int out = sysDistributeMapper.insertSysDistribute(sysDistribute);
        insertDepts(sysDistribute);
        insertPerms(sysDistribute);
        insertMenus(sysDistribute);
        distributeRole(sysDistribute, true);
        return out;
    }

    @Override
    public void flashSysDistribute() {
        List<SysDistribute> sysDistributes = selectSysDistributeList(new SysDistribute());
        for (SysDistribute sysDistribute : sysDistributes) {
            distributeRole(sysDistribute, true);
        }
    }

    public void distributeRole(SysDistribute sysDistribute, boolean b) {
        Long top = null;
        List<BasDistDept> deptList = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "dept");
        if (deptList == null) {
            RedisCache.dept();
            deptList = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "dept");
        }
        List<Long> deptIds = new ArrayList<>();
        List<SysRole> roleList = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "role");
        if (roleList == null) {
            RedisCache.role();
            roleList = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "role");
        }
        List<Long> roleIds = new ArrayList<>();
        List<SysMenu> menuList = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "menu");
        if (menuList == null) {
            RedisCache.menu();
            menuList = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "menu");
        }
        Set<Long> menuIds = new HashSet<>();
        if (sysDistribute.getPositionId() != null && sysDistribute.getDeptIds() != null && sysDistribute.getMenuIds() != null && sysDistribute.getPermsIds() != null) {
            for (BasDistDept dept : deptList) {
                if (ArrayUtils.contains(sysDistribute.getDeptIds(), dept.getDeptId()) && dept.getCreateListNum() != null && dept.getCreateListNum().equals(1)) {
                    top = dept.getDeptId();
                }
                if (ArrayUtils.contains(sysDistribute.getDeptIds(), dept.getDeptId())) {
                    deptIds.add(dept.getDeptId());
                }
                if (dept.getParentId().equals(top)) {
                    deptIds.add(dept.getDeptId());
                }
            }
            for (SysRole role : roleList) {
                if (ArrayUtils.contains(deptIds.toArray(), role.getDeptId()) && role.getPosition().getPositionSort() != null && role.getPosition().getPositionSort() <= sysDistribute.getPositionId().intValue()) {
                    roleIds.add(role.getRoleId());
                }
            }
            for (SysMenu menu : menuList) {
                if (SearchUtils.existSame(sysDistribute.getMenuIds(), menu.getAncestors().split(",")) || ArrayUtils.contains(sysDistribute.getMenuIds(), menu.getMenuId())) {
                    if (ArrayUtils.contains(sysDistribute.getPermsIds(), Long.parseLong(menu.getPermsType())) || menu.getPermsType().equals("0") || menu.getPermsType().equals("1")) {
                        menuIds.add(menu.getMenuId());
                        for (String a : menu.getAncestors().split(",")) {
                            menuIds.add(Long.parseLong(a));
                        }
                    }
                }
            }
            midRoleMenuMapper.deleteRoleMenuByDistributeId(sysDistribute.getDistributeId());
            List<MidRoleMenu> midRoleMenus = new ArrayList<>();
            for (Long r : roleIds) {
                for (Long m : menuIds) {
                    if (m.equals(0L)) {
                        continue;
                    }
                    MidRoleMenu menu = new MidRoleMenu();
                    menu.setDistributeId(sysDistribute.getDistributeId());
                    menu.setRoleId(r);
                    menu.setMenuId(m);
                    midRoleMenus.add(menu);
                }
            }
            if (midRoleMenus.size() > 0 && b) {
                midRoleMenuMapper.batchRoleMenu(midRoleMenus);
            }
        } else {
            throw new ServiceException("有空值，无法设置权限");
        }
    }

    private void insertMenus(SysDistribute sysDistribute) {
        Long[] menuIds = sysDistribute.getMenuIds();
        if (StringUtils.isNotEmpty(menuIds)) {
            List<MidDistributeMenu> list = new ArrayList<>(menuIds.length);
            for (Long r : menuIds) {
                MidDistributeMenu midDistributeMenu = new MidDistributeMenu();
                midDistributeMenu.setDistributeId(sysDistribute.getDistributeId());
                midDistributeMenu.setMenuId(r);
                list.add(midDistributeMenu);
            }
            midDistributeMenuMapper.batchMenu(list);
        }
    }

    private void insertPerms(SysDistribute sysDistribute) {
        Long[] roleIds = sysDistribute.getPermsIds();
        if (StringUtils.isNotEmpty(roleIds)) {
            List<MidDistributePerms> list = new ArrayList<>(roleIds.length);
            for (Long r : roleIds) {
                MidDistributePerms midDistributePerms = new MidDistributePerms();
                midDistributePerms.setDistributeId(sysDistribute.getDistributeId());
                midDistributePerms.setPermsId(r);
                list.add(midDistributePerms);
            }
            midDistributePermsMapper.batchPerms(list);
        }
    }

    private void insertDepts(SysDistribute sysDistribute) {
        Long[] deptIds = sysDistribute.getDeptIds();
        if (StringUtils.isNotEmpty(deptIds)) {
            List<MidDistributeDept> list = new ArrayList<>(deptIds.length);
            for (Long r : deptIds) {
                MidDistributeDept midDistributeDept = new MidDistributeDept();
                midDistributeDept.setDistributeId(sysDistribute.getDistributeId());
                midDistributeDept.setDeptId(r);
                list.add(midDistributeDept);
            }
            midDistributeDeptMapper.batchDept(list);
        }
    }

    /**
     * 修改权限分配
     *
     * @param sysDistribute 权限分配
     * @return 结果
     */
    @Override
    public int updateSysDistribute(SysDistribute sysDistribute) {
        sysDistribute.setUpdateBy(SecurityUtils.getUserId());
        sysDistribute.setUpdateTime(DateUtils.getNowDate());
        int out = sysDistributeMapper.updateSysDistribute(sysDistribute);
        midDistributeDeptMapper.deleteMidDistributeDeptByDistributeId(sysDistribute.getDistributeId());
        midDistributeMenuMapper.deleteMidDistributeMenuByDistributeId(sysDistribute.getDistributeId());
        midDistributePermsMapper.deleteMidDistributePermsByDistributeId(sysDistribute.getDistributeId());
        insertDepts(sysDistribute);
        insertPerms(sysDistribute);
        insertMenus(sysDistribute);
        distributeRole(sysDistribute, true);
        return out;
    }

    /**
     * 修改权限分配状态
     *
     * @param sysDistribute 权限分配
     * @return 权限分配
     */
    @Override
    public int changeStatus(SysDistribute sysDistribute) {
        return sysDistributeMapper.updateSysDistribute(sysDistribute);
    }

    /**
     * 批量删除权限分配
     *
     * @param distributeIds 需要删除的权限分配主键
     * @return 结果
     */
    @Override
    public int deleteSysDistributeByDistributeIds(Long[] distributeIds) {
        for (Long d : distributeIds) {
            SysDistribute sysDistribute = sysDistributeMapper.selectSysDistributeByDistributeId(d);
            distributeRole(sysDistribute, false);
        }
        midDistributeDeptMapper.deleteMidDistributeDeptByDistributeIds(distributeIds);
        midDistributeMenuMapper.deleteMidDistributeMenuByDistributeIds(distributeIds);
        midDistributePermsMapper.deleteMidDistributePermsByDistributeIds(distributeIds);
        return sysDistributeMapper.deleteSysDistributeByDistributeIds(distributeIds);
    }

    /**
     * 删除权限分配信息
     *
     * @param distributeId 权限分配主键
     * @return 结果
     */
    @Override
    public int deleteSysDistributeByDistributeId(Long distributeId) {
        return sysDistributeMapper.deleteSysDistributeByDistributeId(distributeId);
    }
}
