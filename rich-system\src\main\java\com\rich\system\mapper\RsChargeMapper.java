package com.rich.system.mapper;

import com.rich.common.core.domain.entity.RsCharge;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 费用明细Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@Mapper
public interface RsChargeMapper {
    /**
     * 查询费用明细
     *
     * @param chargeId 费用明细主键
     * @return 费用明细
     */
    RsCharge selectRsChargeByChargeId(Long chargeId);

    /**
     * 查询费用明细列表
     *
     * @param rsCharge 费用明细
     * @return 费用明细集合
     */
    List<RsCharge> selectRsChargeList(RsCharge rsCharge);

    /**
     * 新增费用明细
     *
     * @param rsCharge 费用明细
     * @return 结果
     */
    int insertRsCharge(RsCharge rsCharge);

    /**
     * 修改费用明细
     *
     * @param rsCharge 费用明细
     * @return 结果
     */
    int updateRsCharge(RsCharge rsCharge);

    /**
     * 删除费用明细
     *
     * @param chargeId 费用明细主键
     * @return 结果
     */
    int deleteRsChargeByChargeId(Long chargeId);

    /**
     * 批量删除费用明细
     *
     * @param chargeIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsChargeByChargeIds(Long[] chargeIds);

    int deleteRsCharge(Long serviceId);

    List<RsCharge> selectRsChargeListByServiceId(Long serviceId);

    List<RsCharge> selectRsChargeListByRctId(Long rctId);

    int batchRsCharge(List<RsCharge> rsCharge);

    void deleteRsChargeByServiceId(Long serviceId);

    void deleteRsChargeByRctId(Long rctId);

    int upsertRsCharge(RsCharge rsCharge);

    void flushExchangeRate(@Param("exchangeRate") BigDecimal exchangeRate, @Param("validFrom") Date validFrom, @Param("validTo") Date validTo);

    List<RsCharge> selectWriteOffRsChargeList(RsCharge rsCharge);

    List<RsCharge> findHedging(RsCharge rsCharge);

    List<RsCharge> selectRsChargeListForWriteOff(RsCharge rsCharge);
}
