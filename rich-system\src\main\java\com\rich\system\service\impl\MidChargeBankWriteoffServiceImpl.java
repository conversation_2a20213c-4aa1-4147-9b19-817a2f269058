package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.MidChargeBankWriteoff;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.MidChargeBankWriteoffMapper;
import com.rich.system.service.MidChargeBankWriteoffService;

/**
 * 记录银行销账的明细中间Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-06
 */
@Service
public class MidChargeBankWriteoffServiceImpl implements MidChargeBankWriteoffService {
    @Autowired
    private MidChargeBankWriteoffMapper midChargeBankWriteoffMapper;

    /**
     * 查询记录银行销账的明细中间
     * 
     * @param midChargeBankId 记录银行销账的明细中间主键
     * @return 记录银行销账的明细中间
     */
    @Override
    public MidChargeBankWriteoff selectMidChargeBankWriteoffByMidChargeBankId(Long midChargeBankId) {
        return midChargeBankWriteoffMapper.selectMidChargeBankWriteoffByMidChargeBankId(midChargeBankId);
    }

    /**
     * 查询记录银行销账的明细中间列表
     * 
     * @param midChargeBankWriteoff 记录银行销账的明细中间
     * @return 记录银行销账的明细中间
     */
    @Override
    public List<MidChargeBankWriteoff> selectMidChargeBankWriteoffList(MidChargeBankWriteoff midChargeBankWriteoff) {
        return midChargeBankWriteoffMapper.selectMidChargeBankWriteoffList(midChargeBankWriteoff);
    }

    /**
     * 新增记录银行销账的明细中间
     * 
     * @param midChargeBankWriteoff 记录银行销账的明细中间
     * @return 结果
     */
    @Override
    public int insertMidChargeBankWriteoff(MidChargeBankWriteoff midChargeBankWriteoff) {
        return midChargeBankWriteoffMapper.insertMidChargeBankWriteoff(midChargeBankWriteoff);
    }

    /**
     * 修改记录银行销账的明细中间
     * 
     * @param midChargeBankWriteoff 记录银行销账的明细中间
     * @return 结果
     */
    @Override
    public int updateMidChargeBankWriteoff(MidChargeBankWriteoff midChargeBankWriteoff) {
        return midChargeBankWriteoffMapper.updateMidChargeBankWriteoff(midChargeBankWriteoff);
    }

    /**
     * 修改记录银行销账的明细中间状态
     *
     * @param midChargeBankWriteoff 记录银行销账的明细中间
     * @return 记录银行销账的明细中间
     */
    @Override
    public int changeStatus(MidChargeBankWriteoff midChargeBankWriteoff) {
        return midChargeBankWriteoffMapper.updateMidChargeBankWriteoff(midChargeBankWriteoff);
    }

    /**
     * 批量删除记录银行销账的明细中间
     * 
     * @param midChargeBankIds 需要删除的记录银行销账的明细中间主键
     * @return 结果
     */
    @Override
    public int deleteMidChargeBankWriteoffByMidChargeBankIds(Long[] midChargeBankIds) {
        return midChargeBankWriteoffMapper.deleteMidChargeBankWriteoffByMidChargeBankIds(midChargeBankIds);
    }

    /**
     * 删除记录银行销账的明细中间信息
     * 
     * @param midChargeBankId 记录银行销账的明细中间主键
     * @return 结果
     */
    @Override
    public int deleteMidChargeBankWriteoffByMidChargeBankId(Long midChargeBankId) {
        return midChargeBankWriteoffMapper.deleteMidChargeBankWriteoffByMidChargeBankId(midChargeBankId);
    }
}
