package com.rich.common.enums;

/**
 * <AUTHOR>
 * @Date 2023/6/12 14:43
 * @Version 1.0
 */
public enum companyRole {

    supplier("1", "供应商"),
    client("2", "客户");

    companyRole(String code, String info) {
        this.code = code;
        this.info = info;
    }

    private final String code;
    private final String info;

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
