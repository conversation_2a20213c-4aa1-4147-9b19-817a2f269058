package com.rich.common.core.domain.entity;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 提单形式对象 bas_bl_form
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
public class BasBlForm extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String blFormCode;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String localName;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String enName;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long orderNum;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String docIssueTypeCode;

    public String getBlFormCode() {
        return blFormCode;
    }

    public void setBlFormCode(String blFormCode) {
        this.blFormCode = blFormCode;
    }

    public String getLocalName() {
        return localName;
    }

    public void setLocalName(String localName) {
        this.localName = localName;
    }

    public String getEnName() {
        return enName;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }

    public Long getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Long orderNum) {
        this.orderNum = orderNum;
    }

    public String getDocIssueTypeCode() {
        return docIssueTypeCode;
    }

    public void setDocIssueTypeCode(String docIssueTypeCode) {
        this.docIssueTypeCode = docIssueTypeCode;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("blFormCode", getBlFormCode())
                .append("localName", getLocalName())
                .append("enName", getEnName())
                .append("orderNum", getOrderNum())
                .append("docIssueTypeCode", getDocIssueTypeCode())
                .append("remark", getRemark())
                .toString();
    }
}
