package com.rich.system.service.impl;

import com.rich.common.constant.UserConstants;
import com.rich.common.core.domain.entity.BasDistLine;
import com.rich.common.core.text.Convert;
import com.rich.common.exception.ServiceException;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.StringUtils;
import com.rich.system.mapper.BasDistLineMapper;
import com.rich.system.mapper.BasDistLocationMapper;
import com.rich.system.service.BasDistLineService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 航线Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-08-22
 */
@Service

public class BasDistLineServiceImpl implements BasDistLineService {

    @Autowired
    private  BasDistLineMapper basDistLineMapper;
    @Autowired
    private  BasDistLocationMapper basDistLocationMapper;

    /**
     * 查询航线
     *
     * @param lineId 航线主键
     * @return 航线
     */
    @Override
    public BasDistLine selectBasDistLineByLineId(Long lineId) {
        return basDistLineMapper.selectBasDistLineByLineId(lineId);
    }

    /**
     * 查询航线列表
     *
     * @param basDistLine 航线
     * @return 航线
     */
    @Override
    public List<BasDistLine> selectBasDistLineList(BasDistLine basDistLine) {
        return basDistLineMapper.selectBasDistLineList(basDistLine);
    }

    /**
     * 新增航线
     *
     * @param basDistLine 航线
     * @return 结果
     */
    @Override
    public int insertBasDistLine(BasDistLine basDistLine) {
        BasDistLine info = basDistLineMapper.selectBasDistLineByLineId(basDistLine.getParentId());
        // 如果父节点不为正常状态,则不允许新增子节点
        if (!UserConstants.DEPT_NORMAL.equals(info.getStatus())) {
            throw new ServiceException("区域停用，不允许新增");
        }
        basDistLine.setAncestors(info.getAncestors() + "," + info.getLineId());
        basDistLine.setCreateTime(DateUtils.getNowDate());
        basDistLine.setCreateBy(SecurityUtils.getUserId());
        return basDistLineMapper.insertBasDistLine(basDistLine);
    }

    /**
     * 修改航线
     *
     * @param basDistLine 航线
     * @return 结果
     */
    @Override
    public int updateBasDistLine(BasDistLine basDistLine) {
        basDistLine.setUpdateTime(DateUtils.getNowDate());
        basDistLine.setUpdateBy(SecurityUtils.getUserId());
        BasDistLine newParentLine = basDistLineMapper.selectBasDistLineByLineId(basDistLine.getParentId());
        BasDistLine oldLine = basDistLineMapper.selectBasDistLineByLineId(basDistLine.getLineId());
        if (StringUtils.isNotNull(newParentLine) && StringUtils.isNotNull(oldLine)) {
            String newAncestors = newParentLine.getAncestors() + "," + newParentLine.getLineId();
            String oldAncestors = oldLine.getAncestors();
            basDistLine.setAncestors(newAncestors);
            updateLineChildren(basDistLine.getLineId(), newAncestors, oldAncestors);
            basDistLocationMapper.updateLineAncestors(basDistLine.getLineId(), newAncestors);
        }
        int result = basDistLineMapper.updateBasDistLine(basDistLine);
        if (UserConstants.DEPT_NORMAL.equals(basDistLine.getStatus()) && StringUtils.isNotEmpty(basDistLine.getAncestors())
                && !StringUtils.equals("0", basDistLine.getAncestors())) {
            // 如果该部门是启用状态，则启用该部门的所有上级部门
            updateParentLineStatusNormal(basDistLine);
        }
        return result;
    }


    /**
     * 批量删除航线
     *
     * @param lineIds 需要删除的航线主键
     * @return 结果
     */
    @Override
    public int deleteBasDistLineByLineIds(Long[] lineIds) {
        return basDistLineMapper.deleteBasDistLineByLineIds(lineIds);
    }

    /**
     * 删除航线信息
     *
     * @param lineId 航线主键
     * @return 结果
     */
    @Override
    public int deleteBasDistLineByLineId(Long lineId) {
        return basDistLineMapper.deleteBasDistLineByLineId(lineId);
    }

    @Override
    public int changeStatus(BasDistLine basDistLine) {
        return basDistLineMapper.updateBasDistLine(basDistLine);
    }

    /**
     * 修改子元素关系
     *
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateLineChildren(Long lineId, String newAncestors, String oldAncestors) {
        List<BasDistLine> children = basDistLineMapper.selectChildrenLineById(lineId);
        for (BasDistLine child : children) {
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
        }
        if (children.size() > 0) {
            basDistLineMapper.updateLineChildren(children);
        }
    }

    /**
     * 修改该部门的父级部门状态
     */
    private void updateParentLineStatusNormal(BasDistLine basDistLine) {
        String ancestors = basDistLine.getAncestors();
        Long[] lineIds = Convert.toLongArray(ancestors);
        basDistLineMapper.updateLineStatusNormal(lineIds);
    }
}
