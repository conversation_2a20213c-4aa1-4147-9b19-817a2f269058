package com.rich.system.mapper;

import com.rich.system.domain.MidOrganization;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 组织Mapper接口
 *
 * <AUTHOR>
 * @date 2022-11-28
 */
@Mapper
public interface MidOrganizationMapper {
    /**
     * 查询组织
     *
     * @return 组织
     */
    List<Long> selectMidOrganizationByOrganizationId(Long organizationId, String belongTo);

    List<Long> selectMidOrganizationById(Long belongId, String belongTo);

    /**
     * 查询组织列表
     *
     * @param midOrganization 组织
     * @return 组织集合
     */
    List<MidOrganization> selectMidOrganizationList(MidOrganization midOrganization);

    /**
     * 新增组织
     *
     * @param midOrganization 组织
     * @return 结果
     */
    int insertMidOrganization(MidOrganization midOrganization);

    /**
     * 修改组织
     *
     * @param midOrganization 组织
     * @return 结果
     */
    int updateMidOrganization(MidOrganization midOrganization);

    /**
     * 删除组织
     *
     * @return 结果
     */
    int deleteMidOrganizationById(Long belongId, String belongTo);

    /**
     * 批量删除组织
     *
     * @return 结果
     */
    int deleteMidOrganizationByIds(Long[] belongIds, String belongTo);

    int batchOrganization(List<MidOrganization> list);
}
