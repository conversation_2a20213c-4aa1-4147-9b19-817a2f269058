package com.rich.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @TableName rs_staff
 */
public class RsStaff extends BaseEntity {
    /**
     * 员工唯一ID
     */
    private Long staffId;

    private String role;

    /**
     * 员工编码
     */
    @Excel(name = "员工编码")
    private String staffCode;

    /**
     * 部门ID
     */
    @Excel(name = "部门ID")
    private Long sqdDeptId;

    /**
     * 员工登录账号
     */
    @Excel(name = "员工登录账号")
    private String staffUsername;

    /**
     * 员工登录密码
     */
    @Excel(name = "员工登录密码")
    private String staffPassword;

    /**
     * 员工简称
     */
    @Excel(name = "员工简称")
    private String staffShortName;

    /**
     * 中文姓
     */
    @Excel(name = "中文姓")
    private String staffFamilyLocalName;

    /**
     * 中文名
     */
    @Excel(name = "中文名")
    private String staffGivingLocalName;

    /**
     * 英文姓
     */
    @Excel(name = "英文姓")
    private String staffFamilyEnName;

    /**
     * 英文名
     */
    @Excel(name = "英文名")
    private String staffGivingEnName;

    /**
     * 企业邮箱
     */
    @Excel(name = "企业邮箱")
    private String staffEmailEnterprise;

    /**
     * 个人身份证
     */
    @Excel(name = "个人身份证")
    private String staffIdentity;

    /**
     * 固定电话
     */
    @Excel(name = "固定电话")
    private String staffTelNum;

    /**
     * 手机号码
     */
    @Excel(name = "手机号码")
    private String staffPhoneNum;

    /**
     * 头像地址
     */
    @Excel(name = "头像地址")
    private String staffAvatar;

    /**
     * 个人生日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "个人生日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date staffBirthday;

    /**
     * QQ
     */
    @Excel(name = "QQ")
    private String staffQ;

    /**
     * 性别（0：男，1：女
     */
    @Excel(name = "性别", readConverterExp = "性别（0：男，1：女")
    private String staffGender;

    /**
     * 籍贯
     */
    @Excel(name = "籍贯")
    private String staffNativeplace;

    /**
     * 地点
     */
    @Excel(name = "地点")
    private Long locationId;

    /**
     * 详细住址
     */
    @Excel(name = "详细住址")
    private String staffAddress;

    /**
     * 紧急联系人
     */
    @Excel(name = "紧急联系人")
    private String emergencyContactor;

    /**
     * 联系人关系
     */
    @Excel(name = "联系人关系")
    private String emergencyContactorRelation;

    /**
     * 紧急联系方式
     */
    @Excel(name = "紧急联系方式")
    private String emergencyPhone;

    /**
     * 毕业院校
     */
    @Excel(name = "毕业院校")
    private String staffGraduation;

    /**
     * 证件类型
     */
    @Excel(name = "证件类型")
    private Long credentialType;

    /**
     * 婚姻状态（0：已婚， 1：未婚，2：离异，3：未知
     */
    @Excel(name = "婚姻状态", readConverterExp = "婚姻状态（0：已婚， 1：未婚，2：离异，3：未知")
    private String staffMarital;

    /**
     * 国籍
     */
    @Excel(name = "国籍")
    private String staffNation;

    /**
     * 宗教信仰
     */
    @Excel(name = "宗教信仰")
    private String staffReligion;

    /**
     * 政治面貌
     */
    @Excel(name = "政治面貌")
    private String staffPoliticalCountenance;

    /**
     * 居住性质
     */
    @Excel(name = "居住性质")
    private String residentType;

    /**
     * 第一学历
     */
    @Excel(name = "第一学历")
    private String firstDegree;

    /**
     * 毕业时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "毕业时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date graduationDate;

    /**
     * 毕业院校
     */
    @Excel(name = "毕业院校")
    private String graduationFrom;

    /**
     * 专业
     */
    @Excel(name = "专业")
    private String major;

    /**
     * 最高学历
     */
    @Excel(name = "最高学历")
    private String degree;

    /**
     * 入职时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入职时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date inductionDate;

    /**
     * 健康状况
     */
    @Excel(name = "健康状况")
    private String healthy;

    /**
     * 过敏源
     */
    @Excel(name = "过敏源")
    private String allergySource;

    /**
     * 特别注意
     */
    @Excel(name = "特别注意")
    private String specifically;

    /**
     * 是否入职（0：未入职，1：在职，-1：离职
     */
    @Excel(name = "是否入职", readConverterExp = "是否入职（0：未入职，1：在职，-1：离职")
    private String staffJobStatus;

    /**
     * 最后登录IP
     */
    @Excel(name = "最后登录IP")
    private String loginIp;

    /**
     * 最后登录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最后登录时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date loginDate;

    /**
     * mac地址
     */
    @Excel(name = "mac地址")
    private String macAddress;

    private BasDistDept dept;

    /**
     * 角色对象
     */
    private List<SysRole> roles;

    private List<MidRsStaffRole> midRsStaffRoles;

    private List<BasAccount> staffAccounts;

    private MidRsStaffRole midRsStaffRole;

    /**
     * 角色ID
     */
    private Long roleId;

    private String staffLocalName;

    private String staffEnName;
    private String belongsCompany;
    private String wxNickName;
    private String openid;
    private String unionid;
    private String staffType;

    public String getStaffType() {
        return staffType;
    }

    public void setStaffType(String staffType) {
        this.staffType = staffType;
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getWxNickName() {
        return wxNickName;
    }

    public void setWxNickName(String wxNickName) {
        this.wxNickName = wxNickName;
    }

    public String getBelongsCompany() {
        return belongsCompany;
    }

    public void setBelongsCompany(String belongsCompany) {
        this.belongsCompany = belongsCompany;
    }

    public static boolean isAdmin(String role) {
        return !Objects.equals(role, "") && Objects.equals(role, "admin");
    }

    public String getRole() {
        return role;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    private static final long serialVersionUID = 1L;

    public String getStaffTelNum() {
        return staffTelNum;
    }

    public void setStaffTelNum(String staffTelNum) {
        this.staffTelNum = staffTelNum;
    }

    public String getStaffQ() {
        return staffQ;
    }

    public void setStaffQ(String staffQ) {
        this.staffQ = staffQ;
    }

    public String getEmergencyContactor() {
        return emergencyContactor;
    }

    public void setEmergencyContactor(String emergencyContactor) {
        this.emergencyContactor = emergencyContactor;
    }

    public String getEmergencyContactorRelation() {
        return emergencyContactorRelation;
    }

    public void setEmergencyContactorRelation(String emergencyContactorRelation) {
        this.emergencyContactorRelation = emergencyContactorRelation;
    }

    public String getEmergencyPhone() {
        return emergencyPhone;
    }

    public void setEmergencyPhone(String emergencyPhone) {
        this.emergencyPhone = emergencyPhone;
    }

    public Long getCredentialType() {
        return credentialType;
    }

    public void setCredentialType(Long credentialType) {
        this.credentialType = credentialType;
    }

    public String getStaffNation() {
        return staffNation;
    }

    public void setStaffNation(String staffNation) {
        this.staffNation = staffNation;
    }

    public String getStaffReligion() {
        return staffReligion;
    }

    public void setStaffReligion(String staffReligion) {
        this.staffReligion = staffReligion;
    }

    public String getStaffPoliticalCountenance() {
        return staffPoliticalCountenance;
    }

    public void setStaffPoliticalCountenance(String staffPoliticalCountenance) {
        this.staffPoliticalCountenance = staffPoliticalCountenance;
    }

    public String getResidentType() {
        return residentType;
    }

    public void setResidentType(String residentType) {
        this.residentType = residentType;
    }

    public String getFirstDegree() {
        return firstDegree;
    }

    public void setFirstDegree(String firstDegree) {
        this.firstDegree = firstDegree;
    }

    public Date getGraduationDate() {
        return graduationDate;
    }

    public void setGraduationDate(Date graduationDate) {
        this.graduationDate = graduationDate;
    }

    public String getGraduationFrom() {
        return graduationFrom;
    }

    public void setGraduationFrom(String graduationFrom) {
        this.graduationFrom = graduationFrom;
    }

    public String getMajor() {
        return major;
    }

    public void setMajor(String major) {
        this.major = major;
    }

    public String getDegree() {
        return degree;
    }

    public void setDegree(String degree) {
        this.degree = degree;
    }

    public Date getInductionDate() {
        return inductionDate;
    }

    public void setInductionDate(Date inductionDate) {
        this.inductionDate = inductionDate;
    }

    public String getHealthy() {
        return healthy;
    }

    public void setHealthy(String healthy) {
        this.healthy = healthy;
    }

    public String getAllergySource() {
        return allergySource;
    }

    public void setAllergySource(String allergySource) {
        this.allergySource = allergySource;
    }

    public String getSpecifically() {
        return specifically;
    }

    public void setSpecifically(String specifically) {
        this.specifically = specifically;
    }

    public RsStaff() {
    }

    public Long getSqdDeptId() {
        return sqdDeptId;
    }

    public void setSqdDeptId(Long sqdDeptId) {
        this.sqdDeptId = sqdDeptId;
    }

    public MidRsStaffRole getMidRsStaffRole() {
        return midRsStaffRole;
    }

    public void setMidRsStaffRole(MidRsStaffRole midRsStaffRole) {
        this.midRsStaffRole = midRsStaffRole;
    }

    public String getStaffLocalName() {
        return staffLocalName;
    }

    public void setStaffLocalName(String staffLocalName) {
        this.staffLocalName = staffLocalName;
    }

    public String getStaffEnName() {
        return staffEnName;
    }

    public void setStaffEnName(String staffEnName) {
        this.staffEnName = staffEnName;
    }

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public List<BasAccount> getStaffAccounts() {
        return staffAccounts;
    }

    public void setStaffAccounts(List<BasAccount> staffAccounts) {
        this.staffAccounts = staffAccounts;
    }

    public List<MidRsStaffRole> getMidRsStaffRoles() {
        return midRsStaffRoles;
    }

    public void setMidRsStaffRoles(List<MidRsStaffRole> midRsStaffRoles) {
        this.midRsStaffRoles = midRsStaffRoles;
    }

    public RsStaff(Long staffId) {
        this.staffId = staffId;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public boolean isAdmin() {
        return isAdmin(this.role);
    }

    public BasDistDept getDept() {
        return dept;
    }

    public void setDept(BasDistDept dept) {
        this.dept = dept;
    }

    public List<SysRole> getRoles() {
        return roles;
    }

    public void setRoles(List<SysRole> roles) {
        this.roles = roles;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    /**
     * 员工唯一ID
     */
    public Long getStaffId() {
        return staffId;
    }

    /**
     * 员工唯一ID
     */
    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    /**
     * 员工编码
     */
    public String getStaffCode() {
        return staffCode;
    }

    /**
     * 员工编码
     */
    public void setStaffCode(String staffCode) {
        this.staffCode = staffCode;
    }

    /**
     * 员工登录账号
     */
    public String getStaffUsername() {
        return staffUsername;
    }

    /**
     * 员工登录账号
     */
    public void setStaffUsername(String staffUsername) {
        this.staffUsername = staffUsername;
    }

    /**
     * 员工登录密码
     */
    public String getStaffPassword() {
        return staffPassword;
    }

    /**
     * 员工登录密码
     */
    public void setStaffPassword(String staffPassword) {
        this.staffPassword = staffPassword;
    }

    /**
     * 员工简称
     */
    public String getStaffShortName() {
        return staffShortName;
    }

    /**
     * 员工简称
     */
    public void setStaffShortName(String staffShortName) {
        this.staffShortName = staffShortName;
    }

    /**
     * 中文姓
     */
    public String getStaffFamilyLocalName() {
        return staffFamilyLocalName;
    }

    /**
     * 中文姓
     */
    public void setStaffFamilyLocalName(String staffFamilyLocalName) {
        this.staffFamilyLocalName = staffFamilyLocalName;
    }

    /**
     * 中文名
     */
    public String getStaffGivingLocalName() {
        return staffGivingLocalName;
    }

    /**
     * 中文名
     */
    public void setStaffGivingLocalName(String staffGivingLocalName) {
        this.staffGivingLocalName = staffGivingLocalName;
    }

    /**
     * 英文姓
     */
    public String getStaffFamilyEnName() {
        return staffFamilyEnName;
    }

    /**
     * 英文姓
     */
    public void setStaffFamilyEnName(String staffFamilyEnName) {
        this.staffFamilyEnName = staffFamilyEnName;
    }

    /**
     * 英文名
     */
    public String getStaffGivingEnName() {
        return staffGivingEnName;
    }

    /**
     * 英文名
     */
    public void setStaffGivingEnName(String staffGivingEnName) {
        this.staffGivingEnName = staffGivingEnName;
    }

    /**
     * 企业邮箱
     */
    public String getStaffEmailEnterprise() {
        return staffEmailEnterprise;
    }

    /**
     * 企业邮箱
     */
    public void setStaffEmailEnterprise(String staffEmailEnterprise) {
        this.staffEmailEnterprise = staffEmailEnterprise;
    }

    /**
     * 个人身份证
     */
    public String getStaffIdentity() {
        return staffIdentity;
    }

    /**
     * 个人身份证
     */
    public void setStaffIdentity(String staffIdentity) {
        this.staffIdentity = staffIdentity;
    }

    /**
     * 手机号码
     */
    public String getStaffPhoneNum() {
        return staffPhoneNum;
    }

    /**
     * 手机号码
     */
    public void setStaffPhoneNum(String staffPhoneNum) {
        this.staffPhoneNum = staffPhoneNum;
    }

    /**
     * 个人生日
     */
    public Date getStaffBirthday() {
        return staffBirthday;
    }

    /**
     * 个人生日
     */
    public void setStaffBirthday(Date staffBirthday) {
        this.staffBirthday = staffBirthday;
    }

    /**
     * 性别（0：男，1：女
     */
    public String getStaffGender() {
        return staffGender;
    }

    /**
     * 性别（0：男，1：女
     */
    public void setStaffGender(String staffGender) {
        this.staffGender = staffGender;
    }

    /**
     * 籍贯
     */
    public String getStaffNativeplace() {
        return staffNativeplace;
    }

    /**
     * 籍贯
     */
    public void setStaffNativeplace(String staffNativeplace) {
        this.staffNativeplace = staffNativeplace;
    }


    /**
     * 详细住址
     */
    public String getStaffAddress() {
        return staffAddress;
    }

    /**
     * 详细住址
     */
    public void setStaffAddress(String staffAddress) {
        this.staffAddress = staffAddress;
    }

    /**
     * 毕业院校
     */
    public String getStaffGraduation() {
        return staffGraduation;
    }

    /**
     * 毕业院校
     */
    public void setStaffGraduation(String staffGraduation) {
        this.staffGraduation = staffGraduation;
    }

    /**
     * 婚姻状态（0：已婚， 1：未婚，2：离异，3：丧偶，4：未知
     */
    public String getStaffMarital() {
        return staffMarital;
    }

    /**
     * 婚姻状态（0：已婚， 1：未婚，2：离异，3：丧偶，4：未知
     */
    public void setStaffMarital(String staffMarital) {
        this.staffMarital = staffMarital;
    }

    /**
     * 是否入职（0：未入职，1：在职，-1：离职
     */
    public String getStaffJobStatus() {
        return staffJobStatus;
    }

    /**
     * 是否入职（0：未入职，1：在职，-1：离职
     */
    public void setStaffJobStatus(String staffJobStatus) {
        this.staffJobStatus = staffJobStatus;
    }

    /**
     * 头像地址
     */
    public String getStaffAvatar() {
        return staffAvatar;
    }

    /**
     * 头像地址
     */
    public void setStaffAvatar(String staffAvatar) {
        this.staffAvatar = staffAvatar;
    }

    /**
     * 最后登录IP
     */
    public String getLoginIp() {
        return loginIp;
    }

    /**
     * 最后登录IP
     */
    public void setLoginIp(String loginIp) {
        this.loginIp = loginIp;
    }

    /**
     * 最后登录时间
     */
    public Date getLoginDate() {
        return loginDate;
    }

    /**
     * 最后登录时间
     */
    public void setLoginDate(Date loginDate) {
        this.loginDate = loginDate;
    }

}