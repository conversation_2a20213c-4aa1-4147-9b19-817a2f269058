package com.rich.common.core.domain.entity;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 进口清关对象 rs_import_customs
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
public class RsImportCustoms extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long importCustomsId;

    /**
     * 所属服务实例id ,
     */
    @Excel(name = "所属服务实例id ,")
    private Long serviceId;

    /**
     * 所属服务类型id ,
     */
    @Excel(name = "所属服务类型id ,")
    private Long sqdServiceTypeId;

    /**
     * 所属操作单号 ,
     */
    @Excel(name = "所属操作单号 ,")
    private String sqdRctNo;

    /**
     * 清关时间 ,（目的港清关时间）
     */
    @Excel(name = "清关时间 ,", readConverterExp = "目=的港清关时间")
    private Date cvClearanceTime;

    // 服务实例
    private RsServiceInstances rsServiceInstances;

    private List<RsCharge> rsChargeList;

    private List<RsDoc> rsDocList;

    public List<RsCharge> getRsChargeList() {
        return rsChargeList;
    }

    public void setRsChargeList(List<RsCharge> rsChargeList) {
        this.rsChargeList = rsChargeList;
    }

    public List<RsDoc> getRsDocList() {
        return rsDocList;
    }

    public void setRsDocList(List<RsDoc> rsDocList) {
        this.rsDocList = rsDocList;
    }

    public RsServiceInstances getRsServiceInstances() {
        return rsServiceInstances;
    }

    public void setRsServiceInstances(RsServiceInstances rsServiceInstances) {
        this.rsServiceInstances = rsServiceInstances;
    }

    public Long getImportCustomsId() {
        return importCustomsId;
    }

    public void setImportCustomsId(Long importCustomsId) {
        this.importCustomsId = importCustomsId;
    }

    public Long getServiceId() {
        return serviceId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    public Long getSqdServiceTypeId() {
        return sqdServiceTypeId;
    }

    public void setSqdServiceTypeId(Long sqdServiceTypeId) {
        this.sqdServiceTypeId = sqdServiceTypeId;
    }

    public String getSqdRctNo() {
        return sqdRctNo;
    }

    public void setSqdRctNo(String sqdRctNo) {
        this.sqdRctNo = sqdRctNo;
    }

    public Date getCvClearanceTime() {
        return cvClearanceTime;
    }

    public void setCvClearanceTime(Date cvClearanceTime) {
        this.cvClearanceTime = cvClearanceTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("importCustomsId", getImportCustomsId())
                .append("serviceId", getServiceId())
                .append("sqdServiceTypeId", getSqdServiceTypeId())
                .append("sqdRctNo", getSqdRctNo())
                .append("cvClearanceTime", getCvClearanceTime())
                .toString();
    }
}
