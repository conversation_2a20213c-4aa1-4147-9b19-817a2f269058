package com.rich.system.mapper;

import com.rich.system.domain.MidUnit;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-05
 */
@Mapper
public interface MidUnitMapper {
    /**
     * 查询【请填写功能名称】
     *
     * @return 【请填写功能名称】
     */
    List<Long> selectMidUnitById(Long belongId, String belongTo);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param midUnit 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    List<MidUnit> selectMidUnitList(MidUnit midUnit);

    /**
     * 新增【请填写功能名称】
     *
     * @param midUnit 【请填写功能名称】
     * @return 结果
     */
    int insertMidUnit(MidUnit midUnit);

    /**
     * 修改【请填写功能名称】
     *
     * @param midUnit 【请填写功能名称】
     * @return 结果
     */
    int updateMidUnit(MidUnit midUnit);

    /**
     * 删除【请填写功能名称】
     *
     * @return 结果
     */
    int deleteMidUnitById(Long belongId, String belongTo);

    /**
     * 批量删除【请填写功能名称】
     *
     * @return 结果
     */
    int deleteMidUnitByIds(Long[] belongIds, String belongTo);

    int batchUnit(List<MidUnit> list);
}
