package com.rich.system.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsPaymentTitleMapper;
import com.rich.common.core.domain.entity.RsPaymentTitle;
import com.rich.system.service.RsPaymentTitleService;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-29
 */
@Service
public class RsPaymentTitleServiceImpl implements RsPaymentTitleService {
    @Autowired
    private RsPaymentTitleMapper rsPaymentTitleMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param code 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public RsPaymentTitle selectRsPaymentTitleByCode(String code) {
        return rsPaymentTitleMapper.selectRsPaymentTitleByCode(code);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param rsPaymentTitle 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<RsPaymentTitle> selectRsPaymentTitleList(RsPaymentTitle rsPaymentTitle) {
        return rsPaymentTitleMapper.selectRsPaymentTitleList(rsPaymentTitle);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param rsPaymentTitle 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertRsPaymentTitle(RsPaymentTitle rsPaymentTitle) {
        return rsPaymentTitleMapper.insertRsPaymentTitle(rsPaymentTitle);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param rsPaymentTitle 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateRsPaymentTitle(RsPaymentTitle rsPaymentTitle) {
        return rsPaymentTitleMapper.updateRsPaymentTitle(rsPaymentTitle);
    }

    /**
     * 修改【请填写功能名称】状态
     *
     * @param rsPaymentTitle 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public int changeStatus(RsPaymentTitle rsPaymentTitle) {
        return rsPaymentTitleMapper.updateRsPaymentTitle(rsPaymentTitle);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param codes 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteRsPaymentTitleByCodes(String[] codes) {
        return rsPaymentTitleMapper.deleteRsPaymentTitleByCodes(codes);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param code 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteRsPaymentTitleByCode(String code) {
        return rsPaymentTitleMapper.deleteRsPaymentTitleByCode(code);
    }
}
