package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsOpRoroShip;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsOpRoroShipService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 滚装船服务Controller
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@RestController
@RequestMapping("/system/oproroship")
public class RsOpRoroShipController extends BaseController {
    @Autowired
    private RsOpRoroShipService rsOpRoroShipService;

    /**
     * 查询滚装船服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:oproroship:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsOpRoroShip rsOpRoroShip) {
        startPage();
        List<RsOpRoroShip> list = rsOpRoroShipService.selectRsOpRoroShipList(rsOpRoroShip);
        return getDataTable(list);
    }

    /**
     * 导出滚装船服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:oproroship:export')")
    @Log(title = "滚装船服务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsOpRoroShip rsOpRoroShip) {
        List<RsOpRoroShip> list = rsOpRoroShipService.selectRsOpRoroShipList(rsOpRoroShip);
        ExcelUtil<RsOpRoroShip> util = new ExcelUtil<RsOpRoroShip>(RsOpRoroShip.class);
        util.exportExcel(response, list, "滚装船服务数据");
    }

    /**
     * 获取滚装船服务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:oproroship:query')")
    @GetMapping(value = "/{roroShipId}")
    public AjaxResult getInfo(@PathVariable("roroShipId") Long roroShipId) {
        return AjaxResult.success(rsOpRoroShipService.selectRsOpRoroShipByRoroShipId(roroShipId));
    }

    /**
     * 新增滚装船服务
     */
    @PreAuthorize("@ss.hasPermi('system:oproroship:add')")
    @Log(title = "滚装船服务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsOpRoroShip rsOpRoroShip) {
        return toAjax(rsOpRoroShipService.insertRsOpRoroShip(rsOpRoroShip));
    }

    /**
     * 修改滚装船服务
     */
    @PreAuthorize("@ss.hasPermi('system:oproroship:edit')")
    @Log(title = "滚装船服务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsOpRoroShip rsOpRoroShip) {
        return toAjax(rsOpRoroShipService.updateRsOpRoroShip(rsOpRoroShip));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:oproroship:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsOpRoroShip rsOpRoroShip) {
        rsOpRoroShip.setUpdateBy(getUserId());
        return toAjax(rsOpRoroShipService.changeStatus(rsOpRoroShip));
    }

    /**
     * 删除滚装船服务
     */
    @PreAuthorize("@ss.hasPermi('system:oproroship:remove')")
    @Log(title = "滚装船服务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{roroShipIds}")
    public AjaxResult remove(@PathVariable Long[] roroShipIds) {
        return toAjax(rsOpRoroShipService.deleteRsOpRoroShipByRoroShipIds(roroShipIds));
    }
}
