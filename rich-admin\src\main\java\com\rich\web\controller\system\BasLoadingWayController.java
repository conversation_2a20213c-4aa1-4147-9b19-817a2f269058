package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.BasLoadingWay;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.BasLoadingWayService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 装柜方式Controller
 *
 * <AUTHOR>
 * @date 2024-03-25
 */
@RestController
@RequestMapping("/system/loadingway")
public class BasLoadingWayController extends BaseController {
    @Autowired
    private BasLoadingWayService basLoadingWayService;

    /**
     * 查询装柜方式列表
     */
    @PreAuthorize("@ss.hasPermi('system:loadingway:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasLoadingWay basLoadingWay) {
        startPage();
        List<BasLoadingWay> list = basLoadingWayService.selectBasLoadingWayList(basLoadingWay);
        return getDataTable(list);
    }

    /**
     * 导出装柜方式列表
     */
    @PreAuthorize("@ss.hasPermi('system:loadingway:export')")
    @Log(title = "装柜方式", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasLoadingWay basLoadingWay) {
        List<BasLoadingWay> list = basLoadingWayService.selectBasLoadingWayList(basLoadingWay);
        ExcelUtil<BasLoadingWay> util = new ExcelUtil<BasLoadingWay>(BasLoadingWay.class);
        util.exportExcel(response, list, "装柜方式数据");
    }

    /**
     * 获取装柜方式详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:loadingway:query')")
    @GetMapping(value = "/{loadingWayCode}")
    public AjaxResult getInfo(@PathVariable("loadingWayCode") String loadingWayCode) {
        return AjaxResult.success(basLoadingWayService.selectBasLoadingWayByLoadingWayCode(loadingWayCode));
    }

    /**
     * 新增装柜方式
     */
    @PreAuthorize("@ss.hasPermi('system:loadingway:add')")
    @Log(title = "装柜方式", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasLoadingWay basLoadingWay) {
        return toAjax(basLoadingWayService.insertBasLoadingWay(basLoadingWay));
    }

    /**
     * 修改装柜方式
     */
    @PreAuthorize("@ss.hasPermi('system:loadingway:edit')")
    @Log(title = "装柜方式", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasLoadingWay basLoadingWay) {
        return toAjax(basLoadingWayService.updateBasLoadingWay(basLoadingWay));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:loadingway:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasLoadingWay basLoadingWay) {
        basLoadingWay.setUpdateBy(getUserId());
        return toAjax(basLoadingWayService.changeStatus(basLoadingWay));
    }

    /**
     * 删除装柜方式
     */
    @PreAuthorize("@ss.hasPermi('system:loadingway:remove')")
    @Log(title = "装柜方式", businessType = BusinessType.DELETE)
    @DeleteMapping("/{loadingWayCodes}")
    public AjaxResult remove(@PathVariable String[] loadingWayCodes) {
        return toAjax(basLoadingWayService.deleteBasLoadingWayByLoadingWayCodes(loadingWayCodes));
    }
}
