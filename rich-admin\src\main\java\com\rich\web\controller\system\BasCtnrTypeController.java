package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasCtnrType;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasCtnrTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 箱型特征Controller
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@RestController
@RequestMapping("/system/ctnrtype")
public class BasCtnrTypeController extends BaseController {
    @Autowired
    private BasCtnrTypeService basCtnrTypeService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询箱型特征列表
     */
    @PreAuthorize("@ss.hasPermi('system:ctnrtype:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasCtnrType basCtnrType) {
        startPage();
        List<BasCtnrType> list = basCtnrTypeService.selectBasCtnrTypeList(basCtnrType);
        return getDataTable(list);
    }

    @GetMapping("/selectList")
    public AjaxResult selectList(BasCtnrType basCtnrType) {
        List<BasCtnrType> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "ctnrType");
        if (list == null) {
            list = basCtnrTypeService.selectBasCtnrTypeList(basCtnrType);
            redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "ctnrType");
            redisCache.setCacheObject(CacheConstants.DATA_CACHE_KEY + "ctnrType", list);
        }
        return AjaxResult.success(list);
    }

    /**
     * 导出箱型特征列表
     */
    @PreAuthorize("@ss.hasPermi('system:ctnrtype:export')")
    @Log(title = "箱型特征", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasCtnrType basCtnrType) {
        List<BasCtnrType> list = basCtnrTypeService.selectBasCtnrTypeList(basCtnrType);
        ExcelUtil<BasCtnrType> util = new ExcelUtil<BasCtnrType>(BasCtnrType.class);
        util.exportExcel(response, list, "箱型特征数据");
    }

    /**
     * 获取箱型特征详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:ctnrtype:query')")
    @GetMapping(value = "/{ctnrTypeId}")
    public AjaxResult getInfo(@PathVariable("ctnrTypeId") Long ctnrTypeId) {
        return AjaxResult.success(basCtnrTypeService.selectBasCtnrTypeByCtnrTypeId(ctnrTypeId));
    }

    /**
     * 新增箱型特征
     */
    @PreAuthorize("@ss.hasPermi('system:ctnrtype:add')")
    @Log(title = "箱型特征", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasCtnrType basCtnrType) {
        return toAjax(basCtnrTypeService.insertBasCtnrType(basCtnrType));
    }

    /**
     * 修改箱型特征
     */
    @PreAuthorize("@ss.hasPermi('system:ctnrtype:edit')")
    @Log(title = "箱型特征", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasCtnrType basCtnrType) {
        return toAjax(basCtnrTypeService.updateBasCtnrType(basCtnrType));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:ctnrtype:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody BasCtnrType basCtnrType) {
        basCtnrType.setUpdateBy(getUserId());
        return toAjax(basCtnrTypeService.changeStatus(basCtnrType));
    }

    /**
     * 删除箱型特征
     */
    @PreAuthorize("@ss.hasPermi('system:ctnrtype:remove')")
    @Log(title = "箱型特征", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ctnrTypeIds}")
    public AjaxResult remove(@PathVariable Long[] ctnrTypeIds) {
        return toAjax(basCtnrTypeService.deleteBasCtnrTypeByCtnrTypeIds(ctnrTypeIds));
    }
}
