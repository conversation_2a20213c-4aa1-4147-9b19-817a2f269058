package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsAggregatorConfigs;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsAggregatorConfigsMapper;
import com.rich.system.service.RsAggregatorConfigsService;

/**
 * 数据汇总配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-18
 */
@Service
public class RsAggregatorConfigsServiceImpl implements RsAggregatorConfigsService {
    @Autowired
    private RsAggregatorConfigsMapper rsAggregatorConfigsMapper;

    /**
     * 查询数据汇总配置
     *
     * @param id 数据汇总配置主键
     * @return 数据汇总配置
     */
    @Override
    public RsAggregatorConfigs selectRsAggregatorConfigsById(Long id) {
        return rsAggregatorConfigsMapper.selectRsAggregatorConfigsById(id);
    }

    /**
     * 查询数据汇总配置列表
     *
     * @param rsAggregatorConfigs 数据汇总配置
     * @return 数据汇总配置
     */
    @Override
    public List<RsAggregatorConfigs> selectRsAggregatorConfigsList(RsAggregatorConfigs rsAggregatorConfigs) {
        return rsAggregatorConfigsMapper.selectRsAggregatorConfigsList(rsAggregatorConfigs);
    }

    /**
     * 新增数据汇总配置
     *
     * @param rsAggregatorConfigs 数据汇总配置
     * @return 结果
     */
    @Override
    public int insertRsAggregatorConfigs(RsAggregatorConfigs rsAggregatorConfigs) {
        rsAggregatorConfigs.setCreateTime(DateUtils.getNowDate());
        rsAggregatorConfigs.setCreateBy(SecurityUtils.getUserId());
        return rsAggregatorConfigsMapper.insertRsAggregatorConfigs(rsAggregatorConfigs);
    }

    /**
     * 修改数据汇总配置
     *
     * @param rsAggregatorConfigs 数据汇总配置
     * @return 结果
     */
    @Override
    public int updateRsAggregatorConfigs(RsAggregatorConfigs rsAggregatorConfigs) {
        rsAggregatorConfigs.setUpdateTime(DateUtils.getNowDate());
        rsAggregatorConfigs.setUpdateBy(SecurityUtils.getUserId());
        return rsAggregatorConfigsMapper.updateRsAggregatorConfigs(rsAggregatorConfigs);
    }

    /**
     * 修改数据汇总配置状态
     *
     * @param rsAggregatorConfigs 数据汇总配置
     * @return 数据汇总配置
     */
    @Override
    public int changeStatus(RsAggregatorConfigs rsAggregatorConfigs) {
        return rsAggregatorConfigsMapper.updateRsAggregatorConfigs(rsAggregatorConfigs);
    }

    /**
     * 批量删除数据汇总配置
     *
     * @param ids 需要删除的数据汇总配置主键
     * @return 结果
     */
    @Override
    public int deleteRsAggregatorConfigsByIds(Long[] ids) {
        return rsAggregatorConfigsMapper.deleteRsAggregatorConfigsByIds(ids);
    }

    /**
     * 删除数据汇总配置信息
     *
     * @param id 数据汇总配置主键
     * @return 结果
     */
    @Override
    public int deleteRsAggregatorConfigsById(Long id) {
        return rsAggregatorConfigsMapper.deleteRsAggregatorConfigsById(id);
    }
}
