package com.rich.common.core.domain.entity;

import java.math.BigDecimal;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 仓库客户信息对象 mp_warehouse_client
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
public class MpWarehouseClient extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long warehouseClientId;

    /**
     * 客户系统id，跟客户资料公用
     */
    @Excel(name = "客户系统id，跟客户资料公用")
    private Long clientSystemId;

    /**
     * 客户名称
     */
    @Excel(name = "客户名称")
    private String clientName;

    /**
     * 客户的仓库简称/ 编码
     */
    @Excel(name = "客户的仓库简称/ 编码")
    private String clientCode;

    /**
     * 客户性质
     */
    @Excel(name = "客户性质")
    private String clientType;

    /**
     * 目的国
     */
    @Excel(name = "目的国")
    private String destinationCountry;

    /**
     * 收货人电话
     */
    @Excel(name = "收货人电话")
    private String consigneePhone;

    /**
     * 报价LCL
     */
    @Excel(name = "报价LCL")
    private BigDecimal rateLcl;

    /**
     * 报价20GP
     */
    @Excel(name = "报价20GP")
    private BigDecimal rate20gp;

    /**
     * 报价40HQ
     */
    @Excel(name = "报价40HQ")
    private BigDecimal rate40hq;

    /**
     * 报价4
     */
    @Excel(name = "报价4")
    private BigDecimal rate4;

    /**
     * 报价5
     */
    @Excel(name = "报价5")
    private BigDecimal rate5;

    /**
     * 免堆期
     */
    @Excel(name = "免堆期")
    private Long freeStackPeriod;

    /**
     * 超期仓租
     */
    @Excel(name = "超期仓租")
    private BigDecimal overdueRent;

    /**
     * 入仓费
     */
    @Excel(name = "入仓费")
    private BigDecimal inboundFee;

    /**
     * 扣货
     */
    @Excel(name = "扣货")
    private String cargoDeduction;

    /**
     * 有效
     */
    @Excel(name = "有效")
    private Integer isActive;

    /**
     * 业务员
     */
    @Excel(name = "业务员")
    private Long salesId;

    /**
     * 单件重量
     */
    @Excel(name = "单件重量")
    private BigDecimal singlePieceWeight;

    /**
     * 单件体积
     */
    @Excel(name = "单件体积")
    private BigDecimal singlePieceVolume;

    /**
     * 标准入仓费：数字，单位RMB元
     */
    @Excel(name = "标准入仓费：数字，单位RMB元")
    private BigDecimal standardInboundFee;

    /**
     * 精确入仓费：数字，单位RMB元
     */
    @Excel(name = "精确入仓费：数字，单位RMB元")
    private BigDecimal preciseInboundFee;

    /**
     * 快递入仓费：数字，单位RMB元
     */
    @Excel(name = "快递入仓费：数字，单位RMB元")
    private BigDecimal expressInboundFee;

    /**
     * 默认记录方式：标准/ 精确/ 快递
     */
    @Excel(name = "默认记录方式：标准/ 精确/ 快递")
    private String defaultRecordMode;

    /**
     * 费用现结：是否（0-否，1-是）
     */
    @Excel(name = "费用现结：是否", readConverterExp = "0=-否，1-是")
    private Integer immediatePaymentFee;

    /**
     * 是否已含卸货费（0-否，1-是）
     */
    @Excel(name = "是否已含卸货费", readConverterExp = "0=-否，1-是")
    private Integer includesUnloadingFee;

    /**
     * 是否含入仓费（0-否，1-是）
     */
    @Excel(name = "是否含入仓费", readConverterExp = "0=-否，1-是")
    private Integer includesInboundFee;

    /**
     * 是否已含打包费
     */
    @Excel(name = "是否已含打包费")
    private Integer includesPackingFee;

    /**
     * 跟进人
     */
    @Excel(name = "跟进人")
    private String follower;

    /**
     * 客户目的国公司名称
     */
    @Excel(name = "客户目的国公司名称")
    private String destinationCompanyName;

    /**
     * 客户目的国仓库地址
     */
    @Excel(name = "客户目的国仓库地址")
    private String destinationWarehouseAddress;

    /**
     * 客户目的国仓库联系方式
     */
    @Excel(name = "客户目的国仓库联系方式")
    private String destinationWarehouseContact;

    /**
     * 分单号类型
     */
    @Excel(name = "分单号类型")
    private String subOrderType;
    private String clientRegion;
    private Long belongId;

    public Long getBelongId() {
        return belongId;
    }

    public void setBelongId(Long belongId) {
        this.belongId = belongId;
    }

    public String getClientRegion() {
        return clientRegion;
    }

    public void setClientRegion(String clientRegion) {
        this.clientRegion = clientRegion;
    }

    public Long getWarehouseClientId() {
        return warehouseClientId;
    }

    public void setWarehouseClientId(Long warehouseClientId) {
        this.warehouseClientId = warehouseClientId;
    }

    public Long getClientSystemId() {
        return clientSystemId;
    }

    public void setClientSystemId(Long clientSystemId) {
        this.clientSystemId = clientSystemId;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getClientCode() {
        return clientCode;
    }

    public void setClientCode(String clientCode) {
        this.clientCode = clientCode;
    }

    public String getClientType() {
        return clientType;
    }

    public void setClientType(String clientType) {
        this.clientType = clientType;
    }

    public String getDestinationCountry() {
        return destinationCountry;
    }

    public void setDestinationCountry(String destinationCountry) {
        this.destinationCountry = destinationCountry;
    }

    public String getConsigneePhone() {
        return consigneePhone;
    }

    public void setConsigneePhone(String consigneePhone) {
        this.consigneePhone = consigneePhone;
    }

    public BigDecimal getRateLcl() {
        return rateLcl;
    }

    public void setRateLcl(BigDecimal rateLcl) {
        this.rateLcl = rateLcl;
    }

    public BigDecimal getRate20gp() {
        return rate20gp;
    }

    public void setRate20gp(BigDecimal rate20gp) {
        this.rate20gp = rate20gp;
    }

    public BigDecimal getRate40hq() {
        return rate40hq;
    }

    public void setRate40hq(BigDecimal rate40hq) {
        this.rate40hq = rate40hq;
    }

    public BigDecimal getRate4() {
        return rate4;
    }

    public void setRate4(BigDecimal rate4) {
        this.rate4 = rate4;
    }

    public BigDecimal getRate5() {
        return rate5;
    }

    public void setRate5(BigDecimal rate5) {
        this.rate5 = rate5;
    }

    public Long getFreeStackPeriod() {
        return freeStackPeriod;
    }

    public void setFreeStackPeriod(Long freeStackPeriod) {
        this.freeStackPeriod = freeStackPeriod;
    }

    public BigDecimal getOverdueRent() {
        return overdueRent;
    }

    public void setOverdueRent(BigDecimal overdueRent) {
        this.overdueRent = overdueRent;
    }

    public BigDecimal getInboundFee() {
        return inboundFee;
    }

    public void setInboundFee(BigDecimal inboundFee) {
        this.inboundFee = inboundFee;
    }

    public String getCargoDeduction() {
        return cargoDeduction;
    }

    public void setCargoDeduction(String cargoDeduction) {
        this.cargoDeduction = cargoDeduction;
    }

    public Integer getIsActive() {
        return isActive;
    }

    public void setIsActive(Integer isActive) {
        this.isActive = isActive;
    }

    public Long getSalesId() {
        return salesId;
    }

    public void setSalesId(Long salesId) {
        this.salesId = salesId;
    }

    public BigDecimal getSinglePieceWeight() {
        return singlePieceWeight;
    }

    public void setSinglePieceWeight(BigDecimal singlePieceWeight) {
        this.singlePieceWeight = singlePieceWeight;
    }

    public BigDecimal getSinglePieceVolume() {
        return singlePieceVolume;
    }

    public void setSinglePieceVolume(BigDecimal singlePieceVolume) {
        this.singlePieceVolume = singlePieceVolume;
    }

    public BigDecimal getStandardInboundFee() {
        return standardInboundFee;
    }

    public void setStandardInboundFee(BigDecimal standardInboundFee) {
        this.standardInboundFee = standardInboundFee;
    }

    public BigDecimal getPreciseInboundFee() {
        return preciseInboundFee;
    }

    public void setPreciseInboundFee(BigDecimal preciseInboundFee) {
        this.preciseInboundFee = preciseInboundFee;
    }

    public BigDecimal getExpressInboundFee() {
        return expressInboundFee;
    }

    public void setExpressInboundFee(BigDecimal expressInboundFee) {
        this.expressInboundFee = expressInboundFee;
    }

    public String getDefaultRecordMode() {
        return defaultRecordMode;
    }

    public void setDefaultRecordMode(String defaultRecordMode) {
        this.defaultRecordMode = defaultRecordMode;
    }

    public Integer getImmediatePaymentFee() {
        return immediatePaymentFee;
    }

    public void setImmediatePaymentFee(Integer immediatePaymentFee) {
        this.immediatePaymentFee = immediatePaymentFee;
    }

    public Integer getIncludesUnloadingFee() {
        return includesUnloadingFee;
    }

    public void setIncludesUnloadingFee(Integer includesUnloadingFee) {
        this.includesUnloadingFee = includesUnloadingFee;
    }

    public Integer getIncludesInboundFee() {
        return includesInboundFee;
    }

    public void setIncludesInboundFee(Integer includesInboundFee) {
        this.includesInboundFee = includesInboundFee;
    }

    public Integer getIncludesPackingFee() {
        return includesPackingFee;
    }

    public void setIncludesPackingFee(Integer includesPackingFee) {
        this.includesPackingFee = includesPackingFee;
    }

    public String getFollower() {
        return follower;
    }

    public void setFollower(String follower) {
        this.follower = follower;
    }

    public String getDestinationCompanyName() {
        return destinationCompanyName;
    }

    public void setDestinationCompanyName(String destinationCompanyName) {
        this.destinationCompanyName = destinationCompanyName;
    }

    public String getDestinationWarehouseAddress() {
        return destinationWarehouseAddress;
    }

    public void setDestinationWarehouseAddress(String destinationWarehouseAddress) {
        this.destinationWarehouseAddress = destinationWarehouseAddress;
    }

    public String getDestinationWarehouseContact() {
        return destinationWarehouseContact;
    }

    public void setDestinationWarehouseContact(String destinationWarehouseContact) {
        this.destinationWarehouseContact = destinationWarehouseContact;
    }

    public String getSubOrderType() {
        return subOrderType;
    }

    public void setSubOrderType(String subOrderType) {
        this.subOrderType = subOrderType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("warehouseClientId", getWarehouseClientId())
                .append("clientSystemId", getClientSystemId())
                .append("clientName", getClientName())
                .append("clientCode", getClientCode())
                .append("clientType", getClientType())
                .append("destinationCountry", getDestinationCountry())
                .append("consigneePhone", getConsigneePhone())
                .append("rateLcl", getRateLcl())
                .append("rate20gp", getRate20gp())
                .append("rate40hq", getRate40hq())
                .append("rate4", getRate4())
                .append("rate5", getRate5())
                .append("freeStackPeriod", getFreeStackPeriod())
                .append("overdueRent", getOverdueRent())
                .append("inboundFee", getInboundFee())
                .append("cargoDeduction", getCargoDeduction())
                .append("isActive", getIsActive())
                .append("salesId", getSalesId())
                .append("singlePieceWeight", getSinglePieceWeight())
                .append("singlePieceVolume", getSinglePieceVolume())
                .append("standardInboundFee", getStandardInboundFee())
                .append("preciseInboundFee", getPreciseInboundFee())
                .append("expressInboundFee", getExpressInboundFee())
                .append("defaultRecordMode", getDefaultRecordMode())
                .append("immediatePaymentFee", getImmediatePaymentFee())
                .append("includesUnloadingFee", getIncludesUnloadingFee())
                .append("includesInboundFee", getIncludesInboundFee())
                .append("includesPackingFee", getIncludesPackingFee())
                .append("follower", getFollower())
                .append("destinationCompanyName", getDestinationCompanyName())
                .append("destinationWarehouseAddress", getDestinationWarehouseAddress())
                .append("destinationWarehouseContact", getDestinationWarehouseContact())
                .append("subOrderType", getSubOrderType())
                .toString();
    }
}
