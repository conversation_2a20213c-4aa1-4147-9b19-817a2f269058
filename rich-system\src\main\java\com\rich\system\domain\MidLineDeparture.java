package com.rich.system.domain;

import com.rich.common.core.domain.BaseEntity;
import com.rich.common.core.domain.entity.ExtCompany;
import com.rich.common.core.domain.entity.RsStaff;

import java.util.List;

/**
 * 启运港航线对象 mid_company_line_departure
 *
 * <AUTHOR>
 * @date 2022-09-15
 */
public class MidLineDeparture extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long lineId;

    private Long belongId;

    private String belongTo;

    private List<ExtCompany> extCompanyList;

    private List<RsStaff> staffList;

    public List<RsStaff> getStaffList() {
        return staffList;
    }

    public void setStaffList(List<RsStaff> staffList) {
        this.staffList = staffList;
    }

    public Long getLineId() {
        return lineId;
    }

    public void setLineId(Long lineId) {
        this.lineId = lineId;
    }

    public Long getBelongId() {
        return belongId;
    }

    public void setBelongId(Long belongId) {
        this.belongId = belongId;
    }

    public String getBelongTo() {
        return belongTo;
    }

    public void setBelongTo(String belongTo) {
        this.belongTo = belongTo;
    }

    public List<ExtCompany> getExtCompanyList() {
        return extCompanyList;
    }

    public void setExtCompanyList(List<ExtCompany> extCompanyList) {
        this.extCompanyList = extCompanyList;
    }

}
