package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.MpInventory;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.MpInventoryService;
import com.rich.system.service.RsCargoDetailsService;
import com.rich.system.service.RsInventoryService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * 库存Controller
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@RestController
@RequestMapping("/mp/inventory")
public class MpInventoryController extends BaseController {
    @Autowired
    private MpInventoryService mpInventoryService;

    @Autowired
    private RsInventoryService rsInventoryService;

    @Autowired
    private RsCargoDetailsService rsCargoDetailsService;

    /**
     * 查询库存列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MpInventory mpInventory) {
        startPage();
        // 获取符合原始条件的库存记录
        List<MpInventory> list = mpInventoryService.selectMpInventoryList(mpInventory);

        // 清除分页线程变量，避免影响第二次查询
        clearPage();

        // 如果存在search条件，查询logisticsInfo==search && clientCode==null的记录
        if (StringUtils.isNotEmpty(mpInventory.getSearch())) {
            MpInventory additionalQuery = new MpInventory();
            additionalQuery.setLogisticsInfo(mpInventory.getSearch());
            additionalQuery.setClientCode(null); // 设置clientCode为null

            // 执行额外的查询
            List<MpInventory> additionalList = mpInventoryService.selectMpInventoryList(additionalQuery);

            // 合并两个结果集（避免重复记录）
            for (MpInventory inventory : additionalList) {
                boolean exists = false;
                for (MpInventory existingInventory : list) {
                    if (existingInventory.getInventoryId().equals(inventory.getInventoryId())) {
                        exists = true;
                        break;
                    }
                }
                if (!exists) {
                    list.add(inventory);
                }
            }
        }
        
        return getDataTable(list);
    }

    /**
     * 导出库存列表
     */
    @Log(title = "库存", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MpInventory mpInventory) {
        List<MpInventory> list = mpInventoryService.selectMpInventoryList(mpInventory);
        ExcelUtil<MpInventory> util = new ExcelUtil<MpInventory>(MpInventory.class);
        util.exportExcel(response, list, "库存数据");
    }

    /**
     * 获取库存详细信息
     */
    @GetMapping(value = "/{inventoryId}")
    public AjaxResult getInfo(@PathVariable("inventoryId") Long inventoryId) {
        return AjaxResult.success(mpInventoryService.selectMpInventoryByInventoryId(inventoryId));
    }

    /**
     * 新增库存
     */
    @Log(title = "库存", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MpInventory mpInventory) {
        if (mpInventory.getEstimatedArrivalTime() == null) {
            mpInventory.setEstimatedArrivalTime(new Date());
        }
        if (mpInventory.getInboundDate() == null) {
            mpInventory.setInboundDate(new Date());
        }
        Long userId = SecurityUtils.getLoginUser().getUserId();
        mpInventory.setCreatedBy(userId);
        return AjaxResult.success(mpInventoryService.insertMpInventory(mpInventory));
    }

    @PostMapping("/delete")
    public AjaxResult delete(@RequestBody MpInventory mpInventory) {
        if (mpInventory.getRsInventoryId() != null) {
            rsInventoryService.deleteRsInventoryByInventoryId(mpInventory.getRsInventoryId());
        }
        return AjaxResult.success(mpInventoryService.deleteMpInventoryByInventoryId(mpInventory.getInventoryId()));
    }

    /**
     * 修改库存
     */
    @Log(title = "库存", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MpInventory mpInventory) {
        return toAjax(mpInventoryService.updateMpInventory(mpInventory));
    }

    /**
     * 状态状态
     */
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody MpInventory mpInventory) {
//        mpInventory.setUpdateBy(getUserId());
        return toAjax(mpInventoryService.changeStatus(mpInventory));
    }

    /**
     * 删除库存
     */
    @Log(title = "库存", businessType = BusinessType.DELETE)
    @DeleteMapping("/{inventoryIds}")
    public AjaxResult remove(@PathVariable Long[] inventoryIds) {
        return toAjax(mpInventoryService.deleteMpInventoryByInventoryIds(inventoryIds));
    }

    @GetMapping("/express")
    public AjaxResult express(String expressNo) {
        MpInventory mpInventory = mpInventoryService.selectMpInventoryByExpressNo(expressNo);
        return AjaxResult.success(mpInventory);
    }

    /**
     * 确认入仓
     */
    @Log(title = "入仓", businessType = BusinessType.UPDATE)
    @PutMapping("/enter")
    public AjaxResult enter(@RequestBody MpInventory mpInventory) {
        return AjaxResult.success(mpInventoryService.enter(mpInventory));
    }

    @GetMapping("/status")
    public AjaxResult getStatusNumber(MpInventory mpInventory) {
        return AjaxResult.success(mpInventoryService.getStatusNumber(mpInventory));
    }
}
