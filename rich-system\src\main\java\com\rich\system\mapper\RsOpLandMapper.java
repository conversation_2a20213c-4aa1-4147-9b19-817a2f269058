package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsOpLand;
import org.apache.ibatis.annotations.Mapper;

/**
 * 陆运服务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Mapper
public interface RsOpLandMapper {
    /**
     * 查询陆运服务
     *
     * @param landId 陆运服务主键
     * @return 陆运服务
     */
    RsOpLand selectRsOpLandByLandId(Long landId);

    /**
     * 查询陆运服务列表
     *
     * @param rsOpLand 陆运服务
     * @return 陆运服务集合
     */
    List<RsOpLand> selectRsOpLandList(RsOpLand rsOpLand);

    /**
     * 新增陆运服务
     *
     * @param rsOpLand 陆运服务
     * @return 结果
     */
    int insertRsOpLand(RsOpLand rsOpLand);

    /**
     * 修改陆运服务
     *
     * @param rsOpLand 陆运服务
     * @return 结果
     */
    int updateRsOpLand(RsOpLand rsOpLand);

    /**
     * 删除陆运服务
     *
     * @param landId 陆运服务主键
     * @return 结果
     */
    int deleteRsOpLandByLandId(Long landId);

    /**
     * 批量删除陆运服务
     *
     * @param landIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsOpLandByLandIds(Long[] landIds);

    RsOpLand selectRsOpLandByRctId(Long rctId);
}
