package com.rich.system.service.impl;

import com.rich.common.constant.CacheConstants;
import com.rich.common.core.domain.entity.BasDistCargoType;
import com.rich.common.core.domain.entity.BasDistLine;
import com.rich.common.core.domain.entity.BasDistLocation;
import com.rich.common.core.domain.entity.RsLocalCharge;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.core.text.Convert;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SearchUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.StringUtils;
import com.rich.system.domain.*;
import com.rich.system.mapper.*;
import com.rich.system.service.RsLocalService;
import org.apache.commons.lang3.ArrayUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.util.*;

import static com.rich.common.utils.PageUtils.startPage;

/**
 * 物流附加费策略Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-14
 */
@Service
public class RsLocalServiceImpl implements RsLocalService {
    @Autowired
    private RsLocalMapper rsLocalMapper;
    @Autowired
    private MidCargoTypeMapper midCargoTypeMapper;
    @Autowired
    private MidLocationDepartureMapper midLocationDepartureMapper;
    @Autowired
    private MidLineDepartureMapper midLineDepartureMapper;
    @Autowired
    private MidLocationDestinationMapper midLocationDestinationMapper;
    @Autowired
    private MidLineDestinationMapper midLineDestinationMapper;
    @Autowired
    private MidCarrierMapper midCarrierMapper;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private RedisCacheImpl RedisCache;

    /**
     * 查询物流附加费策略
     *
     * @param localChargeId 物流附加费策略主键
     * @return 物流附加费策略
     */
    @Override
    public RsLocalCharge selectRsLocalByLocalId(Long localChargeId) {
        return rsLocalMapper.selectRsLocalByLocalId(localChargeId);
    }

    /**
     * 查询物流附加费策略列表
     *
     * @param RsLocalCharge 物流附加费策略
     * @return 物流附加费策略
     */
    @Override
    public List<RsLocalCharge> selectRsLocalList(RsLocalCharge RsLocalCharge) {
        boolean search = RsLocalCharge.getCargoTypeIds() != null ||
                RsLocalCharge.getLocationDepartureIds() != null ||
                RsLocalCharge.getLineDepartureIds() != null ||
                RsLocalCharge.getLocationDestinationIds() != null ||
                RsLocalCharge.getLineDestinationIds() != null ||
                RsLocalCharge.getCarrierIds() != null;
        List<Long> qc;
        if (search) {
            qc = queryLocals(RsLocalCharge);
            if (qc == null || qc.isEmpty()) {
                return null;
            }
            RsLocalCharge.setLocalIds(qc);
        }
        startPage();
        return rsLocalMapper.selectRsLocalList(RsLocalCharge);
    }

    /**
     * 新增物流附加费策略
     *
     * @param RsLocalCharge 物流附加费策略
     * @return 结果
     */
    @Override
    public int insertRsLocal(RsLocalCharge RsLocalCharge) {
        RsLocalCharge.setCreateTime(DateUtils.getNowDate());
        RsLocalCharge.setCreateBy(SecurityUtils.getUserId());
        RsLocalCharge.setUpdateBy(SecurityUtils.getUserId());
        RsLocalCharge.setUpdateTime(DateUtils.getNowDate());
        RsLocalCharge.setInquiryNo("RPL" + DateUtils.dateTimeNow() + String.format("%05d", new SecureRandom().nextInt(100000)));
        int out = rsLocalMapper.insertRsLocal(RsLocalCharge);
        insertCarriers(RsLocalCharge);
        insertCargoType(RsLocalCharge);
        insertLineDeparture(RsLocalCharge);
        insertLocationDeparture(RsLocalCharge);
        insertLineDestination(RsLocalCharge);
        insertLocationDestination(RsLocalCharge);
        RedisCache.local();
        return out;
    }

    /**
     * 修改物流附加费策略
     *
     * @param RsLocalCharge 物流附加费策略
     * @return 结果
     */
    @Override
    public int updateRsLocal(RsLocalCharge RsLocalCharge) {
        RsLocalCharge.setUpdateBy(SecurityUtils.getUserId());
        RsLocalCharge.setUpdateTime(DateUtils.getNowDate());
        midCargoTypeMapper.deleteMidCargoTypeById(RsLocalCharge.getLocalChargeId(), "local");
        midLineDepartureMapper.deleteMidLineDepartureById(RsLocalCharge.getLocalChargeId(), "local");
        midLocationDepartureMapper.deleteMidLocationDepartureById(RsLocalCharge.getLocalChargeId(), "local");
        midLineDestinationMapper.deleteMidLineDestinationById(RsLocalCharge.getLocalChargeId(), "local");
        midLocationDestinationMapper.deleteMidLocationDestinationById(RsLocalCharge.getLocalChargeId(), "local");
        midCarrierMapper.deleteMidCarrierById(RsLocalCharge.getLocalChargeId(), "local");
        insertCarriers(RsLocalCharge);
        insertCargoType(RsLocalCharge);
        insertLocationDestination(RsLocalCharge);
        insertLineDestination(RsLocalCharge);
        insertLocationDeparture(RsLocalCharge);
        insertLineDeparture(RsLocalCharge);
        int out = rsLocalMapper.updateRsLocal(RsLocalCharge);
        RedisCache.local();
        return out;
    }

    /**
     * 批量删除物流附加费策略
     *
     * @param localIds 需要删除的物流附加费策略主键
     * @return 结果
     */
    @Override
    public int deleteRsLocalByLocalIds(Long[] localIds) {
        midCargoTypeMapper.deleteMidCargoTypeByIds(localIds, "local");
        RedisCache.midCargoType("local", "localCargoType");

        midLineDepartureMapper.deleteMidLineDepartureByIds(localIds, "local");
        RedisCache.lineDeparture("local", "localLineDeparture");

        midLocationDepartureMapper.deleteMidLocationDepartureByIds(localIds, "local");
        RedisCache.locationDeparture("local", "localLocationDeparture");

        midLineDestinationMapper.deleteMidLineDestinationByIds(localIds, "local");
        RedisCache.lineDestination("local", "localLineDestination");

        midLocationDestinationMapper.deleteMidLocationDestinationByIds(localIds, "local");
        RedisCache.locationDestination("local", "localLocationDestination");

        midCarrierMapper.deleteMidCarrierByIds(localIds, "local");
        RedisCache.midCarrier("local", "localCarriers");

        int out = rsLocalMapper.deleteRsLocalByLocalIds(localIds);
        RedisCache.local();
        return out;
    }

    /**
     * 删除物流附加费策略信息
     *
     * @param localChargeId 物流附加费策略主键
     * @return 结果
     */
    @Override
    public int deleteRsLocalByLocalId(Long localChargeId) {
        midCargoTypeMapper.deleteMidCargoTypeById(localChargeId, "local");
        RedisCache.midCargoType("local", "localCargoType");

        midLineDepartureMapper.deleteMidLineDepartureById(localChargeId, "local");
        RedisCache.lineDeparture("local", "localLineDeparture");

        midLocationDepartureMapper.deleteMidLocationDepartureById(localChargeId, "local");
        RedisCache.locationDeparture("local", "localLocationDeparture");

        midLineDestinationMapper.deleteMidLineDestinationById(localChargeId, "local");
        RedisCache.lineDestination("local", "localLineDestination");

        midLocationDestinationMapper.deleteMidLocationDestinationById(localChargeId, "local");
        RedisCache.locationDestination("local", "localLocationDestination");

        midCarrierMapper.deleteMidCarrierById(localChargeId, "local");
        RedisCache.midCarrier("local", "localCarriers");

        int out = rsLocalMapper.deleteRsLocalByLocalId(localChargeId);
        RedisCache.local();
        return out;
    }

    /**
     * 根据localId查询货物类型
     *
     * @param localChargeId
     * @return
     */
    @Override
    public List<Long> selectCargoTypes(Long localChargeId) {
        List<MidCargoType> midCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "localCargoType");
        if (midCargoTypes == null) {
            RedisCache.midCargoType("local", "localCargoType");
            midCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "localCargoType");
        }
        List<Long> r = new ArrayList<>();
        for (MidCargoType midCargoType : midCargoTypes) {
            if (midCargoType.getBelongId().equals(localChargeId)) {
                r.add(midCargoType.getCargoTypeId());
            }
        }
        return r;
    }

    /**
     * 根据local的id查询起运区域,根据中间表mid_location_departure( belong_id、location_id、belong_to)
     *
     * @param localChargeId 费用ID
     * @return
     */
    @Override
    public List<Long> selectLocationDeparture(Long localChargeId) {
        List<MidLocationDeparture> midLocationDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "localLocationDeparture");
        if (midLocationDepartures == null) {
            RedisCache.locationDeparture("local", "localLocationDeparture");
            midLocationDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "localLocationDeparture");
        }
        List<Long> r = new ArrayList<>();
        for (MidLocationDeparture midLocationDeparture : midLocationDepartures) {
            // 匹配到local的费用类型
            if (midLocationDeparture.getBelongId().equals(localChargeId)) {
                r.add(midLocationDeparture.getLocationId());
            }
        }
        // 返回区域id
        return r;
    }

    /**
     * 根据localId查询对应的目的区域
     *
     * @param localChargeId
     * @return
     */
    @Override
    public List<Long> selectLocationDestination(Long localChargeId) {
        List<MidLocationDestination> midLocationDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "localLocationDestination");
        if (midLocationDestinations == null) {
            RedisCache.locationDestination("local", "localLocationDestination");
            midLocationDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "localLocationDestination");
        }
        List<Long> r = new ArrayList<>();
        for (MidLocationDestination midLocationDestination : midLocationDestinations) {
            if (midLocationDestination.getBelongId().equals(localChargeId)) {
                r.add(midLocationDestination.getLocationId());
            }
        }
        return r;
    }

    /**
     * 根据localId查询启运航线
     *
     * @param localChargeId
     * @return
     */
    @Override
    public List<Long> selectLineDeparture(Long localChargeId) {
        List<MidLineDeparture> midLineDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "localLineDeparture");
        if (midLineDepartures == null) {
            RedisCache.lineDeparture("local", "localLineDeparture");
            midLineDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "localLineDeparture");
        }
        List<Long> r = new ArrayList<>();
        for (MidLineDeparture midLineDeparture : midLineDepartures) {
            if (midLineDeparture.getBelongId().equals(localChargeId)) {
                r.add(midLineDeparture.getLineId());
            }
        }
        return r;
    }

    /**
     * 根据localId查询目的航线
     *
     * @param localChargeId
     * @return
     */
    @Override
    public List<Long> selectLineDestination(Long localChargeId) {
        List<MidLineDestination> midLineDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "localLineDestination");
        if (midLineDestinations == null) {
            RedisCache.lineDestination("local", "localLineDestination");
            midLineDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "localLineDestination");
        }
        List<Long> r = new ArrayList<>();
        for (MidLineDestination midLineDestination : midLineDestinations) {
            if (midLineDestination.getBelongId().equals(localChargeId)) {
                r.add(midLineDestination.getLineId());
            }
        }
        return r;
    }

    /**
     * 根据localId查询承运人
     *
     * @param localChargeId
     * @return
     */
    @Override
    public List<Long> selectCarriers(Long localChargeId) {
        List<MidCarrier> midCarriers = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "localCarriers");
        if (midCarriers == null) {
            RedisCache.midCarrier("local", "localCarriers");
            midCarriers = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "localCarriers");
        }
        List<Long> r = new ArrayList<>();
        for (MidCarrier midCarrier : midCarriers) {
            if (midCarrier.getBelongId().equals(localChargeId)) {
                r.add(midCarrier.getCarrierId());
            }
        }
        return r;
    }

    @Override
    public List<RsLocalCharge> importFreight(List<RsLocalCharge> rsLocalChargeList, boolean updateSupport) {
        HashSet<RsLocalCharge> failList = new HashSet<>();
        for (RsLocalCharge rsLocalCharge : rsLocalChargeList) {
            // 默认海运整柜
            rsLocalCharge.setServiceTypeId(1L);
            rsLocalCharge.setLogisticsTypeId(1L);
            // 服务类型
            if (rsLocalCharge.getServiceType() != null && !Objects.equals(rsLocalCharge.getServiceType(), "")) {
                Long SId = RedisCache.getId("服务大类", rsLocalCharge.getServiceType().replace(" ", ""));
                if (SId == null) {
                    rsLocalCharge.setServiceType("系统匹配失败: " + rsLocalCharge.getServiceType());
                    failList.add(rsLocalCharge);
                    continue;
                }
                rsLocalCharge.setServiceTypeId(SId);
            }
            // 物流类型
            if (rsLocalCharge.getLogisticsType() != null && !Objects.equals(rsLocalCharge.getLogisticsType(), "")) {
                Long LId = RedisCache.getId("服务大类", rsLocalCharge.getLogisticsType().replace(" ", ""));
                if (LId == null) {
                    rsLocalCharge.setLogisticsType("系统匹配失败: " + rsLocalCharge.getLogisticsType());
                    failList.add(rsLocalCharge);
                    continue;
                }
                rsLocalCharge.setLogisticsTypeId(LId);
            }
            // 货物类型
            if (rsLocalCharge.getCargoType() != null && !Objects.equals(rsLocalCharge.getCargoType(), "")) {
                String regex = ",|，";
                String[] cargoTypes = rsLocalCharge.getCargoType().split(regex);
                ArrayList<Long> cargoTypeIds = new ArrayList<>();
                for (String cargoType : cargoTypes) {
                    Long LId = RedisCache.getId("货物类型", cargoType.replace(" ", ""));
                    if (LId == null) {
                        rsLocalCharge.setCargoType("系统匹配失败: " + rsLocalCharge.getCargoType() + "-" + cargoType);
                        failList.add(rsLocalCharge);
                        break;
                    }
                    cargoTypeIds.add(LId);
                }
                rsLocalCharge.setCargoTypeIds(cargoTypeIds.toArray(new Long[0]));
            }
            // 费用类型
            if (rsLocalCharge.getCharge() != null && !Objects.equals(rsLocalCharge.getCharge(), "")) {
                Long LId = RedisCache.getId("价格类别", rsLocalCharge.getCharge().replace(" ", ""));
                if (LId == null) {
                    rsLocalCharge.setCharge("系统匹配失败: " + rsLocalCharge.getCharge());
                    failList.add(rsLocalCharge);
                    continue;
                }
                rsLocalCharge.setChargeId(LId);
            }
            // 承运人
            if (rsLocalCharge.getCarrier() != null && !Objects.equals(rsLocalCharge.getCarrier(), "")) {
                String regex = ",|，";
                String[] carriers = rsLocalCharge.getCarrier().split(regex);
                ArrayList<Long> carrierIds = new ArrayList<>();
                for (String carrier : carriers) {
                    Long LId = RedisCache.getId("承运人", carrier.replace(" ", ""));
                    if (LId == null) {
                        rsLocalCharge.setCarrier("系统匹配失败: " + rsLocalCharge.getCarrier() + "-" + carrier);
                        failList.add(rsLocalCharge);
                        break;
                    }
                    carrierIds.add(LId);
                }
                rsLocalCharge.setCarrierIds(carrierIds.toArray(new Long[0]));
            }
            // 启运区域
            if (rsLocalCharge.getLocationDeparture() != null && !Objects.equals(rsLocalCharge.getLocationDeparture(), "")) {
                String regex = ",|，";
                String[] locationDepartures = rsLocalCharge.getLocationDeparture().split(regex);
                ArrayList<Long> locationDepartureIds = new ArrayList<>();
                for (String locationDeparture : locationDepartures) {
                    Long LId = RedisCache.getId("启运港", locationDeparture.replace(" ", ""));
                    if (LId == null) {
                        rsLocalCharge.setLocationDeparture("系统匹配失败: " + rsLocalCharge.getLocationDeparture() + "-" + locationDeparture);
                        failList.add(rsLocalCharge);
                        break;
                    }
                    locationDepartureIds.add(LId);
                }
                rsLocalCharge.setLocationDepartureIds(locationDepartureIds.toArray(new Long[0]));
            }
            // 目的区域
            if (rsLocalCharge.getLocationDestination() != null && !Objects.equals(rsLocalCharge.getLocationDestination(), "")) {
                String regex = ",|，";
                String[] locationDestinations = rsLocalCharge.getLocationDestination().split(regex);
                ArrayList<Long> locationDestinationIds = new ArrayList<>();
                for (String locationDestination : locationDestinations) {
                    Long LId = RedisCache.getId("启运港", locationDestination.replace(" ", ""));
                    if (LId == null) {
                        rsLocalCharge.setLocationDestination("系统匹配失败: " + rsLocalCharge.getLocationDestination() + "-" + locationDestination);
                        failList.add(rsLocalCharge);
                        break;
                    }
                    locationDestinationIds.add(LId);
                }
                rsLocalCharge.setLocationDestinationIds(locationDestinationIds.toArray(new Long[0]));
            }
            // 目的航线
            if (rsLocalCharge.getLineDestination() != null && !Objects.equals(rsLocalCharge.getLineDestination(), "")) {
                String regex = ",|，";
                String[] lineDestinations = rsLocalCharge.getLineDestination().split(regex);
                ArrayList<Long> lineDestinationIds = new ArrayList<>();
                for (String lineDestination : lineDestinations) {
                    Long LId = RedisCache.getId("航线", lineDestination.replace(" ", ""));
                    if (LId == null) {
                        rsLocalCharge.setLineDestination("系统匹配失败: " + rsLocalCharge.getLineDestination() + "-" + lineDestination);
                        failList.add(rsLocalCharge);
                        break;
                    }
                    lineDestinationIds.add(LId);
                }
                rsLocalCharge.setLineDestinationIds(lineDestinationIds.toArray(new Long[0]));
            }

            if (!failList.contains(rsLocalCharge)) {
                // 是否更新原有费用
                if (rsLocalCharge.getInquiryNo() != null && !Objects.equals(rsLocalCharge.getInquiryNo(), "") && updateSupport) {
                    int a = updateRsLocal(rsLocalCharge);
                } else {
                    insertRsLocal(rsLocalCharge);
                }
            }
        }


        return new ArrayList<>(failList);
    }

    private void insertCargoType(@NotNull RsLocalCharge RsLocalCharge) {
        Long[] roles = RsLocalCharge.getCargoTypeIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidCargoType> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidCargoType MidCargoType = new MidCargoType();
                MidCargoType.setBelongId(RsLocalCharge.getLocalChargeId());
                MidCargoType.setBelongTo("local");
                MidCargoType.setCargoTypeId(r);
                list.add(MidCargoType);
            }
            midCargoTypeMapper.batchCargoType(list);
        }
        RedisCache.midCargoType("local", "localCargoType");
    }

    private void insertCarriers(@NotNull RsLocalCharge RsLocalCharge) {
        Long[] roles = RsLocalCharge.getCarrierIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidCarrier> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidCarrier midCarrier = new MidCarrier();
                midCarrier.setBelongId(RsLocalCharge.getLocalChargeId());
                midCarrier.setCarrierId(r);
                midCarrier.setBelongTo("local");
                list.add(midCarrier);
            }
            midCarrierMapper.batchCarrier(list);
        }
        RedisCache.midCarrier("local", "localCarriers");
    }

    private void insertLineDeparture(@NotNull RsLocalCharge RsLocalCharge) {
        Long[] roles = RsLocalCharge.getLineDepartureIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidLineDeparture> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidLineDeparture MidLineDeparture = new MidLineDeparture();
                MidLineDeparture.setBelongId(RsLocalCharge.getLocalChargeId());
                MidLineDeparture.setLineId(r);
                MidLineDeparture.setBelongTo("local");
                list.add(MidLineDeparture);
            }
            midLineDepartureMapper.batchLD(list);
        }
        RedisCache.lineDeparture("local", "localLineDeparture");
    }

    private void insertLocationDeparture(@NotNull RsLocalCharge RsLocalCharge) {
        Long[] roles = RsLocalCharge.getLocationDepartureIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidLocationDeparture> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidLocationDeparture MidLocationDeparture = new MidLocationDeparture();
                MidLocationDeparture.setBelongId(RsLocalCharge.getLocalChargeId());
                MidLocationDeparture.setLocationId(r);
                MidLocationDeparture.setBelongTo("local");
                list.add(MidLocationDeparture);
            }
            midLocationDepartureMapper.batchLD(list);
        }
        RedisCache.locationDeparture("local", "localLocationDeparture");
    }

    private void insertLocationDestination(@NotNull RsLocalCharge RsLocalCharge) {
        Long[] roles = RsLocalCharge.getLocationDestinationIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidLocationDestination> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidLocationDestination MidLocationDestination = new MidLocationDestination();
                MidLocationDestination.setBelongId(RsLocalCharge.getLocalChargeId());
                MidLocationDestination.setLocationId(r);
                MidLocationDestination.setBelongTo("local");
                list.add(MidLocationDestination);
            }
            midLocationDestinationMapper.batchLD(list);
        }
        RedisCache.locationDestination("local", "localLocationDestination");
    }

    private void insertLineDestination(@NotNull RsLocalCharge RsLocalCharge) {
        Long[] roles = RsLocalCharge.getLineDestinationIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidLineDestination> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidLineDestination MidLineDestination = new MidLineDestination();
                MidLineDestination.setBelongId(RsLocalCharge.getLocalChargeId());
                MidLineDestination.setLineId(r);
                MidLineDestination.setBelongTo("local");
                list.add(MidLineDestination);
            }
            midLineDestinationMapper.batchLD(list);
        }
        RedisCache.lineDestination("local", "localLineDestination");
    }

    private List<Long> queryLocals(RsLocalCharge local) {
        List<List<Long>> lists = new ArrayList<>();
        List<RsLocalCharge> locals = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "local");
        if (locals == null) {
            RedisCache.local();
            locals = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "local");
        }
        List<BasDistLocation> basDistLocations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        if (basDistLocations == null) {
            RedisCache.location();
            basDistLocations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        }
        List<BasDistLine> basDistLines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        if (basDistLines == null) {
            RedisCache.line();
            basDistLines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        }
        List<BasDistCargoType> basDistCargoTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        if (basDistCargoTypes == null) {
            RedisCache.cargoType();
            basDistCargoTypes = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "cargoType");
        }
        List<MidCarrier> midCarriers = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "localCarriers");
        if (midCarriers == null) {
            RedisCache.midCarrier("local", "localCarriers");
            midCarriers = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "localCarriers");
        }
        List<MidCargoType> midCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "localCargoType");
        if (midCargoTypes == null) {
            RedisCache.midCargoType("local", "localCargoType");
            midCargoTypes = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "localCargoType");
        }
        List<MidLocationDeparture> midLocationDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "localLocationDeparture");
        if (midLocationDepartures == null) {
            RedisCache.locationDeparture("local", "localLocationDeparture");
            midLocationDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "localLocationDeparture");
        }
        List<MidLineDeparture> midLineDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "localLineDeparture");
        if (midLineDepartures == null) {
            RedisCache.lineDeparture("local", "localLineDeparture");
            midLineDepartures = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "localLineDeparture");
        }
        List<MidLocationDestination> midLocationDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "localLocationDestination");
        if (midLocationDestinations == null) {
            RedisCache.locationDestination("local", "localLocationDestination");
            midLocationDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "localLocationDestination");
        }
        List<MidLineDestination> midLineDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "localLineDestination");
        if (midLineDestinations == null) {
            RedisCache.lineDestination("local", "localLineDestination");
            midLineDestinations = redisCache.getCacheObject(CacheConstants.MID_CACHE_KEY + "localLineDestination");
        }
        if (local.getCarrierIds() != null) {
            Set<Long> set = new HashSet<>();
            for (MidCarrier d : midCarriers) {
                if (ArrayUtils.contains(local.getCarrierIds(), d.getCarrierId())) {
                    set.add(d.getBelongId());
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (!list.isEmpty()) {
                lists.add(list);
            } else {
                return null;
            }
        }
        if (local.getCargoTypeIds() != null) {
            Set<Long> set = new HashSet<>();
            if (ArrayUtils.contains(local.getCargoTypeIds(), -1L)) {
                for (RsLocalCharge c : locals) {
                    set.add(c.getLocalChargeId());
                }
            } else {
                Set<Long> c = new HashSet<>();
                for (BasDistCargoType cargoType : basDistCargoTypes) {
                    String[] ancestors = cargoType.getAncestors().split(",");
                    if (SearchUtils.existSame(ancestors, local.getCargoTypeIds())) {
                        c.add(cargoType.getCargoTypeId());
                    }
                    if (ArrayUtils.contains(local.getCargoTypeIds(), cargoType.getCargoTypeId())) {
                        c.add(cargoType.getCargoTypeId());
                        for (String a : ancestors) {
                            c.add(Convert.toLong(a));
                        }
                    }
                }
                for (MidCargoType midCargoType : midCargoTypes) {
                    if (c.contains(midCargoType.getCargoTypeId())) {
                        set.add(midCargoType.getBelongId());
                    }
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (!list.isEmpty()) {
                lists.add(list);
            } else {
                return null;
            }
        }
        if (local.getLocationDepartureIds() != null) {
            Set<Long> set = new HashSet<>();
            if (ArrayUtils.contains(local.getLocationDepartureIds(), -1L)) {
                for (RsLocalCharge c : locals) {
                    set.add(c.getLocalChargeId());
                }
            } else {
                Set<Long> olines = new HashSet<>();
                Set<Long> olocations = new HashSet<>();
                for (BasDistLocation location : basDistLocations) {
                    String[] ancestors = location.getAncestors().split(",");
                    if (ArrayUtils.contains(local.getLocationDepartureIds(), location.getLocationId())) {
                        olocations.add(location.getLocationId());
                        for (String a : ancestors) {
                            olocations.add(Convert.toLong(a));
                        }
                        if (location.getLineId() != null) {
                            for (BasDistLine line : basDistLines) {
                                String[] lineAncestors = line.getAncestors().split(",");
                                if (line.getLineId().equals(location.getLineId())) {
                                    olines.add(location.getLineId());
                                    for (String a : lineAncestors) {
                                        olines.add(Convert.toLong(a));
                                    }
                                }
                                if (ArrayUtils.contains(lineAncestors, location.getLineId().toString())) {
                                    olines.add(line.getLineId());
                                }
                            }
                        }
                    }
                    if (SearchUtils.existSame(ancestors, local.getLocationDepartureIds())) {
                        olocations.add(location.getLocationId());
                    }
                }
                for (MidLocationDeparture locationDeparture : midLocationDepartures) {
                    if (olocations.contains(locationDeparture.getLocationId())) {
                        set.add(locationDeparture.getBelongId());
                    }
                }
                for (MidLineDeparture lineDeparture : midLineDepartures) {
                    if (olines.contains(lineDeparture.getLineId())) {
                        set.add(lineDeparture.getBelongId());
                    }
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (!list.isEmpty()) {
                lists.add(list);
            } else {
                return null;
            }
        }
        if (local.getLineDepartureIds() != null) {
            Set<Long> set = new HashSet<>();
            if (ArrayUtils.contains(local.getLineDepartureIds(), -1L)) {
                for (RsLocalCharge c : locals) {
                    set.add(c.getLocalChargeId());
                }
            } else {
                Set<Long> olocations = new HashSet<>();
                Set<Long> olines = new HashSet<>();
                for (BasDistLine line : basDistLines) {
                    String[] ancestors = line.getAncestors().split(",");
                    if (ArrayUtils.contains(local.getLineDepartureIds(), line.getLineId())) {
                        olines.add(line.getLineId());
                        for (String a : ancestors) {
                            olines.add(Convert.toLong(a));
                        }
                    }
                    if (SearchUtils.existSame(ancestors, local.getLineDepartureIds())) {
                        olines.add(line.getLineId());
                    }
                }
                for (BasDistLocation location : basDistLocations) {
                    if (location.getLineId() != null && ArrayUtils.contains(olines.toArray(), location.getLineId())) {
                        String[] ancestors = location.getAncestors().split(",");
                        olocations.add(location.getLocationId());
                        for (String a : ancestors) {
                            olocations.add(Convert.toLong(a));
                        }
                    }
                }
                for (MidLocationDeparture locationDeparture : midLocationDepartures) {
                    if (olocations.contains(locationDeparture.getLocationId())) {
                        set.add(locationDeparture.getBelongId());
                    }
                }
                for (MidLineDeparture lineDeparture : midLineDepartures) {
                    if (olines.contains(lineDeparture.getLineId())) {
                        set.add(lineDeparture.getBelongId());
                    }
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (!list.isEmpty()) {
                lists.add(list);
            } else {
                return null;
            }
        }
        if (local.getLocationDestinationIds() != null) {
            Set<Long> set = new HashSet<>();
            if (ArrayUtils.contains(local.getLocationDestinationIds(), -1L)) {
                for (RsLocalCharge c : locals) {
                    set.add(c.getLocalChargeId());
                }
            } else {
                Set<Long> olines = new HashSet<>();
                Set<Long> olocations = new HashSet<>();
                for (BasDistLocation location : basDistLocations) {
                    String[] ancestors = location.getAncestors().split(",");
                    if (ArrayUtils.contains(local.getLocationDestinationIds(), location.getLocationId())) {
                        olocations.add(location.getLocationId());
                        for (String a : ancestors) {
                            olocations.add(Convert.toLong(a));
                        }
                        if (location.getLineId() != null) {
                            for (BasDistLine line : basDistLines) {
                                String[] lineAncestors = line.getAncestors().split(",");
                                if (line.getLineId().equals(location.getLineId())) {
                                    olines.add(location.getLineId());
                                    for (String a : lineAncestors) {
                                        olines.add(Convert.toLong(a));
                                    }
                                }
                                if (ArrayUtils.contains(lineAncestors, location.getLineId().toString())) {
                                    olines.add(line.getLineId());
                                }
                            }
                        }
                    }
                    if (SearchUtils.existSame(ancestors, local.getLocationDestinationIds())) {
                        olocations.add(location.getLocationId());
                    }
                }
                for (MidLocationDestination locationDestination : midLocationDestinations) {
                    if (olocations.contains(locationDestination.getLocationId())) {
                        set.add(locationDestination.getBelongId());
                    }
                }
                for (MidLineDestination lineDestination : midLineDestinations) {
                    if (olines.contains(lineDestination.getLineId())) {
                        set.add(lineDestination.getBelongId());
                    }
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (!list.isEmpty()) {
                lists.add(list);
            } else {
                return null;
            }
        }
        if (local.getLineDestinationIds() != null) {
            Set<Long> set = new HashSet<>();
            if (ArrayUtils.contains(local.getLineDepartureIds(), -1L)) {
                for (RsLocalCharge c : locals) {
                    set.add(c.getLocalChargeId());
                }
            } else {
                Set<Long> olocations = new HashSet<>();
                Set<Long> olines = new HashSet<>();
                for (BasDistLine line : basDistLines) {
                    String[] ancestors = line.getAncestors().split(",");
                    if (ArrayUtils.contains(local.getLineDestinationIds(), line.getLineId())) {
                        olines.add(line.getLineId());
                        for (String a : ancestors) {
                            olines.add(Convert.toLong(a));
                        }
                    }
                    if (SearchUtils.existSame(ancestors, local.getLineDestinationIds())) {
                        olines.add(line.getLineId());
                    }
                }
                for (BasDistLocation location : basDistLocations) {
                    if (location.getLineId() != null && ArrayUtils.contains(olines.toArray(), location.getLineId())) {
                        String[] ancestors = location.getAncestors().split(",");
                        olocations.add(location.getLocationId());
                        for (String a : ancestors) {
                            olocations.add(Convert.toLong(a));
                        }
                    }
                }
                for (MidLocationDestination locationDestination : midLocationDestinations) {
                    if (olocations.contains(locationDestination.getLocationId())) {
                        set.add(locationDestination.getBelongId());
                    }
                }
                for (MidLineDestination lineDestination : midLineDestinations) {
                    if (olines.contains(lineDestination.getLineId())) {
                        set.add(lineDestination.getBelongId());
                    }
                }
            }
            List<Long> list = new ArrayList<>(set);
            if (!list.isEmpty()) {
                lists.add(list);
            } else {
                return null;
            }
        }
        if (!lists.isEmpty()) {
            return SearchUtils.getLongs(lists);
        } else {
            return null;
        }
    }
}
