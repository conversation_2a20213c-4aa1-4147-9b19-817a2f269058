package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.RsFreight;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasDistLocationService;
import com.rich.system.service.ExtCompanyService;
import com.rich.system.service.RsFreightService;
import com.rich.system.service.impl.RedisCacheImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 基础运费Controller
 *
 * <AUTHOR>
 * @date 2023-01-04
 */
@RestController
@RequestMapping("/system/freight")
public class RsFreightController extends BaseController {

    @Autowired
    private RsFreightService rsFreightService;
    @Autowired
    private BasDistLocationService basDistLocationService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private RedisCacheImpl RedisCache;
    @Resource
    private ExtCompanyService extCompanyService;

    /**
     * 查询基础运费列表
     */
    @PreAuthorize("@ss.hasPermi('system:freight:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsFreight rsFreight) {
        List<RsFreight> list = rsFreightService.selectRsFreightList(rsFreight);
        if (list != null && !list.isEmpty()) {
            return getDataTable(list);
        } else {
            return null;
        }
    }

    /**
     * 导出基础运费列表
     */
    @PreAuthorize("@ss.hasPermi('system:freight:export')")
    @Log(title = "基础运费", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsFreight rsFreight) {
        List<RsFreight> list = rsFreightService.selectRsFreightListExport(rsFreight);
        ExcelUtil<RsFreight> util = new ExcelUtil<RsFreight>(RsFreight.class);
        util.exportExcel(response, list, "基础运费数据");
    }

    /**
     * 获取基础运费详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:freight:edit')")
    @GetMapping(value = "/{freightId}")
    public AjaxResult getInfo(@PathVariable("freightId") Long freightId) {
        AjaxResult ajaxResult = AjaxResult.success();
        Set<Long> set = new HashSet<>();
        RsFreight rsFreight = rsFreightService.selectRsFreightByFreightId(freightId);
        ajaxResult.put(AjaxResult.DATA_TAG, rsFreight);
        if (rsFreight.getPrecarriageRegionId() != null) {
            set.add(rsFreight.getPrecarriageRegionId());
        }
        if (rsFreight.getPolId() != null) {
            set.add(rsFreight.getPolId());
        }
        if (rsFreight.getTransitPortId() != null) {
            set.add(rsFreight.getTransitPortId());
        }
        if (rsFreight.getDestinationPortId() != null) {
            set.add(rsFreight.getDestinationPortId());
        }
        ajaxResult.put("cargoTypeIds", rsFreightService.selectCargoTypes(freightId));
        ajaxResult.put("company", extCompanyService.selectExtCompanyByCompanyId(rsFreight.getSupplierId()));
        ajaxResult.put("locationOptions", !set.isEmpty() ? basDistLocationService.selectBasDistLocationByIds(set) : null);
        return ajaxResult;
    }

    /**
     * 新增基础运费
     */
    @PreAuthorize("@ss.hasPermi('system:freight:add')")
    @Log(title = "基础运费", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsFreight rsFreight) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "freight");
        return toAjax(rsFreightService.insertRsFreight(rsFreight));
    }

    /**
     * 修改基础运费
     */
    @PreAuthorize("@ss.hasPermi('system:freight:edit')")
    @Log(title = "基础运费", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsFreight rsFreight) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "freight");
        return toAjax(rsFreightService.updateRsFreight(rsFreight));
    }

    /**
     * 删除基础运费
     */
    @PreAuthorize("@ss.hasPermi('system:freight:remove')")
    @Log(title = "基础运费", businessType = BusinessType.DELETE)
    @DeleteMapping("/{freightIds}")
    public AjaxResult remove(@PathVariable Long[] freightIds) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "freight");
        return AjaxResult.success(rsFreightService.deleteRsFreightByFreightIds(freightIds));
    }


    /**
     * 下载导入模板
     *
     * @param response
     * @throws IOException
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException {
        try {
            ExcelUtil<RsFreight> util = new ExcelUtil<>(RsFreight.class);
//            for (Object[] o : util.getFields()) {
//                Excel annotation = (Excel) o[1];
//                util.
//                InvocationHandler handler = Proxy.getInvocationHandler(annotation);
//                Field field = handler.getClass().getDeclaredField("memberValues");
//                field.setAccessible(true);
//                Map memberValues = (Map) field.get(handler);
//                String[] combo = RedisCache.outputList(annotation.name());
//                memberValues.put("combo", combo);
//            }
            util.importTemplateExcel(response, "基础海运费");
        } catch (Exception ignore) {

        }
    }

    /**
     * 处理导入数据
     *
     * @param file
     * @param updateSupport
     * @param
     * @return
     * @throws Exception
     */
    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('system:freight:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport, Long serviceTypeId, String typeId) throws Exception {
        // 移除缓存
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "freight");
        ExcelUtil<RsFreight> util = new ExcelUtil<>(RsFreight.class);
        // 解析excel文件,获取数据
        List<RsFreight> freightList = util.importExcel(file.getInputStream());
        List<RsFreight> failList = rsFreightService.importFreight(freightList, updateSupport, serviceTypeId, typeId);
        if (!failList.isEmpty()) {
            redisCache.setCacheObject("importFailList", failList);
            return AjaxResult.success("上传失败列表");
        } else {
            return AjaxResult.success("全部上传成功");
        }
    }

    /**
     * 导出基础运费列表
     */
    @PreAuthorize("@ss.hasPermi('system:freight:import')")
    @Log(title = "基础运费", businessType = BusinessType.EXPORT)
    @PostMapping("/failList")
    public void failList(HttpServletResponse response) {
        List<RsFreight> list = redisCache.getCacheObject("importFailList");
        ExcelUtil<RsFreight> util = new ExcelUtil<>(RsFreight.class);
        util.exportExcel(response, list, "上传失败列表");
        redisCache.deleteObject("importFailList");
    }
}
