package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.BasDocType;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.BasDocTypeMapper;
import com.rich.system.service.BasDocTypeService;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-08
 */
@Service
public class BasDocTypeServiceImpl implements BasDocTypeService {
    @Autowired
    private BasDocTypeMapper basDocTypeMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param docTypeCode 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public BasDocType selectBasDocTypeByDocTypeCode(String docTypeCode) {
        return basDocTypeMapper.selectBasDocTypeByDocTypeCode(docTypeCode);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param basDocType 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<BasDocType> selectBasDocTypeList(BasDocType basDocType) {
        return basDocTypeMapper.selectBasDocTypeList(basDocType);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param basDocType 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertBasDocType(BasDocType basDocType) {
        basDocType.setCreateTime(DateUtils.getNowDate());
        basDocType.setCreateBy(SecurityUtils.getUserId());
        return basDocTypeMapper.insertBasDocType(basDocType);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param basDocType 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateBasDocType(BasDocType basDocType) {
        basDocType.setUpdateTime(DateUtils.getNowDate());
        basDocType.setUpdateBy(SecurityUtils.getUserId());
        return basDocTypeMapper.updateBasDocType(basDocType);
    }

    /**
     * 修改【请填写功能名称】状态
     *
     * @param basDocType 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public int changeStatus(BasDocType basDocType) {
        return basDocTypeMapper.updateBasDocType(basDocType);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param docTypeCodes 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteBasDocTypeByDocTypeCodes(String[] docTypeCodes) {
        return basDocTypeMapper.deleteBasDocTypeByDocTypeCodes(docTypeCodes);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param docTypeCode 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteBasDocTypeByDocTypeCode(String docTypeCode) {
        return basDocTypeMapper.deleteBasDocTypeByDocTypeCode(docTypeCode);
    }
}
