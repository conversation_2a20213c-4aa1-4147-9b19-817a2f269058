package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.RsWarehouseClient;
import com.rich.common.core.domain.entity.SysRole;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.RsWarehouseClientService;
import com.rich.system.service.impl.RedisCacheImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 仓库客户信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-11
 */
@RestController
@RequestMapping("/system/warehouseclient")
public class RsWarehouseClientController extends BaseController {
    @Autowired
    private RsWarehouseClientService rsWarehouseClientService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisCacheImpl RedisCache;

    @GetMapping("/selectList")
    public AjaxResult selectList(RsWarehouseClient rsWarehouseClient) {
        List<RsWarehouseClient> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "warehouseClient");
        if (list == null) {
            RedisCache.warehouseClient();
            list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "warehouseClient");
        }
        return AjaxResult.success(AjaxResult.DATA_TAG, list);
    }

    /**
     * 查询仓库客户信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:warehouseclient:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsWarehouseClient rsWarehouseClient) {
        if (hasWarehouseAdminRole(SecurityUtils.getLoginUser().getUser().getRoles()) || SecurityUtils.getDeptId().equals(109L) || SecurityUtils.getDeptId() == 102L || SecurityUtils.getLoginUser().getUser().isAdmin()) {
            rsWarehouseClient.setSalesId(null);
        } else {
            rsWarehouseClient.setSalesId(SecurityUtils.getUserId());
        }
        startPage();
        List<RsWarehouseClient> list = rsWarehouseClientService.selectRsWarehouseClientList(rsWarehouseClient);
        return getDataTable(list);
    }

    /**
     * 导出仓库客户信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:warehouseclient:export')")
    @Log(title = "仓库客户信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsWarehouseClient rsWarehouseClient) {
        List<RsWarehouseClient> list = rsWarehouseClientService.selectRsWarehouseClientList(rsWarehouseClient);
        ExcelUtil<RsWarehouseClient> util = new ExcelUtil<RsWarehouseClient>(RsWarehouseClient.class);
        util.exportExcel(response, list, "仓库客户信息数据");
    }

    /**
     * 获取仓库客户信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:warehouseclient:query')")
    @GetMapping(value = "/{warehouseClientId}")
    public AjaxResult getInfo(@PathVariable("warehouseClientId") Long warehouseClientId) {
        return AjaxResult.success(rsWarehouseClientService.selectRsWarehouseClientByWarehouseClientId(warehouseClientId));
    }

    /**
     * 新增仓库客户信息
     */
    @PreAuthorize("@ss.hasPermi('system:warehouseclient:add')")
    @Log(title = "仓库客户信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsWarehouseClient rsWarehouseClient) {
        // client_code查重
        if (rsWarehouseClient.getClientCode().isEmpty()) {
            throw new RuntimeException("请正确填写客户代码");
        }
        int i = rsWarehouseClientService.checkClientCode(rsWarehouseClient.getClientCode());
        if (i > 0) {
            throw new RuntimeException("系统中存在重复客户代码");
        }
        int rows = rsWarehouseClientService.insertRsWarehouseClient(rsWarehouseClient);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "warehouseClient");
        return toAjax(rows);
    }

    /**
     * 修改仓库客户信息
     */
    @PreAuthorize("@ss.hasPermi('system:warehouseclient:edit')")
    @Log(title = "仓库客户信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsWarehouseClient rsWarehouseClient) {
        int rows = rsWarehouseClientService.updateRsWarehouseClient(rsWarehouseClient);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "warehouseClient");
        return toAjax(rows);
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:warehouseclient:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsWarehouseClient rsWarehouseClient) {
        rsWarehouseClient.setUpdateBy(getUserId());
        int rows = rsWarehouseClientService.changeStatus(rsWarehouseClient);
        return toAjax(rows);
    }

    /**
     * 删除仓库客户信息
     */
    @PreAuthorize("@ss.hasPermi('system:warehouseclient:remove')")
    @Log(title = "仓库客户信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{warehouseClientIds}")
    public AjaxResult remove(@PathVariable Long[] warehouseClientIds) {
        int rows = rsWarehouseClientService.deleteRsWarehouseClientByWarehouseClientIds(warehouseClientIds);
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "warehouseClient");
        return toAjax(rows);
    }

    private boolean hasWarehouseAdminRole(List<SysRole> roles) {
        if (roles == null || roles.isEmpty()) {
            return false;
        }

        // 检查用户角色列表中是否包含"Warehouse admin"角色
        return roles.stream()
                .anyMatch(role -> "Warehouse admin".equals(role.getRoleKey())
                        || "warehouse_admin".equals(role.getRoleKey()));
    }
}
