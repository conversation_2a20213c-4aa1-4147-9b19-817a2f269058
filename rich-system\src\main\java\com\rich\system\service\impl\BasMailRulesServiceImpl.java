package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.BasMailRules;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.system.mapper.BasMailRulesMapper;
import com.rich.system.service.BasMailRulesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 邮件规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@Service
public class BasMailRulesServiceImpl implements BasMailRulesService {
    @Autowired
    private BasMailRulesMapper basMailRulesMapper;

    /**
     * 查询邮件规则
     *
     * @param mailRulesId 邮件规则主键
     * @return 邮件规则
     */
    @Override
    public BasMailRules selectBasMailRulesByMailRulesId(Long mailRulesId) {
        return basMailRulesMapper.selectBasMailRulesByMailRulesId(mailRulesId);
    }

    /**
     * 查询邮件规则列表
     *
     * @param basMailRules 邮件规则
     * @return 邮件规则
     */
    @Override
    public List<BasMailRules> selectBasMailRulesList(BasMailRules basMailRules) {
        return basMailRulesMapper.selectBasMailRulesList(basMailRules);
    }

    /**
     * 新增邮件规则
     *
     * @param basMailRules 邮件规则
     * @return 结果
     */
    @Override
    public int insertBasMailRules(BasMailRules basMailRules) {
        basMailRules.setCreateTime(DateUtils.getNowDate());
        basMailRules.setCreateBy(SecurityUtils.getUserId());
        return basMailRulesMapper.insertBasMailRules(basMailRules);
    }

    /**
     * 修改邮件规则
     *
     * @param basMailRules 邮件规则
     * @return 结果
     */
    @Override
    public int updateBasMailRules(BasMailRules basMailRules) {
        basMailRules.setUpdateTime(DateUtils.getNowDate());
        basMailRules.setUpdateBy(SecurityUtils.getUserId());
        return basMailRulesMapper.updateBasMailRules(basMailRules);
    }

    /**
     * 修改邮件规则状态
     *
     * @param basMailRules 邮件规则
     * @return 邮件规则
     */
    @Override
    public int changeStatus(BasMailRules basMailRules) {
        return basMailRulesMapper.updateBasMailRules(basMailRules);
    }

    /**
     * 批量删除邮件规则
     *
     * @param mailRulesIds 需要删除的邮件规则主键
     * @return 结果
     */
    @Override
    public int deleteBasMailRulesByMailRulesIds(Long[] mailRulesIds) {
        return basMailRulesMapper.deleteBasMailRulesByMailRulesIds(mailRulesIds);
    }

    /**
     * 删除邮件规则信息
     *
     * @param mailRulesId 邮件规则主键
     * @return 结果
     */
    @Override
    public int deleteBasMailRulesByMailRulesId(Long mailRulesId) {
        return basMailRulesMapper.deleteBasMailRulesByMailRulesId(mailRulesId);
    }
}
