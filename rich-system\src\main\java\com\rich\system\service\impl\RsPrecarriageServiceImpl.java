package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsPrecarriage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsPrecarriageMapper;
import com.rich.system.service.RsPrecarriageService;

/**
 * 前程运输，操作单中的记录包含的前程运输信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@Service
public class RsPrecarriageServiceImpl implements RsPrecarriageService {
    @Autowired
    private RsPrecarriageMapper rsPrecarriageMapper;

    /**
     * 查询前程运输，操作单中的记录包含的前程运输信息
     *
     * @param precarriageId 前程运输，操作单中的记录包含的前程运输信息主键
     * @return 前程运输，操作单中的记录包含的前程运输信息
     */
    @Override
    public RsPrecarriage selectRsPrecarriageByPrecarriageId(Long precarriageId) {
        return rsPrecarriageMapper.selectRsPrecarriageByPrecarriageId(precarriageId);
    }

    /**
     * 查询前程运输，操作单中的记录包含的前程运输信息列表
     *
     * @param rsPrecarriage 前程运输，操作单中的记录包含的前程运输信息
     * @return 前程运输，操作单中的记录包含的前程运输信息
     */
    @Override
    public List<RsPrecarriage> selectRsPrecarriageList(RsPrecarriage rsPrecarriage) {
        return rsPrecarriageMapper.selectRsPrecarriageList(rsPrecarriage);
    }

    /**
     * 新增前程运输，操作单中的记录包含的前程运输信息
     *
     * @param rsPrecarriage 前程运输，操作单中的记录包含的前程运输信息
     * @return 结果
     */
    @Override
    public int insertRsPrecarriage(RsPrecarriage rsPrecarriage) {
        return rsPrecarriageMapper.insertRsPrecarriage(rsPrecarriage);
    }

    /**
     * 修改前程运输，操作单中的记录包含的前程运输信息
     *
     * @param rsPrecarriage 前程运输，操作单中的记录包含的前程运输信息
     * @return 结果
     */
    @Override
    public int updateRsPrecarriage(RsPrecarriage rsPrecarriage) {
        return rsPrecarriageMapper.updateRsPrecarriage(rsPrecarriage);
    }

    /**
     * 修改前程运输，操作单中的记录包含的前程运输信息状态
     *
     * @param rsPrecarriage 前程运输，操作单中的记录包含的前程运输信息
     * @return 前程运输，操作单中的记录包含的前程运输信息
     */
    @Override
    public int changeStatus(RsPrecarriage rsPrecarriage) {
        return rsPrecarriageMapper.updateRsPrecarriage(rsPrecarriage);
    }

    /**
     * 批量删除前程运输，操作单中的记录包含的前程运输信息
     *
     * @param precarriageIds 需要删除的前程运输，操作单中的记录包含的前程运输信息主键
     * @return 结果
     */
    @Override
    public int deleteRsPrecarriageByPrecarriageIds(Long[] precarriageIds) {
        return rsPrecarriageMapper.deleteRsPrecarriageByPrecarriageIds(precarriageIds);
    }

    /**
     * 删除前程运输，操作单中的记录包含的前程运输信息信息
     *
     * @param precarriageId 前程运输，操作单中的记录包含的前程运输信息主键
     * @return 结果
     */
    @Override
    public int deleteRsPrecarriageByPrecarriageId(Long precarriageId) {
        return rsPrecarriageMapper.deleteRsPrecarriageByPrecarriageId(precarriageId);
    }
}
